<?php
/**
 * صفحة إدارة المستخدمين
 * تتيح للمدير إضافة وتعديل وحذف المستخدمين وإدارة صلاحياتهم
 */

// تعريف ثابت لمنع الوصول المباشر للملفات المرتبطة
define('BASEPATH', true);

// استدعاء ملفات التهيئة
require_once '../../config/init.php';

// التحقق من تسجيل الدخول
requireLogin();

// التحقق من صلاحيات المستخدم (المدير فقط)
redirectIfNotAllowed(ROLE_ADMIN);

// عنوان الصفحة
$pageTitle = 'إدارة المستخدمين';

// إنشاء كائن المستخدم
$userModel = new User($db);

// إنشاء كائن الفروع
$branchModel = new Branch($db);

// استرجاع قائمة الفروع
$branches = $branchModel->getBranches();

// تحديد العملية المطلوبة
$operation = $_GET['op'] ?? '';

// معالجة حذف المستخدم
if ($operation == 'delete' && isset($_GET['id'])) {
    $userId = intval($_GET['id']);

    // التحقق من عدم حذف الحساب الشخصي
    if ($userId == $_SESSION['user_id']) {
        $_SESSION['error_message'] = 'لا يمكنك حذف حسابك الشخصي';
    } else {
        try {
            $userModel->deleteUser($userId);
            $_SESSION['success_message'] = 'تم حذف المستخدم بنجاح';
        } catch (Exception $e) {
            $_SESSION['error_message'] = 'حدث خطأ أثناء حذف المستخدم: ' . $e->getMessage();
        }
    }

    // إعادة التوجيه لتجنب إعادة الحذف
    header('Location: users.php');
    exit;
}

// معالجة تحديث حالة المستخدم (تفعيل/تعطيل)
if ($operation == 'toggle_status' && isset($_GET['id'])) {
    $userId = intval($_GET['id']);
    $isActive = $_GET['status'] ?? '';

    if ($isActive !== '') {
        $isActive = $isActive == '1' ? 1 : 0;

        // التحقق من عدم تعطيل الحساب الشخصي
        if ($userId == $_SESSION['user_id'] && $isActive == 0) {
            $_SESSION['error_message'] = 'لا يمكنك تعطيل حسابك الشخصي';
        } else {
            try {
                $userModel->updateUserStatus($userId, $isActive);
                $_SESSION['success_message'] = $isActive ? 'تم تفعيل المستخدم بنجاح' : 'تم تعطيل المستخدم بنجاح';
            } catch (Exception $e) {
                $_SESSION['error_message'] = 'حدث خطأ أثناء تحديث حالة المستخدم: ' . $e->getMessage();
            }
        }
    }

    // إعادة التوجيه
    header('Location: users.php');
    exit;
}

// معالجة إعادة تعيين كلمة المرور
if ($operation == 'reset_password' && isset($_GET['id'])) {
    $userId = intval($_GET['id']);

    try {
        // إنشاء كلمة مرور عشوائية
        $newPassword = generateRandomPassword();

        // تحديث كلمة المرور
        $userModel->resetPassword($userId, $newPassword);

        // تخزين كلمة المرور في الجلسة لعرضها
        $_SESSION['temp_password'] = $newPassword;
        $_SESSION['success_message'] = 'تم إعادة تعيين كلمة المرور بنجاح';
    } catch (Exception $e) {
        $_SESSION['error_message'] = 'حدث خطأ أثناء إعادة تعيين كلمة المرور: ' . $e->getMessage();
    }

    // إعادة التوجيه
    header('Location: users.php');
    exit;
}

// معالجة إضافة/تعديل المستخدم
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    try {
        $userId = isset($_POST['user_id']) ? intval($_POST['user_id']) : 0;
        $isEditMode = $userId > 0;

        // جمع بيانات المستخدم
        $userData = [
            'name' => sanitizeInput($_POST['name']),
            'username' => sanitizeInput($_POST['username']),
            'email' => sanitizeInput($_POST['email']),
            'role' => sanitizeInput($_POST['role']),
            'branch_id' => intval($_POST['branch_id']),
            'is_active' => isset($_POST['is_active']) ? 1 : 0
        ];

        // التحقق من صحة البيانات
        if (empty($userData['name']) || empty($userData['username'])) {
            throw new Exception('يرجى إدخال الاسم واسم المستخدم');
        }

        // التحقق من عدم تكرار اسم المستخدم
        if ($userModel->isUsernameTaken($userData['username'], $userId)) {
            throw new Exception('اسم المستخدم موجود بالفعل، يرجى اختيار اسم آخر');
        }

        // إذا كان إضافة مستخدم جديد، أضف كلمة المرور
        if (!$isEditMode) {
            $password = $_POST['password'] ?? '';

            if (empty($password)) {
                throw new Exception('كلمة المرور مطلوبة لإضافة مستخدم جديد');
            }

            if (strlen($password) < 6) {
                throw new Exception('كلمة المرور يجب أن تكون 6 أحرف على الأقل');
            }

            $userData['password'] = $password;
        }

        // إذا تم توفير كلمة مرور جديدة للمستخدم الحالي
        if ($isEditMode && !empty($_POST['new_password'])) {
            $userData['password'] = $_POST['new_password'];
        }

        // معالجة الصلاحيات
        $permissions = $_POST['permissions'] ?? [];

        // حفظ بيانات المستخدم
        if ($isEditMode) {
            $userModel->updateUser($userId, $userData);
            $userModel->updateUserPermissions($userId, $permissions);
            $_SESSION['success_message'] = 'تم تحديث بيانات المستخدم بنجاح';
        } else {
            $newUserId = $userModel->addUser($userData);
            $userModel->updateUserPermissions($newUserId, $permissions);
            $_SESSION['success_message'] = 'تم إضافة المستخدم بنجاح';
        }

    } catch (Exception $e) {
        $_SESSION['error_message'] = $e->getMessage();
    }

    // إعادة التوجيه بعد المعالجة
    header('Location: users.php');
    exit;
}

// بيانات المستخدم للتعديل
$userForEdit = null;
if ($operation == 'edit' && isset($_GET['id'])) {
    $userId = intval($_GET['id']);
    $userForEdit = $userModel->getUserById($userId);

    // استرجاع صلاحيات المستخدم
    if ($userForEdit) {
        $userForEdit['permissions'] = $userModel->getUserPermissions($userId);
    }
}

// استرجاع قائمة المستخدمين
$users = $userModel->getUsers();

// تحضير مصفوفة الصلاحيات المتاحة
$availablePermissions = [
    'dashboard_view' => 'عرض لوحة التحكم',
    'users_manage' => 'إدارة المستخدمين',
    'employees_view' => 'عرض الموظفين',
    'employees_add' => 'إضافة موظفين',
    'employees_edit' => 'تعديل الموظفين',
    'employees_delete' => 'حذف الموظفين',
    'employees_salaries' => 'إدارة رواتب الموظفين',
    'branches_view' => 'عرض الفروع',
    'branches_add' => 'إضافة فرع',
    'branches_edit' => 'تعديل فرع',
    'branches_delete' => 'حذف فرع',
    'customers_view' => 'عرض العملاء',
    'customers_add' => 'إضافة عملاء',
    'customers_edit' => 'تعديل العملاء',
    'customers_delete' => 'حذف العملاء',
    'services_view' => 'عرض الخدمات',
    'services_add' => 'إضافة خدمات',
    'services_edit' => 'تعديل الخدمات',
    'services_delete' => 'حذف الخدمات',
    'products_view' => 'عرض المنتجات',
    'products_add' => 'إضافة منتجات',
    'products_edit' => 'تعديل المنتجات',
    'products_delete' => 'حذف المنتجات',
    'promotions_view' => 'عرض العروض والخصومات',
    'promotions_create' => 'اضافة عروض',
    'promotions_edit' => 'تعديل العروض',
    'inventory_view' => 'عرض المخزون',
    'inventory_adjust' => 'تعديل المخزون',
    'appointments_view' => 'عرض المواعيد',
    'appointments_create' => 'إضافة مواعيد',
    'appointments_edit' => 'تعديل المواعيد',
    'invoices_view' => 'عرض الفواتير',
    'invoices_create' => 'إنشاء فاتورة',
    'invoices_edit' => 'تعديل فاتورة',
    'invoices_delete' => 'حذف فاتورة',
    'invoices_print' => 'طباعة الفواتير',
    'expenses_view' => 'عرض المصروفات',
    'expenses_add' => 'إضافة مصروفات',
    'expenses_edit' => 'تعديل المصروفات',
    'expenses_delete' => 'حذف المصروفات',
    'reports_view' => 'عرض التقارير',
    'reports_sales' => 'تقارير المبيعات',
    'reports_services' => 'تقارير الخدمات',
    'reports_employees' => 'تقارير الموظفين',
    'reports_expenses' => 'تقارير المصروفات',
    'reports_appointments' => 'تقارير المواعيد',
    'endday_manage' => 'إدارة نهاية اليوم',
    'settings_view' => 'عرض الإعدادات',
    'settings_edit' => 'تعديل الإعدادات'
];

// ترتيب الصلاحيات في مجموعات
$permissionGroups = [
    'الصلاحيات العامة' => ['dashboard_view', 'users_manage', 'settings_view', 'settings_edit'],
    'الموظفين' => ['employees_view', 'employees_add', 'employees_edit', 'employees_delete', 'employees_salaries'],
    'الفروع' => ['branches_view', 'branches_add', 'branches_edit', 'branches_delete'],
    'العملاء' => ['customers_view', 'customers_add', 'customers_edit', 'customers_delete'],
    'الخدمات' => ['services_view', 'services_add', 'services_edit', 'services_delete'],
    'المنتجات والمخزون' => ['products_view', 'products_add', 'products_edit', 'products_delete', 'inventory_view', 'inventory_adjust'],
    'المواعيد' => ['appointments_view', 'appointments_create', 'appointments_edit'],
    'الفواتير' => ['invoices_view', 'invoices_create', 'invoices_edit', 'invoices_delete', 'invoices_print'],
    'المصروفات' => ['expenses_view', 'expenses_add', 'expenses_edit', 'expenses_delete'],
    'العروض والخصومات'=> ['promotions_view', 'promotions_create', 'promotions_edit'],
    'التقارير' => ['reports_view', 'reports_sales', 'reports_services', 'reports_employees', 'reports_expenses', 'reports_appointments'],
    'نهاية اليوم' => ['endday_manage']
];

// استدعاء رأس الصفحة
include '../../includes/templates/header.php';
?>

<!-- المحتوى الرئيسي للصفحة -->
<div class="container-fluid">
    <!-- العنوان وزر الإضافة -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-body d-flex justify-content-between align-items-center">
                    <h1 class="h3 mb-0">إدارة المستخدمين</h1>
                    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#userModal">
                        <i class="fas fa-plus-circle"></i> إضافة مستخدم جديد
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- عرض رسائل النجاح والخطأ -->
    <?php if (isset($_SESSION['success_message'])): ?>
    <div class="alert alert-success" role="alert">
        <?php
            echo $_SESSION['success_message'];
            unset($_SESSION['success_message']);
        ?>
    </div>
    <?php endif; ?>

    <?php if (isset($_SESSION['error_message'])): ?>
    <div class="alert alert-danger" role="alert">
        <?php
            echo $_SESSION['error_message'];
            unset($_SESSION['error_message']);
        ?>
    </div>
    <?php endif; ?>

    <!-- عرض كلمة المرور المؤقتة -->
    <?php if (isset($_SESSION['temp_password'])): ?>
    <div class="alert alert-info" role="alert">
        <strong>كلمة المرور الجديدة:</strong> <?php echo $_SESSION['temp_password']; ?>
        <p class="mb-0 mt-2 small text-muted">هذه هي المرة الوحيدة التي سيتم فيها عرض كلمة المرور، يرجى حفظها في مكان آمن.</p>
    </div>
    <?php unset($_SESSION['temp_password']); ?>
    <?php endif; ?>

    <!-- جدول المستخدمين -->
    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th>#</th>
                                    <th>الاسم</th>
                                    <th>اسم المستخدم</th>
                                    <th>البريد الإلكتروني</th>
                                    <th>الدور</th>
                                    <th>الفرع</th>
                                    <th>الحالة</th>
                                    <th>آخر تسجيل دخول</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (empty($users)): ?>
                                    <tr>
                                        <td colspan="9" class="text-center py-4">لا يوجد مستخدمين</td>
                                    </tr>
                                <?php else: ?>
                                    <?php foreach ($users as $index => $user): ?>
                                        <tr>
                                            <td><?php echo $index + 1; ?></td>
                                            <td><?php echo htmlspecialchars($user['name']); ?></td>
                                            <td><?php echo htmlspecialchars($user['username']); ?></td>
                                            <td><?php echo htmlspecialchars($user['email'] ?? '-'); ?></td>
                                            <td>
                                                <?php
                                                $roleTranslations = [
                                                    'admin' => 'مدير',
                                                    'manager' => 'مشرف',
                                                    'cashier' => 'كاشير',
                                                    'employee' => 'موظف'
                                                ];
                                                echo $roleTranslations[$user['role']] ?? $user['role'];
                                                ?>
                                            </td>
                                            <td><?php echo htmlspecialchars($user['branch_name'] ?? 'جميع الفروع'); ?></td>
                                            <td>
                                                <?php if ($user['is_active']): ?>
                                                    <span class="badge bg-success">مفعل</span>
                                                <?php else: ?>
                                                    <span class="badge bg-danger">معطل</span>
                                                <?php endif; ?>
                                            </td>
                                            <td><?php echo $user['last_login'] ? date('Y-m-d H:i', strtotime($user['last_login'])) : 'لم يسجل الدخول بعد'; ?></td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <a href="users.php?op=edit&id=<?php echo $user['id']; ?>" class="btn btn-primary">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <?php if ($user['id'] != $_SESSION['user_id']): ?>
                                                        <?php if ($user['is_active']): ?>
                                                            <a href="users.php?op=toggle_status&id=<?php echo $user['id']; ?>&status=0" class="btn btn-warning" title="تعطيل">
                                                                <i class="fas fa-ban"></i>
                                                            </a>
                                                        <?php else: ?>
                                                            <a href="users.php?op=toggle_status&id=<?php echo $user['id']; ?>&status=1" class="btn btn-success" title="تفعيل">
                                                                <i class="fas fa-check"></i>
                                                            </a>
                                                        <?php endif; ?>
                                                        <a href="users.php?op=reset_password&id=<?php echo $user['id']; ?>" class="btn btn-info" title="إعادة تعيين كلمة المرور" onclick="return confirm('هل أنت متأكد من إعادة تعيين كلمة المرور؟')">
                                                            <i class="fas fa-key"></i>
                                                        </a>
                                                        <a href="users.php?op=delete&id=<?php echo $user['id']; ?>" class="btn btn-danger" title="حذف" onclick="return confirm('هل أنت متأكد من حذف هذا المستخدم؟')">
                                                            <i class="fas fa-trash"></i>
                                                        </a>
                                                    <?php endif; ?>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- نموذج إضافة/تعديل المستخدم -->
<div class="modal fade" id="userModal" tabindex="-1" aria-labelledby="userModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <form method="post" id="userForm">
                <div class="modal-header">
                    <h5 class="modal-title" id="userModalLabel">
                        <?php echo $userForEdit ? 'تعديل المستخدم: ' . htmlspecialchars($userForEdit['name']) : 'إضافة مستخدم جديد'; ?>
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <!-- معرف المستخدم للتعديل -->
                    <?php if ($userForEdit): ?>
                        <input type="hidden" name="user_id" value="<?php echo $userForEdit['id']; ?>">
                    <?php endif; ?>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="name" class="form-label">الاسم <span class="text-danger">*</span></label>
                            <input type="text" id="name" name="name" class="form-control" value="<?php echo $userForEdit['name'] ?? ''; ?>" required>
                        </div>
                        <div class="col-md-6">
                            <label for="username" class="form-label">اسم المستخدم <span class="text-danger">*</span></label>
                            <input type="text" id="username" name="username" class="form-control" value="<?php echo $userForEdit['username'] ?? ''; ?>" required <?php echo $userForEdit ? 'readonly' : ''; ?>>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="email" class="form-label">البريد الإلكتروني</label>
                            <input type="email" id="email" name="email" class="form-control" value="<?php echo $userForEdit['email'] ?? ''; ?>">
                        </div>
                        <div class="col-md-6">
                            <label for="role" class="form-label">الدور <span class="text-danger">*</span></label>
                            <select id="role" name="role" class="form-select" required>
                                <option value="admin" <?php echo ($userForEdit['role'] ?? '') == 'admin' ? 'selected' : ''; ?>>مدير</option>
                                <option value="manager" <?php echo ($userForEdit['role'] ?? '') == 'manager' ? 'selected' : ''; ?>>مشرف</option>
                                <option value="cashier" <?php echo ($userForEdit['role'] ?? '') == 'cashier' ? 'selected' : ''; ?>>كاشير</option>
                                <option value="employee" <?php echo ($userForEdit['role'] ?? '') == 'employee' ? 'selected' : ''; ?>>موظف</option>
                            </select>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="branch_id" class="form-label">الفرع</label>
                            <select id="branch_id" name="branch_id" class="form-select">
                                <option value="0">جميع الفروع</option>
                                <?php foreach ($branches as $branch): ?>
                                    <option value="<?php echo $branch['id']; ?>" <?php echo ($userForEdit['branch_id'] ?? 0) == $branch['id'] ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($branch['name']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label for="password" class="form-label">
                                <?php echo $userForEdit ? 'كلمة المرور الجديدة' : 'كلمة المرور'; ?>
                                <?php echo $userForEdit ? '' : '<span class="text-danger">*</span>'; ?>
                            </label>
                            <input type="password" id="password" name="<?php echo $userForEdit ? 'new_password' : 'password'; ?>" class="form-control" <?php echo $userForEdit ? '' : 'required'; ?> minlength="6">
                            <small class="form-text text-muted">يجب أن تكون كلمة المرور 6 أحرف على الأقل</small>
                        </div>
                    </div>

                    <div class="form-check mb-3">
                        <input class="form-check-input" type="checkbox" id="is_active" name="is_active" <?php echo !$userForEdit || ($userForEdit['is_active'] ?? false) ? 'checked' : ''; ?>>
                        <label class="form-check-label" for="is_active">مفعل</label>
                    </div>

                    <!-- الصلاحيات -->
                    <div class="mb-3">
                        <label class="form-label">الصلاحيات</label>
                        <div class="row">
                            <?php
                            $userPermissions = [];
                            if ($userForEdit && isset($userForEdit['permissions'])) {
                                $userPermissions = $userForEdit['permissions'];
                            }
                            ?>

                            <?php foreach ($permissionGroups as $groupName => $permissions): ?>
                                <div class="col-md-6 mb-3">
                                    <div class="card">
                                        <div class="card-header">
                                            <strong><?php echo $groupName; ?></strong>
                                        </div>
                                        <div class="card-body">
                                            <?php foreach ($permissions as $permission): ?>
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" id="<?php echo $permission; ?>" name="permissions[]" value="<?php echo $permission; ?>" <?php echo in_array($permission, $userPermissions) ? 'checked' : ''; ?>>
                                                    <label class="form-check-label" for="<?php echo $permission; ?>">
                                                        <?php echo $availablePermissions[$permission] ?? $permission; ?>
                                                    </label>
                                                </div>
                                            <?php endforeach; ?>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">حفظ</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- استدعاء قالب الفوتر -->
<?php include '../../includes/templates/footer.php'; ?>

<script>
    $(document).ready(function() {
        // تحديد الدور وتحديد الصلاحيات تلقائياً
        $('#role').change(function() {
            var role = $(this).val();

            // إلغاء تحديد جميع الصلاحيات
            $('input[name="permissions[]"]').prop('checked', false);

            // تحديد الصلاحيات حسب الدور
            if (role == 'admin') {
                // المدير له جميع الصلاحيات
                $('input[name="permissions[]"]').prop('checked', true);
            } else if (role == 'manager') {
                // المشرف له صلاحيات أقل من المدير
                var managerPermissions = [
                    'dashboard_view', 'employees_view', 'employees_add', 'employees_edit',
                    'branches_view',
                    'customers_view', 'customers_add', 'customers_edit', 'customers_delete',
                    'services_view', 'services_add', 'services_edit',
                    'products_view', 'products_add', 'products_edit',
                    'inventory_view', 'inventory_adjust',
                    'appointments_view', 'appointments_create', 'appointments_edit',
                    'invoices_view', 'invoices_create', 'invoices_edit',
                    'expenses_view', 'expenses_add', 'expenses_edit',
                    'reports_view','invoices_print', 'reports_sales', 'reports_services', 'reports_employees', 'reports_expenses', 'reports_appointments',
                    'endday_manage'
                ];

                for (var i = 0; i < managerPermissions.length; i++) {
                    $('#' + managerPermissions[i]).prop('checked', true);
                }
            } else if (role == 'cashier') {
                // الكاشير له صلاحيات محدودة
                var cashierPermissions = [
                    'dashboard_view',
                    'customers_view', 'customers_add', 'customers_edit',
                    'services_view',
                    'products_view',
                    'inventory_view',
                    'invoices_print',
                    'appointments_view', 'appointments_create', 'appointments_edit',
                    'invoices_view', 'invoices_create',
                    'endday_manage'
                ];

                for (var i = 0; i < cashierPermissions.length; i++) {
                    $('#' + cashierPermissions[i]).prop('checked', true);
                }
            } else if (role == 'employee') {
                // الموظف له الحد الأدنى من الصلاحيات
                var employeePermissions = [
                    'dashboard_view',
                    'customers_view',
                    'services_view',
                    'appointments_view',
                    'appointments_edit'
                ];

                for (var i = 0; i < employeePermissions.length; i++) {
                    $('#' + employeePermissions[i]).prop('checked', true);
                }
            }
        });

        // إذا كان في وضع التعديل، يتم فتح النموذج
        <?php if ($userForEdit): ?>
        var userModal = new bootstrap.Modal(document.getElementById('userModal'));
        userModal.show();
        <?php endif; ?>
    });
</script>
