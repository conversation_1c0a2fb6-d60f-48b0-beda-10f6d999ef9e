/**
 * Módulo para enviar notificaciones de informes a través de WhatsApp
 * Utiliza el mismo enfoque que whatsapp-invoice-notification.js
 */

const WhatsAppReportNotification = {
    // URL del servidor local de WhatsApp
    serverUrl: 'http://localhost:3000',

    // URL base de la API del sistema
    apiUrl: BASE_URL + 'api/',

    // Estado de la última operación
    lastOperationStatus: null,
    
    // Conjunto para rastrear informes ya notificados y evitar duplicados
    notifiedReports: new Set(),

    // Opciones de configuración
    options: {
        debug: true,                // Habilitar mensajes de depuración
        timeout: 30000,             // Tiempo de espera para las solicitudes (30 segundos)
        showSuccessMessage: true    // Mostrar mensaje de éxito al enviar
    },

    /**
     * Inicializar el módulo
     * @param {Object} options Opciones de configuración
     */
    init: function(options) {
        // Combinar opciones proporcionadas con las predeterminadas
        if (options) {
            this.options = { ...this.options, ...options };
        }

        console.log('WhatsAppReportNotification inicializado con opciones:', this.options);
        console.log('URL del servidor local de WhatsApp:', this.serverUrl);
        console.log('URL base de la API del sistema:', this.apiUrl);
    },

    /**
     * Enviar notificación de informe a los administradores
     * @param {string} reportTitle Título del informe
     * @param {string} reportContent Contenido del informe
     * @returns {Promise<Object>} Resultado de la operación
     */
    sendReportNotification: function(reportTitle, reportContent) {
        return new Promise((resolve, reject) => {
            console.log(`=== INICIO PROCESO DE ENVÍO DE INFORME: ${reportTitle} ===`);
            
            // Verificar si ya se ha enviado una notificación para este informe
            const reportId = this._generateReportId(reportTitle, reportContent);
            if (this.notifiedReports.has(reportId)) {
                console.log(`DUPLICADO DETECTADO: Notificación para informe "${reportTitle}" ya fue enviada anteriormente.`);
                console.log(`Informes ya notificados: [${Array.from(this.notifiedReports).join(', ')}]`);
                resolve({ status: 'success', message: 'Notificación ya enviada anteriormente' });
                return;
            }

            console.log(`Preparando notificación de WhatsApp para informe: ${reportTitle}`);
            
            // Enviar la solicitud a la API
            const xhr = new XMLHttpRequest();
            xhr.open('POST', this.apiUrl + 'admin_notification.php', true);
            xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');
            xhr.responseType = 'json';
            xhr.timeout = this.options.timeout;

            xhr.onload = () => {
                console.log(`Respuesta recibida, código de estado: ${xhr.status}`);
                
                if (xhr.status === 200) {
                    const response = xhr.response;
                    console.log('Respuesta completa:', response);

                    if (response && response.status === 'success') {
                        console.log(`¡Éxito! Informe "${reportTitle}" enviado correctamente`);
                        
                        // Marcar este informe como ya notificado
                        this.notifiedReports.add(reportId);
                        console.log(`Informe "${reportTitle}" marcado como notificado correctamente`);
                        
                        // Mostrar mensaje de éxito si está habilitado
                        if (this.options.showSuccessMessage) {
                            this._showSuccessMessage();
                        }

                        this.lastOperationStatus = 'success';
                        console.log(`=== FIN PROCESO DE ENVÍO DE INFORME: ${reportTitle} - ÉXITO ===`);
                        resolve({ status: 'success', message: 'Informe enviado correctamente' });
                    } else {
                        const errorMsg = response ? response.message : 'Error desconocido al enviar informe';
                        console.error(`Error al enviar informe "${reportTitle}": ${errorMsg}`);
                        
                        this.lastOperationStatus = 'error';
                        console.log(`=== FIN PROCESO DE ENVÍO DE INFORME: ${reportTitle} - ERROR ===`);
                        reject(new Error(errorMsg));
                    }
                } else {
                    const errorMsg = `Error al enviar informe: ${xhr.status}`;
                    console.error(errorMsg);
                    
                    this.lastOperationStatus = 'error';
                    console.log(`=== FIN PROCESO DE ENVÍO DE INFORME: ${reportTitle} - ERROR ===`);
                    reject(new Error(errorMsg));
                }
            };

            xhr.ontimeout = () => {
                const errorMsg = 'Tiempo de espera agotado al enviar informe';
                console.error(errorMsg);

                // A pesar del timeout, el informe podría haberse enviado correctamente
                console.warn('El informe podría haberse enviado a pesar del timeout');
                
                this.lastOperationStatus = 'warning';
                console.log(`=== FIN PROCESO DE ENVÍO DE INFORME: ${reportTitle} - ADVERTENCIA (TIMEOUT) ===`);
                resolve({
                    status: 'warning',
                    message: 'Tiempo de espera agotado, pero el informe podría haberse enviado correctamente'
                });
            };

            xhr.onerror = (e) => {
                const errorMsg = 'Error de red al enviar informe';
                console.error(errorMsg, e);
                
                this.lastOperationStatus = 'error';
                console.log(`=== FIN PROCESO DE ENVÍO DE INFORME: ${reportTitle} - ERROR ===`);
                reject(new Error(errorMsg));
            };

            // Preparar los datos para enviar
            const formData = new URLSearchParams();
            formData.append('action', 'send_custom_report');
            formData.append('report_title', reportTitle);
            formData.append('report_content', reportContent);
            
            console.log('Enviando datos a la API:', {
                action: 'send_custom_report',
                report_title: reportTitle,
                report_content: reportContent.substring(0, 100) + '...' // Mostrar solo los primeros 100 caracteres
            });
            
            // Enviar la solicitud
            xhr.send(formData.toString());
        });
    },

    /**
     * Generar un ID único para el informe basado en su título y contenido
     * @private
     * @param {string} title Título del informe
     * @param {string} content Contenido del informe
     * @returns {string} ID único para el informe
     */
    _generateReportId: function(title, content) {
        // Usar una combinación del título y los primeros 50 caracteres del contenido
        return title + '_' + content.substring(0, 50).replace(/\s+/g, '_');
    },

    /**
     * Mostrar mensaje de éxito
     * @private
     */
    _showSuccessMessage: function() {
        // Verificar si toastr está disponible
        if (typeof toastr !== 'undefined') {
            toastr.success('تم إرسال التقرير بنجاح للمدراء');
        } else {
            alert('تم إرسال التقرير بنجاح للمدراء');
        }
    }
};
