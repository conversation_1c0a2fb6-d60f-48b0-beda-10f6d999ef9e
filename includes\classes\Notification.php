<?php
/**
 * فئة إدارة الإشعارات
 */

// منع الوصول المباشر للملف
if (!defined('BASEPATH')) {
    exit('لا يمكن الوصول المباشر لهذا الملف');
}

class Notification {
    private $db;

    /**
     * المُنشئ
     *
     * @param Database $db كائن قاعدة البيانات
     */
    public function __construct($db) {
        $this->db = $db;
    }

    /**
     * إنشاء إشعار جديد
     *
     * @param array $data بيانات الإشعار
     * @return int معرف الإشعار الجديد
     */
    public function createNotification($data) {
        try {
            $this->db->prepare("INSERT INTO notifications (
                type,
                recipient_id,
                recipient_type,
                title,
                message,
                related_id,
                related_type,
                branch_id,
                is_read,
                is_sent,
                send_email,
                send_sms,
                scheduled_at
            ) VALUES (
                :type,
                :recipient_id,
                :recipient_type,
                :title,
                :message,
                :related_id,
                :related_type,
                :branch_id,
                :is_read,
                :is_sent,
                :send_email,
                :send_sms,
                :scheduled_at
            )");

            $this->db->bind(':type', $data['type'] ?? 'system');
            $this->db->bind(':recipient_id', $data['recipient_id'] ?? null);
            $this->db->bind(':recipient_type', $data['recipient_type'] ?? 'customer');
            $this->db->bind(':title', $data['title']);
            $this->db->bind(':message', $data['message']);
            $this->db->bind(':related_id', $data['related_id'] ?? null);
            $this->db->bind(':related_type', $data['related_type'] ?? null);
            $this->db->bind(':branch_id', $data['branch_id'] ?? null);
            $this->db->bind(':is_read', $data['is_read'] ?? 0);
            $this->db->bind(':is_sent', $data['is_sent'] ?? 0);
            $this->db->bind(':send_email', $data['send_email'] ?? 0);
            $this->db->bind(':send_sms', $data['send_sms'] ?? 0);
            $this->db->bind(':scheduled_at', $data['scheduled_at'] ?? null);

            $this->db->execute();
            return $this->db->lastInsertId();
        } catch (Exception $e) {
            error_log('خطأ في إنشاء إشعار: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * إنشاء إشعار تذكير بموعد
     *
     * @param int $appointmentId معرف الموعد
     * @param int $hoursBeforeAppointment عدد الساعات قبل الموعد
     * @return int معرف الإشعار الجديد
     */
    public function createAppointmentReminder($appointmentId, $hoursBeforeAppointment = 24) {
        try {
            // الحصول على بيانات الموعد
            $this->db->prepare("SELECT a.*,
                               c.name AS customer_name,
                               c.phone AS customer_phone,
                               c.email AS customer_email,
                               s.name AS service_name,
                               e.name AS employee_name,
                               b.id AS branch_id,
                               b.name AS branch_name
                        FROM appointments a
                        LEFT JOIN customers c ON a.customer_id = c.id
                        LEFT JOIN services s ON a.service_id = s.id
                        LEFT JOIN employees e ON a.employee_id = e.id
                        LEFT JOIN branches b ON a.branch_id = b.id
                        WHERE a.id = :id");
            $this->db->bind(':id', $appointmentId);
            $appointment = $this->db->fetch();

            if (!$appointment) {
                throw new Exception('الموعد غير موجود');
            }

            // التحقق من وجود عميل
            if (!$appointment['customer_id']) {
                throw new Exception('لا يوجد عميل مرتبط بالموعد');
            }

            // حساب وقت الإشعار
            $appointmentDateTime = $appointment['date'] . ' ' . $appointment['start_time'];
            $scheduledAt = date('Y-m-d H:i:s', strtotime($appointmentDateTime) - ($hoursBeforeAppointment * 3600));

            // الحصول على إعدادات الإشعارات
            $settingsModel = new Settings($this->db);
            $allSettings = $settingsModel->getAllSettings();

            // استخراج إعدادات الإشعارات
            $notificationSettings = [];
            foreach ($allSettings as $key => $value) {
                if (strpos($key, 'notification_') === 0) {
                    $shortKey = substr($key, strlen('notification_'));
                    $notificationSettings[$shortKey] = $value;
                }
            }

            // إنشاء بيانات الإشعار
            $notificationData = [
                'type' => 'appointment_reminder',
                'recipient_id' => $appointment['customer_id'],
                'recipient_type' => 'customer',
                'title' => 'تذكير بموعدك القادم',
                'message' => "تذكير بموعدك القادم للخدمة: {$appointment['service_name']} مع {$appointment['employee_name']} في {$appointment['branch_name']} بتاريخ " . date('Y-m-d', strtotime($appointment['date'])) . " الساعة " . date('h:i A', strtotime($appointment['start_time'])),
                'related_id' => $appointmentId,
                'related_type' => 'appointment',
                'branch_id' => $appointment['branch_id'],
                'is_read' => 0,
                'is_sent' => 0,
                'send_email' => isset($notificationSettings['enable_email']) && $notificationSettings['enable_email'] == '1' && !empty($appointment['customer_email']) ? 1 : 0,
                'send_sms' => isset($notificationSettings['enable_sms']) && $notificationSettings['enable_sms'] == '1' && !empty($appointment['customer_phone']) ? 1 : 0,
                'send_whatsapp' => isset($notificationSettings['enable_whatsapp']) && $notificationSettings['enable_whatsapp'] == '1' && !empty($appointment['customer_phone']) ? 1 : 0,
                'scheduled_at' => $scheduledAt
            ];

            // إنشاء الإشعار
            return $this->createNotification($notificationData);
        } catch (Exception $e) {
            error_log('خطأ في إنشاء تذكير بموعد: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * الحصول على الإشعارات المجدولة للإرسال
     *
     * @return array قائمة الإشعارات المجدولة
     */
    public function getScheduledNotifications() {
        try {
            $this->db->prepare("SELECT * FROM notifications
                              WHERE is_sent = 0
                              AND scheduled_at IS NOT NULL
                              AND scheduled_at <= NOW()");
            $this->db->execute();
            return $this->db->fetchAll();
        } catch (Exception $e) {
            error_log('خطأ في الحصول على الإشعارات المجدولة: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * تحديث حالة الإشعار
     *
     * @param int $notificationId معرف الإشعار
     * @param array $data بيانات التحديث
     * @return bool نجاح أو فشل التحديث
     */
    public function updateNotification($notificationId, $data) {
        try {
            $updateFields = [];
            $params = [':id' => $notificationId];

            // بناء استعلام التحديث ديناميكيًا
            foreach ($data as $key => $value) {
                $updateFields[] = "$key = :$key";
                $params[":$key"] = $value;
            }

            if (empty($updateFields)) {
                return false;
            }

            $updateQuery = "UPDATE notifications SET " . implode(', ', $updateFields) . ", updated_at = NOW() WHERE id = :id";
            $this->db->prepare($updateQuery);

            // ربط المعلمات
            foreach ($params as $key => $value) {
                $this->db->bind($key, $value);
            }

            return $this->db->execute();
        } catch (Exception $e) {
            error_log('خطأ في تحديث الإشعار: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * تحديث حالة الإشعار إلى مرسل
     *
     * @param int $notificationId معرف الإشعار
     * @return bool نجاح أو فشل التحديث
     */
    public function markAsSent($notificationId) {
        return $this->updateNotification($notificationId, [
            'is_sent' => 1,
            'sent_at' => date('Y-m-d H:i:s')
        ]);
    }

    /**
     * تحديث حالة الإشعار إلى مقروء
     *
     * @param int $notificationId معرف الإشعار
     * @return bool نجاح أو فشل التحديث
     */
    public function markAsRead($notificationId) {
        return $this->updateNotification($notificationId, [
            'is_read' => 1
        ]);
    }

    /**
     * إرسال الإشعارات المجدولة
     *
     * @return array إحصائيات الإرسال
     */
    public function sendScheduledNotifications() {
        $stats = [
            'total' => 0,
            'sent' => 0,
            'failed' => 0
        ];

        try {
            // الحصول على الإشعارات المجدولة
            $notifications = $this->getScheduledNotifications();
            $stats['total'] = count($notifications);

            foreach ($notifications as $notification) {
                try {
                    // إرسال الإشعار
                    $sent = $this->sendNotification($notification);

                    if ($sent) {
                        // تحديث حالة الإشعار
                        $this->markAsSent($notification['id']);
                        $stats['sent']++;
                    } else {
                        $stats['failed']++;
                    }
                } catch (Exception $e) {
                    error_log('خطأ في إرسال الإشعار #' . $notification['id'] . ': ' . $e->getMessage());
                    $stats['failed']++;
                }
            }

            return $stats;
        } catch (Exception $e) {
            error_log('خطأ في إرسال الإشعارات المجدولة: ' . $e->getMessage());
            return $stats;
        }
    }

    /**
     * إرسال إشعار
     *
     * @param array $notification بيانات الإشعار
     * @return bool نجاح أو فشل الإرسال
     */
    private function sendNotification($notification) {
        try {
            $success = true;

            // إرسال بريد إلكتروني إذا كان مطلوبًا
            if ($notification['send_email']) {
                $success = $success && $this->sendEmailNotification($notification);
            }

            // إرسال رسالة نصية إذا كان مطلوبًا
            if ($notification['send_sms']) {
                $success = $success && $this->sendSmsNotification($notification);
            }

            // إرسال رسالة WhatsApp إذا كان مطلوبًا
            if (isset($notification['send_whatsapp']) && $notification['send_whatsapp']) {
                $success = $success && $this->sendWhatsAppNotification($notification);
            }

            return $success;
        } catch (Exception $e) {
            error_log('خطأ في إرسال الإشعار: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * إرسال إشعار بالبريد الإلكتروني
     *
     * @param array $notification بيانات الإشعار
     * @return bool نجاح أو فشل الإرسال
     */
    private function sendEmailNotification($notification) {
        try {
            // الحصول على بريد المستلم
            $recipientEmail = $this->getRecipientEmail($notification['recipient_id'], $notification['recipient_type']);

            if (!$recipientEmail) {
                error_log('لا يوجد بريد إلكتروني للمستلم: ' . $notification['recipient_id'] . ' من النوع: ' . $notification['recipient_type']);
                return false;
            }

            // تسجيل محاولة الإرسال
            $logDir = dirname(__DIR__, 2) . '/logs';
            if (!is_dir($logDir)) {
                mkdir($logDir, 0755, true);
            }

            $logFile = $logDir . '/email_notifications_' . date('Y-m-d') . '.log';
            $timestamp = date('Y-m-d H:i:s');
            $message = "[$timestamp] محاولة إرسال بريد إلكتروني للإشعار #" . $notification['id'] . " إلى: $recipientEmail";
            file_put_contents($logFile, $message . PHP_EOL, FILE_APPEND);

            // هنا يمكن إضافة كود لإرسال البريد الإلكتروني باستخدام PHPMailer أو أي مكتبة أخرى
            // مثال: return sendEmail($recipientEmail, $notification['title'], $notification['message']);

            // للاختبار، نعتبر أن الإرسال نجح
            return true;
        } catch (Exception $e) {
            error_log('خطأ في إرسال إشعار بالبريد الإلكتروني: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * إرسال إشعار برسالة نصية
     *
     * @param array $notification بيانات الإشعار
     * @return bool نجاح أو فشل الإرسال
     */
    private function sendSmsNotification($notification) {
        try {
            // الحصول على رقم هاتف المستلم
            $recipientPhone = $this->getRecipientPhone($notification['recipient_id'], $notification['recipient_type']);

            if (!$recipientPhone) {
                error_log('لا يوجد رقم هاتف للمستلم: ' . $notification['recipient_id'] . ' من النوع: ' . $notification['recipient_type']);
                return false;
            }

            // تسجيل محاولة الإرسال
            $logDir = dirname(__DIR__, 2) . '/logs';
            if (!is_dir($logDir)) {
                mkdir($logDir, 0755, true);
            }

            $logFile = $logDir . '/sms_notifications_' . date('Y-m-d') . '.log';
            $timestamp = date('Y-m-d H:i:s');
            $message = "[$timestamp] محاولة إرسال رسالة نصية للإشعار #" . $notification['id'] . " إلى: $recipientPhone";
            file_put_contents($logFile, $message . PHP_EOL, FILE_APPEND);

            // هنا يمكن إضافة كود لإرسال الرسالة النصية باستخدام خدمة SMS
            // مثال: return sendSMS($recipientPhone, $notification['message']);

            // للاختبار، نعتبر أن الإرسال نجح
            return true;
        } catch (Exception $e) {
            error_log('خطأ في إرسال إشعار برسالة نصية: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * الحصول على بريد المستلم
     *
     * @param int $recipientId معرف المستلم
     * @param string $recipientType نوع المستلم
     * @return string|null بريد المستلم
     */
    private function getRecipientEmail($recipientId, $recipientType) {
        try {
            if ($recipientType === 'customer') {
                $this->db->prepare("SELECT email FROM customers WHERE id = :id");
            } else {
                $this->db->prepare("SELECT email FROM users WHERE id = :id");
            }

            $this->db->bind(':id', $recipientId);
            return $this->db->fetchColumn();
        } catch (Exception $e) {
            error_log('خطأ في الحصول على بريد المستلم: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * الحصول على رقم هاتف المستلم
     *
     * @param int $recipientId معرف المستلم
     * @param string $recipientType نوع المستلم
     * @return string|null رقم هاتف المستلم
     */
    private function getRecipientPhone($recipientId, $recipientType) {
        try {
            if ($recipientType === 'customer') {
                $this->db->prepare("SELECT phone FROM customers WHERE id = :id");
            } else {
                // افتراض أن المستخدمين لديهم حقل هاتف
                $this->db->prepare("SELECT phone FROM users WHERE id = :id");
            }

            $this->db->bind(':id', $recipientId);
            return $this->db->fetchColumn();
        } catch (Exception $e) {
            error_log('خطأ في الحصول على رقم هاتف المستلم: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * إرسال إشعار عبر WhatsApp
     *
     * @param array $notification بيانات الإشعار
     * @return bool نجاح أو فشل الإرسال
     */
    private function sendWhatsAppNotification($notification) {
        try {
            // الحصول على رقم هاتف المستلم
            $recipientPhone = $this->getRecipientPhone($notification['recipient_id'], $notification['recipient_type']);

            if (!$recipientPhone) {
                error_log('لا يوجد رقم هاتف للمستلم: ' . $notification['recipient_id'] . ' من النوع: ' . $notification['recipient_type']);
                return false;
            }

            // الحصول على رمز البلد من الفرع
            $branchId = $notification['branch_id'] ?? null;
            $countryCode = '+20'; // القيمة الافتراضية

            if ($branchId) {
                $branchModel = new Branch($this->db);
                $branch = $branchModel->getBranchById($branchId);
                if ($branch && isset($branch['country_code'])) {
                    $countryCode = $branch['country_code'];
                }
            }

            // تسجيل محاولة الإرسال
            $logDir = dirname(__DIR__, 2) . '/logs';
            if (!is_dir($logDir)) {
                mkdir($logDir, 0755, true);
            }

            $logFile = $logDir . '/whatsapp_notifications_' . date('Y-m-d') . '.log';
            $timestamp = date('Y-m-d H:i:s');
            $message = "[$timestamp] محاولة إرسال رسالة WhatsApp للإشعار #" . $notification['id'] . " إلى: $recipientPhone";
            file_put_contents($logFile, $message . PHP_EOL, FILE_APPEND);

            // إنشاء كائن خدمة WhatsApp
            $whatsappService = new WhatsAppAutomation($this->db);

            // إرسال الرسالة
            $result = $whatsappService->sendMessage($recipientPhone, $notification['message'], [
                'country_code' => $countryCode,
                'notification_id' => $notification['id'],
                'related_id' => $notification['related_id'],
                'related_type' => $notification['related_type']
            ]);

            // تسجيل نتيجة الإرسال
            $resultMessage = "[$timestamp] " . ($result ? 'تم إرسال رسالة WhatsApp بنجاح' : 'فشل إرسال رسالة WhatsApp') . " للإشعار #" . $notification['id'];
            file_put_contents($logFile, $resultMessage . PHP_EOL, FILE_APPEND);

            return $result;
        } catch (Exception $e) {
            error_log('خطأ في إرسال إشعار عبر WhatsApp: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * إنشاء تذكيرات للمواعيد القادمة
     *
     * @param int $daysAhead عدد الأيام المستقبلية للبحث عن المواعيد
     * @return array إحصائيات إنشاء التذكيرات
     */
    public function createAppointmentReminders($daysAhead = 7) {
        $stats = [
            'total' => 0,
            'created' => 0,
            'failed' => 0
        ];

        try {
            // الحصول على إعدادات الإشعارات
            $settingsModel = new Settings($this->db);
            $allSettings = $settingsModel->getAllSettings();

            // استخراج إعدادات الإشعارات
            $notificationSettings = [];
            foreach ($allSettings as $key => $value) {
                if (strpos($key, 'notification_') === 0) {
                    $shortKey = substr($key, strlen('notification_'));
                    $notificationSettings[$shortKey] = $value;
                }
            }

            // التحقق من تفعيل تذكير المواعيد
            if (!isset($notificationSettings['appointment_reminder']) || $notificationSettings['appointment_reminder'] != '1') {
                return $stats;
            }

            // الحصول على عدد ساعات التذكير قبل الموعد
            $hoursBeforeAppointment = isset($notificationSettings['reminder_hours']) ? intval($notificationSettings['reminder_hours']) : 24;

            // الحصول على المواعيد القادمة
            $startDate = date('Y-m-d');
            $endDate = date('Y-m-d', strtotime("+$daysAhead days"));

            $this->db->prepare("SELECT a.* FROM appointments a
                              WHERE a.date BETWEEN :start_date AND :end_date
                              AND a.status = 'booked'
                              AND a.customer_id IS NOT NULL");
            $this->db->bind(':start_date', $startDate);
            $this->db->bind(':end_date', $endDate);
            $appointments = $this->db->fetchAll();

            $stats['total'] = count($appointments);

            foreach ($appointments as $appointment) {
                try {
                    // التحقق من وجود تذكير سابق لهذا الموعد
                    $this->db->prepare("SELECT COUNT(*) FROM notifications
                                      WHERE related_id = :appointment_id
                                      AND related_type = 'appointment'
                                      AND type = 'appointment_reminder'");
                    $this->db->bind(':appointment_id', $appointment['id']);
                    $existingReminders = $this->db->fetchColumn();

                    if ($existingReminders > 0) {
                        // تم إنشاء تذكير بالفعل لهذا الموعد
                        continue;
                    }

                    // إنشاء تذكير للموعد
                    $this->createAppointmentReminder($appointment['id'], $hoursBeforeAppointment);
                    $stats['created']++;
                } catch (Exception $e) {
                    error_log('خطأ في إنشاء تذكير للموعد #' . $appointment['id'] . ': ' . $e->getMessage());
                    $stats['failed']++;
                }
            }

            return $stats;
        } catch (Exception $e) {
            error_log('خطأ في إنشاء تذكيرات المواعيد: ' . $e->getMessage());
            return $stats;
        }
    }
}
