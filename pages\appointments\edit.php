<?php
/**
 * صفحة تعديل موعد
 * تتيح للمستخدم تعديل موعد موجود في النظام
 */

// تعريف ثابت لمنع الوصول المباشر للملفات المرتبطة
define('BASEPATH', true);

// استدعاء ملفات التهيئة
require_once '../../config/init.php';

// التحقق من تسجيل الدخول
redirectIfNotLoggedIn();

// التحقق من وجود صلاحية تعديل المواعيد
if (!hasPermission('appointments_edit')) {
    setErrorMessage(ACCESS_DENIED_MSG);
    redirect(BASE_URL . 'pages/appointments/index.php');
    exit;
}

// إنشاء كائنات النماذج
$appointmentModel = new Appointment($db);
$customerModel = new Customer($db);
$serviceModel = new Service($db);
$employeeModel = new Employee($db);
$branchModel = new Branch($db);

// التحقق من وجود معرف الموعد
if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    setErrorMessage('معرف الموعد غير صحيح');
    redirect(BASE_URL . 'pages/appointments/index.php');
    exit;
}

$appointmentId = intval($_GET['id']);

// استرجاع بيانات الموعد
$appointmentData = $appointmentModel->getAppointmentById($appointmentId);

// التحقق من وجود الموعد
if (!$appointmentData) {
    setErrorMessage('الموعد غير موجود');
    redirect(BASE_URL . 'pages/appointments/index.php');
    exit;
}

// التحقق من صلاحية تعديل الموعد (فقط للمدير أو مدير الفرع الذي ينتمي إليه الموعد)
if ($_SESSION['user_role'] != ROLE_ADMIN && $_SESSION['user_branch_id'] != $appointmentData['branch_id']) {
    setErrorMessage('ليس لديك صلاحية لتعديل هذا الموعد');
    redirect(BASE_URL . 'pages/appointments/index.php');
    exit;
}

// استرجاع قائمة العملاء
$customers = $customerModel->getCustomers(['is_active' => 1]);

// استرجاع قائمة الخدمات
$services = $serviceModel->getServices(['is_active' => 1]);

// استرجاع قائمة الموظفين
$employees = $employeeModel->getEmployees(['is_active' => 1]);

// استرجاع قائمة الفروع للمدير
$branches = [];
if ($_SESSION['user_role'] == ROLE_ADMIN || $_SESSION['user_role'] == ROLE_MANAGER) {
    $branches = $branchModel->getBranches(['is_active' => 1]);
}

// متغيرات الصفحة
$pageTitle = 'تعديل موعد';
$formAction = 'edit';

// معالجة نموذج تعديل موعد
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        // التحقق من البيانات المطلوبة
        $requiredFields = ['customer_id', 'service_id', 'employee_id', 'date', 'start_time', 'branch_id'];
        foreach ($requiredFields as $field) {
            if (empty($_POST[$field])) {
                throw new Exception("الحقل $field مطلوب");
            }
        }
        
        // جمع بيانات الموعد
        $updatedAppointmentData = [
            'id' => $appointmentId,
            'customer_id' => intval($_POST['customer_id']),
            'service_id' => intval($_POST['service_id']),
            'employee_id' => intval($_POST['employee_id']),
            'date' => sanitizeInput($_POST['date']),
            'start_time' => sanitizeInput($_POST['start_time']),
            'status' => sanitizeInput($_POST['status']),
            'notes' => isset($_POST['notes']) ? sanitizeInput($_POST['notes']) : null,
            'branch_id' => intval($_POST['branch_id'])
        ];
        
        // تحديث الموعد
        $appointmentModel->updateAppointment($updatedAppointmentData);
        
        // رسالة نجاح وإعادة التوجيه
        setSuccessMessage('تم تحديث الموعد بنجاح');
        redirect(BASE_URL . 'pages/appointments/index.php');
        exit;
    } catch (Exception $e) {
        setErrorMessage($e->getMessage());
    }
}

// استدعاء قالب الهيدر
include '../../includes/templates/header.php';
?>

<style>
    .time-slots {
        display: flex;
        flex-wrap: wrap;
        gap: 10px;
        margin-top: 15px;
    }
    
    .time-slot {
        padding: 8px 12px;
        border: 1px solid #ddd;
        border-radius: 4px;
        cursor: pointer;
        transition: all 0.2s;
    }
    
    .time-slot:hover {
        background-color: #f8f9fa;
    }
    
    .time-slot.selected {
        background-color: #007bff;
        color: white;
        border-color: #007bff;
    }
    
    .time-slot.unavailable {
        background-color: #f8d7da;
        color: #721c24;
        border-color: #f5c6cb;
        cursor: not-allowed;
        opacity: 0.6;
    }
</style>

<!-- المحتوى الرئيسي للصفحة -->
<div class="container-fluid">
    <!-- رأس الصفحة -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h5 mb-0">
            <i class="fas fa-edit"></i> تعديل موعد
        </h1>
        <a href="index.php" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-right ml-1"></i> العودة للمواعيد
        </a>
    </div>
    
    <!-- محتوى الصفحة -->
    <div class="row">
        <div class="col-md-8">
            <!-- نموذج تعديل موعد -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">بيانات الموعد</h5>
                </div>
                <div class="card-body">
                    <form id="appointmentForm" method="post" action="">
                        <!-- بيانات العميل -->
                        <div class="mb-4">
                            <h6 class="mb-3">
                                <i class="fas fa-user"></i> بيانات العميل
                            </h6>
                            <div class="row">
                                <div class="col-md-8">
                                    <div class="mb-3">
                                        <label for="customer_id" class="form-label required-field">العميل</label>
                                        <select class="form-select" id="customer_id" name="customer_id" required>
                                            <option value="">اختر العميل</option>
                                            <?php foreach ($customers as $customer): ?>
                                                <option value="<?php echo $customer['id']; ?>" <?php echo ($appointmentData['customer_id'] == $customer['id']) ? 'selected' : ''; ?>>
                                                    <?php echo $customer['name']; ?> (<?php echo $customer['phone']; ?>)
                                                </option>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3 d-flex align-items-end h-100">
                                        <button type="button" class="btn btn-outline-primary add-customer-btn">
                                            <i class="fas fa-plus"></i> إضافة عميل جديد
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- بيانات الخدمة -->
                        <div class="mb-4">
                            <h6 class="mb-3">
                                <i class="fas fa-concierge-bell"></i> بيانات الخدمة
                            </h6>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="service_id" class="form-label required-field">الخدمة</label>
                                        <select class="form-select" id="service_id" name="service_id" required>
                                            <option value="">اختر الخدمة</option>
                                            <?php foreach ($services as $service): ?>
                                                <option value="<?php echo $service['id']; ?>" <?php echo ($appointmentData['service_id'] == $service['id']) ? 'selected' : ''; ?>>
                                                    <?php echo $service['name']; ?> (<?php echo formatCurrency($service['price']); ?>)
                                                </option>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="employee_id" class="form-label required-field">الموظف</label>
                                        <select class="form-select" id="employee_id" name="employee_id" required>
                                            <option value="">اختر الموظف</option>
                                            <?php foreach ($employees as $employee): ?>
                                                <option value="<?php echo $employee['id']; ?>" <?php echo ($appointmentData['employee_id'] == $employee['id']) ? 'selected' : ''; ?>>
                                                    <?php echo $employee['name']; ?>
                                                </option>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- بيانات الموعد -->
                        <div class="mb-4">
                            <h6 class="mb-3">
                                <i class="fas fa-calendar-alt"></i> بيانات الموعد
                            </h6>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="date" class="form-label required-field">التاريخ</label>
                                        <input type="text" class="form-control date-picker" id="date" name="date" value="<?php echo date('Y/m/d', strtotime($appointmentData['date'])); ?>" required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="start_time" class="form-label required-field">الوقت</label>
                                        <input type="hidden" id="start_time" name="start_time" value="<?php echo $appointmentData['start_time']; ?>" required>
                                        <div class="d-flex">
                                            <button type="button" class="btn btn-outline-primary me-2" id="showTimeSlots">
                                                <i class="fas fa-clock"></i> اختر وقت
                                            </button>
                                            <span id="selectedTime" class="form-control-plaintext"><?php echo !empty($appointmentData['start_time']) ? date('h:i A', strtotime($appointmentData['start_time'])) : 'لم يتم اختيار وقت'; ?></span>
                                        </div>
                                        <!-- فترات الوقت المتاحة -->
                                        <div id="timeSlots" class="time-slots" style="display: none;">
                                            <!-- سيتم تعبئتها عبر AJAX -->
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="branch_id" class="form-label required-field">الفرع</label>
                                        <?php if ($_SESSION['user_role'] == ROLE_ADMIN || $_SESSION['user_role'] == ROLE_MANAGER): ?>
                                            <select class="form-select" id="branch_id" name="branch_id" required>
                                                <option value="">اختر الفرع</option>
                                                <?php foreach ($branches as $branch): ?>
                                                    <option value="<?php echo $branch['id']; ?>" <?php echo ($appointmentData['branch_id'] == $branch['id']) ? 'selected' : ''; ?>>
                                                        <?php echo $branch['name']; ?>
                                                    </option>
                                                <?php endforeach; ?>
                                            </select>
                                        <?php else: ?>
                                            <input type="hidden" id="branch_id" name="branch_id" value="<?php echo $_SESSION['user_branch_id']; ?>">
                                            <input type="text" class="form-control" value="<?php echo $_SESSION['user_branch_name']; ?>" readonly>
                                        <?php endif; ?>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="status" class="form-label required-field">حالة الموعد</label>
                                        <select class="form-select" id="status" name="status" required>
                                            <option value="booked" <?php echo ($appointmentData['status'] == 'booked') ? 'selected' : ''; ?>>محجوز</option>
                                            <option value="waiting" <?php echo ($appointmentData['status'] == 'waiting') ? 'selected' : ''; ?>>في الانتظار</option>
                                            <option value="completed" <?php echo ($appointmentData['status'] == 'completed') ? 'selected' : ''; ?>>مكتمل</option>
                                            <option value="cancelled" <?php echo ($appointmentData['status'] == 'cancelled') ? 'selected' : ''; ?>>ملغي</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="notes" class="form-label">ملاحظات</label>
                                <textarea class="form-control" id="notes" name="notes" rows="3"><?php echo $appointmentData['notes']; ?></textarea>
                            </div>
                        </div>
                        
                        <!-- أزرار النموذج -->
                        <div class="d-flex justify-content-end">
                            <button type="button" class="btn btn-outline-secondary me-2" onclick="window.location.href='index.php'">إلغاء</button>
                            <button type="submit" class="btn btn-primary">حفظ التغييرات</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <!-- معلومات العميل -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-user-circle"></i> معلومات العميل
                    </h5>
                </div>
                <div class="card-body">
                    <div id="customerInfo">
                        <p class="text-muted text-center">اختر عميل لعرض معلوماته</p>
                    </div>
                </div>
            </div>
            
            <!-- جدول المواعيد اليوم -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-calendar-day"></i> مواعيد اليوم
                    </h5>
                </div>
                <div class="card-body p-0">
                    <div id="todayAppointments" class="list-group list-group-flush">
                        <!-- سيتم تعبئتها عبر AJAX -->
                        <div class="text-center py-4">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">جاري التحميل...</span>
                            </div>
                            <p class="mt-2">جاري تحميل مواعيد اليوم...</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- نموذج فترات الوقت المتاحة -->
<div class="modal fade" id="timeSlotsModal" tabindex="-1" aria-labelledby="timeSlotsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="timeSlotsModalLabel">اختر وقت الموعد</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="إغلاق"></button>
            </div>
            <div class="modal-body">
                <div id="modalTimeSlots" class="time-slots">
                    <!-- سيتم تعبئتها عبر AJAX -->
                    <div class="text-center py-4">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">جاري التحميل...</span>
                        </div>
                        <p class="mt-2">جاري تحميل الأوقات المتاحة...</p>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                <button type="button" class="btn btn-primary" id="selectTimeBtn" disabled>اختيار الوقت</button>
            </div>
        </div>
    </div>
</div>

<!-- نموذج إضافة عميل جديد -->
<div class="modal fade" id="addCustomerModal" tabindex="-1" aria-labelledby="addCustomerModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addCustomerModalLabel">إضافة عميل جديد</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="إغلاق"></button>
            </div>
            <div class="modal-body">
                <form id="addCustomerForm">
                    <div class="mb-3">
                        <label for="customer_name" class="form-label required-field">اسم العميل</label>
                        <input type="text" class="form-control" id="customer_name" name="name" required>
                    </div>
                    <div class="mb-3">
                        <label for="customer_phone" class="form-label required-field">رقم الهاتف</label>
                        <input type="text" class="form-control" id="customer_phone" name="phone" required>
                    </div>
                    <div class="mb-3">
                        <label for="customer_email" class="form-label">البريد الإلكتروني</label>
                        <input type="email" class="form-control" id="customer_email" name="email">
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" id="saveCustomerBtn">حفظ</button>
            </div>
        </div>
    </div>
</div>

<?php include '../../includes/templates/footer.php'; ?>

<script>
    $(document).ready(function() {
        // تهيئة اختيار التاريخ
        $('.date-picker').daterangepicker({
            singleDatePicker: true,
            showDropdowns: true,
            minDate: moment(),
            locale: {
                format: 'YYYY/MM/DD',
                applyLabel: 'تطبيق',
                cancelLabel: 'إلغاء',
                daysOfWeek: ['أح', 'إث', 'ثل', 'أر', 'خم', 'جم', 'سب'],
                monthNames: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو', 'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'],
                firstDay: 0
            }
        });
        
        // عرض معلومات العميل عند اختياره
        $('#customer_id').on('change', function() {
            var customerId = $(this).val();
            if (customerId) {
                loadCustomerInfo(customerId);
            } else {
                $('#customerInfo').html('<p class="text-muted text-center">اختر عميل لعرض معلوماته</p>');
            }
        });
        
        // تحميل معلومات العميل
        function loadCustomerInfo(customerId) {
            $.ajax({
                url: API_URL + 'customers.php',
                type: 'GET',
                data: { action: 'get_customer', id: customerId },
                dataType: 'json',
                success: function(response) {
                    if (response.success) {
                        var customer = response.data;
                        var html = `
                            <div class="text-center mb-3">
                                <div class="avatar avatar-lg">
                                    <span class="avatar-text rounded-circle">${customer.name.charAt(0)}</span>
                                </div>
                                <h5 class="mt-2 mb-0">${customer.name}</h5>
                                <p class="text-muted">${customer.phone}</p>
                            </div>
                            <hr>
                            <div class="customer-details">
                                <p><i class="fas fa-envelope me-2"></i> ${customer.email || 'غير متوفر'}</p>
                                <p><i class="fas fa-calendar-check me-2"></i> ${customer.total_visits || 0} زيارة</p>
                                <p><i class="fas fa-clock me-2"></i> آخر زيارة: ${customer.last_visit || 'لا يوجد'}</p>
                            </div>
                        `;
                        $('#customerInfo').html(html);
                    } else {
                        showAlert('error', 'خطأ في تحميل بيانات العميل');
                    }
                },
                error: function() {
                    showAlert('error', 'حدث خطأ في الاتصال بالخادم');
                }
            });
        }
        
        // تحميل مواعيد اليوم
        function loadTodayAppointments() {
            $.ajax({
                url: API_URL + 'appointments.php',
                type: 'GET',
                data: { action: 'get_today_appointments' },
                dataType: 'json',
                success: function(response) {
                    if (response.success) {
                        var appointments = response.data;
                        var html = '';
                        
                        if (appointments.length === 0) {
                            html = '<div class="list-group-item text-center py-3">لا توجد مواعيد لهذا اليوم</div>';
                        } else {
                            appointments.forEach(function(appointment) {
                                var statusClass = getStatusClass(appointment.status);
                                html += `
                                    <a href="edit.php?id=${appointment.id}" class="list-group-item list-group-item-action">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <div>
                                                <h6 class="mb-1">${appointment.customer_name}</h6>
                                                <p class="mb-1 small text-muted">${appointment.service_name}</p>
                                            </div>
                                            <div class="text-end">
                                                <span class="badge ${statusClass}">${getStatusText(appointment.status)}</span>
                                                <div class="small">${appointment.time}</div>
                                            </div>
                                        </div>
                                    </a>
                                `;
                            });
                        }
                        
                        $('#todayAppointments').html(html);
                    } else {
                        $('#todayAppointments').html('<div class="list-group-item text-center py-3">خطأ في تحميل المواعيد</div>');
                    }
                },
                error: function() {
                    $('#todayAppointments').html('<div class="list-group-item text-center py-3">حدث خطأ في الاتصال بالخادم</div>');
                }
            });
        }
        
        // الحصول على صنف حالة الموعد
        function getStatusClass(status) {
            switch(status) {
                case 'confirmed': return 'bg-success';
                case 'pending': return 'bg-warning';
                case 'completed': return 'bg-info';
                case 'cancelled': return 'bg-danger';
                default: return 'bg-secondary';
            }
        }
        
        // الحصول على نص حالة الموعد
        function getStatusText(status) {
            switch(status) {
                case 'confirmed': return 'مؤكد';
                case 'pending': return 'قيد الانتظار';
                case 'completed': return 'مكتمل';
                case 'cancelled': return 'ملغي';
                default: return 'غير معروف';
            }
        }
        
        // تحميل الأوقات المتاحة
        $('#appointment_date, #service_id, #employee_id').on('change', function() {
            updateAvailableTimeSlots();
        });
        
        // تحديث الأوقات المتاحة
        function updateAvailableTimeSlots() {
            var date = $('#appointment_date').val();
            var serviceId = $('#service_id').val();
            var employeeId = $('#employee_id').val();
            var appointmentId = <?php echo $appointmentId; ?>;
            
            if (date && serviceId && employeeId) {
                $('#time_slot').prop('disabled', false);
                $('#showTimeSlotsBtn').prop('disabled', false);
            } else {
                $('#time_slot').prop('disabled', true);
                $('#showTimeSlotsBtn').prop('disabled', true);
                return;
            }
        }
        
        // عرض نموذج الأوقات المتاحة
        $('#showTimeSlotsBtn').on('click', function() {
            var date = $('#appointment_date').val();
            var serviceId = $('#service_id').val();
            var employeeId = $('#employee_id').val();
            var appointmentId = <?php echo $appointmentId; ?>;
            
            $('#modalTimeSlots').html(`
                <div class="text-center py-4">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">جاري التحميل...</span>
                    </div>
                    <p class="mt-2">جاري تحميل الأوقات المتاحة...</p>
                </div>
            `);
            
            $('#timeSlotsModal').modal('show');
            
            $.ajax({
                url: API_URL + 'appointments.php',
                type: 'GET',
                data: {
                    action: 'get_available_slots',
                    date: date,
                    service_id: serviceId,
                    employee_id: employeeId,
                    appointment_id: appointmentId
                },
                dataType: 'json',
                success: function(response) {
                    if (response.success) {
                        renderTimeSlots(response.data);
                    } else {
                        $('#modalTimeSlots').html(`<div class="alert alert-danger">${response.message}</div>`);
                    }
                },
                error: function() {
                    $('#modalTimeSlots').html('<div class="alert alert-danger">حدث خطأ في الاتصال بالخادم</div>');
                }
            });
        });
        
        // عرض الأوقات المتاحة
        function renderTimeSlots(slots) {
            if (slots.length === 0) {
                $('#modalTimeSlots').html('<div class="alert alert-info">لا توجد أوقات متاحة في هذا اليوم</div>');
                return;
            }
            
            var html = '<div class="row">';
            slots.forEach(function(slot) {
                var disabled = slot.available ? '' : 'disabled';
                var btnClass = slot.available ? 'btn-outline-primary' : 'btn-outline-secondary';
                
                html += `
                    <div class="col-md-3 col-6 mb-2">
                        <button type="button" class="btn ${btnClass} w-100 time-slot-btn" ${disabled} data-value="${slot.time}">
                            ${slot.time}
                        </button>
                    </div>
                `;
            });
            html += '</div>';
            
            $('#modalTimeSlots').html(html);
            
            // اختيار الوقت
            $('.time-slot-btn').on('click', function() {
                $('.time-slot-btn').removeClass('active');
                $(this).addClass('active');
                $('#selectTimeBtn').prop('disabled', false);
            });
        }
        
        // تأكيد اختيار الوقت
        $('#selectTimeBtn').on('click', function() {
            var selectedTime = $('.time-slot-btn.active').data('value');
            $('#time_slot').val(selectedTime);
            $('#timeSlotsModal').modal('hide');
        });
        
        // إضافة عميل جديد
        $('#addNewCustomerBtn').on('click', function() {
            $('#addCustomerForm')[0].reset();
            $('#addCustomerModal').modal('show');
        });
        
        // حفظ العميل الجديد
        $('#saveCustomerBtn').on('click', function() {
            var formData = $('#addCustomerForm').serialize();
            
            $.ajax({
                url: API_URL + 'customers.php',
                type: 'POST',
                data: formData + '&action=add_customer',
                dataType: 'json',
                success: function(response) {
                    if (response.success) {
                        $('#addCustomerModal').modal('hide');
                        
                        // إضافة العميل الجديد للقائمة
                        var newOption = new Option(response.data.name, response.data.id, true, true);
                        $('#customer_id').append(newOption).trigger('change');
                        
                        showAlert('success', 'تم إضافة العميل بنجاح');
                    } else {
                        showAlert('error', response.message);
                    }
                },
                error: function() {
                    showAlert('error', 'حدث خطأ في الاتصال بالخادم');
                }
            });
        });
        
        // تحميل البيانات الأولية
        loadTodayAppointments();
        
        // إذا كان هناك عميل محدد، قم بتحميل معلوماته
        var selectedCustomerId = $('#customer_id').val();
        if (selectedCustomerId) {
            loadCustomerInfo(selectedCustomerId);
        }
    });
</script>
