<?php

// التحقق من وجود جدول customer_messages
$db->query("SHOW TABLES LIKE 'customer_messages'");
if ($db->rowCount() === 0) {
    $sql = file_get_contents(__DIR__ . '/../database/migrations/create_customer_messages_table.sql');
    try {
        $queries = explode(';', $sql);
        foreach ($queries as $query) {
            if (trim($query) !== '') {
                $db->query($query);
            }
        }
        echo "تم إنشاء جدول customer_messages بنجاح.<br>";
    } catch (Exception $e) {
        echo "خطأ في إنشاء جدول customer_messages: " . $e->getMessage() . "<br>";
    }
}

// العودة إلى الصفحة الرئيسية
header('Location: ' . BASE_URL . 'index.php');
exit; 