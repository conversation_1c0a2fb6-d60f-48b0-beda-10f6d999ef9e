<?php
/**
 * نقطة النهاية الخاصة بالخدمات
 * تتعامل مع العمليات المتعلقة بإدارة الخدمات في النظام
 */

// تعريف ثابت لمنع الوصول المباشر للملف
define('BASEPATH', true);

// استدعاء ملفات التهيئة
require_once __DIR__ . '/../config/init.php';

// التحقق من تسجيل الدخول
requireLogin();
// التحقق من صلاحيات المستخدم
hasPermission('services_view');

// إعداد الاستجابة JSON
header('Content-Type: application/json');

try {
    // إنشاء كائن الخدمة
    $serviceObj = new Service($db);

    // تحديد الإجراء
    $action = $_GET['action'] ?? 'list';

    switch ($action) {
        // قائمة الخدمات
        case 'list':
            // إعداد الفلاتر
            $filters = [];
            
            // فلاتر البحث
            if (isset($_GET['search'])) {
                $filters['search'] = trim($_GET['search']);
            }
            
            if (isset($_GET['category_id'])) {
                $filters['category_id'] = intval($_GET['category_id']);
            }
            
            if (isset($_GET['branch_id'])) {
                $filters['branch_id'] = intval($_GET['branch_id']);
            }
            
            if (isset($_GET['is_active'])) {
                $filters['is_active'] = intval($_GET['is_active']);
            }
            
            if (isset($_GET['min_price'])) {
                $filters['min_price'] = floatval($_GET['min_price']);
            }
            
            if (isset($_GET['max_price'])) {
                $filters['max_price'] = floatval($_GET['max_price']);
            }
            
            if (isset($_GET['employee_id'])) {
                $filters['employee_id'] = intval($_GET['employee_id']);
            }
            
            // الترتيب
            if (isset($_GET['sort_by'])) {
                $filters['sort_by'] = trim($_GET['sort_by']);
            }
            
            if (isset($_GET['sort_dir'])) {
                $filters['sort_dir'] = trim($_GET['sort_dir']);
            }
            
            // الصفحة والحد
            $page = isset($_GET['page']) ? intval($_GET['page']) : 1;
            $limit = isset($_GET['limit']) ? intval($_GET['limit']) : 10;
            $offset = ($page - 1) * $limit;
            
            $filters['limit'] = $limit;
            $filters['offset'] = $offset;
            
            // جلب الخدمات
            $services = $serviceObj->getServices($filters);
            $totalServices = $serviceObj->getServicesCount($filters);
            
            // إعداد الاستجابة
            echo json_encode([
                'status' => 'success',
                'data' => $services,
                'pagination' => [
                    'total' => $totalServices,
                    'page' => $page,
                    'limit' => $limit,
                    'total_pages' => ceil($totalServices / $limit)
                ]
            ]);
            break;
        
        // عرض خدمة محددة
        case 'view':
            // التحقق من وجود المعرف
            if (!isset($_GET['id'])) {
                throw new Exception('معرف الخدمة مطلوب');
            }
            
            $serviceId = intval($_GET['id']);
            $service = $serviceObj->getServiceById($serviceId);
            
            if (!$service) {
                throw new Exception('الخدمة غير موجودة');
            }
            
            echo json_encode([
                'status' => 'success',
                'data' => $service
            ]);
            break;
        
        // إضافة خدمة جديدة
        case 'add':
            // التحقق من الصلاحية
            hasPermission('services_add');
            
            // التحقق من البيانات المطلوبة
            if (!isset($_POST['name']) || empty(trim($_POST['name']))) {
                throw new Exception('اسم الخدمة مطلوب');
            }
            
            if (!isset($_POST['price']) || !is_numeric($_POST['price'])) {
                throw new Exception('السعر مطلوب ويجب أن يكون رقمًا');
            }
            
            // تحضير بيانات الخدمة
            $serviceData = [
                'name' => trim($_POST['name']),
                'description' => $_POST['description'] ?? null,
                'price' => floatval($_POST['price']),
                'duration' => isset($_POST['duration']) ? intval($_POST['duration']) : 30,
                'category_id' => !empty($_POST['category_id']) ? intval($_POST['category_id']) : null,
                'is_active' => isset($_POST['is_active']) ? intval($_POST['is_active']) : 1,
                'branch_id' => !empty($_POST['branch_id']) ? intval($_POST['branch_id']) : null
            ];
            
            // معالجة الموظفين المرتبطين
            if (!empty($_POST['employees'])) {
                $serviceData['employees'] = array_map('intval', explode(',', $_POST['employees']));
            }
            
            // إضافة الخدمة
            $serviceId = $serviceObj->addService($serviceData);
            
            echo json_encode([
                'status' => 'success',
                'message' => 'تمت إضافة الخدمة بنجاح',
                'data' => ['id' => $serviceId]
            ]);
            break;
        
        // تحديث خدمة
        case 'update':
            // التحقق من الصلاحية
            hasPermission('services_edit');
            
            // التحقق من وجود المعرف
            if (!isset($_POST['id'])) {
                throw new Exception('معرف الخدمة مطلوب');
            }
            
            $serviceId = intval($_POST['id']);
            
            // التحقق من البيانات المطلوبة
            if (!isset($_POST['name']) || empty(trim($_POST['name']))) {
                throw new Exception('اسم الخدمة مطلوب');
            }
            
            if (!isset($_POST['price']) || !is_numeric($_POST['price'])) {
                throw new Exception('السعر مطلوب ويجب أن يكون رقمًا');
            }
            
            // تحضير بيانات الخدمة
            $serviceData = [
                'name' => trim($_POST['name']),
                'description' => $_POST['description'] ?? null,
                'price' => floatval($_POST['price']),
                'duration' => isset($_POST['duration']) ? intval($_POST['duration']) : 30,
                'category_id' => !empty($_POST['category_id']) ? intval($_POST['category_id']) : null,
                'is_active' => isset($_POST['is_active']) ? intval($_POST['is_active']) : 1,
                'branch_id' => !empty($_POST['branch_id']) ? intval($_POST['branch_id']) : null
            ];
            
            // معالجة الموظفين المرتبطين
            if (isset($_POST['employees'])) {
                $serviceData['employees'] = !empty($_POST['employees']) 
                    ? array_map('intval', explode(',', $_POST['employees'])) 
                    : [];
            }
            
            // تحديث الخدمة
            $serviceObj->updateService($serviceId, $serviceData);
            
            echo json_encode([
                'status' => 'success',
                'message' => 'تم تحديث الخدمة بنجاح'
            ]);
            break;
        
        // حذف خدمة
        case 'delete':
            // التحقق من الصلاحية
            hasPermission('services_delete');
            
            // التحقق من وجود المعرف
            if (!isset($_POST['id'])) {
                throw new Exception('معرف الخدمة مطلوب');
            }
            
            $serviceId = intval($_POST['id']);
            
            // حذف الخدمة
            $serviceObj->deleteService($serviceId);
            
            echo json_encode([
                'status' => 'success',
                'message' => 'تم حذف الخدمة بنجاح'
            ]);
            break;
        
        // التقارير
        case 'reports':
            // التحقق من الصلاحية
            hasPermission('reports_view');
            
            $reportType = $_GET['report_type'] ?? 'summary';
            
            switch ($reportType) {
                // تقرير ملخص الخدمات
                case 'summary':
                    $filters = [];
                    
                    if (isset($_GET['branch_id'])) {
                        $filters['branch_id'] = intval($_GET['branch_id']);
                    }
                    
                    if (isset($_GET['start_date'])) {
                        $filters['start_date'] = trim($_GET['start_date']);
                    }
                    
                    if (isset($_GET['end_date'])) {
                        $filters['end_date'] = trim($_GET['end_date']);
                    }
                    
                    if (isset($_GET['month'])) {
                        $filters['month'] = intval($_GET['month']);
                    }
                    
                    if (isset($_GET['year'])) {
                        $filters['year'] = intval($_GET['year']);
                    }
                    
                    // توليد التقرير
                    $report = $serviceObj->generateServicesReport($filters);
                    
                    echo json_encode([
                        'status' => 'success',
                        'data' => $report
                    ]);
                    break;
                
                // مقارنة الفترات
                case 'compare_periods':
                    if (!isset($_GET['period1_start']) || !isset($_GET['period1_end']) ||
                        !isset($_GET['period2_start']) || !isset($_GET['period2_end'])) {
                        throw new Exception('يجب تحديد الفترات للمقارنة');
                    }
                    
                    $period1 = [
                        'start_date' => trim($_GET['period1_start']),
                        'end_date' => trim($_GET['period1_end'])
                    ];
                    
                    $period2 = [
                        'start_date' => trim($_GET['period2_start']),
                        'end_date' => trim($_GET['period2_end'])
                    ];
                    
                    $branchId = isset($_GET['branch_id']) ? intval($_GET['branch_id']) : null;
                    
                    // مقارنة الفترات
                    $comparisonReport = $serviceObj->compareServicesPeriods($period1, $period2, $branchId);
                    
                    echo json_encode([
                        'status' => 'success',
                        'data' => $comparisonReport
                    ]);
                    break;
                
                default:
                    throw new Exception('نوع التقرير غير معروف');
            }
            break;
        
        // فئات الخدمات
        case 'categories':
            $categoryAction = $_GET['category_action'] ?? 'list';
            
            switch ($categoryAction) {
                // عرض الفئات
                case 'list':
                    $categories = $serviceObj->getServiceCategories();
                    
                    echo json_encode([
                        'status' => 'success',
                        'data' => $categories
                    ]);
                    break;
                
                // إضافة فئة
                case 'add':
                    hasPermission('services_edit');
                    
                    if (!isset($_POST['name']) || empty(trim($_POST['name']))) {
                        throw new Exception('اسم الفئة مطلوب');
                    }
                    
                    $categoryData = [
                        'name' => trim($_POST['name']),
                        'description' => $_POST['description'] ?? null
                    ];
                    
                    $categoryId = $serviceObj->addServiceCategory($categoryData);
                    
                    echo json_encode([
                        'status' => 'success',
                        'message' => 'تمت إضافة الفئة بنجاح',
                        'data' => ['id' => $categoryId]
                    ]);
                    break;
                
                // تحديث فئة
                case 'update':
                    hasPermission('services_edit');
                    
                    if (!isset($_POST['id'])) {
                        throw new Exception('معرف الفئة مطلوب');
                    }
                    
                    if (!isset($_POST['name']) || empty(trim($_POST['name']))) {
                        throw new Exception('اسم الفئة مطلوب');
                    }
                    
                    $categoryId = intval($_POST['id']);
                    $categoryData = [
                        'name' => trim($_POST['name']),
                        'description' => $_POST['description'] ?? null
                    ];
                    
                    $serviceObj->updateServiceCategory($categoryId, $categoryData);
                    
                    echo json_encode([
                        'status' => 'success',
                        'message' => 'تم تحديث الفئة بنجاح'
                    ]);
                    break;
                
                // حذف فئة
                case 'delete':
                    hasPermission('services_delete');
                    
                    if (!isset($_POST['id'])) {
                        throw new Exception('معرف الفئة مطلوب');
                    }
                    
                    $categoryId = intval($_POST['id']);
                    
                    $serviceObj->deleteServiceCategory($categoryId);
                    
                    echo json_encode([
                        'status' => 'success',
                        'message' => 'تم حذف الفئة بنجاح'
                    ]);
                    break;
                
                default:
                    throw new Exception('إجراء غير معروف');
            }
            break;
        
        // التحقق من توفر موعد للخدمة
        case 'check_availability':
            if (!isset($_GET['service_id']) || !isset($_GET['date']) || 
                !isset($_GET['start_time']) || !isset($_GET['branch_id'])) {
                throw new Exception('يجب تحديد جميع المعلومات المطلوبة');
            }
            $serviceId = intval($_GET['service_id']);
            $date = trim($_GET['date']);
            $startTime = trim($_GET['start_time']);
            $branchId = intval($_GET['branch_id']);
            
            $isAvailable = $serviceObj->checkServiceAvailability($serviceId, $date, $startTime, $branchId);
            
            echo json_encode([
                'status' => 'success',
                'data' => [
                    'available' => $isAvailable
                ]
            ]);
            break;
        
        case 'get_branch_services':
            // التحقق من وجود معرف الفرع
            if (!isset($_GET['branch_id']) || empty($_GET['branch_id'])) {
                throw new Exception('معرف الفرع مطلوب');
            }
            
            $branchId = intval($_GET['branch_id']);
            
            // استرجاع الخدمات المتاحة في الفرع
            $filters = [
                'branch_id' => $branchId,
                'is_active' => 1
            ];
            
            $services = $serviceObj->getServices($filters);
            
            echo json_encode([
                'status' => 'success',
                'services' => $services
            ]);
            break;
        
        default:
            throw new Exception('إجراء غير معروف');
    }
} catch (Exception $e) {
    // معالجة الأخطاء
    http_response_code(400);
    
    // تسجيل الخطأ
    error_log('خطأ في نقطة النهاية الخاصة بالخدمات: ' . $e->getMessage());
    
    // إرجاع رسالة الخطأ للعميل
    echo json_encode([
        'status' => 'error',
        'message' => $e->getMessage()
    ]);
}
