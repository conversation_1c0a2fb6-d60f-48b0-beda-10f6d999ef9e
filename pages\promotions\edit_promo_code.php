<?php
/**
 * صفحة تعديل كود ترويج
 */

// تعريف ثابت لمنع الوصول المباشر للملفات المرتبطة
define('BASEPATH', true);

// استدعاء ملفات التهيئة
require_once '../../config/init.php';

// التحقق من تسجيل الدخول
requireLogin();

// التحقق من الصلاحيات
requirePermission('promotions_edit');

// التحقق من وجود معرف كود الترويج
if (!isset($_GET['id']) || empty($_GET['id'])) {
    redirect('pages/promotions/promo_codes.php');
}

$promoCodeId = intval($_GET['id']);

// إنشاء كائنات النماذج
$promoCodeModel = new PromoCode($db);
$customerModel = new Customer($db);
$branchModel = new Branch($db);

// الحصول على بيانات كود الترويج
$promoCode = $promoCodeModel->getPromoCodeById($promoCodeId);

// التحقق من وجود كود الترويج
if (!$promoCode) {
    setFlashMessage('error', 'كود الترويج غير موجود.');
    redirect('pages/promotions/promo_codes.php');
}

// عنوان الصفحة
$pageTitle = 'تعديل كود ترويج: ' . $promoCode['name'];

// التحقق من صلاحيات المستخدم
$isAdmin = hasPermission('admin_access');

// الفرع الحالي
$branchId = $_SESSION['user_branch_id'] ?? null;

// الحصول على قائمة الفروع إذا كان المستخدم مديراً
$branches = [];
if ($isAdmin) {
    $branches = $branchModel->getBranches(['is_active' => 1]);
}

// الحصول على قائمة العملاء
$customers = $customerModel->getCustomers(['is_active' => 1, 'limit' => 1000]);

// الحصول على قائمة العملاء المرتبطين بكود الترويج
$selectedCustomers = [];
if ($promoCode['is_for_specific_customers']) {
    $promoCodeCustomers = $promoCodeModel->getPromoCodeCustomers($promoCodeId);
    foreach ($promoCodeCustomers as $customer) {
        $selectedCustomers[] = $customer['id'];
    }
}

// استدعاء رأس الصفحة
include '../../includes/templates/header.php';
?>

<!-- المحتوى الرئيسي للصفحة -->
<div class="container-fluid">
    <!-- عنوان الصفحة -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2 class="h3 mb-0 text-gray-800">تعديل كود ترويج</h2>
        <div>
            <a href="view_promo_code.php?id=<?php echo $promoCodeId; ?>" class="btn btn-info me-2">
                <i class="fas fa-eye me-1"></i> عرض التفاصيل
            </a>
            <a href="promo_codes.php" class="btn btn-secondary">
                <i class="fas fa-arrow-right me-1"></i> العودة إلى القائمة
            </a>
        </div>
    </div>

    <!-- بطاقة تعديل كود ترويج -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">بيانات كود الترويج</h6>
        </div>
        <div class="card-body">
            <form id="editPromoCodeForm">
                <input type="hidden" id="promo_code_id" name="id" value="<?php echo $promoCodeId; ?>">

                <div class="row mb-3">
                    <div class="col-md-6">
                        <label for="code" class="form-label">كود الترويج <span class="text-danger">*</span></label>
                        <div class="input-group">
                            <input type="text" id="code" name="code" class="form-control" value="<?php echo htmlspecialchars($promoCode['code']); ?>" required>
                            <button type="button" id="generateRandomCode" class="btn btn-outline-secondary">
                                <i class="fas fa-sync-alt"></i> توليد كود جديد
                            </button>
                        </div>
                        <small class="form-text text-muted">يجب أن يكون الكود فريدًا ويتكون من أحرف وأرقام فقط.</small>
                    </div>
                    <div class="col-md-6">
                        <label for="name" class="form-label">اسم العرض <span class="text-danger">*</span></label>
                        <input type="text" id="name" name="name" class="form-control" value="<?php echo htmlspecialchars($promoCode['name']); ?>" required>
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-md-12">
                        <label for="description" class="form-label">وصف العرض</label>
                        <textarea id="description" name="description" class="form-control" rows="3"><?php echo htmlspecialchars($promoCode['description'] ?? ''); ?></textarea>
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-md-6">
                        <label for="discount_type" class="form-label">نوع الخصم <span class="text-danger">*</span></label>
                        <select id="discount_type" name="discount_type" class="form-select" required>
                            <option value="percentage" <?php echo ($promoCode['discount_type'] === 'percentage') ? 'selected' : ''; ?>>نسبة مئوية (%)</option>
                            <option value="fixed" <?php echo ($promoCode['discount_type'] === 'fixed') ? 'selected' : ''; ?>>مبلغ ثابت (ج.م)</option>
                        </select>
                    </div>
                    <div class="col-md-6">
                        <label for="discount_value" class="form-label">قيمة الخصم <span class="text-danger">*</span></label>
                        <div class="input-group">
                            <input type="number" id="discount_value" name="discount_value" class="form-control" min="0" step="0.01" value="<?php echo $promoCode['discount_value']; ?>" required>
                            <span class="input-group-text discount-symbol"><?php echo ($promoCode['discount_type'] === 'percentage') ? '%' : 'ج.م'; ?></span>
                        </div>
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-md-6">
                        <label for="min_invoice_amount" class="form-label">الحد الأدنى لقيمة الفاتورة</label>
                        <div class="input-group">
                            <input type="number" id="min_invoice_amount" name="min_invoice_amount" class="form-control" min="0" step="0.01" value="<?php echo $promoCode['min_invoice_amount'] ?? ''; ?>">
                            <span class="input-group-text">ج.م</span>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <label for="max_invoice_amount" class="form-label">الحد الأقصى لقيمة الفاتورة</label>
                        <div class="input-group">
                            <input type="number" id="max_invoice_amount" name="max_invoice_amount" class="form-control" min="0" step="0.01" value="<?php echo $promoCode['max_invoice_amount'] ?? ''; ?>">
                            <span class="input-group-text">ج.م</span>
                        </div>
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-md-6">
                        <label for="max_uses" class="form-label">الحد الأقصى لعدد مرات الاستخدام</label>
                        <input type="number" id="max_uses" name="max_uses" class="form-control" min="1" value="<?php echo $promoCode['max_uses'] ?? ''; ?>">
                        <small class="form-text text-muted">اتركه فارغًا للاستخدام غير المحدود.</small>
                    </div>
                    <div class="col-md-6">
                        <label for="required_loyalty_points" class="form-label">نقاط الولاء المطلوبة</label>
                        <input type="number" id="required_loyalty_points" name="required_loyalty_points" class="form-control" min="0" value="<?php echo $promoCode['required_loyalty_points'] ?? ''; ?>">
                        <small class="form-text text-muted">عدد نقاط الولاء التي يجب أن يمتلكها العميل لاستخدام الكود.</small>
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-md-6">
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="one_use_per_customer" name="one_use_per_customer" <?php echo isset($promoCode['one_use_per_customer']) && $promoCode['one_use_per_customer'] ? 'checked' : ''; ?>>
                            <label class="form-check-label" for="one_use_per_customer">استخدام مرة واحدة لكل عميل</label>
                            <small class="form-text text-muted d-block">عند تفعيل هذا الخيار، يمكن لكل عميل استخدام الكود مرة واحدة فقط.</small>
                        </div>
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-md-6">
                        <label for="start_date" class="form-label">تاريخ بداية العرض</label>
                        <input type="date" id="start_date" name="start_date" class="form-control" value="<?php echo $promoCode['start_date'] ?? ''; ?>">
                    </div>
                    <div class="col-md-6">
                        <label for="end_date" class="form-label">تاريخ نهاية العرض</label>
                        <input type="date" id="end_date" name="end_date" class="form-control" value="<?php echo $promoCode['end_date'] ?? ''; ?>">
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-md-6">
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="is_active" name="is_active" <?php echo $promoCode['is_active'] ? 'checked' : ''; ?>>
                            <label class="form-check-label" for="is_active">نشط</label>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="is_for_specific_customers" name="is_for_specific_customers" <?php echo ($promoCode['is_for_specific_customers'] == 1) ? 'checked' : ''; ?>>
                            <label class="form-check-label" for="is_for_specific_customers">مخصص لعملاء محددين</label>
                        </div>
                    </div>
                </div>

                <div id="specificCustomersSection" class="row mb-3 <?php echo ($promoCode['is_for_specific_customers'] == 1) ? '' : 'd-none'; ?>">
                    <div class="col-md-12">
                        <label for="customer_ids" class="form-label">اختر العملاء <span class="text-danger">*</span></label>
                        <select id="customer_ids" name="customer_ids[]" class="form-select" multiple data-placeholder="اختر العملاء...">
                            <?php foreach ($customers as $customer): ?>
                            <option value="<?php echo $customer['id']; ?>" <?php echo in_array($customer['id'], $selectedCustomers) ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($customer['name']) . ' (' . $customer['phone'] . ')'; ?>
                            </option>
                            <?php endforeach; ?>
                        </select>
                        <small class="form-text text-muted">يمكنك اختيار أكثر من عميل.</small>
                    </div>
                </div>

                <?php if ($isAdmin && !empty($branches)): ?>
                <div class="row mb-3">
                    <div class="col-md-6">
                        <label for="branch_id" class="form-label">الفرع</label>
                        <select id="branch_id" name="branch_id" class="form-select">
                            <option value="">جميع الفروع</option>
                            <?php foreach ($branches as $branch): ?>
                            <option value="<?php echo $branch['id']; ?>" <?php echo ($promoCode['branch_id'] == $branch['id']) ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($branch['name']); ?>
                            </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                </div>
                <?php else: ?>
                <input type="hidden" name="branch_id" value="<?php echo $branchId; ?>">
                <?php endif; ?>

                <div class="row mt-4">
                    <div class="col-md-12">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i> حفظ التغييرات
                        </button>
                        <a href="promo_codes.php" class="btn btn-secondary">
                            <i class="fas fa-times me-1"></i> إلغاء
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- معلومات إضافية -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">معلومات إضافية</h6>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <p><strong>عدد مرات الاستخدام الحالية:</strong> <?php echo $promoCode['current_uses']; ?></p>
                </div>
                <div class="col-md-6">
                    <p><strong>تاريخ الإنشاء:</strong> <?php echo date('Y-m-d H:i', strtotime($promoCode['created_at'])); ?></p>
                </div>
            </div>
            <div class="row">
                <div class="col-md-6">
                    <p><strong>آخر تحديث:</strong> <?php echo date('Y-m-d H:i', strtotime($promoCode['updated_at'])); ?></p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- استدعاء قالب الفوتر -->
<?php include '../../includes/templates/footer.php'; ?>

<!-- استدعاء مكتبة Select2 -->
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>

<script>
$(document).ready(function() {
    // تهيئة Select2 للعملاء
    $('#customer_ids').select2({
        dir: 'rtl',
        width: '100%',
        placeholder: 'اختر العملاء...',
        allowClear: true
    });

    // تغيير رمز نوع الخصم
    $('#discount_type').on('change', function() {
        const discountType = $(this).val();
        if (discountType === 'percentage') {
            $('.discount-symbol').text('%');
        } else {
            $('.discount-symbol').text('ج.م');
        }
    });

    // إظهار/إخفاء قسم العملاء المحددين
    $('#is_for_specific_customers').on('change', function() {
        if ($(this).is(':checked')) {
            $('#specificCustomersSection').removeClass('d-none');
            $('#customer_ids').prop('required', true);
        } else {
            $('#specificCustomersSection').addClass('d-none');
            $('#customer_ids').prop('required', false);
        }
    });

    // توليد كود عشوائي جديد
    $('#generateRandomCode').on('click', function() {
        $.ajax({
            url: '../../api/promo_codes.php?action=generate_code',
            type: 'GET',
            dataType: 'json',
            success: function(response) {
                if (response.status === 'success') {
                    $('#code').val(response.code);
                }
            }
        });
    });

    // التحقق من صحة التواريخ
    $('#end_date').on('change', function() {
        const startDate = $('#start_date').val();
        const endDate = $(this).val();

        if (startDate && endDate && new Date(endDate) < new Date(startDate)) {
            alert('تاريخ النهاية يجب أن يكون بعد تاريخ البداية');
            $(this).val('');
        }
    });

    $('#start_date').on('change', function() {
        const startDate = $(this).val();
        const endDate = $('#end_date').val();

        if (startDate && endDate && new Date(endDate) < new Date(startDate)) {
            alert('تاريخ البداية يجب أن يكون قبل تاريخ النهاية');
            $(this).val('');
        }
    });

    // إرسال النموذج
    $('#editPromoCodeForm').on('submit', function(e) {
        e.preventDefault();

        // التحقق من صحة البيانات
        if (!validateForm()) {
            return;
        }

        // جمع بيانات النموذج
        const formData = {
            id: $('#promo_code_id').val(),
            code: $('#code').val(),
            name: $('#name').val(),
            description: $('#description').val(),
            discount_type: $('#discount_type').val(),
            discount_value: $('#discount_value').val(),
            min_invoice_amount: $('#min_invoice_amount').val() || null,
            max_invoice_amount: $('#max_invoice_amount').val() || null,
            max_uses: $('#max_uses').val() || null,
            is_for_specific_customers: $('#is_for_specific_customers').is(':checked') ? 1 : 0,
            one_use_per_customer: $('#one_use_per_customer').is(':checked') ? 1 : 0,
            required_loyalty_points: $('#required_loyalty_points').val() || null,
            start_date: $('#start_date').val() || null,
            end_date: $('#end_date').val() || null,
            is_active: $('#is_active').is(':checked') ? 1 : 0,
            branch_id: $('#branch_id').val() || null
        };

        // للتأكد من أن قيمة is_for_specific_customers يتم إرسالها بشكل صحيح
        console.log('is_for_specific_customers value: ' + formData.is_for_specific_customers);
        console.log('is_for_specific_customers checkbox checked: ' + $('#is_for_specific_customers').is(':checked'));

        // إضافة العملاء المحددين إذا كان الكود مخصصًا لعملاء محددين
        if (formData.is_for_specific_customers == 1) {
            formData.customer_ids = $('#customer_ids').val();
            console.log('Adding customer_ids to formData: ' + JSON.stringify(formData.customer_ids));
        } else {
            console.log('Not adding customer_ids to formData because is_for_specific_customers is not 1');
        }

        // إرسال البيانات
        $.ajax({
            url: '../../api/promo_codes.php?action=update',
            type: 'POST',
            data: formData,
            dataType: 'json',
            success: function(response) {
                if (response.status === 'success') {
                    // عرض رسالة نجاح
                    showAlert('success', response.message);

                    // الانتقال إلى صفحة أكواد الترويج بعد ثانيتين
                    setTimeout(function() {
                        window.location.href = 'promo_codes.php';
                    }, 2000);
                } else {
                    // عرض رسالة الخطأ
                    showAlert('danger', response.message);
                }
            },
            error: function() {
                // عرض رسالة خطأ عامة
                showAlert('danger', 'حدث خطأ أثناء معالجة الطلب. يرجى المحاولة مرة أخرى.');
            }
        });
    });

    // التحقق من صحة النموذج
    function validateForm() {
        // التحقق من وجود الحقول المطلوبة
        if (!$('#code').val() || !$('#name').val() || !$('#discount_value').val()) {
            showAlert('danger', 'يرجى ملء جميع الحقول المطلوبة.');
            return false;
        }

        // التحقق من قيمة الخصم
        const discountValue = parseFloat($('#discount_value').val());
        if (isNaN(discountValue) || discountValue <= 0) {
            showAlert('danger', 'يجب أن تكون قيمة الخصم أكبر من صفر.');
            return false;
        }

        // التحقق من النسبة المئوية
        if ($('#discount_type').val() === 'percentage' && discountValue > 100) {
            showAlert('danger', 'يجب أن تكون النسبة المئوية بين 0 و 100.');
            return false;
        }

        // التحقق من العملاء المحددين
        if ($('#is_for_specific_customers').is(':checked') && (!$('#customer_ids').val() || $('#customer_ids').val().length === 0)) {
            showAlert('danger', 'يرجى اختيار عميل واحد على الأقل.');
            return false;
        }

        return true;
    }

    // دالة لعرض التنبيهات
    function showAlert(type, message) {
        const alertHtml = `
            <div class="alert alert-${type} alert-dismissible fade show" role="alert">
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        `;

        // إضافة التنبيه في أعلى النموذج
        $('#editPromoCodeForm').prepend(alertHtml);

        // إزالة التنبيه تلقائيًا بعد 5 ثوانٍ
        setTimeout(function() {
            $('.alert').alert('close');
        }, 5000);
    }
});
</script>
