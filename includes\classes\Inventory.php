<?php
/**
 * فئة المخزون
 * تتعامل مع إدارة مخزون المنتجات في صالون الحلاقة والكوافير
 */

// منع الوصول المباشر للملف
if (!defined('BASEPATH')) {
    exit('لا يمكن الوصول المباشر لهذا الملف');
}

class Inventory {
    private $db;

    /**
     * إنشاء كائن من فئة المخزون
     * @param Database $db كائن قاعدة البيانات
     */
    public function __construct($db) {
        $this->db = $db;
    }

    /**
     * الحصول على ملخص حركات المخزون للمنتج
     * @param int $productId معرف المنتج
     * @param int $branchId معرف الفرع (اختياري)
     * @return array ملخص حركات المخزون
     */
    public function getProductTransactionSummary($productId, $branchId = null) {
        try {
            $sql = "SELECT
                      SUM(CASE WHEN transaction_type = 'in' THEN quantity ELSE 0 END) as total_in,
                      SUM(CASE WHEN transaction_type = 'out' THEN quantity ELSE 0 END) as total_out,
                      COUNT(*) as total_transactions
                    FROM inventory_transactions
                    WHERE product_id = :product_id";

            $params = [':product_id' => $productId];

            if ($branchId) {
                $sql .= " AND branch_id = :branch_id";
                $params[':branch_id'] = $branchId;
            }

            $this->db->prepare($sql);

            foreach ($params as $param => $value) {
                $this->db->bind($param, $value);
            }

            $result = $this->db->fetch();

            return [
                'total_in' => $result ? (int)$result['total_in'] : 0,
                'total_out' => $result ? (int)$result['total_out'] : 0,
                'total_transactions' => $result ? (int)$result['total_transactions'] : 0,
                'current_stock' => $this->getProductStock($productId, $branchId)
            ];
        } catch (Exception $e) {
            error_log('خطأ أثناء استرجاع ملخص حركات المخزون: ' . $e->getMessage());
            throw $e;
        }
    }
    /**
     * الحصول على إحصائيات المخزون
     *
     * @param int $branchId معرف الفرع
     * @return array إحصائيات المخزون
     */
    public function getInventoryStats($branchId) {
        try {
            // إنشاء ملف سجل للتصحيح
            $logFile = dirname(__FILE__) . '/inventory_stats_debug.log';
            file_put_contents($logFile, "=== " . date('Y-m-d H:i:s') . " ===\n", FILE_APPEND);
            file_put_contents($logFile, "Branch ID: " . $branchId . "\n", FILE_APPEND);

            // إجمالي المنتجات في المخزون
            if ($branchId === 0) {
                // عرض جميع المنتجات بغض النظر عن الفرع
                $totalQuery = "SELECT COUNT(*) as total_products
                              FROM products
                              WHERE is_active = 1";
                $this->db->prepare($totalQuery);
                file_put_contents($logFile, "Total Products Query (All Branches): " . $totalQuery . "\n", FILE_APPEND);
            } else {
                // عرض المنتجات الخاصة بفرع محدد
                $totalQuery = "SELECT COUNT(DISTINCT p.id) as total_products
                              FROM products p
                              JOIN inventory i ON p.id = i.product_id
                              WHERE i.branch_id = :branch_id AND p.is_active = 1";
                $this->db->prepare($totalQuery);
                $this->db->bind(':branch_id', $branchId);
                file_put_contents($logFile, "Total Products Query (Branch $branchId): " . $totalQuery . "\n", FILE_APPEND);
            }
            $totalProducts = $this->db->fetchColumn();
            file_put_contents($logFile, "Total Products Result: " . $totalProducts . "\n", FILE_APPEND);

            // المنتجات منخفضة المخزون
            if ($branchId === 0) {
                $lowStockQuery = "SELECT COUNT(DISTINCT p.id) as low_stock
                                 FROM products p
                                 JOIN inventory i ON p.id = i.product_id
                                 WHERE p.is_active = 1
                                 AND i.quantity > 0 AND i.quantity <= p.min_quantity";
                $this->db->prepare($lowStockQuery);
                file_put_contents($logFile, "Low Stock Query (All Branches): " . $lowStockQuery . "\n", FILE_APPEND);
            } else {
                $lowStockQuery = "SELECT COUNT(DISTINCT p.id) as low_stock
                                 FROM products p
                                 JOIN inventory i ON p.id = i.product_id
                                 WHERE i.branch_id = :branch_id AND p.is_active = 1
                                 AND i.quantity > 0 AND i.quantity <= p.min_quantity";
                $this->db->prepare($lowStockQuery);
                $this->db->bind(':branch_id', $branchId);
                file_put_contents($logFile, "Low Stock Query (Branch $branchId): " . $lowStockQuery . "\n", FILE_APPEND);
            }
            $lowStock = $this->db->fetchColumn();
            file_put_contents($logFile, "Low Stock Result: " . $lowStock . "\n", FILE_APPEND);

            // المنتجات التي نفدت من المخزون
            if ($branchId === 0) {
                $outOfStockQuery = "SELECT COUNT(DISTINCT p.id) as out_of_stock
                                   FROM products p
                                   LEFT JOIN inventory i ON p.id = i.product_id
                                   WHERE p.is_active = 1
                                   AND (i.quantity = 0 OR i.quantity IS NULL)";
                $this->db->prepare($outOfStockQuery);
                file_put_contents($logFile, "Out of Stock Query (All Branches): " . $outOfStockQuery . "\n", FILE_APPEND);
            } else {
                $outOfStockQuery = "SELECT COUNT(DISTINCT p.id) as out_of_stock
                                   FROM products p
                                   LEFT JOIN inventory i ON p.id = i.product_id
                                   WHERE i.branch_id = :branch_id AND p.is_active = 1
                                   AND (i.quantity = 0 OR i.quantity IS NULL)";
                $this->db->prepare($outOfStockQuery);
                $this->db->bind(':branch_id', $branchId);
                file_put_contents($logFile, "Out of Stock Query (Branch $branchId): " . $outOfStockQuery . "\n", FILE_APPEND);
            }
            $outOfStock = $this->db->fetchColumn();
            file_put_contents($logFile, "Out of Stock Result: " . $outOfStock . "\n", FILE_APPEND);

            // إجمالي قيمة المخزون
            if ($branchId === 0) {
                $inventoryValueQuery = "SELECT SUM(i.quantity * p.cost) as total_value
                                       FROM products p
                                       JOIN inventory i ON p.id = i.product_id
                                       WHERE p.is_active = 1";
                $this->db->prepare($inventoryValueQuery);
                file_put_contents($logFile, "Total Value Query (All Branches): " . $inventoryValueQuery . "\n", FILE_APPEND);
            } else {
                $inventoryValueQuery = "SELECT SUM(i.quantity * p.cost) as total_value
                                       FROM products p
                                       JOIN inventory i ON p.id = i.product_id
                                       WHERE i.branch_id = :branch_id AND p.is_active = 1";
                $this->db->prepare($inventoryValueQuery);
                $this->db->bind(':branch_id', $branchId);
                file_put_contents($logFile, "Total Value Query (Branch $branchId): " . $inventoryValueQuery . "\n", FILE_APPEND);
            }
            $result = $this->db->fetch();
            $totalValue = $result ? $result['total_value'] : 0;
            file_put_contents($logFile, "Total Value Result: " . $totalValue . "\n", FILE_APPEND);

            // إرجاع الإحصائيات
            $stats = [
                'total_products' => (int)$totalProducts,
                'low_stock' => (int)$lowStock,
                'out_of_stock' => (int)$outOfStock,
                'total_value' => (float)$totalValue,
                'branch_id' => $branchId
            ];

            file_put_contents($logFile, "Final Stats: " . json_encode($stats, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n\n", FILE_APPEND);

            return $stats;
        } catch (Exception $e) {
            $errorMsg = 'خطأ أثناء استرجاع إحصائيات المخزون: ' . $e->getMessage();
            error_log($errorMsg);

            // تسجيل الخطأ في ملف السجل
            if (isset($logFile)) {
                file_put_contents($logFile, "ERROR: " . $errorMsg . "\n", FILE_APPEND);
                file_put_contents($logFile, "Stack Trace: " . $e->getTraceAsString() . "\n\n", FILE_APPEND);
            }

            throw $e;
        }
    }
    /**
     * الحصول على رصيد المنتج في المخزون
     * @param int $productId معرف المنتج
     * @param int $branchId معرف الفرع
     * @return int الرصيد الحالي أو 0 إذا لم يتم العثور عليه
     */
    public function getProductStock($productId, $branchId = null) {
        try {
            $sql = "SELECT quantity FROM inventory WHERE product_id = :product_id";
            $params = [':product_id' => $productId];

            if ($branchId) {
                $sql .= " AND branch_id = :branch_id";
                $params[':branch_id'] = $branchId;
            }

            $this->db->prepare($sql);

            foreach ($params as $param => $value) {
                $this->db->bind($param, $value);
            }

            $result = $this->db->fetch();
            return $result ? (int)$result['quantity'] : 0;
        } catch (Exception $e) {
            error_log('خطأ أثناء استرجاع رصيد المنتج: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * ضبط كمية المنتج في المخزون
     * @param int $productId معرف المنتج
     * @param int $quantity الكمية الجديدة
     * @param int $branchId معرف الفرع
     * @param string $notes ملاحظات (اختياري)
     * @param bool $logTransaction سجل حركة المخزون (true بشكل افتراضي)
     * @return bool نجاح أو فشل العملية
     */
    public function setProductStock($productId, $quantity, $branchId, $notes = '', $logTransaction = true) {
        try {
            // التحقق من وجود معاملة نشطة
            $transactionStartedHere = false;
            if (!$this->db->inTransaction()) {
                $this->db->beginTransaction();
                $transactionStartedHere = true;
            }

            // الحصول على الكمية الحالية
            $currentQuantity = $this->getProductStock($productId, $branchId);

            // التحقق مما إذا كان المنتج موجود في المخزون
            $this->db->prepare("SELECT id FROM inventory WHERE product_id = :product_id AND branch_id = :branch_id");
            $this->db->bind(':product_id', $productId);
            $this->db->bind(':branch_id', $branchId);
            $inventoryRecord = $this->db->fetch();

            if ($inventoryRecord) {
                // تحديث كمية المنتج
                $this->db->prepare("UPDATE inventory SET quantity = :quantity, updated_at = NOW()
                                   WHERE product_id = :product_id AND branch_id = :branch_id");
                $this->db->bind(':quantity', $quantity);
                $this->db->bind(':product_id', $productId);
                $this->db->bind(':branch_id', $branchId);
                $this->db->execute();
            } else {
                // إضافة سجل مخزون جديد
                $this->db->prepare("INSERT INTO inventory (product_id, quantity, branch_id)
                                   VALUES (:product_id, :quantity, :branch_id)");
                $this->db->bind(':product_id', $productId);
                $this->db->bind(':quantity', $quantity);
                $this->db->bind(':branch_id', $branchId);
                $this->db->execute();
            }

            // تسجيل حركة المخزون إذا كان مطلوبًا وكان هناك تغيير في الكمية
            if ($logTransaction && $quantity != $currentQuantity) {
                // تحديد نوع الحركة (إضافة أو سحب)
                $transactionType = $quantity > $currentQuantity ? 'in' : 'out';
                $difference = abs($quantity - $currentQuantity);

                if ($difference > 0) {
                    // إنشاء بيانات حركة المخزون
                    $transactionData = [
                        'product_id' => $productId,
                        'transaction_type' => $transactionType,
                        'quantity' => $difference,
                        'previous_quantity' => $currentQuantity,
                        'current_quantity' => $quantity,
                        'notes' => $notes,
                        'user_id' => $_SESSION['user_id'] ?? null,
                        'branch_id' => $branchId
                    ];

                    // إضافة حركة المخزون
                    $this->addInventoryTransaction($transactionData);
                }
            }

            // تأكيد المعاملة إذا بدأناها هنا
            if ($transactionStartedHere && $this->db->inTransaction()) {
                $this->db->commit();
            }

            return true;
        } catch (Exception $e) {
            // التراجع عن المعاملة في حالة حدوث خطأ (فقط إذا بدأناها هنا)
            if (isset($transactionStartedHere) && $transactionStartedHere && $this->db->inTransaction()) {
                $this->db->rollBack();
            }

            error_log('خطأ أثناء ضبط كمية المنتج: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * إضافة كمية للمنتج في المخزون
     * @param int $productId معرف المنتج
     * @param int $quantity الكمية المضافة
     * @param int $branchId معرف الفرع
     * @param string $notes ملاحظات (اختياري)
     * @return bool نجاح أو فشل العملية
     */
    public function addStock($productId, $quantity, $branchId, $notes = '') {
        try {
            $currentQuantity = $this->getProductStock($productId, $branchId);
            $newQuantity = $currentQuantity + $quantity;

            return $this->setProductStock($productId, $newQuantity, $branchId, $notes);
        } catch (Exception $e) {
            error_log('خطأ أثناء إضافة كمية للمنتج: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * سحب كمية من المنتج في المخزون
     * @param int $productId معرف المنتج
     * @param int $quantity الكمية المسحوبة
     * @param int $branchId معرف الفرع
     * @param string $notes ملاحظات (اختياري)
     * @return bool نجاح أو فشل العملية
     */
    public function removeStock($productId, $quantity, $branchId, $notes = '') {
        try {
            $currentQuantity = $this->getProductStock($productId, $branchId);

            if ($currentQuantity < $quantity) {
                throw new Exception('الكمية المطلوبة غير متوفرة في المخزون');
            }

            $newQuantity = $currentQuantity - $quantity;

            return $this->setProductStock($productId, $newQuantity, $branchId, $notes);
        } catch (Exception $e) {
            error_log('خطأ أثناء سحب كمية من المنتج: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * الحصول على قائمة المخزون
     * @param array $filters فلاتر البحث (اختياري)
     * @return array قائمة المخزون
     */
    public function getInventory($filters = []) {
        try {
            // إنشاء ملف سجل للتصحيح
            $logFile = dirname(__FILE__) . '/inventory_debug.log';
            file_put_contents($logFile, "=== " . date('Y-m-d H:i:s') . " ===\n", FILE_APPEND);
            file_put_contents($logFile, "Filters: " . json_encode($filters) . "\n", FILE_APPEND);

            $sql = "SELECT i.*, p.name as product_name, p.price, p.cost, p.min_quantity,
                          pc.name as category_name, b.name as branch_name
                    FROM inventory i
                    JOIN products p ON i.product_id = p.id
                    LEFT JOIN product_categories pc ON p.category_id = pc.id
                    LEFT JOIN branches b ON i.branch_id = b.id";

            $whereConditions = [];
            $bindings = [];

            // تطبيق الفلاتر
            if (!empty($filters['search'])) {
                $whereConditions[] = "(p.name LIKE :search OR p.description LIKE :search)";
                $bindings[':search'] = '%' . $filters['search'] . '%';
            }

            if (!empty($filters['category_id'])) {
                $whereConditions[] = "p.category_id = :category_id";
                $bindings[':category_id'] = $filters['category_id'];
            }

            // تطبيق فلتر الفرع
            if (isset($filters['branch_id'])) {
                if ($filters['branch_id'] === 0) {
                    // إذا كان branch_id = 0، فهذا يعني "كل الفروع" - لا تضف أي شرط للفرع
                    file_put_contents($logFile, "All branches selected (branch_id = 0)\n", FILE_APPEND);
                } else if ($filters['branch_id'] > 0) {
                    // فرع محدد
                    $whereConditions[] = "i.branch_id = :branch_id";
                    $bindings[':branch_id'] = (int)$filters['branch_id'];
                    file_put_contents($logFile, "Branch filter applied: " . $filters['branch_id'] . "\n", FILE_APPEND);
                } else {
                    // إذا لم يتم تحديد فرع، استخدم الفرع الحالي للمستخدم إذا كان متاحًا
                    if (isset($_SESSION['branch_id']) && $_SESSION['branch_id'] > 0) {
                        $whereConditions[] = "i.branch_id = :branch_id";
                        $bindings[':branch_id'] = (int)$_SESSION['branch_id'];
                        file_put_contents($logFile, "Using session branch_id: " . $_SESSION['branch_id'] . "\n", FILE_APPEND);
                    } else {
                        // إذا لم يكن هناك فرع محدد ولا فرع في الجلسة، قم بتقييد النتائج للفرع الرئيسي أو الأول
                        $whereConditions[] = "i.branch_id = (SELECT MIN(id) FROM branches WHERE is_active = 1)";
                        file_put_contents($logFile, "No branch specified, using default branch\n", FILE_APPEND);
                    }
                }
            }

            if (isset($filters['is_for_sale'])) {
                $whereConditions[] = "p.is_for_sale = :is_for_sale";
                $bindings[':is_for_sale'] = $filters['is_for_sale'];
            }

            if (isset($filters['low_stock'])) {
                $whereConditions[] = "i.quantity <= p.min_quantity";
            }

            if (isset($filters['out_of_stock'])) {
                $whereConditions[] = "i.quantity = 0";
            }

            if (isset($filters['min_quantity'])) {
                $whereConditions[] = "i.quantity >= :min_quantity";
                $bindings[':min_quantity'] = $filters['min_quantity'];
            }

            if (isset($filters['max_quantity'])) {
                $whereConditions[] = "i.quantity <= :max_quantity";
                $bindings[':max_quantity'] = $filters['max_quantity'];
            }

            // إضافة شروط WHERE إذا وجدت
            if (!empty($whereConditions)) {
                $sql .= " WHERE " . implode(' AND ', $whereConditions);
            }

            file_put_contents($logFile, "SQL Query: " . $sql . "\n", FILE_APPEND);
            file_put_contents($logFile, "Bindings: " . json_encode($bindings) . "\n", FILE_APPEND);

            // ترتيب النتائج
            if (!empty($filters['sort_by'])) {
                $allowedFields = ['product_name', 'quantity', 'price', 'cost', 'branch_name'];
                if (in_array($filters['sort_by'], $allowedFields)) {
                    $sql .= " ORDER BY " . $filters['sort_by'];

                    if (!empty($filters['sort_dir']) && in_array(strtoupper($filters['sort_dir']), ['ASC', 'DESC'])) {
                        $sql .= " " . strtoupper($filters['sort_dir']);
                    } else {
                        $sql .= " ASC";
                    }
                } else {
                    $sql .= " ORDER BY p.name ASC";
                }
            } else {
                $sql .= " ORDER BY p.name ASC";
            }

            // إضافة الحد والإزاحة إذا وجدت
            if (!empty($filters['limit'])) {
                $sql .= " LIMIT :limit";
                if (!empty($filters['offset'])) {
                    $sql .= " OFFSET :offset";
                }
            }

            $this->db->prepare($sql);

            // ربط القيم
            foreach ($bindings as $param => $value) {
                $this->db->bind($param, $value);
            }

            // ربط الحد والإزاحة إذا وجدت
            if (!empty($filters['limit'])) {
                $this->db->bind(':limit', $filters['limit'], PDO::PARAM_INT);
                if (!empty($filters['offset'])) {
                    $this->db->bind(':offset', $filters['offset'], PDO::PARAM_INT);
                }
            }

            $inventory = $this->db->fetchAll();
            file_put_contents($logFile, "Results count: " . count($inventory) . "\n\n", FILE_APPEND);

            // إضافة حالة المخزون لكل منتج
            foreach ($inventory as &$item) {
                $item['stock_status'] = $this->getStockStatus($item['quantity'], $item['min_quantity']);
            }

            return $inventory;
        } catch (Exception $e) {
            error_log('خطأ أثناء استرجاع قائمة المخزون: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * الحصول على عدد عناصر المخزون
     * @param array $filters فلاتر البحث (اختياري)
     * @return int عدد العناصر
     */
    public function getInventoryCount($filters = []) {
        try {
            $sql = "SELECT COUNT(*) FROM inventory i JOIN products p ON i.product_id = p.id";

            $whereConditions = [];
            $bindings = [];

            // تطبيق الفلاتر الأساسية
            if (!empty($filters['search'])) {
                $whereConditions[] = "(p.name LIKE :search OR p.description LIKE :search)";
                $bindings[':search'] = '%' . $filters['search'] . '%';
            }

            if (!empty($filters['category_id'])) {
                $whereConditions[] = "p.category_id = :category_id";
                $bindings[':category_id'] = $filters['category_id'];
            }

            if (!empty($filters['branch_id'])) {
                $whereConditions[] = "i.branch_id = :branch_id";
                $bindings[':branch_id'] = $filters['branch_id'];
            }

            if (isset($filters['low_stock'])) {
                $whereConditions[] = "i.quantity <= p.min_quantity";
            }

            if (isset($filters['out_of_stock'])) {
                $whereConditions[] = "i.quantity = 0";
            }

            // إضافة شروط WHERE إذا وجدت
            if (!empty($whereConditions)) {
                $sql .= " WHERE " . implode(' AND ', $whereConditions);
            }

            $this->db->prepare($sql);

            // ربط القيم
            foreach ($bindings as $param => $value) {
                $this->db->bind($param, $value);
            }

            return $this->db->fetchColumn();
        } catch (Exception $e) {
            error_log('خطأ أثناء حساب عدد عناصر المخزون: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * تحديد حالة المخزون
     * @param int $currentQuantity الكمية الحالية
     * @param int $minQuantity الحد الأدنى للكمية
     * @return string حالة المخزون (out_of_stock, low_stock, normal)
     */
    private function getStockStatus($currentQuantity, $minQuantity) {
        if ($currentQuantity <= 0) {
            return 'out_of_stock';
        } elseif ($currentQuantity <= $minQuantity) {
            return 'low_stock';
        } else {
            return 'normal';
        }
    }

    /**
     * الحصول على قائمة المنتجات منخفضة المخزون
     * @param int $branchId معرف الفرع (اختياري)
     * @return array قائمة المنتجات
     */
    public function getLowStockProducts($branchId = null) {
        try {
            $filters = ['low_stock' => true];

            if ($branchId) {
                $filters['branch_id'] = $branchId;
            }

            return $this->getInventory($filters);
        } catch (Exception $e) {
            error_log('خطأ أثناء استرجاع المنتجات منخفضة المخزون: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * الحصول على قائمة المنتجات المنتهية من المخزون
     * @param int $branchId معرف الفرع (اختياري)
     * @return array قائمة المنتجات
     */
    public function getOutOfStockProducts($branchId = null) {
        try {
            $filters = ['out_of_stock' => true];

            if ($branchId) {
                $filters['branch_id'] = $branchId;
            }

            return $this->getInventory($filters);
        } catch (Exception $e) {
            error_log('خطأ أثناء استرجاع المنتجات المنتهية من المخزون: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * الحصول على حركات المخزون لمنتج معين
     * @param int $productId معرف المنتج
     * @param int $branchId معرف الفرع (اختياري)
     * @param int $limit عدد النتائج (اختياري)
     * @return array قائمة الحركات
     */
    public function getProductTransactions($productId, $branchId = null, $limit = 20) {
        try {
            $sql = "SELECT t.*, u.name as user_name, b.name as branch_name
                    FROM inventory_transactions t
                    LEFT JOIN users u ON t.user_id = u.id
                    LEFT JOIN branches b ON t.branch_id = b.id
                    WHERE t.product_id = :product_id";

            $bindings = [':product_id' => $productId];

            if ($branchId) {
                $sql .= " AND t.branch_id = :branch_id";
                $bindings[':branch_id'] = $branchId;
            }

            $sql .= " ORDER BY t.created_at DESC";

            if ($limit) {
                $sql .= " LIMIT :limit";
                $bindings[':limit'] = $limit;
            }

            $this->db->prepare($sql);

            foreach ($bindings as $param => $value) {
                if ($param === ':limit') {
                    $this->db->bind($param, $value, PDO::PARAM_INT);
                } else {
                    $this->db->bind($param, $value);
                }
            }

            return $this->db->fetchAll();
        } catch (Exception $e) {
            error_log('خطأ أثناء استرجاع حركات المخزون للمنتج: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * حذف منتج من المخزون
     * @param int $productId معرف المنتج
     * @param int $branchId معرف الفرع
     * @return bool نجاح أو فشل العملية
     */
    public function deleteProductFromInventory($productId, $branchId) {
        try {
            $this->db->beginTransaction();

            // حذف سجلات حركة المخزون
            $this->db->prepare("DELETE FROM inventory_transactions
                               WHERE product_id = :product_id AND branch_id = :branch_id");
            $this->db->bind(':product_id', $productId);
            $this->db->bind(':branch_id', $branchId);
            $this->db->execute();

            // حذف سجل المخزون
            $this->db->prepare("DELETE FROM inventory
                               WHERE product_id = :product_id AND branch_id = :branch_id");
            $this->db->bind(':product_id', $productId);
            $this->db->bind(':branch_id', $branchId);
            $this->db->execute();

            $this->db->commit();
            return true;
        } catch (Exception $e) {
            $this->db->rollBack();
            error_log('خطأ أثناء حذف المنتج من المخزون: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * نقل مخزون بين الفروع
     * @param int $productId معرف المنتج
     * @param int $sourceBranchId معرف الفرع المصدر
     * @param int $targetBranchId معرف الفرع الهدف
     * @param int $quantity الكمية المنقولة
     * @param string $notes ملاحظات (اختياري)
     * @return bool نجاح أو فشل العملية
     */
    public function transferStock($productId, $sourceBranchId, $targetBranchId, $quantity, $notes = '') {
        try {
            if ($sourceBranchId == $targetBranchId) {
                throw new Exception('لا يمكن نقل المخزون لنفس الفرع');
            }

            $this->db->beginTransaction();

            // سحب المخزون من الفرع المصدر
            $sourceQuantity = $this->getProductStock($productId, $sourceBranchId);

            if ($sourceQuantity < $quantity) {
                throw new Exception('الكمية المطلوبة غير متوفرة في المخزون');
            }

            $this->removeStock($productId, $quantity, $sourceBranchId, 'نقل مخزون إلى فرع آخر - ' . $notes);

            // إضافة المخزون للفرع الهدف
            $this->addStock($productId, $quantity, $targetBranchId, 'استلام مخزون من فرع آخر - ' . $notes);

            $this->db->commit();
            return true;
        } catch (Exception $e) {
            $this->db->rollBack();
            error_log('خطأ أثناء نقل المخزون بين الفروع: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * تقرير المخزون
     * @param array $filters فلاتر البحث (اختياري)
     * @return array تقرير المخزون
     */
    public function getInventoryReport($filters = []) {
        try {
            // الحصول على بيانات المخزون
            $inventory = $this->getInventory($filters);

            // إحصائيات عامة
            $totalProducts = count($inventory);
            $totalValue = 0;
            $totalCost = 0;
            $outOfStockCount = 0;
            $lowStockCount = 0;

            foreach ($inventory as $item) {
                // حساب القيمة الإجمالية والتكلفة
                $totalValue += $item['price'] * $item['quantity'];
                $totalCost += $item['cost'] * $item['quantity'];

                // حساب عدد المنتجات منخفضة المخزون والمنتهية
                if ($item['quantity'] <= 0) {
                    $outOfStockCount++;
                } elseif ($item['quantity'] <= $item['min_quantity']) {
                    $lowStockCount++;
                }
            }

            // تجميع المنتجات حسب الفئة
            $categorySummary = [];

            foreach ($inventory as $item) {
                $categoryId = $item['category_id'] ?? 0;
                $categoryName = $item['category_name'] ?? 'بدون فئة';

                if (!isset($categorySummary[$categoryId])) {
                    $categorySummary[$categoryId] = [
                        'category_id' => $categoryId,
                        'category_name' => $categoryName,
                        'product_count' => 0,
                        'total_quantity' => 0,
                        'total_value' => 0,
                        'total_cost' => 0
                    ];
                }

                $categorySummary[$categoryId]['product_count']++;
                $categorySummary[$categoryId]['total_quantity'] += $item['quantity'];
                $categorySummary[$categoryId]['total_value'] += $item['price'] * $item['quantity'];
                $categorySummary[$categoryId]['total_cost'] += $item['cost'] * $item['quantity'];
            }

            // تحويل المصفوفة الترابطية إلى مصفوفة عادية
            $categorySummary = array_values($categorySummary);

            // ترتيب الفئات حسب القيمة الإجمالية
            usort($categorySummary, function($a, $b) {
                return $b['total_value'] - $a['total_value'];
            });

            return [
                'summary' => [
                    'total_products' => $totalProducts,
                    'total_value' => $totalValue,
                    'total_cost' => $totalCost,
                    'expected_profit' => $totalValue - $totalCost,
                    'out_of_stock_count' => $outOfStockCount,
                    'low_stock_count' => $lowStockCount
                ],
                'category_summary' => $categorySummary,
                'inventory' => $inventory
            ];
        } catch (Exception $e) {
            error_log('خطأ أثناء إنشاء تقرير المخزون: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * تسوية المخزون
     * @param int $productId معرف المنتج
     * @param int $actualQuantity الكمية الفعلية
     * @param int $branchId معرف الفرع
     * @param string $notes ملاحظات (اختياري)
     * @return bool نجاح أو فشل العملية
     */
    public function adjustInventory($productId, $actualQuantity, $branchId, $notes = 'تسوية مخزون') {
        try {
            return $this->setProductStock($productId, $actualQuantity, $branchId, $notes);
        } catch (Exception $e) {
            error_log('خطأ أثناء تسوية المخزون: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * تحديث المخزون بعد البيع
     * @param int $productId معرف المنتج
     * @param int $quantity الكمية المباعة
     * @param int $branchId معرف الفرع
     * @param int $invoiceId معرف الفاتورة (اختياري)
     * @return bool نجاح أو فشل العملية
     */
    public function updateStockAfterSale($productId, $quantity, $branchId, $invoiceId = null) {
        try {
            $notes = 'بيع منتج';

            if ($invoiceId) {
                $notes .= ' - فاتورة رقم: ' . $invoiceId;
            }

            return $this->removeStock($productId, $quantity, $branchId, $notes);
        } catch (Exception $e) {
            error_log('خطأ أثناء تحديث المخزون بعد البيع: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * تحديث المخزون بعد إلغاء البيع
     * @param int $productId معرف المنتج
     * @param int $quantity الكمية المرجعة
     * @param int $branchId معرف الفرع
     * @param int $invoiceId معرف الفاتورة (اختياري)
     * @return bool نجاح أو فشل العملية
     */
    public function updateStockAfterCancelSale($productId, $quantity, $branchId, $invoiceId = null) {
        try {
            $notes = 'إلغاء بيع منتج';

            if ($invoiceId) {
                $notes .= ' - فاتورة رقم: ' . $invoiceId;
            }

            return $this->addStock($productId, $quantity, $branchId, $notes);
        } catch (Exception $e) {
            error_log('خطأ أثناء تحديث المخزون بعد إلغاء البيع: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * استيراد بيانات المخزون الأولية
     * @param array $inventoryData بيانات المخزون
     * @param int $branchId معرف الفرع
     * @return bool نجاح أو فشل العملية
     */
    public function importInitialInventory($inventoryData, $branchId) {
        try {
            $this->db->beginTransaction();

            foreach ($inventoryData as $item) {
                $productId = $item['product_id'];
                $quantity = $item['quantity'];

                $this->setProductStock($productId, $quantity, $branchId, 'استيراد بيانات المخزون الأولية');
            }

            $this->db->commit();
            return true;
        } catch (Exception $e) {
            $this->db->rollBack();
            error_log('خطأ أثناء استيراد بيانات المخزون الأولية: ' . $e->getMessage());
            throw $e;
        }
    }

    /** التحقق من توفر المنتج في المخزون
    * @param int $productId معرف المنتج
    * @param int $requiredQuantity الكمية المطلوبة
    * @param int $branchId معرف الفرع
    * @return bool هل المنتج متوفر بالكمية المطلوبة
    */
   public function isProductAvailable($productId, $requiredQuantity, $branchId) {
       try {
           $currentQuantity = $this->getProductStock($productId, $branchId);
           return $currentQuantity >= $requiredQuantity;
       } catch (Exception $e) {
           error_log('خطأ أثناء التحقق من توفر المنتج: ' . $e->getMessage());
           throw $e;
       }
   }

   /**
    * الحصول على المنتجات الأكثر مبيعًا
    * @param array $filters فلاتر البحث (اختياري)
    * @param int $limit عدد النتائج
    * @return array قائمة المنتجات
    */
   public function getTopSellingProducts($filters = [], $limit = 5) {
       try {
           $sql = "SELECT p.id, p.name, p.price, p.cost, SUM(ii.quantity) as sold_quantity,
                         SUM(ii.total) as total_sales, COUNT(DISTINCT i.id) as orders_count
                   FROM products p
                   JOIN invoice_items ii ON p.id = ii.item_id AND ii.item_type = 'product'
                   JOIN invoices i ON ii.invoice_id = i.id";

           $whereConditions = ["p.is_for_sale = 1", "i.payment_status = 'paid'"];
           $bindings = [];

           // تطبيق الفلاتر
           if (!empty($filters['branch_id'])) {
               $whereConditions[] = "i.branch_id = :branch_id";
               $bindings[':branch_id'] = $filters['branch_id'];
           }

           if (!empty($filters['start_date']) && !empty($filters['end_date'])) {
               $whereConditions[] = "i.created_at BETWEEN :start_date AND :end_date";
               $bindings[':start_date'] = $filters['start_date'] . ' 00:00:00';
               $bindings[':end_date'] = $filters['end_date'] . ' 23:59:59';
           } elseif (!empty($filters['month']) && !empty($filters['year'])) {
               $whereConditions[] = "MONTH(i.created_at) = :month AND YEAR(i.created_at) = :year";
               $bindings[':month'] = $filters['month'];
               $bindings[':year'] = $filters['year'];
           }

           // إضافة شروط WHERE
           $sql .= " WHERE " . implode(' AND ', $whereConditions);

           // تجميع حسب المنتج وترتيب النتائج
           $sql .= " GROUP BY p.id, p.name, p.price, p.cost
                     ORDER BY sold_quantity DESC
                     LIMIT :limit";

           $this->db->prepare($sql);

           // ربط القيم
           foreach ($bindings as $param => $value) {
               $this->db->bind($param, $value);
           }

           $this->db->bind(':limit', $limit, PDO::PARAM_INT);

           return $this->db->fetchAll();
       } catch (Exception $e) {
           error_log('خطأ أثناء استرجاع المنتجات الأكثر مبيعًا: ' . $e->getMessage());
           throw $e;
       }
   }

   /**
    * جرد المخزون
    * @param int $branchId معرف الفرع
    * @param array $productIds قائمة معرفات المنتجات (اختياري)
    * @return array نتائج الجرد
    */
   public function stockTake($branchId, $productIds = []) {
       try {
           $filters = ['branch_id' => $branchId];

           // الحصول على بيانات المخزون الحالية
           $inventory = $this->getInventory($filters);

           // تصفية المنتجات حسب المعرفات المطلوبة
           if (!empty($productIds)) {
               $inventory = array_filter($inventory, function($item) use ($productIds) {
                   return in_array($item['product_id'], $productIds);
               });
           }

           // تنسيق البيانات للجرد
           $stockTakeData = [];

           foreach ($inventory as $item) {
               $stockTakeData[] = [
                   'product_id' => $item['product_id'],
                   'product_name' => $item['product_name'],
                   'system_quantity' => $item['quantity'],
                   'actual_quantity' => null, // سيتم ملؤها أثناء الجرد
                   'difference' => null, // سيتم حسابها
                   'price' => $item['price'],
                   'cost' => $item['cost'],
                   'category_name' => $item['category_name'],
                   'min_quantity' => $item['min_quantity']
               ];
           }

           return $stockTakeData;
       } catch (Exception $e) {
           error_log('خطأ أثناء جرد المخزون: ' . $e->getMessage());
           throw $e;
       }
   }

   /**
    * معالجة نتائج الجرد
    * @param array $stockTakeResults نتائج الجرد
    * @param int $branchId معرف الفرع
    * @param string $notes ملاحظات (اختياري)
    * @return bool نجاح أو فشل العملية
    */
   public function processStockTakeResults($stockTakeResults, $branchId, $notes = 'نتائج جرد المخزون') {
       try {
           $this->db->beginTransaction();

           // Make sure $stockTakeResults is an array
           if (is_string($stockTakeResults)) {
               $stockTakeResults = json_decode($stockTakeResults, true);
           }

           // Check if it's a valid array after potential conversion
           if (!is_array($stockTakeResults)) {
               throw new Exception('بيانات الجرد غير صالحة');
           }

           foreach ($stockTakeResults as $result) {
               $productId = $result['product_id'];
               $actualQuantity = $result['actual_quantity'];

               // تحديث المخزون بالكمية الفعلية
               $this->adjustInventory($productId, $actualQuantity, $branchId, $notes);
           }

           $this->db->commit();
           return true;
       } catch (Exception $e) {
           $this->db->rollBack();
           error_log('خطأ أثناء معالجة نتائج جرد المخزون: ' . $e->getMessage());
           throw $e;
       }
   }

   /**
    * تتبع استهلاك المنتجات
    * @param array $filters فلاتر البحث (اختياري)
    * @return array تقرير الاستهلاك
    */
   public function trackProductConsumption($filters = []) {
       try {
           $sql = "SELECT p.id, p.name, p.cost, COUNT(t.id) as transactions_count,
                         SUM(CASE WHEN t.transaction_type = 'out' THEN t.quantity ELSE 0 END) as consumption
                   FROM products p
                   JOIN inventory_transactions t ON p.id = t.product_id";

           $whereConditions = ["p.is_for_sale = 0"]; // المنتجات للاستخدام الداخلي فقط
           $bindings = [];

           // تطبيق الفلاتر
           if (!empty($filters['branch_id'])) {
               $whereConditions[] = "t.branch_id = :branch_id";
               $bindings[':branch_id'] = $filters['branch_id'];
           }

           if (!empty($filters['start_date']) && !empty($filters['end_date'])) {
               $whereConditions[] = "t.created_at BETWEEN :start_date AND :end_date";
               $bindings[':start_date'] = $filters['start_date'] . ' 00:00:00';
               $bindings[':end_date'] = $filters['end_date'] . ' 23:59:59';
           } elseif (!empty($filters['month']) && !empty($filters['year'])) {
               $whereConditions[] = "MONTH(t.created_at) = :month AND YEAR(t.created_at) = :year";
               $bindings[':month'] = $filters['month'];
               $bindings[':year'] = $filters['year'];
           }

           // إضافة شروط WHERE
           $sql .= " WHERE " . implode(' AND ', $whereConditions);

           // تجميع حسب المنتج وترتيب النتائج
           $sql .= " GROUP BY p.id, p.name, p.cost
                     ORDER BY consumption DESC";

           $this->db->prepare($sql);

           // ربط القيم
           foreach ($bindings as $param => $value) {
               $this->db->bind($param, $value);
           }

           return $this->db->fetchAll();
       } catch (Exception $e) {
           error_log('خطأ أثناء تتبع استهلاك المنتجات: ' . $e->getMessage());
           throw $e;
       }
   }

   /**
    * حساب قيمة المخزون
    * @param int $branchId معرف الفرع (اختياري)
    * @return array قيمة المخزون (التكلفة وسعر البيع)
    */
   public function calculateInventoryValue($branchId = null) {
       try {
           $sql = "SELECT
                     SUM(i.quantity * p.cost) as total_cost,
                     SUM(i.quantity * p.price) as total_value
                   FROM inventory i
                   JOIN products p ON i.product_id = p.id";

           $bindings = [];

           if ($branchId) {
               $sql .= " WHERE i.branch_id = :branch_id";
               $bindings[':branch_id'] = $branchId;
           }

           $this->db->prepare($sql);

           // ربط القيم
           foreach ($bindings as $param => $value) {
               $this->db->bind($param, $value);
           }

           $result = $this->db->fetch();

           return [
               'total_cost' => $result ? (float)$result['total_cost'] : 0,
               'total_value' => $result ? (float)$result['total_value'] : 0,
               'expected_profit' => $result ? (float)($result['total_value'] - $result['total_cost']) : 0
           ];
       } catch (Exception $e) {
           error_log('خطأ أثناء حساب قيمة المخزون: ' . $e->getMessage());
           throw $e;
       }
   }

   /**
    * تحليل حركة المخزون
    * @param int $productId معرف المنتج
    * @param array $filters فلاتر البحث (اختياري)
    * @return array تحليل الحركة
    */
   public function analyzeProductMovement($productId, $filters = []) {
       try {
           // الحصول على معلومات المنتج
           $this->db->prepare("SELECT p.*, pc.name as category_name
                              FROM products p
                              LEFT JOIN product_categories pc ON p.category_id = pc.id
                              WHERE p.id = :id");
           $this->db->bind(':id', $productId);
           $product = $this->db->fetch();

           if (!$product) {
               throw new Exception('المنتج غير موجود');
           }

           // الحصول على حركات المنتج
           $sql = "SELECT t.*, u.name as user_name, b.name as branch_name
                   FROM inventory_transactions t
                   LEFT JOIN users u ON t.user_id = u.id
                   LEFT JOIN branches b ON t.branch_id = b.id
                   WHERE t.product_id = :product_id";

           $bindings = [':product_id' => $productId];

           if (!empty($filters['branch_id'])) {
               $sql .= " AND t.branch_id = :branch_id";
               $bindings[':branch_id'] = $filters['branch_id'];
           }

           if (!empty($filters['start_date']) && !empty($filters['end_date'])) {
               $sql .= " AND t.created_at BETWEEN :start_date AND :end_date";
               $bindings[':start_date'] = $filters['start_date'] . ' 00:00:00';
               $bindings[':end_date'] = $filters['end_date'] . ' 23:59:59';
           }

           $sql .= " ORDER BY t.created_at DESC";

           $this->db->prepare($sql);

           // ربط القيم
           foreach ($bindings as $param => $value) {
               $this->db->bind($param, $value);
           }

           $transactions = $this->db->fetchAll();

           // حساب إحصائيات المنتج
           $totalIn = 0;
           $totalOut = 0;
           $transactionsByType = ['in' => [], 'out' => []];

           foreach ($transactions as $transaction) {
               if ($transaction['transaction_type'] == 'in') {
                   $totalIn += $transaction['quantity'];
                   $transactionsByType['in'][] = $transaction;
               } else {
                   $totalOut += $transaction['quantity'];
                   $transactionsByType['out'][] = $transaction;
               }
           }

           // الحصول على مخزون المنتج في جميع الفروع
           $this->db->prepare("SELECT i.branch_id, i.quantity, b.name as branch_name
                              FROM inventory i
                              JOIN branches b ON i.branch_id = b.id
                              WHERE i.product_id = :product_id");
           $this->db->bind(':product_id', $productId);
           $branchStock = $this->db->fetchAll();

           // استرجاع مبيعات المنتج
           $sql = "SELECT i.id as invoice_id, i.invoice_number, i.created_at,
                          ii.quantity, ii.price, ii.total,
                          b.name as branch_name
                   FROM invoice_items ii
                   JOIN invoices i ON ii.invoice_id = i.id
                   LEFT JOIN branches b ON i.branch_id = b.id
                   WHERE ii.item_id = :product_id AND ii.item_type = 'product'";

           if (!empty($filters['start_date']) && !empty($filters['end_date'])) {
               $sql .= " AND i.created_at BETWEEN :start_date AND :end_date";
           }

           $sql .= " ORDER BY i.created_at DESC";

           $this->db->prepare($sql);
           $this->db->bind(':product_id', $productId);

           if (!empty($filters['start_date']) && !empty($filters['end_date'])) {
               $this->db->bind(':start_date', $filters['start_date'] . ' 00:00:00');
               $this->db->bind(':end_date', $filters['end_date'] . ' 23:59:59');
           }

           $sales = $this->db->fetchAll();

           // حساب إجمالي المبيعات
           $totalSoldQuantity = 0;
           $totalSalesAmount = 0;

           foreach ($sales as $sale) {
               $totalSoldQuantity += $sale['quantity'];
               $totalSalesAmount += $sale['total'];
           }

           return [
               'product' => $product,
               'transactions' => $transactions,
               'transactions_by_type' => $transactionsByType,
               'stats' => [
                   'total_in' => $totalIn,
                   'total_out' => $totalOut,
                   'net_change' => $totalIn - $totalOut,
                   'total_transactions' => count($transactions),
                   'total_sold_quantity' => $totalSoldQuantity,
                   'total_sales_amount' => $totalSalesAmount
               ],
               'branch_stock' => $branchStock,
               'sales' => $sales
           ];
       } catch (Exception $e) {
           error_log('خطأ أثناء تحليل حركة المنتج: ' . $e->getMessage());
           throw $e;
       }
   }

   /**
    * الحصول على منتجات راكدة
    * @param int $days عدد الأيام بدون حركة
    * @param int $branchId معرف الفرع (اختياري)
    * @return array قائمة المنتجات
    */
   public function getStagnantProducts($days = 30, $branchId = null) {
       try {
           $sql = "SELECT p.id, p.name, p.price, p.cost, p.min_quantity,
                          pc.name as category_name, i.quantity,
                          i.quantity * p.cost as stock_cost,
                          MAX(t.created_at) as last_movement_date,
                          DATEDIFF(NOW(), MAX(t.created_at)) as days_since_last_movement
                   FROM inventory i
                   JOIN products p ON i.product_id = p.id
                   LEFT JOIN product_categories pc ON p.category_id = pc.id
                   LEFT JOIN inventory_transactions t ON p.id = t.product_id AND i.branch_id = t.branch_id
                   WHERE i.quantity > 0
                   AND p.is_for_sale = 1";

           $bindings = [];

           if ($branchId) {
               $sql .= " AND i.branch_id = :branch_id";
               $bindings[':branch_id'] = $branchId;
           }

           $sql .= " GROUP BY p.id, p.name, p.price, p.cost, p.min_quantity,
                             pc.name, i.quantity, i.branch_id
                     HAVING days_since_last_movement >= :days OR days_since_last_movement IS NULL
                     ORDER BY days_since_last_movement DESC, stock_cost DESC";

           $this->db->prepare($sql);

           if ($branchId) {
               $this->db->bind(':branch_id', $branchId);
           }

           $this->db->bind(':days', $days, PDO::PARAM_INT);

           return $this->db->fetchAll();
       } catch (Exception $e) {
           error_log('خطأ أثناء استرجاع المنتجات الراكدة: ' . $e->getMessage());
           throw $e;
       }
   }

   /**
    * التنبؤ بموعد نفاد المخزون
    * @param int $productId معرف المنتج
    * @param int $branchId معرف الفرع
    * @param int $periodDays عدد أيام فترة التحليل السابقة
    * @return array نتائج التنبؤ
    */
   public function predictStockOutDate($productId, $branchId, $periodDays = 30) {
       try {
           // الحصول على الكمية الحالية
           $currentQuantity = $this->getProductStock($productId, $branchId);

           // الحصول على معلومات المنتج
           $this->db->prepare("SELECT p.name, p.min_quantity FROM products p WHERE p.id = :id");
           $this->db->bind(':id', $productId);
           $product = $this->db->fetch();

           if (!$product) {
               throw new Exception('المنتج غير موجود');
           }

           // حساب معدل الاستهلاك اليومي بناءً على فترة سابقة
           $startDate = date('Y-m-d', strtotime('-' . $periodDays . ' days'));
           $endDate = date('Y-m-d');

           $this->db->prepare("SELECT SUM(ii.quantity) as sold_quantity
                              FROM invoice_items ii
                              JOIN invoices i ON ii.invoice_id = i.id
                              WHERE ii.item_id = :product_id
                              AND ii.item_type = 'product'
                              AND i.branch_id = :branch_id
                              AND i.created_at BETWEEN :start_date AND :end_date");
           $this->db->bind(':product_id', $productId);
           $this->db->bind(':branch_id', $branchId);
           $this->db->bind(':start_date', $startDate . ' 00:00:00');
           $this->db->bind(':end_date', $endDate . ' 23:59:59');

           $result = $this->db->fetch();
           $soldQuantity = $result ? (int)$result['sold_quantity'] : 0;

           // حساب معدل الاستهلاك اليومي
           $dailyConsumptionRate = $soldQuantity / $periodDays;

           // تحديد موعد نفاد المخزون
           $daysUntilStockOut = $dailyConsumptionRate > 0 ? ceil($currentQuantity / $dailyConsumptionRate) : null;
           $daysUntilLowStock = $dailyConsumptionRate > 0 ? ceil(($currentQuantity - $product['min_quantity']) / $dailyConsumptionRate) : null;

           $stockOutDate = $daysUntilStockOut ? date('Y-m-d', strtotime('+' . $daysUntilStockOut . ' days')) : null;
           $lowStockDate = $daysUntilLowStock ? date('Y-m-d', strtotime('+' . $daysUntilLowStock . ' days')) : null;

           return [
               'product_id' => $productId,
               'product_name' => $product['name'],
               'current_quantity' => $currentQuantity,
               'min_quantity' => $product['min_quantity'],
               'period_days' => $periodDays,
               'sold_quantity' => $soldQuantity,
               'daily_consumption_rate' => $dailyConsumptionRate,
               'days_until_stock_out' => $daysUntilStockOut,
               'days_until_low_stock' => $daysUntilLowStock,
               'stock_out_date' => $stockOutDate,
               'low_stock_date' => $lowStockDate
           ];
       } catch (Exception $e) {
           error_log('خطأ أثناء التنبؤ بموعد نفاد المخزون: ' . $e->getMessage());
           throw $e;
       }
   }
   /**
    * تقليل كمية المنتج في المخزون (مستخدمة عند البيع)
    * @param int $productId معرف المنتج
    * @param int $quantity الكمية المطلوب تقليلها
    * @param int $branchId معرف الفرع
    * @return bool نجاح أو فشل العملية
    */
   public function decreaseStock($productId, $quantity, $branchId) {
       try {
           // التحقق من توفر المنتج
           if (!$this->isProductAvailable($productId, $quantity, $branchId)) {
               throw new Exception("المنتج غير متوفر بالكمية المطلوبة");
           }

           // استخدام دالة removeStock لتقليل المخزون وتسجيل حركة المخزون
           return $this->removeStock($productId, $quantity, $branchId, 'بيع منتج');
       } catch (Exception $e) {
           error_log('خطأ أثناء تقليل كمية المنتج في المخزون: ' . $e->getMessage());
           throw $e;
       }
   }
   /**
    * تسجيل حركة المخزون
    * @param int $productId معرف المنتج
    * @param int $branchId معرف الفرع
    * @param int $quantity الكمية (موجبة للإضافة، سالبة للسحب)
    * @param string $notes ملاحظات
    * @return bool نجاح أو فشل العملية
    */
   public function logInventoryMovement($productId, $branchId, $quantity, $notes = '') {
       try {
           // الحصول على الكمية الحالية قبل التغيير
           $previousQuantity = $this->getProductStock($productId, $branchId);

           // حساب الكمية الجديدة بعد التغيير
           $currentQuantity = $previousQuantity + $quantity;

           // تحديد نوع الحركة
           $transactionType = $quantity > 0 ? 'in' : 'out';
           $absoluteQuantity = abs($quantity);

           // إضافة سجل حركة المخزون
           $this->db->prepare("INSERT INTO inventory_transactions
                              (product_id, transaction_type, quantity, previous_quantity, current_quantity, notes, user_id, branch_id)
                              VALUES (:product_id, :transaction_type, :quantity, :previous_quantity, :current_quantity, :notes, :user_id, :branch_id)");
           $this->db->bind(':product_id', $productId);
           $this->db->bind(':transaction_type', $transactionType);
           $this->db->bind(':quantity', $absoluteQuantity);
           $this->db->bind(':previous_quantity', $previousQuantity);
           $this->db->bind(':current_quantity', $currentQuantity);
           $this->db->bind(':notes', $notes);
           $this->db->bind(':user_id', $_SESSION['user_id'] ?? null);
           $this->db->bind(':branch_id', $branchId);

           return $this->db->execute();
       } catch (Exception $e) {
           error_log('خطأ أثناء تسجيل حركة المخزون: ' . $e->getMessage());
           throw $e;
       }
   }
   /**
    * إضافة حركة مخزون جديدة
    * @param array $data بيانات حركة المخزون
    * @return int معرف حركة المخزون الجديدة
    */
   public function addInventoryTransaction($data) {
       try {
           // التحقق من وجود البيانات المطلوبة
           if (empty($data['product_id']) || empty($data['transaction_type']) ||
               !isset($data['quantity']) || !isset($data['previous_quantity']) ||
               !isset($data['current_quantity'])) {
               throw new Exception('بيانات حركة المخزون غير مكتملة');
           }

           // التحقق من نوع الحركة
           if (!in_array($data['transaction_type'], ['in', 'out'])) {
               throw new Exception('نوع حركة المخزون غير صالح');
           }

           // التحقق من وجود معاملة نشطة
           $transactionStartedHere = false;
           if (!$this->db->inTransaction()) {
               $this->db->beginTransaction();
               $transactionStartedHere = true;
           }

           // إضافة سجل حركة المخزون
           $this->db->prepare("INSERT INTO inventory_transactions
                             (product_id, transaction_type, quantity, previous_quantity,
                              current_quantity, notes, user_id, branch_id)
                             VALUES (:product_id, :transaction_type, :quantity, :previous_quantity,
                                    :current_quantity, :notes, :user_id, :branch_id)");

           $this->db->bind(':product_id', $data['product_id']);
           $this->db->bind(':transaction_type', $data['transaction_type']);
           $this->db->bind(':quantity', $data['quantity']);
           $this->db->bind(':previous_quantity', $data['previous_quantity']);
           $this->db->bind(':current_quantity', $data['current_quantity']);
           $this->db->bind(':notes', $data['notes'] ?? '');
           $this->db->bind(':user_id', $data['user_id'] ?? ($_SESSION['user_id'] ?? null));
           $this->db->bind(':branch_id', $data['branch_id'] ?? null);

           $this->db->execute();
           $transactionId = (int)$this->db->lastInsertId();

           // تأكيد المعاملة إذا بدأناها هنا
           if ($transactionStartedHere && $this->db->inTransaction()) {
               $this->db->commit();
           }

           return $transactionId;
       } catch (Exception $e) {
           // التراجع عن المعاملة في حالة حدوث خطأ (فقط إذا بدأناها هنا)
           if (isset($transactionStartedHere) && $transactionStartedHere && $this->db->inTransaction()) {
               $this->db->rollBack();
           }

           error_log('خطأ في إضافة حركة مخزون: ' . $e->getMessage());
           throw $e;
       }
   }

   /**
    * الحصول على حركات المخزون
    * @param array $filters فلاتر البحث (اختياري)
    * @return array قائمة حركات المخزون
    */
   public function getInventoryTransactions($filters = []) {
       try {
           $sql = "SELECT it.*, p.name as product_name, p.is_for_sale, u.name as user_name, b.name as branch_name
                   FROM inventory_transactions it
                   JOIN products p ON it.product_id = p.id
                   LEFT JOIN users u ON it.user_id = u.id
                   LEFT JOIN branches b ON it.branch_id = b.id";

           $whereConditions = [];
           $bindings = [];

           // تطبيق الفلاتر
           if (!empty($filters['product_id'])) {
               $whereConditions[] = "it.product_id = :product_id";
               $bindings[':product_id'] = $filters['product_id'];
           }

           if (!empty($filters['branch_id'])) {
               $whereConditions[] = "it.branch_id = :branch_id";
               $bindings[':branch_id'] = $filters['branch_id'];
           }

           if (!empty($filters['transaction_type'])) {
               $whereConditions[] = "it.transaction_type = :transaction_type";
               $bindings[':transaction_type'] = $filters['transaction_type'];
           }

           if (!empty($filters['start_date']) && !empty($filters['end_date'])) {
               $whereConditions[] = "DATE(it.created_at) BETWEEN :start_date AND :end_date";
               $bindings[':start_date'] = $filters['start_date'];
               $bindings[':end_date'] = $filters['end_date'];
           }

           if (!empty($filters['category_id'])) {
               $whereConditions[] = "p.category_id = :category_id";
               $bindings[':category_id'] = $filters['category_id'];
           }

           // إضافة شروط WHERE إذا وجدت
           if (!empty($whereConditions)) {
               $sql .= " WHERE " . implode(' AND ', $whereConditions);
           }

           // ترتيب النتائج
           $sql .= " ORDER BY it.created_at DESC";

           // إضافة الحد والإزاحة إذا وجدت
           if (!empty($filters['limit'])) {
               $sql .= " LIMIT :limit";
               if (!empty($filters['offset'])) {
                   $sql .= " OFFSET :offset";
               }
           }

           $this->db->prepare($sql);

           // ربط القيم
           foreach ($bindings as $param => $value) {
               $this->db->bind($param, $value);
           }

           // ربط الحد والإزاحة إذا وجدت
           if (!empty($filters['limit'])) {
               $this->db->bind(':limit', $filters['limit'], PDO::PARAM_INT);
               if (!empty($filters['offset'])) {
                   $this->db->bind(':offset', $filters['offset'], PDO::PARAM_INT);
               }
           }

           return $this->db->fetchAll();
       } catch (Exception $e) {
           error_log('خطأ أثناء استرجاع حركات المخزون: ' . $e->getMessage());
           throw $e;
       }
   }
}
