<?php
/**
 * API لتنظيف الإشعارات المكررة
 */

// تعريف ثابت لمنع الوصول المباشر للملفات المرتبطة
define('BASEPATH', true);

// استدعاء ملفات التهيئة
require_once '../config/init.php';

// التحقق من تسجيل الدخول للوصول إلى API
requireLogin();

// التحقق من الصلاحيات (للمديرين فقط)
if (!isAdmin() && !isManager()) {
    header('Content-Type: application/json');
    echo json_encode([
        'status' => 'error',
        'message' => 'ليس لديك صلاحية للوصول إلى هذه الوظيفة',
        'code' => 403
    ]);
    exit;
}

// إنشاء اتصال بقاعدة البيانات
$db = new Database();

// الاستجابة الافتراضية
$response = [
    'status' => 'error',
    'message' => 'لم يتم تنفيذ أي إجراء',
    'code' => 400
];

try {
    // حذف الإشعارات المكررة
    // نحتفظ بأحدث إشعار لكل موعد ونوع إشعار ومستلم
    $db->prepare("
        DELETE n1 FROM notifications n1
        INNER JOIN notifications n2 ON
            n1.related_id = n2.related_id AND
            n1.related_type = n2.related_type AND
            n1.type = n2.type AND
            n1.recipient_id = n2.recipient_id AND
            n1.recipient_type = n2.recipient_type AND
            n1.id < n2.id AND
            n1.is_read = 0 AND
            DATE(n1.created_at) = CURRENT_DATE()
    ");
    $db->execute();
    $deletedCount = $db->rowCount();
    
    // حذف الإشعارات القديمة (أكثر من 7 أيام)
    $db->prepare("
        DELETE FROM notifications
        WHERE created_at < DATE_SUB(NOW(), INTERVAL 7 DAY)
    ");
    $db->execute();
    $oldDeletedCount = $db->rowCount();
    
    $response = [
        'status' => 'success',
        'message' => 'تم تنظيف الإشعارات بنجاح',
        'code' => 200,
        'data' => [
            'deleted_duplicates' => $deletedCount,
            'deleted_old' => $oldDeletedCount
        ]
    ];
} catch (Exception $e) {
    error_log('خطأ في API تنظيف الإشعارات: ' . $e->getMessage());
    
    $response = [
        'status' => 'error',
        'message' => 'حدث خطأ أثناء معالجة الطلب: ' . $e->getMessage(),
        'code' => 500
    ];
}

// إرسال الاستجابة
header('Content-Type: application/json');
echo json_encode($response);
exit;
