<?php
/**
 * فئة المصروفات
 * تتعامل مع إدارة مصروفات صالون الحلاقة والكوافير
 */

// منع الوصول المباشر للملف
if (!defined('BASEPATH')) {
    exit('لا يمكن الوصول المباشر لهذا الملف');
}

class Expense {
    private $db;

    /**
     * إنشاء كائن من فئة المصروفات
     * @param Database $db كائن قاعدة البيانات
     */
    public function __construct($db) {
        $this->db = $db;
    }

    /**
     * إضافة مصروف جديد
     * @param array $data بيانات المصروف
     * @return int|false معرف المصروف الجديد أو false إذا فشلت العملية
     */
    public function addExpense($data) {
        try {
            $this->db->prepare("INSERT INTO expenses (category_id, amount, description, date, payment_method, user_id, branch_id, end_day_id)
                              VALUES (:category_id, :amount, :description, :date, :payment_method, :user_id, :branch_id, :end_day_id)");

            $this->db->bind(':category_id', $data['category_id'] ?? null);
            $this->db->bind(':amount', $data['amount']);
            $this->db->bind(':description', $data['description'] ?? null);
            $this->db->bind(':date', $data['date'] ?? date('Y-m-d'));
            $this->db->bind(':payment_method', $data['payment_method'] ?? 'cash');
            $this->db->bind(':user_id', $data['user_id'] ?? $_SESSION['user_id'] ?? null);
            $this->db->bind(':branch_id', $data['branch_id'] ?? $_SESSION['user_branch_id'] ?? null);
            $this->db->bind(':end_day_id', $data['end_day_id'] ?? null);

            $this->db->execute();
            return (int)$this->db->lastInsertId();
        } catch (Exception $e) {
            error_log('خطأ أثناء إضافة المصروف: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * تحديث بيانات مصروف
     * @param int $expenseId معرف المصروف
     * @param array $data البيانات المحدثة
     * @return bool نجاح أو فشل العملية
     */
    public function updateExpense($expenseId, $data) {
        try {
            // التحقق من وجود المصروف
            $expense = $this->getExpenseById($expenseId);
            if (!$expense) {
                throw new Exception('لم يتم العثور على المصروف');
            }

            // التحقق مما إذا كان المصروف مرتبط بنهاية يوم مغلقة
            if (!empty($expense['end_day_id'])) {
                $this->db->prepare("SELECT closed_at FROM end_days WHERE id = :end_day_id");
                $this->db->bind(':end_day_id', $expense['end_day_id']);
                $endDay = $this->db->fetch();

                if ($endDay && !empty($endDay['closed_at'])) {
                    throw new Exception('لا يمكن تعديل المصروف لأنه مرتبط بنهاية يوم مغلقة');
                }
            }

            $this->db->prepare("UPDATE expenses
                              SET category_id = :category_id,
                                  amount = :amount,
                                  description = :description,
                                  date = :date,
                                  payment_method = :payment_method,
                                  branch_id = :branch_id
                              WHERE id = :id");

            $this->db->bind(':category_id', $data['category_id'] ?? $expense['category_id']);
            $this->db->bind(':amount', $data['amount'] ?? $expense['amount']);
            $this->db->bind(':description', $data['description'] ?? $expense['description']);
            $this->db->bind(':date', $data['date'] ?? $expense['date']);
            $this->db->bind(':payment_method', $data['payment_method'] ?? $expense['payment_method']);
            $this->db->bind(':branch_id', $data['branch_id'] ?? $expense['branch_id']);
            $this->db->bind(':id', $expenseId);

            return $this->db->execute();
        } catch (Exception $e) {
            error_log('خطأ أثناء تحديث بيانات المصروف: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * حذف مصروف
     * @param int $expenseId معرف المصروف
     * @return bool نجاح أو فشل العملية
     */
    public function deleteExpense($expenseId) {
        try {
            // التحقق من وجود المصروف
            $expense = $this->getExpenseById($expenseId);
            if (!$expense) {
                throw new Exception('لم يتم العثور على المصروف');
            }

            // التحقق مما إذا كان المصروف مرتبط بنهاية يوم مغلقة
            if (!empty($expense['end_day_id'])) {
                $this->db->prepare("SELECT closed_at FROM end_days WHERE id = :end_day_id");
                $this->db->bind(':end_day_id', $expense['end_day_id']);
                $endDay = $this->db->fetch();

                if ($endDay && !empty($endDay['closed_at'])) {
                    throw new Exception('لا يمكن حذف المصروف لأنه مرتبط بنهاية يوم مغلقة');
                }
            }

            $this->db->prepare("DELETE FROM expenses WHERE id = :id");
            $this->db->bind(':id', $expenseId);

            return $this->db->execute();
        } catch (Exception $e) {
            error_log('خطأ أثناء حذف المصروف: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * الحصول على بيانات مصروف بواسطة المعرف
     * @param int $expenseId معرف المصروف
     * @return array|false بيانات المصروف أو false إذا لم يتم العثور عليه
     */
    public function getExpenseById($expenseId) {
        try {
            $this->db->prepare("SELECT e.*, c.name as category_name, u.name as user_name, b.name as branch_name
                              FROM expenses e
                              LEFT JOIN expense_categories c ON e.category_id = c.id
                              LEFT JOIN users u ON e.user_id = u.id
                              LEFT JOIN branches b ON e.branch_id = b.id
                              WHERE e.id = :id");
            $this->db->bind(':id', $expenseId);

            return $this->db->fetch();
        } catch (Exception $e) {
            error_log('خطأ أثناء استرجاع بيانات المصروف: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * الحصول على قائمة المصروفات
     * @param array $filters فلاتر البحث (اختياري)
     * @return array قائمة المصروفات
     */
    public function getExpenses($filters = []) {
        try {
            $sql = "SELECT e.*, c.name as category_name, u.name as user_name, b.name as branch_name
                    FROM expenses e
                    LEFT JOIN expense_categories c ON e.category_id = c.id
                    LEFT JOIN users u ON e.user_id = u.id
                    LEFT JOIN branches b ON e.branch_id = b.id";

            $whereConditions = [];
            $bindings = [];

            // تطبيق الفلاتر
            if (!empty($filters['search'])) {
                $whereConditions[] = "(e.description LIKE :search OR c.name LIKE :search)";
                $bindings[':search'] = '%' . $filters['search'] . '%';
            }

            if (!empty($filters['category_id'])) {
                $whereConditions[] = "e.category_id = :category_id";
                $bindings[':category_id'] = $filters['category_id'];
            }

            if (!empty($filters['branch_id'])) {
                $whereConditions[] = "e.branch_id = :branch_id";
                $bindings[':branch_id'] = $filters['branch_id'];
            }

            if (!empty($filters['payment_method'])) {
                $whereConditions[] = "e.payment_method = :payment_method";
                $bindings[':payment_method'] = $filters['payment_method'];
            }

            if (!empty($filters['user_id'])) {
                $whereConditions[] = "e.user_id = :user_id";
                $bindings[':user_id'] = $filters['user_id'];
            }

            if (!empty($filters['end_day_id'])) {
                $whereConditions[] = "e.end_day_id = :end_day_id";
                $bindings[':end_day_id'] = $filters['end_day_id'];
            }

            if (!empty($filters['start_date']) && !empty($filters['end_date'])) {
                $whereConditions[] = "e.date BETWEEN :start_date AND :end_date";
                $bindings[':start_date'] = $filters['start_date'];
                $bindings[':end_date'] = $filters['end_date'];
            } elseif (!empty($filters['date'])) {
                $whereConditions[] = "e.date = :date";
                $bindings[':date'] = $filters['date'];
            } elseif (!empty($filters['month']) && !empty($filters['year'])) {
                $whereConditions[] = "MONTH(e.date) = :month AND YEAR(e.date) = :year";
                $bindings[':month'] = $filters['month'];
                $bindings[':year'] = $filters['year'];
            } elseif (!empty($filters['year'])) {
                $whereConditions[] = "YEAR(e.date) = :year";
                $bindings[':year'] = $filters['year'];
            }

            // إضافة شروط WHERE إذا وجدت
            if (!empty($whereConditions)) {
                $sql .= " WHERE " . implode(' AND ', $whereConditions);
            }

            // ترتيب النتائج
            if (!empty($filters['sort_by'])) {
                $sql .= " ORDER BY " . $filters['sort_by'];

                if (!empty($filters['sort_dir']) && in_array(strtoupper($filters['sort_dir']), ['ASC', 'DESC'])) {
                    $sql .= " " . strtoupper($filters['sort_dir']);
                } else {
                    $sql .= " DESC";
                }
            } else {
                $sql .= " ORDER BY e.date DESC, e.id DESC";
            }

            // إضافة الحد والإزاحة إذا وجدت
            if (!empty($filters['limit'])) {
                $sql .= " LIMIT :limit";
                if (!empty($filters['offset'])) {
                    $sql .= " OFFSET :offset";
                }
            }

            $this->db->prepare($sql);

            // ربط القيم
            foreach ($bindings as $param => $value) {
                $this->db->bind($param, $value);
            }

            // ربط الحد والإزاحة إذا وجدت
            if (!empty($filters['limit'])) {
                $this->db->bind(':limit', $filters['limit'], PDO::PARAM_INT);
                if (!empty($filters['offset'])) {
                    $this->db->bind(':offset', $filters['offset'], PDO::PARAM_INT);
                }
            }

            return $this->db->fetchAll();
        } catch (Exception $e) {
            error_log('خطأ أثناء استرجاع قائمة المصروفات: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * الحصول على عدد المصروفات
     * @param array $filters فلاتر البحث (اختياري)
     * @return int عدد المصروفات
     */
    public function getExpensesCount($filters = []) {
        try {
            $sql = "SELECT COUNT(*) FROM expenses e LEFT JOIN expense_categories c ON e.category_id = c.id";

            $whereConditions = [];
            $bindings = [];

            // تطبيق الفلاتر الأساسية
            if (!empty($filters['search'])) {
                $whereConditions[] = "(e.description LIKE :search OR c.name LIKE :search)";
                $bindings[':search'] = '%' . $filters['search'] . '%';
            }

            if (!empty($filters['category_id'])) {
                $whereConditions[] = "e.category_id = :category_id";
                $bindings[':category_id'] = $filters['category_id'];
            }

            if (!empty($filters['branch_id'])) {
                $whereConditions[] = "e.branch_id = :branch_id";
                $bindings[':branch_id'] = $filters['branch_id'];
            }

            if (!empty($filters['payment_method'])) {
                $whereConditions[] = "e.payment_method = :payment_method";
                $bindings[':payment_method'] = $filters['payment_method'];
            }

            if (!empty($filters['start_date']) && !empty($filters['end_date'])) {
                $whereConditions[] = "e.date BETWEEN :start_date AND :end_date";
                $bindings[':start_date'] = $filters['start_date'];
                $bindings[':end_date'] = $filters['end_date'];
            } elseif (!empty($filters['date'])) {
                $whereConditions[] = "e.date = :date";
                $bindings[':date'] = $filters['date'];
            } elseif (!empty($filters['month']) && !empty($filters['year'])) {
                $whereConditions[] = "MONTH(e.date) = :month AND YEAR(e.date) = :year";
                $bindings[':month'] = $filters['month'];
                $bindings[':year'] = $filters['year'];
            }

            // إضافة شروط WHERE إذا وجدت
            if (!empty($whereConditions)) {
                $sql .= " WHERE " . implode(' AND ', $whereConditions);
            }

            $this->db->prepare($sql);

            // ربط القيم
            foreach ($bindings as $param => $value) {
                $this->db->bind($param, $value);
            }

            return $this->db->fetchColumn();
        } catch (Exception $e) {
            error_log('خطأ أثناء حساب عدد المصروفات: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * حساب إجمالي المصروفات
     * @param array $filters فلاتر البحث (اختياري)
     * @return float إجمالي المصروفات
     */
    public function getTotalExpenses($filters = []) {
        try {
            $sql = "SELECT SUM(amount) FROM expenses e";

            $whereConditions = [];
            $bindings = [];

            // تطبيق الفلاتر
            if (!empty($filters['category_id'])) {
                $whereConditions[] = "e.category_id = :category_id";
                $bindings[':category_id'] = $filters['category_id'];
            }

            if (!empty($filters['branch_id'])) {
                $whereConditions[] = "e.branch_id = :branch_id";
                $bindings[':branch_id'] = $filters['branch_id'];
            }

            if (!empty($filters['payment_method'])) {
                $whereConditions[] = "e.payment_method = :payment_method";
                $bindings[':payment_method'] = $filters['payment_method'];
            }

            if (!empty($filters['user_id'])) {
                $whereConditions[] = "e.user_id = :user_id";
                $bindings[':user_id'] = $filters['user_id'];
            }

            if (!empty($filters['end_day_id'])) {
                $whereConditions[] = "e.end_day_id = :end_day_id";
                $bindings[':end_day_id'] = $filters['end_day_id'];
            }

            if (!empty($filters['start_date']) && !empty($filters['end_date'])) {
                $whereConditions[] = "e.date BETWEEN :start_date AND :end_date";
                $bindings[':start_date'] = $filters['start_date'];
                $bindings[':end_date'] = $filters['end_date'];
            } elseif (!empty($filters['date'])) {
                $whereConditions[] = "e.date = :date";
                $bindings[':date'] = $filters['date'];
            } elseif (!empty($filters['month']) && !empty($filters['year'])) {
                $whereConditions[] = "MONTH(e.date) = :month AND YEAR(e.date) = :year";
                $bindings[':month'] = $filters['month'];
                $bindings[':year'] = $filters['year'];
            } elseif (!empty($filters['year'])) {
                $whereConditions[] = "YEAR(e.date) = :year";
                $bindings[':year'] = $filters['year'];
            }

            // إضافة شروط WHERE إذا وجدت
            if (!empty($whereConditions)) {
                $sql .= " WHERE " . implode(' AND ', $whereConditions);
            }

            $this->db->prepare($sql);

            // ربط القيم
            foreach ($bindings as $param => $value) {
                $this->db->bind($param, $value);
            }

            $result = $this->db->fetchColumn();
            return $result ? (float)$result : 0;
        } catch (Exception $e) {
            error_log('خطأ أثناء حساب إجمالي المصروفات: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * إضافة فئة مصروفات جديدة
     * @param array $data بيانات الفئة
     * @return int|false معرف الفئة الجديدة أو false إذا فشلت العملية
     */
    public function addExpenseCategory($data) {
        try {
            // التحقق من عدم وجود فئة بنفس الاسم
            $this->db->prepare("SELECT id FROM expense_categories WHERE name = :name");
            $this->db->bind(':name', $data['name']);

            if ($this->db->fetch()) {
                throw new Exception('توجد فئة بنفس الاسم بالفعل');
            }

            $this->db->prepare("INSERT INTO expense_categories (name, description) VALUES (:name, :description)");
            $this->db->bind(':name', $data['name']);
            $this->db->bind(':description', $data['description'] ?? null);

            $this->db->execute();
            return (int)$this->db->lastInsertId();
        } catch (Exception $e) {
            error_log('خطأ أثناء إضافة فئة المصروفات: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * تحديث بيانات فئة مصروفات
     * @param int $categoryId معرف الفئة
     * @param array $data بيانات الفئة المحدثة
     * @return bool نجاح أو فشل العملية
     */
    public function updateExpenseCategory($categoryId, $data) {
        try {
            // التحقق من وجود الفئة
            $this->db->prepare("SELECT id FROM expense_categories WHERE id = :id");
            $this->db->bind(':id', $categoryId);

            if (!$this->db->fetch()) {
                throw new Exception('فئة المصروفات غير موجودة');
            }

            // التحقق من عدم وجود فئة أخرى بنفس الاسم
            $this->db->prepare("SELECT id FROM expense_categories WHERE name = :name AND id != :id");
            $this->db->bind(':name', $data['name']);
            $this->db->bind(':id', $categoryId);

            if ($this->db->fetch()) {
                throw new Exception('توجد فئة أخرى بنفس الاسم');
            }

            $this->db->prepare("UPDATE expense_categories SET name = :name, description = :description WHERE id = :id");
            $this->db->bind(':name', $data['name']);
            $this->db->bind(':description', $data['description'] ?? null);
            $this->db->bind(':id', $categoryId);

            return $this->db->execute();
        } catch (Exception $e) {
            error_log('خطأ أثناء تحديث بيانات فئة المصروفات: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * حذف فئة مصروفات
     * @param int $categoryId معرف الفئة
     * @return bool نجاح أو فشل العملية
     */
    public function deleteExpenseCategory($categoryId) {
        try {
            // التحقق من عدم وجود مصروفات مرتبطة بالفئة
            $this->db->prepare("SELECT COUNT(*) FROM expenses WHERE category_id = :category_id");
            $this->db->bind(':category_id', $categoryId);

            if ($this->db->fetchColumn() > 0) {
                throw new Exception('لا يمكن حذف الفئة لوجود مصروفات مرتبطة بها');
            }

            $this->db->prepare("DELETE FROM expense_categories WHERE id = :id");
            $this->db->bind(':id', $categoryId);

            return $this->db->execute();
        } catch (Exception $e) {
            error_log('خطأ أثناء حذف فئة المصروفات: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * الحصول على بيانات فئة مصروفات بواسطة المعرف
     * @param int $categoryId معرف الفئة
     * @return array|false بيانات الفئة أو false إذا لم يتم العثور عليها
     */
    public function getExpenseCategoryById($categoryId) {
        try {
            $this->db->prepare("SELECT * FROM expense_categories WHERE id = :id");
            $this->db->bind(':id', $categoryId);

            return $this->db->fetch();
        } catch (Exception $e) {
            error_log('خطأ أثناء استرجاع بيانات فئة المصروفات: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * الحصول على قائمة فئات المصروفات
     * @return array قائمة الفئات
     */
    public function getExpenseCategories() {
        try {
            $this->db->prepare("SELECT * FROM expense_categories ORDER BY name ASC");
            return $this->db->fetchAll();
        } catch (Exception $e) {
            error_log('خطأ أثناء استرجاع قائمة فئات المصروفات: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * الحصول على تقرير المصروفات حسب الفئة
     * @param array $filters فلاتر البحث (اختياري)
     * @return array التقرير
     */
    public function getExpensesByCategory($filters = []) {
        try {
            $sql = "SELECT
                    COALESCE(c.id, 0) as category_id,
                    COALESCE(c.name, 'بدون تصنيف') as category_name,
                    SUM(e.amount) as total,
                    COUNT(e.id) as count
                    FROM expenses e
                    LEFT JOIN expense_categories c ON e.category_id = c.id";

            $whereConditions = [];
            $bindings = [];

            // تطبيق الفلاتر
            if (!empty($filters['branch_id'])) {
                $whereConditions[] = "e.branch_id = :branch_id";
                $bindings[':branch_id'] = $filters['branch_id'];
            }

            if (!empty($filters['start_date']) && !empty($filters['end_date'])) {
                $whereConditions[] = "e.date BETWEEN :start_date AND :end_date";
                $bindings[':start_date'] = $filters['start_date'];
                $bindings[':end_date'] = $filters['end_date'];
            } elseif (!empty($filters['month']) && !empty($filters['year'])) {
                $whereConditions[] = "MONTH(e.date) = :month AND YEAR(e.date) = :year";
                $bindings[':month'] = $filters['month'];
                $bindings[':year'] = $filters['year'];
            } elseif (!empty($filters['year'])) {
                $whereConditions[] = "YEAR(e.date) = :year";
                $bindings[':year'] = $filters['year'];
            }

            // إضافة شروط WHERE إذا وجدت
            if (!empty($whereConditions)) {
                $sql .= " WHERE " . implode(' AND ', $whereConditions);
            }

            $sql .= " GROUP BY COALESCE(c.id, 0), COALESCE(c.name, 'بدون تصنيف') ORDER BY total DESC";

            $this->db->prepare($sql);

            // ربط القيم
            foreach ($bindings as $param => $value) {
                $this->db->bind($param, $value);
            }

            return $this->db->fetchAll();
        } catch (Exception $e) {
            error_log('خطأ أثناء استرجاع تقرير المصروفات حسب الفئة: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * الحصول على تقرير المصروفات الشهرية
     * @param int $year السنة
     * @param int $branchId معرف الفرع (اختياري)
     * @return array التقرير
     */
    public function getMonthlyExpensesReport($year, $branchId = null) {
        try {
            $sql = "SELECT
                      MONTH(e.date) as month,
                      SUM(e.amount) as total
                    FROM expenses e
                    WHERE YEAR(e.date) = :year";

            $bindings = [':year' => $year];

            if ($branchId) {
                $sql .= " AND e.branch_id = :branch_id";
                $bindings[':branch_id'] = $branchId;
            }

            $sql .= " GROUP BY MONTH(e.date) ORDER BY MONTH(e.date) ASC";

            $this->db->prepare($sql);

            foreach ($bindings as $param => $value) {
                $this->db->bind($param, $value);
            }

            // تنسيق النتائج في مصفوفة بمفاتيح الأشهر (1-12)
            $result = $this->db->fetchAll();
            $monthlyReport = array_fill(1, 12, 0); // إنشاء مصفوفة للأشهر 1-12 بقيمة افتراضية 0

            foreach ($result as $row) {
                $monthlyReport[$row['month']] = (float)$row['total'];
            }

            return $monthlyReport;
        } catch (Exception $e) {
            error_log('خطأ أثناء استرجاع تقرير المصروفات الشهرية: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * ربط المصروفات بنهاية اليوم
     * @param int $endDayId معرف نهاية اليوم
     * @param string $date تاريخ اليوم (للتوافق مع الواجهة القديمة)
     * @param int $branchId معرف الفرع
     * @return int عدد المصروفات التي تم ربطها
     */
    public function linkExpensesToEndDay($endDayId, $date, $branchId) {
        try {
            // الحصول على معلومات نهاية اليوم
            $this->db->prepare("SELECT date FROM end_days WHERE id = :end_day_id");
            $this->db->bind(':end_day_id', $endDayId);
            $endDayInfo = $this->db->fetch();

            if (!$endDayInfo) {
                throw new Exception('لم يتم العثور على نهاية اليوم');
            }

            // تحديث جميع المصروفات التي ليس لها يوم نهاية محدد
            $this->db->prepare("UPDATE expenses
                              SET end_day_id = :end_day_id
                              WHERE branch_id = :branch_id
                              AND DATE(date) = :date
                              AND (end_day_id IS NULL OR end_day_id = 0)");

            $this->db->bind(':end_day_id', $endDayId);
            $this->db->bind(':date', $endDayInfo['date']);
            $this->db->bind(':branch_id', $branchId);
            $this->db->execute();

            // الحصول على عدد الصفوف المتأثرة
            return $this->db->rowCount();
        } catch (Exception $e) {
            error_log('خطأ أثناء ربط المصروفات بنهاية اليوم: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * الحصول على مصروفات اليوم
     * @param string $date التاريخ
     * @param int $branchId معرف الفرع (اختياري)
     * @return array قائمة المصروفات
     */
    public function getDailyExpenses($date, $branchId = null) {
        try {
            $filters = ['date' => $date];

            if ($branchId) {
                $filters['branch_id'] = $branchId;
            }

            return $this->getExpenses($filters);
        } catch (Exception $e) {
            error_log('خطأ أثناء استرجاع مصروفات اليوم: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * حساب إجمالي مصروفات اليوم
     * @param string $date التاريخ
     * @param int $branchId معرف الفرع (اختياري)
     * @param string $paymentMethod طريقة الدفع (اختياري)
     * @return float إجمالي المصروفات
     */
    public function getDailyExpensesTotal($date, $branchId = null, $paymentMethod = null) {
        try {
            $filters = ['date' => $date];

            if ($branchId) {
                $filters['branch_id'] = $branchId;
            }

            if ($paymentMethod) {
                $filters['payment_method'] = $paymentMethod;
            }

            return $this->getTotalExpenses($filters);
        } catch (Exception $e) {
            error_log('خطأ أثناء حساب إجمالي مصروفات اليوم: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * مقارنة المصروفات بين فترتين زمنيتين
     * @param array $period1 الفترة الأولى (start_date, end_date)
     * @param array $period2 الفترة الثانية (start_date, end_date)
     * @param int $branchId معرف الفرع (اختياري)
     * @return array نتائج المقارنة
     */
    public function compareExpensesPeriods($period1, $period2, $branchId = null) {
        try {
            // الحصول على إجمالي المصروفات للفترة الأولى
            $filters1 = [
                'start_date' => $period1['start_date'],
                'end_date' => $period1['end_date']
            ];

            if ($branchId) {
                $filters1['branch_id'] = $branchId;
            }

            $total1 = $this->getTotalExpenses($filters1);

            // الحصول على إجمالي المصروفات للفترة الثانية
            $filters2 = [
                'start_date' => $period2['start_date'],
                'end_date' => $period2['end_date']
            ];

            if ($branchId) {
                $filters2['branch_id'] = $branchId;
            }

            $total2 = $this->getTotalExpenses($filters2);

            // حساب الفرق والنسبة المئوية
            $difference = $total2 - $total1;
            $percentChange = $total1 > 0 ? ($difference / $total1) * 100 : 0;

            // الحصول على المصروفات حسب الفئة للفترتين
            $categoryExpenses1 = $this->getExpensesByCategory($filters1);
            $categoryExpenses2 = $this->getExpensesByCategory($filters2);

            // تنظيم البيانات حسب الفئة
            $categoryComparison = [];

            // تجميع كل الفئات من الفترتين
            $allCategories = [];

            foreach ($categoryExpenses1 as $category) {
                $allCategories[$category['category_id']] = $category['category_name'];
            }

            foreach ($categoryExpenses2 as $category) {
                $allCategories[$category['category_id']] = $category['category_name'];
            }

            // مقارنة المصروفات حسب الفئة
            foreach ($allCategories as $categoryId => $categoryName) {
                $categoryTotal1 = 0;
                $categoryTotal2 = 0;

                // البحث عن المبلغ في الفترة الأولى
                foreach ($categoryExpenses1 as $category) {
                    if ($category['category_id'] == $categoryId) {
                        $categoryTotal1 = (float)$category['total'];
                        break;
                    }
                }

                // البحث عن المبلغ في الفترة الثانية
                foreach ($categoryExpenses2 as $category) {
                    if ($category['category_id'] == $categoryId) {
                        $categoryTotal2 = (float)$category['total'];
                        break;
                    }
                }

                $categoryDifference = $categoryTotal2 - $categoryTotal1;
                $categoryPercentChange = $categoryTotal1 > 0 ? ($categoryDifference / $categoryTotal1) * 100 : 0;

                $categoryComparison[] = [
                    'category_id' => $categoryId,
                    'category_name' => $categoryName,
                    'period1_total' => $categoryTotal1,
                    'period2_total' => $categoryTotal2,
                    'difference' => $categoryDifference,
                    'percent_change' => $categoryPercentChange
                ];
            }

            // ترتيب التقرير حسب قيمة الفرق
            usort($categoryComparison, function($a, $b) {
                return abs($b['difference']) - abs($a['difference']);
            });

            return [
                'period1' => [
                    'start_date' => $period1['start_date'],
                    'end_date' => $period1['end_date'],
                    'total' => $total1
                ],
                'period2' => [
                    'start_date' => $period2['start_date'],
                    'end_date' => $period2['end_date'],
                    'total' => $total2
                ],
                'difference' => $difference,
                'percent_change' => $percentChange,
                'categories' => $categoryComparison
            ];
        } catch (Exception $e) {
            error_log('خطأ أثناء مقارنة فترات المصروفات: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * الحصول على مصروفات نهاية اليوم
     * @param int $endDayId معرف نهاية اليوم
     * @return array قائمة المصروفات
     */
    public function getEndDayExpenses($endDayId) {
        try {
            return $this->getExpenses(['end_day_id' => $endDayId]);
        } catch (Exception $e) {
            error_log('خطأ أثناء استرجاع مصروفات نهاية اليوم: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * حساب إجمالي مصروفات نهاية اليوم
     * @param int $endDayId معرف نهاية اليوم
     * @return float إجمالي المصروفات
     */
    public function getEndDayExpensesTotal($endDayId) {
        try {
            return $this->getTotalExpenses(['end_day_id' => $endDayId]);
        } catch (Exception $e) {
            error_log('خطأ أثناء حساب إجمالي مصروفات نهاية اليوم: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * الحصول على أكثر فئات المصروفات تكلفة
     * @param array $filters فلاتر البحث (اختياري)
     * @param int $limit عدد الفئات المطلوبة
     * @return array قائمة الفئات
     */
    public function getTopExpenseCategories($filters = [], $limit = 5) {
        try {
            $result = $this->getExpensesByCategory($filters);

            // ترتيب النتائج حسب المبلغ الإجمالي
            usort($result, function($a, $b) {
                return $b['total'] - $a['total'];
            });

            // تقليص النتائج للعدد المطلوب
            return array_slice($result, 0, $limit);
        } catch (Exception $e) {
            error_log('خطأ أثناء استرجاع أكثر فئات المصروفات تكلفة: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * إنشاء تقرير المصروفات
     * @param array $filters فلاتر البحث
     * @return array التقرير
     */
    public function generateExpensesReport($filters) {
        try {
            $report = [
                'filters' => $filters,
                'summary' => [],
                'categories' => [],
                'payment_methods' => [],
                'daily_expenses' => [],
                'expenses' => []
            ];

            // إجمالي المصروفات
            $report['summary']['total'] = $this->getTotalExpenses($filters);
            $report['summary']['count'] = $this->getExpensesCount($filters);

            // المصروفات حسب الفئة
            $report['categories'] = $this->getExpensesByCategory($filters);

            // المصروفات حسب طريقة الدفع
            $paymentMethods = ['cash', 'card', 'other'];
            foreach ($paymentMethods as $method) {
                $methodFilters = $filters;
                $methodFilters['payment_method'] = $method;
                $total = $this->getTotalExpenses($methodFilters);

                $report['payment_methods'][] = [
                    'method' => $method,
                    'total' => $total
                ];
            }

            // تقرير المصروفات اليومية (إذا كان نطاق التاريخ متاحًا)
            if (!empty($filters['start_date']) && !empty($filters['end_date'])) {
                $startDate = new DateTime($filters['start_date']);
                $endDate = new DateTime($filters['end_date']);
                $endDate->modify('+1 day'); // لتضمين تاريخ النهاية

                $dailyData = [];
                $currentDate = clone $startDate;

                while ($currentDate < $endDate) {
                    $dateStr = $currentDate->format('Y-m-d');
                    $dailyFilters = $filters;
                    $dailyFilters['date'] = $dateStr;

                    $dailyTotal = $this->getTotalExpenses($dailyFilters);

                    $dailyData[] = [
                        'date' => $dateStr,
                        'day' => $currentDate->format('d'),
                        'month' => $currentDate->format('m'),
                        'year' => $currentDate->format('Y'),
                        'total' => $dailyTotal
                    ];

                    $currentDate->modify('+1 day');
                }

                $report['daily_expenses'] = $dailyData;
            }

            // تفاصيل المصروفات
            $report['expenses'] = $this->getExpenses($filters);

            return $report;
        } catch (Exception $e) {
            error_log('خطأ أثناء إنشاء تقرير المصروفات: ' . $e->getMessage());
            throw $e;
        }
    }
}
