<?php
/**
 * واجهة برمجة التطبيقات الخاصة برسائل العملاء
 * تتيح تسجيل رسائل الواتساب والرسائل النصية المرسلة للعملاء
 */

// تعريف ثابت لمنع الوصول المباشر للملفات المرتبطة
define('BASEPATH', true);

// استدعاء ملفات التهيئة
require_once __DIR__ . '/../config/init.php';

// التحقق من تسجيل الدخول وإعادة توجيه المستخدم إذا لم يكن مسجلا
redirectIfNotLoggedIn();

// تحديد الإجراء المطلوب تنفيذه
$action = $_GET['action'] ?? '';

// معالجة الإجراءات المختلفة
switch ($action) {
    // تسجيل رسالة جديدة
    case 'log_message':
        // التحقق من الصلاحيات
        requirePermission('customers_view');
        
        // التأكد من وجود البيانات المطلوبة
        if (!isset($_POST['customer_id']) || !isset($_POST['message']) || !isset($_POST['channel'])) {
            echo json_encode([
                'status' => 'error',
                'message' => 'البيانات المطلوبة غير مكتملة'
            ]);
            exit;
        }
        
        // تجهيز البيانات
        $customerId = intval($_POST['customer_id']);
        $message = sanitizeInput($_POST['message']);
        $channel = sanitizeInput($_POST['channel']);
        $success = isset($_POST['success']) ? intval($_POST['success']) : 1;
        $error = isset($_POST['error']) ? sanitizeInput($_POST['error']) : null;
        
        try {
            // تسجيل الرسالة في قاعدة البيانات
            $db->prepare("INSERT INTO customer_messages (customer_id, message, channel, success, error_message, created_by, created_at)
                         VALUES (:customer_id, :message, :channel, :success, :error, :user_id, NOW())");
            $db->bind(':customer_id', $customerId);
            $db->bind(':message', $message);
            $db->bind(':channel', $channel);
            $db->bind(':success', $success);
            $db->bind(':error', $error);
            $db->bind(':user_id', $_SESSION['user_id']);
            
            if ($db->execute()) {
                echo json_encode([
                    'status' => 'success',
                    'message' => 'تم تسجيل الرسالة بنجاح',
                    'message_id' => $db->lastInsertId()
                ]);
            } else {
                echo json_encode([
                    'status' => 'error',
                    'message' => 'حدث خطأ أثناء تسجيل الرسالة'
                ]);
            }
        } catch (Exception $e) {
            // تسجيل الخطأ وإرجاع رسالة خطأ
            error_log("Error in customer_messages.php (log_message): " . $e->getMessage());
            echo json_encode([
                'status' => 'error',
                'message' => 'حدث خطأ في النظام: ' . $e->getMessage()
            ]);
        }
        break;
        
    // الحصول على رسائل العميل
    case 'get_messages':
        // التحقق من الصلاحيات
        requirePermission('customers_view');
        
        // التأكد من وجود معرف العميل
        if (!isset($_GET['customer_id'])) {
            echo json_encode([
                'status' => 'error',
                'message' => 'لم يتم تحديد العميل'
            ]);
            exit;
        }
        
        $customerId = intval($_GET['customer_id']);
        $limit = isset($_GET['limit']) ? intval($_GET['limit']) : 20;
        
        try {
            // استرجاع رسائل العميل
            $db->prepare("SELECT cm.*, u.name as user_name
                         FROM customer_messages cm
                         LEFT JOIN users u ON cm.created_by = u.id
                         WHERE cm.customer_id = :customer_id
                         ORDER BY cm.created_at DESC
                         LIMIT :limit");
            $db->bind(':customer_id', $customerId);
            $db->bind(':limit', $limit);
            $db->execute();
            
            $messages = $db->fetchAll();
            
            echo json_encode([
                'status' => 'success',
                'messages' => $messages
            ]);
        } catch (Exception $e) {
            // تسجيل الخطأ وإرجاع رسالة خطأ
            error_log("Error in customer_messages.php (get_messages): " . $e->getMessage());
            echo json_encode([
                'status' => 'error',
                'message' => 'حدث خطأ في النظام: ' . $e->getMessage()
            ]);
        }
        break;
        
    // إذا كان الإجراء غير معروف
    default:
        echo json_encode([
            'status' => 'error',
            'message' => 'الإجراء غير معروف'
        ]);
        break;
} 