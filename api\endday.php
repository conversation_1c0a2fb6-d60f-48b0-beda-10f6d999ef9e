<?php
/**
 * نقاط النهاية الخاصة بنهاية اليوم
 */

// تأكيد تحميل الملفات الأساسية
require_once __DIR__ . '/../config/init.php';

// التحقق من نوع الطلب (POST أو GET)
$action = $_GET['action'] ?? '';

// معالجة الطلبات
try {
    // إعداد رأس الاستجابة
    header('Content-Type: application/json');

    // إنشاء كائنات النماذج
    $db = new Database();
    $invoiceModel = new Invoice($db);
    $expenseModel = new Expense($db);
    $endDayModel = new EndDay($db);
    $adminNotification = new AdminNotification($db);

    // التحقق من صلاحيات المستخدم
    if (!isLoggedIn()) {
        throw new Exception('يجب تسجيل الدخول أولاً', 401);
    }

    // التأكد من وجود صلاحية إدارة نهاية اليوم
    requirePermission('endday_manage');

    switch ($action) {
        // بدء نهاية اليوم
        case 'start':
            // التأكد من أن الطلب عبر POST
            if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
                throw new Exception('الطريقة غير مسموح بها', 405);
            }

            // التحقق من وجود معرف الفرع
            $branchId = input('branch_id') ?? $_SESSION['user_branch_id'];
            if (!$branchId) {
                throw new Exception('معرف الفرع مطلوب', 400);
            }

            // التحقق من وجود تاريخ مخصص
            $date = input('date');
            if (!$date) {
                $date = date('Y-m-d'); // استخدام التاريخ الحالي إذا لم يتم تحديد تاريخ
            } else {
                // التحقق من صحة التاريخ
                if (!validateDate($date)) {
                    throw new Exception('تنسيق التاريخ غير صحيح. يجب أن يكون بتنسيق Y-m-d', 400);
                }
            }

            // بدء يوم العمل باستخدام النموذج
            $endDayId = $endDayModel->startEndDay($branchId, $_SESSION['user_id'], $date);

            // الحصول على بيانات نهاية اليوم الحالية
            $currentEndDay = $endDayModel->getCurrentEndDay($branchId, $date);

            // ربط المصروفات بنهاية اليوم
            $expenseModel->linkExpensesToEndDay(
                $endDayId,
                $date,
                $branchId
            );

            // ملاحظة: تم نقل إرسال إشعارات WhatsApp إلى جانب العميل باستخدام JavaScript
            // لم يعد هناك حاجة لاستدعاء $adminNotification->sendWorkdayOpenNotification هنا

            echo json_encode([
                'status' => 'success',
                'message' => 'تم بدء نهاية اليوم بنجاح',
                'end_day_id' => $endDayId,
                'total_sales' => $currentEndDay['current_sales'] ?? 0,
                'total_expenses' => $currentEndDay['current_expenses'] ?? 0
            ]);
            break;

        // إغلاق نهاية اليوم
        case 'close':
            // التأكد من أن الطلب عبر POST
            if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
                throw new Exception('الطريقة غير مسموح بها', 405);
            }

            // التحقق من وجود معرف الفرع
            $branchId = input('branch_id') ?? $_SESSION['user_branch_id'];
            $notes = input('notes');

            if (!$branchId) {
                throw new Exception('معرف الفرع مطلوب', 400);
            }

            // البحث عن نهاية اليوم المفتوحة للفرع (بغض النظر عن التاريخ)
            $db->prepare("SELECT id FROM end_days
                          WHERE branch_id = :branch_id
                          AND closed_at IS NULL");
            $db->bind(':branch_id', $branchId);
            $endDay = $db->fetch();

            if (!$endDay) {
                throw new Exception('لا يوجد يوم عمل مفتوح لهذا الفرع', 400);
            }

            // إغلاق يوم العمل باستخدام النموذج
            $endDayModel->closeEndDay($endDay['id'], $notes, $_SESSION['user_id']);

            // الحصول على بيانات يوم العمل بعد الإغلاق
            $closedEndDay = $endDayModel->getEndDayById($endDay['id']);

            // ملاحظة: تم نقل إرسال إشعارات WhatsApp إلى جانب العميل باستخدام JavaScript
            // لم يعد هناك حاجة لاستدعاء $adminNotification->sendWorkdayCloseNotification هنا

            echo json_encode([
                'status' => 'success',
                'message' => 'تم إغلاق نهاية اليوم بنجاح'
            ]);
            break;

        // عرض تفاصيل نهاية اليوم
        case 'view':
            // التحقق من وجود معرف نهاية اليوم
            $endDayId = input('id');
            if (!$endDayId) {
                throw new Exception('معرف نهاية اليوم مطلوب', 400);
            }

            // استرجاع بيانات نهاية اليوم
            $db->prepare("SELECT ed.*,
                                 b.name as branch_name,
                                 u.name as closed_by_name
                          FROM end_days ed
                          LEFT JOIN branches b ON ed.branch_id = b.id
                          LEFT JOIN users u ON ed.closed_by = u.id
                          WHERE ed.id = :id");
            $db->bind(':id', $endDayId);
            $endDay = $db->fetch();

            if (!$endDay) {
                throw new Exception('لم يتم العثور على نهاية اليوم', 404);
            }

            // استرجاع المصروفات
            $endDay['expenses'] = $expenseModel->getEndDayExpenses($endDayId);
            $endDay['expenses_total'] = $expenseModel->getEndDayExpensesTotal($endDayId);

            // استرجاع الفواتير
            $db->prepare("SELECT * FROM invoices WHERE end_day_id = :end_day_id");
            $db->bind(':end_day_id', $endDayId);
            $endDay['invoices'] = $db->fetchAll();

            echo json_encode([
                'status' => 'success',
                'end_day' => $endDay
            ]);
            break;

        // قائمة نهايات الأيام
        case 'list':
            // معلمات الفلترة
            $filters = [
                'branch_id' => input('branch_id') ?? $_SESSION['user_branch_id'],
                'start_date' => input('start_date'),
                'end_date' => input('end_date'),
                'limit' => input('limit') ?? 50,
                'offset' => input('offset') ?? 0
            ];

            // استرجاع قائمة نهايات الأيام
            $sql = "SELECT ed.*,
                           b.name as branch_name,
                           u.name as closed_by_name
                    FROM end_days ed
                    LEFT JOIN branches b ON ed.branch_id = b.id
                    LEFT JOIN users u ON ed.closed_by = u.id";

            $whereConditions = [];
            $bindings = [];

            // تطبيق الفلاتر
            if (!empty($filters['branch_id'])) {
                $whereConditions[] = "ed.branch_id = :branch_id";
                $bindings[':branch_id'] = $filters['branch_id'];
            }

            if (!empty($filters['start_date']) && !empty($filters['end_date'])) {
                $whereConditions[] = "ed.date BETWEEN :start_date AND :end_date";
                $bindings[':start_date'] = $filters['start_date'];
                $bindings[':end_date'] = $filters['end_date'];
            }

            // إضافة شروط WHERE
            if (!empty($whereConditions)) {
                $sql .= " WHERE " . implode(' AND ', $whereConditions);
            }

            // الترتيب
            $sql .= " ORDER BY ed.date DESC";

            // الحد والإزاحة
            $sql .= " LIMIT :limit OFFSET :offset";
            $bindings[':limit'] = $filters['limit'];
            $bindings[':offset'] = $filters['offset'];

            $db->prepare($sql);

            // ربط القيم
            foreach ($bindings as $param => $value) {
                $db->bind($param, $value);
            }

            $endDays = $db->fetchAll();

            // حساب العدد الإجمالي
            // إنشاء استعلام العد بدون LIMIT و OFFSET
            $countSql = "SELECT COUNT(*) FROM end_days ed";

            // إضافة شروط WHERE إذا كانت موجودة
            if (!empty($whereConditions)) {
                $countSql .= " WHERE " . implode(' AND ', $whereConditions);
            }

            $db->prepare($countSql);

            // ربط القيم للعد (فقط الفلاتر، بدون limit و offset)
            $countBindings = array_filter($bindings, function($key) {
                return $key !== ':limit' && $key !== ':offset';
            }, ARRAY_FILTER_USE_KEY);

            foreach ($countBindings as $param => $value) {
                $db->bind($param, $value);
            }

            $totalCount = $db->fetchColumn();

            echo json_encode([
                'status' => 'success',
                'end_days' => $endDays,
                'total_count' => $totalCount
            ]);
            break;

        // إعادة فتح يوم عمل مغلق
        case 'reopen':
            // التأكد من أن الطلب عبر POST
            if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
                throw new Exception('الطريقة غير مسموح بها', 405);
            }

            // التحقق من وجود معرف نهاية اليوم
            $endDayId = input('end_day_id');
            if (!$endDayId) {
                throw new Exception('معرف نهاية اليوم مطلوب', 400);
            }

            // إعادة فتح يوم العمل
            $success = $endDayModel->reopenEndDay($endDayId, $_SESSION['user_id']);

            // الحصول على بيانات نهاية اليوم الحالية
            $endDay = $endDayModel->getEndDayById($endDayId);

            echo json_encode([
                'status' => 'success',
                'message' => 'تم إعادة فتح يوم العمل بنجاح',
                'end_day' => $endDay
            ]);
            break;

        // حذف يوم عمل
        case 'delete':
            // التأكد من أن الطلب عبر POST
            if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
                throw new Exception('الطريقة غير مسموح بها', 405);
            }

            // التحقق من أن المستخدم مدير
            if (!isAdmin()) {
                throw new Exception('ليس لديك صلاحية لحذف يوم عمل', 403);
            }

            // التحقق من وجود معرف نهاية اليوم
            $endDayId = input('end_day_id');
            if (!$endDayId) {
                throw new Exception('معرف نهاية اليوم مطلوب', 400);
            }

            // حذف يوم العمل
            $success = $endDayModel->deleteEndDay($endDayId, $_SESSION['user_id']);

            echo json_encode([
                'status' => 'success',
                'message' => 'تم حذف يوم العمل وجميع البيانات المرتبطة به بنجاح'
            ]);
            break;

        // الافتراضي: إجراء غير معروف
        default:
            throw new Exception('إجراء غير معروف', 404);
    }
} catch (Exception $e) {
    // معالجة الأخطاء
    $errorCode = $e->getCode();

    // التحقق من أن رمز الخطأ رقم صحيح وضمن نطاق رموز HTTP
    if (!is_int($errorCode) || $errorCode < 100 || $errorCode > 599) {
        // إذا لم يكن رمز الخطأ صالحاً، استخدم 500 (خطأ داخلي في الخادم)
        $errorCode = 500;
    }

    http_response_code($errorCode);

    echo json_encode([
        'status' => 'error',
        'message' => $e->getMessage(),
        'code' => $errorCode,
        'original_code' => $e->getCode() // للتشخيص
    ]);
} catch (PDOException $e) {
    // التحقق من أن الخطأ هو انتهاك قيد التفرد
    if ($e->getCode() == 23000 && strpos($e->getMessage(), 'Duplicate entry') !== false && strpos($e->getMessage(), 'branch_date') !== false) {
        http_response_code(400);
        echo json_encode([
            'status' => 'error',
            'message' => 'لا يمكن فتح يوم عمل جديد في نفس التاريخ. يرجى اختيار تاريخ آخر لبدء يوم عمل جديد.'
        ]);
    } else {
        // إرجاع رسالة الخطأ العامة
        http_response_code(500);
        echo json_encode([
            'status' => 'error',
            'message' => 'حدث خطأ في قاعدة البيانات: ' . $e->getMessage()
        ]);
    }
}