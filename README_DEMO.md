# نظام إدارة الصالونات - النسخة التجريبية

## 🎯 نظرة عامة

هذه هي النسخة التجريبية لنظام إدارة الصالونات، وهو نظام شامل لإدارة صالونات التجميل والحلاقة. تم تصميم هذه النسخة لعرض جميع ميزات النظام مع بيانات تجريبية وهمية.

## ⚠️ تحذير مهم

**هذه نسخة تجريبية فقط!**
- جميع البيانات المعروضة وهمية لأغراض العرض والتجربة
- بعض الوظائف مقيدة لأغراض الأمان
- يتم إعادة تعيين البيانات دورياً
- لا تستخدم هذه النسخة في بيئة الإنتاج

## 🚀 الميزات الرئيسية

### 📅 إدارة المواعيد
- حجز وإدارة المواعيد
- تنبيهات تلقائية للمواعيد
- جدولة الموظفين
- إدارة حالات المواعيد (محجوز، منتظر، مكتمل، ملغي)

### 👥 إدارة العملاء
- قاعدة بيانات شاملة للعملاء
- نظام نقاط الولاء
- تتبع تاريخ الزيارات
- ملاحظات خاصة لكل عميل

### 💰 إدارة المبيعات والفواتير
- إنشاء فواتير احترافية
- إدارة طرق الدفع المختلفة
- تطبيق الخصومات والعروض
- تتبع المدفوعات المؤجلة

### 📦 إدارة المخزون
- تتبع المنتجات والكميات
- تنبيهات نفاد المخزون
- إدارة حركات المخزون
- تصنيف المنتجات

### 👨‍💼 إدارة الموظفين
- ملفات شخصية للموظفين
- نظام الحضور والانصراف
- حساب الرواتب والعمولات
- إدارة الصلاحيات

### 📊 التقارير والإحصائيات
- تقارير المبيعات اليومية والشهرية
- إحصائيات الأداء
- تقارير المخزون
- تحليل ربحية الخدمات

### 🎁 العروض والخصومات
- إنشاء عروض ترويجية
- أكواد خصم
- خصومات تلقائية
- تتبع استخدام العروض

## 🔐 بيانات الدخول التجريبية

### المدير العام
- **اسم المستخدم:** `admin`
- **كلمة المرور:** `password`
- **الصلاحيات:** جميع الصلاحيات

### مدير الفرع
- **اسم المستخدم:** `manager`
- **كلمة المرور:** `password`
- **الصلاحيات:** إدارة العمليات اليومية

### أمين الصندوق
- **اسم المستخدم:** `cashier`
- **كلمة المرور:** `password`
- **الصلاحيات:** إنشاء الفواتير وإدارة المدفوعات

### مستخدم تجريبي
- **اسم المستخدم:** `demo`
- **كلمة المرور:** `password`
- **الصلاحيات:** عرض البيانات فقط

## 🛠️ التثبيت والإعداد

### متطلبات النظام
- PHP 7.4 أو أحدث
- MySQL 5.7 أو أحدث
- خادم ويب (Apache/Nginx)
- مساحة تخزين 100 ميجابايت على الأقل

### خطوات التثبيت

1. **تحميل الملفات**
   ```bash
   git clone [repository-url]
   cd salon-demo
   ```

2. **إعداد قاعدة البيانات**
   - إنشاء قاعدة بيانات جديدة باسم `salon_demo`
   - استيراد ملف `demo_database.sql`

3. **تشغيل سكريبت الإعداد**
   ```bash
   php setup_demo.php
   ```
   أو عبر المتصفح:
   ```
   http://your-domain/setup_demo.php?admin_key=demo_setup_2025
   ```

4. **التحقق من الإعداد**
   - زيارة الصفحة الرئيسية
   - تسجيل الدخول بأحد الحسابات التجريبية

## 📁 هيكل المشروع

```
salon-demo/
├── assets/
│   ├── css/
│   │   └── demo-mode.css
│   └── js/
│       └── demo-mode.js
├── config/
│   ├── config.php
│   └── config_demo.php
├── includes/
│   └── demo_check.php
├── pages/
│   ├── auth/
│   ├── dashboard/
│   ├── customers/
│   ├── appointments/
│   ├── services/
│   ├── products/
│   ├── employees/
│   ├── reports/
│   └── settings/
├── scripts/
├── logs/
├── backups/
├── demo_database.sql
├── setup_demo.php
├── index.php
└── README_DEMO.md
```

## 🔒 العمليات المقيدة في النسخة التجريبية

لأغراض الأمان، تم تقييد العمليات التالية:

- ❌ حذف البيانات الأساسية
- ❌ إنشاء واستعادة النسخ الاحتياطية
- ❌ تعديل إعدادات النظام الحساسة
- ❌ إعادة تعيين النظام
- ❌ تعديل بيانات المستخدمين الأساسيين

## 🔄 إعادة تعيين البيانات

يتم إعادة تعيين البيانات التجريبية تلقائياً كل 24 ساعة للحفاظ على تجربة متسقة للمستخدمين الجدد.

## 🎨 التخصيص البصري

تتضمن النسخة التجريبية عناصر بصرية مميزة:

- 🏷️ شارات "نسخة تجريبية"
- ⚠️ تحذيرات واضحة
- 🌊 علامة مائية في الخلفية
- 🎯 مؤشرات بصرية للبيانات الوهمية
- 🎨 ألوان مميزة للواجهة

## 📞 الدعم والاستفسارات

للحصول على النسخة الكاملة أو للاستفسارات:

- 📧 **البريد الإلكتروني:** <EMAIL>
- 🌐 **الموقع الإلكتروني:** [موقع الشركة]
- 📱 **الهاتف:** [رقم الهاتف]

## 📝 الترخيص

هذه النسخة التجريبية مخصصة للعرض والتجربة فقط. جميع الحقوق محفوظة.

## 🔄 التحديثات

- **الإصدار:** 1.0.0-DEMO
- **تاريخ الإنشاء:** 21 يونيو 2025
- **آخر تحديث:** 21 يونيو 2025

## 🚨 ملاحظات مهمة

1. **لا تستخدم في الإنتاج:** هذه نسخة تجريبية فقط
2. **البيانات وهمية:** جميع البيانات المعروضة غير حقيقية
3. **الأمان:** كلمات المرور بسيطة لأغراض التجربة فقط
4. **الأداء:** قد يختلف الأداء عن النسخة الفعلية
5. **الدعم:** الدعم الفني محدود للنسخة التجريبية

---

**شكراً لتجربة نظام إدارة الصالونات! 🎉**

للحصول على النسخة الكاملة مع جميع الميزات والدعم الفني الكامل، يرجى التواصل معنا.
