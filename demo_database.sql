-- php<PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- Demo Database for Salon Management System
-- Generated for Demo Version - Updated with comprehensive demo data
-- تاريخ الإنشاء: 21 يونيو 2025
-- هذه قاعدة بيانات تجريبية تحتوي على بيانات وهمية لأغراض العرض والتجربة فقط

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
SET AUTOCOMMIT = 0;
START TRANSACTION;
SET time_zone = "+00:00";

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `salon_demo`
-- قاعدة بيانات تجريبية لنظام إدارة الصالونات
--

-- --------------------------------------------------------

--
-- Table structure for table `appointments`
--

CREATE TABLE `appointments` (
  `id` int(11) NOT NULL,
  `customer_id` int(11) DEFAULT NULL,
  `employee_id` int(11) DEFAULT NULL,
  `service_id` int(11) DEFAULT NULL,
  `date` date NOT NULL,
  `start_time` time NOT NULL,
  `end_time` time NOT NULL,
  `status` enum('booked','waiting','completed','cancelled') NOT NULL DEFAULT 'booked',
  `notes` text DEFAULT NULL,
  `branch_id` int(11) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NULL DEFAULT NULL ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping demo data for table `appointments`
--

INSERT INTO `appointments` (`id`, `customer_id`, `employee_id`, `service_id`, `date`, `start_time`, `end_time`, `status`, `notes`, `branch_id`, `created_at`, `updated_at`) VALUES
(1, 1, 1, 1, '2025-06-22', '10:00:00', '10:30:00', 'booked', 'موعد قص شعر', 1, '2025-06-21 10:00:00', NULL),
(2, 2, 2, 2, '2025-06-22', '11:00:00', '12:00:00', 'booked', 'صبغة شعر كاملة', 1, '2025-06-21 11:00:00', NULL),
(3, 3, 1, 3, '2025-06-22', '14:00:00', '14:45:00', 'booked', 'تسريحة مناسبة', 1, '2025-06-21 12:00:00', NULL),
(4, 4, 3, 4, '2025-06-22', '15:30:00', '16:00:00', 'booked', 'عناية بالبشرة', 1, '2025-06-21 13:00:00', NULL),
(5, 5, 2, 1, '2025-06-23', '09:00:00', '09:30:00', 'booked', 'قص شعر سريع', 1, '2025-06-21 14:00:00', NULL),
(6, 6, 1, 5, '2025-06-23', '10:00:00', '10:30:00', 'booked', 'مانيكير', 1, '2025-06-21 15:00:00', NULL),
(7, 7, 3, 6, '2025-06-23', '11:00:00', '11:45:00', 'booked', 'بديكير', 1, '2025-06-21 16:00:00', NULL),
(8, 8, 2, 7, '2025-06-23', '14:00:00', '17:00:00', 'booked', 'فرد شعر برازيلي', 1, '2025-06-21 17:00:00', NULL),
(9, 9, 1, 8, '2025-06-23', '15:30:00', '16:30:00', 'booked', 'حمام كريم', 1, '2025-06-21 18:00:00', NULL),
(10, 10, 2, 3, '2025-06-24', '09:00:00', '09:45:00', 'booked', 'تسريحة للعمل', 1, '2025-06-21 19:00:00', NULL),
(11, 11, 3, 4, '2025-06-24', '10:00:00', '11:00:00', 'booked', 'تنظيف بشرة عميق', 1, '2025-06-21 20:00:00', NULL),
(12, 12, 1, 2, '2025-06-24', '11:30:00', '13:30:00', 'booked', 'صبغة وقص', 1, '2025-06-21 21:00:00', NULL),
(13, 13, 2, 1, '2025-06-24', '14:00:00', '14:30:00', 'completed', 'قص شعر منتهي', 1, '2025-06-20 10:00:00', '2025-06-20 14:30:00'),
(14, 14, 3, 5, '2025-06-24', '15:00:00', '15:30:00', 'completed', 'مانيكير منتهي', 1, '2025-06-20 11:00:00', '2025-06-20 15:30:00'),
(15, 15, 1, 6, '2025-06-24', '16:00:00', '16:45:00', 'completed', 'بديكير منتهي', 1, '2025-06-20 12:00:00', '2025-06-20 16:45:00'),
(16, 16, 2, 8, '2025-06-25', '09:00:00', '10:00:00', 'waiting', 'في الانتظار', 1, '2025-06-21 22:00:00', NULL),
(17, 17, 3, 4, '2025-06-25', '10:30:00', '11:30:00', 'cancelled', 'ألغي الموعد', 1, '2025-06-21 23:00:00', '2025-06-22 08:00:00'),
(18, 18, 1, 1, '2025-06-25', '11:00:00', '11:30:00', 'booked', 'قص شعر عادي', 1, '2025-06-22 00:00:00', NULL),
(19, 19, 2, 3, '2025-06-25', '14:00:00', '14:45:00', 'booked', 'تسريحة خاصة', 1, '2025-06-22 01:00:00', NULL),
(20, 20, 3, 2, '2025-06-25', '15:30:00', '17:30:00', 'booked', 'صبغة جديدة', 1, '2025-06-22 02:00:00', NULL);

-- --------------------------------------------------------

--
-- Table structure for table `branches`
--

CREATE TABLE `branches` (
  `id` int(11) NOT NULL,
  `name` varchar(100) NOT NULL,
  `address` varchar(255) DEFAULT NULL,
  `phone` varchar(200) DEFAULT NULL,
  `manager_id` int(11) DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NULL DEFAULT NULL ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping demo data for table `branches`
--

INSERT INTO `branches` (`id`, `name`, `address`, `phone`, `manager_id`, `is_active`, `created_at`, `updated_at`) VALUES
(1, 'صالون الجمال الراقي - الفرع الرئيسي', 'شارع الملك فهد، الرياض', '0112345678', 1, 1, '2025-06-21 08:00:00', NULL);

-- --------------------------------------------------------

--
-- Table structure for table `customers`
--

CREATE TABLE `customers` (
  `id` int(11) NOT NULL,
  `name` varchar(100) NOT NULL,
  `phone` varchar(20) NOT NULL,
  `email` varchar(100) DEFAULT NULL,
  `address` varchar(255) DEFAULT NULL,
  `birthday` date DEFAULT NULL,
  `notes` text DEFAULT NULL,
  `loyalty_points` int(11) DEFAULT 0,
  `branch_id` int(11) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NULL DEFAULT NULL ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping demo data for table `customers`
--

INSERT INTO `customers` (`id`, `name`, `phone`, `email`, `address`, `birthday`, `notes`, `loyalty_points`, `branch_id`, `created_at`, `updated_at`) VALUES
(1, 'فاطمة أحمد', '0501234567', '<EMAIL>', 'حي النخيل، الرياض', '1990-05-15', 'عميلة مميزة، تفضل الألوان الطبيعية', 150, 1, '2025-06-01 10:00:00', NULL),
(2, 'نورا محمد', '0509876543', '<EMAIL>', 'حي الملز، الرياض', '1985-08-22', 'تحب التجديد في القصات', 200, 1, '2025-06-02 11:00:00', NULL),
(3, 'سارة علي', '0512345678', '<EMAIL>', 'حي العليا، الرياض', '1992-12-10', 'بشرة حساسة، تحتاج منتجات خاصة', 75, 1, '2025-06-03 12:00:00', NULL),
(4, 'مريم خالد', '0556789012', '<EMAIL>', 'حي الورود، الرياض', '1988-03-18', 'تفضل المواعيد المسائية', 120, 1, '2025-06-04 13:00:00', NULL),
(5, 'هند سعد', '0567890123', '<EMAIL>', 'حي الياسمين، الرياض', '1995-07-25', 'عميلة جديدة، أول زيارة', 25, 1, '2025-06-05 14:00:00', NULL),
(6, 'ريم عبدالله', '0578901234', '<EMAIL>', 'حي الربيع، الرياض', '1987-11-30', 'تحب الألوان الجريئة', 180, 1, '2025-06-06 15:00:00', NULL),
(7, 'أمل حسن', '0589012345', '<EMAIL>', 'حي الشفا، الرياض', '1993-09-12', 'شعر مجعد، تحتاج عناية خاصة', 90, 1, '2025-06-07 16:00:00', NULL),
(8, 'لينا فهد', '0590123456', '<EMAIL>', 'حي الحمراء، الرياض', '1991-04-08', 'تفضل القصات القصيرة', 110, 1, '2025-06-08 17:00:00', NULL),
(9, 'دانا عمر', '0501112233', '<EMAIL>', 'حي السليمانية، الرياض', '1989-06-14', 'عميلة منتظمة، موعد شهري', 250, 1, '2025-06-09 18:00:00', NULL),
(10, 'غادة طارق', '0502223344', '<EMAIL>', 'حي الفيصلية، الرياض', '1994-01-20', 'تحب التسريحات الكلاسيكية', 60, 1, '2025-06-10 19:00:00', NULL),
(11, 'زينب محمود', '0503334455', '<EMAIL>', 'حي الملقا، الرياض', '1986-02-28', 'تحب العناية بالأظافر', 320, 1, '2025-06-11 10:00:00', NULL),
(12, 'خديجة سالم', '0504445566', '<EMAIL>', 'حي الصحافة، الرياض', '1992-07-14', 'عميلة VIP، تحجز مواعيد متعددة', 450, 1, '2025-06-12 11:00:00', NULL),
(13, 'عائشة عبدالرحمن', '0505556677', '<EMAIL>', 'حي النرجس، الرياض', '1989-11-03', 'تفضل الخدمات الطبيعية', 180, 1, '2025-06-13 12:00:00', NULL),
(14, 'حفصة أحمد', '0506667788', '<EMAIL>', 'حي الروضة، الرياض', '1994-04-17', 'شعر طويل، تحتاج وقت إضافي', 95, 1, '2025-06-14 13:00:00', NULL),
(15, 'رقية علي', '0507778899', '<EMAIL>', 'حي العقيق، الرياض', '1987-09-22', 'تحب التجديد المستمر', 275, 1, '2025-06-15 14:00:00', NULL),
(16, 'أم كلثوم محمد', '0508889900', '<EMAIL>', 'حي الياسمين، الرياض', '1983-12-05', 'عميلة منذ سنوات، تثق في الفريق', 520, 1, '2025-06-16 15:00:00', NULL),
(17, 'سكينة حسن', '0509990011', '<EMAIL>', 'حي الورود، الرياض', '1991-06-30', 'تحب الألوان الهادئة', 140, 1, '2025-06-17 16:00:00', NULL),
(18, 'جميلة عبدالله', '0500001122', '<EMAIL>', 'حي الفيصلية، الرياض', '1988-01-12', 'بشرة دهنية، تحتاج عناية خاصة', 210, 1, '2025-06-18 17:00:00', NULL),
(19, 'وردة سعد', '0501112234', '<EMAIL>', 'حي الحمراء، الرياض', '1993-08-25', 'تحب التسريحات العصرية', 165, 1, '2025-06-19 18:00:00', NULL),
(20, 'ياسمين طارق', '0502223345', '<EMAIL>', 'حي النخيل، الرياض', '1990-03-18', 'عميلة جديدة، تبحث عن تغيير جذري', 30, 1, '2025-06-20 19:00:00', NULL);

-- --------------------------------------------------------

--
-- Table structure for table `employees`
--

CREATE TABLE `employees` (
  `id` int(11) NOT NULL,
  `name` varchar(100) NOT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `email` varchar(100) DEFAULT NULL,
  `position` varchar(50) DEFAULT NULL,
  `hire_date` date DEFAULT NULL,
  `salary` decimal(10,2) DEFAULT NULL,
  `commission_rate` decimal(5,2) DEFAULT 0.00,
  `is_active` tinyint(1) DEFAULT 1,
  `branch_id` int(11) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NULL DEFAULT NULL ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping demo data for table `employees`
--

INSERT INTO `employees` (`id`, `name`, `phone`, `email`, `position`, `hire_date`, `salary`, `commission_rate`, `is_active`, `branch_id`, `created_at`, `updated_at`) VALUES
(1, 'أميرة سالم', '0511111111', '<EMAIL>', 'مصففة شعر رئيسية', '2023-01-15', 4500.00, 15.00, 1, 1, '2025-01-15 08:00:00', NULL),
(2, 'ياسمين أحمد', '0522222222', '<EMAIL>', 'خبيرة صبغات', '2023-03-20', 4000.00, 12.00, 1, 1, '2025-03-20 08:00:00', NULL),
(3, 'رنا محمد', '0533333333', '<EMAIL>', 'أخصائية عناية بالبشرة', '2023-05-10', 3800.00, 10.00, 1, 1, '2025-05-10 08:00:00', NULL),
(4, 'سلمى علي', '0544444444', '<EMAIL>', 'مساعدة', '2023-08-01', 2500.00, 5.00, 1, 1, '2025-08-01 08:00:00', NULL);

-- --------------------------------------------------------

--
-- Table structure for table `services`
--

CREATE TABLE `services` (
  `id` int(11) NOT NULL,
  `name` varchar(100) NOT NULL,
  `description` text DEFAULT NULL,
  `price` decimal(10,2) NOT NULL,
  `duration` int(11) DEFAULT NULL COMMENT 'Duration in minutes',
  `category` varchar(50) DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NULL DEFAULT NULL ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping demo data for table `services`
--

INSERT INTO `services` (`id`, `name`, `description`, `price`, `duration`, `category`, `is_active`, `created_at`, `updated_at`) VALUES
(1, 'قص شعر عادي', 'قص شعر بسيط وتسوية الأطراف', 80.00, 30, 'قص شعر', 1, '2025-01-01 08:00:00', NULL),
(2, 'صبغة شعر كاملة', 'صبغة شعر بلون واحد مع العناية', 250.00, 120, 'صبغات', 1, '2025-01-01 08:00:00', NULL),
(3, 'تسريحة مناسبة', 'تسريحة شعر للمناسبات الخاصة', 150.00, 45, 'تسريحات', 1, '2025-01-01 08:00:00', NULL),
(4, 'تنظيف بشرة', 'تنظيف عميق للبشرة مع القناع', 180.00, 60, 'عناية بالبشرة', 1, '2025-01-01 08:00:00', NULL),
(5, 'مانيكير', 'عناية كاملة بالأظافر مع الطلاء', 60.00, 30, 'عناية بالأظافر', 1, '2025-01-01 08:00:00', NULL),
(6, 'بديكير', 'عناية كاملة بأظافر القدمين', 80.00, 45, 'عناية بالأظافر', 1, '2025-01-01 08:00:00', NULL),
(7, 'فرد شعر برازيلي', 'فرد الشعر بالكيراتين البرازيلي', 450.00, 180, 'علاجات', 1, '2025-01-01 08:00:00', NULL),
(8, 'حمام كريم', 'علاج مرطب ومغذي للشعر', 120.00, 60, 'علاجات', 1, '2025-01-01 08:00:00', NULL),
(9, 'قص شعر أطفال', 'قص شعر مخصص للأطفال', 50.00, 20, 'قص شعر', 1, '2025-01-01 08:00:00', NULL),
(10, 'تسريحة عروس', 'تسريحة شعر فاخرة للعرائس', 300.00, 90, 'تسريحات', 1, '2025-01-01 08:00:00', NULL),
(11, 'صبغة خصل', 'صبغة خصل ملونة في الشعر', 180.00, 90, 'صبغات', 1, '2025-01-01 08:00:00', NULL),
(12, 'ماسك للوجه', 'ماسك مرطب ومنظف للبشرة', 100.00, 30, 'عناية بالبشرة', 1, '2025-01-01 08:00:00', NULL),
(13, 'تشقير الحواجب', 'تشقير وتهذيب الحواجب', 40.00, 20, 'عناية بالبشرة', 1, '2025-01-01 08:00:00', NULL),
(14, 'مكياج سهرة', 'مكياج كامل للمناسبات المسائية', 200.00, 60, 'مكياج', 1, '2025-01-01 08:00:00', NULL),
(15, 'مكياج عروس', 'مكياج فاخر للعرائس', 400.00, 120, 'مكياج', 1, '2025-01-01 08:00:00', NULL);

-- --------------------------------------------------------

--
-- Table structure for table `products`
--

CREATE TABLE `products` (
  `id` int(11) NOT NULL,
  `name` varchar(100) NOT NULL,
  `description` text DEFAULT NULL,
  `price` decimal(10,2) NOT NULL,
  `cost` decimal(10,2) DEFAULT NULL,
  `category_id` int(11) DEFAULT NULL,
  `is_for_sale` tinyint(1) DEFAULT 1 COMMENT '1=للبيع, 0=للاستخدام الداخلي',
  `min_quantity` int(11) DEFAULT 5,
  `is_active` tinyint(1) DEFAULT 1,
  `branch_id` int(11) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NULL DEFAULT NULL ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping demo data for table `products`
--

INSERT INTO `products` (`id`, `name`, `description`, `price`, `cost`, `category_id`, `is_for_sale`, `min_quantity`, `is_active`, `branch_id`, `created_at`, `updated_at`) VALUES
(1, 'شامبو للشعر الجاف', 'شامبو مرطب للشعر الجاف والمتضرر', 45.00, 25.00, 1, 1, 5, 1, 1, '2025-01-01 08:00:00', NULL),
(2, 'بلسم مرطب', 'بلسم مرطب ومغذي لجميع أنواع الشعر', 35.00, 20.00, 1, 1, 5, 1, 1, '2025-01-01 08:00:00', NULL),
(3, 'كريم فرد الشعر', 'كريم فرد طبيعي بدون مواد كيميائية ضارة', 150.00, 80.00, 1, 1, 3, 1, 1, '2025-01-01 08:00:00', NULL),
(4, 'صبغة شعر طبيعية', 'صبغة شعر بمكونات طبيعية - لون بني', 55.00, 30.00, 1, 1, 5, 1, 1, '2025-01-01 08:00:00', NULL),
(5, 'زيت أرغان للشعر', 'زيت أرغان طبيعي لتغذية وترطيب الشعر', 75.00, 40.00, 1, 1, 3, 1, 1, '2025-01-01 08:00:00', NULL),
(6, 'كريم تصفيف الشعر', 'كريم تصفيف قوي الثبات', 28.00, 15.00, 1, 1, 5, 1, 1, '2025-01-01 08:00:00', NULL),
(7, 'قناع للوجه', 'قناع مرطب ومنظف للبشرة', 25.00, 12.00, 2, 1, 5, 1, 1, '2025-01-01 08:00:00', NULL),
(8, 'طلاء أظافر', 'طلاء أظافر عالي الجودة - ألوان متنوعة', 18.00, 8.00, 4, 1, 10, 1, 1, '2025-01-01 08:00:00', NULL),
(9, 'مقص شعر احترافي', 'مقص حاد ومتين للاستخدام المهني', 120.00, 80.00, 3, 0, 2, 1, 1, '2025-01-01 08:00:00', NULL),
(10, 'فرشاة تصفيف دائرية', 'فرشاة لتصفيف الشعر وإعطاء حجم', 25.00, 12.00, 3, 1, 5, 1, 1, '2025-01-01 08:00:00', NULL),
(11, 'كريم مرطب للوجه', 'كريم مرطب ومغذي للبشرة الجافة', 55.00, 30.00, 2, 1, 5, 1, 1, '2025-01-01 08:00:00', NULL),
(12, 'مزيل طلاء الأظافر', 'مزيل لطيف وفعال لطلاء الأظافر', 12.00, 6.00, 4, 1, 10, 1, 1, '2025-01-01 08:00:00', NULL),
(13, 'ماسكارا سوداء', 'ماسكارا مقاومة للماء لإطلالة جذابة', 32.00, 18.00, 5, 1, 5, 1, 1, '2025-01-01 08:00:00', NULL),
(14, 'أحمر شفاه وردي', 'أحمر شفاه كريمي بلون وردي طبيعي', 28.00, 15.00, 5, 1, 5, 1, 1, '2025-01-01 08:00:00', NULL),
(15, 'مقشر طبيعي للوجه', 'مقشر لطيف لإزالة خلايا الجلد الميتة', 40.00, 22.00, 2, 1, 5, 1, 1, '2025-01-01 08:00:00', NULL);

-- --------------------------------------------------------

--
-- Table structure for table `inventory`
--

CREATE TABLE `inventory` (
  `id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `branch_id` int(11) NOT NULL,
  `quantity` int(11) NOT NULL DEFAULT 0,
  `min_quantity` int(11) DEFAULT 5,
  `max_quantity` int(11) DEFAULT 100,
  `last_updated` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping demo data for table `inventory`
--

INSERT INTO `inventory` (`id`, `product_id`, `branch_id`, `quantity`, `min_quantity`, `max_quantity`, `last_updated`) VALUES
(1, 1, 1, 25, 5, 50, '2025-06-21 08:00:00'),
(2, 2, 1, 30, 5, 50, '2025-06-21 08:00:00'),
(3, 3, 1, 8, 3, 20, '2025-06-21 08:00:00'),
(4, 4, 1, 15, 5, 30, '2025-06-21 08:00:00'),
(5, 5, 1, 12, 3, 25, '2025-06-21 08:00:00'),
(6, 6, 1, 20, 5, 40, '2025-06-21 08:00:00'),
(7, 7, 1, 18, 5, 35, '2025-06-21 08:00:00'),
(8, 8, 1, 45, 10, 80, '2025-06-21 08:00:00');

-- --------------------------------------------------------

--
-- Table structure for table `users`
--

CREATE TABLE `users` (
  `id` int(11) NOT NULL,
  `username` varchar(50) NOT NULL,
  `password` varchar(255) NOT NULL,
  `name` varchar(100) NOT NULL,
  `email` varchar(100) DEFAULT NULL,
  `role` enum('admin','manager','cashier','employee') NOT NULL DEFAULT 'employee',
  `branch_id` int(11) DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `last_login` timestamp NULL DEFAULT NULL,
  `last_logout` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NULL DEFAULT NULL ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping demo data for table `users`
--

INSERT INTO `users` (`id`, `username`, `password`, `name`, `email`, `role`, `branch_id`, `is_active`, `last_login`, `last_logout`, `created_at`, `updated_at`) VALUES
(1, 'admin', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'مدير النظام', '<EMAIL>', 'admin', 1, 1, NULL, NULL, '2025-01-01 08:00:00', NULL),
(2, 'manager', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'مدير الفرع', '<EMAIL>', 'manager', 1, 1, NULL, NULL, '2025-01-01 08:00:00', NULL),
(3, 'cashier', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'أمين الصندوق', '<EMAIL>', 'cashier', 1, 1, NULL, NULL, '2025-01-01 08:00:00', NULL),
(4, 'demo', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'مستخدم تجريبي', '<EMAIL>', 'manager', 1, 1, NULL, NULL, '2025-01-01 08:00:00', NULL);

-- Note: All passwords are 'password' for demo purposes

-- --------------------------------------------------------

--
-- Table structure for table `invoices`
--

CREATE TABLE `invoices` (
  `id` int(11) NOT NULL,
  `invoice_number` varchar(20) NOT NULL,
  `customer_id` int(11) DEFAULT NULL,
  `cashier_id` int(11) DEFAULT NULL,
  `employee_id` int(11) DEFAULT NULL COMMENT 'الحلاق',
  `total_amount` decimal(10,2) NOT NULL DEFAULT 0.00,
  `discount_amount` decimal(10,2) DEFAULT 0.00,
  `discount_type` enum('percentage','amount') DEFAULT NULL,
  `tax_amount` decimal(10,2) DEFAULT 0.00,
  `final_amount` decimal(10,2) NOT NULL DEFAULT 0.00,
  `payment_method` enum('cash','card','other') NOT NULL DEFAULT 'cash',
  `payment_status` enum('paid','partial','unpaid') NOT NULL DEFAULT 'paid',
  `paid_amount` decimal(10,2) DEFAULT 0.00,
  `notes` text DEFAULT NULL,
  `branch_id` int(11) DEFAULT NULL,
  `end_day_id` int(11) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NULL DEFAULT NULL ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping demo data for table `invoices`
--

INSERT INTO `invoices` (`id`, `invoice_number`, `customer_id`, `cashier_id`, `employee_id`, `total_amount`, `discount_amount`, `discount_type`, `tax_amount`, `final_amount`, `payment_method`, `payment_status`, `paid_amount`, `notes`, `branch_id`, `end_day_id`, `created_at`, `updated_at`) VALUES
(1, 'INV-2025-001', 1, 1, 1, 230.00, 0.00, NULL, 34.50, 264.50, 'cash', 'paid', 264.50, 'قص شعر وصبغة', 1, 1, '2025-06-20 10:00:00', NULL),
(2, 'INV-2025-002', 2, 1, 1, 150.00, 15.00, 'amount', 20.25, 155.25, 'card', 'paid', 155.25, 'تسريحة مناسبة مع خصم', 1, 1, '2025-06-20 14:00:00', NULL),
(3, 'INV-2025-003', 3, 1, 2, 180.00, 0.00, NULL, 27.00, 207.00, 'cash', 'paid', 207.00, 'تنظيف بشرة', 1, 1, '2025-06-20 16:00:00', NULL),
(4, 'INV-2025-004', 4, 1, 1, 140.00, 0.00, NULL, 21.00, 161.00, 'cash', 'unpaid', 0.00, 'مانيكير وبديكير - دفع مؤجل', 1, NULL, '2025-06-21 09:00:00', NULL),
(5, 'INV-2025-005', 5, 1, 2, 80.00, 0.00, NULL, 12.00, 92.00, 'cash', 'paid', 92.00, 'قص شعر عادي', 1, NULL, '2025-06-21 11:00:00', NULL),
(6, 'INV-2025-006', 6, 1, 1, 450.00, 45.00, 'amount', 60.75, 465.75, 'card', 'paid', 465.75, 'فرد شعر برازيلي مع خصم VIP', 1, 1, '2025-06-19 10:00:00', NULL),
(7, 'INV-2025-007', 7, 1, 2, 200.00, 0.00, NULL, 30.00, 230.00, 'cash', 'paid', 230.00, 'حمام كريم وتسريحة', 1, 1, '2025-06-19 14:00:00', NULL),
(8, 'INV-2025-008', 8, 1, 1, 120.00, 12.00, 'amount', 16.20, 124.20, 'cash', 'paid', 124.20, 'دفع بنقاط الولاء', 1, 1, '2025-06-19 16:00:00', NULL),
(9, 'INV-2025-009', 9, 1, 2, 340.00, 0.00, NULL, 51.00, 391.00, 'card', 'paid', 391.00, 'باقة خدمات متكاملة', 1, 1, '2025-06-18 10:00:00', NULL),
(10, 'INV-2025-010', 10, 1, 1, 95.00, 0.00, NULL, 14.25, 109.25, 'cash', 'paid', 109.25, 'قص وتصفيف', 1, 1, '2025-06-18 15:00:00', NULL),
(11, 'INV-2025-011', 11, 1, 2, 140.00, 0.00, NULL, 21.00, 161.00, 'card', 'paid', 161.00, 'مانيكير وبديكير', 1, 1, '2025-06-17 11:00:00', NULL),
(12, 'INV-2025-012', 12, 1, 1, 520.00, 52.00, 'amount', 70.20, 538.20, 'card', 'paid', 538.20, 'باقة VIP كاملة', 1, 1, '2025-06-17 14:00:00', NULL),
(13, 'INV-2025-013', 13, 1, 2, 180.00, 18.00, 'amount', 24.30, 186.30, 'cash', 'paid', 186.30, 'تنظيف بشرة مع خصم', 1, 1, '2025-06-16 10:00:00', NULL),
(14, 'INV-2025-014', 14, 1, 1, 250.00, 0.00, NULL, 37.50, 287.50, 'cash', 'partial', 150.00, 'صبغة شعر - دفع جزئي', 1, 1, '2025-06-16 13:00:00', NULL),
(15, 'INV-2025-015', 15, 1, 2, 75.00, 0.00, NULL, 11.25, 86.25, 'cash', 'paid', 86.25, 'زيت أرغان للشعر', 1, 1, '2025-06-15 16:00:00', NULL);

-- --------------------------------------------------------

--
-- Table structure for table `invoice_items`
--

CREATE TABLE `invoice_items` (
  `id` int(11) NOT NULL,
  `invoice_id` int(11) NOT NULL,
  `item_type` enum('service','product') NOT NULL,
  `item_id` int(11) NOT NULL,
  `employee_id` int(11) DEFAULT NULL,
  `quantity` int(11) NOT NULL DEFAULT 1,
  `unit_price` decimal(10,2) NOT NULL,
  `total` decimal(10,2) NOT NULL,
  `notes` text DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping demo data for table `invoice_items`
--

INSERT INTO `invoice_items` (`id`, `invoice_id`, `item_type`, `item_id`, `employee_id`, `quantity`, `unit_price`, `total`, `notes`) VALUES
(1, 1, 'service', 1, 1, 1, 80.00, 80.00, 'قص شعر'),
(2, 1, 'service', 2, 2, 1, 250.00, 250.00, 'صبغة شعر'),
(3, 1, 'product', 1, NULL, 1, 45.00, 45.00, 'شامبو للعناية'),
(4, 2, 'service', 3, 1, 1, 150.00, 150.00, 'تسريحة مناسبة'),
(5, 3, 'service', 4, 3, 1, 180.00, 180.00, 'تنظيف بشرة'),
(6, 4, 'service', 5, 1, 1, 60.00, 60.00, 'مانيكير'),
(7, 4, 'service', 6, 1, 1, 80.00, 80.00, 'بديكير'),
(8, 5, 'service', 1, 1, 1, 80.00, 80.00, 'قص شعر عادي');

-- --------------------------------------------------------

--
-- Table structure for table `expenses`
--

CREATE TABLE `expenses` (
  `id` int(11) NOT NULL,
  `category_id` int(11) DEFAULT NULL,
  `amount` decimal(10,2) NOT NULL,
  `description` text DEFAULT NULL,
  `date` date NOT NULL,
  `payment_method` enum('cash','card','other') NOT NULL DEFAULT 'cash',
  `user_id` int(11) DEFAULT NULL,
  `branch_id` int(11) DEFAULT NULL,
  `end_day_id` int(11) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NULL DEFAULT NULL ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping demo data for table `expenses`
--

INSERT INTO `expenses` (`id`, `category_id`, `amount`, `description`, `date`, `payment_method`, `user_id`, `branch_id`, `end_day_id`, `created_at`, `updated_at`) VALUES
(1, 1, 450.00, 'فاتورة الكهرباء الشهرية', '2025-06-20', 'cash', 1, 1, 1, '2025-06-20 08:00:00', NULL),
(2, 2, 1200.00, 'شامبو وبلسم وصبغات', '2025-06-20', 'cash', 1, 1, 1, '2025-06-20 10:00:00', NULL),
(3, 3, 150.00, 'إصلاح مجفف الشعر الكبير', '2025-06-21', 'cash', 1, 1, NULL, '2025-06-21 09:00:00', NULL),
(4, 4, 80.00, 'مواد تنظيف وتعقيم', '2025-06-21', 'cash', 1, 1, NULL, '2025-06-21 10:00:00', NULL),
(5, 1, 120.00, 'فاتورة المياه الشهرية', '2025-06-19', 'cash', 1, 1, 1, '2025-06-19 08:00:00', NULL),
(6, 5, 15000.00, 'رواتب شهر يونيو', '2025-06-01', 'cash', 1, 1, NULL, '2025-06-01 08:00:00', NULL),
(7, 2, 350.00, 'مقصات ومشط وفرش', '2025-06-18', 'cash', 1, 1, 1, '2025-06-18 10:00:00', NULL),
(8, 6, 3500.00, 'إيجار شهر يونيو', '2025-06-01', 'cash', 1, 1, NULL, '2025-06-01 09:00:00', NULL),
(9, 7, 800.00, 'تأمين طبي شهري', '2025-06-15', 'cash', 1, 1, NULL, '2025-06-15 10:00:00', NULL),
(10, 8, 200.00, 'وقود سيارة التوصيل', '2025-06-17', 'cash', 1, 1, 1, '2025-06-17 11:00:00', NULL),
(11, 9, 300.00, 'حملة إعلانية على فيسبوك', '2025-06-16', 'card', 1, 1, 1, '2025-06-16 12:00:00', NULL),
(12, 3, 250.00, 'صيانة وحدة التكييف', '2025-06-14', 'cash', 1, 1, NULL, '2025-06-14 13:00:00', NULL);

-- --------------------------------------------------------

--
-- Table structure for table `settings`
--

CREATE TABLE `settings` (
  `id` int(11) NOT NULL,
  `setting_key` varchar(100) NOT NULL,
  `setting_value` text DEFAULT NULL,
  `description` varchar(255) DEFAULT NULL,
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping demo data for table `settings`
--

INSERT INTO `settings` (`id`, `setting_key`, `setting_value`, `description`, `updated_at`) VALUES
(1, 'system_name', 'صالون الجمال الراقي - نسخة تجريبية', 'اسم النظام', '2025-06-21 08:00:00'),
(2, 'system_currency', 'ريال سعودي', 'عملة النظام', '2025-06-21 08:00:00'),
(3, 'system_currency_symbol', 'ر.س', 'رمز العملة', '2025-06-21 08:00:00'),
(4, 'tax_rate', '15', 'نسبة الضريبة المضافة', '2025-06-21 08:00:00'),
(5, 'loyalty_points_rate', '1', 'نقطة ولاء لكل ريال', '2025-06-21 08:00:00'),
(6, 'demo_mode', '1', 'تفعيل الوضع التجريبي', '2025-06-21 08:00:00'),
(7, 'salon_address', 'شارع الملك فهد، الرياض، المملكة العربية السعودية', 'عنوان الصالون', '2025-06-21 08:00:00'),
(8, 'salon_phone', '0112345678', 'هاتف الصالون', '2025-06-21 08:00:00'),
(9, 'salon_email', '<EMAIL>', 'بريد الصالون الإلكتروني', '2025-06-21 08:00:00'),
(10, 'working_hours_start', '09:00', 'بداية ساعات العمل', '2025-06-21 08:00:00'),
(11, 'working_hours_end', '22:00', 'نهاية ساعات العمل', '2025-06-21 08:00:00'),
(12, 'appointment_duration', '30', 'مدة الموعد الافتراضية بالدقائق', '2025-06-21 08:00:00'),
(13, 'max_advance_booking', '30', 'أقصى عدد أيام للحجز المسبق', '2025-06-21 08:00:00'),
(14, 'demo_reset_time', '03:00', 'وقت إعادة تعيين البيانات التجريبية', '2025-06-21 08:00:00'),
(15, 'demo_warning_enabled', '1', 'تفعيل تحذيرات النسخة التجريبية', '2025-06-21 08:00:00');

-- --------------------------------------------------------

--
-- Table structure for table `customer_visits`
--

CREATE TABLE `customer_visits` (
  `id` int(11) NOT NULL,
  `customer_id` int(11) NOT NULL,
  `visit_date` datetime NOT NULL DEFAULT current_timestamp(),
  `notes` text DEFAULT NULL,
  `branch_id` int(11) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping demo data for table `customer_visits`
--

INSERT INTO `customer_visits` (`id`, `customer_id`, `visit_date`, `notes`, `branch_id`) VALUES
(1, 1, '2025-06-20 10:30:00', 'زيارة لقص الشعر', 1),
(2, 2, '2025-06-20 14:15:00', 'صبغة شعر', 1),
(3, 3, '2025-06-20 16:45:00', 'تنظيف بشرة', 1),
(4, 4, '2025-06-19 11:20:00', 'مانيكير وبديكير', 1),
(5, 5, '2025-06-19 15:30:00', 'قص شعر سريع', 1);

-- --------------------------------------------------------

--
-- Table structure for table `employee_attendance`
--

CREATE TABLE `employee_attendance` (
  `id` int(11) NOT NULL,
  `employee_id` int(11) NOT NULL,
  `date` date NOT NULL,
  `check_in` time DEFAULT NULL,
  `check_out` time DEFAULT NULL,
  `notes` text DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping demo data for table `employee_attendance`
--

INSERT INTO `employee_attendance` (`id`, `employee_id`, `date`, `check_in`, `check_out`, `notes`) VALUES
(1, 1, '2025-06-21', '09:00:00', '18:00:00', 'يوم عمل عادي'),
(2, 2, '2025-06-21', '09:15:00', '18:30:00', 'تأخير بسيط'),
(3, 3, '2025-06-21', '08:45:00', '17:45:00', 'حضور مبكر'),
(4, 4, '2025-06-21', '09:30:00', '18:00:00', 'حضور متأخر'),
(5, 1, '2025-06-20', '09:00:00', '18:00:00', 'يوم عمل عادي'),
(6, 2, '2025-06-20', '09:00:00', '19:00:00', 'ساعة إضافية');

-- --------------------------------------------------------

--
-- Table structure for table `employee_salaries`
--

CREATE TABLE `employee_salaries` (
  `id` int(11) NOT NULL,
  `employee_id` int(11) NOT NULL,
  `month` int(11) NOT NULL,
  `year` int(11) NOT NULL,
  `fixed_amount` decimal(10,2) DEFAULT 0.00,
  `commission_amount` decimal(10,2) DEFAULT 0.00,
  `bonuses` decimal(10,2) DEFAULT 0.00,
  `deductions` decimal(10,2) DEFAULT 0.00,
  `total_amount` decimal(10,2) NOT NULL,
  `payment_date` date DEFAULT NULL,
  `payment_status` enum('paid','unpaid') NOT NULL DEFAULT 'unpaid',
  `notes` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NULL DEFAULT NULL ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping demo data for table `employee_salaries`
--

INSERT INTO `employee_salaries` (`id`, `employee_id`, `month`, `year`, `fixed_amount`, `commission_amount`, `bonuses`, `deductions`, `total_amount`, `payment_date`, `payment_status`, `notes`, `created_at`, `updated_at`) VALUES
(1, 1, 6, 2025, 4500.00, 675.00, 200.00, 0.00, 5375.00, '2025-06-01', 'paid', 'راتب يونيو مع عمولة وبونص', '2025-06-01 08:00:00', NULL),
(2, 2, 6, 2025, 4000.00, 480.00, 0.00, 0.00, 4480.00, '2025-06-01', 'paid', 'راتب يونيو مع عمولة', '2025-06-01 08:00:00', NULL),
(3, 3, 6, 2025, 3800.00, 380.00, 100.00, 50.00, 4230.00, '2025-06-01', 'paid', 'راتب يونيو مع خصم تأخير', '2025-06-01 08:00:00', NULL),
(4, 4, 6, 2025, 2500.00, 125.00, 0.00, 0.00, 2625.00, NULL, 'unpaid', 'راتب يونيو لم يدفع بعد', '2025-06-01 08:00:00', NULL);

-- --------------------------------------------------------

--
-- Table structure for table `end_days`
--

CREATE TABLE `end_days` (
  `id` int(11) NOT NULL,
  `date` date NOT NULL,
  `cash_amount` decimal(10,2) DEFAULT 0.00,
  `card_amount` decimal(10,2) DEFAULT 0.00,
  `other_amount` decimal(10,2) DEFAULT 0.00,
  `total_sales` decimal(10,2) DEFAULT 0.00,
  `total_expenses` decimal(10,2) DEFAULT 0.00,
  `total_discounts` decimal(10,2) DEFAULT 0.00,
  `notes` text DEFAULT NULL,
  `closed_by` int(11) DEFAULT NULL,
  `branch_id` int(11) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `closed_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping demo data for table `end_days`
--

INSERT INTO `end_days` (`id`, `date`, `cash_amount`, `card_amount`, `other_amount`, `total_sales`, `total_expenses`, `total_discounts`, `notes`, `closed_by`, `branch_id`, `created_at`, `closed_at`) VALUES
(1, '2025-06-20', 1250.00, 890.00, 0.00, 2140.00, 450.00, 85.00, 'يوم جيد في المبيعات', 3, 1, '2025-06-20 22:00:00', '2025-06-20 22:00:00'),
(2, '2025-06-19', 980.00, 1120.00, 50.00, 2150.00, 380.00, 120.00, 'مبيعات ممتازة', 3, 1, '2025-06-19 22:00:00', '2025-06-19 22:00:00'),
(3, '2025-06-18', 1450.00, 650.00, 0.00, 2100.00, 520.00, 95.00, 'يوم عادي', 3, 1, '2025-06-18 22:00:00', '2025-06-18 22:00:00');

-- --------------------------------------------------------

--
-- Table structure for table `notifications`
--

CREATE TABLE `notifications` (
  `id` int(11) NOT NULL,
  `type` enum('appointment_reminder','system','custom') NOT NULL DEFAULT 'system',
  `recipient_id` int(11) DEFAULT NULL,
  `recipient_type` enum('customer','user') NOT NULL DEFAULT 'customer',
  `title` varchar(255) NOT NULL,
  `message` text NOT NULL,
  `related_id` int(11) DEFAULT NULL,
  `related_type` varchar(50) DEFAULT NULL,
  `branch_id` int(11) DEFAULT NULL,
  `is_read` tinyint(1) NOT NULL DEFAULT 0,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `read_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping demo data for table `notifications`
--

INSERT INTO `notifications` (`id`, `type`, `recipient_id`, `recipient_type`, `title`, `message`, `related_id`, `related_type`, `branch_id`, `is_read`, `created_at`, `read_at`) VALUES
(1, 'appointment_reminder', 1, 'customer', 'تذكير بموعد', 'لديك موعد غداً في الساعة 10:00 صباحاً', 1, 'appointment', 1, 0, '2025-06-21 10:00:00', NULL),
(2, 'system', 1, 'user', 'نفاد المخزون', 'شامبو الشعر الجاف أوشك على النفاد', NULL, NULL, 1, 0, '2025-06-21 11:00:00', NULL),
(3, 'system', 3, 'user', 'دفعة جديدة', 'تم استلام دفعة نقدية بقيمة 264.50 ر.س', 1, 'invoice', 1, 1, '2025-06-20 10:30:00', '2025-06-20 11:00:00'),
(4, 'appointment_reminder', 17, 'customer', 'موعد ملغي', 'تم إلغاء موعدك المحجوز ليوم غد', 17, 'appointment', 1, 0, '2025-06-22 08:00:00', NULL),
(5, 'system', 1, 'user', 'تذكير صيانة', 'حان موعد صيانة أجهزة التكييف', NULL, NULL, 1, 0, '2025-06-21 12:00:00', NULL),
(6, 'custom', 2, 'customer', 'عرض خاص', 'خصم 20% على جميع خدمات الشعر هذا الأسبوع', NULL, NULL, 1, 0, '2025-06-21 13:00:00', NULL),
(7, 'system', 2, 'user', 'موظف جديد', 'تم إضافة موظف جديد للفريق', 4, 'employee', 1, 1, '2025-06-21 14:00:00', '2025-06-21 15:00:00');

-- --------------------------------------------------------

--
-- Table structure for table `product_categories`
--

CREATE TABLE `product_categories` (
  `id` int(11) NOT NULL,
  `name` varchar(100) NOT NULL,
  `description` text DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping demo data for table `product_categories`
--

INSERT INTO `product_categories` (`id`, `name`, `description`) VALUES
(1, 'منتجات الشعر', 'شامبو وبلسم وزيوت الشعر'),
(2, 'منتجات البشرة', 'كريمات ومقشرات البشرة'),
(3, 'أدوات التصفيف', 'مقصات وأمشاط وفرش'),
(4, 'منتجات الأظافر', 'طلاء أظافر وأدوات المانيكير'),
(5, 'مستحضرات التجميل', 'مكياج ومساحيق التجميل'),
(6, 'منتجات العناية', 'كريمات مرطبة وواقي شمس');

-- --------------------------------------------------------

--
-- Table structure for table `service_categories`
--

CREATE TABLE `service_categories` (
  `id` int(11) NOT NULL,
  `name` varchar(100) NOT NULL,
  `description` text DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping demo data for table `service_categories`
--

INSERT INTO `service_categories` (`id`, `name`, `description`) VALUES
(1, 'خدمات الشعر', 'قص وصبغ وتصفيف الشعر'),
(2, 'خدمات البشرة', 'تنظيف وعناية بالبشرة'),
(3, 'خدمات الأظافر', 'مانيكير وبديكير'),
(4, 'خدمات التجميل', 'مكياج ورسم الحواجب'),
(5, 'خدمات العناية', 'ماسكات وعلاجات طبيعية');

-- --------------------------------------------------------

--
-- Table structure for table `expense_categories`
--

CREATE TABLE `expense_categories` (
  `id` int(11) NOT NULL,
  `name` varchar(100) NOT NULL,
  `description` text DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping demo data for table `expense_categories`
--

INSERT INTO `expense_categories` (`id`, `name`, `description`) VALUES
(1, 'فواتير', 'فواتير المرافق والخدمات'),
(2, 'مشتريات', 'شراء المنتجات والمستلزمات'),
(3, 'صيانة', 'صيانة وإصلاحات'),
(4, 'تشغيل', 'مصروفات التشغيل اليومية'),
(5, 'رواتب', 'رواتب ومكافآت الموظفين'),
(6, 'إيجار', 'إيجار المحل والمعدات'),
(7, 'تأمين', 'التأمين الطبي والتأمينات'),
(8, 'مواصلات', 'مصروفات المواصلات والوقود'),
(9, 'تسويق', 'مصروفات التسويق والإعلان');

-- --------------------------------------------------------

--
-- Table structure for table `inventory_transactions`
--

CREATE TABLE `inventory_transactions` (
  `id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `transaction_type` enum('in','out') NOT NULL,
  `quantity` int(11) NOT NULL,
  `previous_quantity` int(11) NOT NULL,
  `current_quantity` int(11) NOT NULL,
  `notes` text DEFAULT NULL,
  `user_id` int(11) DEFAULT NULL,
  `branch_id` int(11) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping demo data for table `inventory_transactions`
--

INSERT INTO `inventory_transactions` (`id`, `product_id`, `transaction_type`, `quantity`, `previous_quantity`, `current_quantity`, `notes`, `user_id`, `branch_id`, `created_at`) VALUES
(1, 1, 'in', 10, 0, 10, 'إضافة مخزون أولي', 1, 1, '2025-06-01 08:00:00'),
(2, 2, 'in', 8, 0, 8, 'إضافة مخزون أولي', 1, 1, '2025-06-01 08:00:00'),
(3, 3, 'in', 15, 0, 15, 'إضافة مخزون أولي', 1, 1, '2025-06-01 08:00:00'),
(4, 1, 'out', 2, 10, 8, 'استهلاك في الخدمات', 2, 1, '2025-06-20 10:30:00'),
(5, 2, 'out', 1, 8, 7, 'بيع للعميل', 3, 1, '2025-06-20 14:15:00'),
(6, 4, 'in', 5, 0, 5, 'شراء جديد', 1, 1, '2025-06-15 09:00:00'),
(7, 5, 'in', 12, 0, 12, 'شراء جديد', 1, 1, '2025-06-15 09:00:00');

-- --------------------------------------------------------

--
-- Table structure for table `promotions`
--

CREATE TABLE `promotions` (
  `id` int(11) NOT NULL,
  `name` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `condition_type` enum('total_amount','items_count','specific_product','specific_service') NOT NULL,
  `condition_value` decimal(10,2) NOT NULL,
  `condition_max_value` decimal(10,2) DEFAULT NULL,
  `discount_type` enum('percentage','fixed') NOT NULL,
  `discount_value` decimal(10,2) NOT NULL DEFAULT 0.00,
  `start_date` date DEFAULT NULL,
  `end_date` date DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `usage_limit` int(11) DEFAULT NULL,
  `used_count` int(11) DEFAULT 0,
  `branch_id` int(11) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NULL DEFAULT NULL ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping demo data for table `promotions`
--

INSERT INTO `promotions` (`id`, `name`, `description`, `condition_type`, `condition_value`, `condition_max_value`, `discount_type`, `discount_value`, `start_date`, `end_date`, `is_active`, `usage_limit`, `used_count`, `branch_id`, `created_at`, `updated_at`) VALUES
(1, 'خصم الصيف', 'خصم 20% على جميع خدمات الشعر', 'total_amount', 100.00, NULL, 'percentage', 20.00, '2025-06-01', '2025-08-31', 1, 100, 15, 1, '2025-06-01 08:00:00', NULL),
(2, 'عرض العميلة الجديدة', 'خصم 50 ريال للعميلات الجديدات', 'total_amount', 150.00, NULL, 'fixed', 50.00, '2025-06-01', '2025-12-31', 1, NULL, 8, 1, '2025-06-01 08:00:00', NULL),
(3, 'باقة VIP', 'خصم 15% على الباقات فوق 400 ريال', 'total_amount', 400.00, NULL, 'percentage', 15.00, '2025-06-15', '2025-07-15', 1, 50, 3, 1, '2025-06-15 08:00:00', NULL);

-- --------------------------------------------------------

--
-- Table structure for table `promotion_usage`
--

CREATE TABLE `promotion_usage` (
  `id` int(11) NOT NULL,
  `promotion_id` int(11) NOT NULL,
  `invoice_id` int(11) NOT NULL,
  `customer_id` int(11) DEFAULT NULL,
  `discount_amount` decimal(10,2) NOT NULL DEFAULT 0.00,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping demo data for table `promotion_usage`
--

INSERT INTO `promotion_usage` (`id`, `promotion_id`, `invoice_id`, `customer_id`, `discount_amount`, `created_at`) VALUES
(1, 1, 2, 2, 30.00, '2025-06-20 14:00:00'),
(2, 2, 5, 5, 50.00, '2025-06-21 11:00:00'),
(3, 3, 12, 12, 80.70, '2025-06-17 14:00:00');

-- --------------------------------------------------------

--
-- Table structure for table `promo_codes`
--

CREATE TABLE `promo_codes` (
  `id` int(11) NOT NULL,
  `code` varchar(50) NOT NULL,
  `name` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `discount_type` enum('percentage','fixed') NOT NULL,
  `discount_value` decimal(10,2) NOT NULL,
  `start_date` date DEFAULT NULL,
  `end_date` date DEFAULT NULL,
  `min_invoice_amount` decimal(10,2) DEFAULT NULL,
  `max_invoice_amount` decimal(10,2) DEFAULT NULL,
  `usage_limit` int(11) DEFAULT NULL,
  `used_count` int(11) DEFAULT 0,
  `is_active` tinyint(1) DEFAULT 1,
  `branch_id` int(11) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NULL DEFAULT NULL ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping demo data for table `promo_codes`
--

INSERT INTO `promo_codes` (`id`, `code`, `name`, `description`, `discount_type`, `discount_value`, `start_date`, `end_date`, `min_invoice_amount`, `max_invoice_amount`, `usage_limit`, `used_count`, `is_active`, `branch_id`, `created_at`, `updated_at`) VALUES
(1, 'SUMMER25', 'كود خصم الصيف', 'خصم 25% لفترة محدودة', 'percentage', 25.00, '2025-06-01', '2025-08-31', 100.00, 1000.00, 50, 12, 1, 1, '2025-06-01 08:00:00', NULL),
(2, 'WELCOME50', 'ترحيب بالعملاء الجدد', 'خصم 50 ريال للعملاء الجدد', 'fixed', 50.00, '2025-06-01', '2025-12-31', 200.00, NULL, 100, 25, 1, 1, '2025-06-01 08:00:00', NULL),
(3, 'VIP15', 'خصم VIP', 'خصم خاص للعملاء المميزين', 'percentage', 15.00, '2025-06-01', '2025-07-31', 300.00, NULL, 30, 8, 1, 1, '2025-06-01 08:00:00', NULL);

-- --------------------------------------------------------

--
-- Table structure for table `promo_code_customers`
--

CREATE TABLE `promo_code_customers` (
  `id` int(11) NOT NULL,
  `promo_code_id` int(11) NOT NULL COMMENT 'معرف كود الخصم',
  `customer_id` int(11) NOT NULL COMMENT 'معرف العميل',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `promo_code_customers`
--

INSERT INTO `promo_code_customers` (`id`, `promo_code_id`, `customer_id`, `created_at`) VALUES
(1, 2, 8, '2025-06-02 22:46:34'),
(2, 3, 3, '2025-06-03 22:06:27'),
(3, 3, 5, '2025-06-03 22:06:27'),
(4, 3, 8, '2025-06-03 22:06:27'),
(5, 3, 12, '2025-06-03 22:06:27');

-- --------------------------------------------------------

--
-- Table structure for table `promo_code_usage`
--

CREATE TABLE `promo_code_usage` (
  `id` int(11) NOT NULL,
  `promo_code_id` int(11) NOT NULL,
  `invoice_id` int(11) NOT NULL,
  `customer_id` int(11) DEFAULT NULL,
  `discount_amount` decimal(10,2) NOT NULL DEFAULT 0.00,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping demo data for table `promo_code_usage`
--

INSERT INTO `promo_code_usage` (`id`, `promo_code_id`, `invoice_id`, `customer_id`, `discount_amount`, `created_at`) VALUES
(1, 1, 6, 6, 116.44, '2025-06-19 10:00:00'),
(2, 2, 8, 8, 50.00, '2025-06-19 16:00:00'),
(3, 3, 9, 9, 58.65, '2025-06-18 10:00:00');

-- --------------------------------------------------------

--
-- Table structure for table `service_employees`
--

CREATE TABLE `service_employees` (
  `service_id` int(11) NOT NULL,
  `employee_id` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping demo data for table `service_employees`
--

INSERT INTO `service_employees` (`service_id`, `employee_id`) VALUES
(1, 1), (1, 2), (1, 3),
(2, 1), (2, 2),
(3, 1), (3, 2), (3, 3),
(4, 3),
(5, 3),
(6, 3),
(7, 1), (7, 2),
(8, 1), (8, 2), (8, 3);

-- --------------------------------------------------------

--
-- Table structure for table `user_permissions`
--

CREATE TABLE `user_permissions` (
  `id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `permission` varchar(50) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping demo data for table `user_permissions`
--

INSERT INTO `user_permissions` (`id`, `user_id`, `permission`) VALUES
(1, 1, 'manage_users'),
(2, 1, 'manage_settings'),
(3, 1, 'view_reports'),
(4, 1, 'manage_inventory'),
(5, 2, 'view_reports'),
(6, 2, 'manage_appointments'),
(7, 2, 'manage_customers'),
(8, 3, 'create_invoices'),
(9, 3, 'manage_payments'),
(10, 4, 'view_schedule');

-- --------------------------------------------------------

--
-- Indexes for dumped tables
--

ALTER TABLE `appointments`
  ADD PRIMARY KEY (`id`),
  ADD KEY `customer_id` (`customer_id`),
  ADD KEY `employee_id` (`employee_id`),
  ADD KEY `service_id` (`service_id`),
  ADD KEY `branch_id` (`branch_id`);

ALTER TABLE `branches`
  ADD PRIMARY KEY (`id`),
  ADD KEY `manager_id` (`manager_id`);

ALTER TABLE `customers`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `phone` (`phone`),
  ADD KEY `branch_id` (`branch_id`);

ALTER TABLE `employees`
  ADD PRIMARY KEY (`id`),
  ADD KEY `branch_id` (`branch_id`);

ALTER TABLE `services`
  ADD PRIMARY KEY (`id`);

ALTER TABLE `products`
  ADD PRIMARY KEY (`id`),
  ADD KEY `category_id` (`category_id`),
  ADD KEY `branch_id` (`branch_id`);

ALTER TABLE `inventory`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `product_branch` (`product_id`,`branch_id`),
  ADD KEY `branch_id` (`branch_id`);

ALTER TABLE `users`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `username` (`username`),
  ADD KEY `branch_id` (`branch_id`);

ALTER TABLE `invoices`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `invoice_number` (`invoice_number`),
  ADD KEY `customer_id` (`customer_id`),
  ADD KEY `cashier_id` (`cashier_id`),
  ADD KEY `employee_id` (`employee_id`),
  ADD KEY `branch_id` (`branch_id`),
  ADD KEY `end_day_id` (`end_day_id`);

ALTER TABLE `invoice_items`
  ADD PRIMARY KEY (`id`),
  ADD KEY `invoice_id` (`invoice_id`),
  ADD KEY `employee_id` (`employee_id`);

ALTER TABLE `expenses`
  ADD PRIMARY KEY (`id`),
  ADD KEY `category_id` (`category_id`),
  ADD KEY `user_id` (`user_id`),
  ADD KEY `branch_id` (`branch_id`),
  ADD KEY `end_day_id` (`end_day_id`);

ALTER TABLE `settings`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `setting_key` (`setting_key`);

ALTER TABLE `customer_visits`
  ADD PRIMARY KEY (`id`),
  ADD KEY `customer_id` (`customer_id`),
  ADD KEY `branch_id` (`branch_id`);

ALTER TABLE `employee_attendance`
  ADD PRIMARY KEY (`id`),
  ADD KEY `employee_id` (`employee_id`);

ALTER TABLE `employee_salaries`
  ADD PRIMARY KEY (`id`),
  ADD KEY `employee_id` (`employee_id`);

ALTER TABLE `end_days`
  ADD PRIMARY KEY (`id`),
  ADD KEY `closed_by` (`closed_by`),
  ADD KEY `branch_id` (`branch_id`);

ALTER TABLE `expense_categories`
  ADD PRIMARY KEY (`id`);

ALTER TABLE `inventory_transactions`
  ADD PRIMARY KEY (`id`),
  ADD KEY `product_id` (`product_id`),
  ADD KEY `user_id` (`user_id`),
  ADD KEY `branch_id` (`branch_id`);

ALTER TABLE `notifications`
  ADD PRIMARY KEY (`id`),
  ADD KEY `recipient_id` (`recipient_id`),
  ADD KEY `branch_id` (`branch_id`);

ALTER TABLE `product_categories`
  ADD PRIMARY KEY (`id`);

ALTER TABLE `promotions`
  ADD PRIMARY KEY (`id`),
  ADD KEY `branch_id` (`branch_id`);

ALTER TABLE `promotion_usage`
  ADD PRIMARY KEY (`id`),
  ADD KEY `promotion_id` (`promotion_id`),
  ADD KEY `invoice_id` (`invoice_id`),
  ADD KEY `customer_id` (`customer_id`);

ALTER TABLE `promo_codes`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `code` (`code`),
  ADD KEY `branch_id` (`branch_id`);

ALTER TABLE `promo_code_customers`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `promo_code_customer` (`promo_code_id`,`customer_id`),
  ADD KEY `customer_id` (`customer_id`);

ALTER TABLE `promo_code_usage`
  ADD PRIMARY KEY (`id`),
  ADD KEY `promo_code_id` (`promo_code_id`),
  ADD KEY `invoice_id` (`invoice_id`),
  ADD KEY `customer_id` (`customer_id`);

ALTER TABLE `service_categories`
  ADD PRIMARY KEY (`id`);

ALTER TABLE `service_employees`
  ADD PRIMARY KEY (`service_id`,`employee_id`),
  ADD KEY `employee_id` (`employee_id`);

ALTER TABLE `user_permissions`
  ADD PRIMARY KEY (`id`),
  ADD KEY `user_id` (`user_id`);

--
-- AUTO_INCREMENT for dumped tables
--

ALTER TABLE `appointments`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=21;

ALTER TABLE `branches`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

ALTER TABLE `customers`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=21;

ALTER TABLE `employees`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;

ALTER TABLE `services`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=9;

ALTER TABLE `products`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=9;

ALTER TABLE `inventory`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=9;

ALTER TABLE `users`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;

ALTER TABLE `invoices`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=16;

ALTER TABLE `invoice_items`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=25;

ALTER TABLE `expenses`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=13;

ALTER TABLE `settings`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=16;

ALTER TABLE `customer_visits`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=6;

ALTER TABLE `employee_attendance`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=7;

ALTER TABLE `employee_salaries`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;

ALTER TABLE `end_days`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

ALTER TABLE `expense_categories`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=8;

ALTER TABLE `inventory_transactions`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=8;

ALTER TABLE `notifications`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=8;

ALTER TABLE `product_categories`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=7;

ALTER TABLE `promotions`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

ALTER TABLE `promotion_usage`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

ALTER TABLE `promo_codes`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

ALTER TABLE `promo_code_customers`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=6;

ALTER TABLE `promo_code_usage`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

ALTER TABLE `service_categories`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=6;

ALTER TABLE `user_permissions`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=11;

--
-- Constraints for table `expenses`
--
ALTER TABLE `expenses`
  ADD CONSTRAINT `expenses_category_id_fk` FOREIGN KEY (`category_id`) REFERENCES `expense_categories` (`id`) ON DELETE SET NULL ON UPDATE CASCADE,
  ADD CONSTRAINT `expenses_user_id_fk` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE SET NULL ON UPDATE CASCADE,
  ADD CONSTRAINT `expenses_branch_id_fk` FOREIGN KEY (`branch_id`) REFERENCES `branches` (`id`) ON DELETE SET NULL ON UPDATE CASCADE,
  ADD CONSTRAINT `expenses_end_day_id_fk` FOREIGN KEY (`end_day_id`) REFERENCES `end_days` (`id`) ON DELETE SET NULL ON UPDATE CASCADE;

--
-- Constraints for table `products`
--
ALTER TABLE `products`
  ADD CONSTRAINT `products_category_id_fk` FOREIGN KEY (`category_id`) REFERENCES `product_categories` (`id`) ON DELETE SET NULL ON UPDATE CASCADE,
  ADD CONSTRAINT `products_branch_id_fk` FOREIGN KEY (`branch_id`) REFERENCES `branches` (`id`) ON DELETE SET NULL ON UPDATE CASCADE;

--
-- Constraints for table `invoices`
--
ALTER TABLE `invoices`
  ADD CONSTRAINT `invoices_customer_id_fk` FOREIGN KEY (`customer_id`) REFERENCES `customers` (`id`) ON DELETE SET NULL ON UPDATE CASCADE,
  ADD CONSTRAINT `invoices_cashier_id_fk` FOREIGN KEY (`cashier_id`) REFERENCES `users` (`id`) ON DELETE SET NULL ON UPDATE CASCADE,
  ADD CONSTRAINT `invoices_employee_id_fk` FOREIGN KEY (`employee_id`) REFERENCES `employees` (`id`) ON DELETE SET NULL ON UPDATE CASCADE,
  ADD CONSTRAINT `invoices_branch_id_fk` FOREIGN KEY (`branch_id`) REFERENCES `branches` (`id`) ON DELETE SET NULL ON UPDATE CASCADE,
  ADD CONSTRAINT `invoices_end_day_id_fk` FOREIGN KEY (`end_day_id`) REFERENCES `end_days` (`id`) ON DELETE SET NULL ON UPDATE CASCADE;

--
-- Constraints for table `promo_code_customers`
--
ALTER TABLE `promo_code_customers`
  ADD CONSTRAINT `promo_code_customers_customer_id_fk` FOREIGN KEY (`customer_id`) REFERENCES `customers` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  ADD CONSTRAINT `promo_code_customers_promo_code_id_fk` FOREIGN KEY (`promo_code_id`) REFERENCES `promo_codes` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;

COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
