<?php
/**
 * فئة المنتج
 * تتعامل مع إدارة منتجات صالون الحلاقة والكوافير
 */

// منع الوصول المباشر للملف
if (!defined('BASEPATH')) {
    exit('لا يمكن الوصول المباشر لهذا الملف');
}

class Product {
    private $db;

    /**
     * إنشاء كائن من فئة المنتج
     * @param Database $db كائن قاعدة البيانات
     */
    public function __construct($db) {
        $this->db = $db;
    }

    /**
     * إضافة منتج جديد
     * @param array $productData بيانات المنتج
     * @return int|false معرف المنتج الجديد أو false إذا فشلت العملية
     */
    public function addProduct($data) {
        try {
            // Check if there's already an active transaction
            $transactionStartedHere = false;
            if (!$this->db->inTransaction()) {
                $this->db->beginTransaction();
                $transactionStartedHere = true;
            }

           // التحقق من عدم وجود منتج بنفس الاسم
           $this->db->prepare("SELECT id FROM products WHERE name = :name");
           $this->db->bind(':name', $data['name']);

           if ($this->db->fetch()) {
               throw new Exception('يوجد منتج مسجل بنفس الاسم');
           }

           // إضافة المنتج
           $this->db->prepare("INSERT INTO products (
               name, description, price, cost, category_id,
               is_for_sale, min_quantity, is_active, branch_id
           ) VALUES (
               :name, :description, :price, :cost, :category_id,
               :is_for_sale, :min_quantity, :is_active, :branch_id
           )");

           $this->db->bind(':name', $data['name']);
           $this->db->bind(':description', $data['description'] ?? null);
           $this->db->bind(':price', $data['price']);
           $this->db->bind(':cost', $data['cost'] ?? null);
           $this->db->bind(':category_id', $data['category_id'] ?? null);
           $this->db->bind(':is_for_sale', $data['is_for_sale'] ?? 1);
           $this->db->bind(':min_quantity', $data['min_quantity'] ?? 5);
           $this->db->bind(':is_active', $data['is_active'] ?? 1);
           $this->db->bind(':branch_id', $data['branch_id'] ?? null);

           $this->db->execute();
           $productId = $this->db->lastInsertId();

           return $productId;
        } catch (Exception $e) {
            // Only rollback if we started the transaction
            if ($transactionStartedHere && $this->db->inTransaction()) {
                $this->db->rollBack();
            }
            throw $e;
        }
    }
    // public function addProduct($productData) {
    //     try {
    //         $this->db->beginTransaction();

    //         // التحقق من عدم وجود منتج بنفس الاسم
    //         $this->db->prepare("SELECT id FROM products WHERE name = :name");
    //         $this->db->bind(':name', $productData['name']);

    //         if ($this->db->fetch()) {
    //             throw new Exception('يوجد منتج مسجل بنفس الاسم');
    //         }

    //         // إضافة المنتج
    //         $this->db->prepare("INSERT INTO products (
    //             name, description, price, cost, category_id,
    //             is_for_sale, min_quantity, is_active, branch_id
    //         ) VALUES (
    //             :name, :description, :price, :cost, :category_id,
    //             :is_for_sale, :min_quantity, :is_active, :branch_id
    //         )");

    //         $this->db->bind(':name', $productData['name']);
    //         $this->db->bind(':description', $productData['description'] ?? null);
    //         $this->db->bind(':price', $productData['price']);
    //         $this->db->bind(':cost', $productData['cost'] ?? null);
    //         $this->db->bind(':category_id', $productData['category_id'] ?? null);
    //         $this->db->bind(':is_for_sale', $productData['is_for_sale'] ?? 1);
    //         $this->db->bind(':min_quantity', $productData['min_quantity'] ?? 5);
    //         $this->db->bind(':is_active', $productData['is_active'] ?? 1);
    //         $this->db->bind(':branch_id', $productData['branch_id'] ?? null);

    //         $this->db->execute();
    //         $productId = $this->db->lastInsertId();

    //         // إضافة مخزون أولي للمنتج
    //         if (isset($productData['initial_quantity'])) {
    //             $inventoryModel = new Inventory($this->db);
    //             $inventoryModel->setProductStock(
    //                 $productId,
    //                 $productData['initial_quantity'],
    //                 $productData['branch_id'] ?? $_SESSION['user_branch_id'],
    //                 'مخزون أولي للمنتج'
    //             );
    //         }

    //         $this->db->commit();
    //         return (int)$productId;
    //     } catch (Exception $e) {
    //         $this->db->rollBack();
    //         error_log('خطأ أثناء إضافة المنتج: ' . $e->getMessage());
    //         throw $e;
    //     }
    // }

    /**
     * تحديث بيانات منتج
     * @param int $productId معرف المنتج
     * @param array $productData البيانات المحدثة
     * @return bool نجاح أو فشل العملية
     */
    public function updateProduct($productId, $productData) {
        try {
            // التحقق من وجود معاملة نشطة بالفعل
            $transactionStartedHere = false;
            if (!$this->db->inTransaction()) {
                $this->db->beginTransaction();
                $transactionStartedHere = true;
            }

            // التحقق من وجود المنتج
            $this->db->prepare("SELECT * FROM products WHERE id = :id");
            $this->db->bind(':id', $productId);
            $existingProduct = $this->db->fetch();

            if (!$existingProduct) {
                throw new Exception('المنتج غير موجود');
            }

            // التحقق من عدم وجود منتج آخر بنفس الاسم
            $this->db->prepare("SELECT id FROM products WHERE name = :name AND id != :id");
            $this->db->bind(':name', $productData['name']);
            $this->db->bind(':id', $productId);

            if ($this->db->fetch()) {
                throw new Exception('يوجد منتج آخر مسجل بنفس الاسم');
            }

            // تحديث بيانات المنتج
            $this->db->prepare("UPDATE products SET
                name = :name,
                description = :description,
                price = :price,
                cost = :cost,
                category_id = :category_id,
                is_for_sale = :is_for_sale,
                min_quantity = :min_quantity,
                is_active = :is_active,
                branch_id = :branch_id
                WHERE id = :id");

            $this->db->bind(':id', $productId);
            $this->db->bind(':name', $productData['name']);
            $this->db->bind(':description', $productData['description'] ?? $existingProduct['description']);
            $this->db->bind(':price', $productData['price']);
            $this->db->bind(':cost', $productData['cost'] ?? $existingProduct['cost']);
            $this->db->bind(':category_id', $productData['category_id'] ?? $existingProduct['category_id']);
            $this->db->bind(':is_for_sale', $productData['is_for_sale'] ?? $existingProduct['is_for_sale']);
            $this->db->bind(':min_quantity', $productData['min_quantity'] ?? $existingProduct['min_quantity']);
            $this->db->bind(':is_active', $productData['is_active'] ?? $existingProduct['is_active']);
            $this->db->bind(':branch_id', $productData['branch_id'] ?? $existingProduct['branch_id']);

            $this->db->execute();

            // فقط إنهاء المعاملة إذا بدأناها هنا
            if ($transactionStartedHere) {
                $this->db->commit();
            }

            return true;
        } catch (Exception $e) {
            // فقط التراجع عن المعاملة إذا بدأناها هنا
            if (isset($transactionStartedHere) && $transactionStartedHere && $this->db->inTransaction()) {
                $this->db->rollBack();
            }
            error_log('خطأ أثناء تحديث بيانات المنتج: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * حذف منتج
     * @param int $productId معرف المنتج
     * @return bool نجاح أو فشل العملية
     */
    public function deleteProduct($productId) {
        try {
            $this->db->beginTransaction();

            // التحقق من عدم وجود فواتير مرتبطة بالمنتج
            $this->db->prepare("SELECT COUNT(*) FROM invoice_items
                              WHERE item_type = 'product' AND item_id = :product_id");
            $this->db->bind(':product_id', $productId);
            if ($this->db->fetchColumn() > 0) {
                throw new Exception('لا يمكن حذف المنتج لوجود فواتير مرتبطة به');
            }

            // التحقق من عدم وجود مخزون للمنتج
            $this->db->prepare("SELECT COUNT(*) FROM inventory
                              WHERE product_id = :product_id AND quantity > 0");
            $this->db->bind(':product_id', $productId);
            if ($this->db->fetchColumn() > 0) {
                throw new Exception('لا يمكن حذف المنتج لوجود مخزون');
            }

            // حذف المخزون المرتبط
            $this->db->prepare("DELETE FROM inventory WHERE product_id = :product_id");
            $this->db->bind(':product_id', $productId);
            $this->db->execute();

            // حذف حركات المخزون المرتبطة
            $this->db->prepare("DELETE FROM inventory_transactions WHERE product_id = :product_id");
            $this->db->bind(':product_id', $productId);
            $this->db->execute();

            // حذف المنتج
            $this->db->prepare("DELETE FROM products WHERE id = :id");
            $this->db->bind(':id', $productId);
            $this->db->execute();

            $this->db->commit();
            return true;
        } catch (Exception $e) {
            $this->db->rollBack();
            error_log('خطأ أثناء حذف المنتج: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * الحصول على بيانات منتج بواسطة المعرف
     * @param int $productId معرف المنتج
     * @return array|false بيانات المنتج أو false إذا لم يتم العثور عليه
     */
    public function getProductById($productId) {
        try {
            $this->db->prepare("SELECT p.*,
                              pc.name as category_name,
                              b.name as branch_name,
                              COALESCE(i.quantity, 0) as current_stock
                              FROM products p
                              LEFT JOIN product_categories pc ON p.category_id = pc.id
                              LEFT JOIN branches b ON p.branch_id = b.id
                              LEFT JOIN inventory i ON p.id = i.product_id
                              WHERE p.id = :id");
            $this->db->bind(':id', $productId);
            $product = $this->db->fetch();

            return $product;
        } catch (Exception $e) {
            error_log('خطأ أثناء استرجاع بيانات المنتج: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * الحصول على قائمة المنتجات
     * @param array $filters فلاتر البحث (اختياري)
     * @return array قائمة المنتجات
     */
    public function getProducts($filters = []) {
        try {
            $sql = "SELECT p.*,
                           pc.name as category_name,
                           b.name as branch_name,
                           COALESCE(i.quantity, 0) as current_stock
                    FROM products p
                    LEFT JOIN product_categories pc ON p.category_id = pc.id
                    LEFT JOIN branches b ON p.branch_id = b.id
                    LEFT JOIN inventory i ON p.id = i.product_id";

            // إضافة شرط الفرع للانضمام مع جدول المخزون
            if (!empty($filters['branch_id'])) {
                $sql .= " AND i.branch_id = :inventory_branch_id";
            }

            $whereConditions = [];
            $bindings = [];

            // تطبيق الفلاتر
            if (!empty($filters['search'])) {
                $whereConditions[] = "(p.name LIKE :search OR p.description LIKE :search)";
                $bindings[':search'] = '%' . $filters['search'] . '%';
            }

            // إضافة شرط الفرع إذا كان محدداً
            if (!empty($filters['branch_id'])) {
                $whereConditions[] = "(p.branch_id = :branch_id OR p.branch_id IS NULL)";
                $bindings[':branch_id'] = $filters['branch_id'];
                $bindings[':inventory_branch_id'] = $filters['branch_id'];
            }

            // إضافة شروط البحث إلى الاستعلام
            if (!empty($whereConditions)) {
                $sql .= " WHERE " . implode(' AND ', $whereConditions);
            }

            // إضافة ترتيب النتائج
            $sql .= " GROUP BY p.id ORDER BY p.name ASC";

            $this->db->prepare($sql);

            // ربط المتغيرات
            foreach ($bindings as $param => $value) {
                $this->db->bind($param, $value);
            }

            // تنفيذ الاستعلام
            return $this->db->fetchAll();
        } catch (Exception $e) {
            error_log('خطأ في الحصول على قائمة المنتجات: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * إضافة فئة منتجات جديدة
     * @param array $categoryData بيانات الفئة
     * @return int|false معرف الفئة الجديدة أو false إذا فشلت العملية
     */
    public function addProductCategory($categoryData) {
        try {
            // التحقق من عدم وجود فئة بنفس الاسم
            $this->db->prepare("SELECT id FROM product_categories WHERE name = :name");
            $this->db->bind(':name', $categoryData['name']);

            if ($this->db->fetch()) {
                throw new Exception('توجد فئة منتجات بنفس الاسم بالفعل');
            }

            $this->db->prepare("INSERT INTO product_categories (name, description)
                              VALUES (:name, :description)");
            $this->db->bind(':name', $categoryData['name']);
            $this->db->bind(':description', $categoryData['description'] ?? null);

            $this->db->execute();
            return (int)$this->db->lastInsertId();
        } catch (Exception $e) {
            error_log('خطأ أثناء إضافة فئة المنتجات: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * تحديث بيانات فئة منتجات
     * @param int $categoryId معرف الفئة
     * @param array $categoryData البيانات المحدثة
     * @return bool نجاح أو فشل العملية
     */
    public function updateProductCategory($categoryId, $categoryData) {
        try {
            // التحقق من وجود الفئة
            $this->db->prepare("SELECT id FROM product_categories WHERE id = :id");
            $this->db->bind(':id', $categoryId);

            if (!$this->db->fetch()) {
                throw new Exception('فئة المنتجات غير موجودة');
            }

            // التحقق من عدم وجود فئة أخرى بنفس الاسم
            $this->db->prepare("SELECT id FROM product_categories WHERE name = :name AND id != :id");
            $this->db->bind(':name', $categoryData['name']);
            $this->db->bind(':id', $categoryId);

            if ($this->db->fetch()) {
                throw new Exception('توجد فئة أخرى بنفس الاسم');
            }

            $this->db->prepare("UPDATE product_categories
                              SET name = :name,
                                  description = :description
                              WHERE id = :id");

            $this->db->bind(':name', $categoryData['name']);
            $this->db->bind(':description', $categoryData['description'] ?? null);
            $this->db->bind(':id', $categoryId);

            return $this->db->execute();
        } catch (Exception $e) {
            error_log('خطأ أثناء تحديث بيانات فئة المنتجات: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * حذف فئة منتجات
     * @param int $categoryId معرف الفئة
     * @return bool نجاح أو فشل العملية
     */
    public function deleteProductCategory($categoryId) {
        try {
            // التحقق من عدم وجود منتجات مرتبطة بالفئة
            $this->db->prepare("SELECT COUNT(*) FROM products WHERE category_id = :category_id");
            $this->db->bind(':category_id', $categoryId);

            if ($this->db->fetchColumn() > 0) {
                throw new Exception('لا يمكن حذف الفئة لوجود منتجات مرتبطة بها');
            }

            $this->db->prepare("DELETE FROM product_categories WHERE id = :id");
            $this->db->bind(':id', $categoryId);

            return $this->db->execute();
        } catch (Exception $e) {
            error_log('خطأ أثناء حذف فئة المنتجات: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * الحصول على بيانات فئة منتجات بواسطة المعرف
     * @param int $categoryId معرف الفئة
     * @return array|false بيانات الفئة أو false إذا لم يتم العثور عليها
     */
    public function getProductCategoryById($categoryId) {
        try {
            $this->db->prepare("SELECT * FROM product_categories WHERE id = :id");
            $this->db->bind(':id', $categoryId);

            return $this->db->fetch();
        } catch (Exception $e) {
            error_log('خطأ أثناء استرجاع بيانات فئة المنتجات: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * الحصول على قائمة فئات المنتجات مع عدد المنتجات المرتبطة بكل فئة
     * @return array قائمة الفئات
     */
    public function getProductCategories() {
        try {
            $this->db->prepare("SELECT pc.*, COUNT(p.id) as product_count
                               FROM product_categories pc
                               LEFT JOIN products p ON pc.id = p.category_id
                               GROUP BY pc.id
                               ORDER BY pc.name ASC");
            return $this->db->fetchAll();
        } catch (Exception $e) {
            error_log('خطأ أثناء استرجاع قائمة فئات المنتجات: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * حساب عدد المنتجات
     * @param array $filters فلاتر البحث (اختياري)
     * @return int عدد المنتجات
     */
    public function getProductsCount($filters = []) {
        try {
            $sql = "SELECT COUNT(*) FROM products p";

            $whereConditions = [];
            $bindings = [];

            // تطبيق الفلاتر
            if (!empty($filters['search'])) {
                $whereConditions[] = "(p.name LIKE :search OR p.description LIKE :search)";
                $bindings[':search'] = '%' . $filters['search'] . '%';
            }

            if (!empty($filters['category_id'])) {
                $whereConditions[] = "p.category_id = :category_id";
                $bindings[':category_id'] = $filters['category_id'];
            }

            if (!empty($filters['branch_id'])) {
                $whereConditions[] = "p.branch_id = :branch_id";
                $bindings[':branch_id'] = $filters['branch_id'];
            }

            if (isset($filters['is_for_sale'])) {
                $whereConditions[] = "p.is_for_sale = :is_for_sale";
                $bindings[':is_for_sale'] = $filters['is_for_sale'];
            }

            if (isset($filters['is_active'])) {
                $whereConditions[] = "p.is_active = :is_active";
                $bindings[':is_active'] = $filters['is_active'];
            }

            // إضافة شروط WHERE إذا وجدت
            if (!empty($whereConditions)) {
                $sql .= " WHERE " . implode(' AND ', $whereConditions);
            }

            $this->db->prepare($sql);

            // ربط القيم
            foreach ($bindings as $param => $value) {
                $this->db->bind($param, $value);
            }

            return $this->db->fetchColumn();
        } catch (Exception $e) {
            error_log('خطأ أثناء حساب عدد المنتجات: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * إنشاء تقرير المنتجات
     * @param array $filters فلاتر البحث (اختياري)
     * @return array تقرير المنتجات
     */
    public function generateProductReport($filters = []) {
        try {
            // تقرير شامل للمنتجات
            $report = [
                'filters' => $filters,
                'summary' => [],
                'categories' => [],
                'products' => []
            ];

            // إجمالي المنتجات
            $report['summary']['total_products'] = $this->getProductsCount($filters);

            // قيمة المخزون
            $inventoryModel = new Inventory($this->db);
            $inventoryValue = $inventoryModel->calculateInventoryValue(
                $filters['branch_id'] ?? null
            );

            $report['summary']['total_cost'] = $inventoryValue['total_cost'];
            $report['summary']['total_value'] = $inventoryValue['total_value'];
            $report['summary']['expected_profit'] = $inventoryValue['expected_profit'];

            // تصنيف المنتجات حسب الفئة
            $report['categories'] = $this->getProductCategoriesSummary($filters);

            // المنتجات منخفضة المخزون
            $report['low_stock_products'] = $inventoryModel->getLowStockProducts(
                $filters['branch_id'] ?? null
            );

            // المنتجات الأكثر مبيعًا
            $report['top_selling_products'] = $inventoryModel->getTopSellingProducts(
                $filters,
                10
            );

            // قائمة المنتجات
            $report['products'] = $this->getProducts($filters);

            return $report;
        } catch (Exception $e) {
            error_log('خطأ أثناء إنشاء تقرير المنتجات: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * الحصول على ملخص الفئات
     * @param array $filters فلاتر البحث (اختياري)
     * @return array ملخص الفئات
     */
    private function getProductCategoriesSummary($filters = []) {
        try {
            $sql = "SELECT pc.id, pc.name,
                           COUNT(p.id) as product_count,
                           SUM(i.quantity * p.cost) as total_cost,
                           SUM(i.quantity * p.price) as total_value
                    FROM product_categories pc
                    LEFT JOIN products p ON pc.id = p.category_id
                    LEFT JOIN inventory i ON p.id = i.product_id";

            $whereConditions = [];
            $bindings = [];

            if (!empty($filters['branch_id'])) {
                $whereConditions[] = "i.branch_id = :branch_id";
                $bindings[':branch_id'] = $filters['branch_id'];
            }

            // إضافة شروط WHERE إذا وجدت
            if (!empty($whereConditions)) {
                $sql .= " WHERE " . implode(' AND ', $whereConditions);
            }

            $sql .= " GROUP BY pc.id, pc.name ORDER BY product_count DESC";

            $this->db->prepare($sql);

            // ربط القيم
            foreach ($bindings as $param => $value) {
                $this->db->bind($param, $value);
            }

            return $this->db->fetchAll();
        } catch (Exception $e) {
            error_log('خطأ أثناء استرجاع ملخص فئات المنتجات: ' . $e->getMessage());
            throw $e;
        }
    }
}
