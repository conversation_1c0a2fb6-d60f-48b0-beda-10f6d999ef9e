/**
 * Cliente de WhatsApp para el lado del cliente
 * Maneja el envío de mensajes de WhatsApp directamente desde el navegador
 */

// Objeto principal del cliente de WhatsApp
const WhatsAppClient = {
    // URL del servidor local (configurable)
    serverUrl: 'http://localhost:3000',

    // Variable para almacenar si se ha mostrado ya el mensaje de error de conexión
    hasShownConnectionError: false,

    // Estado de la conexión
    isConnected: false,

    // Estado de disponibilidad del servidor
    isServerAvailable: false,

    // Contador de intentos de conexión
    connectionAttempts: 0,

    // Máximo de intentos de conexión antes de usar el modo de respaldo
    maxConnectionAttempts: 2,

    // Opciones de configuración
    options: {
        debug: true,
        autoReconnect: true,
        reconnectInterval: 5000, // 5 segundos
        maxReconnectAttempts: 3
    },

    /**
     * Inicializar el cliente de WhatsApp
     * @param {Object} options Opciones de configuración
     */
    init: function(options = {}) {
        // Combinar opciones proporcionadas con las predeterminadas
        this.options = { ...this.options, ...options };

        // Verificar la conexión al servidor local
        this.checkConnection();

        // Registrar eventos
        this._registerEvents();

        // Registrar en la consola si el modo de depuración está activado
        if (this.options.debug) {
            console.log('Cliente de WhatsApp inicializado');
        }
    },

    /**
     * Verificar la conexión al servidor local
     * @returns {Promise<boolean>} Estado de la conexión
     */
    checkConnection: function() {
        return new Promise((resolve, reject) => {
            try {
                // Incrementar el contador de intentos
                this.connectionAttempts++;

                // Si ya hemos intentado conectar varias veces y el servidor no está disponible,
                // mostrar un mensaje de error
                if (this.connectionAttempts > this.maxConnectionAttempts && !this.isServerAvailable) {
                    if (this.options.debug) {
                        console.warn(`Se han realizado ${this.connectionAttempts} intentos de conexión sin éxito.`);
                    }

                    this.isConnected = false;
                    this._triggerEvent('connectionChange', { isConnected: false });

                    if (this.options.debug) {
                        console.log('No se pudo conectar al servidor local de WhatsApp.');
                    }

                    resolve(false);
                    return;
                }

                // Realizar una solicitud al servidor local para verificar la conexión
                const xhr = new XMLHttpRequest();
                xhr.open('GET', `${this.serverUrl}/status`, true);
                xhr.setRequestHeader('Content-Type', 'application/json');
                xhr.responseType = 'json';
                xhr.timeout = 5000; // Timeout de 5 segundos para evitar esperas largas

                xhr.onload = () => {
                    if (xhr.status === 200) {
                        // El servidor está disponible
                        this.isServerAvailable = true;

                        const data = xhr.response;
                        this.isConnected = data && data.isLoggedIn || false;

                        // Reiniciar el contador de intentos si la conexión fue exitosa
                        this.connectionAttempts = 0;

                        if (this.options.debug) {
                            console.log(`Estado de conexión a WhatsApp: ${this.isConnected ? 'Conectado' : 'No conectado'}`);
                        }

                        // Disparar evento de cambio de estado
                        this._triggerEvent('connectionChange', { isConnected: this.isConnected });

                        resolve(this.isConnected);
                    } else {
                        this.isConnected = false;
                        const errorMsg = `Error de conexión: ${xhr.status}`;

                        if (this.options.debug) {
                            console.error(errorMsg);
                        }

                        // Disparar evento de error
                        this._triggerEvent('error', { error: new Error(errorMsg) });

                        // Verificar si debemos usar el modo de respaldo
                        this._checkFallbackMode(resolve, reject, errorMsg);
                    }
                };

                xhr.ontimeout = () => {
                    this.isConnected = false;
                    this.isServerAvailable = false;
                    const errorMsg = 'Tiempo de espera agotado al conectar con el servidor de WhatsApp';

                    if (this.options.debug) {
                        console.error(errorMsg);
                    }

                    // Disparar evento de error
                    this._triggerEvent('error', { error: new Error(errorMsg) });

                    // Verificar si debemos usar el modo de respaldo
                    this._checkFallbackMode(resolve, reject, errorMsg);
                };

                xhr.onerror = () => {
                    this.isConnected = false;
                    this.isServerAvailable = false;
                    const errorMsg = 'Error de red al conectar con el servidor de WhatsApp';

                    if (this.options.debug) {
                        console.error(errorMsg);
                    }

                    // Disparar evento de error
                    this._triggerEvent('error', { error: new Error(errorMsg) });

                    // Verificar si debemos usar el modo de respaldo
                    this._checkFallbackMode(resolve, reject, errorMsg);
                };

                xhr.send();
            } catch (error) {
                this.isConnected = false;
                this.isServerAvailable = false;

                if (this.options.debug) {
                    console.error('Error al verificar la conexión a WhatsApp:', error);
                }

                // Disparar evento de error
                this._triggerEvent('error', { error });

                // Verificar si debemos usar el modo de respaldo
                this._checkFallbackMode(resolve, reject, error.message);
            }
        });
    },

    /**
     * Manejar errores de conexión
     * @private
     * @param {Function} resolve Función para resolver la promesa
     * @param {Function} reject Función para rechazar la promesa
     * @param {string} errorMsg Mensaje de error
     */
    _checkFallbackMode: function(resolve, reject, errorMsg) {
        // Intentar reconectar si está habilitado
        if (this.options.autoReconnect) {
            this._attemptReconnect();
        }

        reject(new Error(errorMsg));
    },

    /**
     * Enviar un mensaje de WhatsApp
     * @param {string} phone Número de teléfono del destinatario
     * @param {string} message Mensaje a enviar
     * @returns {Promise<Object>} Resultado del envío
     */
    sendMessage: function(phone, message) {
        return new Promise((resolve, reject) => {
            // Verificar si el cliente está conectado
            if (!this.isConnected) {
                // Intentar conectar primero
                this.checkConnection()
                    .then(isConnected => {
                        if (isConnected) {
                            {
                                // Si se conectó correctamente, intentar enviar el mensaje desde el cliente
                                this._sendMessageRequest(phone, message)
                                    .then(resolve)
                                    .catch(error => {
                                        // Mostrar mensaje de error en la consola
                                        console.error('Error al enviar mensaje a WhatsApp:', error);

                                        {
                                            // Mostrar alerta al usuario solo una vez
                                            if (!this.hasShownConnectionError) {
                                                this.hasShownConnectionError = true;
                                                alert('Error al enviar mensaje a WhatsApp. Asegúrate de que el servidor local esté en ejecución y que WhatsApp Web esté abierto y conectado.');
                                            }

                                            reject(error);
                                        }
                                    });
                            }
                        } else {
                            {
                                // Mostrar alerta al usuario solo una vez
                                if (!this.hasShownConnectionError) {
                                    this.hasShownConnectionError = true;
                                    alert('No se pudo conectar al servidor de WhatsApp. Asegúrate de que el servidor local esté en ejecución.');
                                }

                                reject(new Error('No se pudo conectar al servidor de WhatsApp'));
                            }
                        }
                    })
                    .catch(error => {
                        // Mostrar mensaje de error en la consola
                        console.error('Error al verificar la conexión con WhatsApp:', error);

                        {
                            // Mostrar alerta al usuario solo una vez
                            if (!this.hasShownConnectionError) {
                                this.hasShownConnectionError = true;
                                alert('Error al conectar con el servidor de WhatsApp. Asegúrate de que el servidor local esté en ejecución.');
                            }

                            reject(error);
                        }
                    });
            } else {
                // Si ya está conectado, enviar el mensaje directamente
                {
                    // Enviar el mensaje desde el cliente
                    this._sendMessageRequest(phone, message)
                        .then(resolve)
                        .catch(error => {
                            // Mostrar mensaje de error en la consola
                            console.error('Error al enviar mensaje a WhatsApp:', error);

                            {
                                // Mostrar alerta al usuario solo una vez
                                if (!this.hasShownConnectionError) {
                                    this.hasShownConnectionError = true;
                                    alert('Error al enviar mensaje a WhatsApp. Asegúrate de que WhatsApp Web esté abierto y conectado.');
                                }

                                reject(error);
                            }
                        });
                }
            }
        });
    },



    /**
     * Enviar una solicitud para enviar un mensaje
     * @private
     * @param {string} phone Número de teléfono del destinatario
     * @param {string} message Mensaje a enviar
     * @returns {Promise<Object>} Resultado del envío
     */
    _sendMessageRequest: function(phone, message) {
        return new Promise((resolve, reject) => {
            try {
                // Formatear el número de teléfono (eliminar espacios, guiones, etc.)
                phone = this._formatPhoneNumber(phone);

                // Realizar una solicitud al servidor local para enviar el mensaje
                const xhr = new XMLHttpRequest();
                xhr.open('POST', `${this.serverUrl}/send`, true);
                xhr.setRequestHeader('Content-Type', 'application/json');
                xhr.responseType = 'json';

                xhr.onload = () => {
                    if (xhr.status === 200) {
                        const data = xhr.response;

                        if (this.options.debug) {
                            console.log(`Mensaje enviado a ${phone}:`, data);
                        }

                        // Disparar evento de mensaje enviado
                        this._triggerEvent('messageSent', { phone, message, response: data });

                        resolve(data);
                    } else {
                        const errorMsg = `Error al enviar mensaje: ${xhr.status}`;

                        if (this.options.debug) {
                            console.error(`Error al enviar mensaje a ${phone}: ${errorMsg}`);
                        }

                        // Disparar evento de error
                        this._triggerEvent('error', { error: new Error(errorMsg), phone, message });

                        reject(new Error(errorMsg));
                    }
                };

                xhr.onerror = () => {
                    const errorMsg = 'Error de red al enviar mensaje a WhatsApp';

                    if (this.options.debug) {
                        console.error(`Error al enviar mensaje a ${phone}: ${errorMsg}`);
                    }

                    // Disparar evento de error
                    this._triggerEvent('error', { error: new Error(errorMsg), phone, message });

                    reject(new Error(errorMsg));
                };

                // Enviar los datos
                const sendData = {
                    phone: phone,
                    message: message
                };

                xhr.send(JSON.stringify(sendData));
            } catch (error) {
                if (this.options.debug) {
                    console.error(`Error al enviar mensaje a ${phone}:`, error);
                }

                // Disparar evento de error
                this._triggerEvent('error', { error, phone, message });

                reject(error);
            }
        });
    },

    /**
     * Formatear un número de teléfono para WhatsApp
     * @private
     * @param {string} phone Número de teléfono a formatear
     * @returns {string} Número de teléfono formateado
     */
    _formatPhoneNumber: function(phone) {
        // Eliminar todos los caracteres no numéricos
        phone = phone.replace(/\D/g, '');

        // Eliminar el primer 0 si existe
        if (phone.startsWith('0')) {
            phone = phone.substring(1);
        }

        // Agregar el código de país si no existe
        if (!phone.startsWith('20')) {
            phone = '20' + phone;
        }

        return phone;
    },

    /**
     * Intentar reconectar al servidor
     * @private
     */
    _attemptReconnect: function() {
        let attempts = 0;

        const reconnect = () => {
            if (attempts >= this.options.maxReconnectAttempts) {
                if (this.options.debug) {
                    console.error(`Se alcanzó el número máximo de intentos de reconexión (${this.options.maxReconnectAttempts})`);
                }
                return;
            }

            attempts++;

            if (this.options.debug) {
                console.log(`Intento de reconexión ${attempts}/${this.options.maxReconnectAttempts}...`);
            }

            this.checkConnection()
                .then(isConnected => {
                    if (!isConnected) {
                        // Si no se pudo conectar, intentar de nuevo después del intervalo
                        setTimeout(reconnect, this.options.reconnectInterval);
                    } else {
                        if (this.options.debug) {
                            console.log('Reconexión exitosa');
                        }
                    }
                })
                .catch(() => {
                    // Si hubo un error, intentar de nuevo después del intervalo
                    setTimeout(reconnect, this.options.reconnectInterval);
                });
        };

        // Iniciar el primer intento de reconexión después del intervalo
        setTimeout(reconnect, this.options.reconnectInterval);
    },

    // Eventos
    _events: {},

    /**
     * Registrar eventos predeterminados
     * @private
     */
    _registerEvents: function() {
        // No hay eventos predeterminados por ahora
    },

    /**
     * Registrar un manejador de eventos
     * @param {string} event Nombre del evento
     * @param {Function} handler Función manejadora
     */
    on: function(event, handler) {
        if (!this._events[event]) {
            this._events[event] = [];
        }

        this._events[event].push(handler);
    },

    /**
     * Eliminar un manejador de eventos
     * @param {string} event Nombre del evento
     * @param {Function} handler Función manejadora a eliminar
     */
    off: function(event, handler) {
        if (!this._events[event]) {
            return;
        }

        this._events[event] = this._events[event].filter(h => h !== handler);
    },

    /**
     * Disparar un evento
     * @private
     * @param {string} event Nombre del evento
     * @param {Object} data Datos del evento
     */
    _triggerEvent: function(event, data) {
        if (!this._events[event]) {
            return;
        }

        this._events[event].forEach(handler => {
            try {
                handler(data);
            } catch (error) {
                console.error(`Error en el manejador de eventos para '${event}':`, error);
            }
        });
    }
};

// Inicializar el cliente de WhatsApp cuando el documento esté listo
document.addEventListener('DOMContentLoaded', function() {
    WhatsAppClient.init();
});
