<?php
/**
 * صفحة إدارة الموظفين
 */

// تعريف ثابت لمنع الوصول المباشر للملفات المرتبطة
define('BASEPATH', true);

// استدعاء ملفات التهيئة
require_once '../../config/init.php';

// التحقق من تسجيل الدخول
requireLogin();

// التحقق من وجود صلاحية عرض الموظفين
requirePermission('employees_view');

// عنوان الصفحة
$pageTitle = 'إدارة الموظفين';

// استدعاء رأس الصفحة
include '../../includes/templates/header.php';

// إنشاء كائن الموظفين
$employeeModel = new Employee($db);

// فلتر الفرع الحالي
$branchId = $_SESSION['user_branch_id'] ?? null;

// الحصول على رمز العملة واسمها من الإعدادات
$settingsModel = new Settings($db);
$currencySymbol = $settingsModel->get('system_currency_symbol', 'ر.س');
$currencyName = $settingsModel->get('system_currency', 'ريال سعودي');

// استرجاع الوظائف المتاحة
$positions = [
    'مدير', 'كاشير', 'حلاق', 'مصفف شعر', 'فني تجميل', 'مساعد', 'أخرى'
];

// استرجاع أنواع الرواتب
$salaryTypes = [
    'fixed' => 'ثابت',
    'percentage' => 'نسبة',
    'both' => 'ثابت ونسبة'
];
?>

<!-- المحتوى الرئيسي للصفحة -->
<div class="container-fluid">
    <!-- رأس الصفحة -->
    <div class="card border-0 shadow-sm mb-4">
        <div class="card-body d-flex justify-content-between align-items-center">
            <h5 class="mb-0">
                <i class="fas fa-user-tie me-2"></i> قائمة الموظفين
            </h5>
            <div>
                <?php if (hasPermission('employees_salaries')): ?>
                <a href="salaries.php" class="btn btn-info me-2">
                    <i class="fas fa-money-bill-wave me-1"></i> الرواتب
                </a>
                <?php endif; ?>

                <?php if (hasPermission('employees_add')): ?>
                <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addEmployeeModal">
                    <i class="fas fa-plus me-1"></i> إضافة موظف
                </button>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- بطاقة البحث والفلترة -->
    <div class="card border-0 shadow-sm mb-4">
        <div class="card-body">
            <form id="searchForm" class="row align-items-center">
                <div class="col-md-3 mb-3 mb-md-0">
                    <label for="search_term" class="form-label">بحث</label>
                    <input type="text" id="search_term" class="form-control" placeholder="بحث بالاسم أو رقم الهاتف...">
                </div>

                <div class="col-md-2 mb-3 mb-md-0">
                    <label for="position_filter" class="form-label">الوظيفة</label>
                    <select id="position_filter" class="form-select">
                        <option value="">كل الوظائف</option>
                        <?php foreach($positions as $position): ?>
                            <option value="<?php echo $position; ?>"><?php echo $position; ?></option>
                        <?php endforeach; ?>
                    </select>
                </div>

                <?php if (isAdmin()): ?>
                <div class="col-md-2 mb-3 mb-md-0">
                    <label for="branch_filter" class="form-label">الفرع</label>
                    <select id="branch_filter" class="form-select">
                        <option value="">كل الفروع</option>
                        <?php
                        // استرجاع قائمة الفروع
                        $branchModel = new Branch($db);
                        $branches = $branchModel->getBranches(['is_active' => 1]);
                        foreach ($branches as $branch) {
                            echo '<option value="' . $branch['id'] . '">' . htmlspecialchars($branch['name']) . '</option>';
                        }
                        ?>
                    </select>
                </div>
                <?php endif; ?>

                <div class="col-md-2 mb-3 mb-md-0">
                    <label for="status_filter" class="form-label">الحالة</label>
                    <select id="status_filter" class="form-select">
                        <option value="">الكل</option>
                        <option value="1" selected>نشط</option>
                        <option value="0">غير نشط</option>
                    </select>
                </div>

                <div class="col-md-2 mb-3 mb-md-0">
                    <label class="form-label d-block">&nbsp;</label>
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="fas fa-search me-1"></i> بحث
                    </button>
                </div>
                <div class="col-md-1">
                    <label class="form-label d-block">&nbsp;</label>
                    <button type="reset" class="btn btn-secondary w-100">
                        <i class="fas fa-redo me-1"></i> إعادة
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- جدول الموظفين -->
    <div class="card border-0 shadow-sm">
        <div class="card-body">
            <div class="table-responsive">
                <table id="employeesTable" class="table table-hover">
                    <thead class="table-light">
                        <tr>
                            <th>#</th>
                            <th>الاسم</th>
                            <th>الوظيفة</th>
                            <th>رقم الهاتف</th>
                            <th>نوع الراتب</th>
                            <th>الراتب الثابت</th>
                            <th>النسبة</th>
                            <th>الفرع</th>
                            <th>الحالة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <!-- سيتم تعبئة البيانات عبر AJAX -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- مودال إضافة موظف جديد -->
<div class="modal fade" id="addEmployeeModal" tabindex="-1" aria-labelledby="addEmployeeModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addEmployeeModalLabel">إضافة موظف جديد</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="إغلاق"></button>
            </div>
            <div class="modal-body">
                <form id="addEmployeeForm">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="name" class="form-label">الاسم <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="name" name="name" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="position" class="form-label">الوظيفة <span class="text-danger">*</span></label>
                            <select class="form-select" id="position" name="position" required>
                                <option value="">اختر الوظيفة</option>
                                <?php foreach($positions as $position): ?>
                                    <option value="<?php echo $position; ?>"><?php echo $position; ?></option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="phone" class="form-label">رقم الهاتف <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="phone" name="phone" dir="ltr" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="email" class="form-label">البريد الإلكتروني</label>
                            <input type="email" class="form-control" id="email" name="email" dir="ltr">
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label for="salary_type" class="form-label">نوع الراتب <span class="text-danger">*</span></label>
                            <select class="form-select" id="salary_type" name="salary_type" required>
                                <?php foreach($salaryTypes as $value => $label): ?>
                                    <option value="<?php echo $value; ?>"><?php echo $label; ?></option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label for="fixed_salary" class="form-label">الراتب الثابت</label>
                            <input type="number" class="form-control" id="fixed_salary" name="fixed_salary" value="0" min="0" step="0.01">
                        </div>
                        <div class="col-md-4 mb-3">
                            <label for="commission_percentage" class="form-label">نسبة العمولة (%)</label>
                            <input type="number" class="form-control" id="commission_percentage" name="commission_percentage" value="0" min="0" max="100" step="0.01">
                        </div>
                    </div>

                    <?php if (isAdmin()): ?>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="branch_id" class="form-label">الفرع</label>
                            <select class="form-select" id="branch_id" name="branch_id">
                                <?php
                                // استرجاع قائمة الفروع
                                $branchModel = new Branch($db);
                                $branches = $branchModel->getBranches(['is_active' => 1]);
                                foreach ($branches as $branch) {
                                    $selected = ($_SESSION['user_branch_id'] == $branch['id']) ? 'selected' : '';
                                    echo '<option value="' . $branch['id'] . '" ' . $selected . '>' . htmlspecialchars($branch['name']) . '</option>';
                                }
                                ?>
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="is_active" class="form-label">الحالة</label>
                            <select class="form-select" id="is_active" name="is_active">
                                <option value="1" selected>نشط</option>
                                <option value="0">غير نشط</option>
                            </select>
                        </div>
                    </div>
                    <?php else: ?>
                    <input type="hidden" name="branch_id" value="<?php echo $_SESSION['user_branch_id']; ?>">
                    <input type="hidden" name="is_active" value="1">
                    <?php endif; ?>

                    <hr>

                    <div class="row">
                        <div class="col-md-12 mb-3">
                            <label for="services" class="form-label">الخدمات التي يقدمها</label>
                            <select class="form-select select2" id="services" name="services[]" multiple>
                                <?php
                                // استرجاع جميع الخدمات
                                $serviceModel = new Service($db);
                                $services = $serviceModel->getServices(['is_active' => 1]);

                                foreach ($services as $service) {
                                    echo '<option value="' . $service['id'] . '">' . htmlspecialchars($service['name']) . '</option>';
                                }
                                ?>
                            </select>
                        </div>
                    </div>

                    <hr>

                    <div class="form-check mb-3">
                        <input class="form-check-input" type="checkbox" id="create_user" name="create_user" value="1">
                        <label class="form-check-label" for="create_user">
                            إنشاء حساب مستخدم للموظف
                        </label>
                    </div>

                    <div id="userAccountSection" style="display: none;">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="username" class="form-label">اسم المستخدم</label>
                                <input type="text" class="form-control" id="username" name="username">
                                <div class="form-text">سيتم استخدام الاسم الأول ورقم الهاتف إذا تم تركه فارغًا</div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="password" class="form-label">كلمة المرور</label>
                                <input type="text" class="form-control" id="password" name="password">
                                <div class="form-text">سيتم إنشاء كلمة مرور عشوائية إذا تم تركها فارغة</div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" id="submitAddEmployee">حفظ</button>
            </div>
        </div>
    </div>
</div>

<!-- مودال تعديل موظف -->
<div class="modal fade" id="editEmployeeModal" tabindex="-1" aria-labelledby="editEmployeeModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="editEmployeeModalLabel">تعديل بيانات الموظف</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="إغلاق"></button>
            </div>
            <div class="modal-body">
                <form id="editEmployeeForm">
                    <input type="hidden" id="edit_employee_id" name="employee_id">

                    <!-- بطاقات المعلومات -->
                    <div class="card mb-3 border-0 shadow-sm">
                        <div class="card-header bg-light">
                            <h6 class="mb-0"><i class="fas fa-user-edit me-2"></i>المعلومات الأساسية</h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="edit_name" class="form-label">الاسم <span class="text-danger">*</span></label>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="fas fa-user"></i></span>
                                        <input type="text" class="form-control" id="edit_name" name="name" required>
                                    </div>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="edit_position" class="form-label">الوظيفة <span class="text-danger">*</span></label>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="fas fa-briefcase"></i></span>
                                        <select class="form-select" id="edit_position" name="position" required>
                                            <option value="">اختر الوظيفة</option>
                                            <?php foreach($positions as $position): ?>
                                                <option value="<?php echo $position; ?>"><?php echo $position; ?></option>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="edit_phone" class="form-label">رقم الهاتف <span class="text-danger">*</span></label>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="fas fa-phone"></i></span>
                                        <input type="text" class="form-control" id="edit_phone" name="phone" dir="ltr" required>
                                    </div>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="edit_email" class="form-label">البريد الإلكتروني</label>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="fas fa-envelope"></i></span>
                                        <input type="email" class="form-control" id="edit_email" name="email" dir="ltr">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="card mb-3 border-0 shadow-sm">
                        <div class="card-header bg-light">
                            <h6 class="mb-0"><i class="fas fa-money-bill-wave me-2"></i>معلومات الراتب</h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-4 mb-3">
                                    <label for="edit_salary_type" class="form-label">نوع الراتب <span class="text-danger">*</span></label>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="fas fa-file-invoice-dollar"></i></span>
                                        <select class="form-select" id="edit_salary_type" name="salary_type" required>
                                            <?php foreach($salaryTypes as $value => $label): ?>
                                                <option value="<?php echo $value; ?>"><?php echo $label; ?></option>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label for="edit_fixed_salary" class="form-label">الراتب الثابت</label>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="fas fa-dollar-sign"></i></span>
                                        <input type="number" class="form-control" id="edit_fixed_salary" name="fixed_salary" min="0" step="0.01">
                                    </div>
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label for="edit_commission_percentage" class="form-label">نسبة العمولة (%)</label>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="fas fa-percentage"></i></span>
                                        <input type="number" class="form-control" id="edit_commission_percentage" name="commission_percentage" min="0" max="100" step="0.01">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <?php if (isAdmin()): ?>
                    <div class="card mb-3 border-0 shadow-sm">
                        <div class="card-header bg-light">
                            <h6 class="mb-0"><i class="fas fa-building me-2"></i>معلومات إدارية</h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="edit_branch_id" class="form-label">الفرع</label>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="fas fa-code-branch"></i></span>
                                        <select class="form-select" id="edit_branch_id" name="branch_id">
                                            <?php
                                            foreach ($branches as $branch) {
                                                echo '<option value="' . $branch['id'] . '">' . htmlspecialchars($branch['name']) . '</option>';
                                            }
                                            ?>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="edit_is_active" class="form-label">الحالة</label>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="fas fa-toggle-on"></i></span>
                                        <select class="form-select" id="edit_is_active" name="is_active">
                                            <option value="1">نشط</option>
                                            <option value="0">غير نشط</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <?php else: ?>
                    <input type="hidden" name="branch_id" value="<?php echo $_SESSION['user_branch_id']; ?>">
                    <input type="hidden" name="is_active" value="1">
                    <?php endif; ?>

                    <div class="card mb-3 border-0 shadow-sm">
                        <div class="card-header bg-light">
                            <h6 class="mb-0"><i class="fas fa-concierge-bell me-2"></i>الخدمات</h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-12 mb-3">
                                    <label for="edit_services" class="form-label">الخدمات التي يقدمها</label>
                                    <select class="form-select select2" id="edit_services" name="services[]" multiple>
                                        <?php
                                        // استرجاع جميع الخدمات
                                        $serviceModel = new Service($db);
                                        $services = $serviceModel->getServices(['is_active' => 1]);

                                        foreach ($services as $service) {
                                            echo '<option value="' . $service['id'] . '">' . htmlspecialchars($service['name']) . '</option>';
                                        }
                                        ?>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="card mb-3 border-0 shadow-sm">
                        <div class="card-header bg-light">
                            <h6 class="mb-0"><i class="fas fa-user-lock me-2"></i>معلومات الحساب</h6>
                        </div>
                        <div class="card-body">
                            <div class="form-check mb-3">
                                <input class="form-check-input" type="checkbox" id="edit_create_user" name="create_user" value="1">
                                <label class="form-check-label" for="edit_create_user">
                                    إنشاء/تحديث حساب مستخدم للموظف
                                </label>
                            </div>

                            <div id="edit_userAccountSection" style="display: none;">
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="edit_username" class="form-label">اسم المستخدم</label>
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="fas fa-user-tag"></i></span>
                                            <input type="text" class="form-control" id="edit_username" name="username">
                                        </div>
                                        <div class="form-text">سيتم استخدام الاسم الأول ورقم الهاتف إذا تم تركه فارغًا</div>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="edit_password" class="form-label">كلمة المرور</label>
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="fas fa-key"></i></span>
                                            <input type="text" class="form-control" id="edit_password" name="password">
                                            <button class="btn btn-outline-secondary" type="button" id="generatePasswordBtn">
                                                <i class="fas fa-random"></i>
                                            </button>
                                        </div>
                                        <div class="form-text">اترك الحقل فارغًا للاحتفاظ بكلمة المرور الحالية</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-1"></i> إلغاء
                </button>
                <button type="button" class="btn btn-primary" id="submitEditEmployee">
                    <i class="fas fa-save me-1"></i> حفظ التعديلات
                </button>
            </div>
        </div>
    </div>
</div>

<!-- مودال عرض تفاصيل الموظف -->
<div class="modal fade" id="viewEmployeeModal" tabindex="-1" aria-labelledby="viewEmployeeModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="viewEmployeeModalLabel">تفاصيل الموظف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="إغلاق"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label fw-bold">الاسم:</label>
                            <p id="view_name" class="mb-0"></p>
                        </div>
                        <div class="mb-3">
                            <label class="form-label fw-bold">الوظيفة:</label>
                            <p id="view_position" class="mb-0"></p>
                        </div>
                        <div class="mb-3">
                            <label class="form-label fw-bold">رقم الهاتف:</label>
                            <p id="view_phone" dir="ltr" class="mb-0"></p>
                        </div>
                        <div class="mb-3">
                            <label class="form-label fw-bold">البريد الإلكتروني:</label>
                            <p id="view_email" dir="ltr" class="mb-0"></p>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label fw-bold">نوع الراتب:</label>
                            <p id="view_salary_type" class="mb-0"></p>
                        </div>
                        <div class="mb-3">
                            <label class="form-label fw-bold">الراتب الثابت:</label>
                            <p id="view_fixed_salary" class="mb-0"></p>
                        </div>
                        <div class="mb-3">
                            <label class="form-label fw-bold">نسبة العمولة:</label>
                            <p id="view_commission_percentage" class="mb-0"></p>
                        </div>
                        <div class="mb-3">
                            <label class="form-label fw-bold">الفرع:</label>
                            <p id="view_branch_name" class="mb-0"></p>
                        </div>
                    </div>
                </div>

                <hr>

                <div class="mb-3">
                    <label class="form-label fw-bold">الخدمات:</label>
                    <div id="view_services" class="mb-0"></div>
                </div>

                <!-- علامات التبويب لعرض مزيد من المعلومات -->
                <ul class="nav nav-tabs mt-4" id="employeeTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="stats-tab" data-bs-toggle="tab" data-bs-target="#stats" type="button" role="tab" aria-controls="stats" aria-selected="true">
                            الإحصائيات
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="attendance-tab" data-bs-toggle="tab" data-bs-target="#attendance" type="button" role="tab" aria-controls="attendance" aria-selected="false">
                            سجل الحضور
                        </button>
                    </li>
                </ul>

                <div class="tab-content p-3 border border-top-0 rounded-bottom" id="employeeTabsContent">
                    <!-- محتوى الإحصائيات -->
                    <div class="tab-pane fade show active" id="stats" role="tabpanel" aria-labelledby="stats-tab">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <div class="card h-100">
                                    <div class="card-body text-center">
                                        <h6 class="card-title text-muted">إجمالي الخدمات هذا الشهر</h6>
                                        <h2 class="display-4 fw-bold text-primary" id="view_services_count">0</h2>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <div class="card h-100">
                                    <div class="card-body text-center">
                                        <h6 class="card-title text-muted">إجمالي المبيعات هذا الشهر</h6>
                                        <h2 class="display-4 fw-bold text-success" id="view_sales_total">0</h2>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- محتوى سجل الحضور -->
                    <div class="tab-pane fade" id="attendance" role="tabpanel" aria-labelledby="attendance-tab">
                        <div class="row mb-3">
                            <div class="col-md-5">
                                <select id="attendance_month" class="form-select">
                                    <?php
                                    $months = [
                                        '1' => 'يناير', '2' => 'فبراير', '3' => 'مارس',
                                        '4' => 'أبريل', '5' => 'مايو', '6' => 'يونيو',
                                        '7' => 'يوليو', '8' => 'أغسطس', '9' => 'سبتمبر',
                                        '10' => 'أكتوبر', '11' => 'نوفمبر', '12' => 'ديسمبر'
                                    ];
                                    $currentMonth = date('n');
                                    foreach ($months as $num => $name) {
                                        $selected = ($num == $currentMonth) ? 'selected' : '';
                                        echo "<option value=\"$num\" $selected>$name</option>";
                                    }
                                    ?>
                                </select>
                            </div>
                            <div class="col-md-5">
                                <select id="attendance_year" class="form-select">
                                    <?php
                                    $currentYear = date('Y');
                                    for ($i = $currentYear - 2; $i <= $currentYear; $i++) {
                                        $selected = ($i == $currentYear) ? 'selected' : '';
                                        echo "<option value=\"$i\" $selected>$i</option>";
                                    }
                                    ?>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <button id="load_attendance" class="btn btn-primary w-100">عرض</button>
                            </div>
                        </div>

                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>التاريخ</th>
                                        <th>وقت الحضور</th>
                                        <th>وقت الانصراف</th>
                                        <th>ملاحظات</th>
                                    </tr>
                                </thead>
                                <tbody id="attendance_table_body">
                                    <!-- سيتم تعبئة البيانات عبر AJAX -->
                                </tbody>
                            </table>
                        </div>
                        <div id="no_attendance_message" class="text-center text-muted py-3" style="display: none;">
                            لا توجد سجلات حضور لهذا الشهر
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <?php if (hasPermission('employees_edit')): ?>
                <button type="button" class="btn btn-warning me-auto" id="editEmployeeBtn">
                    <i class="fas fa-edit me-1"></i> تعديل البيانات
                </button>
                <?php endif; ?>

                <div class="ms-auto">
                    <?php if (hasPermission('employees_salaries')): ?>
                    <a href="salaries.php" class="btn btn-success" id="viewSalaryBtn">
                        <i class="fas fa-money-bill-wave me-1"></i> عرض الرواتب
                    </a>
                    <?php endif; ?>

                    <?php if (hasPermission('pos_view')): ?>
                    <a href="../pos/index.php" class="btn btn-primary" id="createInvoiceBtn">
                        <i class="fas fa-cash-register me-1"></i> إنشاء فاتورة
                    </a>
                    <?php endif; ?>

                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- مودال تسجيل الحضور -->
<div class="modal fade" id="recordAttendanceModal" tabindex="-1" aria-labelledby="recordAttendanceModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="recordAttendanceModalLabel">تسجيل حضور</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="إغلاق"></button>
            </div>
            <div class="modal-body">
                <form id="attendanceForm">
                    <input type="hidden" id="attendance_employee_id" name="employee_id">

                    <div class="mb-3">
                        <label for="attendance_date" class="form-label">التاريخ <span class="text-danger">*</span></label>
                        <input type="text" class="form-control date-picker" id="attendance_date" name="date" required value="<?php echo date('Y/m/d'); ?>">
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="check_in" class="form-label">وقت الحضور</label>
                            <input type="time" class="form-control" id="check_in" name="check_in">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="check_out" class="form-label">وقت الانصراف</label>
                            <input type="time" class="form-control" id="check_out" name="check_out">
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="attendance_notes" class="form-label">ملاحظات</label>
                        <textarea class="form-control" id="attendance_notes" name="notes" rows="3"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" id="submitAttendance">حفظ</button>
            </div>
        </div>
    </div>
</div>
<div class="modal fade" id="paySalaryModal" tabindex="-1" aria-labelledby="paySalaryModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-success text-white">
                <h5 class="modal-title" id="paySalaryModalLabel">دفع راتب موظف</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="إغلاق"></button>
            </div>
            <div class="modal-body">
                <h5 class="employee-name text-center mb-4"></h5>

                <ul class="nav nav-tabs" id="salaryTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="current-month-tab" data-bs-toggle="tab" data-bs-target="#current-month" type="button" role="tab" aria-controls="current-month" aria-selected="true">
                            الشهر الحالي
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="custom-month-tab" data-bs-toggle="tab" data-bs-target="#custom-month" type="button" role="tab" aria-controls="custom-month" aria-selected="false">
                            شهر محدد
                        </button>
                    </li>
                </ul>

                <div class="tab-content p-3 border border-top-0 rounded-bottom" id="salaryTabsContent">
                    <!-- تبويب الشهر الحالي -->
                    <div class="tab-pane fade show active" id="current-month" role="tabpanel" aria-labelledby="current-month-tab">
                        <div id="currentMonthSalaryDetails">
                            <div class="text-center mb-3">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">جارٍ التحميل...</span>
                                </div>
                                <p class="mt-2">جارٍ حساب مستحقات الشهر الحالي...</p>
                            </div>
                        </div>

                        <form id="currentMonthSalaryForm">
                            <input type="hidden" id="current_employee_id" name="employee_id">
                            <input type="hidden" id="current_month" name="month">
                            <input type="hidden" id="current_year" name="year">
                            <input type="hidden" id="current_salary_id" name="salary_id">

                            <div class="row" id="currentMonthPaymentDetails" style="display: none;">
                                <div class="col-md-6 mb-3">
                                    <label for="current_payment_date" class="form-label">تاريخ الدفع</label>
                                    <input type="text" class="form-control date-picker" id="current_payment_date" name="payment_date" value="<?php echo date('Y/m/d'); ?>" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="current_payment_status" class="form-label">حالة الدفع</label>
                                    <select class="form-select" id="current_payment_status" name="status" required>
                                        <option value="paid">مدفوع</option>
                                        <option value="unpaid">غير مدفوع</option>
                                    </select>
                                </div>
                                <div class="col-md-12 mb-3">
                                    <label for="current_notes" class="form-label">ملاحظات</label>
                                    <textarea class="form-control" id="current_notes" name="notes" rows="2"></textarea>
                                </div>
                            </div>
                        </form>
                    </div>

                    <!-- تبويب الشهر المحدد -->
                    <div class="tab-pane fade" id="custom-month" role="tabpanel" aria-labelledby="custom-month-tab">
                        <form id="customMonthSalaryForm">
                            <input type="hidden" id="custom_employee_id" name="employee_id">

                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label for="custom_month" class="form-label">الشهر</label>
                                    <select id="custom_month" name="month" class="form-select" required>
                                        <?php
                                        $months = [
                                            '1' => 'يناير', '2' => 'فبراير', '3' => 'مارس',
                                            '4' => 'أبريل', '5' => 'مايو', '6' => 'يونيو',
                                            '7' => 'يوليو', '8' => 'أغسطس', '9' => 'سبتمبر',
                                            '10' => 'أكتوبر', '11' => 'نوفمبر', '12' => 'ديسمبر'
                                        ];

                                        // شهر سابق للشهر الحالي
                                        $previousMonth = date('n') - 1;
                                        if ($previousMonth <= 0) {
                                            $previousMonth = 12;
                                        }

                                        foreach ($months as $num => $name) {
                                            $selected = ($num == $previousMonth) ? 'selected' : '';
                                            echo "<option value=\"$num\" $selected>$name</option>";
                                        }
                                        ?>
                                    </select>
                                </div>
                                <div class="col-md-6">
                                    <label for="custom_year" class="form-label">السنة</label>
                                    <select id="custom_year" name="year" class="form-select" required>
                                        <?php
                                        $currentYear = date('Y');
                                        // السنة السابقة إذا كان الشهر المختار هو ديسمبر والشهر الحالي هو يناير
                                        $defaultYear = ($previousMonth == 12 && date('n') == 1) ? $currentYear - 1 : $currentYear;

                                        for ($i = $currentYear - 2; $i <= $currentYear; $i++) {
                                            $selected = ($i == $defaultYear) ? 'selected' : '';
                                            echo "<option value=\"$i\" $selected>$i</option>";
                                        }
                                        ?>
                                    </select>
                                </div>
                            </div>

                            <div class="mb-3">
                                <button type="button" id="calculateCustomSalary" class="btn btn-primary">
                                    <i class="fas fa-calculator me-1"></i> حساب الراتب
                                </button>
                            </div>

                            <div id="customMonthSalaryDetails" style="display: none;">
                                <hr>
                                <div class="salary-details-content"></div>

                                <div class="row mt-3">
                                    <div class="col-md-6 mb-3">
                                        <label for="custom_payment_date" class="form-label">تاريخ الدفع</label>
                                        <input type="text" class="form-control date-picker" id="custom_payment_date" name="payment_date" value="<?php echo date('Y/m/d'); ?>" required>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="custom_payment_status" class="form-label">حالة الدفع</label>
                                        <select class="form-select" id="custom_payment_status" name="payment_status" required>
                                            <option value="paid">مدفوع</option>
                                            <option value="unpaid">غير مدفوع</option>
                                        </select>
                                    </div>
                                    <div class="col-md-12 mb-3">
                                        <label for="custom_notes" class="form-label">ملاحظات</label>
                                        <textarea class="form-control" id="custom_notes" name="notes" rows="2"></textarea>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-success" id="saveSalaryPayment" disabled>
                    <i class="fas fa-money-bill-wave me-1"></i> حفظ
                </button>
            </div>
        </div>
    </div>
</div>
<!-- مودال إضافة مكافأة للموظف -->
<div class="modal fade" id="addBonusModal" tabindex="-1" aria-labelledby="addBonusModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-success text-white">
                <h5 class="modal-title" id="addBonusModalLabel">إضافة مكافأة للموظف</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="إغلاق"></button>
            </div>
            <div class="modal-body">
                <h5 class="employee-name text-center mb-4"></h5>
                <form id="addBonusForm">
                    <input type="hidden" id="bonus_employee_id" name="employee_id">
                    <input type="hidden" id="bonus_month" name="month" value="<?php echo date('n'); ?>">
                    <input type="hidden" id="bonus_year" name="year" value="<?php echo date('Y'); ?>">

                    <div class="mb-3">
                        <label for="bonus_amount" class="form-label">قيمة المكافأة <span class="text-danger">*</span></label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-money-bill"></i></span>
                            <input type="number" class="form-control" id="bonus_amount" name="bonus_amount" min="0" step="0.01" required>
                            <span class="input-group-text"><?php echo $currencySymbol; ?></span>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="bonus_notes" class="form-label">سبب المكافأة</label>
                        <textarea class="form-control" id="bonus_notes" name="notes" rows="3"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-success" id="saveBonusBtn">
                    <i class="fas fa-save me-1"></i> حفظ المكافأة
                </button>
            </div>
        </div>
    </div>
</div>

<!-- مودال إضافة خصم للموظف -->
<div class="modal fade" id="addDeductionModal" tabindex="-1" aria-labelledby="addDeductionModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title" id="addDeductionModalLabel">إضافة خصم للموظف</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="إغلاق"></button>
            </div>
            <div class="modal-body">
                <h5 class="employee-name text-center mb-4"></h5>
                <form id="addDeductionForm">
                    <input type="hidden" id="deduction_employee_id" name="employee_id">
                    <input type="hidden" id="deduction_month" name="month" value="<?php echo date('n'); ?>">
                    <input type="hidden" id="deduction_year" name="year" value="<?php echo date('Y'); ?>">

                    <div class="mb-3">
                        <label for="deduction_amount" class="form-label">قيمة الخصم <span class="text-danger">*</span></label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-money-bill"></i></span>
                            <input type="number" class="form-control" id="deduction_amount" name="deduction_amount" min="0" step="0.01" required>
                            <span class="input-group-text"><?php echo $currencySymbol; ?></span>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="deduction_notes" class="form-label">سبب الخصم</label>
                        <textarea class="form-control" id="deduction_notes" name="notes" rows="3"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-danger" id="saveDeductionBtn">
                    <i class="fas fa-save me-1"></i> حفظ الخصم
                </button>
            </div>
        </div>
    </div>
</div>

<!-- مودال إزالة خصم للموظف -->
<div class="modal fade" id="removeDeductionModal" tabindex="-1" aria-labelledby="removeDeductionModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-warning text-dark">
                <h5 class="modal-title" id="removeDeductionModalLabel">إزالة خصم للموظف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="إغلاق"></button>
            </div>
            <div class="modal-body">
                <h5 class="employee-name text-center mb-4"></h5>
                <form id="removeDeductionForm">
                    <input type="hidden" id="remove_deduction_employee_id" name="employee_id">
                    <input type="hidden" id="remove_deduction_month" name="month" value="<?php echo date('n'); ?>">
                    <input type="hidden" id="remove_deduction_year" name="year" value="<?php echo date('Y'); ?>">

                    <div class="mb-3">
                        <label for="remove_deduction_amount" class="form-label">قيمة الخصم المراد إزالته <span class="text-danger">*</span></label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-money-bill"></i></span>
                            <input type="number" class="form-control" id="remove_deduction_amount" name="remove_deduction_amount" min="0" step="0.01" required>
                            <span class="input-group-text"><?php echo $currencySymbol; ?></span>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="remove_deduction_notes" class="form-label">سبب إزالة الخصم</label>
                        <textarea class="form-control" id="remove_deduction_notes" name="notes" rows="3"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-warning" id="removeDeductionBtn">
                    <i class="fas fa-minus-circle me-1"></i> إزالة الخصم
                </button>
            </div>
        </div>
    </div>
</div>

<!-- مودال إزالة مكافأة للموظف -->
<div class="modal fade" id="removeBonusModal" tabindex="-1" aria-labelledby="removeBonusModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-info text-white">
                <h5 class="modal-title" id="removeBonusModalLabel">إزالة مكافأة للموظف</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="إغلاق"></button>
            </div>
            <div class="modal-body">
                <h5 class="employee-name text-center mb-4"></h5>
                <form id="removeBonusForm">
                    <input type="hidden" id="remove_bonus_employee_id" name="employee_id">
                    <input type="hidden" id="remove_bonus_month" name="month" value="<?php echo date('n'); ?>">
                    <input type="hidden" id="remove_bonus_year" name="year" value="<?php echo date('Y'); ?>">

                    <div class="mb-3">
                        <label for="remove_bonus_amount" class="form-label">قيمة المكافأة المراد إزالتها <span class="text-danger">*</span></label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-money-bill"></i></span>
                            <input type="number" class="form-control" id="remove_bonus_amount" name="remove_bonus_amount" min="0" step="0.01" required>
                            <span class="input-group-text"><?php echo $currencySymbol; ?></span>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="remove_bonus_notes" class="form-label">سبب إزالة المكافأة</label>
                        <textarea class="form-control" id="remove_bonus_notes" name="notes" rows="3"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-info" id="removeBonusBtn">
                    <i class="fas fa-minus-circle me-1"></i> إزالة المكافأة
                </button>
            </div>
        </div>
    </div>
</div>
<!-- استدعاء قالب الفوتر -->
<?php include '../../includes/templates/footer.php'; ?>

<!-- سكريبت الصفحة -->
<script>
// تعريف متغيرات عالمية
var API_URL = '../../api/';
var currencySymbol = '<?php echo $currencySymbol; ?>';
var currencyName = '<?php echo $currencyName; ?>';

// دالة تنسيق العملة
function formatCurrency(amount) {
    return parseFloat(amount).toFixed(2) + ' ' + currencySymbol;
}

// عند تحميل الصفحة
$(document).ready(function() {
        // تهيئة جدول البيانات
        var employeesTable = $('#employeesTable').DataTable({
            processing: true,
            serverSide: false,
            ajax: {
                url: API_URL + 'employees.php',
                type: 'POST',
                data: function(d) {
                    d.action = 'get_employees';
                    d.search = $('#search_term').val();
                    d.position = $('#position_filter').val();
                    d.branch_id = $('#branch_filter').val();
                    d.is_active = $('#status_filter').val();
                },
                dataSrc: function(response) {
                    if (response && response.status === 'success' && response.employees) {
                        return response.employees;
                    }
                    return [];
                }
            },
            columns: [
                { data: 'id' },
                { data: 'name' },
                { data: 'position' },
                { data: 'phone' },
                {
                    data: 'salary_type',
                    render: function(data) {
                        switch(data) {
                            case 'fixed': return 'ثابت';
                            case 'percentage': return 'نسبة';
                            case 'both': return 'ثابت ونسبة';
                            default: return data;
                        }
                    }
                },
                {
                    data: 'fixed_salary',
                    render: function(data) {
                        return formatCurrency(data);
                    }
                },
                {
                    data: 'commission_percentage',
                    render: function(data) {
                        return parseFloat(data) + '%';
                    }
                },
                { data: 'branch_name' },
                {
                    data: 'is_active',
                    render: function(data) {
                        if (data == 1) {
                            return '<span class="badge bg-success">نشط</span>';
                        } else {
                            return '<span class="badge bg-danger">غير نشط</span>';
                        }
                    }
                },
                {
                    data: null,
                    orderable: false,
                    render: function(data) {
                        let actions = `
                            <div class="dropdown">
                                <button class="btn btn-sm btn-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                                    الإجراءات
                                </button>
                                <ul class="dropdown-menu dropdown-menu-end">
                                    <li><a class="dropdown-item view-employee" href="javascript:void(0)" data-id="${data.id}">
                                        <i class="fas fa-eye me-2"></i> عرض التفاصيل
                                    </a></li>`;

                        <?php if (hasPermission('employees_edit')): ?>
                        actions += `
                            <li><a class="dropdown-item edit-employee" href="javascript:void(0)" data-id="${data.id}">
                                <i class="fas fa-edit me-2"></i> تعديل
                            </a></li>
                            <li><a class="dropdown-item record-attendance" href="javascript:void(0)" data-id="${data.id}">
                                <i class="fas fa-user-clock me-2"></i> تسجيل حضور
                            </a></li>`;
                        <?php endif; ?>
                        actions += `
    <li><a class="dropdown-item pay-salary" href="javascript:void(0)" data-id="${data.id}" data-name="${data.name}">
        <i class="fas fa-money-bill-alt me-2"></i> دفع راتب
    </a></li>`;

                        <?php if (hasPermission('pos_view')): ?>
                        actions += `
    <li><a class="dropdown-item" href="../pos/index.php?employee_id=${data.id}">
        <i class="fas fa-cash-register me-2"></i> إنشاء فاتورة
    </a></li>`;
                        <?php endif; ?>

    actions += `
    <li><a class="dropdown-item add-bonus" href="javascript:void(0)" data-id="${data.id}" data-name="${data.name}">
        <i class="fas fa-gift me-2"></i> إضافة مكافأة
    </a></li>
    <li><a class="dropdown-item remove-bonus" href="javascript:void(0)" data-id="${data.id}" data-name="${data.name}">
        <i class="fas fa-minus-square me-2"></i> إزالة مكافأة
    </a></li>
    <li><a class="dropdown-item add-deduction" href="javascript:void(0)" data-id="${data.id}" data-name="${data.name}">
        <i class="fas fa-minus-circle me-2"></i> إضافة خصم
    </a></li>
    <li><a class="dropdown-item remove-deduction" href="javascript:void(0)" data-id="${data.id}" data-name="${data.name}">
        <i class="fas fa-plus-circle me-2"></i> إزالة خصم
    </a></li>`;

                        <?php if (hasPermission('employees_delete')): ?>
                        actions += `
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item text-danger delete-employee" href="javascript:void(0)" data-id="${data.id}">
                                <i class="fas fa-trash-alt me-2"></i> حذف
                            </a></li>`;
                        <?php endif; ?>

                        actions += `
                                </ul>
                            </div>`;

                        return actions;
                    }
                }
            ],
            language: {
                url: '../../assets/plugins/datatables/ar.json'
            },
            dom: "<'row'<'col-sm-12 col-md-6'l><'col-sm-12 col-md-6'f>><'row'<'col-sm-12'tr>><'row'<'col-sm-12 col-md-5'i><'col-sm-12 col-md-7'p>>",
            lengthMenu: [[10, 25, 50, 100, -1], [10, 25, 50, 100, "الكل"]]
        });
        const datePickerSettings = {
        singleDatePicker: true,
        showDropdowns: true,
        locale: {
            format: 'YYYY-MM-DD', // تغيير من YYYY/MM/DD إلى YYYY-MM-DD
            applyLabel: 'تطبيق',
            cancelLabel: 'إلغاء',
            fromLabel: 'من',
            toLabel: 'إلى',
            customRangeLabel: 'مخصص',
            daysOfWeek: ['أح', 'إث', 'ثل', 'أر', 'خم', 'جم', 'سب'],
            monthNames: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو', 'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'],
            firstDay: 0
        }
    };
    $('.date-picker').daterangepicker(datePickerSettings);

        // البحث
        $('#searchForm').on('submit', function(e) {
            e.preventDefault();
            employeesTable.ajax.reload();
        });

        // إعادة ضبط البحث
        $('#searchForm button[type="reset"]').on('click', function() {
            $('#search_term').val('');
            $('#branch_filter').val('');
            $('#position_filter').val('');
            $('#status_filter').val('1');
            employeesTable.ajax.reload();
        });

        // تبديل قسم حساب المستخدم عند إضافة موظف
        $('#create_user').on('change', function() {
            if ($(this).is(':checked')) {
                $('#userAccountSection').show();
            } else {
                $('#userAccountSection').hide();
            }
        });

        // تبديل قسم حساب المستخدم عند تعديل موظف
        $('#edit_create_user').on('change', function() {
            if ($(this).is(':checked')) {
                $('#edit_userAccountSection').show();
            } else {
                $('#edit_userAccountSection').hide();
            }
        });

        // عرض/إخفاء حقول الراتب بناءً على نوع الراتب المختار
        $('#salary_type').on('change', function() {
            const salaryType = $(this).val();

            if (salaryType === 'fixed') {
                $('#fixed_salary').prop('readonly', false);
                $('#commission_percentage').prop('readonly', true).val(0);
            } else if (salaryType === 'percentage') {
                $('#fixed_salary').prop('readonly', true).val(0);
                $('#commission_percentage').prop('readonly', false);
            } else { // both
                $('#fixed_salary').prop('readonly', false);
                $('#commission_percentage').prop('readonly', false);
            }
        });

        // عرض/إخفاء حقول الراتب عند التعديل
        $('#edit_salary_type').on('change', function() {
            const salaryType = $(this).val();

            if (salaryType === 'fixed') {
                $('#edit_fixed_salary').prop('readonly', false);
                $('#edit_commission_percentage').prop('readonly', true).val(0);
            } else if (salaryType === 'percentage') {
                $('#edit_fixed_salary').prop('readonly', true).val(0);
                $('#edit_commission_percentage').prop('readonly', false);
            } else { // both
                $('#edit_fixed_salary').prop('readonly', false);
                $('#edit_commission_percentage').prop('readonly', false);
            }
        });
        // إضافة موظف جديد
        $('#submitAddEmployee').on('click', function() {
            const form = $('#addEmployeeForm')[0];
            if (!form.checkValidity()) {
                form.reportValidity();
                return;
            }

            const formData = new FormData(form);
            formData.append('action', 'add_employee');

            // جمع الخدمات المختارة
            const services = $('#services').val();
            if (services && services.length > 0) {
                formData.delete('services[]');
                formData.append('services', services);
            }

            $.ajax({
                url: '../../api/employees.php',
                type: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                dataType: 'json',
                success: function(response) {
                    if (response.status === 'success') {
                        // إغلاق المودال وإعادة تحميل الجدول
                        $('#addEmployeeModal').modal('hide');
                        form.reset();

                        // إظهار كلمة المرور المولدة إذا كانت موجودة
                        if (response.generated_password) {
                            let message = response.message + `<br><br>تم إنشاء حساب مستخدم بكلمة المرور: <strong>${response.generated_password}</strong>`;
                            showAlert(message);
                        } else {
                            showAlert(response.message);
                        }

                        employeesTable.ajax.reload();
                    } else {
                        showAlert(response.message, 'danger');
                    }
                },
                error: function() {
                    showAlert('حدث خطأ أثناء إضافة الموظف', 'danger');
                }
            });
        });

        // تحميل بيانات الموظف للتعديل
        $(document).on('click', '.edit-employee', function() {
            const employeeId = $(this).data('id');

            $.ajax({
                url: '../../api/employees.php',
                type: 'POST',
                data: {
                    action: 'get_employee',
                    employee_id: employeeId
                },
                dataType: 'json',
                success: function(response) {
                    if (response.status === 'success') {
                        const employee = response.employee;

                        $('#edit_employee_id').val(employee.id);
                        $('#edit_name').val(employee.name);
                        $('#edit_position').val(employee.position);
                        $('#edit_phone').val(employee.phone);
                        $('#edit_email').val(employee.email);
                        $('#edit_salary_type').val(employee.salary_type).trigger('change');
                        $('#edit_fixed_salary').val(employee.fixed_salary);
                        $('#edit_commission_percentage').val(employee.commission_percentage);

                        if ($('#edit_branch_id').length) {
                            $('#edit_branch_id').val(employee.branch_id);
                        }

                        $('#edit_is_active').val(employee.is_active);

                        // تحميل الخدمات
                        if (employee.services && employee.services.length > 0) {
                            $('#edit_services').val(employee.services.map(s => s.id)).trigger('change');
                        } else {
                            $('#edit_services').val([]).trigger('change');
                        }

                        // إذا كان لديه حساب مستخدم
                        if (employee.user_id) {
                            $('#edit_create_user').prop('checked', true).trigger('change');
                            $('#edit_username').val(employee.username);
                        } else {
                            $('#edit_create_user').prop('checked', false).trigger('change');
                            $('#edit_username').val('');
                        }

                        $('#editEmployeeModal').modal('show');
                    } else {
                        showAlert(response.message, 'danger');
                    }
                },
                error: function() {
                    showAlert('حدث خطأ أثناء تحميل بيانات الموظف', 'danger');
                }
            });
        });

        // تعديل بيانات الموظف
        $('#submitEditEmployee').on('click', function() {
            const form = $('#editEmployeeForm')[0];
            if (!form.checkValidity()) {
                form.reportValidity();
                return;
            }

            const formData = new FormData(form);
            formData.append('action', 'update_employee');

            // جمع الخدمات المختارة
            const services = $('#edit_services').val();
            if (services && services.length > 0) {
                formData.delete('services[]');
                formData.append('services', services);
            }

            $.ajax({
                url: '../../api/employees.php',
                type: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                dataType: 'json',
                success: function(response) {
                    if (response.status === 'success') {
                        // إغلاق المودال وإعادة تحميل الجدول
                        $('#editEmployeeModal').modal('hide');

                        // إظهار كلمة المرور المولدة إذا كانت موجودة
                        if (response.generated_password) {
                            let message = response.message + `<br><br>تم إنشاء حساب مستخدم بكلمة المرور: <strong>${response.generated_password}</strong>`;
                            showAlert(message);
                        } else {
                            showAlert(response.message);
                        }

                        employeesTable.ajax.reload();
                    } else {
                        showAlert(response.message, 'danger');
                    }
                },
                error: function() {
                    showAlert('حدث خطأ أثناء تعديل بيانات الموظف', 'danger');
                }
            });
        });

        // حذف موظف
        $(document).on('click', '.delete-employee', function() {
            const employeeId = $(this).data('id');

            confirmAction('هل أنت متأكد من حذف هذا الموظف؟', function() {
                $.ajax({
                    url: '../../api/employees.php',
                    type: 'POST',
                    data: {
                        action: 'delete_employee',
                        employee_id: employeeId
                    },
                    dataType: 'json',
                    success: function(response) {
                        if (response.status === 'success') {
                            showAlert(response.message);
                            employeesTable.ajax.reload();
                        } else {
                            showAlert(response.message, 'danger');
                        }
                    },
                    error: function() {
                        showAlert('حدث خطأ أثناء حذف الموظف', 'danger');
                    }
                });
            });
        });

        // عرض تفاصيل الموظف
        $(document).on('click', '.view-employee', function() {
            const employeeId = $(this).data('id');

            $.ajax({
                url: '../../api/employees.php',
                type: 'POST',
                data: {
                    action: 'get_employee',
                    employee_id: employeeId,
                    with_services: true
                },
                dataType: 'json',
                success: function(response) {
                    if (response.status === 'success') {
                        const employee = response.employee;

                        // عرض بيانات الموظف
                        $('#view_name').text(employee.name);
                        $('#view_position').text(employee.position);
                        $('#view_phone').text(employee.phone || '-');
                        $('#view_email').text(employee.email || '-');

                        // عرض بيانات الراتب
                        let salaryType = '';
                        switch(employee.salary_type) {
                            case 'fixed': salaryType = 'ثابت'; break;
                            case 'percentage': salaryType = 'نسبة'; break;
                            case 'both': salaryType = 'ثابت ونسبة'; break;
                            default: salaryType = employee.salary_type;
                        }

                        $('#view_salary_type').text(salaryType);
                        $('#view_fixed_salary').text(formatCurrency(employee.fixed_salary));
                        $('#view_commission_percentage').text(parseFloat(employee.commission_percentage).toFixed(2) + '%');
                        $('#view_branch_name').text(employee.branch_name || '-');

                        // عرض الخدمات
                        const servicesDiv = $('#view_services');
                        servicesDiv.empty();

                        if (employee.services && employee.services.length > 0) {
                            const servicesList = $('<div class="d-flex flex-wrap gap-1"></div>');
                            employee.services.forEach(function(service) {
                                servicesList.append(`<span class="badge bg-info">${service.name}</span>`);
                            });
                            servicesDiv.append(servicesList);
                        } else {
                            servicesDiv.text('لا توجد خدمات مسجلة لهذا الموظف');
                        }

                        // تحديث روابط الإجراءات
                        $('#editEmployeeBtn').data('id', employee.id);
                        $('#viewSalaryBtn').attr('href', `salaries.php?employee_id=${employee.id}`);
                        $('#createInvoiceBtn').attr('href', `../pos/index.php?employee_id=${employee.id}`);

                        // تحميل الإحصائيات
                        loadEmployeeStats(employee.id);

                        // تحميل سجل الحضور للشهر الحالي
                        loadEmployeeAttendance(employee.id);

                        // عرض المودال
                        $('#viewEmployeeModal').modal('show');
                    } else {
                        showAlert(response.message, 'danger');
                    }
                },
                error: function() {
                    showAlert('حدث خطأ أثناء تحميل بيانات الموظف', 'danger');
                }
            });
        });

        // تحميل إحصائيات الموظف
        function loadEmployeeStats(employeeId) {
            $.ajax({
                url: '../../api/employees.php',
                type: 'POST',
                data: {
                    action: 'get_employee_stats',
                    employee_id: employeeId,
                    period: 'month'
                },
                dataType: 'json',
                success: function(response) {
                    if (response.status === 'success' && response.stats) {
                        const stats = response.stats;

                        $('#view_services_count').text(stats.services_count || 0);
                        $('#view_sales_total').text(parseFloat(stats.sales_total || 0).toFixed(2) + '<?php $currencySymbol; ?>');
                    }
                }
            });
        }

        // تحميل سجل حضور الموظف
        function loadEmployeeAttendance(employeeId, month, year) {
            month = month || $('#attendance_month').val();
            year = year || $('#attendance_year').val();

            $.ajax({
                url: '../../api/employees.php',
                type: 'POST',
                data: {
                    action: 'get_employee_attendance',
                    employee_id: employeeId,
                    month: month,
                    year: year
                },
                dataType: 'json',
                success: function(response) {
                    if (response.status === 'success') {
                        const attendanceBody = $('#attendance_table_body');
                        attendanceBody.empty();

                        if (response.attendance && response.attendance.length > 0) {
                            $('#no_attendance_message').hide();

                            response.attendance.forEach(function(record) {
                                attendanceBody.append(`
                                    <tr>
                                        <td>${moment(record.date).format('YYYY/MM/DD')}</td>
                                        <td>${record.check_in ? moment(record.check_in, 'HH:mm:ss').format('hh:mm A') : '-'}</td>
                                        <td>${record.check_out ? moment(record.check_out, 'HH:mm:ss').format('hh:mm A') : '-'}</td>
                                        <td>${record.notes || '-'}</td>
                                    </tr>
                                `);
                            });
                        } else {
                            $('#no_attendance_message').show();
                        }
                    }
                }
            });
        }

        // زر تحميل سجل الحضور
        $('#load_attendance').on('click', function() {
            const employeeId = $('#editEmployeeBtn').data('id');
            const month = $('#attendance_month').val();
            const year = $('#attendance_year').val();

            loadEmployeeAttendance(employeeId, month, year);
        });

        // فتح مودال التعديل من مودال التفاصيل
        $('#editEmployeeBtn').on('click', function() {
            const employeeId = $(this).data('id');
            $('#viewEmployeeModal').modal('hide');

            setTimeout(function() {
                $('.edit-employee[data-id="' + employeeId + '"]').click();
            }, 500);
        });

        // فتح مودال تسجيل الحضور
        $(document).on('click', '.record-attendance', function() {
            const employeeId = $(this).data('id');
            $('#attendance_employee_id').val(employeeId);

            // ضبط التاريخ واوقات الدوام الافتراضية
            $('#attendance_date').val(getCurrentDateFormatted());
            $('#check_in').val(moment().format('HH:mm'));
            $('#check_out').val('');
            $('#attendance_notes').val('');

            $('#recordAttendanceModal').modal('show');
        });

        // تسجيل الحضور
        $('#submitAttendance').on('click', function() {
            const form = $('#attendanceForm')[0];
            if (!form.checkValidity()) {
                form.reportValidity();
                return;
            }

            const formData = new FormData(form);
            formData.append('action', 'record_attendance');

            // تنسيق التاريخ للـ API (إنجليزي بصيغة YYYY-MM-DD)
        const dateValue = $('#attendance_date').val();
        formData.delete('date');
        formData.append('date', dateValue.replace(/\/|\-/g, '-')); // التاريخ بالفعل بصيغة YYYY-MM-DD

            $.ajax({
                url: '../../api/employees.php',
                type: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                dataType: 'json',
                success: function(response) {
                    if (response.status === 'success') {
                        $('#recordAttendanceModal').modal('hide');
                        showAlert(response.message);
                    } else {
                        showAlert(response.message, 'danger');
                    }
                },
                error: function() {
                    showAlert('حدث خطأ أثناء تسجيل الحضور', 'danger');
                }
            });
        });

        // تهيئة Select2 للخدمات
        $('#services, #edit_services').select2({
            dir: "rtl",
            language: "ar",
            placeholder: "اختر الخدمات",
            width: '100%'
        });
        $(document).on('click', '.pay-salary', function() {
    const employeeId = $(this).data('id');
    const employeeName = $(this).data('name');

    // ضبط عنوان المودال وإعادة تعيين النماذج
    $('#paySalaryModal .employee-name').text(employeeName);
    $('#current_employee_id, #custom_employee_id').val(employeeId);

    // ضبط الشهر والسنة الحالية للنموذج الأول
    const currentDate = new Date();
    $('#current_month').val(currentDate.getMonth() + 1); // الشهر من 1-12
    $('#current_year').val(currentDate.getFullYear());

    // تحميل بيانات راتب الشهر الحالي
    loadCurrentMonthSalary(employeeId);

    // إعادة تعيين نموذج الشهر المحدد
    $('#customMonthSalaryDetails').hide();
    $('#saveSalaryPayment').prop('disabled', true);

    // عرض المودال
    $('#paySalaryModal').modal('show');
});

// تحميل بيانات راتب الشهر الحالي
function loadCurrentMonthSalary(employeeId) {
    const month = $('#current_month').val();
    const year = $('#current_year').val();

    // إظهار محتوى التحميل
    $('#currentMonthSalaryDetails').html(`
        <div class="text-center mb-3">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">جارٍ التحميل...</span>
            </div>
            <p class="mt-2">جارٍ حساب مستحقات الشهر الحالي...</p>
        </div>
    `);

    // إخفاء تفاصيل الدفع
    $('#currentMonthPaymentDetails').hide();

    $.ajax({
        url: '../../api/employees.php',
        type: 'POST',
        data: {
            action: 'get_employee_salary',
            employee_id: employeeId,
            month: month,
            year: year
        },
        dataType: 'json',
        success: function(response) {
            if (response.status === 'success') {
                const salary = response.salary;

                // تنسيق عرض الراتب
                let salaryHtml = '';

                if (salary && salary.id) {
                    // راتب موجود بالفعل
                    $('#current_salary_id').val(salary.id);

                    salaryHtml = `
                        <div class="card mb-3 border-0 shadow-sm">
                            <div class="card-body">
                                <div class="row mb-2">
                                    <div class="col-6 text-muted">عدد أيام الشهر:</div>
                                    <div class="col-6 text-start">${salary.days_in_month} يوم</div>
                                </div>
                                ${salary.calculated_days ? `
                                <div class="row mb-2">
                                    <div class="col-6 text-muted">عدد الأيام المحسوبة:</div>
                                    <div class="col-6 text-start">${salary.calculated_days} يوم</div>
                                </div>
                                ` : ''}
                                <div class="row mb-2">
                                    <div class="col-6 text-muted">الراتب الثابت:</div>
                                    <div class="col-6 text-start">${parseFloat(salary.fixed_amount).toFixed(2)} ${currencySymbol}</div>
                                </div>
                                <div class="row mb-2">
                                    <div class="col-6 text-muted">مبلغ العمولة:</div>
                                    <div class="col-6 text-start">${parseFloat(salary.commission_amount).toFixed(2)} ${currencySymbol}</div>
                                </div>
                                <div class="row mb-2">
                                    <div class="col-6 text-muted">المكافآت:</div>
                                    <div class="col-6 text-start">${parseFloat(salary.bonuses).toFixed(2)} ${currencySymbol}</div>
                                </div>
                                <div class="row mb-2">
                                    <div class="col-6 text-muted">الخصومات:</div>
                                    <div class="col-6 text-start">${parseFloat(salary.deductions).toFixed(2)} ${currencySymbol}</div>
                                </div>
                                <hr>
                                <div class="row fw-bold">
                                    <div class="col-6">إجمالي الراتب:</div>
                                    <div class="col-6 text-start fs-5 text-success">${parseFloat(salary.total_amount).toFixed(2)} ${currencySymbol}</div>
                                </div>
                            </div>
                        </div>
                    `;

                    // عرض حالة الدفع الحالية
                    if (salary.payment_status === 'paid') {
                        salaryHtml += `
                            <div class="alert alert-success">
                                <i class="fas fa-check-circle me-2"></i> تم دفع الراتب بتاريخ ${moment(salary.payment_date).format('YYYY/MM/DD')}
                            </div>
                        `;

                        // تعبئة بيانات الدفع
                        $('#current_payment_date').val(getCurrentDateFormatted());
                        $('#current_payment_status').val('paid');
                        $('#current_notes').val(salary.notes || '');

                        // عرض تفاصيل الدفع مع تعطيل زر الحفظ
                        $('#currentMonthPaymentDetails').show();
                        $('#saveSalaryPayment').prop('disabled', true);
                    } else {
                        salaryHtml += `
                            <div class="alert alert-warning">
                                <i class="fas fa-exclamation-circle me-2"></i> لم يتم دفع الراتب بعد
                            </div>
                            <div class="d-flex justify-content-end mb-3">
                                <button type="button" id="recalculateCurrentSalary" class="btn btn-outline-primary btn-sm" data-id="${salary.id}">
                                    <i class="fas fa-sync-alt me-1"></i> إعادة حساب الراتب
                                </button>
                            </div>
                        `;

                        // عرض تفاصيل الدفع مع تفعيل زر الحفظ
                        $('#currentMonthPaymentDetails').show();
                        $('#saveSalaryPayment').prop('disabled', false);
                    }
                } else {
                    // لا يوجد راتب محسوب بعد
                    salaryHtml = `
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i> لم يتم حساب راتب هذا الشهر بعد.
                        </div>
                        <button type="button" id="calculateCurrentSalary" class="btn btn-primary">
                            <i class="fas fa-calculator me-1"></i> حساب الراتب
                        </button>
                    `;
                }

                $('#currentMonthSalaryDetails').html(salaryHtml);
            } else {
                $('#currentMonthSalaryDetails').html(`
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i> ${response.message || 'حدث خطأ أثناء تحميل بيانات الراتب'}
                    </div>
                `);
            }
        },
        error: function() {
            $('#currentMonthSalaryDetails').html(`
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i> حدث خطأ أثناء الاتصال بالخادم
                </div>
            `);
        }
    });
}

// حساب راتب الشهر الحالي
$(document).on('click', '#calculateCurrentSalary', function() {
    const employeeId = $('#current_employee_id').val();
    const month = $('#current_month').val();
    const year = $('#current_year').val();

    // إظهار محتوى التحميل
    $('#currentMonthSalaryDetails').html(`
        <div class="text-center mb-3">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">جارٍ التحميل...</span>
            </div>
            <p class="mt-2">جارٍ حساب الراتب...</p>
        </div>
    `);

    $.ajax({
        url: '../../api/employees.php',
        type: 'POST',
        data: {
            action: 'calculate_salary',
            employee_id: employeeId,
            month: month,
            year: year
        },
        dataType: 'json',
        success: function(response) {
            if (response.status === 'success') {
                // إعادة تحميل بيانات الراتب
                loadCurrentMonthSalary(employeeId);
            } else {
                $('#currentMonthSalaryDetails').html(`
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i> ${response.message || 'حدث خطأ أثناء حساب الراتب'}
                    </div>
                `);
            }
        },
        error: function() {
            $('#currentMonthSalaryDetails').html(`
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i> حدث خطأ أثناء الاتصال بالخادم
                </div>
            `);
        }
    });
});

// حساب راتب الشهر المحدد
$('#calculateCustomSalary').on('click', function() {
    const employeeId = $('#custom_employee_id').val();
    const month = $('#custom_month').val();
    const year = $('#custom_year').val();

    // إخفاء تفاصيل الراتب
    $('#customMonthSalaryDetails').hide();
    $('#saveSalaryPayment').prop('disabled', true);

    // إظهار محتوى التحميل
    $('<div>', {
        class: 'text-center mb-3',
        html: `
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">جارٍ التحميل...</span>
            </div>
            <p class="mt-2">جارٍ حساب راتب ${$('#custom_month option:selected').text()} ${year}...</p>
        `
    }).insertAfter($(this));

    $(this).prop('disabled', true);

    $.ajax({
        url: '../../api/employees.php',
        type: 'POST',
        data: {
            action: 'calculate_salary',
            employee_id: employeeId,
            month: month,
            year: year
        },
        dataType: 'json',
        success: function(response) {
            $('.text-center.mb-3').remove();
            $('#calculateCustomSalary').prop('disabled', false);

            if (response.status === 'success') {
                const salary = response.salary;
                let salaryHtml = '';

                if (salary && salary.id) {
                    // راتب موجود بالفعل
                    $('#custom_salary_id').val(salary.id);

                    salaryHtml = `
                        <div class="card mb-3 border-0 shadow-sm">
                            <div class="card-body">
                                <div class="row mb-2">
                                    <div class="col-6 text-muted">عدد أيام الشهر:</div>
                                    <div class="col-6 text-start">${salary.days_in_month} يوم</div>
                                </div>
                                ${salary.calculated_days ? `
                                <div class="row mb-2">
                                    <div class="col-6 text-muted">عدد الأيام المحسوبة:</div>
                                    <div class="col-6 text-start">${salary.calculated_days} يوم</div>
                                </div>
                                ` : ''}
                                <div class="row mb-2">
                                    <div class="col-6 text-muted">الراتب الثابت:</div>
                                    <div class="col-6 text-start">${parseFloat(salary.fixed_amount).toFixed(2)} ${currencySymbol}</div>
                                </div>
                                <div class="row mb-2">
                                    <div class="col-6 text-muted">مبلغ العمولة:</div>
                                    <div class="col-6 text-start">${parseFloat(salary.commission_amount).toFixed(2)} ${currencySymbol}</div>
                                </div>
                                <div class="row mb-2">
                                    <div class="col-6 text-muted">المكافآت:</div>
                                    <div class="col-6 text-start">${parseFloat(salary.bonuses).toFixed(2)} ${currencySymbol}</div>
                                </div>
                                <div class="row mb-2">
                                    <div class="col-6 text-muted">الخصومات:</div>
                                    <div class="col-6 text-start">${parseFloat(salary.deductions).toFixed(2)} ${currencySymbol}</div>
                                </div>
                                <hr>
                                <div class="row fw-bold">
                                    <div class="col-6">إجمالي الراتب:</div>
                                    <div class="col-6 text-start fs-5 text-success">${parseFloat(salary.total_amount).toFixed(2)} ${currencySymbol}</div>
                                </div>
                            </div>
                        </div>
                    `;

                    // عرض حالة الدفع الحالية
                    if (salary.payment_status === 'paid') {
                        salaryHtml += `
                            <div class="alert alert-success">
                                <i class="fas fa-check-circle me-2"></i> تم دفع الراتب بتاريخ ${moment(salary.payment_date).format('YYYY/MM/DD')}
                            </div>
                        `;

                        // تعبئة بيانات الدفع
                        $('#custom_payment_date').val(moment(salary.payment_date).format('YYYY-MM-DD'));
                        $('#custom_payment_status').val('paid');
                        $('#custom_notes').val(salary.notes || '');

                        // عرض تفاصيل الراتب مع تعطيل زر الحفظ
                        $('#customMonthSalaryDetails').show();
                        $('#saveSalaryPayment').prop('disabled', true);
                    } else {
                        salaryHtml += `
                            <div class="alert alert-warning">
                                <i class="fas fa-exclamation-circle me-2"></i> لم يتم دفع الراتب بعد
                            </div>
                            <div class="d-flex justify-content-end mb-3">
                                <button type="button" id="recalculateCustomSalary" class="btn btn-outline-primary btn-sm" data-id="${salary.id}" data-month="${month}" data-year="${year}">
                                    <i class="fas fa-sync-alt me-1"></i> إعادة حساب الراتب
                                </button>
                            </div>
                        `;

                        // عرض تفاصيل الراتب مع تفعيل زر الحفظ
                        $('#customMonthSalaryDetails').show();
                        $('#saveSalaryPayment').prop('disabled', false);
                    }
                } else {
                    // لا يوجد راتب محسوب - عرض زر لحساب الراتب
                    salaryHtml = `
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i> لم يتم حساب راتب ${$('#custom_month option:selected').text()} ${year} بعد.
                        </div>
                        <button type="button" id="calculateSpecificSalary" class="btn btn-primary" data-month="${month}" data-year="${year}">
                            <i class="fas fa-calculator me-1"></i> حساب الراتب
                        </button>
                    `;
                }

                $('#customMonthSalaryDetails').show();
                $('#customMonthSalaryDetails .salary-details-content').html(salaryHtml);
            } else {
                // خطأ في الاستجابة
                $('#customMonthSalaryDetails').show();
                $('#customMonthSalaryDetails .salary-details-content').html(`
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i> ${response.message || 'حدث خطأ أثناء تحميل بيانات الراتب'}
                    </div>
                `);
            }
        },
        error: function() {
            $('.text-center.mb-3').remove();
            $('#calculateCustomSalary').prop('disabled', false);

            $('#customMonthSalaryDetails').show();
            $('#customMonthSalaryDetails .salary-details-content').html(`
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i> حدث خطأ أثناء الاتصال بالخادم
                </div>
            `);
        }
    });
});

// إعادة حساب راتب الشهر الحالي
$(document).on('click', '#recalculateCurrentSalary', function() {
    const salaryId = $(this).data('id');
    const employeeId = $('#current_employee_id').val();
    const month = $('#current_month').val();
    const year = $('#current_year').val();

    // إظهار محتوى التحميل
    $('#currentMonthSalaryDetails').html(`
        <div class="text-center mb-3">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">جارِ التحميل...</span>
            </div>
            <p class="mt-2">جارِ إعادة حساب الراتب...</p>
        </div>
    `);

    // إخفاء تفاصيل الدفع
    $('#currentMonthPaymentDetails').hide();

    $.ajax({
        url: '../../api/employees.php',
        type: 'POST',
        data: {
            action: 'calculate_salary',
            employee_id: employeeId,
            month: month,
            year: year,
            recalculate: true,
            salary_id: salaryId
        },
        dataType: 'json',
        success: function(response) {
            if (response.status === 'success') {
                // إعادة تحميل بيانات الراتب
                loadCurrentMonthSalary(employeeId);
                showAlert('تم إعادة حساب الراتب بنجاح', 'success');
            } else {
                $('#currentMonthSalaryDetails').html(`
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i> ${response.message || 'حدث خطأ أثناء إعادة حساب الراتب'}
                    </div>
                `);
            }
        },
        error: function() {
            $('#currentMonthSalaryDetails').html(`
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i> حدث خطأ أثناء الاتصال بالخادم
                </div>
            `);
        }
    });
});

// حساب راتب شهر محدد
$(document).on('click', '#calculateSpecificSalary', function() {
    const employeeId = $('#custom_employee_id').val();
    const month = $(this).data('month');
    const year = $(this).data('year');

    // إظهار محتوى التحميل
    $(this).replaceWith(`
        <div class="text-center mb-3">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">جارٍ التحميل...</span>
            </div>
            <p class="mt-2">جارٍ حساب الراتب...</p>
        </div>
    `);

    $.ajax({
        url: '../../api/employees.php',
        type: 'POST',
        data: {
            action: 'calculate_salary',
            employee_id: employeeId,
            month: month,
            year: year
        },
        dataType: 'json',
        success: function(response) {
            // إعادة تحميل الشاشة
            $('#calculateCustomSalary').click();
        },
        error: function() {
            $('.text-center.mb-3').remove();
            $('#customMonthSalaryDetails .salary-details-content').append(`
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i> حدث خطأ أثناء الاتصال بالخادم
                </div>
            `);
        }
    });
});

// تبديل التبويبات (إعادة تعيين زر الحفظ)
$('#salaryTabs button').on('shown.bs.tab', function (e) {
    // تحديد ما إذا كان يجب تمكين زر الحفظ بناءً على التبويب النشط
    const activeTabId = $(e.target).attr('id');

    if (activeTabId === 'current-month-tab') {
        // تحقق مما إذا كان يمكن دفع راتب الشهر الحالي
        const paymentStatus = $('#current_payment_status').val();
        $('#saveSalaryPayment').prop('disabled', paymentStatus === 'paid');
    } else {
        // تحقق مما إذا كانت تفاصيل الراتب معروضة في التبويب المخصص
        const isCustomTabDetailsVisible = $('#customMonthSalaryDetails').is(':visible');
        const paymentStatus = $('#custom_payment_status').val();
        $('#saveSalaryPayment').prop('disabled', !isCustomTabDetailsVisible || paymentStatus === 'paid');
    }
});
// حفظ دفع الراتب
$('#saveSalaryPayment').on('click', function() {
    // تحديد التبويب النشط
    const activeTab = $('#salaryTabs .nav-link.active').attr('id');
    let formData, actionType;

    if (activeTab === 'current-month-tab') {
        // راتب الشهر الحالي
        const form = $('#currentMonthSalaryForm')[0];
        if (!form.checkValidity()) {
            form.reportValidity();
            return;
        }

        formData = new FormData(form);
        const paymentDate = $('#current_payment_date').val();
            formData.delete('payment_date');
            formData.append('payment_date', paymentDate); // التاريخ بالفعل بصيغة YYYY-MM-DD

        // معرف الراتب المحسوب مسبقًا
        const salaryId = $('#current_salary_id').val();

        if (salaryId) {
            // تحديث حالة الراتب
            actionType = 'update_salary_status';
            formData.append('salary_id', salaryId);
        } else {
            // إذا لم يكن هناك راتب محسوب، نعرض رسالة خطأ
            showAlert('يجب حساب الراتب أولاً قبل الدفع', 'danger');
            return;
        }
    } else {
        // راتب شهر محدد
        const form = $('#customMonthSalaryForm')[0];
        if (!form.checkValidity()) {
            form.reportValidity();
            return;
        }

        formData = new FormData(form);

        // معرف الراتب المحسوب مسبقًا
        const salaryId = $('#custom_salary_id').val();

        if (salaryId) {
            // تحديث حالة الراتب
            actionType = 'update_salary_status';
            formData.append('salary_id', salaryId);
        } else {
            // إذا لم يكن هناك راتب محسوب، نعرض رسالة خطأ
            showAlert('يجب حساب الراتب أولاً قبل الدفع', 'danger');
            return;
        }
    }

    // إضافة نوع الإجراء
    formData.append('action', actionType);

    // تعطيل زر الحفظ لمنع النقر المتكرر
    $(this).prop('disabled', true).html('<i class="fas fa-spinner fa-spin me-1"></i> جاري الحفظ...');

    $.ajax({
        url: '../../api/employees.php',
        type: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        dataType: 'json',
        success: function(response) {
            if (response.status === 'success') {
                // إغلاق المودال وإظهار رسالة نجاح
                $('#paySalaryModal').modal('hide');
                showAlert(response.message || 'تم دفع الراتب بنجاح');

                // إعادة تحميل الجدول
                employeesTable.ajax.reload();
            } else {
                showAlert(response.message || 'حدث خطأ أثناء حفظ دفع الراتب', 'danger');
                // إعادة تفعيل زر الحفظ
                $('#saveSalaryPayment').prop('disabled', false).html('<i class="fas fa-money-bill-wave me-1"></i> حفظ');
            }
        },
        error: function() {
            showAlert('حدث خطأ أثناء الاتصال بالخادم', 'danger');
            // إعادة تفعيل زر الحفظ
            $('#saveSalaryPayment').prop('disabled', false).html('<i class="fas fa-money-bill-wave me-1"></i> حفظ');
        }
    });
});

// إعادة حساب راتب شهر محدد
$(document).on('click', '#recalculateCustomSalary', function() {
    const salaryId = $(this).data('id');
    const employeeId = $('#custom_employee_id').val();
    const month = $(this).data('month');
    const year = $(this).data('year');

    // إظهار محتوى التحميل
    $('#customMonthSalaryDetails .salary-details-content').html(`
        <div class="text-center mb-3">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">جارِ التحميل...</span>
            </div>
            <p class="mt-2">جارِ إعادة حساب الراتب...</p>
        </div>
    `);

    // إخفاء تفاصيل الدفع
    $('#customMonthPaymentDetails').hide();

    $.ajax({
        url: '../../api/employees.php',
        type: 'POST',
        data: {
            action: 'calculate_salary',
            employee_id: employeeId,
            month: month,
            year: year,
            recalculate: true,
            salary_id: salaryId
        },
        dataType: 'json',
        success: function(response) {
            if (response.status === 'success') {
                // إعادة تحميل بيانات الراتب
                $('#calculateCustomSalary').click();
                showAlert('تم إعادة حساب الراتب بنجاح', 'success');
            } else {
                $('#customMonthSalaryDetails .salary-details-content').html(`
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i> ${response.message || 'حدث خطأ أثناء إعادة حساب الراتب'}
                    </div>
                `);
            }
        },
        error: function() {
            $('#customMonthSalaryDetails .salary-details-content').html(`
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i> حدث خطأ أثناء الاتصال بالخادم
                </div>
            `);
        }
    });
});

// إضافة دالة مساعدة لإنشاء كلمة مرور عشوائية
$(document).on('click', '#generatePasswordBtn', function() {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let password = '';
    for (let i = 0; i < 8; i++) {
        password += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    $('#edit_password').val(password);
});

// تهيئة تقويم التاريخ في مودال دفع الراتب
  function initSalaryDatePickers() {
        $('#current_payment_date, #custom_payment_date').daterangepicker(datePickerSettings);
    }

// استدعاء دالة تهيئة تقويم التاريخ عند فتح مودال دفع الراتب
$('#paySalaryModal').on('shown.bs.modal', function() {
    initSalaryDatePickers();
});
function getCurrentDateFormatted() {
        return moment().format('YYYY-MM-DD');
    }
// إضافة المستمعات لتغيير حالة الدفع
$('#current_payment_status, #custom_payment_status').on('change', function() {
    const paymentStatus = $(this).val();
    const activeTab = $('#salaryTabs .nav-link.active').attr('id');

    // تفعيل/تعطيل زر الحفظ بناءً على حالة الدفع
    if (activeTab === 'current-month-tab' && $('#current_salary_id').val()) {
        $('#saveSalaryPayment').prop('disabled', paymentStatus === 'paid');
    } else if (activeTab === 'custom-month-tab' && $('#custom_salary_id').val()) {
        $('#saveSalaryPayment').prop('disabled', paymentStatus === 'paid');
    }
});

// إضافة مكافأة للموظف
$(document).on('click', '.add-bonus', function() {
    const employeeId = $(this).data('id');
    const employeeName = $(this).data('name');

    // ضبط عنوان المودال وإعادة تعيين النموذج
    $('#addBonusModal .employee-name').text(employeeName);
    $('#bonus_employee_id').val(employeeId);
    $('#bonus_amount').val('');
    $('#bonus_notes').val('');

    // عرض المودال
    $('#addBonusModal').modal('show');
});

// حفظ المكافأة
$('#saveBonusBtn').on('click', function() {
    const form = $('#addBonusForm')[0];
    if (!form.checkValidity()) {
        form.reportValidity();
        return;
    }

    // جمع البيانات
    const employeeId = $('#bonus_employee_id').val();
    const month = $('#bonus_month').val();
    const year = $('#bonus_year').val();
    const bonusAmount = $('#bonus_amount').val();
    const notes = $('#bonus_notes').val();

    // تعطيل زر الحفظ لمنع النقر المتكرر
    $(this).prop('disabled', true).html('<i class="fas fa-spinner fa-spin me-1"></i> جاري الحفظ...');

    // أولاً، نحصل على الراتب الحالي للشهر
    $.ajax({
        url: '../../api/employees.php',
        type: 'POST',
        data: {
            action: 'get_employee_salary',
            employee_id: employeeId,
            month: month,
            year: year
        },
        dataType: 'json',
        success: function(response) {
            if (response.status === 'success') {
                const salary = response.salary;

                if (salary && salary.id) {
                    // تحديث بيانات الراتب مع إضافة المكافأة
                    const newBonuses = parseFloat(salary.bonuses || 0) + parseFloat(bonusAmount);
                    const newTotal = parseFloat(salary.total_amount || 0) + parseFloat(bonusAmount);

                    // حفظ التحديث
                    $.ajax({
                        url: '../../api/employees.php',
                        type: 'POST',
                        data: {
                            action: 'update_salary',
                            salary_id: salary.id,
                            fixed_amount: salary.fixed_amount,
                            commission_amount: salary.commission_amount,
                            bonuses: newBonuses,
                            deductions: salary.deductions,
                            total_amount: newTotal,
                            payment_status: salary.payment_status,
                            notes: notes.length > 0 ? (salary.notes ? salary.notes + ' | ' + notes : notes) : salary.notes
                        },
                        dataType: 'json',
                        success: function(updateResponse) {
                            // إعادة تفعيل زر الحفظ
                            $('#saveBonusBtn').prop('disabled', false).html('<i class="fas fa-save me-1"></i> حفظ المكافأة');

                            if (updateResponse.status === 'success') {
                                // إغلاق المودال وإظهار رسالة نجاح
                                $('#addBonusModal').modal('hide');
                                showAlert('تم إضافة المكافأة بنجاح للموظف');
                            } else {
                                showAlert(updateResponse.message || 'حدث خطأ أثناء حفظ المكافأة', 'danger');
                            }
                        },
                        error: function() {
                            // إعادة تفعيل زر الحفظ
                            $('#saveBonusBtn').prop('disabled', false).html('<i class="fas fa-save me-1"></i> حفظ المكافأة');
                            showAlert('حدث خطأ أثناء الاتصال بالخادم', 'danger');
                        }
                    });
                } else {
                    // إذا لم يكن هناك راتب محسوب، نقوم بحساب الراتب أولاً
                    $.ajax({
                        url: '../../api/employees.php',
                        type: 'POST',
                        data: {
                            action: 'calculate_salary',
                            employee_id: employeeId,
                            month: month,
                            year: year
                        },
                        dataType: 'json',
                        success: function(calcResponse) {
                            if (calcResponse.status === 'success') {
                                // إعادة المحاولة بعد حساب الراتب
                                $('#saveBonusBtn').prop('disabled', false).click();
                            } else {
                                // إعادة تفعيل زر الحفظ
                                $('#saveBonusBtn').prop('disabled', false).html('<i class="fas fa-save me-1"></i> حفظ المكافأة');
                                showAlert('لم نتمكن من حساب الراتب. ' + (calcResponse.message || ''), 'danger');
                            }
                        },
                        error: function() {
                            // إعادة تفعيل زر الحفظ
                            $('#saveBonusBtn').prop('disabled', false).html('<i class="fas fa-save me-1"></i> حفظ المكافأة');
                            showAlert('حدث خطأ أثناء الاتصال بالخادم', 'danger');
                        }
                    });
                }
            } else {
                // إعادة تفعيل زر الحفظ
                $('#saveBonusBtn').prop('disabled', false).html('<i class="fas fa-save me-1"></i> حفظ المكافأة');
                showAlert('حدث خطأ أثناء جلب بيانات الراتب. ' + (response.message || ''), 'danger');
            }
        },
        error: function() {
            // إعادة تفعيل زر الحفظ
            $('#saveBonusBtn').prop('disabled', false).html('<i class="fas fa-save me-1"></i> حفظ المكافأة');
            showAlert('حدث خطأ أثناء الاتصال بالخادم', 'danger');
        }
    });
});

// إضافة خصم للموظف
$(document).on('click', '.add-deduction', function() {
    const employeeId = $(this).data('id');
    const employeeName = $(this).data('name');

    // ضبط عنوان المودال وإعادة تعيين النموذج
    $('#addDeductionModal .employee-name').text(employeeName);
    $('#deduction_employee_id').val(employeeId);
    $('#deduction_amount').val('');
    $('#deduction_notes').val('');

    // عرض المودال
    $('#addDeductionModal').modal('show');
});

// إزالة خصم للموظف
$(document).on('click', '.remove-deduction', function() {
    const employeeId = $(this).data('id');
    const employeeName = $(this).data('name');

    // ضبط عنوان المودال وإعادة تعيين النموذج
    $('#removeDeductionModal .employee-name').text(employeeName);
    $('#remove_deduction_employee_id').val(employeeId);
    $('#remove_deduction_amount').val('');
    $('#remove_deduction_notes').val('');

    // عرض المودال
    $('#removeDeductionModal').modal('show');
});

// إزالة مكافأة للموظف
$(document).on('click', '.remove-bonus', function() {
    const employeeId = $(this).data('id');
    const employeeName = $(this).data('name');

    // ضبط عنوان المودال وإعادة تعيين النموذج
    $('#removeBonusModal .employee-name').text(employeeName);
    $('#remove_bonus_employee_id').val(employeeId);
    $('#remove_bonus_amount').val('');
    $('#remove_bonus_notes').val('');

    // عرض المودال
    $('#removeBonusModal').modal('show');
});

// حفظ الخصم
$('#saveDeductionBtn').on('click', function() {
    const form = $('#addDeductionForm')[0];
    if (!form.checkValidity()) {
        form.reportValidity();
        return;
    }

    // جمع البيانات
    const employeeId = $('#deduction_employee_id').val();
    const month = $('#deduction_month').val();
    const year = $('#deduction_year').val();
    const deductionAmount = $('#deduction_amount').val();
    const notes = $('#deduction_notes').val();

    // تعطيل زر الحفظ لمنع النقر المتكرر
    $(this).prop('disabled', true).html('<i class="fas fa-spinner fa-spin me-1"></i> جاري الحفظ...');

    // أولاً، نحصل على الراتب الحالي للشهر
    $.ajax({
        url: '../../api/employees.php',
        type: 'POST',
        data: {
            action: 'get_employee_salary',
            employee_id: employeeId,
            month: month,
            year: year
        },
        dataType: 'json',
        success: function(response) {
            if (response.status === 'success') {
                const salary = response.salary;

                if (salary && salary.id) {
                    // تحديث بيانات الراتب مع إضافة الخصم
                    const newDeductions = parseFloat(salary.deductions || 0) + parseFloat(deductionAmount);
                    const newTotal = parseFloat(salary.total_amount || 0) - parseFloat(deductionAmount);

                    // حفظ التحديث
                    $.ajax({
                        url: '../../api/employees.php',
                        type: 'POST',
                        data: {
                            action: 'update_salary',
                            salary_id: salary.id,
                            fixed_amount: salary.fixed_amount,
                            commission_amount: salary.commission_amount,
                            bonuses: salary.bonuses,
                            deductions: newDeductions,
                            total_amount: newTotal >= 0 ? newTotal : 0, // تأكد من أن المجموع لا يكون سالباً
                            payment_status: salary.payment_status,
                            notes: notes.length > 0 ? (salary.notes ? salary.notes + ' | ' + notes : notes) : salary.notes
                        },
                        dataType: 'json',
                        success: function(updateResponse) {
                            // إعادة تفعيل زر الحفظ
                            $('#saveDeductionBtn').prop('disabled', false).html('<i class="fas fa-save me-1"></i> حفظ الخصم');

                            if (updateResponse.status === 'success') {
                                // إغلاق المودال وإظهار رسالة نجاح
                                $('#addDeductionModal').modal('hide');
                                showAlert('تم إضافة الخصم بنجاح للموظف');
                            } else {
                                showAlert(updateResponse.message || 'حدث خطأ أثناء حفظ الخصم', 'danger');
                            }
                        },
                        error: function() {
                            // إعادة تفعيل زر الحفظ
                            $('#saveDeductionBtn').prop('disabled', false).html('<i class="fas fa-save me-1"></i> حفظ الخصم');
                            showAlert('حدث خطأ أثناء الاتصال بالخادم', 'danger');
                        }
                    });
                } else {
                    // إذا لم يكن هناك راتب محسوب، نقوم بحساب الراتب أولاً
                    $.ajax({
                        url: '../../api/employees.php',
                        type: 'POST',
                        data: {
                            action: 'calculate_salary',
                            employee_id: employeeId,
                            month: month,
                            year: year
                        },
                        dataType: 'json',
                        success: function(calcResponse) {
                            if (calcResponse.status === 'success') {
                                // إعادة المحاولة بعد حساب الراتب
                                $('#saveDeductionBtn').prop('disabled', false).click();
                            } else {
                                // إعادة تفعيل زر الحفظ
                                $('#saveDeductionBtn').prop('disabled', false).html('<i class="fas fa-save me-1"></i> حفظ الخصم');
                                showAlert('لم نتمكن من حساب الراتب. ' + (calcResponse.message || ''), 'danger');
                            }
                        },
                        error: function() {
                            // إعادة تفعيل زر الحفظ
                            $('#saveDeductionBtn').prop('disabled', false).html('<i class="fas fa-save me-1"></i> حفظ الخصم');
                            showAlert('حدث خطأ أثناء الاتصال بالخادم', 'danger');
                        }
                    });
                }
            } else {
                // إعادة تفعيل زر الحفظ
                $('#saveDeductionBtn').prop('disabled', false).html('<i class="fas fa-save me-1"></i> حفظ الخصم');
                showAlert('حدث خطأ أثناء جلب بيانات الراتب. ' + (response.message || ''), 'danger');
            }
        },
        error: function() {
            // إعادة تفعيل زر الحفظ
            $('#saveDeductionBtn').prop('disabled', false).html('<i class="fas fa-save me-1"></i> حفظ الخصم');
            showAlert('حدث خطأ أثناء الاتصال بالخادم', 'danger');
        }
    });
});

// حفظ إزالة الخصم
$('#removeDeductionBtn').on('click', function() {
    const form = $('#removeDeductionForm')[0];
    if (!form.checkValidity()) {
        form.reportValidity();
        return;
    }

    // جمع البيانات
    const employeeId = $('#remove_deduction_employee_id').val();
    const month = $('#remove_deduction_month').val();
    const year = $('#remove_deduction_year').val();
    const deductionAmount = $('#remove_deduction_amount').val();
    const notes = $('#remove_deduction_notes').val();

    // تعطيل زر الحفظ لمنع النقر المتكرر
    $(this).prop('disabled', true).html('<i class="fas fa-spinner fa-spin me-1"></i> جاري المعالجة...');

    // أولاً، نحصل على الراتب الحالي للشهر
    $.ajax({
        url: '../../api/employees.php',
        type: 'POST',
        data: {
            action: 'get_employee_salary',
            employee_id: employeeId,
            month: month,
            year: year
        },
        dataType: 'json',
        success: function(response) {
            if (response.status === 'success') {
                const salary = response.salary;

                if (salary && salary.id) {
                    // التحقق من أن قيمة الخصم المراد إزالته لا تتجاوز إجمالي الخصومات الحالية
                    const currentDeductions = parseFloat(salary.deductions || 0);
                    const removeAmount = parseFloat(deductionAmount);

                    if (removeAmount > currentDeductions) {
                        // إعادة تفعيل زر الحفظ
                        $('#removeDeductionBtn').prop('disabled', false).html('<i class="fas fa-minus-circle me-1"></i> إزالة الخصم');
                        showAlert('قيمة الخصم المراد إزالته تتجاوز إجمالي الخصومات الحالية (' + currentDeductions.toFixed(2) + ' ' + currencySymbol + ')', 'danger');
                        return;
                    }

                    // تحديث بيانات الراتب مع إزالة الخصم
                    const newDeductions = currentDeductions - removeAmount;
                    const newTotal = parseFloat(salary.total_amount || 0) + removeAmount;

                    // حفظ التحديث
                    $.ajax({
                        url: '../../api/employees.php',
                        type: 'POST',
                        data: {
                            action: 'update_salary',
                            salary_id: salary.id,
                            fixed_amount: salary.fixed_amount,
                            commission_amount: salary.commission_amount,
                            bonuses: salary.bonuses,
                            deductions: newDeductions,
                            total_amount: newTotal,
                            payment_status: salary.payment_status,
                            notes: notes.length > 0 ? (salary.notes ? salary.notes + ' | ' + notes : notes) : salary.notes
                        },
                        dataType: 'json',
                        success: function(updateResponse) {
                            // إعادة تفعيل زر الحفظ
                            $('#removeDeductionBtn').prop('disabled', false).html('<i class="fas fa-minus-circle me-1"></i> إزالة الخصم');

                            if (updateResponse.status === 'success') {
                                // إغلاق المودال وإظهار رسالة نجاح
                                $('#removeDeductionModal').modal('hide');
                                showAlert('تم إزالة الخصم بنجاح من الموظف');
                            } else {
                                showAlert(updateResponse.message || 'حدث خطأ أثناء إزالة الخصم', 'danger');
                            }
                        },
                        error: function() {
                            // إعادة تفعيل زر الحفظ
                            $('#removeDeductionBtn').prop('disabled', false).html('<i class="fas fa-minus-circle me-1"></i> إزالة الخصم');
                            showAlert('حدث خطأ أثناء الاتصال بالخادم', 'danger');
                        }
                    });
                } else {
                    // إعادة تفعيل زر الحفظ
                    $('#removeDeductionBtn').prop('disabled', false).html('<i class="fas fa-minus-circle me-1"></i> إزالة الخصم');
                    showAlert('لا يوجد راتب محسوب لهذا الشهر', 'warning');
                }
            } else {
                // إعادة تفعيل زر الحفظ
                $('#removeDeductionBtn').prop('disabled', false).html('<i class="fas fa-minus-circle me-1"></i> إزالة الخصم');
                showAlert('حدث خطأ أثناء جلب بيانات الراتب. ' + (response.message || ''), 'danger');
            }
        },
        error: function() {
            // إعادة تفعيل زر الحفظ
            $('#removeDeductionBtn').prop('disabled', false).html('<i class="fas fa-minus-circle me-1"></i> إزالة الخصم');
            showAlert('حدث خطأ أثناء الاتصال بالخادم', 'danger');
        }
    });
});

// حفظ إزالة المكافأة
$('#removeBonusBtn').on('click', function() {
    const form = $('#removeBonusForm')[0];
    if (!form.checkValidity()) {
        form.reportValidity();
        return;
    }

    // جمع البيانات
    const employeeId = $('#remove_bonus_employee_id').val();
    const month = $('#remove_bonus_month').val();
    const year = $('#remove_bonus_year').val();
    const bonusAmount = $('#remove_bonus_amount').val();
    const notes = $('#remove_bonus_notes').val();

    // تعطيل زر الحفظ لمنع النقر المتكرر
    $(this).prop('disabled', true).html('<i class="fas fa-spinner fa-spin me-1"></i> جاري المعالجة...');

    // أولاً، نحصل على الراتب الحالي للشهر
    $.ajax({
        url: '../../api/employees.php',
        type: 'POST',
        data: {
            action: 'get_employee_salary',
            employee_id: employeeId,
            month: month,
            year: year
        },
        dataType: 'json',
        success: function(response) {
            if (response.status === 'success') {
                const salary = response.salary;

                if (salary && salary.id) {
                    // التحقق من أن قيمة المكافأة المراد إزالتها لا تتجاوز إجمالي المكافآت الحالية
                    const currentBonuses = parseFloat(salary.bonuses || 0);
                    const removeAmount = parseFloat(bonusAmount);

                    if (removeAmount > currentBonuses) {
                        // إعادة تفعيل زر الحفظ
                        $('#removeBonusBtn').prop('disabled', false).html('<i class="fas fa-minus-circle me-1"></i> إزالة المكافأة');
                        showAlert('قيمة المكافأة المراد إزالتها تتجاوز إجمالي المكافآت الحالية (' + currentBonuses.toFixed(2) + ' ' + currencySymbol + ')', 'danger');
                        return;
                    }

                    // تحديث بيانات الراتب مع إزالة المكافأة
                    const newBonuses = currentBonuses - removeAmount;
                    const newTotal = parseFloat(salary.total_amount || 0) - removeAmount;

                    // حفظ التحديث
                    $.ajax({
                        url: '../../api/employees.php',
                        type: 'POST',
                        data: {
                            action: 'update_salary',
                            salary_id: salary.id,
                            fixed_amount: salary.fixed_amount,
                            commission_amount: salary.commission_amount,
                            bonuses: newBonuses,
                            deductions: salary.deductions,
                            total_amount: newTotal >= 0 ? newTotal : 0, // تأكد من أن المجموع لا يكون سالباً
                            payment_status: salary.payment_status,
                            notes: notes.length > 0 ? (salary.notes ? salary.notes + ' | ' + notes : notes) : salary.notes
                        },
                        dataType: 'json',
                        success: function(updateResponse) {
                            // إعادة تفعيل زر الحفظ
                            $('#removeBonusBtn').prop('disabled', false).html('<i class="fas fa-minus-circle me-1"></i> إزالة المكافأة');

                            if (updateResponse.status === 'success') {
                                // إغلاق المودال وإظهار رسالة نجاح
                                $('#removeBonusModal').modal('hide');
                                showAlert('تم إزالة المكافأة بنجاح من الموظف');
                            } else {
                                showAlert(updateResponse.message || 'حدث خطأ أثناء إزالة المكافأة', 'danger');
                            }
                        },
                        error: function() {
                            // إعادة تفعيل زر الحفظ
                            $('#removeBonusBtn').prop('disabled', false).html('<i class="fas fa-minus-circle me-1"></i> إزالة المكافأة');
                            showAlert('حدث خطأ أثناء الاتصال بالخادم', 'danger');
                        }
                    });
                } else {
                    // إعادة تفعيل زر الحفظ
                    $('#removeBonusBtn').prop('disabled', false).html('<i class="fas fa-minus-circle me-1"></i> إزالة المكافأة');
                    showAlert('لا يوجد راتب محسوب لهذا الشهر', 'warning');
                }
            } else {
                // إعادة تفعيل زر الحفظ
                $('#removeBonusBtn').prop('disabled', false).html('<i class="fas fa-minus-circle me-1"></i> إزالة المكافأة');
                showAlert('حدث خطأ أثناء جلب بيانات الراتب. ' + (response.message || ''), 'danger');
            }
        },
        error: function() {
            // إعادة تفعيل زر الحفظ
            $('#removeBonusBtn').prop('disabled', false).html('<i class="fas fa-minus-circle me-1"></i> إزالة المكافأة');
            showAlert('حدث خطأ أثناء الاتصال بالخادم', 'danger');
        }
    });
});

// دالة لعرض رسائل التنبيه
function showAlert(message, type = 'success') {
    // إنشاء عنصر التنبيه
    const alertHtml = `
        <div class="alert alert-${type} alert-dismissible fade show" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="إغلاق"></button>
        </div>
    `;

    // عرض التنبيه في أعلى الصفحة
    const alertContainer = $('#alertsContainer');
    if (alertContainer.length) {
        alertContainer.html(alertHtml);
    } else {
        // إذا لم يكن هناك حاوية للتنبيهات، قم بإنشاء واحدة
        $('<div>', {
            id: 'alertsContainer',
            class: 'position-fixed top-0 start-50 translate-middle-x p-3',
            style: 'z-index: 5000; width: 80%;',
            html: alertHtml
        }).appendTo('body');
    }

    // إخفاء التنبيه تلقائيًا بعد 5 ثوانٍ
    setTimeout(function() {
        $('.alert').alert('close');
    }, 5000);
}

// دالة لعرض نافذة تأكيد
function confirmAction(message, callback) {
    // إنشاء مودال التأكيد
    if (!$('#confirmModal').length) {
        const confirmModal = `
            <div class="modal fade" id="confirmModal" tabindex="-1" aria-labelledby="confirmModalLabel" aria-hidden="true">
                <div class="modal-dialog modal-sm">
                    <div class="modal-content">
                        <div class="modal-header bg-warning">
                            <h5 class="modal-title" id="confirmModalLabel">تأكيد العملية</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="إغلاق"></button>
                        </div>
                        <div class="modal-body">
                            <p id="confirmMessage"></p>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                            <button type="button" class="btn btn-danger" id="confirmActionBtn">تأكيد</button>
                        </div>
                    </div>
                </div>
            </div>
        `;
        $('body').append(confirmModal);
    }

    // تعيين رسالة التأكيد
    $('#confirmMessage').text(message);

    // تعيين المستمع لزر التأكيد
    $('#confirmActionBtn').off('click').on('click', function() {
        $('#confirmModal').modal('hide');
        callback();
    });

    // عرض مودال التأكيد
    $('#confirmModal').modal('show');
}
        // تهيئة عناصر التاريخ
        $('.date-picker').daterangepicker({

            singleDatePicker: true,
            showDropdowns: true,
            locale: {
                format: 'YYYY/MM/DD',
                applyLabel: 'تطبيق',
                cancelLabel: 'إلغاء',
                fromLabel: 'من',
                toLabel: 'إلى',
                customRangeLabel: 'مخصص',
                daysOfWeek: ['أح', 'إث', 'ثل', 'أر', 'خم', 'جم', 'سب'],
                monthNames: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو', 'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'],
                firstDay: 0
            }
        });
    }
);
</script>
