<?php
/**
 * كلاس إشعارات الفواتير للمدراء
 * يتعامل مع إرسال إشعارات الفواتير للمدراء
 */

class AdminInvoiceNotification {
    private $db;
    private $settings;
    private $whatsappService;
    private $logFile;

    /**
     * إنشاء كائن جديد من إشعارات الفواتير للمدراء
     *
     * @param Database $db كائن قاعدة البيانات
     */
    public function __construct($db) {
        $this->db = $db;

        // إنشاء كائن الإعدادات
        $this->settings = new Settings($db);

        // إنشاء كائن خدمة WhatsApp
        $this->whatsappService = new WhatsAppAutomation($db);

        // تحديد ملف السجل
        $this->logFile = __DIR__ . '/../../logs/admin_invoice_notifications.log';

        // التأكد من وجود مجلد السجلات
        if (!is_dir(dirname($this->logFile))) {
            mkdir(dirname($this->logFile), 0755, true);
        }
    }

    /**
     * إرسال إشعار فاتورة جديدة للمدراء
     *
     * @param int $invoiceId معرف الفاتورة
     * @return bool نجاح أو فشل الإرسال
     */
    public function sendNewInvoiceNotification($invoiceId) {
        try {
            // التحقق من تفعيل إشعارات المدير وإشعارات الفواتير الجديدة
            $notificationSettings = $this->getNotificationSettings();

            // تسجيل الإعدادات للتصحيح
            $this->log("إعدادات الإشعارات: " . print_r($notificationSettings, true));

            // التحقق من تفعيل إشعارات المدير وإشعارات الفواتير الجديدة
            if (!$this->isAdminNotificationsEnabled($notificationSettings) ||
                !$this->isNewInvoiceNotificationsEnabled($notificationSettings)) {
                $this->log("إشعارات الفواتير الجديدة للمدراء غير مفعلة. لم يتم إرسال إشعار للفاتورة رقم $invoiceId");
                return false;
            }

            // الحصول على أرقام هواتف المدراء
            $adminPhoneNumbers = $this->getAdminPhoneNumbers($notificationSettings);
            if (empty($adminPhoneNumbers)) {
                $this->log("لا توجد أرقام هواتف للمدراء. لم يتم إرسال إشعار للفاتورة رقم $invoiceId");
                return false;
            }

            // الحصول على بيانات الفاتورة
            $invoice = $this->getInvoiceData($invoiceId);
            if (!$invoice) {
                $this->log("لم يتم العثور على الفاتورة رقم $invoiceId");
                return false;
            }

            // إعداد رسالة الإشعار
            $message = $this->prepareNotificationMessage($invoice);

            // التحقق من تفعيل خدمة WhatsApp
            $allSettings = $this->settings->getAllSettings();
            $whatsappEnabled = isset($allSettings['whatsapp_enabled']) ? (bool)$allSettings['whatsapp_enabled'] : false;

            if (!$whatsappEnabled) {
                // تفعيل خدمة WhatsApp مؤقتًا لإرسال الإشعار
                $this->log("تفعيل خدمة WhatsApp مؤقتًا لإرسال إشعار الفاتورة للمدراء");
                $this->settings->updateSetting('whatsapp_enabled', 1);
            }

            // التحقق من تشغيل الخادم المحلي في حالة وضع جانب العميل
            $whatsappClientSide = isset($allSettings['whatsapp_client_side']) ? (bool)$allSettings['whatsapp_client_side'] : false;

            if ($whatsappClientSide) {
                // التحقق من الاتصال بالخادم المحلي
                $localServerUrl = 'http://localhost:3000';
                $ch = curl_init($localServerUrl);
                curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 2); // مهلة الاتصال ثانيتين فقط
                curl_setopt($ch, CURLOPT_TIMEOUT, 2); // مهلة الطلب ثانيتين فقط
                curl_exec($ch);
                $error = curl_error($ch);
                $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
                curl_close($ch);

                if ($httpCode !== 200) {
                    $this->log("الخادم المحلي غير متاح. يرجى التأكد من تشغيل الخادم المحلي على جهازك. خطأ: $error");

                    // إذا كانت خدمة WhatsApp غير مفعلة مسبقًا، نعيدها إلى حالتها السابقة
                    if (!$whatsappEnabled) {
                        $this->settings->updateSetting('whatsapp_enabled', 0);
                    }

                    return false;
                }
            }

            // إرسال الرسالة لكل مدير
            $success = true;
            foreach ($adminPhoneNumbers as $phoneNumber) {
                $result = $this->whatsappService->sendMessage($phoneNumber, $message);
                if (!$result) {
                    $success = false;
                    $this->log("فشل إرسال إشعار الفاتورة رقم $invoiceId إلى المدير برقم $phoneNumber");
                } else {
                    $this->log("تم إرسال إشعار الفاتورة رقم $invoiceId بنجاح إلى المدير برقم $phoneNumber");
                }
            }

            // إذا كانت خدمة WhatsApp غير مفعلة مسبقًا، نعيدها إلى حالتها السابقة
            if (!$whatsappEnabled) {
                $this->settings->updateSetting('whatsapp_enabled', 0);
            }

            return $success;
        } catch (Exception $e) {
            $this->log("خطأ في إرسال إشعار الفاتورة رقم $invoiceId: " . $e->getMessage());
            return false;
        }
    }

    /**
     * الحصول على بيانات الفاتورة
     *
     * @param int $invoiceId معرف الفاتورة
     * @return array|false بيانات الفاتورة أو false في حالة الفشل
     */
    private function getInvoiceData($invoiceId) {
        try {
            // استعلام للحصول على بيانات الفاتورة والعميل
            $this->db->prepare("
                SELECT i.*, c.name as customer_name, c.phone as customer_phone, b.name as branch_name,
                       u.name as cashier_name, e.name as employee_name
                FROM invoices i
                LEFT JOIN customers c ON i.customer_id = c.id
                LEFT JOIN branches b ON i.branch_id = b.id
                LEFT JOIN users u ON i.cashier_id = u.id
                LEFT JOIN employees e ON i.employee_id = e.id
                WHERE i.id = :invoice_id
            ");
            $this->db->bind(':invoice_id', $invoiceId);
            $this->db->execute();
            $invoice = $this->db->fetch();

            if (!$invoice) {
                return false;
            }

            // الحصول على عناصر الفاتورة
            $this->db->prepare("
                SELECT ii.*,
                    CASE
                        WHEN ii.item_type = 'service' THEN s.name
                        WHEN ii.item_type = 'product' THEN p.name
                        ELSE 'غير معروف'
                    END as item_name
                FROM invoice_items ii
                LEFT JOIN services s ON ii.item_type = 'service' AND ii.item_id = s.id
                LEFT JOIN products p ON ii.item_type = 'product' AND ii.item_id = p.id
                WHERE ii.invoice_id = :invoice_id
            ");
            $this->db->bind(':invoice_id', $invoiceId);
            $this->db->execute();
            $invoice['items'] = $this->db->fetchAll();

            return $invoice;
        } catch (Exception $e) {
            $this->log("خطأ في الحصول على بيانات الفاتورة: " . $e->getMessage());
            return false;
        }
    }

    /**
     * إعداد رسالة الإشعار
     *
     * @param array $invoice بيانات الفاتورة
     * @return string نص الرسالة
     */
    private function prepareNotificationMessage($invoice) {
        // الحصول على إعدادات الفاتورة
        $invoiceSettings = $this->settings->getSettingsByPrefix('invoice_');
        $companyName = $invoiceSettings['company_name'] ?? $invoice['branch_name'] ?? 'صالون البدرواي';

        // تنسيق التاريخ
        $invoiceDate = date('Y-m-d h:i A', strtotime($invoice['created_at']));

        // إعداد الرسالة
        $message = "تم إنشاء فاتورة جديدة\n\n";
        $message .= "رقم الفاتورة: " . $invoice['invoice_number'] . "\n";
        $message .= "التاريخ: " . $invoiceDate . "\n";
        $message .= "الفرع: " . $invoice['branch_name'] . "\n";
        $message .= "الكاشير: " . $invoice['cashier_name'] . "\n";

        if (!empty($invoice['customer_name'])) {
            $message .= "العميل: " . $invoice['customer_name'] . "\n";
        }

        if (!empty($invoice['employee_name'])) {
            $message .= "الموظف: " . $invoice['employee_name'] . "\n";
        }

        $message .= "المبلغ الإجمالي: " . number_format($invoice['total_amount'], 2) . " ج\n";

        if ($invoice['discount_amount'] > 0) {
            $message .= "الخصم: " . number_format($invoice['discount_amount'], 2) . " ج\n";
        }

        $message .= "المبلغ النهائي: " . number_format($invoice['final_amount'], 2) . " ج\n";
        $message .= "طريقة الدفع: " . $this->getPaymentMethodName($invoice['payment_method']) . "\n";
        $message .= "حالة الدفع: " . $this->getPaymentStatusName($invoice['payment_status']) . "\n";

        if ($invoice['payment_status'] === 'partial') {
            $message .= "المبلغ المدفوع: " . number_format($invoice['paid_amount'], 2) . " ج\n";
            $message .= "المبلغ المتبقي: " . number_format($invoice['final_amount'] - $invoice['paid_amount'], 2) . " ج\n";
        }

        // إضافة عناصر الفاتورة
        if (!empty($invoice['items'])) {
            $message .= "\nعناصر الفاتورة:\n";
            foreach ($invoice['items'] as $index => $item) {
                $itemName = $item['item_name'] ?? 'غير معروف';
                $itemType = $item['item_type'] === 'service' ? 'خدمة' : 'منتج';
                $quantity = $item['quantity'] ?? 0;
                $price = $item['price'] ?? 0;
                $total = $item['total'] ?? 0;

                $message .= ($index + 1) . ". " . $itemName . " (" . $itemType . ") - " . $quantity . " × " . number_format($price, 2) . " = " . number_format($total, 2) . " ج\n";
            }
        }

        $message .= "\nنظام " . $companyName;

        return $message;
    }

    /**
     * الحصول على اسم طريقة الدفع
     *
     * @param string $paymentMethod رمز طريقة الدفع
     * @return string اسم طريقة الدفع
     */
    private function getPaymentMethodName($paymentMethod) {
        switch ($paymentMethod) {
            case 'cash':
                return 'نقدي';
            case 'card':
                return 'بطاقة';
            case 'other':
                return 'أخرى';
            default:
                return 'غير معروف';
        }
    }

    /**
     * الحصول على اسم حالة الدفع
     *
     * @param string $paymentStatus رمز حالة الدفع
     * @return string اسم حالة الدفع
     */
    private function getPaymentStatusName($paymentStatus) {
        switch ($paymentStatus) {
            case 'paid':
                return 'مدفوع';
            case 'partial':
                return 'مدفوع جزئي';
            case 'unpaid':
                return 'غير مدفوع';
            default:
                return 'غير معروف';
        }
    }

    /**
     * التحقق من تفعيل إشعارات المدير
     *
     * @param array $settings إعدادات الإشعارات
     * @return bool هل إشعارات المدير مفعلة
     */
    private function isAdminNotificationsEnabled($settings) {
        // التحقق من المفتاح الكامل أولاً
        if (isset($settings['admin_notifications']) && $settings['admin_notifications']) {
            return true;
        }

        // التحقق من المفتاح بدون الحرف الأول (مشكلة معروفة في النظام)
        foreach ($settings as $key => $value) {
            if (strpos($key, 'dmin_notifications') !== false && $value) {
                return true;
            }
        }

        return false;
    }

    /**
     * التحقق من تفعيل إشعارات الفواتير الجديدة
     *
     * @param array $settings إعدادات الإشعارات
     * @return bool هل إشعارات الفواتير الجديدة مفعلة
     */
    private function isNewInvoiceNotificationsEnabled($settings) {
        // التحقق من المفتاح الكامل أولاً
        if (isset($settings['notify_new_invoice']) && $settings['notify_new_invoice']) {
            return true;
        }

        // التحقق من المفتاح بدون الحرف الأول (مشكلة معروفة في النظام)
        foreach ($settings as $key => $value) {
            if (strpos($key, 'otify_new_invoice') !== false && $value) {
                return true;
            }
        }

        return false;
    }

    /**
     * الحصول على أرقام هواتف المدراء
     *
     * @param array $settings إعدادات الإشعارات
     * @return array مصفوفة تحتوي على أرقام هواتف المدراء
     */
    private function getAdminPhoneNumbers($settings) {
        // الحصول على أرقام الهواتف من الإعدادات
        $phoneNumbersStr = '';

        // التحقق من المفتاح الكامل أولاً
        if (isset($settings['admin_phone_numbers'])) {
            $phoneNumbersStr = $settings['admin_phone_numbers'];
        } else {
            // التحقق من المفتاح بدون الحرف الأول (مشكلة معروفة في النظام)
            foreach ($settings as $key => $value) {
                if (strpos($key, 'dmin_phone_numbers') !== false) {
                    $phoneNumbersStr = $value;
                    break;
                }
            }
        }

        if (empty($phoneNumbersStr)) {
            $this->log("لا توجد أرقام هواتف للمدراء في الإعدادات");
            return [];
        }

        // تقسيم النص إلى أسطر
        $phoneNumbers = explode("\n", $phoneNumbersStr);

        // تنظيف الأرقام
        $cleanedNumbers = [];
        foreach ($phoneNumbers as $number) {
            $number = trim($number);
            if (!empty($number)) {
                $cleanedNumbers[] = $number;
            }
        }

        $this->log("تم العثور على " . count($cleanedNumbers) . " رقم هاتف للمدراء");
        return $cleanedNumbers;
    }

    /**
     * الحصول على إعدادات الإشعارات
     *
     * @return array إعدادات الإشعارات
     */
    private function getNotificationSettings() {
        return $this->settings->getSettingsByPrefix('notification_');
    }

    /**
     * تسجيل رسالة في ملف السجل
     *
     * @param string $message الرسالة المراد تسجيلها
     * @return void
     */
    private function log($message) {
        $timestamp = date('Y-m-d H:i:s');
        $logMessage = "[$timestamp] $message" . PHP_EOL;
        file_put_contents($this->logFile, $logMessage, FILE_APPEND);
    }
}
