<?php
/**
 * ملف تكوين التسجيل
 * 
 * يحتوي على إعدادات التسجيل وتوجيه سجلات PHP
 */

// التأكد من عدم الوصول المباشر للملف
if (!defined('BASEPATH')) {
    exit('لا يمكن الوصول إلى هذا الملف مباشرة');
}

// تحديد مسار مجلد السجلات
$logDir = dirname(dirname(__FILE__)) . '/logs/';

// التأكد من وجود المجلد
if (!is_dir($logDir)) {
    mkdir($logDir, 0755, true);
}

// تكوين سجلات PHP
ini_set('log_errors', 1);
ini_set('error_log', $logDir . 'php_errors_' . date('Y-m-d') . '.log');
ini_set('display_errors', 0); // إخفاء الأخطاء في الإنتاج

// تعريف دالة معالجة الأخطاء المخصصة
function custom_error_handler($errno, $errstr, $errfile, $errline) {
    // تنسيق رسالة الخطأ
    $error_message = date('Y-m-d H:i:s') . " - Error [$errno] $errstr in $errfile on line $errline";
    
    // تسجيل الخطأ في ملف السجل
    error_log($error_message);
    
    // إرجاع false للسماح لـ PHP بمعالجة الخطأ بالطريقة الافتراضية
    return false;
}

// تعيين معالج الأخطاء المخصص
set_error_handler('custom_error_handler', E_ALL & ~E_NOTICE & ~E_DEPRECATED & ~E_STRICT);

// تعريف دالة معالجة الاستثناءات غير المعالجة
function exception_handler($exception) {
    // تنسيق رسالة الاستثناء
    $error_message = date('Y-m-d H:i:s') . " - Uncaught Exception: " . $exception->getMessage() . 
                     " in " . $exception->getFile() . " on line " . $exception->getLine() . 
                     "\nStack trace: " . $exception->getTraceAsString();
    
    // تسجيل الاستثناء في ملف السجل
    error_log($error_message);
    
    // عرض رسالة خطأ عامة للمستخدم
    echo "حدث خطأ غير متوقع. يرجى المحاولة مرة أخرى لاحقًا.";
    exit;
}

// تعيين معالج الاستثناءات
set_exception_handler('exception_handler');
