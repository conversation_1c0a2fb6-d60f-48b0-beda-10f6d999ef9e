/**
 * وظائف JavaScript مشتركة للنظام
 * يتم استخدام هذا الملف لتوحيد سلوك العناصر المشتركة في جميع الصفحات
 */

// تعريف متغيرات عامة
// مسار API النظام
// استخدام المتغيرات المعرفة مسبقًا إذا كانت موجودة
if (typeof BASE_URL === 'undefined') {
    var BASE_URL = window.location.origin + '/' + window.location.pathname.split('/')[1] + '/';
}
if (typeof API_URL === 'undefined') {
    var API_URL = BASE_URL + 'api/';
}

// نظام رسائل التنبيه
const AlertSystem = {
    /**
     * عرض رسالة تنبيه
     * @param {string} message - نص الرسالة
     * @param {string} type - نوع الرسالة (success, info, warning, danger)
     * @param {number} duration - مدة ظهور الرسالة بالمللي ثانية (0 للبقاء حتى يتم إغلاقها يدويًا)
     * @param {string} container - معرف الحاوية التي سيتم إضافة الرسالة إليها
     */
    showAlert: function(message, type = 'info', duration = 5000, container = '#alertContainer') {
        // إنشاء معرف فريد للرسالة
        const alertId = 'alert-' + Date.now();

        // إنشاء HTML للرسالة
        const alertHTML = `
            <div id="${alertId}" class="alert alert-${type} alert-dismissible fade show" role="alert">
                ${message}
                <button type="button" class="close" data-dismiss="alert" aria-label="إغلاق">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
        `;

        // إضافة الرسالة إلى الحاوية
        const containerElement = document.querySelector(container);
        if (containerElement) {
            containerElement.innerHTML += alertHTML;

            // إضافة معالج حدث لزر الإغلاق
            const alertElement = document.getElementById(alertId);
            const closeButton = alertElement.querySelector('.close');
            closeButton.addEventListener('click', function() {
                alertElement.remove();
            });

            // إغلاق الرسالة تلقائيًا بعد المدة المحددة
            if (duration > 0) {
                setTimeout(function() {
                    if (alertElement && alertElement.parentNode) {
                        alertElement.classList.remove('show');
                        setTimeout(function() {
                            if (alertElement && alertElement.parentNode) {
                                alertElement.remove();
                            }
                        }, 300);
                    }
                }, duration);
            }
        } else {
            console.error('لم يتم العثور على حاوية الرسائل:', container);
        }
    },

    /**
     * عرض رسالة نجاح
     * @param {string} message - نص الرسالة
     * @param {number} duration - مدة ظهور الرسالة بالمللي ثانية
     */
    success: function(message, duration = 5000) {
        this.showAlert(message, 'success', duration);
    },

    /**
     * عرض رسالة معلومات
     * @param {string} message - نص الرسالة
     * @param {number} duration - مدة ظهور الرسالة بالمللي ثانية
     */
    info: function(message, duration = 5000) {
        this.showAlert(message, 'info', duration);
    },

    /**
     * عرض رسالة تحذير
     * @param {string} message - نص الرسالة
     * @param {number} duration - مدة ظهور الرسالة بالمللي ثانية
     */
    warning: function(message, duration = 5000) {
        this.showAlert(message, 'warning', duration);
    },

    /**
     * عرض رسالة خطأ
     * @param {string} message - نص الرسالة
     * @param {number} duration - مدة ظهور الرسالة بالمللي ثانية
     */
    error: function(message, duration = 5000) {
        this.showAlert(message, 'danger', duration);
    }
};

// نظام تأكيد الحذف
const ConfirmSystem = {
    /**
     * عرض مربع حوار تأكيد
     * @param {string} message - نص الرسالة
     * @param {Function} onConfirm - الدالة التي سيتم استدعاؤها عند التأكيد
     * @param {Function} onCancel - الدالة التي سيتم استدعاؤها عند الإلغاء
     */
    confirm: function(message, onConfirm, onCancel = null) {
        if (confirm(message)) {
            if (typeof onConfirm === 'function') {
                onConfirm();
            }
        } else {
            if (typeof onCancel === 'function') {
                onCancel();
            }
        }
    },

    /**
     * عرض مربع حوار تأكيد الحذف
     * @param {Function} onConfirm - الدالة التي سيتم استدعاؤها عند التأكيد
     */
    confirmDelete: function(onConfirm) {
        this.confirm('هل أنت متأكد من رغبتك في حذف هذا العنصر؟', onConfirm);
    }
};

// نظام تنسيق الجداول
const TableSystem = {
    /**
     * تهيئة الجدول
     * @param {string} tableId - معرف الجدول
     */
    initTable: function(tableId) {
        const table = document.getElementById(tableId);
        if (table) {
            // إضافة الفئات الأساسية للجدول
            table.classList.add('table', 'table-striped', 'table-hover', 'table-bordered');

            // إضافة فئة للحاوية
            const parent = table.parentNode;
            if (parent && !parent.classList.contains('table-container')) {
                parent.classList.add('table-container');
            }

            // تهيئة أزرار الحذف
            const deleteButtons = table.querySelectorAll('.delete-btn');
            deleteButtons.forEach(button => {
                button.addEventListener('click', function(e) {
                    e.preventDefault();
                    const url = this.getAttribute('data-url');
                    const id = this.getAttribute('data-id');

                    ConfirmSystem.confirmDelete(function() {
                        if (url) {
                            window.location.href = url;
                        } else if (id) {
                            // يمكن استخدام AJAX لحذف العنصر
                            console.log('حذف العنصر بالمعرف:', id);
                        }
                    });
                });
            });
        }
    }
};

// نظام تنسيق النماذج
const FormSystem = {
    /**
     * تهيئة النموذج
     * @param {string} formId - معرف النموذج
     * @param {Function} onSubmit - الدالة التي سيتم استدعاؤها عند إرسال النموذج
     */
    initForm: function(formId, onSubmit = null) {
        const form = document.getElementById(formId);
        if (form) {
            // إضافة معالج حدث للإرسال
            form.addEventListener('submit', function(e) {
                if (typeof onSubmit === 'function') {
                    e.preventDefault();
                    onSubmit(e);
                }
            });

            // تهيئة حقول التاريخ
            const dateInputs = form.querySelectorAll('input[type="date"]');
            dateInputs.forEach(input => {
                // يمكن إضافة تهيئة خاصة لحقول التاريخ هنا
            });

            // تهيئة حقول الاختيار
            const selectInputs = form.querySelectorAll('select');
            selectInputs.forEach(select => {
                // يمكن إضافة تهيئة خاصة لحقول الاختيار هنا
            });
        }
    },

    /**
     * التحقق من صحة النموذج
     * @param {string} formId - معرف النموذج
     * @returns {boolean} - هل النموذج صحيح
     */
    validateForm: function(formId) {
        const form = document.getElementById(formId);
        if (form) {
            // التحقق من الحقول المطلوبة
            const requiredInputs = form.querySelectorAll('[required]');
            let isValid = true;

            requiredInputs.forEach(input => {
                if (!input.value.trim()) {
                    isValid = false;
                    input.classList.add('is-invalid');
                } else {
                    input.classList.remove('is-invalid');
                }
            });

            return isValid;
        }

        return false;
    }
};

// تهيئة عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // إنشاء حاوية للرسائل إذا لم تكن موجودة
    if (!document.getElementById('alertContainer')) {
        const alertContainer = document.createElement('div');
        alertContainer.id = 'alertContainer';
        alertContainer.style.position = 'fixed';
        alertContainer.style.top = '20px';
        alertContainer.style.right = '20px';
        alertContainer.style.zIndex = '9999';
        alertContainer.style.maxWidth = '400px';
        document.body.appendChild(alertContainer);
    }

    // تهيئة جميع الجداول
    const tables = document.querySelectorAll('table.data-table');
    tables.forEach(table => {
        TableSystem.initTable(table.id);
    });
});
