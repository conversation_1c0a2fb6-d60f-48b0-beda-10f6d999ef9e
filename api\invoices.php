<?php
/**
 * نقاط النهاية الخاصة بإدارة الفواتير
 */
// إعداد رؤوس CORS للسماح بالوصول من أي مصدر
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');

// تأكيد تحميل الملفات الأساسية
require_once __DIR__ . '/../config/init.php';

// التحقق من نوع الطلب (POST أو GET أو PUT أو DELETE)
$action = isset($_GET['action']) ? $_GET['action'] : '';

// معالجة الطلبات
try {
    // إعداد رأس الاستجابة
    header('Content-Type: application/json');

    // إنشاء كائنات النماذج
    $db = new Database();
    $invoiceModel = new Invoice($db);
    $inventoryModel = new Inventory($db);

    // التحقق من صلاحيات المستخدم
    if (!isLoggedIn()) {
        throw new Exception('يجب تسجيل الدخول أولاً', 401);
    }

    switch ($action) {
        // إنشاء فاتورة جديدة
        /**
         * إنشاء فاتورة جديدة
         */
        case 'create':
            // التأكد من وجود الصلاحية
            requirePermission('invoices_create');

            // التأكد من أن الطلب عبر POST
            if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
                throw new Exception('الطريقة غير مسموح بها', 405);
            }

            // تسجيل البيانات الواردة للتصحيح
            error_log('البيانات الواردة: ' . print_r($_POST, true));

            // التقاط بيانات الفاتورة الأساسية
            $invoiceData = [
                'customer_id' => !empty($_POST['customer_id']) ? intval($_POST['customer_id']) : null,
                'employee_id' => !empty($_POST['employee_id']) ? intval($_POST['employee_id']) : null,
                'invoice_number' => $_POST['invoice_number'] ?? null,
                'total_amount' => floatval($_POST['total_amount'] ?? 0),
                'discount_amount' => floatval($_POST['discount_amount'] ?? 0),
                'discount_type' => $_POST['discount_type'] ?? 'amount',
                'tax_amount' => floatval($_POST['tax_amount'] ?? 0),
                'final_amount' => floatval($_POST['final_amount'] ?? 0),
                'payment_method' => $_POST['payment_method'] ?? 'cash',
                'payment_status' => $_POST['payment_status'] ?? 'paid',
                'notes' => $_POST['notes'] ?? null,
                'branch_id' => !empty($_POST['branch_id']) ? intval($_POST['branch_id']) : $_SESSION['user_branch_id'],
                'cashier_id' => $_SESSION['user_id']
            ];

            // إضافة معلومات كود الترويج إذا كان موجودًا
            if (!empty($_POST['promo_code_id'])) {
                $invoiceData['promo_code_id'] = intval($_POST['promo_code_id']);
                $invoiceData['promo_code'] = $_POST['promo_code'] ?? '';
            }

            // إضافة المبلغ المدفوع بناءً على حالة الدفع
            if ($invoiceData['payment_status'] === 'paid') {
                $invoiceData['paid_amount'] = $invoiceData['final_amount'];
            } elseif ($invoiceData['payment_status'] === 'partial') {
                $invoiceData['paid_amount'] = isset($_POST['paid_amount']) ? floatval($_POST['paid_amount']) : 0;
                // التحقق من أن المبلغ المدفوع أقل من المبلغ الإجمالي وأكبر من صفر
                if ($invoiceData['paid_amount'] <= 0) {
                    throw new Exception('يجب أن يكون المبلغ المدفوع أكبر من صفر للدفع الجزئي');
                }
                if ($invoiceData['paid_amount'] >= $invoiceData['final_amount']) {
                    throw new Exception('المبلغ المدفوع يجب أن يكون أقل من المبلغ الإجمالي للدفع الجزئي');
                }
            } else {
                $invoiceData['paid_amount'] = 0;
            }

            // Generate invoice number if not provided
                $invoiceData['invoice_number'] = generateInvoiceNumber($invoiceData['branch_id']);


            // التحقق من البيانات الإلزامية
            if (empty($invoiceData['final_amount'])) {
                throw new Exception('يجب تحديد المبلغ النهائي للفاتورة');
            }

            // التحقق من وجود نهاية يوم مفتوحة للفرع
            $branchId = $invoiceData['branch_id'];
            $db->prepare("SELECT id FROM end_days
                          WHERE branch_id = :branch_id
                          AND closed_at IS NULL");
            $db->bind(':branch_id', $branchId);
            $openEndDay = $db->fetch();

            if (!$openEndDay) {
                throw new Exception('لا يمكن إنشاء فاتورة بدون بدء يوم العمل للفرع', 400);
            }

            // إضافة معرف نهاية اليوم إلى بيانات الفاتورة
            $invoiceData['end_day_id'] = $openEndDay['id'];

            // استخراج عناصر الفاتورة
            $itemsJson = !empty($_POST['items']) ? $_POST['items'] : (!empty($_POST['invoice_items']) ? $_POST['invoice_items'] : '[]');
            $items = json_decode($itemsJson, true);

            // تسجيل العناصر بعد فك التشفير
            error_log('العناصر بعد فك التشفير: ' . print_r($items, true));

            if (empty($items) || !is_array($items)) {
                throw new Exception('يجب إضافة عناصر للفاتورة');
            }

            // التحقق من تنسيق العناصر
            foreach ($items as $key => $item) {
                if (empty($item['item_id']) || empty($item['item_type']) || !isset($item['quantity']) || !isset($item['price'])) {
                    throw new Exception('بيانات العنصر رقم ' . ($key + 1) . ' غير مكتملة');
                }

                // التأكد من تعيين الموظف لكل خدمة
                if ($item['item_type'] === 'service' && empty($item['employee_id'])) {
                    throw new Exception('يجب تحديد الحلاق/الموظف لكل خدمة');
                }

                // التأكد من أن الكمية موجبة
                if ($item['quantity'] <= 0) {
                    throw new Exception('يجب أن تكون الكمية أكبر من صفر للعنصر ' . $item['name']);
                }
            }

            // التحقق من توفر المنتجات في المخزون قبل إنشاء الفاتورة
            $branchId = $invoiceData['branch_id'];
            foreach ($items as $item) {
                if ($item['item_type'] === 'product') {
                    $isAvailable = $inventoryModel->isProductAvailable(
                        $item['item_id'],
                        $item['quantity'],
                        $branchId
                    );

                    if (!$isAvailable) {
                        // الحصول على اسم المنتج
                        $productModel = new Product($db);
                        $product = $productModel->getProductById($item['item_id']);
                        $productName = $product ? $product['name'] : "المنتج {$item['item_id']}";

                        throw new Exception("المنتج {$productName} غير متوفر بالكمية المطلوبة");
                    }
                }
            }

            try {
                // بدء المعاملة
                $db->beginTransaction();

                // إنشاء الفاتورة
                $invoiceId = $invoiceModel->createInvoiceWithTransaction($invoiceData, $items);

                // تأكيد المعاملة
                $db->commit();

                // إرسال إشعار الفاتورة في الخلفية باستخدام طلب غير متزامن
                // لا ننتظر اكتمال العملية لإرجاع الاستجابة للمستخدم

                // تسجيل معلومات عن الفاتورة للإرسال في الخلفية
                $invoiceData = [
                    'invoice_id' => $invoiceId,
                    'background' => 'true'
                ];

                // لا نرسل إشعار الفاتورة هنا - سيتم إرساله من خلال طلب AJAX منفصل بعد الطباعة

                // لا نرسل إشعار للمدراء هنا - سيتم إرساله من خلال طلب AJAX منفصل

                // إرجاع النتيجة
                echo json_encode([
                    'status' => 'success',
                    'message' => 'تم إنشاء الفاتورة بنجاح',
                    'invoice_id' => $invoiceId
                ]);
            } catch (Exception $e) {
                // التراجع عن المعاملة في حالة حدوث خطأ
                if ($db->inTransaction()) {
                    $db->rollBack();
                }

                // تسجيل الخطأ
                error_log('خطأ أثناء إنشاء الفاتورة: ' . $e->getMessage());

                // إعادة رمي الاستثناء
                throw new Exception('فشل في إنشاء الفاتورة: ' . $e->getMessage());
            }
            break;

        // تحديث فاتورة
        case 'update':
            // التأكد من وجود الصلاحية
            requirePermission('invoices_edit');

            // التأكد من أن الطلب عبر PUT أو POST
            if ($_SERVER['REQUEST_METHOD'] === 'PUT') {
                // قراءة البيانات من الطلب PUT
                parse_str(file_get_contents('php://input'), $putData);
                $invoiceId = $putData['id'] ?? null;
            } elseif ($_SERVER['REQUEST_METHOD'] === 'POST') {
                // قراءة البيانات من الطلب POST
                $putData = $_POST;
                $invoiceId = $putData['edit_invoice_id'] ?? null;

                // تسجيل البيانات للتصحيح
                error_log('POST data for update: ' . print_r($putData, true));
            } else {
                throw new Exception('الطريقة غير مسموح بها', 405);
            }

            // التحقق من وجود معرف الفاتورة
            if (!$invoiceId) {
                throw new Exception('معرف الفاتورة مطلوب', 400);
            }

            // التحقق من صحة المدخلات
            $invoiceData = [
                'customer_id' => !empty($putData['customer_id']) ? intval($putData['customer_id']) : null,
                'employee_id' => !empty($putData['employee_id']) ? intval($putData['employee_id']) : null,
                'total_amount' => floatval($putData['total_amount'] ?? 0),
                'discount_amount' => floatval($putData['discount_amount'] ?? 0),
                'discount_type' => $putData['discount_type'] ?? 'amount',
                'tax_amount' => floatval($putData['tax_amount'] ?? 0),
                'final_amount' => floatval($putData['final_amount'] ?? 0),
                'payment_method' => $putData['payment_method'] ?? 'cash',
                'payment_status' => $putData['payment_status'] ?? 'paid',
                'notes' => $putData['notes'] ?? '',
                'branch_id' => !empty($putData['branch_id']) ? intval($putData['branch_id']) : $_SESSION['user_branch_id']
            ];

            // إضافة المبلغ المدفوع بناءً على حالة الدفع
            if ($invoiceData['payment_status'] === 'paid') {
                $invoiceData['paid_amount'] = $invoiceData['final_amount'];
            } elseif ($invoiceData['payment_status'] === 'partial') {
                $invoiceData['paid_amount'] = isset($putData['paid_amount']) ? floatval($putData['paid_amount']) : 0;
                // التحقق من أن المبلغ المدفوع أقل من المبلغ الإجمالي وأكبر من صفر
                if ($invoiceData['paid_amount'] <= 0) {
                    throw new Exception('يجب أن يكون المبلغ المدفوع أكبر من صفر للدفع الجزئي');
                }
                if ($invoiceData['paid_amount'] >= $invoiceData['final_amount']) {
                    throw new Exception('المبلغ المدفوع يجب أن يكون أقل من المبلغ الإجمالي للدفع الجزئي');
                }
            } else {
                $invoiceData['paid_amount'] = 0;
            }

            // معالجة عناصر الفاتورة فقط إذا تم توفيرها
            if (isset($putData['items']) || isset($putData['invoice_items'])) {
                // استخدام أي حقل متوفر
                $itemsJson = !empty($putData['items']) ? $putData['items'] : (!empty($putData['invoice_items']) ? $putData['invoice_items'] : '[]');
                $invoiceData['items'] = json_decode($itemsJson, true);
                // تسجيل عناصر الفاتورة للتصحيح
                error_log('Invoice items: ' . print_r($invoiceData['items'], true));
            }



            // لا نتحقق من وجود عناصر للسماح بتحديث حالة الدفع فقط

            // تحديث الفاتورة
            $invoiceModel->updateInvoice($invoiceId, $invoiceData);

            echo json_encode([
                'status' => 'success',
                'message' => 'تم تحديث الفاتورة بنجاح',
                'invoice_id' => $invoiceId
            ]);
            break;

        // تحديث حالة دفع الفاتورة
        case 'update_status':
            // لا نتحقق من الصلاحية هنا لأننا سنسمح للكاشير بتحديث حالة الفواتير التي أنشأها
            // سيتم التحقق من الصلاحية داخل دالة updatePaymentStatus

            // التحقق من وجود معرف الفاتورة وحالة الدفع
            if (empty($_POST['invoice_id']) || empty($_POST['payment_status'])) {
                throw new Exception('يجب توفير معرف الفاتورة وحالة الدفع', 400);
            }

            $invoiceId = intval($_POST['invoice_id']);
            $paymentStatus = $_POST['payment_status'];
            $paidAmount = isset($_POST['paid_amount']) ? floatval($_POST['paid_amount']) : 0;

            // تسجيل معلومات للتحقق
            error_log("API - Invoice ID: " . $invoiceId);
            error_log("API - Payment Status: " . $paymentStatus);
            error_log("API - Paid Amount: " . $paidAmount);
            error_log("API - User ID: " . ($_SESSION['user_id'] ?? 'not set'));

            // تحديث حالة الدفع والمبلغ المدفوع
            $invoiceModel->updatePaymentStatus($invoiceId, $paymentStatus, $_SESSION['user_id'], $paidAmount);

            // الحصول على بيانات الفاتورة المحدثة
            $invoice = $invoiceModel->getInvoiceById($invoiceId);

            // إرسال الرد
            header('Content-Type: application/json');
            echo json_encode([
                'success' => true,
                'message' => 'تم تحديث حالة الدفع بنجاح',
                'invoice' => $invoice
            ]);
            break;

        // حذف فاتورة
        case 'delete':
            // لا نتحقق من الصلاحية هنا لأننا سنسمح للكاشير بحذف الفواتير التي أنشأها
            // سيتم التحقق من الصلاحية داخل دالة deleteInvoice

            // التحقق من وجود معرف الفاتورة من أي مصدر
            $invoiceId = null;

            // التحقق من الطلب POST
            if ($_SERVER['REQUEST_METHOD'] === 'POST') {
                // التحقق من البيانات المرسلة كـ JSON
                $input = file_get_contents('php://input');
                error_log('POST request raw input: ' . $input); // تسجيل المدخلات الخام

                // محاولة تحليل المدخلات كـ JSON
                $postData = json_decode($input, true);
                error_log('POST request parsed JSON: ' . print_r($postData, true)); // تسجيل البيانات المحللة

                // التحقق من البيانات JSON
                if (is_array($postData) && isset($postData['id'])) {
                    $invoiceId = $postData['id'];
                } else {
                    // التحقق من البيانات العادية
                    $invoiceId = $_POST['id'] ?? null;
                }
            }
            // التحقق من الطلب GET
            elseif ($_SERVER['REQUEST_METHOD'] === 'GET') {
                $invoiceId = $_GET['id'] ?? null;
            }
            // التحقق من الطلب DELETE
            elseif ($_SERVER['REQUEST_METHOD'] === 'DELETE') {
                // قراءة البيانات من الطلب DELETE
                $input = file_get_contents('php://input');
                error_log('DELETE request raw input: ' . $input); // تسجيل المدخلات الخام

                // محاولة تحليل المدخلات كـ JSON
                $deleteData = json_decode($input, true);
                error_log('DELETE request parsed JSON: ' . print_r($deleteData, true)); // تسجيل البيانات المحللة

                // محاولة الحصول على معرف الفاتورة من عدة مصادر
                if (is_array($deleteData) && isset($deleteData['id'])) {
                    $invoiceId = $deleteData['id'];
                } elseif (isset($_GET['id'])) {
                    $invoiceId = $_GET['id'];
                } else {
                    // إذا لم يتم العثور على معرف الفاتورة، نحاول استخراجه من URL
                    $urlParts = explode('/', $_SERVER['REQUEST_URI']);
                    $lastPart = end($urlParts);
                    if (is_numeric($lastPart)) {
                        $invoiceId = $lastPart;
                    }
                }
            }
            // إذا كانت الطريقة غير مسموح بها
            else {
                throw new Exception('الطريقة غير مسموح بها', 405);
            }

            // تسجيل معلومات عن الطلب
            error_log('Request method: ' . $_SERVER['REQUEST_METHOD']);
            error_log('Action: ' . $action);
            error_log('Invoice ID: ' . ($invoiceId ?? 'null'));
            error_log('GET params: ' . print_r($_GET, true));
            error_log('POST params: ' . print_r($_POST, true));

            // التحقق من وجود معرف الفاتورة
            if (!$invoiceId) {
                throw new Exception('معرف الفاتورة مطلوب', 400);
            }

            // حذف الفاتورة مع تمرير معرف المستخدم الحالي
            $invoiceModel->deleteInvoice($invoiceId, $_SESSION['user_id']);

            echo json_encode([
                'status' => 'success',
                'message' => 'تم حذف الفاتورة بنجاح'
            ]);
            break;
        // استرجاع قائمة الفواتير
        case 'list':
            // التأكد من وجود الصلاحية
            requirePermission('invoices_view');

            // معلمات الفلترة والترتيب
            $filters = [
                'search' => input('search'),
                'customer_id' => input('customer_id'),
                'employee_id' => input('employee_id'),
                'branch_id' => input('branch_id') ?? $_SESSION['user_branch_id'],
                'payment_method' => input('payment_method'),
                'payment_status' => input('payment_status'),
                'start_date' => input('start_date'),
                'end_date' => input('end_date'),
                'limit' => input('limit') ?? 50,
                'offset' => input('offset') ?? 0
            ];

            // استرجاع الفواتير
            $invoices = $invoiceModel->getInvoices($filters);
            $totalCount = $invoiceModel->getInvoicesCount($filters);
            $totalSales = $invoiceModel->getTotalSales($filters);

            echo json_encode([
                'status' => 'success',
                'invoices' => $invoices,
                'total_count' => $totalCount,
                'total_sales' => $totalSales
            ]);
            break;

        // استرجاع تفاصيل فاتورة محددة
        case 'view':
            // التأكد من وجود الصلاحية
            requirePermission('invoices_view');

            // التحقق من وجود معرف الفاتورة
            $invoiceId = input('id');
            if (!$invoiceId) {
                throw new Exception('معرف الفاتورة مطلوب', 400);
            }

            // استرجاع بيانات الفاتورة
            $invoice = $invoiceModel->getInvoiceById($invoiceId);

            if (!$invoice) {
                throw new Exception('لم يتم العثور على الفاتورة', 404);
            }

            echo json_encode([
                'status' => 'success',
                'invoice' => $invoice
            ]);
            break;

        // تطبيق خصم على فاتورة
        case 'apply-discount':
            // التأكد من وجود الصلاحية
            requirePermission('invoices_edit');

            // التأكد من أن الطلب عبر POST
            if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
                throw new Exception('الطريقة غير مسموح بها', 405);
            }

            // التحقق من صحة المدخلات
            $invoiceId = input('invoice_id');
            $discountAmount = input('discount_amount');
            $discountType = input('discount_type') ?? 'amount';

            if (!$invoiceId || !$discountAmount) {
                throw new Exception('معرف الفاتورة ومبلغ الخصم مطلوبان', 400);
            }

            // تطبيق الخصم
            $invoiceModel->applyDiscount($invoiceId, $discountAmount, $discountType);

            echo json_encode([
                'status' => 'success',
                'message' => 'تم تطبيق الخصم بنجاح'
            ]);
            break;

        // إلغاء فاتورة
        case 'cancel':
            // التأكد من وجود الصلاحية
            requirePermission('invoices_delete');

            // التأكد من أن الطلب عبر POST
            if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
                throw new Exception('الطريقة غير مسموح بها', 405);
            }

            // التحقق من وجود معرف الفاتورة
            $invoiceId = input('invoice_id');

            if (!$invoiceId) {
                throw new Exception('معرف الفاتورة مطلوب', 400);
            }

            // إلغاء الفاتورة
            $invoiceModel->cancelInvoice($invoiceId);

            echo json_encode([
                'status' => 'success',
                'message' => 'تم إلغاء الفاتورة بنجاح'
            ]);
            break;

        // تحضير فاتورة للطباعة
        case 'print':
            // التأكد من وجود الصلاحية
            requirePermission('invoices_print');

            // التحقق من وجود معرف الفاتورة
            $invoiceId = input('id');
            if (!$invoiceId) {
                throw new Exception('معرف الفاتورة مطلوب', 400);
            }

            // تحضير الفاتورة للطباعة
            $invoice = $invoiceModel->prepareInvoiceForPrinting($invoiceId);

            echo json_encode([
                'status' => 'success',
                'invoice' => $invoice
            ]);
            break;

        // طباعة الفاتورة على الطابعة الحرارية
        case 'thermal_print':
            // التأكد من وجود الصلاحية
            requirePermission('invoices_print');

            // التحقق من وجود معرف الفاتورة
            $invoiceId = input('id');
            if (!$invoiceId) {
                throw new Exception('معرف الفاتورة مطلوب', 400);
            }

            // تحضير الفاتورة للطباعة
            $invoice = $invoiceModel->prepareInvoiceForPrinting($invoiceId);

            // استدعاء دالة الطباعة الحرارية
            $printResult = printThermalReceipt($invoice);

            if ($printResult) {
                echo json_encode([
                    'status' => 'success',
                    'message' => 'تمت الطباعة بنجاح'
                ]);
            } else {
                throw new Exception('فشلت عملية الطباعة الحرارية، تأكد من اتصال الطابعة', 500);
            }
            break;

        // إنشاء تقرير المبيعات
        case 'report':
            // التأكد من وجود الصلاحية
            requirePermission('reports_sales');

            // معلمات التقرير
            $filters = [
                'branch_id' => input('branch_id') ?? $_SESSION['user_branch_id'],
                'start_date' => input('start_date'),
                'end_date' => input('end_date'),
                'period' => input('period') ?? 'month'
            ];

            // إنشاء التقرير
            $report = $invoiceModel->generateSalesReport($filters);

            echo json_encode([
                'status' => 'success',
                'report' => $report
            ]);
            break;

        // استرجاع تفاصيل فاتورة محددة مع معلومات إضافية
        case 'get_invoice_details':
            // التأكد من وجود الصلاحية
            requirePermission('invoices_view');

            // التحقق من وجود معرف الفاتورة
            $invoiceId = $_GET['id'];
            if (!$invoiceId) {
                throw new Exception('معرف الفاتورة مطلوب', 400);
            }

            // استرجاع بيانات الفاتورة مع العناصر والمعلومات المرتبطة
            $invoice = $invoiceModel->getInvoiceById($invoiceId);

            if (!$invoice) {
                throw new Exception('لم يتم العثور على الفاتورة', 404);
            }

            // استرجاع عناصر الفاتورة
            $invoiceItems = $invoiceModel->getInvoiceItems($invoiceId);

            // استرجاع معلومات العميل إذا كان موجودًا
            $customerInfo = null;
            if (!empty($invoice['customer_id'])) {
                $customerModel = new Customer($db);
                $customerInfo = $customerModel->getCustomerById($invoice['customer_id']);
            }

            // استرجاع معلومات الموظف إذا كان موجودًا
            $employeeInfo = null;
            if (!empty($invoice['employee_id'])) {
                $employeeModel = new Employee($db);
                $employeeInfo = $employeeModel->getEmployeeById($invoice['employee_id']);
            }

            // استرجاع معلومات الفرع
            $branchInfo = null;
            if (!empty($invoice['branch_id'])) {
                $branchModel = new Branch($db);
                $branchInfo = $branchModel->getBranchById($invoice['branch_id']);
            }

            echo json_encode([
                'status' => 'success',
                'invoice' => $invoice,
                'items' => $invoiceItems,
                'customer' => $customerInfo,
                'employee' => $employeeInfo,
                'branch' => $branchInfo
            ]);
            break;

        // الافتراضي: إجراء غير معروف
        default:
    }
} catch (Exception $e) {
    // معالجة الأخطاء
    $errorCode = is_numeric($e->getCode()) && $e->getCode() >= 100 && $e->getCode() < 600 ? $e->getCode() : 500;
    http_response_code($errorCode);

    echo json_encode([
        'status' => 'error',
        'message' => $e->getMessage(),
        'code' => $e->getCode() ?: 500
    ]);
}
