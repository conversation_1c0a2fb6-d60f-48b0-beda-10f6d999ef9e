/**
 * Responsive Sidebar JavaScript
 * سكريبت القائمة الجانبية المتجاوبة مع الموبايل
 */

$(document).ready(function() {
    // التحقق من حالة القائمة عند تحميل الصفحة
    checkSidebarState();

    // فتح/إغلاق القائمة الجانبية عند النقر على زر التبديل
    $('#sidebarToggle').on('click', function() {
        $('.sidebar').toggleClass('active');
        $('.sidebar-overlay').toggleClass('active');
        $('body').toggleClass('sidebar-active');
    });

    // إغلاق القائمة الجانبية عند النقر على زر الإغلاق
    $('#sidebarCollapseBtn').on('click', function() {
        $('.sidebar').removeClass('active');
        $('.sidebar-overlay').removeClass('active');
        $('body').removeClass('sidebar-active');
    });

    // إغلاق القائمة الجانبية عند النقر على طبقة التغطية
    $('.sidebar-overlay').on('click', function() {
        $('.sidebar').removeClass('active');
        $('.sidebar-overlay').removeClass('active');
        $('body').removeClass('sidebar-active');
    });

    // تمدد/طي القوائم الفرعية
    $('.dropdown-toggle').on('click', function(e) {
        if ($(window).width() <= 768) {
            e.preventDefault();
            $(this).next('.collapse').slideToggle(200);
        }
    });

    // إغلاق القائمة الجانبية عند النقر على رابط في الشاشات الصغيرة
    $('.sidebar ul li a:not(.dropdown-toggle)').on('click', function() {
        if ($(window).width() <= 768) {
            $('.sidebar').removeClass('active');
            $('.sidebar-overlay').removeClass('active');
            $('body').removeClass('sidebar-active');
        }
    });

    // تفعيل وضع القائمة المصغرة
    $('#sidebarMinimizer').on('click', function() {
        $('.sidebar').toggleClass('collapsed');
        $('.content').toggleClass('expanded');

        // حفظ حالة القائمة في التخزين المحلي
        localStorage.setItem('sidebar-collapsed', $('.sidebar').hasClass('collapsed'));
    });

    // استرجاع حالة القائمة من التخزين المحلي
    if (localStorage.getItem('sidebar-collapsed') === 'true') {
        $('.sidebar').addClass('collapsed');
        $('.content').addClass('expanded');
    }

    // إعادة ضبط حالة القائمة عند تغيير حجم النافذة
    $(window).on('resize', function() {
        checkSidebarState();
    });
});

/**
 * التحقق من حالة القائمة وضبطها بناءً على حجم الشاشة
 */
function checkSidebarState() {
    if ($(window).width() > 768) {
        $('.sidebar-overlay').removeClass('active');
        $('body').removeClass('sidebar-active');

        // إظهار القائمة في الشاشات الكبيرة إذا كانت مخفية ولم تكن مصغرة
        if (!$('.sidebar').hasClass('collapsed')) {
            $('.sidebar').addClass('active');
        }

        // ضبط هوامش المحتوى
        if ($('.sidebar').hasClass('collapsed')) {
            $('.content').addClass('expanded');
        } else {
            $('.content').removeClass('expanded');
        }
    } else {
        // إخفاء القائمة في الشاشات الصغيرة
        $('.sidebar').removeClass('active');
        $('.content').addClass('active');
    }
}
