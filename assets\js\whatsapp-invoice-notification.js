/**
 * WhatsApp Invoice Notification
 * Este script maneja el envío de notificaciones de WhatsApp para facturas
 * desde el lado del cliente, comunicándose directamente con el servidor local
 * de WhatsApp en localhost:3000
 */

const WhatsAppInvoiceNotification = {
    // URL del servidor local de WhatsApp
    serverUrl: 'http://localhost:3000',

    // URL base de la API del sistema
    apiUrl: BASE_URL + 'api/',

    // Estado de la última operación
    lastOperationStatus: null,

    // Conjunto para rastrear facturas ya notificadas y evitar duplicados
    notifiedInvoices: new Set(),

    // Opciones de configuración
    options: {
        debug: true,                // Habilitar mensajes de depuración
        maxRetries: 2,              // Número máximo de reintentos
        retryDelay: 2000,           // Tiempo entre reintentos (ms)
        timeout: 60000,             // Tiempo de espera para solicitudes (ms) - 60 segundos
        showSuccessMessage: false   // Mostrar mensaje de éxito al usuario
    },

    /**
     * Inicializar el módulo
     * @param {Object} options Opciones de configuración
     */
    init: function(options) {
        // Combinar opciones proporcionadas con las predeterminadas
        if (options) {
            this.options = { ...this.options, ...options };
        }

        if (this.options.debug) {
            console.log('WhatsAppInvoiceNotification inicializado');
        }

        // Almacenar facturas ya notificadas para evitar duplicados
        this.notifiedInvoices = new Set();

        // Registrar eventos
        this._registerEvents();
    },

    /**
     * Registrar eventos para detectar cuando se crea una nueva factura
     * @private
     */
    _registerEvents: function() {
        // Escuchar el evento personalizado 'invoiceCreated'
        document.addEventListener('invoiceCreated', (event) => {
            const invoiceId = event.detail.invoiceId;
            if (invoiceId) {
                this.sendInvoiceNotification(invoiceId);
            }
        });

        // Ya no escuchamos el evento 'invoicePrinted' para evitar duplicados
        // Solo usamos 'invoiceCreated' para enviar notificaciones
        console.log('WhatsAppInvoiceNotification: Solo escuchando evento invoiceCreated para evitar duplicados');
    },

    /**
     * Enviar notificación de WhatsApp para una factura
     * @param {number} invoiceId ID de la factura
     * @returns {Promise<Object>} Resultado de la operación
     */
    sendInvoiceNotification: function(invoiceId) {
        return new Promise((resolve, reject) => {
            console.log(`=== INICIO PROCESO DE NOTIFICACIÓN PARA FACTURA #${invoiceId} ===`);
            console.log(`Origen de la llamada: ${new Error().stack.split('\n')[2].trim()}`);

            // Verificar si ya se ha enviado una notificación para esta factura
            if (this.notifiedInvoices.has(invoiceId)) {
                console.log(`DUPLICADO DETECTADO: Notificación para factura #${invoiceId} ya fue enviada anteriormente.`);
                console.log(`Facturas ya notificadas: [${Array.from(this.notifiedInvoices).join(', ')}]`);
                resolve({ status: 'success', invoiceId: invoiceId, message: 'Notificación ya enviada anteriormente' });
                return;
            }

            console.log(`Preparando notificación de WhatsApp para factura #${invoiceId} - Primera vez`);
            console.log(`Estado actual de notifiedInvoices: [${Array.from(this.notifiedInvoices).join(', ')}]`);

            // Paso 1: Obtener los datos de la factura desde la API
            this._getInvoiceData(invoiceId)
                .then(invoiceData => {
                    // Paso 2: Enviar el mensaje a través del servidor local de WhatsApp
                    return this._sendWhatsAppMessage(
                        invoiceData.phone,
                        invoiceData.message,
                        invoiceId
                    );
                })
                .then(result => {
                    // Paso 3: Registrar el resultado en el sistema
                    return this._logNotificationResult(invoiceId, true, result);
                })
                .then(logResult => {
                    if (this.options.debug) {
                        console.log(`Notificación de WhatsApp enviada y registrada para factura #${invoiceId}`, logResult);
                    }

                    // Marcar esta factura como ya notificada
                    this.notifiedInvoices.add(invoiceId);
                    console.log(`Factura #${invoiceId} marcada como notificada correctamente`);
                    console.log(`Estado actualizado de notifiedInvoices: [${Array.from(this.notifiedInvoices).join(', ')}]`);

                    // Mostrar mensaje de éxito si está habilitado
                    if (this.options.showSuccessMessage) {
                        this._showSuccessMessage();
                    }

                    this.lastOperationStatus = 'success';
                    console.log(`=== FIN PROCESO DE NOTIFICACIÓN PARA FACTURA #${invoiceId} - ÉXITO ===`);
                    resolve({ status: 'success', invoiceId: invoiceId });
                })
                .catch(error => {
                    console.error(`Error al enviar notificación de WhatsApp para factura #${invoiceId}:`, error);
                    console.log(`Error stack: ${error.stack || 'No disponible'}`);

                    // Verificar si es un error de timeout
                    if (error.message && error.message.includes('Tiempo de espera agotado')) {
                        console.warn('Error de timeout - El mensaje podría haberse enviado correctamente');

                        // Registrar como advertencia en lugar de error
                        this._logNotificationResult(invoiceId, true, {
                            status: 'warning',
                            message: 'Tiempo de espera agotado, pero el mensaje podría haberse enviado correctamente'
                        });

                        this.lastOperationStatus = 'warning';
                        console.log(`=== FIN PROCESO DE NOTIFICACIÓN PARA FACTURA #${invoiceId} - ADVERTENCIA (TIMEOUT) ===`);
                        resolve({
                            status: 'warning',
                            message: 'Tiempo de espera agotado, pero el mensaje podría haberse enviado correctamente',
                            invoiceId: invoiceId
                        });
                    } else {
                        // Registrar el error
                        this._logNotificationResult(invoiceId, false, error);
                        this.lastOperationStatus = 'error';
                        console.log(`=== FIN PROCESO DE NOTIFICACIÓN PARA FACTURA #${invoiceId} - ERROR ===`);
                        reject(error);
                    }
                });
        });
    },

    /**
     * Obtener los datos de la factura desde la API
     * @private
     * @param {number} invoiceId ID de la factura
     * @returns {Promise<Object>} Datos de la factura
     */
    _getInvoiceData: function(invoiceId) {
        return new Promise((resolve, reject) => {
            const xhr = new XMLHttpRequest();
            xhr.open('POST', this.apiUrl + 'invoice_notification.php?action=get_invoice_data', true);
            xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');
            xhr.responseType = 'json';

            xhr.onload = () => {
                if (xhr.status === 200) {
                    const response = xhr.response;

                    if (response && response.status === 'success') {
                        if (this.options.debug) {
                            console.log('Datos de factura obtenidos:', response.data);
                            console.log('INVOICE DATA RECEIVED - discount_value:', response.data.discount_value, 'discount_type:', response.data.discount_type);
                            console.log('FULL RESPONSE:', JSON.stringify(response));
                        }

                        // Verificar si existe el módulo WhatsAppCustomMessage
                        if (typeof WhatsAppCustomMessage !== 'undefined') {
                            // Crear un mensaje personalizado con información de descuento
                            const customMessage = WhatsAppCustomMessage.createInvoiceMessage(response.data);

                            // Actualizar el mensaje en los datos de respuesta
                            response.data.message = customMessage;

                            if (this.options.debug) {
                                console.log('Mensaje personalizado creado:', customMessage);
                            }
                        } else {
                            console.warn('WhatsAppCustomMessage no está disponible. Usando mensaje predeterminado.');
                        }

                        resolve(response.data);
                    } else {
                        const errorMsg = response ? response.message : 'Error desconocido al obtener datos de factura';
                        reject(new Error(errorMsg));
                    }
                } else {
                    reject(new Error(`Error al obtener datos de factura: ${xhr.status}`));
                }
            };

            xhr.onerror = () => {
                reject(new Error('Error de red al obtener datos de factura'));
            };

            // Enviar la solicitud
            const formData = new URLSearchParams();
            formData.append('invoice_id', invoiceId);
            xhr.send(formData.toString());
        });
    },

    /**
     * Enviar mensaje de WhatsApp a través del servidor local
     * @private
     * @param {string} phone Número de teléfono del destinatario
     * @param {string} message Mensaje a enviar
     * @param {number} invoiceId ID de la factura (para referencia)
     * @returns {Promise<Object>} Resultado del envío
     */
    _sendWhatsAppMessage: function(phone, message, invoiceId) {
        return new Promise((resolve, reject) => {
            try {
                if (this.options.debug) {
                    console.log(`Enviando mensaje de WhatsApp a ${phone} para factura #${invoiceId}`);
                }

                // Verificar si hay un número de teléfono válido
                if (!phone || phone === 'null' || phone === 'undefined' || phone.trim() === '') {
                    console.error(`No se puede enviar mensaje: número de teléfono no proporcionado para factura #${invoiceId}`);
                    return reject(new Error('رقم الهاتف غير متوفر'));
                }

                // Formatear el número de teléfono correctamente para WhatsApp
                const formattedPhone = this._formatPhoneNumber(phone);

                if (this.options.debug) {
                    console.log(`Número formateado: ${formattedPhone}`);
                }

                // Verificar si el número formateado es válido
                if (!formattedPhone || formattedPhone.trim() === '') {
                    console.error(`No se puede enviar mensaje: número de teléfono formateado inválido para factura #${invoiceId}`);
                    return reject(new Error('رقم الهاتف غير صالح بعد التنسيق'));
                }

                // Realizar una solicitud al servidor local para enviar el mensaje
                const xhr = new XMLHttpRequest();
                xhr.open('POST', `${this.serverUrl}/send`, true);
                xhr.setRequestHeader('Content-Type', 'application/json');
                xhr.responseType = 'json';
                xhr.timeout = this.options.timeout; // Usar el tiempo de espera configurado en las opciones

                xhr.onload = () => {
                    if (xhr.status === 200) {
                        let response;

                        // Manejar diferentes tipos de respuestas
                        if (typeof xhr.response === 'object') {
                            response = xhr.response;
                        } else if (typeof xhr.responseText === 'string') {
                            try {
                                response = JSON.parse(xhr.responseText);
                            } catch (e) {
                                response = { status: 'error', message: 'Respuesta no válida del servidor' };
                            }
                        } else {
                            response = { status: 'error', message: 'Respuesta desconocida del servidor' };
                        }

                        if (response && response.status === 'success') {
                            if (this.options.debug) {
                                console.log(`Mensaje enviado con éxito a ${formattedPhone}:`, response);
                            }
                            resolve(response);
                        } else {
                            const errorMsg = response ? response.message : 'Error desconocido al enviar mensaje';
                            if (this.options.debug) {
                                console.error(`Error al enviar mensaje a ${formattedPhone}: ${errorMsg}`);
                            }
                            reject(new Error(errorMsg));
                        }
                    } else {
                        const errorMsg = `Error al enviar mensaje: ${xhr.status}`;
                        if (this.options.debug) {
                            console.error(errorMsg);
                        }
                        reject(new Error(errorMsg));
                    }
                };

                xhr.ontimeout = () => {
                    const errorMsg = 'Tiempo de espera agotado al enviar mensaje';
                    if (this.options.debug) {
                        console.error(errorMsg);
                    }

                    // A pesar del timeout, el mensaje podría haberse enviado correctamente
                    // Registrar como advertencia en lugar de error y resolver la promesa
                    console.warn('El mensaje podría haberse enviado a pesar del timeout');
                    resolve({
                        status: 'warning',
                        message: 'Tiempo de espera agotado, pero el mensaje podría haberse enviado correctamente'
                    });
                };

                xhr.onerror = () => {
                    const errorMsg = 'Error de red al enviar mensaje';
                    if (this.options.debug) {
                        console.error(errorMsg);
                    }
                    reject(new Error(errorMsg));
                };

                // Enviar los datos
                const sendData = {
                    phone: formattedPhone,
                    message: message
                };

                if (this.options.debug) {
                    console.log('Enviando datos al servidor:', sendData);
                }

                xhr.send(JSON.stringify(sendData));
            } catch (error) {
                if (this.options.debug) {
                    console.error('Error al enviar mensaje:', error);
                }
                reject(error);
            }
        });
    },



    /**
     * Registrar el resultado de la notificación en el sistema
     * @private
     * @param {number} invoiceId ID de la factura
     * @param {boolean} success Indica si la operación fue exitosa
     * @param {Object} result Resultado de la operación
     * @returns {Promise<Object>} Resultado del registro
     */
    _logNotificationResult: function(invoiceId, success, result) {
        return new Promise((resolve, reject) => {
            const xhr = new XMLHttpRequest();
            xhr.open('POST', this.apiUrl + 'invoice_notification.php?action=log_notification', true);
            xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');
            xhr.responseType = 'json';

            xhr.onload = () => {
                if (xhr.status === 200) {
                    const response = xhr.response;

                    if (response && response.status === 'success') {
                        resolve(response);
                    } else {
                        // No rechazamos la promesa aquí para no interrumpir el flujo principal
                        console.warn('Error al registrar notificación:', response ? response.message : 'Error desconocido');
                        resolve({ status: 'warning', message: 'Error al registrar notificación' });
                    }
                } else {
                    console.warn(`Error al registrar notificación: ${xhr.status}`);
                    resolve({ status: 'warning', message: `Error al registrar notificación: ${xhr.status}` });
                }
            };

            xhr.onerror = () => {
                console.warn('Error de red al registrar notificación');
                resolve({ status: 'warning', message: 'Error de red al registrar notificación' });
            };

            // Enviar la solicitud
            const formData = new URLSearchParams();
            formData.append('invoice_id', invoiceId);
            formData.append('success', success ? 1 : 0);
            formData.append('details', JSON.stringify(result));
            xhr.send(formData.toString());
        });
    },

    /**
     * Mostrar mensaje de éxito al usuario
     * @private
     */
    _showSuccessMessage: function() {
        // Verificar si ya existe un mensaje
        let messageContainer = document.querySelector('.whatsapp-notification-message');

        if (!messageContainer) {
            // Crear el contenedor del mensaje
            messageContainer = document.createElement('div');
            messageContainer.className = 'whatsapp-notification-message';
            messageContainer.innerHTML = `
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="fab fa-whatsapp"></i> Notificación de WhatsApp enviada con éxito.
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
            `;

            // Añadir estilos
            const style = document.createElement('style');
            style.textContent = `
                .whatsapp-notification-message {
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    z-index: 9999;
                    max-width: 350px;
                }

                .whatsapp-notification-message .alert {
                    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
                    border-left: 4px solid #25D366;
                }

                .whatsapp-notification-message .fa-whatsapp {
                    color: #25D366;
                    margin-right: 8px;
                }
            `;

            document.head.appendChild(style);
            document.body.appendChild(messageContainer);

            // Eliminar el mensaje después de 5 segundos
            setTimeout(() => {
                if (document.body.contains(messageContainer)) {
                    messageContainer.remove();
                }
            }, 5000);
        }
    },

    /**
     * Formatear número de teléfono para WhatsApp
     * @private
     * @param {string} phone Número de teléfono
     * @returns {string} Número de teléfono formateado
     */
    _formatPhoneNumber: function(phone) {
        if (!phone) return '';

        // Eliminar todos los caracteres no numéricos excepto el signo +
        let cleaned = phone.replace(/[^\d+]/g, '');

        // Si el número no comienza con +, agregar el código de país de Egipto (+20)
        if (!cleaned.startsWith('+')) {
            // Si comienza con 0, eliminarlo
            if (cleaned.startsWith('0')) {
                cleaned = cleaned.substring(1);
            }

            // Agregar el código de país
            cleaned = '+20' + cleaned;
        }

        // Asegurarse de que el número tenga el formato correcto para WhatsApp
        // WhatsApp espera el formato internacional sin el signo +
        return cleaned.replace(/^\+/, '');
    }
};

// Inicializar el módulo cuando el DOM esté listo
document.addEventListener('DOMContentLoaded', function() {
    WhatsAppInvoiceNotification.init();
});
