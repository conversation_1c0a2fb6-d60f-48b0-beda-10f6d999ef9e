<?php
/**
 * صفحة تقرير المخزون
 * تعرض تقرير شامل عن حالة المخزون وقيمته والمنتجات المستهلكة والمباعة والأرباح/الخسائر
 */

// تعريف ثابت لمنع الوصول المباشر للملفات المرتبطة
define('BASEPATH', true);

// استدعاء ملف التهيئة
require_once '../../config/init.php';

// التحقق من تسجيل الدخول
redirectIfNotLoggedIn();

// التحقق من صلاحية الوصول
if (!hasPermission('reports_view') && !hasPermission('inventory_view')) {
    $_SESSION['error_message'] = 'ليس لديك صلاحية لعرض تقارير المخزون';
    header('Location: ' . BASE_URL . 'pages/dashboard.php');
    exit;
}

// عنوان الصفحة
$pageTitle = 'تقرير المخزون';

// استدعاء رأس الصفحة
include '../../includes/templates/header.php';

// الحصول على معرف الفرع الحالي
$currentBranchId = $_SESSION['user_branch_id'] ?? null;

// التحقق مما إذا كان المستخدم مديرًا (يمكنه رؤية جميع الفروع)
$isAdmin = hasPermission('admin_access');

// الحصول على قائمة الفروع
$branchModel = new Branch($db);
$branches = $branchModel->getBranches();

// الحصول على فئات المنتجات
$productModel = new Product($db);
$categories = $productModel->getProductCategories();
?>

<!-- المحتوى الرئيسي للصفحة -->
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- عنوان الصفحة والأزرار -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h4 class="mb-0"><?php echo $pageTitle; ?></h4>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="<?php echo BASE_URL; ?>pages/dashboard.php">الرئيسية</a></li>
                            <li class="breadcrumb-item"><a href="<?php echo BASE_URL; ?>pages/reports/reports.php">التقارير</a></li>
                            <li class="breadcrumb-item active" aria-current="page">تقرير المخزون</li>
                        </ol>
                    </nav>
                </div>
                <div class="btn-group">
                    <button type="button" class="btn btn-outline-primary export-report-btn" data-report-type="inventory">
                        <i class="fas fa-file-export me-1"></i> تصدير
                    </button>
                    <button type="button" class="btn btn-outline-secondary print-report-btn" data-report-type="inventory">
                        <i class="fas fa-print me-1"></i> طباعة
                    </button>
                </div>
            </div>

            <!-- بطاقة فلاتر التقرير -->
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-white py-3">
                    <h5 class="mb-0">فلاتر التقرير</h5>
                </div>
                <div class="card-body">
                    <form id="inventory-report-form">
                        <div class="row">
                            <?php if ($isAdmin): ?>
                            <div class="col-md-3 mb-3">
                                <label for="inventory-branch-select" class="form-label">الفرع</label>
                                <select class="form-select" id="inventory-branch-select" name="branch_id">
                                    <option value="">جميع الفروع</option>
                                    <?php foreach ($branches as $branch): ?>
                                    <option value="<?php echo $branch['id']; ?>" <?php echo ($currentBranchId == $branch['id']) ? 'selected' : ''; ?>>
                                        <?php echo $branch['name']; ?>
                                    </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <?php else: ?>
                            <input type="hidden" id="inventory-branch-select" name="branch_id" value="<?php echo $currentBranchId; ?>">
                            <?php endif; ?>

                            <div class="col-md-3 mb-3">
                                <label for="inventory-category-select" class="form-label">فئة المنتج</label>
                                <select class="form-select" id="inventory-category-select" name="category_id">
                                    <option value="">جميع الفئات</option>
                                    <?php foreach ($categories as $category): ?>
                                    <option value="<?php echo $category['id']; ?>">
                                        <?php echo $category['name']; ?>
                                    </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>

                            <div class="col-md-4 mb-3">
                                <label for="inventory-date-range" class="form-label">الفترة الزمنية</label>
                                <input type="text" class="form-control" id="inventory-date-range" name="date_range">
                            </div>

                            <div class="col-md-2 mb-3 d-flex align-items-end">
                                <button type="submit" class="btn btn-primary w-100">
                                    <i class="fas fa-filter me-1"></i> عرض التقرير
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- تنبيه التحميل -->
            <div class="alert alert-info d-flex align-items-center d-none" id="loadingAlert">
                <div class="spinner-border spinner-border-sm me-2" role="status">
                    <span class="visually-hidden">جاري التحميل...</span>
                </div>
                <div>
                    جاري تحميل التقرير، يرجى الانتظار...
                </div>
            </div>

            <!-- تنبيه الخطأ -->
            <div class="alert alert-danger d-none" id="errorAlert"></div>

            <!-- قسم نتائج التقرير -->
            <div id="inventory-report-results" class="d-none">
                <!-- ملخص المخزون -->
                <div class="row mb-4">
                    <div class="col-md-3 mb-3">
                        <div class="card bg-primary text-white h-100">
                            <div class="card-body">
                                <h6 class="card-title">إجمالي قيمة المخزون (التكلفة)</h6>
                                <h3 class="mb-0" id="total-inventory-cost">0</h3>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="card bg-success text-white h-100">
                            <div class="card-body">
                                <h6 class="card-title">إجمالي قيمة المخزون (البيع)</h6>
                                <h3 class="mb-0" id="total-inventory-value">0</h3>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="card bg-info text-white h-100">
                            <div class="card-body">
                                <h6 class="card-title">الربح المتوقع</h6>
                                <h3 class="mb-0" id="expected-profit">0</h3>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="card bg-warning text-white h-100">
                            <div class="card-body">
                                <h6 class="card-title">عدد المنتجات</h6>
                                <h3 class="mb-0" id="total-products-count">0</h3>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- ملخص المبيعات والاستهلاك -->
                <div class="row mb-4">
                    <div class="col-md-6 mb-3">
                        <div class="card h-100">
                            <div class="card-header bg-white">
                                <h5 class="mb-0">المنتجات المباعة</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">إجمالي الكمية</label>
                                            <h4 id="sold-products-quantity">0</h4>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">إجمالي القيمة</label>
                                            <h4 id="sold-products-value">0</h4>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-0">
                                            <label class="form-label">إجمالي التكلفة</label>
                                            <h4 id="sold-products-cost">0</h4>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-0">
                                            <label class="form-label">صافي الربح</label>
                                            <h4 id="sold-products-profit">0</h4>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 mb-3">
                        <div class="card h-100">
                            <div class="card-header bg-white">
                                <h5 class="mb-0">المنتجات المستهلكة</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">إجمالي الكمية</label>
                                            <h4 id="consumed-products-quantity">0</h4>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">إجمالي القيمة</label>
                                            <h4 id="consumed-products-value">0</h4>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- جداول التقرير -->
                <div class="row">
                    <!-- المنتجات الأكثر مبيعًا -->
                    <div class="col-md-6 mb-4">
                        <div class="card h-100">
                            <div class="card-header bg-white">
                                <h5 class="mb-0">المنتجات الأكثر مبيعًا</h5>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-bordered table-hover" id="top-selling-products-table">
                                        <thead class="table-light">
                                            <tr>
                                                <th>#</th>
                                                <th>المنتج</th>
                                                <th>الكمية المباعة</th>
                                                <th>إجمالي المبيعات</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <!-- سيتم ملؤها بواسطة JavaScript -->
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- المنتجات الأكثر استهلاكًا -->
                    <div class="col-md-6 mb-4">
                        <div class="card h-100">
                            <div class="card-header bg-white">
                                <h5 class="mb-0">المنتجات الأكثر استهلاكًا</h5>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-bordered table-hover" id="top-consumed-products-table">
                                        <thead class="table-light">
                                            <tr>
                                                <th>#</th>
                                                <th>المنتج</th>
                                                <th>الكمية المستهلكة</th>
                                                <th>إجمالي القيمة</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <!-- سيتم ملؤها بواسطة JavaScript -->
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- المنتجات منخفضة المخزون -->
                <div class="row">
                    <div class="col-12 mb-4">
                        <div class="card">
                            <div class="card-header bg-white">
                                <h5 class="mb-0">المنتجات منخفضة المخزون</h5>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-bordered table-hover" id="low-stock-products-table">
                                        <thead class="table-light">
                                            <tr>
                                                <th>#</th>
                                                <th>المنتج</th>
                                                <th>الفئة</th>
                                                <th>الكمية الحالية</th>
                                                <th>الحد الأدنى</th>
                                                <th>سعر التكلفة</th>
                                                <th>سعر البيع</th>
                                                <th>القيمة الإجمالية</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <!-- سيتم ملؤها بواسطة JavaScript -->
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- استدعاء قالب الفوتر -->
<?php include '../../includes/templates/footer.php'; ?>

<!-- مكتبة لتصدير الجداول -->
<script src="https://cdn.jsdelivr.net/npm/tableexport@5.2.0/dist/js/tableexport.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/xlsx@0.18.5/dist/xlsx.full.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/file-saver@2.0.5/dist/FileSaver.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf-autotable/3.5.28/jspdf.plugin.autotable.min.js"></script>

<!-- استدعاء ملف جافاسكريبت خاص بتقرير المخزون -->
<script src="<?php echo BASE_URL; ?>assets/js/inventory-report.js"></script>

<script>
// تعريف المتغيرات العامة
const BASE_URL = '<?php echo BASE_URL; ?>';

$(document).ready(function() {
    // تهيئة حقل نطاق التاريخ
    $('#inventory-date-range').daterangepicker({
        locale: {
            format: 'YYYY/MM/DD',
            applyLabel: 'تطبيق',
            cancelLabel: 'إلغاء',
            fromLabel: 'من',
            toLabel: 'إلى',
            customRangeLabel: 'مخصص',
            daysOfWeek: ['أحد', 'إثنين', 'ثلاثاء', 'أربعاء', 'خميس', 'جمعة', 'سبت'],
            monthNames: ['يناير', 'فبراير', 'مارس', 'إبريل', 'مايو', 'يونيو', 'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'],
            firstDay: 6
        },
        startDate: moment().subtract(365, 'days'),
        endDate: moment(),
        ranges: {
            'اليوم': [moment(), moment()],
            'أمس': [moment().subtract(1, 'days'), moment().subtract(1, 'days')],
            'آخر 7 أيام': [moment().subtract(6, 'days'), moment()],
            'آخر 30 يوم': [moment().subtract(29, 'days'), moment()],
            'هذا الشهر': [moment().startOf('month'), moment().endOf('month')],
            'الشهر الماضي': [moment().subtract(1, 'month').startOf('month'), moment().subtract(1, 'month').endOf('month')]
        }
    });

    // معالجة نموذج التقرير
    $('#inventory-report-form').on('submit', function(e) {
        e.preventDefault();
        generateInventoryReport();
    });

    // تحميل التقرير عند تحميل الصفحة
    generateInventoryReport();
});

/**
 * إنشاء تقرير المخزون
 */
function generateInventoryReport() {
    // إظهار تنبيه التحميل
    $('#loadingAlert').removeClass('d-none');
    $('#errorAlert').addClass('d-none');
    $('#inventory-report-results').addClass('d-none');

    // الحصول على قيم الفلاتر
    const dateRange = $('#inventory-date-range').data('daterangepicker');
    const formData = {
        type: 'inventory',
        branch_id: $('#inventory-branch-select').val(),
        category_id: $('#inventory-category-select').val(),
        start_date: dateRange.startDate.format('YYYY-MM-DD'),
        end_date: dateRange.endDate.format('YYYY-MM-DD')
    };

    // إرسال طلب AJAX
    $.ajax({
        url: BASE_URL + 'api/reports.php',
        type: 'GET',
        data: formData,
        dataType: 'json',
        success: function(response) {
            // إخفاء تنبيه التحميل
            $('#loadingAlert').addClass('d-none');

            if (response.success) {
                renderInventoryReport(response.data);
            } else {
                // عرض رسالة الخطأ
                $('#errorAlert').removeClass('d-none').text(response.message || 'حدث خطأ أثناء تحميل التقرير');
            }
        },
        error: function(xhr, status, error) {
            // إخفاء تنبيه التحميل وعرض رسالة الخطأ
            $('#loadingAlert').addClass('d-none');
            $('#errorAlert').removeClass('d-none').text('حدث خطأ أثناء الاتصال بالخادم: ' + error);
        }
    });
}

/**
 * عرض تقرير المخزون
 * @param {Object} report - بيانات التقرير
 */
function renderInventoryReport(report) {
    // إظهار قسم نتائج التقرير
    $('#inventory-report-results').removeClass('d-none');

    // تنسيق الأرقام كعملة
    const formatCurrency = (value) => {
        return parseFloat(value || 0).toLocaleString('ar-EG', {
            style: 'currency',
            currency: 'EGP'
        });
    };

    // تعبئة بيانات ملخص المخزون
    $('#total-inventory-cost').text(formatCurrency(report.stock_value.total_cost));
    $('#total-inventory-value').text(formatCurrency(report.stock_value.total_value));
    $('#expected-profit').text(formatCurrency(report.stock_value.expected_profit));
    $('#total-products-count').text(report.summary.total_products || 0);

    // تعبئة بيانات المنتجات المباعة
    $('#sold-products-quantity').text(report.sold_products.total_quantity || 0);
    $('#sold-products-value').text(formatCurrency(report.sold_products.total_value));
    $('#sold-products-cost').text(formatCurrency(report.sold_products.total_cost));
    $('#sold-products-profit').text(formatCurrency(report.sold_products.total_profit));

    // تعبئة بيانات المنتجات المستهلكة
    $('#consumed-products-quantity').text(report.consumed_products.total_quantity || 0);
    $('#consumed-products-value').text(formatCurrency(report.consumed_products.total_value));

    // تعبئة جدول المنتجات الأكثر مبيعًا
    const topSellingTable = $('#top-selling-products-table tbody');
    topSellingTable.empty();

    if (report.top_selling_products && report.top_selling_products.length > 0) {
        report.top_selling_products.forEach((product, index) => {
            topSellingTable.append(`
                <tr>
                    <td>${index + 1}</td>
                    <td>${product.product_name}</td>
                    <td>${product.quantity}</td>
                    <td>${formatCurrency(product.total_value)}</td>
                </tr>
            `);
        });
    } else {
        topSellingTable.append('<tr><td colspan="4" class="text-center">لا توجد بيانات</td></tr>');
    }

    // تعبئة جدول المنتجات الأكثر استهلاكًا
    const topConsumedTable = $('#top-consumed-products-table tbody');
    topConsumedTable.empty();

    if (report.top_consumed_products && report.top_consumed_products.length > 0) {
        report.top_consumed_products.forEach((product, index) => {
            topConsumedTable.append(`
                <tr>
                    <td>${index + 1}</td>
                    <td>${product.product_name}</td>
                    <td>${product.quantity}</td>
                    <td>${formatCurrency(product.total_value)}</td>
                </tr>
            `);
        });
    } else {
        topConsumedTable.append('<tr><td colspan="4" class="text-center">لا توجد بيانات</td></tr>');
    }

    // تعبئة جدول المنتجات منخفضة المخزون
    const lowStockTable = $('#low-stock-products-table tbody');
    lowStockTable.empty();

    if (report.low_stock_products && report.low_stock_products.length > 0) {
        report.low_stock_products.forEach((product, index) => {
            lowStockTable.append(`
                <tr>
                    <td>${index + 1}</td>
                    <td>${product.product_name}</td>
                    <td>${product.category_name || 'غير مصنف'}</td>
                    <td>${product.quantity}</td>
                    <td>${product.min_quantity}</td>
                    <td>${formatCurrency(product.cost)}</td>
                    <td>${formatCurrency(product.price)}</td>
                    <td>${formatCurrency(product.quantity * product.price)}</td>
                </tr>
            `);
        });
    } else {
        lowStockTable.append('<tr><td colspan="8" class="text-center">لا توجد منتجات منخفضة المخزون</td></tr>');
    }
}
</script>
