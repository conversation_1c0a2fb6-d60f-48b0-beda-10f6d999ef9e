/**
 * تنسيقات كروت لوحة التحكم
 * Dashboard Cards Styles
 */

/* تنسيقات عامة للكروت */
.dashboard-card {
    border-radius: 10px;
    overflow: hidden;
    transition: all 0.3s ease;
    margin-bottom: 20px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    border: none;
}

.dashboard-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
}

.dashboard-card .card-body {
    padding: 20px;
}

.dashboard-card .card-title {
    font-weight: 700;
    margin-bottom: 5px;
    font-size: 1rem;
    color: #6c757d;
}

.dashboard-card .card-value {
    font-size: 1.8rem;
    font-weight: 700;
    margin-bottom: 0;
}

/* أيقونات الكروت */
.dashboard-card .icon-wrapper {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 15px;
}

.dashboard-card .icon-wrapper i {
    font-size: 24px;
}

/* ألوان الكروت */
.dashboard-card.card-primary {
    background: linear-gradient(45deg, #4e73df, #6f86e5);
    color: white;
}

.dashboard-card.card-success {
    background: linear-gradient(45deg, #1cc88a, #36e2bd);
    color: white;
}

.dashboard-card.card-info {
    background: linear-gradient(45deg, #36b9cc, #4dd4e7);
    color: white;
}

.dashboard-card.card-warning {
    background: linear-gradient(45deg, #f6c23e, #f8d876);
    color: white;
}

.dashboard-card.card-danger {
    background: linear-gradient(45deg, #e74a3b, #f07d73);
    color: white;
}

.dashboard-card.card-secondary {
    background: linear-gradient(45deg, #858796, #a2a4b0);
    color: white;
}

/* أيقونات خاصة بالكروت */
.icon-sales {
    background-color: rgba(78, 115, 223, 0.2);
    color: #4e73df;
}

.icon-appointments {
    background-color: rgba(54, 185, 204, 0.2);
    color: #36b9cc;
}

.icon-expenses {
    background-color: rgba(231, 74, 59, 0.2);
    color: #e74a3b;
}

.icon-inventory {
    background-color: rgba(246, 194, 62, 0.2);
    color: #f6c23e;
}

.icon-customers {
    background-color: rgba(28, 200, 138, 0.2);
    color: #1cc88a;
}

.icon-employees {
    background-color: rgba(133, 135, 150, 0.2);
    color: #858796;
}

/* تنسيقات للكروت الصغيرة */
.small-card {
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 15px;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.small-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.small-card .card-info {
    flex: 1;
}

.small-card .card-title {
    font-size: 0.9rem;
    margin-bottom: 5px;
    color: #6c757d;
}

.small-card .card-value {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 0;
}

.small-card .card-icon {
    width: 50px;
    height: 50px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 15px;
}

.small-card .card-icon i {
    font-size: 20px;
}

/* ألوان الكروت الصغيرة */
.small-card.bg-primary-light {
    background-color: #eef2ff;
}

.small-card.bg-success-light {
    background-color: #e6fff5;
}

.small-card.bg-info-light {
    background-color: #e6f9ff;
}

.small-card.bg-warning-light {
    background-color: #fff8e6;
}

.small-card.bg-danger-light {
    background-color: #ffe6e6;
}

/* تنسيقات للكروت في الصفحة الرئيسية */
.home-card {
    border-radius: 10px;
    overflow: hidden;
    transition: all 0.3s ease;
    margin-bottom: 20px;
    border: none;
}

.home-card .card-body {
    padding: 20px;
}

.home-card .card-icon {
    font-size: 24px;
    margin-bottom: 15px;
}

.home-card .card-title {
    font-weight: 600;
    margin-bottom: 10px;
}

.home-card .card-text {
    color: #6c757d;
}
