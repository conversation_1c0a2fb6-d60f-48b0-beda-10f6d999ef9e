/**
 * إدارة الإشعارات
 */
$(document).ready(function() {
    // التحقق من تعريف المتغيرات المطلوبة
    console.log('BASE_URL:', typeof BASE_URL !== 'undefined' ? BASE_URL : 'غير معرف');
    console.log('API_URL:', typeof API_URL !== 'undefined' ? API_URL : 'غير معرف');

    if (typeof API_URL === 'undefined') {
        console.error('خطأ: متغير API_URL غير معرف');
        return;
    }
    // تعريف المتغيرات
    const notificationsCount = $('.notifications-count');
    const notificationsList = $('.notifications-list');
    const emptyNotifications = $('.empty-notifications');
    const markAllReadBtn = $('.mark-all-read');

    // تحديث عدد الإشعارات غير المقروءة
    function updateNotificationsCount() {
        $.ajax({
            url: `${API_URL}notifications.php?action=get_unread_count`,
            method: 'GET',
            dataType: 'json',
            success: function(response) {
                if (response.status === 'success') {
                    const count = response.data.unread_count;

                    if (count > 0) {
                        notificationsCount.text(count > 99 ? '99+' : count);
                        notificationsCount.show();
                    } else {
                        notificationsCount.hide();
                    }
                }
            },
            error: function(xhr) {
                console.error('خطأ في الحصول على عدد الإشعارات غير المقروءة');
                console.log(xhr.responseText);
            }
        });
    }

    // تحميل الإشعارات
    function loadNotifications() {
        $.ajax({
            url: `${API_URL}notifications.php?action=get_user_notifications&limit=10`,
            method: 'GET',
            dataType: 'json',
            success: function(response) {
                if (response.status === 'success') {
                    renderNotifications(response.data.notifications);
                }
            },
            error: function(xhr) {
                console.error('خطأ في تحميل الإشعارات');
                console.log(xhr.responseText);
            }
        });
    }

    // عرض الإشعارات
    function renderNotifications(notifications) {
        notificationsList.empty();

        if (notifications.length === 0) {
            emptyNotifications.show();
            return;
        }

        emptyNotifications.hide();

        notifications.forEach(notification => {
            // تحديد أيقونة الإشعار بناءً على النوع
            let iconClass = 'fas fa-bell';
            let iconType = '';

            if (notification.type === 'appointment_reminder') {
                iconClass = 'fas fa-calendar-check';
                iconType = 'appointment';
            } else if (notification.type === 'appointment_minutes_reminder') {
                iconClass = 'fas fa-clock';
                iconType = 'appointment';
            } else if (notification.type === 'appointment_missed') {
                iconClass = 'fas fa-calendar-times';
                iconType = 'alert';
            } else if (notification.type === 'system') {
                iconClass = 'fas fa-cog';
                iconType = 'system';
            } else if (notification.type === 'alert') {
                iconClass = 'fas fa-exclamation-triangle';
                iconType = 'alert';
            }

            // تنسيق وقت الإشعار
            const notificationTime = formatNotificationTime(notification.created_at);

            // إنشاء عنصر الإشعار
            const notificationItem = $(`
                <div class="notification-item d-flex align-items-start ${notification.is_read == 0 ? 'unread' : ''}" data-id="${notification.id}" data-related-type="${notification.related_type || ''}" data-related-id="${notification.related_id || ''}" style="cursor: pointer;">
                    <div class="notification-icon ${iconType}">
                        <i class="${iconClass}"></i>
                    </div>
                    <div class="flex-grow-1">
                        <div class="notification-title">${notification.title}</div>
                        <div class="notification-text">${notification.message}</div>
                        <div class="notification-time">${notificationTime}</div>
                    </div>
                </div>
            `);

            // إضافة حدث النقر على الإشعار
            notificationItem.on('click', function(e) {
                e.preventDefault();
                e.stopPropagation();

                // تعيين الإشعار كمقروء
                markAsRead(notification.id);

                // إغلاق قائمة الإشعارات
                $('.dropdown-menu').removeClass('show');

                // إذا كان الإشعار مرتبط بعنصر، انتقل إليه
                if (notification.related_type && notification.related_id) {
                    // إضافة تأخير بسيط للسماح بإغلاق القائمة قبل الانتقال
                    setTimeout(function() {
                        navigateToRelatedItem(notification.related_type, notification.related_id);
                    }, 100);
                }
            });

            notificationsList.append(notificationItem);
        });
    }

    // تنسيق وقت الإشعار
    function formatNotificationTime(timestamp) {
        const now = new Date();
        const notificationDate = new Date(timestamp);
        const diffMs = now - notificationDate;
        const diffMins = Math.round(diffMs / 60000);
        const diffHours = Math.round(diffMs / 3600000);
        const diffDays = Math.round(diffMs / 86400000);

        if (diffMins < 1) {
            return 'الآن';
        } else if (diffMins < 60) {
            return `منذ ${diffMins} دقيقة`;
        } else if (diffHours < 24) {
            return `منذ ${diffHours} ساعة`;
        } else if (diffDays < 7) {
            return `منذ ${diffDays} يوم`;
        } else {
            return notificationDate.toLocaleDateString('ar-EG');
        }
    }

    // تعيين الإشعار كمقروء
    function markAsRead(notificationId) {
        // تحديث واجهة المستخدم فورًا لتجنب التأخير
        $(`.notification-item[data-id="${notificationId}"]`).removeClass('unread');

        // إرسال طلب لتحديث الإشعار في قاعدة البيانات
        $.ajax({
            url: `${API_URL}notifications.php?action=mark_as_read`,
            method: 'POST',
            data: { notification_id: notificationId },
            dataType: 'json',
            success: function(response) {
                if (response.status === 'success') {
                    // تحديث عدد الإشعارات غير المقروءة
                    updateNotificationsCount();
                    console.log(`تم تعيين الإشعار #${notificationId} كمقروء`);
                }
            },
            error: function(xhr) {
                console.error('خطأ في تعيين الإشعار كمقروء');
                console.log(xhr.responseText);

                // إعادة تصنيف الإشعار كغير مقروء في حالة الخطأ
                $(`.notification-item[data-id="${notificationId}"]`).addClass('unread');
            }
        });

        return true; // للسماح باستمرار الحدث
    }

    // تعيين جميع الإشعارات كمقروءة
    function markAllAsRead() {
        $.ajax({
            url: `${API_URL}notifications.php?action=mark_all_as_read`,
            method: 'POST',
            dataType: 'json',
            success: function(response) {
                if (response.status === 'success') {
                    // تحديث واجهة المستخدم
                    $('.notification-item').removeClass('unread');
                    notificationsCount.hide();
                }
            },
            error: function(xhr) {
                console.error('خطأ في تعيين جميع الإشعارات كمقروءة');
                console.log(xhr.responseText);
            }
        });
    }

    // الانتقال إلى العنصر المرتبط بالإشعار
    function navigateToRelatedItem(type, id) {
        if (!type || !id) {
            console.log('لا يوجد عنصر مرتبط بهذا الإشعار');
            return;
        }

        console.log(`الانتقال إلى العنصر المرتبط: ${type} - ${id}`);

        let url = '';

        switch (type) {
            case 'appointment':
                url = `${BASE_URL}pages/appointments/view.php?id=${id}`;
                break;
            case 'invoice':
                url = `${BASE_URL}pages/invoices/view.php?id=${id}`;
                break;
            case 'customer':
                url = `${BASE_URL}pages/customers/view.php?id=${id}`;
                break;
            case 'employee':
                url = `${BASE_URL}pages/employees/view.php?id=${id}`;
                break;
            case 'service':
                url = `${BASE_URL}pages/services/view.php?id=${id}`;
                break;
            case 'product':
                url = `${BASE_URL}pages/products/view.php?id=${id}`;
                break;
            case 'expense':
                url = `${BASE_URL}pages/expenses/view.php?id=${id}`;
                break;
            case 'day':
                url = `${BASE_URL}pages/days/view.php?id=${id}`;
                break;
            default:
                console.log(`نوع غير معروف: ${type}`);
                return;
        }

        // فتح الرابط في نفس النافذة
        window.location.href = url;
    }

    // تحميل الإشعارات عند فتح القائمة
    $('#notificationsMenu').on('click', function() {
        loadNotifications();
    });

    // تعيين جميع الإشعارات كمقروءة
    markAllReadBtn.on('click', function(e) {
        e.preventDefault();
        e.stopPropagation();
        markAllAsRead();
    });

    // تحديث عدد الإشعارات عند تحميل الصفحة
    updateNotificationsCount();

    // تحميل الإشعارات عند تحميل الصفحة
    // لا نحتاج لتحميلها فورًا، سيتم تحميلها عند النقر على زر الإشعارات

    // تحديث عدد الإشعارات كل دقيقة
    setInterval(updateNotificationsCount, 60000);

    // لا نحتاج لتحديث قائمة الإشعارات تلقائيًا، سيتم تحديثها عند النقر على زر الإشعارات
});
