<?php
/**
 * صفحة الإعدادات الرئيسية
 */

// تعريف ثابت لمنع الوصول المباشر للملفات المرتبطة
define('BASEPATH', true);

// استدعاء ملفات التهيئة
require_once '../../config/init.php';

// التحقق من تسجيل الدخول
requireLogin();

// التحقق من وجود صلاحية إدارة الإعدادات
requirePermission('settings_manage');

// عنوان الصفحة
$pageTitle = 'إعدادات النظام';

// استدعاء رأس الصفحة
include '../../includes/templates/header.php';
?>

<!-- المحتوى الرئيسي للصفحة -->
<div class="container-fluid">
    <!-- رأس الصفحة -->
    <div class="card border-0 shadow-sm mb-4">
        <div class="card-body">
            <h5 class="mb-0">
                <i class="fas fa-cogs me-2"></i> إعدادات النظام
            </h5>
        </div>
    </div>

    <!-- قائمة الإعدادات -->
    <div class="row">
        <!-- إعدادات النظام -->
        <div class="col-md-4 mb-4">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body">
                    <h5 class="card-title">
                        <i class="fas fa-server me-2 text-primary"></i> إعدادات النظام
                    </h5>
                    <p class="card-text">إعدادات النظام الأساسية والمظهر العام</p>
                    <a href="system.php" class="btn btn-primary">
                        <i class="fas fa-cog me-1"></i> إدارة الإعدادات
                    </a>
                </div>
            </div>
        </div>

        <!-- إعدادات الفواتير -->
        <div class="col-md-4 mb-4">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body">
                    <h5 class="card-title">
                        <i class="fas fa-file-invoice-dollar me-2 text-success"></i> إعدادات الفواتير
                    </h5>
                    <p class="card-text">إعدادات طباعة الفواتير والشعار والمعلومات</p>
                    <a href="invoice.php" class="btn btn-success">
                        <i class="fas fa-cog me-1"></i> إدارة الإعدادات
                    </a>
                </div>
            </div>
        </div>

        <!-- إعدادات الإشعارات -->
        <div class="col-md-4 mb-4">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body">
                    <h5 class="card-title">
                        <i class="fas fa-bell me-2 text-warning"></i> إعدادات الإشعارات
                    </h5>
                    <p class="card-text">إعدادات الإشعارات والتنبيهات وتذكيرات المواعيد</p>
                    <a href="notifications.php" class="btn btn-warning">
                        <i class="fas fa-cog me-1"></i> إدارة الإعدادات
                    </a>
                </div>
            </div>
        </div>

        <!-- إعدادات WhatsApp -->
        <div class="col-md-4 mb-4">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body">
                    <h5 class="card-title">
                        <i class="fab fa-whatsapp me-2 text-success"></i> إعدادات WhatsApp
                    </h5>
                    <p class="card-text">إعدادات إرسال رسائل WhatsApp التلقائية للعملاء</p>
                    <a href="whatsapp.php" class="btn btn-success">
                        <i class="fas fa-cog me-1"></i> إدارة الإعدادات
                    </a>
                </div>
            </div>
        </div>

        <!-- إعدادات WATI -->
        <div class="col-md-4 mb-4">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body">
                    <h5 class="card-title">
                        <i class="fab fa-whatsapp me-2 text-primary"></i> إعدادات WATI
                    </h5>
                    <p class="card-text">إعدادات إرسال رسائل WhatsApp باستخدام منصة WATI</p>
                    <a href="wati.php" class="btn btn-primary">
                        <i class="fas fa-cog me-1"></i> إدارة الإعدادات
                    </a>
                </div>
            </div>
        </div>

        <!-- حساب الخصومات للأيام السابقة -->
        <div class="col-md-4 mb-4">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body">
                    <h5 class="card-title">
                        <i class="fas fa-calculator me-2 text-danger"></i> حساب الخصومات
                    </h5>
                    <p class="card-text">حساب قيمة الخصومات للأيام السابقة وتحديث البيانات</p>
                    <a href="calculate_discounts.php" class="btn btn-danger">
                        <i class="fas fa-calculator me-1"></i> حساب الخصومات
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- استدعاء قالب الفوتر -->
<?php include '../../includes/templates/footer.php'; ?>