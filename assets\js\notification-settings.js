/**
 * معالج إعدادات الإشعارات
 */
$(document).ready(function() {
    // تسجيل معلومات التصحيح
    console.log('Notification settings JS loaded');

    // معالجة تغيير طريقة إرسال التذكيرات
    $('#reminder_method').on('change', function() {
        const method = $(this).val();
        if (method === 'js') {
            $('.js-reminder-options').show();
        } else {
            $('.js-reminder-options').hide();
        }
    });

    // معالجة تغيير تفعيل واتساب
    $('#enable_whatsapp').on('change', function() {
        if ($(this).is(':checked')) {
            $('#whatsapp_client_side').prop('disabled', false);
        } else {
            $('#whatsapp_client_side').prop('checked', false);
            $('#whatsapp_client_side').prop('disabled', true);
        }
    });

    // تهيئة الحالة الأولية لزر واتساب
    if (!$('#enable_whatsapp').is(':checked')) {
        $('#whatsapp_client_side').prop('disabled', true);
    }

    // تسجيل النموذج للتصحيح
    $('form').on('submit', function() {
        console.log('Form submitted');
        // تسجيل حالة الاختيارات
        console.log('WhatsApp enabled:', $('#enable_whatsapp').is(':checked'));
        console.log('WhatsApp client side:', $('#whatsapp_client_side').is(':checked'));
    });
});
