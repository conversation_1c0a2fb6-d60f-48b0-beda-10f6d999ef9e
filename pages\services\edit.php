<?php
/**
 * صفحة تعديل خدمة
 * تسمح للمستخدمين بتعديل بيانات الخدمات الموجودة
 */

// تعريف ثابت لمنع الوصول المباشر للملفات المرتبطة
define('BASEPATH', true);

// استدعاء ملف التهيئة
require_once '../../config/init.php';

// التحقق من تسجيل الدخول
redirectIfNotLoggedIn();

// التحقق من صلاحية الوصول
if (!hasPermission('services_edit')) {
    $_SESSION['error_message'] = 'ليس لديك صلاحية لتعديل الخدمات';
    header('Location: ' . BASE_URL . 'pages/services/index.php');
    exit;
}

// التحقق من وجود معرف الخدمة
if (!isset($_GET['id']) || empty($_GET['id'])) {
    $_SESSION['error_message'] = 'معرف الخدمة غير صحيح';
    header('Location: ' . BASE_URL . 'pages/services/index.php');
    exit;
}

$serviceId = intval($_GET['id']);

// الحصول على بيانات الخدمة
$serviceObj = new Service($db);
$service = $serviceObj->getServiceById($serviceId);

// التحقق من وجود الخدمة
if (!$service) {
    $_SESSION['error_message'] = 'الخدمة غير موجودة';
    header('Location: ' . BASE_URL . 'pages/services/index.php');
    exit;
}

// تحديد الفلاتر الافتراضية
$currentBranchId = $_SESSION['user_branch_id'];
$isAdmin = ($_SESSION['user_role'] === ROLE_ADMIN);

// الحصول على فئات الخدمات
$categories = $serviceObj->getServiceCategories();

// الحصول على الموظفين
$employeeObj = new Employee($db);
$branchFilter = $service['branch_id'] ?: $currentBranchId;
$employees = $employeeObj->getEmployees(['is_active' => 1, 'branch_id' => $branchFilter]);

// الحصول على الموظفين المرتبطين بالخدمة
$serviceEmployees = array_column($service['employees'] ?? [], 'id');

// الحصول على الفروع (للمدير فقط)
$branches = [];
if ($isAdmin) {
    $branchObj = new Branch($db);
    $branches = $branchObj->getBranches();
}

// الحصول على رمز العملة من الإعدادات
$settingsModel = new Settings($db);
$currencySymbol = $settingsModel->get('system_currency_symbol', 'ر.س');
$currencyName = $settingsModel->get('system_currency', 'ريال سعودي');

// عنوان الصفحة
$pageTitle = 'تعديل خدمة: ' . $service['name'];

// استدعاء قالب الرأس
include_once '../../includes/templates/header.php';
?>

<!-- بداية محتوى الصفحة -->
<div class="page-content">
    <div class="container-fluid">

        <!-- عنوان الصفحة والأزرار -->
        <div class="row mb-4 align-items-center">
            <div class="col-md-6">
                <h4 class="fw-bold mb-2"><?php echo $pageTitle; ?></h4>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb mb-0">
                        <li class="breadcrumb-item">
                            <a href="<?php echo BASE_URL . 'pages/dashboard.php'; ?>" class="text-decoration-none">
                                <i class="fas fa-home me-1"></i>الرئيسية
                            </a>
                        </li>
                        <li class="breadcrumb-item">
                            <a href="<?php echo BASE_URL . 'pages/services/index.php'; ?>" class="text-decoration-none">
                                الخدمات
                            </a>
                        </li>
                        <li class="breadcrumb-item active" aria-current="page">تعديل خدمة</li>
                    </ol>
                </nav>
            </div>
            <div class="col-md-6 text-md-end">
                <a href="<?php echo BASE_URL . 'pages/services/index.php'; ?>" class="btn btn-secondary">
                    <i class="fas fa-arrow-right me-1"></i> العودة للخدمات
                </a>
            </div>
        </div>

        <!-- نموذج تعديل خدمة -->
        <div class="card">
            <div class="card-header bg-light">
                <h5 class="mb-0">معلومات الخدمة</h5>
            </div>
            <div class="card-body">
                <form id="edit-service-form" method="post">
                    <input type="hidden" name="id" value="<?php echo $service['id']; ?>">

                    <!-- البيانات الأساسية -->
                    <div class="row mb-4">
                        <div class="col-12 mb-3">
                            <h6 class="fw-bold text-primary">البيانات الأساسية</h6>
                        </div>

                        <div class="col-md-6 mb-3">
                            <label for="name" class="form-label">اسم الخدمة <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="name" name="name" value="<?php echo $service['name']; ?>" required>
                        </div>

                        <div class="col-md-6 mb-3">
                            <label for="category_id" class="form-label">الفئة</label>
                            <select class="form-select" id="category_id" name="category_id">
                                <option value="">-- اختر الفئة --</option>
                                <?php foreach ($categories as $category): ?>
                                    <option value="<?php echo $category['id']; ?>" <?php echo ($category['id'] == $service['category_id']) ? 'selected' : ''; ?>>
                                        <?php echo $category['name']; ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <div class="col-md-6 mb-3">
                            <label for="price" class="form-label">السعر <span class="text-danger">*</span></label>
                            <div class="input-group">
                                <input type="number" class="form-control" id="price" name="price" min="0" step="0.01" value="<?php echo $service['price']; ?>" required>
                                <span class="input-group-text"><?php echo $currencySymbol; ?></span>
                            </div>
                        </div>

                        <div class="col-md-6 mb-3">
                            <label for="duration" class="form-label">مدة الخدمة (بالدقائق)</label>
                            <input type="number" class="form-control" id="duration" name="duration" min="1" value="<?php echo $service['duration']; ?>">
                        </div>

                        <?php if ($isAdmin): ?>
                            <div class="col-md-6 mb-3">
                                <label for="branch_id" class="form-label">الفرع</label>
                                <select class="form-select" id="branch_id" name="branch_id">
                                    <option value="">جميع الفروع</option>
                                    <?php foreach ($branches as $branch): ?>
                                        <option value="<?php echo $branch['id']; ?>" <?php echo ($branch['id'] == $service['branch_id']) ? 'selected' : ''; ?>>
                                            <?php echo $branch['name']; ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        <?php else: ?>
                            <input type="hidden" name="branch_id" id="branch_id" value="<?php echo $service['branch_id']; ?>">
                        <?php endif; ?>

                        <div class="col-md-6 mb-3">
                            <label for="is_active" class="form-label">الحالة</label>
                            <select class="form-select" id="is_active" name="is_active">
                                <option value="1" <?php echo ($service['is_active'] == 1) ? 'selected' : ''; ?>>نشط</option>
                                <option value="0" <?php echo ($service['is_active'] == 0) ? 'selected' : ''; ?>>غير نشط</option>
                            </select>
                        </div>
                    </div>

                    <!-- الوصف -->
                    <div class="row mb-4">
                        <div class="col-12 mb-3">
                            <h6 class="fw-bold text-primary">وصف الخدمة</h6>
                        </div>

                        <div class="col-12">
                            <label for="description" class="form-label">الوصف</label>
                            <textarea class="form-control" id="description" name="description" rows="4"><?php echo $service['description']; ?></textarea>
                        </div>
                    </div>

                    <!-- الموظفون المرتبطون -->
                    <div class="row">
                        <div class="col-12 mb-3">
                            <h6 class="fw-bold text-primary">الموظفون المتاحون للخدمة</h6>
                            <p class="text-muted">حدد الموظفين الذين يمكنهم تقديم هذه الخدمة</p>
                        </div>

                        <div class="col-12">
                            <div class="card">
                                <div class="card-body p-0" id="employees-container">
                                    <?php if (!empty($employees)): ?>
                                        <div class="table-responsive">
                                            <table class="table table-bordered table-hover">
                                                <thead class="table-light">
                                                    <tr>
                                                        <th width="5%">
                                                            <div class="form-check">
                                                                <input class="form-check-input select-all-employees" type="checkbox" id="select-all">
                                                            </div>
                                                        </th>
                                                        <th width="30%">اسم الموظف</th>
                                                        <th width="25%">المسمى الوظيفي</th>
                                                        <?php if ($isAdmin): ?>
                                                            <th width="20%">الفرع</th>
                                                        <?php endif; ?>
                                                    </tr>
                                                </thead>
                                                <tbody id="employees-table-body">
                                                    <?php foreach ($employees as $employee): ?>
                                                        <tr class="employee-row" data-branch-id="<?php echo $employee['branch_id']; ?>">
                                                            <td>
                                                                <div class="form-check">
                                                                    <input class="form-check-input employee-checkbox"
                                                                           type="checkbox"
                                                                           name="employees[]"
                                                                           value="<?php echo $employee['id']; ?>"
                                                                           <?php echo in_array($employee['id'], $serviceEmployees) ? 'checked' : ''; ?>>
                                                                </div>
                                                            </td>
                                                            <td><?php echo $employee['name']; ?></td>
                                                            <td><?php echo $employee['position'] ?: '-'; ?></td>
                                                            <?php if ($isAdmin): ?>
                                                                <td><?php echo $employee['branch_name'] ?: '-'; ?></td>
                                                            <?php endif; ?>
                                                        </tr>
                                                    <?php endforeach; ?>
                                                </tbody>
                                            </table>
                                        </div>
                                    <?php else: ?>
                                        <div class="alert alert-info m-3">
                                            لا يوجد موظفين متاحين حال/.
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- أزرار الإجراءات -->
                    <div class="row mt-4">
                        <div class="col-12 text-end">
                            <hr>
                            <a href="<?php echo BASE_URL . 'pages/services/index.php'; ?>" class="btn btn-outline-secondary me-2">
                                <i class="fas fa-times me-1"></i> إلغاء
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-1"></i> حفظ التغييرات
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>

    </div>
</div>

<!-- جافاسكربت خاص بالصفحة -->
<script>
    // التحقق من وجود jQuery وتحميلها إذا لم تكن موجودة
    if (typeof jQuery === 'undefined') {
        document.write('<script src="https://code.jquery.com/jquery-3.6.0.min.js"><\/script>');
    }

    // استخدام window.onload بدلاً من $(document).ready للتأكد من تحميل jQuery
    window.onload = function() {
        // التحقق مرة أخرى من وجود jQuery
        if (typeof jQuery === 'undefined') {
            console.error('فشل تحميل jQuery. بعض الوظائف قد لا تعمل بشكل صحيح.');
            return;
        }

        // ضبط حالة "تحديد الكل" عند التحميل
        function updateSelectAllCheckbox() {
            const totalEmployees = $('.employee-checkbox').length;
            const selectedEmployees = $('.employee-checkbox:checked').length;

            if (totalEmployees > 0 && totalEmployees === selectedEmployees) {
                $('.select-all-employees').prop('checked', true);
            } else {
                $('.select-all-employees').prop('checked', false);
            }
        }

        updateSelectAllCheckbox();

        // تحديد جميع الموظفين / إلغاء تحديد الجميع
        $('.select-all-employees').on('change', function() {
            $('.employee-checkbox:visible').prop('checked', $(this).is(':checked'));
        });

        // تفعيل التصفية حسب الفرع
        $('#branch_id').on('change', function() {
            const branchId = $(this).val();

            // إذا لم يكن هناك تغيير في الفرع، قم بتصفية الموظفين فقط
            if (branchId !== '') {
                // التحقق من وجود موظفين للفرع المحدد
                const hasEmployeesForBranch = $('.employee-row[data-branch-id="' + branchId + '"]').length > 0;

                if (hasEmployeesForBranch) {
                    // تصفية الموظفين حسب الفرع
                    filterEmployeesByBranch();
                } else {
                    // إذا لم يكن هناك موظفين للفرع المحدد، استمر بالطريقة القديمة
                    // توجيه المستخدم لإعادة تحميل الصفحة مع معرف الفرع الجديد
                    const currentUrl = new URL(window.location.href);
                    currentUrl.searchParams.set('branch_id', branchId);

                    // حفظ التغييرات الحالية في بيانات الخدمة في الجلسة
                    const serviceData = {
                        name: $('#name').val(),
                        price: $('#price').val(),
                        duration: $('#duration').val(),
                        description: $('#description').val(),
                        category_id: $('#category_id').val(),
                        is_active: $('#is_active').val()
                    };

                    // يمكن استخدام sessionStorage لتخزين البيانات مؤقتًا
                    sessionStorage.setItem('editServiceData', JSON.stringify(serviceData));

                    // تحذير المستخدم قبل الانتقال
                    if (confirm('سيؤدي تغيير الفرع إلى تحديث قائمة الموظفين المتاحين. هل تريد المتابعة؟')) {
                        window.location.href = currentUrl.toString();
                    } else {
                        // إعادة تحديد الفرع إلى القيمة السابقة
                        $(this).val(<?php echo $service['branch_id'] ?: 'null'; ?>);
                    }
                }
            } else {
                // إذا تم اختيار "جميع الفروع"، أظهر جميع الموظفين
                filterEmployeesByBranch();
            }
        });

        // تنفيذ التصفية عند تحميل الصفحة
        filterEmployeesByBranch();

        // دالة لتصفية الموظفين حسب الفرع
        function filterEmployeesByBranch() {
            const selectedBranchId = $('#branch_id').val();

            if (selectedBranchId === '') {
                // إذا تم اختيار "جميع الفروع"، أظهر جميع الموظفين
                $('.employee-row').show();
            } else {
                // إذا تم اختيار فرع محدد، أظهر فقط موظفي هذا الفرع
                $('.employee-row').hide();
                $('.employee-row[data-branch-id="' + selectedBranchId + '"]').show();
            }

            // إعادة تعيين مربع "تحديد الكل"
            $('.select-all-employees').prop('checked', false);

            // إظهار رسالة إذا لم يكن هناك موظفين للفرع المحدد
            const visibleEmployees = $('.employee-row:visible').length;
            if (visibleEmployees === 0) {
                if ($('#no-employees-message').length === 0) {
                    $('#employees-table-body').after('<tr id="no-employees-message"><td colspan="' + (<?php echo $isAdmin ? '4' : '3'; ?>) + '" class="text-center p-3">لا يوجد موظفين متاحين لهذا الفرع</td></tr>');
                }
            } else {
                $('#no-employees-message').remove();
            }

            // تحديث حالة مربع "تحديد الكل"
            updateSelectAllCheckbox();
        }

        // تحديث حالة "تحديد الكل" عند تغيير أي من صناديق الاختيار الفردية
        $('.employee-checkbox').on('change', function() {
            updateSelectAllCheckbox();
        });

        // معالجة تقديم النموذج
        $('#edit-service-form').on('submit', function(e) {
            e.preventDefault();

            // جمع معرفات الموظفين المحددين
            const selectedEmployees = [];
            $('.employee-checkbox:checked').each(function() {
                selectedEmployees.push($(this).val());
            });

            // بناء بيانات الفورم
            const formData = {
                id: <?php echo $service['id']; ?>,
                name: $('#name').val(),
                description: $('#description').val(),
                price: $('#price').val(),
                duration: $('#duration').val(),
                category_id: $('#category_id').val(),
                branch_id: $('select[name="branch_id"]').val() || $('input[name="branch_id"]').val(),
                is_active: $('#is_active').val(),
                employees: selectedEmployees.join(',')
            };

            // إرسال البيانات باستخدام AJAX
            $.ajax({
                url: '<?php echo API_URL; ?>services.php?action=update',
                type: 'POST',
                data: formData,
                dataType: 'json',
                beforeSend: function() {
                    // إظهار مؤشر التحميل
                    showLoading();
                },
                success: function(response) {
                    hideLoading();

                    if (response.status === 'success') {
                        // عرض رسالة النجاح
                        showToast('نجاح', response.message, 'success');

                        // إعادة توجيه المستخدم إلى صفحة الخدمات بعد ثانيتين
                        setTimeout(function() {
                            window.location.href = '<?php echo BASE_URL; ?>pages/services/index.php';
                        }, 2000);
                    } else {
                        // عرض رسالة الخطأ
                        showToast('خطأ', response.message, 'error');
                    }
                },
                error: function(xhr, status, error) {
                    hideLoading();

                    let errorMessage = 'حدث خطأ أثناء تحديث الخدمة';
                    try {
                        const response = JSON.parse(xhr.responseText);
                        if (response.message) {
                            errorMessage = response.message;
                        }
                    } catch (e) {
                        console.error(e);
                    }

                    showToast('خطأ', errorMessage, 'error');
                }
            });
        });

        // إظهار مؤشر التحميل
        function showLoading() {
            $('button[type="submit"]')
                .prop('disabled', true)
                .html('<span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span> جاري الحفظ...');
        }

        // إخفاء مؤشر التحميل
        function hideLoading() {
            $('button[type="submit"]')
                .prop('disabled', false)
                .html('<i class="fas fa-save me-1"></i> حفظ التغييرات');
        }

        // عرض رسالة توست
        function showToast(title, message, type) {
            // التحقق من وجود مكتبة toastr
            if (typeof toastr !== 'undefined') {
                toastr.options = {
                    closeButton: true,
                    progressBar: true,
                    positionClass: 'toast-top-left',
                    timeOut: 3000
                };

                if (type === 'success') {
                    toastr.success(message, title);
                } else if (type === 'error') {
                    toastr.error(message, title);
                } else if (type === 'warning') {
                    toastr.warning(message, title);
                } else {
                    toastr.info(message, title);
                }
            } else {
                // استخدام تنبيه بديل إذا كانت مكتبة toastr غير متوفرة
                let alertClass = 'alert-info';
                if (type === 'success') alertClass = 'alert-success';
                if (type === 'error') alertClass = 'alert-danger';
                if (type === 'warning') alertClass = 'alert-warning';

                // إنشاء حاوية للتنبيهات إذا لم تكن موجودة
                if (!$('#alertContainer').length) {
                    $('<div id="alertContainer" class="alert-container position-fixed top-0 end-0 p-3" style="z-index: 1050;"></div>').appendTo('body');
                }

                // إنشاء عنصر التنبيه
                const alertId = 'alert-' + new Date().getTime();
                const alertHtml = `
                    <div id="${alertId}" class="alert ${alertClass} alert-dismissible fade show" role="alert">
                        <strong>${title}</strong> ${message}
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                `;

                // إضافة التنبيه إلى الحاوية
                $('#alertContainer').append(alertHtml);

                // إخفاء التنبيه تلقائيًا بعد 3 ثوانٍ
                setTimeout(function() {
                    $('#' + alertId).alert('close');
                }, 3000);
            }
        }
    };
</script>

<?php
// استدعاء قالب الذيل
include_once '../../includes/templates/footer.php';
?>
