<?php
/**
 * API لإنشاء إشعارات للمستخدمين عن المواعيد
 */

// تعريف ثابت لمنع الوصول المباشر للملفات المرتبطة
define('BASEPATH', true);

// استدعاء ملفات التهيئة
require_once '../config/init.php';

// التحقق من تسجيل الدخول للوصول إلى API
requireLogin();

// إنشاء اتصال بقاعدة البيانات
$db = new Database();

// إنشاء كائن الإشعارات
$notificationModel = new Notification($db);

// الاستجابة الافتراضية
$response = [
    'status' => 'error',
    'message' => 'لم يتم تنفيذ أي إجراء',
    'code' => 400
];

try {
    // الحصول على إعدادات الإشعارات
    $settingsModel = new Settings($db);
    $allSettings = $settingsModel->getAllSettings();

    // استخراج إعدادات الإشعارات
    $notificationSettings = [];
    foreach ($allSettings as $key => $value) {
        if (strpos($key, 'notification_') === 0) {
            $shortKey = substr($key, strlen('notification_'));
            $notificationSettings[$shortKey] = $value;
        }
    }

    // التحقق من تفعيل تذكير المواعيد
    $reminderEnabled = isset($notificationSettings['appointment_reminder']) && $notificationSettings['appointment_reminder'] == '1';
    if (!$reminderEnabled) {
        $response = [
            'status' => 'error',
            'message' => 'تذكير المواعيد غير مفعل في الإعدادات',
            'code' => 400
        ];
        header('Content-Type: application/json');
        echo json_encode($response);
        exit;
    }

    // الحصول على المواعيد لليوم الحالي
    $today = date('Y-m-d');
    $now = date('H:i:s');

    // التحقق من تفعيل تذكير الدقائق
    $minutesReminderEnabled = true; // تفعيل دائمًا
    $reminderMinutes = 15; // تذكير قبل الموعد بـ 15 دقيقة

    // حساب وقت البدء والنهاية للبحث عن المواعيد القادمة خلال 15 دقيقة
    $startTime = date('H:i:s', strtotime($now) + (15 * 60));
    $endTime = date('H:i:s', strtotime($now) + (15 * 60) + 300); // إضافة 5 دقائق للنافذة الزمنية

    // حساب وقت البدء والنهاية للبحث عن المواعيد الفائتة
    $pastStartTime = '00:00:00';
    $pastEndTime = date('H:i:s', strtotime($now) - (15 * 60)); // المواعيد التي فاتت منذ أكثر من 15 دقيقة

    $db->prepare("SELECT a.*,
                 c.name AS customer_name,
                 s.name AS service_name,
                 e.name AS employee_name,
                 e.user_id AS employee_user_id,
                 b.id AS branch_id,
                 b.name AS branch_name
          FROM appointments a
          LEFT JOIN customers c ON a.customer_id = c.id
          LEFT JOIN services s ON a.service_id = s.id
          LEFT JOIN employees e ON a.employee_id = e.id
          LEFT JOIN branches b ON a.branch_id = b.id
          WHERE a.date = :today
          AND a.status = 'booked'
          AND a.customer_id IS NOT NULL
          AND e.user_id IS NOT NULL");
    $db->bind(':today', $today);
    $appointments = $db->fetchAll();

    // الحصول على المواعيد القادمة خلال 15 دقيقة
    $db->prepare("SELECT a.*,
                 c.name AS customer_name,
                 s.name AS service_name,
                 e.name AS employee_name,
                 e.user_id AS employee_user_id,
                 b.id AS branch_id,
                 b.name AS branch_name
          FROM appointments a
          LEFT JOIN customers c ON a.customer_id = c.id
          LEFT JOIN services s ON a.service_id = s.id
          LEFT JOIN employees e ON a.employee_id = e.id
          LEFT JOIN branches b ON a.branch_id = b.id
          WHERE a.date = :today
          AND a.status = 'booked'
          AND a.customer_id IS NOT NULL
          AND e.user_id IS NOT NULL
          AND a.start_time BETWEEN :start_time AND :end_time");
    $db->bind(':today', $today);
    $db->bind(':start_time', $startTime);
    $db->bind(':end_time', $endTime);
    $upcomingAppointments = $db->fetchAll();

    // الحصول على المواعيد الفائتة التي لم يتم التعامل معها
    $db->prepare("SELECT a.*,
                 c.name AS customer_name,
                 s.name AS service_name,
                 e.name AS employee_name,
                 e.user_id AS employee_user_id,
                 b.id AS branch_id,
                 b.name AS branch_name
          FROM appointments a
          LEFT JOIN customers c ON a.customer_id = c.id
          LEFT JOIN services s ON a.service_id = s.id
          LEFT JOIN employees e ON a.employee_id = e.id
          LEFT JOIN branches b ON a.branch_id = b.id
          WHERE a.date = :today
          AND a.status = 'booked'
          AND a.customer_id IS NOT NULL
          AND e.user_id IS NOT NULL
          AND a.start_time BETWEEN :past_start_time AND :past_end_time");
    $db->bind(':today', $today);
    $db->bind(':past_start_time', $pastStartTime);
    $db->bind(':past_end_time', $pastEndTime);
    $missedAppointments = $db->fetchAll();

    $createdCount = 0;
    $failedCount = 0;
    $upcomingCount = 0;

    // إنشاء إشعارات للمواعيد اليومية
    foreach ($appointments as $appointment) {
        // التحقق من وجود إشعار سابق لهذا الموعد للمستخدم
        $db->prepare("SELECT COUNT(*) FROM notifications
                    WHERE related_id = :appointment_id
                    AND related_type = 'appointment'
                    AND type = 'appointment_reminder'
                    AND recipient_id = :user_id
                    AND recipient_type = 'user'
                    AND DATE(created_at) = CURRENT_DATE()
                    AND is_read = 0");
        $db->bind(':appointment_id', $appointment['id']);
        $db->bind(':user_id', $appointment['employee_user_id']);
        $existingNotifications = $db->fetchColumn();

        if ($existingNotifications > 0) {
            // تم إنشاء إشعار بالفعل لهذا الموعد للمستخدم اليوم
            continue;
        }

        // إنشاء إشعار للموظف
        $notificationData = [
            'type' => 'appointment_reminder',
            'recipient_id' => $appointment['employee_user_id'],
            'recipient_type' => 'user',
            'title' => 'تذكير بموعد اليوم',
            'message' => "لديك موعد اليوم مع العميل: {$appointment['customer_name']} للخدمة: {$appointment['service_name']} في {$appointment['branch_name']} الساعة " . date('h:i A', strtotime($appointment['start_time'])),
            'related_id' => $appointment['id'],
            'related_type' => 'appointment',
            'branch_id' => $appointment['branch_id'],
            'is_read' => 0,
            'is_sent' => 1, // إرسال فوري
            'send_email' => 0,
            'send_sms' => 0,
            'scheduled_at' => null
        ];

        try {
            $notificationId = $notificationModel->createNotification($notificationData);
            if ($notificationId) {
                $createdCount++;
            } else {
                $failedCount++;
            }
        } catch (Exception $e) {
            error_log('خطأ في إنشاء إشعار للمستخدم: ' . $e->getMessage());
            $failedCount++;
        }
    }

    // إنشاء إشعارات للمواعيد القريبة (قبل الموعد بدقائق)
    if (!empty($upcomingAppointments)) {
        foreach ($upcomingAppointments as $appointment) {
            // التحقق من وجود إشعار سابق لهذا الموعد للمستخدم من نوع تذكير بدقائق
            $db->prepare("SELECT COUNT(*) FROM notifications
                        WHERE related_id = :appointment_id
                        AND related_type = 'appointment'
                        AND type = 'appointment_minutes_reminder'
                        AND recipient_id = :user_id
                        AND recipient_type = 'user'
                        AND created_at > DATE_SUB(NOW(), INTERVAL 30 MINUTE)
                        AND is_read = 0");
            $db->bind(':appointment_id', $appointment['id']);
            $db->bind(':user_id', $appointment['employee_user_id']);
            $existingMinutesNotifications = $db->fetchColumn();

            if ($existingMinutesNotifications > 0) {
                // تم إنشاء إشعار بالفعل لهذا الموعد للمستخدم خلال الساعة الماضية
                continue;
            }

            // إنشاء إشعار للموظف قبل الموعد بدقائق
            $notificationData = [
                'type' => 'appointment_minutes_reminder',
                'recipient_id' => $appointment['employee_user_id'],
                'recipient_type' => 'user',
                'title' => "تنبيه: موعد بعد 15 دقيقة",
                'message' => "لديك موعد بعد 15 دقيقة مع العميل: {$appointment['customer_name']} للخدمة: {$appointment['service_name']} في {$appointment['branch_name']} الساعة " . date('h:i A', strtotime($appointment['start_time'])),
                'related_id' => $appointment['id'],
                'related_type' => 'appointment',
                'branch_id' => $appointment['branch_id'],
                'is_read' => 0,
                'is_sent' => 1, // إرسال فوري
                'send_email' => 0,
                'send_sms' => 0,
                'scheduled_at' => null
            ];

            try {
                $notificationId = $notificationModel->createNotification($notificationData);
                if ($notificationId) {
                    $upcomingCount++;
                    $createdCount++;
                } else {
                    $failedCount++;
                }
            } catch (Exception $e) {
                error_log('خطأ في إنشاء إشعار قبل الموعد بدقائق: ' . $e->getMessage());
                $failedCount++;
            }
        }
    }

    // إنشاء إشعارات للمواعيد الفائتة التي لم يتم التعامل معها
    $missedCount = 0;
    if (!empty($missedAppointments)) {
        foreach ($missedAppointments as $appointment) {
            // التحقق من وجود إشعار سابق لهذا الموعد للمستخدم من نوع موعد فائت
            $db->prepare("SELECT COUNT(*) FROM notifications
                        WHERE related_id = :appointment_id
                        AND related_type = 'appointment'
                        AND type = 'appointment_missed'
                        AND recipient_id = :user_id
                        AND recipient_type = 'user'
                        AND created_at > DATE_SUB(NOW(), INTERVAL 1 HOUR)
                        AND is_read = 0");
            $db->bind(':appointment_id', $appointment['id']);
            $db->bind(':user_id', $appointment['employee_user_id']);
            $existingMissedNotifications = $db->fetchColumn();

            if ($existingMissedNotifications > 0) {
                // تم إنشاء إشعار بالفعل لهذا الموعد الفائت للمستخدم خلال الساعة الماضية
                continue;
            }

            // إنشاء إشعار للموظف عن الموعد الفائت
            $notificationData = [
                'type' => 'appointment_missed',
                'recipient_id' => $appointment['employee_user_id'],
                'recipient_type' => 'user',
                'title' => "تنبيه: موعد فائت لم يتم التعامل معه",
                'message' => "لديك موعد فائت لم يتم التعامل معه مع العميل: {$appointment['customer_name']} للخدمة: {$appointment['service_name']} في {$appointment['branch_name']} الساعة " . date('h:i A', strtotime($appointment['start_time'])),
                'related_id' => $appointment['id'],
                'related_type' => 'appointment',
                'branch_id' => $appointment['branch_id'],
                'is_read' => 0,
                'is_sent' => 1, // إرسال فوري
                'send_email' => 0,
                'send_sms' => 0,
                'scheduled_at' => null
            ];

            try {
                $notificationId = $notificationModel->createNotification($notificationData);
                if ($notificationId) {
                    $missedCount++;
                    $createdCount++;
                } else {
                    $failedCount++;
                }
            } catch (Exception $e) {
                error_log('خطأ في إنشاء إشعار لموعد فائت: ' . $e->getMessage());
                $failedCount++;
            }
        }
    }

    // إنشاء إشعارات للمديرين والمشرفين
    $db->prepare("SELECT id FROM users WHERE role IN ('admin', 'manager')");
    $managers = $db->fetchAll();

    foreach ($managers as $manager) {
        if (count($appointments) > 0) {
            // إنشاء إشعار واحد للمدير عن جميع مواعيد اليوم
            $notificationData = [
                'type' => 'system',
                'recipient_id' => $manager['id'],
                'recipient_type' => 'user',
                'title' => 'مواعيد اليوم',
                'message' => "يوجد " . count($appointments) . " موعد لليوم " . date('Y-m-d') . ". يرجى مراجعة صفحة المواعيد.",
                'related_id' => null,
                'related_type' => null,
                'branch_id' => null,
                'is_read' => 0,
                'is_sent' => 1, // إرسال فوري
                'send_email' => 0,
                'send_sms' => 0,
                'scheduled_at' => null
            ];

            try {
                $notificationId = $notificationModel->createNotification($notificationData);
                if ($notificationId) {
                    $createdCount++;
                } else {
                    $failedCount++;
                }
            } catch (Exception $e) {
                error_log('خطأ في إنشاء إشعار للمدير: ' . $e->getMessage());
                $failedCount++;
            }
        }
    }

    $response = [
        'status' => 'success',
        'message' => 'تم إنشاء الإشعارات بنجاح',
        'code' => 200,
        'data' => [
            'total_appointments' => count($appointments),
            'upcoming_appointments' => count($upcomingAppointments),
            'missed_appointments' => count($missedAppointments),
            'created_notifications' => $createdCount,
            'upcoming_notifications' => $upcomingCount,
            'missed_notifications' => $missedCount,
            'failed_notifications' => $failedCount,
            'minutes_reminder_enabled' => $minutesReminderEnabled,
            'reminder_minutes' => 15
        ]
    ];
} catch (Exception $e) {
    error_log('خطأ في API إنشاء إشعارات المستخدمين: ' . $e->getMessage());

    $response = [
        'status' => 'error',
        'message' => 'حدث خطأ أثناء معالجة الطلب: ' . $e->getMessage(),
        'code' => 500
    ];
}

// إرسال الاستجابة
header('Content-Type: application/json');
echo json_encode($response);
exit;
