<?php
/**
 * صفحة عرض تفاصيل المصروف
 */

// تعريف ثابت لمنع الوصول المباشر للملفات المرتبطة
define('BASEPATH', true);

// استدعاء ملفات التهيئة
require_once '../../config/init.php';

// التحقق من تسجيل الدخول
redirectIfNotLoggedIn();

// التحقق من صلاحية عرض المصروفات
if (!hasPermission('expenses_view')) {
    setErrorMessage('ليس لديك صلاحية لعرض المصروفات');
    redirect('pages/dashboard.php');
}

// التحقق من وجود معرف المصروف
$expenseId = isset($_GET['id']) ? intval($_GET['id']) : 0;

if (!$expenseId) {
    setErrorMessage('لم يتم تحديد المصروف المطلوب');
    redirect('index.php');
}

// إنشاء كائنات النماذج
$db = new Database();
$expenseModel = new Expense($db);
$settingsModel = new Settings($db);

// استرجاع بيانات المصروف
$expense = $expenseModel->getExpenseById($expenseId);

// التحقق من وجود المصروف
if (!$expense) {
    setErrorMessage('المصروف غير موجود أو محذوف');
    redirect('pages/expenses/index.php');
}

// التحقق من صلاحية الوصول للمصروف (للفروع)
// السماح للمستخدم بالوصول إذا كان إداري أو يعمل في الفرع الخاص بالمصروف
if ($_SESSION['user_role'] !== ROLE_ADMIN && $_SESSION['user_role'] !== ROLE_MANAGER && $expense['branch_id'] != $_SESSION['user_branch_id']) {
    setErrorMessage('ليس لديك صلاحية للوصول لهذا المصروف');
    redirect('pages/expenses/index.php');
}

// الحصول على رمز العملة
$currencySymbol = $settingsModel->get('system_currency_symbol', 'ر.س');

// عنوان الصفحة
$pageTitle = 'تفاصيل المصروف #' . $expenseId;

// إضافة أنماط CSS مخصصة
$customStyles = <<<CSS
<style>
    .info-card {
        border-radius: 8px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        margin-bottom: 20px;
    }

    .info-card .card-header {
        background-color: #f8f9fa;
        font-weight: 600;
        padding: 15px 20px;
    }

    .info-card .card-body {
        padding: 20px;
    }

    .info-item {
        margin-bottom: 15px;
    }

    .info-item .label {
        color: #6c757d;
        font-size: 0.9rem;
        margin-bottom: 5px;
    }

    .info-item .value {
        font-weight: 500;
        font-size: 1.1rem;
    }

    .amount-box {
        background-color: #f8f9fa;
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 15px;
        text-align: center;
    }

    .amount-box .title {
        color: #6c757d;
        font-size: 1rem;
        margin-bottom: 5px;
    }

    .amount-box .amount {
        font-weight: 600;
        font-size: 1.5rem;
        color: #28a745;
    }

    @media print {
        .no-print {
            display: none !important;
        }

        body {
            padding: 0;
            margin: 0;
        }

        .container-fluid {
            width: 100%;
            padding: 0;
            margin: 0;
        }

        .card {
            border: none !important;
            box-shadow: none !important;
        }
    }
</style>
CSS;

// استدعاء رأس الصفحة
include '../../includes/templates/header.php';

// إضافة الأنماط المخصصة
echo $customStyles;
?>

<!-- المحتوى الرئيسي للصفحة -->
<div class="container-fluid">
    <div id="alerts-container"></div>

    <!-- عنوان الصفحة والأزرار -->
    <div class="d-flex justify-content-between align-items-center mb-4 no-print">
        <h2 class="h3 mb-0 text-gray-800">
            <i class="fas fa-money-bill-wave ml-2"></i> تفاصيل المصروف #<?php echo $expenseId; ?>
        </h2>

        <div>
            <a href="index.php" class="btn btn-secondary">
                <i class="fas fa-arrow-right ml-1"></i> العودة للقائمة
            </a>

            <?php if (hasPermission('expenses_edit') && (!$expense['end_day_id'] || empty($expense['end_day_closed_at'])) && ($_SESSION['user_role'] === ROLE_ADMIN || $_SESSION['user_role'] === ROLE_MANAGER || $expense['branch_id'] == $_SESSION['user_branch_id'])): ?>
            <button type="button" class="btn btn-primary" id="edit-expense-btn">
                <i class="fas fa-edit ml-1"></i> تعديل المصروف
            </button>
            <?php endif; ?>

            <button class="btn btn-info" onclick="window.print()">
                <i class="fas fa-print ml-1"></i> طباعة
            </button>
        </div>
    </div>

    <div class="row">
        <!-- بطاقة المعلومات الأساسية -->
        <div class="col-md-8">
            <div class="card info-card">
                <div class="card-header">
                    <i class="fas fa-info-circle ml-1"></i> معلومات المصروف
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="info-item">
                                <div class="label">رقم المصروف</div>
                                <div class="value"><?php echo $expense['id']; ?></div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="info-item">
                                <div class="label">تاريخ المصروف</div>
                                <div class="value"><?php echo date('Y-m-d', strtotime($expense['date'])); ?></div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="info-item">
                                <div class="label">فئة المصروف</div>
                                <div class="value"><?php echo $expense['category_name']; ?></div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="info-item">
                                <div class="label">طريقة الدفع</div>
                                <div class="value">
                                    <?php
                                    switch ($expense['payment_method']) {
                                        case 'cash':
                                            echo '<span class="badge bg-success">نقدي</span>';
                                            break;
                                        case 'card':
                                            echo '<span class="badge bg-info">بطاقة</span>';
                                            break;
                                        default:
                                            echo '<span class="badge bg-secondary">أخرى</span>';
                                    }
                                    ?>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="info-item">
                                <div class="label">الفرع</div>
                                <div class="value"><?php echo $expense['branch_name']; ?></div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="info-item">
                                <div class="label">تم الإنشاء بواسطة</div>
                                <div class="value"><?php echo $expense['user_name'] ?: 'غير محدد'; ?></div>
                            </div>
                        </div>
                        <?php if (!empty($expense['end_day_id'])): ?>
                        <div class="col-md-6">
                            <div class="info-item">
                                <div class="label">مرتبط بنهاية اليوم</div>
                                <div class="value">
                                    <a href="../endday/view.php?id=<?php echo $expense['end_day_id']; ?>" class="text-primary">
                                        #<?php echo $expense['end_day_id']; ?>
                                    </a>
                                </div>
                            </div>
                        </div>
                        <?php endif; ?>
                        <div class="col-md-12">
                            <div class="info-item">
                                <div class="label">وصف المصروف</div>
                                <div class="value">
                                    <?php echo !empty($expense['description']) ? nl2br(htmlspecialchars($expense['description'])) : 'لا يوجد وصف'; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- بطاقة المبلغ -->
        <div class="col-md-4">
            <div class="card info-card">
                <div class="card-header">
                    <i class="fas fa-money-bill-alt ml-1"></i> تفاصيل المبلغ
                </div>
                <div class="card-body">
                    <div class="amount-box">
                        <div class="title">مبلغ المصروف</div>
                        <div class="amount"><?php echo number_format($expense['amount'], 2) . ' ' . $currencySymbol; ?></div>
                    </div>

                    <div class="mt-4">
                        <div class="info-item">
                            <div class="label">تاريخ الإنشاء</div>
                            <div class="value"><?php echo date('Y-m-d h:i A', strtotime($expense['created_at'])); ?></div>
                        </div>

                        <?php if (!empty($expense['updated_at'])): ?>
                        <div class="info-item">
                            <div class="label">آخر تحديث</div>
                            <div class="value"><?php echo date('Y-m-d h:i A', strtotime($expense['updated_at'])); ?></div>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <?php if (hasPermission('expenses_delete') && (!$expense['end_day_id'] || empty($expense['end_day_closed_at'])) && ($_SESSION['user_role'] === ROLE_ADMIN || $_SESSION['user_role'] === ROLE_MANAGER || $expense['branch_id'] == $_SESSION['user_branch_id'])): ?>
            <div class="card info-card no-print">
                <div class="card-header bg-danger text-white">
                    <i class="fas fa-trash-alt ml-1"></i> حذف المصروف
                </div>
                <div class="card-body">
                    <p class="mb-3">هل أنت متأكد من رغبتك في حذف هذا المصروف؟</p>
                    <p class="text-danger mb-3">لا يمكن التراجع عن هذا الإجراء.</p>
                    <button type="button" class="btn btn-danger w-100" id="delete-expense-btn">
                        <i class="fas fa-trash-alt ml-1"></i> حذف المصروف
                    </button>
                </div>
            </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- نافذة تعديل المصروف -->
<?php if (hasPermission('expenses_edit') && ($_SESSION['user_role'] === ROLE_ADMIN || $_SESSION['user_role'] === ROLE_MANAGER || $expense['branch_id'] == $_SESSION['user_branch_id'])): ?>
<div class="modal fade" id="editExpenseModal" tabindex="-1" aria-labelledby="editExpenseModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editExpenseModalLabel">تعديل المصروف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="edit-expense-form">
                <div class="modal-body">
                    <input type="hidden" id="edit_expense_id" name="id" value="<?php echo $expenseId; ?>">

                    <div class="mb-3">
                        <label for="category_id_edit" class="form-label">فئة المصروف <span class="text-danger">*</span></label>
                        <select class="form-select" id="category_id_edit" name="category_id" required>
                            <option value="">اختر الفئة</option>
                            <!-- سيتم تعبئة الفئات عن طريق JavaScript -->
                        </select>
                    </div>

                    <div class="mb-3">
                        <label for="amount_edit" class="form-label">المبلغ <span class="text-danger">*</span></label>
                        <div class="input-group">
                            <input type="number" class="form-control" id="amount_edit" name="amount" step="0.01" min="0" required value="<?php echo $expense['amount']; ?>">
                            <span class="input-group-text"><?php echo $currencySymbol; ?></span>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="date_edit" class="form-label">التاريخ <span class="text-danger">*</span></label>
                        <input type="date" class="form-control" id="date_edit" name="date" required value="<?php echo date('Y-m-d', strtotime($expense['date'])); ?>">
                    </div>

                    <div class="mb-3">
                        <label for="payment_method_edit" class="form-label">طريقة الدفع <span class="text-danger">*</span></label>
                        <select class="form-select" id="payment_method_edit" name="payment_method" required>
                            <option value="cash" <?php echo $expense['payment_method'] == 'cash' ? 'selected' : ''; ?>>نقدي</option>
                            <option value="card" <?php echo $expense['payment_method'] == 'card' ? 'selected' : ''; ?>>بطاقة</option>
                            <option value="other" <?php echo $expense['payment_method'] == 'other' ? 'selected' : ''; ?>>أخرى</option>
                        </select>
                    </div>

                    <?php if ($_SESSION['user_role'] === ROLE_ADMIN): ?>
                    <div class="mb-3">
                        <label for="branch_id_edit" class="form-label">الفرع <span class="text-danger">*</span></label>
                        <select class="form-select" id="branch_id_edit" name="branch_id" required>
                            <!-- سيتم تعبئة الفروع عن طريق JavaScript -->
                        </select>
                    </div>
                    <?php endif; ?>

                    <div class="mb-3">
                        <label for="description_edit" class="form-label">الوصف</label>
                        <textarea class="form-control" id="description_edit" name="description" rows="3"><?php echo htmlspecialchars($expense['description']); ?></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">حفظ التغييرات</button>
                </div>
            </form>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- نافذة تأكيد الحذف -->
<?php if (hasPermission('expenses_delete') && ($_SESSION['user_role'] === ROLE_ADMIN || $_SESSION['user_role'] === ROLE_MANAGER || $expense['branch_id'] == $_SESSION['user_branch_id'])): ?>
<div class="modal fade" id="deleteExpenseModal" tabindex="-1" aria-labelledby="deleteExpenseModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteExpenseModalLabel">تأكيد حذف المصروف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من رغبتك في حذف هذا المصروف؟</p>
                <p class="text-danger">لا يمكن التراجع عن هذا الإجراء.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-danger" id="confirm-delete">تأكيد الحذف</button>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- استدعاء قالب الفوتر -->
<?php include '../../includes/templates/footer.php'; ?>

<script>
$(document).ready(function() {
    // متغيرات عامة
    var categories = [];
    var branches = [];

    // تحميل فئات المصروفات
    loadCategories();

    <?php if ($_SESSION['user_role'] === ROLE_ADMIN): ?>
    // تحميل الفروع
    loadBranches();
    <?php endif; ?>

    // إضافة حدث تعديل المصروف
    $('#edit-expense-btn').on('click', function() {
        $('#editExpenseModal').modal('show');
    });

    // إضافة حدث حذف المصروف
    $('#delete-expense-btn').on('click', function() {
        $('#deleteExpenseModal').modal('show');
    });

    // تعديل المصروف
    $('#edit-expense-form').on('submit', function(e) {
        e.preventDefault();

        var expenseId = $('#edit_expense_id').val();
        var formData = $(this).serialize();

        $.ajax({
            url: '../../api/expenses.php?action=update',
            type: 'PUT',
            data: formData,
            dataType: 'json',
            success: function(response) {
                if (response.status === 'success') {
                    showAlert('success', response.message);
                    $('#editExpenseModal').modal('hide');

                    // إعادة تحميل الصفحة بعد التعديل
                    setTimeout(function() {
                        location.reload();
                    }, 1500);
                } else {
                    showAlert('danger', response.message);
                }
            },
            error: function(xhr, status, error) {
                showAlert('danger', 'حدث خطأ أثناء تعديل المصروف. يرجى المحاولة مرة أخرى.');
                console.error(xhr.responseText);
            }
        });
    });

    // تأكيد حذف المصروف
    $('#confirm-delete').on('click', function() {
        var expenseId = <?php echo $expenseId; ?>;

        $.ajax({
            url: '../../api/expenses.php?action=delete',
            type: 'DELETE',
            data: {id: expenseId},
            dataType: 'json',
            success: function(response) {
                if (response.status === 'success') {
                    showAlert('success', response.message);
                    $('#deleteExpenseModal').modal('hide');

                    // العودة إلى صفحة المصروفات بعد الحذف
                    setTimeout(function() {
                        window.location.href = 'index.php';
                    }, 1500);
                } else {
                    showAlert('danger', response.message);
                }
            },
            error: function(xhr, status, error) {
                showAlert('danger', 'حدث خطأ أثناء حذف المصروف. يرجى المحاولة مرة أخرى.');
                console.error(xhr.responseText);
            }
        });
    });

    // دالة تحميل فئات المصروفات
    function loadCategories() {
        $.ajax({
            url: '../../api/expenses.php?action=list-categories',
            type: 'GET',
            dataType: 'json',
            success: function(response) {
                if (response.status === 'success') {
                    categories = response.categories;

                    // تعبئة قائمة الفئات في نموذج التعديل
                    var categoriesHtml = '<option value="">اختر الفئة</option>';
                    $.each(categories, function(index, category) {
                        var selected = (category.id == <?php echo $expense['category_id']; ?>) ? 'selected' : '';
                        categoriesHtml += '<option value="' + category.id + '" ' + selected + '>' + category.name + '</option>';
                    });

                    $('#category_id_edit').html(categoriesHtml);
                } else {
                    showAlert('danger', 'حدث خطأ أثناء تحميل فئات المصروفات');
                }
            },
            error: function(xhr, status, error) {
                showAlert('danger', 'حدث خطأ أثناء تحميل فئات المصروفات');
                console.error(xhr.responseText);
            }
        });
    }

    <?php if ($_SESSION['user_role'] === ROLE_ADMIN): ?>
    // دالة تحميل الفروع
    function loadBranches() {
        $.ajax({
            url: '../../api/branches.php?action=list',
            type: 'GET',
            dataType: 'json',
            success: function(response) {
                if (response.status === 'success') {
                    branches = response.branches;

                    // تعبئة قائمة الفروع في نموذج التعديل
                    var branchesHtml = '<option value="">اختر الفرع</option>';
                    $.each(branches, function(index, branch) {
                        var selected = (branch.id == <?php echo $expense['branch_id']; ?>) ? 'selected' : '';
                        branchesHtml += '<option value="' + branch.id + '" ' + selected + '>' + branch.name + '</option>';
                    });

                    $('#branch_id_edit').html(branchesHtml);
                } else {
                    showAlert('danger', 'حدث خطأ أثناء تحميل الفروع');
                }
            },
            error: function(xhr, status, error) {
                showAlert('danger', 'حدث خطأ أثناء تحميل الفروع');
                console.error(xhr.responseText);
            }
        });
    }
    <?php endif; ?>

    // دالة لعرض التنبيهات
    function showAlert(type, message) {
        const alertHtml = `
            <div class="alert alert-${type} alert-dismissible fade show" role="alert">
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        `;

        // إضافة التنبيه في أعلى الصفحة
        $('#alerts-container').html(alertHtml);

        // إخفاء التنبيه تلقائيًا بعد 5 ثوانٍ
        setTimeout(function() {
            $('.alert').alert('close');
        }, 5000);
    }
});
</script>
