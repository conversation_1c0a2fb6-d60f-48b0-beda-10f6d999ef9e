<?php
/**
 * صفحة إعدادات WhatsApp
 */

// تعريف ثابت لمنع الوصول المباشر للملفات المرتبطة
define('BASEPATH', true);

// استدعاء ملفات التهيئة
require_once '../../config/init.php';

// التحقق من تسجيل الدخول
requireLogin();

// التحقق من وجود صلاحية إدارة الإعدادات
requirePermission('settings_manage');

// عنوان الصفحة
$pageTitle = 'إعدادات WhatsApp';

// إنشاء كائن الإعدادات
$settingsModel = new Settings($db);

// الحصول على إعدادات WhatsApp
$allSettings = $settingsModel->getAllSettings();
$whatsappEnabled = isset($allSettings['whatsapp_enabled']) ? (bool)$allSettings['whatsapp_enabled'] : false;
$whatsappClientSide = true; // دائمًا نستخدم وضع جانب العميل

// إنشاء كائن WhatsApp
$whatsappService = new WhatsAppAutomation($db);

// تأكد من أن إعداد جانب العميل مفعل دائمًا
if (!isset($allSettings['whatsapp_client_side']) || !$allSettings['whatsapp_client_side']) {
    $settingsModel->updateSetting('whatsapp_client_side', 1);
}

// التحقق من حالة تسجيل الدخول إلى WhatsApp
// إضافة معالجة لتجنب تعليق الصفحة في حالة الفشل
$isLoggedIn = false;

try {
    // استخدام الدالة المعدلة للتحقق من حالة تسجيل الدخول
    // ستقوم الدالة بالتحقق من الحالة بناءً على وضع التشغيل (جانب العميل أو جانب الخادم)
    $isLoggedIn = $whatsappService->checkLoginStatus();
} catch (Exception $e) {
    error_log("خطأ في التحقق من حالة تسجيل الدخول إلى WhatsApp: " . $e->getMessage());
    $isLoggedIn = false;
}

// معالجة النموذج
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    try {
        // جمع البيانات من النموذج - فقط إعدادات جانب العميل
        $settings = [
            'whatsapp_enabled' => isset($_POST['whatsapp_enabled']) ? 1 : 0,
            'whatsapp_client_side' => 1, // دائمًا مفعل
            'whatsapp_use_client_side' => 1, // دائمًا مفعل
            'whatsapp_default_country_code' => sanitizeInput($_POST['whatsapp_default_country_code']),
            'notification_enable_whatsapp' => isset($_POST['notification_enable_whatsapp']) ? 1 : 0,
            'notification_reminder_hours' => intval($_POST['notification_reminder_hours'])
        ];

        // حفظ الإعدادات
        foreach ($settings as $key => $value) {
            $settingsModel->updateSetting($key, $value);
        }

        // رسالة نجاح
        $_SESSION['success_message'] = 'تم حفظ إعدادات WhatsApp بنجاح';
    } catch (Exception $e) {
        // رسالة خطأ
        $_SESSION['error_message'] = 'حدث خطأ أثناء حفظ الإعدادات: ' . $e->getMessage();
    }
}

// الحصول على الإعدادات الحالية
$currentSettings = $settingsModel->getAllSettings();

// استدعاء رأس الصفحة
include '../../includes/templates/header.php';
?>

<!-- المحتوى الرئيسي للصفحة -->
<div class="container-fluid">
    <!-- رأس الصفحة -->
    <div class="card border-0 shadow-sm mb-4">
        <div class="card-body d-flex justify-content-between align-items-center">
            <h5 class="mb-0">
                <i class="fab fa-whatsapp me-2 text-success"></i> إعدادات WhatsApp
                <span class="badge bg-warning text-dark ms-2">النسخة التجريبية</span>
            </h5>
            <a href="index.php" class="btn btn-secondary">
                <i class="fas fa-arrow-right me-1"></i> العودة للإعدادات
            </a>
        </div>
    </div>

    <!-- تحذير النسخة التجريبية -->
    <div class="alert alert-warning border-0 shadow-sm mb-4">
        <div class="d-flex align-items-center">
            <i class="fas fa-exclamation-triangle fa-2x text-warning me-3"></i>
            <div>
                <h5 class="alert-heading mb-2">ميزة غير متاحة في النسخة التجريبية</h5>
                <p class="mb-2">إعدادات وخدمات WhatsApp متوفرة فقط في النسخة المدفوعة من النظام.</p>
                <hr>
                <div class="row">
                    <div class="col-md-6">
                        <h6 class="mb-2">المميزات المتوفرة في النسخة المدفوعة:</h6>
                        <ul class="mb-0">
                            <li>إرسال رسائل WhatsApp تلقائية</li>
                            <li>تذكيرات المواعيد عبر WhatsApp</li>
                            <li>رسائل ترحيب للعملاء الجدد</li>
                            <li>إشعارات حالة الطلبات</li>
                            <li>تكامل مع WhatsApp Business API</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6 class="mb-2">للحصول على النسخة الكاملة:</h6>
                        <div class="d-grid gap-2">
                            <a href="https://wa.me/201556262660" target="_blank" class="btn btn-success">
                                <i class="fab fa-whatsapp me-2"></i>تواصل عبر WhatsApp
                            </a>
                            <a href="tel:+201556262660" class="btn btn-outline-primary">
                                <i class="fas fa-phone me-2"></i>اتصال مباشر
                            </a>
                            <a href="mailto:<EMAIL>" class="btn btn-outline-secondary">
                                <i class="fas fa-envelope me-2"></i>إرسال بريد إلكتروني
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- نموذج الإعدادات المعطل -->
    <div class="card border-0 shadow-sm">
        <div class="card-header bg-light">
            <h6 class="mb-0 text-muted">
                <i class="fas fa-lock me-2"></i>إعدادات WhatsApp (متوفرة في النسخة المدفوعة فقط)
            </h6>
        </div>
        <div class="card-body">
            <div class="alert alert-info">
                <i class="fas fa-info-circle me-2"></i>
                <strong>معاينة الإعدادات:</strong> هذه معاينة لإعدادات WhatsApp المتوفرة في النسخة المدفوعة. جميع الحقول معطلة في النسخة التجريبية.
            </div>

            <form method="post" action="" class="demo-disabled-form">
                <div class="alert alert-secondary">
                    <i class="fas fa-info-circle me-2"></i>
                    في النسخة المدفوعة: يمكن إرسال رسائل WhatsApp تلقائيًا باستخدام أتمتة المتصفح مع WhatsApp Web أو عبر WhatsApp Business API.
                </div>

                <h6 class="border-bottom pb-2 mb-3">إعدادات أتمتة WhatsApp</h6>

                <div class="form-check form-switch mb-3">
                    <input class="form-check-input" type="checkbox" id="whatsapp_enabled" name="whatsapp_enabled" disabled>
                    <label class="form-check-label text-muted" for="whatsapp_enabled">تفعيل خدمة WhatsApp</label>
                </div>

                <div class="mb-3">
                    <label for="whatsapp_default_country_code" class="form-label text-muted">رمز الدولة الافتراضي</label>
                    <input type="text" id="whatsapp_default_country_code" name="whatsapp_default_country_code" class="form-control" value="+20" placeholder="مثال: +20" disabled>
                    <div class="form-text">رمز الدولة الافتراضي لأرقام الهواتف (مثل +20 لمصر)</div>
                </div>

                <div class="mb-3">
                    <label for="whatsapp_session_timeout" class="form-label text-muted">مدة انتهاء الجلسة (بالدقائق)</label>
                    <input type="number" id="whatsapp_session_timeout" name="whatsapp_session_timeout" class="form-control" value="60" min="10" max="1440" disabled>
                    <div class="form-text">المدة التي يظل فيها المتصفح مفتوحًا قبل إغلاقه تلقائيًا</div>
                </div>

                <div class="mb-3">
                    <label for="whatsapp_browser_path" class="form-label text-muted">مسار المتصفح (اختياري)</label>
                    <input type="text" id="whatsapp_browser_path" name="whatsapp_browser_path" class="form-control" placeholder="مثال: C:\Program Files\Google\Chrome\Application\chrome.exe" disabled>
                    <div class="form-text">مسار متصفح Chrome أو Chromium على الخادم (اتركه فارغًا لاستخدام المتصفح الافتراضي)</div>
                </div>

                <h6 class="border-bottom pb-2 mb-3 mt-4">إعدادات تذكيرات المواعيد</h6>

                <div class="form-check form-switch mb-3">
                    <input class="form-check-input" type="checkbox" id="notification_enable_whatsapp" name="notification_enable_whatsapp" disabled>
                    <label class="form-check-label text-muted" for="notification_enable_whatsapp">إرسال تذكيرات المواعيد عبر WhatsApp</label>
                </div>

                <div class="mb-3">
                    <label for="notification_reminder_hours" class="form-label text-muted">إرسال التذكير قبل الموعد بـ (ساعات)</label>
                    <input type="number" id="notification_reminder_hours" name="notification_reminder_hours" class="form-control" value="24" min="1" max="72" disabled>
                    <div class="form-text">عدد الساعات قبل الموعد لإرسال التذكير</div>
                </div>

                <div class="text-end">
                    <button type="button" class="btn btn-secondary" disabled>
                        <i class="fas fa-lock me-1"></i> متوفر في النسخة المدفوعة فقط
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- قسم تسجيل الدخول إلى WhatsApp Web -->
    <div class="card border-0 shadow-sm mt-4">
        <div class="card-header bg-light">
            <h6 class="mb-0 text-muted">
                <i class="fas fa-lock me-2"></i>تسجيل الدخول إلى WhatsApp Web (متوفر في النسخة المدفوعة فقط)
            </h6>
        </div>
        <div class="card-body">
            <div class="alert alert-info">
                <i class="fas fa-info-circle me-2"></i>
                <strong>في النسخة المدفوعة:</strong> يمكن تسجيل الدخول إلى WhatsApp Web تلقائياً وإرسال الرسائل بشكل آلي.
            </div>

            <div class="alert alert-secondary">
                <i class="fas fa-mobile-alt me-2"></i>
                <strong>معاينة الوظائف:</strong> في النسخة المدفوعة سيتم فتح نافذة متصفح جديدة لمسح رمز QR باستخدام تطبيق WhatsApp على هاتفك.
            </div>

            <div class="text-center">
                <button type="button" class="btn btn-secondary me-2" disabled>
                    <i class="fab fa-whatsapp me-1"></i> تسجيل الدخول إلى WhatsApp Web
                </button>
                <button type="button" class="btn btn-outline-secondary" disabled>
                    <i class="fas fa-sync-alt me-1"></i> إعادة تعيين الجلسة
                </button>
            </div>

            <div class="mt-3">
                <div class="alert alert-warning">
                    <i class="fas fa-crown me-2"></i>
                    <strong>للحصول على هذه الميزة:</strong> تواصل معنا للحصول على النسخة المدفوعة مع جميع مميزات WhatsApp.
                </div>
            </div>
        </div>
    </div>

    <!-- قسم تعليمات تشغيل WhatsApp من جانب العميل -->
    <div class="card border-0 shadow-sm mt-4">
        <div class="card-header bg-light">
            <h6 class="mb-0 text-muted">
                <i class="fas fa-lock me-2"></i>تعليمات تشغيل WhatsApp (متوفرة في النسخة المدفوعة فقط)
            </h6>
        </div>
        <div class="card-body">
            <div class="alert alert-info">
                <i class="fas fa-info-circle me-2"></i>
                <strong>في النسخة المدفوعة:</strong> يتم توفير خادم WhatsApp المحلي مع تعليمات التثبيت والتشغيل الكاملة.
            </div>

            <h6 class="mt-4 mb-3">متطلبات النظام</h6>
            <ul>
                <li>تثبيت <a href="https://nodejs.org/" target="_blank">Node.js</a> (الإصدار 14 أو أحدث)</li>
                <li>متصفح حديث (Chrome أو Firefox)</li>
                <li>اتصال إنترنت مستقر</li>
                <li>حساب WhatsApp نشط على هاتفك</li>
            </ul>

            <h6 class="mt-4 mb-3">خطوات التثبيت</h6>
            <div class="alert alert-warning mb-3">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <strong>تحديث هام:</strong> تم تحديث خادم WhatsApp المحلي لحل مشكلة عدم تحميل CSS وJavaScript. إذا كنت تواجه مشاكل في إرسال الرسائل، يرجى تنزيل وتثبيت الإصدار الجديد.
            </div>
            <div class="d-flex mb-4">
                <a href="<?php echo BASE_URL; ?>downloads/whatsapp-local-server.zip" download class="btn btn-primary me-2">
                    <i class="fas fa-download me-1"></i> تنزيل الخادم المحلي
                </a>
                <button type="button" class="btn btn-outline-info" data-bs-toggle="modal" data-bs-target="#updateInstructionsModal">
                    <i class="fas fa-info-circle me-1"></i> تعليمات التحديث
                </button>
            </div>
            <ol>
                <li>قم بتنزيل ملف خادم WhatsApp المحلي من الزر أعلاه</li>
                <li>قم بفك ضغط الملف في مجلد على جهاز الكمبيوتر الخاص بك</li>
                <li>افتح موجه الأوامر (Command Prompt) أو Terminal</li>
                <li>انتقل إلى المجلد الذي قمت بفك ضغط الملفات فيه باستخدام الأمر:
                    <pre><code>cd path/to/whatsapp-local-server</code></pre>
                </li>
                <li>قم بتثبيت المكتبات المطلوبة باستخدام الأمر:
                    <pre><code>npm install</code></pre>
                </li>
                <li>قم بتشغيل الخادم المحلي باستخدام أحد الملفات التالية:
                    <ul>
                        <li><code>start-server.bat</code> - لتشغيل الخادم مع تفعيل جامع القمامة</li>
                        <li><code>start-server-cmd.bat</code> - لتشغيل الخادم في نافذة موجه الأوامر (CMD)</li>
                        <li><code>install-and-start.bat</code> - لتثبيت الحزم وتشغيل الخادم في خطوة واحدة</li>
                    </ul>
                </li>
                <li>سيتم فتح نافذة متصفح تلقائيًا. قم بمسح رمز QR باستخدام تطبيق WhatsApp على هاتفك لتسجيل الدخول</li>
                <li>بمجرد تسجيل الدخول، يمكنك استخدام ميزة إرسال رسائل WhatsApp من خلال النظام</li>
            </ol>

            <div class="alert alert-warning mt-4">
                <i class="fas fa-crown me-2"></i>
                <strong>للحصول على هذه الميزة:</strong> تواصل معنا للحصول على النسخة المدفوعة مع خادم WhatsApp المحلي وجميع التعليمات.
            </div>
        </div>
    </div>

    <!-- قسم الاختبار -->
    <div class="card border-0 shadow-sm mt-4">
        <div class="card-header bg-light">
            <h6 class="mb-0 text-muted">
                <i class="fas fa-lock me-2"></i>اختبار إرسال رسالة WhatsApp (متوفر في النسخة المدفوعة فقط)
            </h6>
        </div>
        <div class="card-body">
            <div class="alert alert-info">
                <i class="fas fa-info-circle me-2"></i>
                <strong>في النسخة المدفوعة:</strong> يمكن اختبار إرسال رسائل WhatsApp مباشرة من لوحة التحكم.
            </div>

            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="test_phone" class="form-label text-muted">رقم الهاتف</label>
                        <input type="text" id="test_phone" class="form-control" placeholder="أدخل رقم الهاتف" disabled>
                    </div>
                    <div class="mb-3">
                        <label for="test_message" class="form-label text-muted">الرسالة</label>
                        <textarea id="test_message" class="form-control" rows="3" placeholder="أدخل نص الرسالة" disabled>مرحبا، هذه رسالة اختبار من نظام إدارة الصالون.</textarea>
                    </div>
                    <button type="button" class="btn btn-secondary" disabled>
                        <i class="fab fa-whatsapp me-1"></i> إرسال رسالة اختبار
                    </button>
                </div>
                <div class="col-md-6">
                    <div class="alert alert-secondary">
                        <h6>مميزات الاختبار في النسخة المدفوعة:</h6>
                        <ul>
                            <li>اختبار الاتصال بخادم WhatsApp</li>
                            <li>إرسال رسائل اختبار فورية</li>
                            <li>تتبع حالة التسليم</li>
                            <li>سجل مفصل للرسائل المرسلة</li>
                            <li>إعدادات متقدمة للرسائل</li>
                        </ul>
                    </div>

                    <div class="alert alert-warning">
                        <i class="fas fa-crown me-2"></i>
                        <strong>احصل على النسخة الكاملة:</strong>
                        <div class="mt-2">
                            <a href="https://wa.me/201556262660" target="_blank" class="btn btn-success btn-sm">
                                <i class="fab fa-whatsapp me-1"></i>واتساب
                            </a>
                            <a href="tel:+201556262660" class="btn btn-primary btn-sm">
                                <i class="fas fa-phone me-1"></i>اتصال
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- استدعاء قالب الفوتر -->
<?php include '../../includes/templates/footer.php'; ?>

<!-- إضافة CSS للنموذج المعطل -->
<style>
.demo-disabled-form input:disabled,
.demo-disabled-form textarea:disabled,
.demo-disabled-form select:disabled {
    background-color: #f8f9fa;
    opacity: 0.6;
}

.demo-disabled-form .form-check-input:disabled {
    opacity: 0.5;
}

.text-muted {
    color: #6c757d !important;
}
</style>

<!-- رسالة للمستخدمين الذين يحاولون استخدام الميزات المعطلة -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // إضافة رسالة عند النقر على العناصر المعطلة
    const disabledElements = document.querySelectorAll('input:disabled, textarea:disabled, button:disabled');

    disabledElements.forEach(element => {
        element.addEventListener('click', function(e) {
            e.preventDefault();

            // إنشاء نافذة تنبيه
            const modal = document.createElement('div');
            modal.className = 'modal fade';
            modal.innerHTML = `
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header bg-warning text-dark">
                            <h5 class="modal-title">
                                <i class="fas fa-crown me-2"></i>ميزة متوفرة في النسخة المدفوعة فقط
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <p>هذه الميزة متوفرة فقط في النسخة المدفوعة من النظام.</p>
                            <p><strong>للحصول على النسخة الكاملة مع جميع مميزات WhatsApp:</strong></p>
                            <div class="d-grid gap-2">
                                <a href="https://wa.me/201556262660" target="_blank" class="btn btn-success">
                                    <i class="fab fa-whatsapp me-2"></i>تواصل عبر WhatsApp
                                </a>
                                <a href="tel:+201556262660" class="btn btn-primary">
                                    <i class="fas fa-phone me-2"></i>اتصال مباشر: +201556262660
                                </a>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                        </div>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);
            const bsModal = new bootstrap.Modal(modal);
            bsModal.show();

            modal.addEventListener('hidden.bs.modal', () => {
                modal.remove();
            });
        });
    });
});
</script>
