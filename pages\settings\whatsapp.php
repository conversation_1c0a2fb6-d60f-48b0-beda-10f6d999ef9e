<?php
/**
 * صفحة إعدادات WhatsApp
 */

// تعريف ثابت لمنع الوصول المباشر للملفات المرتبطة
define('BASEPATH', true);

// استدعاء ملفات التهيئة
require_once '../../config/init.php';

// التحقق من تسجيل الدخول
requireLogin();

// التحقق من وجود صلاحية إدارة الإعدادات
requirePermission('settings_manage');

// عنوان الصفحة
$pageTitle = 'إعدادات WhatsApp';

// إنشاء كائن الإعدادات
$settingsModel = new Settings($db);

// الحصول على إعدادات WhatsApp
$allSettings = $settingsModel->getAllSettings();
$whatsappEnabled = isset($allSettings['whatsapp_enabled']) ? (bool)$allSettings['whatsapp_enabled'] : false;
$whatsappClientSide = true; // دائمًا نستخدم وضع جانب العميل

// إنشاء كائن WhatsApp
$whatsappService = new WhatsAppAutomation($db);

// تأكد من أن إعداد جانب العميل مفعل دائمًا
if (!isset($allSettings['whatsapp_client_side']) || !$allSettings['whatsapp_client_side']) {
    $settingsModel->updateSetting('whatsapp_client_side', 1);
}

// التحقق من حالة تسجيل الدخول إلى WhatsApp
// إضافة معالجة لتجنب تعليق الصفحة في حالة الفشل
$isLoggedIn = false;

try {
    // استخدام الدالة المعدلة للتحقق من حالة تسجيل الدخول
    // ستقوم الدالة بالتحقق من الحالة بناءً على وضع التشغيل (جانب العميل أو جانب الخادم)
    $isLoggedIn = $whatsappService->checkLoginStatus();
} catch (Exception $e) {
    error_log("خطأ في التحقق من حالة تسجيل الدخول إلى WhatsApp: " . $e->getMessage());
    $isLoggedIn = false;
}

// معالجة النموذج
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    try {
        // جمع البيانات من النموذج - فقط إعدادات جانب العميل
        $settings = [
            'whatsapp_enabled' => isset($_POST['whatsapp_enabled']) ? 1 : 0,
            'whatsapp_client_side' => 1, // دائمًا مفعل
            'whatsapp_use_client_side' => 1, // دائمًا مفعل
            'whatsapp_default_country_code' => sanitizeInput($_POST['whatsapp_default_country_code']),
            'notification_enable_whatsapp' => isset($_POST['notification_enable_whatsapp']) ? 1 : 0,
            'notification_reminder_hours' => intval($_POST['notification_reminder_hours'])
        ];

        // حفظ الإعدادات
        foreach ($settings as $key => $value) {
            $settingsModel->updateSetting($key, $value);
        }

        // رسالة نجاح
        $_SESSION['success_message'] = 'تم حفظ إعدادات WhatsApp بنجاح';
    } catch (Exception $e) {
        // رسالة خطأ
        $_SESSION['error_message'] = 'حدث خطأ أثناء حفظ الإعدادات: ' . $e->getMessage();
    }
}

// الحصول على الإعدادات الحالية
$currentSettings = $settingsModel->getAllSettings();

// استدعاء رأس الصفحة
include '../../includes/templates/header.php';
?>

<!-- المحتوى الرئيسي للصفحة -->
<div class="container-fluid">
    <!-- رأس الصفحة -->
    <div class="card border-0 shadow-sm mb-4">
        <div class="card-body d-flex justify-content-between align-items-center">
            <h5 class="mb-0">
                <i class="fab fa-whatsapp me-2 text-success"></i> إعدادات WhatsApp
            </h5>
            <a href="index.php" class="btn btn-secondary">
                <i class="fas fa-arrow-right me-1"></i> العودة للإعدادات
            </a>
        </div>
    </div>

    <!-- نموذج الإعدادات -->
    <div class="card border-0 shadow-sm">
        <div class="card-body">
            <form method="post" action="">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    لإرسال رسائل WhatsApp تلقائيًا، يتم استخدام أتمتة المتصفح مع WhatsApp Web من جانب العميل فقط. يجب تشغيل الخادم المحلي على جهازك وتسجيل الدخول إلى WhatsApp Web مرة واحدة على الأقل لبدء استخدام النظام.
                </div>

                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>ملاحظة هامة:</strong> تم تحديث النظام لاستخدام WhatsApp من جانب العميل فقط. لم يعد هناك خيار لإرسال رسائل WhatsApp من جانب الخادم. يجب تشغيل الخادم المحلي على جهازك لاستخدام WhatsApp.
                </div>

                <h6 class="border-bottom pb-2 mb-3">إعدادات أتمتة WhatsApp</h6>

                <div class="form-check form-switch mb-3">
                    <input class="form-check-input" type="checkbox" id="whatsapp_enabled" name="whatsapp_enabled" <?php echo isset($currentSettings['whatsapp_enabled']) && $currentSettings['whatsapp_enabled'] ? 'checked' : ''; ?>>
                    <label class="form-check-label" for="whatsapp_enabled">تفعيل خدمة WhatsApp</label>
                </div>

                <div class="mb-3">
                    <label for="whatsapp_default_country_code" class="form-label">رمز الدولة الافتراضي</label>
                    <input type="text" id="whatsapp_default_country_code" name="whatsapp_default_country_code" class="form-control" value="<?php echo htmlspecialchars($currentSettings['whatsapp_default_country_code'] ?? '+20'); ?>" placeholder="مثال: +20">
                    <div class="form-text">رمز الدولة الافتراضي لأرقام الهواتف (مثل +20 لمصر)</div>
                </div>

                <div class="mb-3">
                    <label for="whatsapp_session_timeout" class="form-label">مدة انتهاء الجلسة (بالدقائق)</label>
                    <input type="number" id="whatsapp_session_timeout" name="whatsapp_session_timeout" class="form-control" value="<?php echo htmlspecialchars($currentSettings['whatsapp_session_timeout'] ?? '60'); ?>" min="10" max="1440">
                    <div class="form-text">المدة التي يظل فيها المتصفح مفتوحًا قبل إغلاقه تلقائيًا</div>
                </div>

                <div class="mb-3">
                    <label for="whatsapp_browser_path" class="form-label">مسار المتصفح (اختياري)</label>
                    <input type="text" id="whatsapp_browser_path" name="whatsapp_browser_path" class="form-control" value="<?php echo htmlspecialchars($currentSettings['whatsapp_browser_path'] ?? ''); ?>" placeholder="مثال: C:\Program Files\Google\Chrome\Application\chrome.exe">
                    <div class="form-text">مسار متصفح Chrome أو Chromium على الخادم (اتركه فارغًا لاستخدام المتصفح الافتراضي)</div>
                </div>

                <h6 class="border-bottom pb-2 mb-3 mt-4">إعدادات تذكيرات المواعيد</h6>

                <div class="form-check form-switch mb-3">
                    <input class="form-check-input" type="checkbox" id="notification_enable_whatsapp" name="notification_enable_whatsapp" <?php echo isset($currentSettings['notification_enable_whatsapp']) && $currentSettings['notification_enable_whatsapp'] ? 'checked' : ''; ?>>
                    <label class="form-check-label" for="notification_enable_whatsapp">إرسال تذكيرات المواعيد عبر WhatsApp</label>
                </div>

                <div class="mb-3">
                    <label for="notification_reminder_hours" class="form-label">إرسال التذكير قبل الموعد بـ (ساعات)</label>
                    <input type="number" id="notification_reminder_hours" name="notification_reminder_hours" class="form-control" value="<?php echo htmlspecialchars($currentSettings['notification_reminder_hours'] ?? '24'); ?>" min="1" max="72">
                    <div class="form-text">عدد الساعات قبل الموعد لإرسال التذكير</div>
                </div>

                <div class="text-end">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-1"></i> حفظ الإعدادات
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- قسم تسجيل الدخول إلى WhatsApp Web -->
    <div class="card border-0 shadow-sm mt-4">
        <div class="card-body">
            <h6 class="border-bottom pb-2 mb-3">تسجيل الدخول إلى WhatsApp Web</h6>

            <?php if ($isLoggedIn): ?>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle me-2"></i>
                    تم تسجيل الدخول إلى WhatsApp Web. يمكنك إرسال الرسائل تلقائيًا.
                </div>

                <div class="text-center">
                    <button type="button" id="login_whatsapp" class="btn btn-outline-primary me-2">
                        <i class="fab fa-whatsapp me-1"></i> إعادة تسجيل الدخول
                    </button>
                    <button type="button" id="reset_whatsapp" class="btn btn-outline-danger">
                        <i class="fas fa-sync-alt me-1"></i> إعادة تعيين الجلسة
                    </button>
                </div>
            <?php else: ?>
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    يجب تسجيل الدخول إلى WhatsApp Web مرة واحدة على الأقل لبدء استخدام النظام. سيفتح النظام نافذة متصفح جديدة لمسح رمز QR باستخدام تطبيق WhatsApp على هاتفك.
                </div>

                <div class="text-center">
                    <button type="button" id="login_whatsapp" class="btn btn-primary me-2">
                        <i class="fab fa-whatsapp me-1"></i> تسجيل الدخول إلى WhatsApp Web
                    </button>
                    <button type="button" id="reset_whatsapp" class="btn btn-outline-danger">
                        <i class="fas fa-sync-alt me-1"></i> إعادة تعيين الجلسة
                    </button>
                </div>
            <?php endif; ?>

            <div id="login_result" class="mt-3"></div>
        </div>
    </div>

    <!-- قسم تعليمات تشغيل WhatsApp من جانب العميل -->
    <?php if ($whatsappClientSide): ?>
    <div class="card border-0 shadow-sm mt-4">
        <div class="card-body">
            <h6 class="border-bottom pb-2 mb-3">تعليمات تشغيل WhatsApp من جانب العميل</h6>

            <div class="alert alert-info">
                <i class="fas fa-info-circle me-2"></i>
                لقد قمت بتفعيل وضع تشغيل WhatsApp من جانب العميل. لاستخدام هذه الميزة، يجب تثبيت وتشغيل خادم WhatsApp المحلي على جهازك.
            </div>

            <h6 class="mt-4 mb-3">متطلبات النظام</h6>
            <ul>
                <li>تثبيت <a href="https://nodejs.org/" target="_blank">Node.js</a> (الإصدار 14 أو أحدث)</li>
                <li>متصفح حديث (Chrome أو Firefox)</li>
                <li>اتصال إنترنت مستقر</li>
                <li>حساب WhatsApp نشط على هاتفك</li>
            </ul>

            <h6 class="mt-4 mb-3">خطوات التثبيت</h6>
            <div class="alert alert-warning mb-3">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <strong>تحديث هام:</strong> تم تحديث خادم WhatsApp المحلي لحل مشكلة عدم تحميل CSS وJavaScript. إذا كنت تواجه مشاكل في إرسال الرسائل، يرجى تنزيل وتثبيت الإصدار الجديد.
            </div>
            <div class="d-flex mb-4">
                <a href="<?php echo BASE_URL; ?>downloads/whatsapp-local-server.zip" download class="btn btn-primary me-2">
                    <i class="fas fa-download me-1"></i> تنزيل الخادم المحلي
                </a>
                <button type="button" class="btn btn-outline-info" data-bs-toggle="modal" data-bs-target="#updateInstructionsModal">
                    <i class="fas fa-info-circle me-1"></i> تعليمات التحديث
                </button>
            </div>
            <ol>
                <li>قم بتنزيل ملف خادم WhatsApp المحلي من الزر أعلاه</li>
                <li>قم بفك ضغط الملف في مجلد على جهاز الكمبيوتر الخاص بك</li>
                <li>افتح موجه الأوامر (Command Prompt) أو Terminal</li>
                <li>انتقل إلى المجلد الذي قمت بفك ضغط الملفات فيه باستخدام الأمر:
                    <pre><code>cd path/to/whatsapp-local-server</code></pre>
                </li>
                <li>قم بتثبيت المكتبات المطلوبة باستخدام الأمر:
                    <pre><code>npm install</code></pre>
                </li>
                <li>قم بتشغيل الخادم المحلي باستخدام أحد الملفات التالية:
                    <ul>
                        <li><code>start-server.bat</code> - لتشغيل الخادم مع تفعيل جامع القمامة</li>
                        <li><code>start-server-cmd.bat</code> - لتشغيل الخادم في نافذة موجه الأوامر (CMD)</li>
                        <li><code>install-and-start.bat</code> - لتثبيت الحزم وتشغيل الخادم في خطوة واحدة</li>
                    </ul>
                </li>
                <li>سيتم فتح نافذة متصفح تلقائيًا. قم بمسح رمز QR باستخدام تطبيق WhatsApp على هاتفك لتسجيل الدخول</li>
                <li>بمجرد تسجيل الدخول، يمكنك استخدام ميزة إرسال رسائل WhatsApp من خلال النظام</li>
            </ol>

            <div class="alert alert-warning mt-4">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <strong>ملاحظة هامة:</strong> يجب أن يظل الخادم المحلي قيد التشغيل طالما تريد استخدام ميزة إرسال رسائل WhatsApp.
            </div>
        </div>
    </div>
    <?php endif; ?>

    <!-- قسم الاختبار -->
    <div class="card border-0 shadow-sm mt-4">
        <div class="card-body">
            <h6 class="border-bottom pb-2 mb-3">اختبار إرسال رسالة WhatsApp</h6>

            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="test_phone" class="form-label">رقم الهاتف</label>
                        <input type="text" id="test_phone" class="form-control" placeholder="أدخل رقم الهاتف">
                    </div>
                    <div class="mb-3">
                        <label for="test_message" class="form-label">الرسالة</label>
                        <textarea id="test_message" class="form-control" rows="3" placeholder="أدخل نص الرسالة">مرحبا، هذه رسالة اختبار من نظام إدارة صالون البدرواي.</textarea>
                    </div>
                    <button type="button" id="send_test_message" class="btn btn-success">
                        <i class="fab fa-whatsapp me-1"></i> إرسال رسالة اختبار
                    </button>
                    <div id="test_result" class="mt-3"></div>
                </div>
                <div class="col-md-6">
                    <div class="alert alert-secondary">
                        <h6>تعليمات الاختبار:</h6>
                        <ol>
                            <li>تأكد من تسجيل الدخول إلى WhatsApp Web أولاً.</li>
                            <li>أدخل رقم هاتف صحيح بتنسيق دولي (مثال: 201234567890).</li>
                            <li>اكتب رسالة اختبار قصيرة.</li>
                            <li>انقر على زر "إرسال رسالة اختبار".</li>
                            <li>سيفتح النظام نافذة WhatsApp Web ويقوم بإرسال الرسالة تلقائيًا.</li>
                        </ol>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- نافذة منبثقة لتعليمات التحديث -->
<div class="modal fade" id="updateInstructionsModal" tabindex="-1" aria-labelledby="updateInstructionsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="updateInstructionsModalLabel">تعليمات تحديث خادم WhatsApp المحلي</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="إغلاق"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    تم تحديث خادم WhatsApp المحلي لحل مشكلة عدم تحميل ملفات CSS وJavaScript الخاصة بـ WhatsApp Web. هذا التحديث يحل مشكلة ظهور رسالة خطأ "mainPage.waitForTimeout is not a function".
                </div>

                <h6 class="mt-4 mb-3">خطوات التحديث:</h6>
                <ol>
                    <li>قم بإيقاف تشغيل الخادم المحلي الحالي إذا كان قيد التشغيل</li>
                    <li>قم بتنزيل الإصدار الجديد من الخادم المحلي</li>
                    <li>قم بفك ضغط الملف في مجلد جديد أو استبدل الملفات القديمة</li>
                    <li>قم بتشغيل الخادم المحلي باستخدام أحد ملفات التشغيل الجديدة:</li>
                    <ul>
                        <li><code>start-server.bat</code> - لتشغيل الخادم مع تفعيل جامع القمامة</li>
                        <li><code>start-server-cmd.bat</code> - لتشغيل الخادم في نافذة موجه الأوامر (CMD)</li>
                        <li><code>install-and-start.bat</code> - لتثبيت الحزم وتشغيل الخادم في خطوة واحدة</li>
                    </ul>
                    <li>قم بمسح رمز QR باستخدام تطبيق WhatsApp على هاتفك لتسجيل الدخول</li>
                </ol>

                <div class="alert alert-warning mt-3">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>ملاحظة هامة:</strong> يجب أن تبقى نافذة موجه الأوامر مفتوحة أثناء استخدام الخادم. إذا أغلقتها، سيتوقف الخادم عن العمل.
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                <a href="<?php echo BASE_URL; ?>downloads/whatsapp-local-server.zip" download class="btn btn-primary">
                    <i class="fas fa-download me-1"></i> تنزيل الخادم المحلي
                </a>
            </div>
        </div>
    </div>
</div>

<!-- استدعاء قالب الفوتر -->
<?php include '../../includes/templates/footer.php'; ?>

<!-- استدعاء ملف JavaScript لإدارة إعدادات WhatsApp من جانب العميل -->
<script src="<?php echo BASE_URL; ?>assets/js/whatsapp-settings.js"></script>
