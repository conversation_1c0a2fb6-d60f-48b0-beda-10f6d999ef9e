<?php
/**
 * الصفحة الرئيسية لنظام إدارة الصالونات - النسخة التجريبية
 */

// التحقق من وجود ملف النسخة التجريبية
if (file_exists('includes/demo_check.php')) {
    require_once 'includes/demo_check.php';
}

// إذا كانت نسخة تجريبية، عرض صفحة الترحيب
if (defined('DEMO_MODE') && DEMO_MODE === true) {
    ?>
    <!DOCTYPE html>
    <html lang="ar" dir="rtl">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>نظام إدارة الصالونات - نسخة تجريبية</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
        <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
        <link href="assets/css/demo-mode.css" rel="stylesheet">
        <style>
            body {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                min-height: 100vh;
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            }
            .hero-section {
                padding: 80px 0;
                color: white;
                text-align: center;
            }
            .demo-card {
                background: rgba(255, 255, 255, 0.95);
                border-radius: 20px;
                box-shadow: 0 20px 40px rgba(0,0,0,0.1);
                padding: 40px;
                margin: 20px 0;
                backdrop-filter: blur(10px);
            }
            .feature-icon {
                font-size: 3rem;
                color: #667eea;
                margin-bottom: 20px;
            }
            .demo-badge-large {
                background: linear-gradient(45deg, #ff6b6b, #ff4757);
                color: white;
                padding: 10px 20px;
                border-radius: 25px;
                font-weight: bold;
                display: inline-block;
                margin-bottom: 20px;
                animation: pulse 2s infinite;
            }
        </style>
    </head>
    <body class="demo-mode">
        <!-- شريط التحذير -->
        <div class="demo-warning-bar">
            <i class="fas fa-exclamation-triangle"></i>
            مرحباً بك في النسخة التجريبية لنظام إدارة الصالونات - جميع البيانات وهمية لأغراض العرض فقط
        </div>

        <!-- القسم الرئيسي -->
        <div class="hero-section">
            <div class="container">
                <div class="row justify-content-center">
                    <div class="col-lg-10">
                        <div class="demo-badge-large">
                            <i class="fas fa-flask"></i> نسخة تجريبية
                        </div>
                        <h1 class="display-4 mb-4">نظام إدارة الصالونات</h1>
                        <p class="lead mb-5">نظام شامل لإدارة صالونات التجميل والحلاقة مع جميع الميزات المتقدمة</p>

                        <div class="row">
                            <div class="col-md-6 mb-4">
                                <a href="pages/auth/login.php" class="btn btn-primary btn-lg w-100">
                                    <i class="fas fa-sign-in-alt"></i> دخول النظام
                                </a>
                            </div>
                            <div class="col-md-6 mb-4">
                                <button onclick="showDemoInfo()" class="btn btn-outline-light btn-lg w-100">
                                    <i class="fas fa-info-circle"></i> معلومات النسخة التجريبية
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- قسم الميزات -->
        <div class="container mb-5">
            <div class="row">
                <div class="col-lg-4 mb-4">
                    <div class="demo-card text-center">
                        <i class="fas fa-calendar-alt feature-icon"></i>
                        <h4>إدارة المواعيد</h4>
                        <p>نظام متقدم لحجز وإدارة المواعيد مع تنبيهات تلقائية</p>
                    </div>
                </div>
                <div class="col-lg-4 mb-4">
                    <div class="demo-card text-center">
                        <i class="fas fa-users feature-icon"></i>
                        <h4>إدارة العملاء</h4>
                        <p>قاعدة بيانات شاملة للعملاء مع نظام نقاط الولاء</p>
                    </div>
                </div>
                <div class="col-lg-4 mb-4">
                    <div class="demo-card text-center">
                        <i class="fas fa-chart-line feature-icon"></i>
                        <h4>التقارير والإحصائيات</h4>
                        <p>تقارير مفصلة عن المبيعات والأرباح والأداء</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- قسم بيانات الدخول -->
        <div class="container mb-5">
            <div class="row justify-content-center">
                <div class="col-lg-8">
                    <div class="demo-card">
                        <h3 class="text-center mb-4"><i class="fas fa-key"></i> بيانات الدخول التجريبية</h3>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="card mb-3">
                                    <div class="card-body">
                                        <h6 class="card-title"><i class="fas fa-user-shield"></i> المدير</h6>
                                        <p class="card-text">
                                            <strong>اسم المستخدم:</strong> admin<br>
                                            <strong>كلمة المرور:</strong> password
                                        </p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="card mb-3">
                                    <div class="card-body">
                                        <h6 class="card-title"><i class="fas fa-user-tie"></i> مدير الفرع</h6>
                                        <p class="card-text">
                                            <strong>اسم المستخدم:</strong> manager<br>
                                            <strong>كلمة المرور:</strong> password
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="card mb-3">
                                    <div class="card-body">
                                        <h6 class="card-title"><i class="fas fa-cash-register"></i> أمين الصندوق</h6>
                                        <p class="card-text">
                                            <strong>اسم المستخدم:</strong> cashier<br>
                                            <strong>كلمة المرور:</strong> password
                                        </p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="card mb-3">
                                    <div class="card-body">
                                        <h6 class="card-title"><i class="fas fa-user"></i> مستخدم تجريبي</h6>
                                        <p class="card-text">
                                            <strong>اسم المستخدم:</strong> demo<br>
                                            <strong>كلمة المرور:</strong> password
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- التذييل -->
        <footer class="bg-dark text-light py-4">
            <div class="container text-center">
                <div class="demo-warning mb-3">
                    <i class="fas fa-exclamation-triangle"></i>
                    هذا النظام في وضع العرض التجريبي - جميع البيانات وهمية لأغراض العرض والتجربة فقط
                </div>
                <p>&copy; 2025 نظام إدارة الصالونات - النسخة التجريبية</p>
                <p>
                    <a href="mailto:<EMAIL>" class="text-light">
                        <i class="fas fa-envelope"></i> للاستفسار عن النسخة الكاملة
                    </a>
                </p>
            </div>
        </footer>

        <!-- العلامة المائية -->
        <div class="demo-watermark">نسخة تجريبية</div>

        <!-- JavaScript -->
        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
        <script src="assets/js/demo-mode.js"></script>

        <script>
        function showDemoInfo() {
            const modal = document.createElement('div');
            modal.className = 'modal fade';
            modal.innerHTML = `
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header bg-warning">
                            <h5 class="modal-title"><i class="fas fa-info-circle"></i> معلومات النسخة التجريبية</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <div class="alert alert-info">
                                <h6><i class="fas fa-flask"></i> حول هذه النسخة:</h6>
                                <ul>
                                    <li>جميع البيانات وهمية لأغراض العرض والتجربة</li>
                                    <li>بعض الوظائف مقيدة لأغراض الأمان</li>
                                    <li>يتم إعادة تعيين البيانات دورياً</li>
                                    <li>الأداء قد يختلف عن النسخة الفعلية</li>
                                </ul>
                            </div>

                            <div class="alert alert-warning">
                                <h6><i class="fas fa-lock"></i> العمليات المقيدة:</h6>
                                <ul>
                                    <li>حذف البيانات الأساسية</li>
                                    <li>إنشاء واستعادة النسخ الاحتياطية</li>
                                    <li>تعديل إعدادات النظام الحساسة</li>
                                    <li>إعادة تعيين النظام</li>
                                </ul>
                            </div>

                            <div class="bg-light p-3 rounded">
                                <small class="text-muted">
                                    <i class="fas fa-envelope"></i>
                                    للحصول على النسخة الكاملة أو الاستفسار:
                                    <a href="mailto:<EMAIL>"><EMAIL></a>
                                </small>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                            <a href="pages/auth/login.php" class="btn btn-primary">دخول النظام</a>
                        </div>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);
            const bsModal = new bootstrap.Modal(modal);
            bsModal.show();

            modal.addEventListener('hidden.bs.modal', () => {
                modal.remove();
            });
        }
        </script>
    </body>
    </html>
    <?php
} else {
    // إذا لم تكن نسخة تجريبية، توجيه إلى صفحة تسجيل الدخول
    header('Location: pages/auth/login.php');
    exit;
}
?>