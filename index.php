<?php
/**
 * الصفحة الرئيسية لنظام إدارة الصالونات - النسخة التجريبية
 * تطوير: بشمهندس محمود صلاح - hoktech.site
 */
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة الصالونات - النسخة التجريبية | HokTech</title>
    <meta name="description" content="نظام شامل لإدارة صالونات التجميل والحلاقة - النسخة التجريبية">
    <!-- Bootstrap RTL CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.0.2/dist/css/bootstrap.rtl.min.css">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Font Tajawal -->
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap">
    <style>
        body {
            font-family: 'Tajawal', sans-serif;
            background-color: #f8f9fa;
            margin: 0;
            overflow-x: hidden;
        }

        :root {
            --primary-color: #3498db;
            --secondary-color: #2ecc71;
            --accent-color: #f39c12;
            --dark-color: #212529;
            --light-color: #f8f9fa;
            --success-color: #2ecc71;
            --info-color: #36b9cc;
            --warning-color: #f6c23e;
            --danger-color: #e74a3b;
        }

        .hero-section {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
            padding: 100px 0;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .demo-badge {
            background: linear-gradient(45deg, var(--warning-color), var(--accent-color));
            color: white;
            padding: 10px 20px;
            border-radius: 25px;
            font-weight: bold;
            display: inline-block;
            margin-bottom: 20px;
            animation: pulse 2s infinite;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        .feature-card {
            background: white;
            border-radius: 10px;
            padding: 30px 20px;
            margin: 20px 0;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            height: 100%;
            border: none;
            text-align: center;
        }

        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
        }

        .feature-icon {
            font-size: 3rem;
            color: var(--primary-color);
            margin-bottom: 20px;
            display: block;
        }

        .btn-demo {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
            color: white;
            padding: 15px 30px;
            border-radius: 5px;
            font-weight: 600;
            font-size: 1rem;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            border: 1px solid var(--primary-color);
        }

        .btn-demo:hover {
            background-color: #2980b9;
            border-color: #2980b9;
            color: white;
            transform: translateY(-2px);
        }

        .btn-outline-demo {
            border: 2px solid white;
            color: white;
            background: transparent;
            padding: 13px 28px;
            border-radius: 5px;
            font-weight: 600;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }

        .btn-outline-demo:hover {
            background: white;
            color: var(--primary-color);
            transform: translateY(-2px);
        }

        .stats-section {
            background: white;
            padding: 80px 0;
            position: relative;
        }

        .stat-item {
            text-align: center;
            padding: 20px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            margin-bottom: 20px;
            transition: all 0.3s ease;
        }

        .stat-item:hover {
            transform: translateY(-3px);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: bold;
            color: var(--primary-color);
            margin-bottom: 10px;
            display: block;
        }

        .stat-label {
            font-size: 1rem;
            color: #6c757d;
            font-weight: 600;
        }

        .floating-shapes {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            overflow: hidden;
            z-index: 1;
            pointer-events: none;
        }

        .shape {
            position: absolute;
            background: rgba(255,255,255,0.05);
            border-radius: 50%;
            animation: float 10s ease-in-out infinite;
        }

        .shape:nth-child(1) {
            width: 80px;
            height: 80px;
            top: 20%;
            left: 10%;
            animation-delay: 0s;
        }

        .shape:nth-child(2) {
            width: 120px;
            height: 120px;
            top: 60%;
            right: 15%;
            animation-delay: 4s;
        }

        .shape:nth-child(3) {
            width: 60px;
            height: 60px;
            top: 80%;
            left: 25%;
            animation-delay: 8s;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
        }

        .section-title {
            text-align: center;
            margin-bottom: 70px;
        }

        .section-title h2 {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--dark-color);
            margin-bottom: 15px;
            position: relative;
        }

        .section-title h2::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 3px;
            background: var(--primary-color);
            border-radius: 2px;
        }

        .section-title p {
            font-size: 1.1rem;
            color: #6c757d;
            max-width: 600px;
            margin: 0 auto;
        }

        .demo-warning-bar {
            background-color: var(--warning-color);
            color: #333;
            padding: 12px 0;
            text-align: center;
            font-weight: 600;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
            position: relative;
            z-index: 1000;
        }

        .footer {
            background: var(--dark-color);
            color: white;
            padding: 60px 0 30px;
            position: relative;
        }

        .contact-info {
            background: rgba(255,255,255,0.1);
            padding: 25px;
            border-radius: 8px;
            margin: 20px 0;
            border: 1px solid rgba(255,255,255,0.2);
        }

        .social-links a {
            color: white;
            font-size: 1.5rem;
            margin: 0 10px;
            transition: all 0.3s ease;
            display: inline-block;
        }

        .social-links a:hover {
            color: var(--primary-color);
            transform: translateY(-2px);
        }

        .developer-info {
            background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
            color: white;
            padding: 30px;
            border-radius: 8px;
            margin: 20px 0;
            text-align: center;
        }

        .demo-watermark {
            position: fixed;
            top: 50%;
            right: -50px;
            transform: rotate(-45deg);
            background: rgba(52, 152, 219, 0.1);
            color: rgba(52, 152, 219, 0.3);
            padding: 10px 100px;
            font-size: 1.2rem;
            font-weight: bold;
            z-index: 1000;
            pointer-events: none;
            border: 2px solid rgba(52, 152, 219, 0.2);
        }

        .login-card {
            background: white;
            border-radius: 10px;
            padding: 30px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            border: none;
            margin-bottom: 20px;
        }

        @media (max-width: 768px) {
            .hero-section {
                padding: 60px 0;
            }

            .section-title h2 {
                font-size: 2rem;
            }

            .stat-number {
                font-size: 2rem;
            }

            .feature-icon {
                font-size: 2.5rem;
            }

            .btn-demo, .btn-outline-demo {
                padding: 12px 25px;
                font-size: 0.9rem;
            }

            .developer-info, .contact-info {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <!-- شريط التحذير -->
    <div class="demo-warning-bar">
        <div class="container">
            <i class="fas fa-exclamation-triangle me-2"></i>
            مرحباً بك في النسخة التجريبية لنظام إدارة الصالونات - جميع البيانات وهمية لأغراض العرض فقط
        </div>
    </div>

    <!-- القسم الرئيسي -->
    <div class="hero-section">
        <div class="floating-shapes">
            <div class="shape"></div>
            <div class="shape"></div>
            <div class="shape"></div>
        </div>

        <div class="container" style="position: relative; z-index: 2;">
            <div class="row justify-content-center">
                <div class="col-lg-10">
                    <div class="demo-badge">
                        <i class="fas fa-flask me-2"></i> نسخة تجريبية
                    </div>
                    <h1 class="display-4 mb-4 fw-bold">نظام إدارة الصالونات</h1>
                    <p class="lead mb-5">الحل الشامل والمتطور لإدارة صالونات التجميل والحلاقة<br>مع جميع الميزات المتقدمة والتقارير التفصيلية</p>

                    <div class="row justify-content-center">
                        <div class="col-md-4 mb-3">
                            <a href="pages/auth/login.php" class="btn-demo w-100 text-center">
                                <i class="fas fa-sign-in-alt me-2"></i> دخول النظام
                            </a>
                        </div>
                        <div class="col-md-4 mb-3">
                            <button onclick="showDemoInfo()" class="btn-outline-demo w-100">
                                <i class="fas fa-info-circle me-2"></i> معلومات التجربة
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- قسم الإحصائيات -->
    <div class="stats-section">
        <div class="container">
            <div class="row">
                <div class="col-lg-3 col-md-6">
                    <div class="stat-item">
                        <span class="stat-number">15+</span>
                        <div class="stat-label">ميزة متقدمة</div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6">
                    <div class="stat-item">
                        <span class="stat-number">100%</span>
                        <div class="stat-label">باللغة العربية</div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6">
                    <div class="stat-item">
                        <span class="stat-number">24/7</span>
                        <div class="stat-label">دعم فني</div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6">
                    <div class="stat-item">
                        <span class="stat-number">∞</span>
                        <div class="stat-label">عدد العملاء</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- قسم الميزات -->
    <div class="container my-5">
        <div class="section-title">
            <h2>مميزات النظام</h2>
            <p>نظام شامل ومتطور يلبي جميع احتياجات صالونات التجميل والحلاقة</p>
        </div>

        <div class="row">
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="feature-card text-center">
                    <i class="fas fa-calendar-check feature-icon"></i>
                    <h4 class="mb-3">إدارة المواعيد</h4>
                    <p>نظام متقدم لحجز وإدارة المواعيد مع تنبيهات تلقائية وتذكيرات للعملاء</p>
                </div>
            </div>
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="feature-card text-center">
                    <i class="fas fa-users feature-icon"></i>
                    <h4 class="mb-3">إدارة العملاء</h4>
                    <p>قاعدة بيانات شاملة للعملاء مع نظام نقاط الولاء وتاريخ الزيارات</p>
                </div>
            </div>
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="feature-card text-center">
                    <i class="fas fa-cash-register feature-icon"></i>
                    <h4 class="mb-3">نقطة البيع</h4>
                    <p>نظام نقطة بيع متطور مع إدارة الفواتير والمدفوعات المتعددة</p>
                </div>
            </div>
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="feature-card text-center">
                    <i class="fas fa-chart-line feature-icon"></i>
                    <h4 class="mb-3">التقارير والإحصائيات</h4>
                    <p>تقارير مفصلة عن المبيعات والأرباح والأداء مع رسوم بيانية تفاعلية</p>
                </div>
            </div>
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="feature-card text-center">
                    <i class="fas fa-boxes feature-icon"></i>
                    <h4 class="mb-3">إدارة المخزون</h4>
                    <p>متابعة المنتجات والمستلزمات مع تنبيهات نفاد المخزون</p>
                </div>
            </div>
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="feature-card text-center">
                    <i class="fas fa-user-tie feature-icon"></i>
                    <h4 class="mb-3">إدارة الموظفين</h4>
                    <p>متابعة أداء الموظفين والعمولات والرواتب والحضور</p>
                </div>
            </div>
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="feature-card text-center">
                    <i class="fas fa-tags feature-icon"></i>
                    <h4 class="mb-3">أكواد الخصم</h4>
                    <p>إنشاء وإدارة أكواد الخصم والعروض الترويجية للعملاء</p>
                </div>
            </div>
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="feature-card text-center">
                    <i class="fas fa-bell feature-icon"></i>
                    <h4 class="mb-3">نظام الإشعارات</h4>
                    <p>نظام اشعارات غير تقليدي يعتمد علي whatsapp للعملاء والاداره </p>
                </div>
            </div>
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="feature-card text-center">
                    <i class="fas fa-mobile-alt feature-icon"></i>
                    <h4 class="mb-3">متوافق مع الجوال</h4>
                    <p>تصميم متجاوب يعمل بكفاءة على جميع الأجهزة والشاشات</p>
                </div>
            </div>
        </div>
    </div>

    <!-- قسم بيانات الدخول -->
    <div class="container my-5">
        <div class="row justify-content-center">
            <div class="col-lg-10">
                <div class="login-card">
                    <div class="section-title">
                        <h2><i class="fas fa-key me-3"></i>بيانات الدخول التجريبية</h2>
                        <p>استخدم أي من الحسابات التالية لتجربة النظام</p>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-4">
                            <div class="feature-card">
                                <div class="text-center">
                                    <i class="fas fa-user-shield feature-icon"></i>
                                    <h5 class="mb-3">المدير العام</h5>
                                    <div class="bg-light p-3 rounded">
                                        <p class="mb-2"><strong>اسم المستخدم:</strong> <code>admin</code></p>
                                        <p class="mb-0"><strong>كلمة المرور:</strong> <code>password</code></p>
                                    </div>
                                    <small class="text-muted mt-2 d-block">صلاحيات كاملة لجميع أجزاء النظام</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 mb-4">
                            <div class="feature-card">
                                <div class="text-center">
                                    <i class="fas fa-user-tie feature-icon"></i>
                                    <h5 class="mb-3">مدير الفرع</h5>
                                    <div class="bg-light p-3 rounded">
                                        <p class="mb-2"><strong>اسم المستخدم:</strong> <code>manager</code></p>
                                        <p class="mb-0"><strong>كلمة المرور:</strong> <code>password</code></p>
                                    </div>
                                    <small class="text-muted mt-2 d-block">إدارة العمليات اليومية والتقارير</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 mb-4">
                            <div class="feature-card">
                                <div class="text-center">
                                    <i class="fas fa-cash-register feature-icon"></i>
                                    <h5 class="mb-3">أمين الصندوق</h5>
                                    <div class="bg-light p-3 rounded">
                                        <p class="mb-2"><strong>اسم المستخدم:</strong> <code>cashier</code></p>
                                        <p class="mb-0"><strong>كلمة المرور:</strong> <code>password</code></p>
                                    </div>
                                    <small class="text-muted mt-2 d-block">نقطة البيع والفواتير والمدفوعات</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 mb-4">
                            <div class="feature-card">
                                <div class="text-center">
                                    <i class="fas fa-user feature-icon"></i>
                                    <h5 class="mb-3">مستخدم تجريبي</h5>
                                    <div class="bg-light p-3 rounded">
                                        <p class="mb-2"><strong>اسم المستخدم:</strong> <code>demo</code></p>
                                        <p class="mb-0"><strong>كلمة المرور:</strong> <code>password</code></p>
                                    </div>
                                    <small class="text-muted mt-2 d-block">صلاحيات محدودة للتجربة الآمنة</small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="text-center mt-4">
                        <a href="pages/auth/login.php" class="btn-demo">
                            <i class="fas fa-sign-in-alt me-2"></i>ابدأ التجربة الآن
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- التذييل -->
    <footer class="footer">
        <div class="container">
            <div class="row">
                <div class="col-lg-4 mb-4">
                    <div class="developer-info">
                        <h4 class="mb-3"><i class="fas fa-code me-2"></i>معلومات المطور</h4>
                        <p class="mb-2"><strong>بشمهندس محمود صلاح</strong></p>
                        <p class="mb-2"><i class="fas fa-globe me-2"></i>hoktech.site</p>
                        <p class="mb-2"><i class="fas fa-phone me-2"></i>+201556262660</p>
                        <p class="mb-0"><i class="fas fa-envelope me-2"></i><EMAIL></p>
                    </div>
                </div>

                <div class="col-lg-4 mb-4">
                    <div class="contact-info">
                        <h4 class="mb-3"><i class="fas fa-info-circle me-2"></i>حول النظام</h4>
                        <p>نظام شامل لإدارة صالونات التجميل والحلاقة مصمم خصيصاً للسوق العربي</p>
                        <ul class="list-unstyled">
                            <li><i class="fas fa-check me-2 text-success"></i>واجهة عربية بالكامل</li>
                            <li><i class="fas fa-check me-2 text-success"></i>تقارير مفصلة</li>
                            <li><i class="fas fa-check me-2 text-success"></i>دعم فني متواصل</li>
                            <li><i class="fas fa-check me-2 text-success"></i>تحديثات مجانية</li>
                        </ul>
                    </div>
                </div>

                <div class="col-lg-4 mb-4">
                    <div class="contact-info">
                        <h4 class="mb-3"><i class="fas fa-handshake me-2"></i>تواصل معنا</h4>
                        <p>للحصول على النسخة الكاملة أو الاستفسار عن الخدمات</p>

                        <div class="social-links text-center mt-4">
                            <a href="https://wa.me/201556262660" target="_blank" title="واتساب">
                                <i class="fab fa-whatsapp"></i>
                            </a>
                            <a href="tel:+201556262660" title="اتصال">
                                <i class="fas fa-phone"></i>
                            </a>
                            <a href="mailto:<EMAIL>" title="بريد إلكتروني">
                                <i class="fas fa-envelope"></i>
                            </a>
                            <a href="https://hoktech.site" target="_blank" title="الموقع الرسمي">
                                <i class="fas fa-globe"></i>
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <hr class="my-4" style="border-color: rgba(255,255,255,0.2);">

            <div class="row">
                <div class="col-md-6">
                    <div class="demo-warning mb-3">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        هذا النظام في وضع العرض التجريبي - جميع البيانات وهمية لأغراض العرض والتجربة فقط
                    </div>
                </div>
                <div class="col-md-6 text-md-end">
                    <p class="mb-0">&copy; 2025 جميع الحقوق محفوظة - HokTech</p>
                    <p class="mb-0">تطوير: بشمهندس محمود صلاح</p>
                </div>
            </div>
        </div>
    </footer>

    <!-- العلامة المائية -->
    <div class="demo-watermark">نسخة تجريبية</div>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        function showDemoInfo() {
            const modal = document.createElement('div');
            modal.className = 'modal fade';
            modal.innerHTML = `
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header" style="background: var(--primary-color); color: white;">
                            <h5 class="modal-title"><i class="fas fa-info-circle me-2"></i>معلومات النسخة التجريبية</h5>
                            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <div class="alert alert-info">
                                <h6><i class="fas fa-flask me-2"></i>حول هذه النسخة:</h6>
                                <ul class="mb-0">
                                    <li>جميع البيانات وهمية لأغراض العرض والتجربة</li>
                                    <li>بعض الوظائف مقيدة لأغراض الأمان</li>
                                    <li>يتم إعادة تعيين البيانات دورياً</li>
                                    <li>الأداء قد يختلف عن النسخة الفعلية</li>
                                </ul>
                            </div>

                            <div class="alert alert-warning">
                                <h6><i class="fas fa-lock me-2"></i>العمليات المقيدة:</h6>
                                <ul class="mb-0">
                                    <li>حذف البيانات الأساسية</li>
                                    <li>إنشاء واستعادة النسخ الاحتياطية</li>
                                    <li>تعديل إعدادات النظام الحساسة</li>
                                    <li>إعادة تعيين النظام</li>
                                </ul>
                            </div>

                            <div class="alert alert-success">
                                <h6><i class="fas fa-star me-2"></i>مميزات النسخة الكاملة:</h6>
                                <ul class="mb-0">
                                    <li>إدارة متعددة الفروع</li>
                                    <li>تكامل مع أنظمة الدفع الإلكتروني</li>
                                    <li>تطبيق جوال للعملاء</li>
                                    <li>نسخ احتياطية تلقائية</li>
                                    <li>دعم فني مباشر</li>
                                </ul>
                            </div>

                            <div class="bg-light p-4 rounded text-center">
                                <h6 class="mb-3"><i class="fas fa-phone me-2"></i>للحصول على النسخة الكاملة</h6>
                                <p class="mb-2"><strong>بشمهندس محمود صلاح</strong></p>
                                <p class="mb-2"><i class="fas fa-phone me-2"></i>+201556262660</p>
                                <p class="mb-0"><i class="fas fa-envelope me-2"></i><EMAIL></p>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                            <a href="pages/auth/login.php" class="btn btn-primary">دخول النظام</a>
                            <a href="https://wa.me/201556262660" target="_blank" class="btn btn-success">
                                <i class="fab fa-whatsapp me-2"></i>تواصل واتساب
                            </a>
                        </div>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);
            const bsModal = new bootstrap.Modal(modal);
            bsModal.show();

            modal.addEventListener('hidden.bs.modal', () => {
                modal.remove();
            });
        }

        // تأثيرات تفاعلية
        document.addEventListener('DOMContentLoaded', function() {
            // تأثير التمرير للبطاقات
            const cards = document.querySelectorAll('.feature-card');

            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.style.opacity = '1';
                        entry.target.style.transform = 'translateY(0)';
                    }
                });
            });

            cards.forEach(card => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(30px)';
                card.style.transition = 'all 0.6s ease';
                observer.observe(card);
            });

            // تأثير الأرقام المتحركة
            const statNumbers = document.querySelectorAll('.stat-number');
            statNumbers.forEach(stat => {
                const observer = new IntersectionObserver((entries) => {
                    entries.forEach(entry => {
                        if (entry.isIntersecting) {
                            const target = entry.target;
                            const text = target.textContent;
                            if (text.includes('+') || text.includes('%')) {
                                target.style.animation = 'pulse 1s ease-in-out';
                            }
                        }
                    });
                });
                observer.observe(stat);
            });
        });
    </script>
</body>
</html>