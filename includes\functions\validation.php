<?php
/**
 * ملف دوال التحقق والتأكد من صحة البيانات
 * يحتوي على دوال للتحقق من صحة المدخلات قبل معالجتها
 */

// منع الوصول المباشر للملف
if (!defined('BASEPATH')) {
    exit('لا يمكن الوصول المباشر لهذا الملف');
}

/**
 * التحقق من صحة اسم المستخدم
 * @param string $username اسم المستخدم
 * @return bool
 */
function validateUsername($username) {
    // الطول بين 4 و 50 حرفًا
    // يحتوي على أحرف إنجليزية وأرقام فقط
    return preg_match('/^[a-zA-Z0-9_]{4,50}$/', $username);
}

/**
 * التحقق من قوة كلمة المرور
 * @param string $password كلمة المرور
 * @return bool
 */
function validatePassword($password) {
    // على الأقل 8 أحرف
    // يحتوي على حرف كبير وصغير ورقم ورمز خاص
    return preg_match('/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/', $password);
}

/**
 * التحقق من صحة الاسم
 * @param string $name الاسم
 * @return bool
 */
function validateName($name) {
    // الاسم يحتوي على حروف عربية وإنجليزية وفراغات
    return preg_match('/^[\p{L} ]{2,100}$/u', $name);
}

/**
 * التحقق من صحة البريد الإلكتروني
 * @param string $email البريد الإلكتروني
 * @return bool
 */
function validateEmail($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
}

/**
 * التحقق من صحة رقم الهاتف السعودي
 * @param string $phone رقم الهاتف
 * @return bool
 */
function validateSaudiPhone($phone) {
    // يبدأ بـ 05 أو +966 أو 966 ويتكون من 9 أرقام
    return preg_match('/^(05\d{8}|\+966\d{9}|966\d{9})$/', $phone);
}

/**
 * التحقق من صحة التاريخ
 * @param string $date التاريخ بتنسيق Y-m-d
 * @return bool
 */
function validateDate($date) {
    $format = 'Y-m-d';
    $d = DateTime::createFromFormat($format, $date);
    return $d && $d->format($format) === $date;
}

/**
 * التحقق من صحة المبلغ المالي
 * @param float $amount المبلغ
 * @param float $min الحد الأدنى (اختياري)
 * @param float $max الحد الأقصى (اختياري)
 * @return bool
 */
function validateAmount($amount, $min = 0, $max = PHP_FLOAT_MAX) {
    return is_numeric($amount) && 
           $amount >= $min && 
           $amount <= $max && 
           $amount >= 0;
}

/**
 * التحقق من صحة رقم الحساب البنكي
 * @param string $accountNumber رقم الحساب
 * @return bool
 */
function validateBankAccount($accountNumber) {
    // يتكون من أرقام فقط وطوله بين 10 و 20 رقمًا
    return preg_match('/^\d{10,20}$/', $accountNumber);
}

/**
 * التحقق من صحة كود التحقق (OTP)
 * @param string $otp كود التحقق
 * @return bool
 */
function validateOTP($otp) {
    // يتكون من 6 أرقام
    return preg_match('/^\d{6}$/', $otp);
}

/**
 * التحقق من صحة العنوان
 * @param string $address العنوان
 * @return bool
 */
function validateAddress($address) {
    // يحتوي على حروف وأرقام وفراغات وبعض الرموز
    return preg_match('/^[\p{L}0-9\s\-.,\/]{5,255}$/u', $address);
}

/**
 * التحقق من صحة رقم الهوية السعودية
 * @param string $id رقم الهوية
 * @return bool
 */
function validateSaudiID($id) {
    // يتكون من 10 أرقام
    return preg_match('/^\d{10}$/', $id);
}

/**
 * التحقق من صحة كود المنتج أو الخدمة
 * @param string $code الكود
 * @return bool
 */
function validateCode($code) {
    // يحتوي على أحرف وأرقام فقط
    return preg_match('/^[a-zA-Z0-9\-_]{3,50}$/', $code);
}

/**
 * التحقق من صحة الوقت
 * @param string $time الوقت بتنسيق H:i
 * @return bool
 */
function validateTime($time) {
    return preg_match('/^([01]\d|2[0-3]):([0-5]\d)$/', $time);
}

/**
 * التحقق من صحة مسار الملف
 * @param string $path مسار الملف
 * @return bool
 */
function validateFilePath($path) {
    return preg_match('/^[a-zA-Z0-9\-_\.\/]+$/', $path);
}

/**
 * التحقق من صحة رقم الفاتورة
 * @param string $invoiceNumber رقم الفاتورة
 * @return bool
 */
function validateInvoiceNumber($invoiceNumber) {
    // نمط رقم الفاتورة مكون من تاريخ وكود فرع ورقم عشوائي
    return preg_match('/^\d{8}\d{2}\d{4}$/', $invoiceNumber);
}

/**
 * التحقق من صحة القيم قبل إضافتها لقاعدة البيانات
 * @param array $data البيانات المراد التحقق منها
 * @param array $rules قواعد التحقق
 * @return array|bool مصفوفة بالأخطاء أو true في حالة النجاح
 */
function validateData($data, $rules) {
    $errors = [];
    
    foreach ($rules as $field => $fieldRules) {
        $value = $data[$field] ?? null;
        
        // التحقق من أن الحقل مطلوب
        if (isset($fieldRules['required']) && $fieldRules['required'] && empty($value)) {
            $errors[$field][] = 'هذا الحقل مطلوب';
        }
        
        // التحقق من الطول
        if (isset($fieldRules['min_length']) && mb_strlen($value) < $fieldRules['min_length']) {
            $errors[$field][] = 'الحد الأدنى للطول هو ' . $fieldRules['min_length'] . ' حرف';
        }
        
        if (isset($fieldRules['max_length']) && mb_strlen($value) > $fieldRules['max_length']) {
            $errors[$field][] = 'الحد الأقصى للطول هو ' . $fieldRules['max_length'] . ' حرف';
        }
        
        // التحقق من القيمة باستخدام دالة محددة
        if (isset($fieldRules['validate']) && is_callable($fieldRules['validate'])) {
            $validationResult = $fieldRules['validate']($value);
            if ($validationResult !== true) {
                $errors[$field][] = $validationResult;
            }
        }
    }
    
    return empty($errors) ? true : $errors;
}