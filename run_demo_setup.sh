#!/bin/bash

# ألوان للنص
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}========================================"
echo "   نظام إدارة الصالونات - النسخة التجريبية"
echo "   سكريبت الإعداد التلقائي"
echo -e "========================================${NC}"
echo

echo -e "${YELLOW}[1/5] التحقق من متطلبات النظام...${NC}"

# التحقق من PHP
if ! command -v php &> /dev/null; then
    echo -e "${RED}خطأ: PHP غير مثبت${NC}"
    echo "يرجى تثبيت PHP أولاً"
    exit 1
fi
echo -e "${GREEN}✓ PHP متوفر$(php --version | head -n1)${NC}"

# التحقق من MySQL
if ! command -v mysql &> /dev/null; then
    echo -e "${YELLOW}تحذير: MySQL غير متوفر في PATH${NC}"
    echo "تأكد من تشغيل خادم MySQL"
else
    echo -e "${GREEN}✓ MySQL متوفر${NC}"
fi

echo
echo -e "${YELLOW}[2/5] التحقق من صلاحيات الكتابة...${NC}"

# التحقق من صلاحيات الكتابة
if [ ! -w "." ]; then
    echo -e "${RED}خطأ: لا توجد صلاحيات كتابة في المجلد الحالي${NC}"
    exit 1
fi
echo -e "${GREEN}✓ صلاحيات الكتابة متوفرة${NC}"

echo
echo -e "${YELLOW}[3/5] إنشاء المجلدات المطلوبة...${NC}"

# إنشاء المجلدات
mkdir -p logs backups scripts includes
chmod 755 logs backups scripts includes
echo -e "${GREEN}✓ تم إنشاء المجلدات${NC}"

echo
echo -e "${YELLOW}[4/5] تشغيل سكريبت الإعداد...${NC}"

# تشغيل سكريبت الإعداد
php setup_demo.php
if [ $? -ne 0 ]; then
    echo -e "${RED}خطأ: فشل في تشغيل سكريبت الإعداد${NC}"
    echo "تحقق من إعدادات قاعدة البيانات"
    exit 1
fi

echo
echo -e "${YELLOW}[5/5] التحقق من الإعداد...${NC}"

# التحقق من الملفات
if [ -f "includes/demo_check.php" ]; then
    echo -e "${GREEN}✓ تم إنشاء ملفات النسخة التجريبية${NC}"
else
    echo -e "${YELLOW}تحذير: لم يتم إنشاء بعض الملفات${NC}"
fi

echo
echo -e "${GREEN}========================================"
echo "تم إعداد النسخة التجريبية بنجاح!"
echo -e "========================================${NC}"
echo
echo -e "${BLUE}بيانات الدخول:${NC}"
echo "- المدير: admin / password"
echo "- مدير الفرع: manager / password"
echo "- أمين الصندوق: cashier / password"
echo "- مستخدم تجريبي: demo / password"
echo
echo -e "${BLUE}للوصول للنظام:${NC}"
echo "http://localhost/demo/"
echo
echo -e "${BLUE}للمساعدة، راجع ملف README_DEMO.md${NC}"
echo

# جعل الملف قابل للتنفيذ
chmod +x "$0"

echo -e "${GREEN}اضغط Enter للمتابعة...${NC}"
read
