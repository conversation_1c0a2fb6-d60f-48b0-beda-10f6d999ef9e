<?php
/**
 * API للتعامل مع النسخ الاحتياطي لقاعدة البيانات
 */

// استدعاء ملفات التهيئة
require_once '../config/init.php';

// التحقق من تسجيل الدخول
requireLogin();

// التحقق من الصلاحيات
requirePermission('endday_manage');

// دالة إنشاء نسخة احتياطية
function createBackup() {
    try {
        // إنشاء مجلد النسخ الاحتياطي إذا لم يكن موجوداً
        $backupDir = __DIR__ . '/../backups';
        if (!file_exists($backupDir)) {
            if (!mkdir($backupDir, 0755, true)) {
                throw new Exception('فشل في إنشاء مجلد النسخ الاحتياطي');
            }
        }

        // إنشاء اسم الملف مع التاريخ والوقت
        $date = date('Y-m-d_H-i-s');
        $backupFile = $backupDir . '/backup_' . $date . '.sql';

        // إنشاء كائن النسخ الاحتياطي
        $backup = new DatabaseBackup(DB_HOST, DB_USER, DB_PASS, DB_NAME);
        
        // عمل النسخة الاحتياطية
        if (!$backup->backup($backupFile)) {
            throw new Exception('فشل في إنشاء النسخة الاحتياطية');
        }

        // التحقق من وجود الملف
        if (!file_exists($backupFile)) {
            throw new Exception('لم يتم إنشاء ملف النسخة الاحتياطية');
        }

        // التحقق من حجم الملف
        if (filesize($backupFile) === 0) {
            throw new Exception('تم إنشاء ملف النسخة الاحتياطية ولكن حجمه صفر');
        }

        // ضغط الملف بكلمة مرور
        $zipFile = $backupFile . '.zip';
        $zip = new ZipArchive();
        if ($zip->open($zipFile, ZipArchive::CREATE) === TRUE) {
            $password = 'hoktech010--';
            $zip->setPassword($password);
            $zip->addFile($backupFile, basename($backupFile));
            $zip->setEncryptionName(basename($backupFile), ZipArchive::EM_AES_256);
            $zip->close();
            // حذف ملف SQL الأصلي
            unlink($backupFile);
            // إرجاع مسار ملف ZIP المحمي
            return 'backups/' . basename($zipFile);
        } else {
            throw new Exception('فشل في ضغط النسخة الاحتياطية بكلمة مرور');
        }
    } catch (Exception $e) {
        error_log('Backup error: ' . $e->getMessage());
        throw $e;
    }
}

// معالجة الطلبات
try {
    $action = $_GET['action'] ?? '';

    // تسجيل الإجراء للتشخيص
    error_log('Backup API action: ' . $action);

    switch ($action) {
        case 'create':
            $backupFile = createBackup();
            echo json_encode([
                'status' => 'success',
                'message' => 'تم إنشاء النسخة الاحتياطية بنجاح',
                'backup_file' => $backupFile
            ]);
            break;

        case 'download':
            $filename = $_GET['filename'] ?? '';
            if (empty($filename)) {
                throw new Exception('اسم الملف مطلوب');
            }

            // تنظيف اسم الملف لمنع الوصول غير المصرح به
            $filename = basename($filename);
            $filePath = __DIR__ . '/../backups/' . $filename;

            if (!file_exists($filePath)) {
                throw new Exception('الملف غير موجود');
            }

            // التحقق من نوع الملف
            $ext = pathinfo($filePath, PATHINFO_EXTENSION);
            if (!in_array($ext, ['zip'])) {
                throw new Exception('نوع الملف غير صالح (يسمح فقط بملفات ZIP المحمية)');
            }

            // إرسال الملف للتحميل
            if ($ext === 'zip') {
                header('Content-Type: application/zip');
            }
            header('Content-Disposition: attachment; filename="' . $filename . '"');
            header('Content-Length: ' . filesize($filePath));
            header('Cache-Control: no-cache, must-revalidate');
            header('Pragma: no-cache');
            header('Expires: 0');
            
            readfile($filePath);
            exit;

        default:
            throw new Exception('إجراء غير صالح: ' . $action);
    }
} catch (Exception $e) {
    error_log('Backup API error: ' . $e->getMessage());
    http_response_code(500);
    echo json_encode([
        'status' => 'error',
        'message' => $e->getMessage()
    ]);
} 