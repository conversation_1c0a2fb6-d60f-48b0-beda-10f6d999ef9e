<?php
/**
 * صفحة تحديث توزيع الخصومات على عناصر الفواتير
 */

// تعريف ثابت لمنع الوصول المباشر للملفات المرتبطة
define('BASEPATH', true);

// استدعاء ملفات التهيئة
require_once __DIR__ . '/../../config/init.php';

// التحقق من تسجيل الدخول
redirectIfNotLoggedIn();

// التحقق من الصلاحيات
if (!hasPermission('admin_access')) {
    $_SESSION['error_message'] = 'ليس لديك صلاحية للوصول إلى هذه الصفحة';
    header('Location: ' . BASE_URL . 'pages/dashboard.php');
    exit;
}

// إنشاء كائن قاعدة البيانات
$db = new Database();

// معالجة النموذج
$updated = false;
$previewMode = false;
$updateResult = null;
$previewData = null;

// استرجاع بيانات المعاينة من الجلسة إذا كانت موجودة
if (isset($_SESSION['discount_preview_data'])) {
    $previewData = $_SESSION['discount_preview_data'];
    $previewMode = true;
}

// معالجة طلب المعاينة
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['preview_discounts'])) {
    try {
        // تنفيذ سكريبت تحديث الخصومات في وضع المعاينة
        ob_start();
        $preview = true; // تعيين وضع المعاينة
        include __DIR__ . '/../../scripts/update_invoice_discounts.php';
        $previewResult = ob_get_clean();

        $previewMode = true;
        $updateResult = $previewResult;

        // تخزين بيانات المعاينة في الجلسة للاستخدام لاحقًا
        if (isset($previewInvoicesData)) {
            $_SESSION['discount_preview_data'] = $previewInvoicesData;
        }
    } catch (Exception $e) {
        $_SESSION['error_message'] = 'حدث خطأ أثناء معاينة تحديث الخصومات: ' . $e->getMessage();
    }
}

// معالجة طلب التحديث بعد المعاينة
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['confirm_update'])) {
    try {
        // تنفيذ سكريبت تحديث الخصومات باستخدام البيانات المخزنة مسبقًا
        ob_start();
        $confirm = true; // تعيين وضع التأكيد
        include __DIR__ . '/../../scripts/update_invoice_discounts.php';
        $updateResult = ob_get_clean();

        $updated = true;
        $previewMode = false;

        // مسح بيانات المعاينة من الجلسة
        unset($_SESSION['discount_preview_data']);

        $_SESSION['success_message'] = 'تم تحديث توزيع الخصومات بنجاح';
    } catch (Exception $e) {
        $_SESSION['error_message'] = 'حدث خطأ أثناء تحديث توزيع الخصومات: ' . $e->getMessage();
    }
}

// معالجة طلب إلغاء المعاينة
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['cancel_preview'])) {
    // مسح بيانات المعاينة من الجلسة
    unset($_SESSION['discount_preview_data']);
    $previewMode = false;
}

// معالجة طلب التحديث المباشر (بدون معاينة)
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_discounts'])) {
    try {
        // تنفيذ سكريبت تحديث الخصومات
        ob_start();
        $preview = false; // تعيين وضع التنفيذ المباشر
        include __DIR__ . '/../../scripts/update_invoice_discounts.php';
        $updateResult = ob_get_clean();

        $updated = true;
        $_SESSION['success_message'] = 'تم تحديث توزيع الخصومات بنجاح';
    } catch (Exception $e) {
        $_SESSION['error_message'] = 'حدث خطأ أثناء تحديث توزيع الخصومات: ' . $e->getMessage();
    }
}

// إدراج قالب الرأس
include_once __DIR__ . '/../../includes/templates/header.php';
?>

<!-- محتوى الصفحة -->
<div class="container-fluid">
    <h1 class="mt-4">تحديث توزيع الخصومات</h1>
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="<?php echo BASE_URL; ?>pages/dashboard.php">الرئيسية</a></li>
            <li class="breadcrumb-item active" aria-current="page">تحديث توزيع الخصومات</li>
        </ol>
    </nav>

    <?php include_once __DIR__ . '/../../includes/templates/alerts.php'; ?>

    <div class="card mb-4">
        <div class="card-header">
            <i class="fas fa-sync-alt me-1"></i>
            تحديث توزيع الخصومات على عناصر الفواتير
        </div>
        <div class="card-body">
            <div class="alert alert-info">
                <p>هذه الأداة ستقوم بتحديث توزيع الخصومات على عناصر الفواتير السابقة بشكل متناسب.</p>
                <p>سيتم توزيع الخصم على كل عنصر في الفاتورة بناءً على نسبة قيمته من إجمالي الفاتورة.</p>
                <p><strong>ملاحظة هامة:</strong> يُفضل عمل نسخة احتياطية من قاعدة البيانات قبل تنفيذ هذه العملية.</p>
                <p>يمكنك استخدام زر <strong>معاينة التحديثات</strong> لعرض التغييرات قبل تطبيقها على قاعدة البيانات.</p>
            </div>

            <?php if (!$previewMode): ?>
            <form method="post" action="">
                <div class="d-grid gap-2 col-md-6 mx-auto">
                    <button type="submit" name="preview_discounts" class="btn btn-info btn-lg mb-2">
                        <i class="fas fa-eye me-1"></i> معاينة التحديثات
                    </button>
                    <button type="submit" name="update_discounts" class="btn btn-primary btn-lg">
                        <i class="fas fa-sync-alt me-1"></i> تحديث توزيع الخصومات مباشرة
                    </button>
                </div>
            </form>
            <?php else: ?>
            <form method="post" action="">
                <div class="alert alert-warning">
                    <p><strong>وضع المعاينة:</strong> يمكنك مراجعة التغييرات أدناه قبل تطبيقها على قاعدة البيانات.</p>
                </div>
                <div class="d-grid gap-2 col-md-6 mx-auto">
                    <button type="submit" name="confirm_update" class="btn btn-success btn-lg mb-2">
                        <i class="fas fa-check me-1"></i> تأكيد التحديثات
                    </button>
                    <button type="submit" name="cancel_preview" class="btn btn-secondary btn-lg">
                        <i class="fas fa-times me-1"></i> إلغاء
                    </button>
                </div>
            </form>
            <?php endif; ?>

            <?php if (($updated || $previewMode) && $updateResult): ?>
            <div class="mt-4">
                <h3><?php echo $previewMode ? 'معاينة التحديثات' : 'نتيجة التحديث'; ?></h3>
                <div class="border p-3 bg-light">
                    <?php echo $updateResult; ?>
                </div>
            </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<?php
// إدراج قالب الذيل
include_once __DIR__ . '/../../includes/templates/footer.php';
?>
