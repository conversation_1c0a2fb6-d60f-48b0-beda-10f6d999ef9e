<?php

/**

 * صفحة نقطة البيع (POS)

 * تتيح إنشاء فواتير جديدة وإضافة الخدمات والمنتجات إليها

 */



// تعريف ثابت لمنع الوصول المباشر للملفات المرتبطة

define('BASEPATH', true);



// استدعاء ملفات التهيئة

require_once __DIR__ . '/../../config/init.php';



// التحقق من تسجيل الدخول

redirectIfNotLoggedIn();



// التحقق من الصلاحيات

if (!hasPermission('invoices_create')) {

    $_SESSION['error_message'] = 'ليس لديك صلاحية للوصول إلى نقطة البيع';

    header('Location: ' . BASE_URL . 'pages/dashboard.php');

    exit;

}



// إنشاء كائنات النماذج المطلوبة

$customerModel = new Customer($db);

$employeeModel = new Employee($db);

$serviceModel = new Service($db);

$productModel = new Product($db);

$branchModel = new Branch($db);

$invoiceModel = new Invoice($db);

$inventoryModel = new Inventory($db);

$promotionModel = new Promotion($db);



// التحقق من صلاحيات المستخدم

$isAdmin = hasPermission('admin_access');



// الحصول على الفرع الحالي (فرع المستخدم أو الفرع المحدد)

$currentBranchId = $_SESSION['user_branch_id'] ?? null;



// إذا كان المستخدم مديراً، يمكنه اختيار الفرع

if ($isAdmin) {

    if (isset($_GET['branch_id'])) {

        $currentBranchId = intval($_GET['branch_id']);

    } elseif ($currentBranchId === null) {

        // إذا كان المستخدم أدمن ولم يتم تحديد فرع، استخدم أول فرع نشط

        $firstBranch = $branchModel->getFirstBranch();

        if ($firstBranch) {

            $currentBranchId = $firstBranch['id'];

        }

    }

}



// الحصول على الفروع المتاحة (للمدير فقط)

$branches = [];

if ($isAdmin) {

    $branches = $branchModel->getBranches(['is_active' => 1]);

}

// التحقق من وجود نهاية يوم مفتوحة للفرع الحالي

$db->prepare("SELECT id FROM end_days

              WHERE branch_id = :branch_id

              AND closed_at IS NULL");

$db->bind(':branch_id', $currentBranchId);

$openEndDay = $db->fetch();



// إذا لم يكن هناك نهاية يوم مفتوحة، عرض رسالة خطأ

$hasOpenEndDay = !empty($openEndDay);

$endDayId = $hasOpenEndDay ? $openEndDay['id'] : null;



// استرجاع إعدادات النظام

$settingsModel = new Settings($db);



// الحصول على معدل الضريبة من الإعدادات

$taxRate = floatval($settingsModel->get('invoice_tax_rate', 15)) / 100; // تحويل النسبة المئوية إلى كسر عشري



// الحصول على العملة ورمزها من الإعدادات

$currency = $settingsModel->get('system_currency', 'ريال سعودي');

$currencySymbol = $settingsModel->get('system_currency_symbol', 'ر.س');

$settings = new Settings($db);

$taxSettings = $settings->getByKey('tax_rate');

if ($taxSettings && !empty($taxSettings['setting_value'])) {

    $taxRate = floatval($taxSettings['setting_value']);

}



// الحصول على الموظفين النشطين في الفرع الحالي

$employees = $employeeModel->getEmployees([

    'branch_id' => $currentBranchId,

    'is_active' => 1

]);



// الحصول على الخدمات المتاحة في الفرع الحالي

$services = $serviceModel->getServices([

    'branch_id' => $currentBranchId,

    'is_active' => 1

]);



// الحصول على المنتجات المتاحة للبيع في الفرع الحالي

$products = $productModel->getProducts([

    'branch_id' => $currentBranchId,

    'is_active' => 1,

    'is_for_sale' => 1

]);



// إضافة معلومات المخزون لكل منتج - هذه الخطوة غير ضرورية الآن لأننا نجلب المخزون مباشرة في استعلام المنتجات

// foreach ($products as &$product) {

//    $stockInfo = $inventoryModel->getProductStock($product['id'], $currentBranchId);

//    $product['current_stock'] = is_array($stockInfo) ? $stockInfo['quantity'] : ($stockInfo ?? 0);

// }



// التنويعات المتاحة للخدمات

$serviceCategories = $serviceModel->getServiceCategories();



// استرجاع آخر رقم فاتورة لإنشاء الرقم الجديد

$lastInvoiceNumber = null;

$recentInvoices = $invoiceModel->getInvoices(['limit' => 1]);

if (!empty($recentInvoices)) {

    $lastInvoiceNumber = $recentInvoices[0]['invoice_number'];

}



// دالة لإنشاء رقم فاتورة جديد





// توليد رقم فاتورة جديد

$newInvoiceNumber = generateInvoiceNumber($lastInvoiceNumber);



// إدراج قالب الرأس

include_once __DIR__ . '/../../includes/templates/header.php';

?>



<!-- CSS مخصص لصفحة نقطة البيع -->

<style>

    .pos-container {

        display: flex;

        flex-wrap: wrap;

        gap: 20px;

    }



    .pos-items {

        flex: 1;

        min-width: 300px;

    }



    .pos-invoice {

        flex: 1;

        min-width: 350px;

    }



    .category-tabs {

        margin-bottom: 20px;

    }



    .item-card {

        cursor: pointer;

        transition: all 0.3s;

        margin-bottom: 10px;

    }



    .item-card:hover:not(.bg-light) {

        transform: translateY(-3px);

        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);

    }



    .invoice-items {

        max-height: 400px;

        overflow-y: auto;

    }



    .invoice-totals {

        margin-top: 15px;

        border-top: 1px solid #ddd;

        padding-top: 15px;

    }



    .quick-actions {

        display: flex;

        gap: 10px;

        margin-bottom: 15px;

    }



    .quantity-control {

        display: flex;

        align-items: center;

    }



    .quantity-control button {

        width: 30px;

    }



    .quantity-control input {

        width: 50px;

        text-align: center;

    }



    .selected-row {

        background-color: #f8f9fa;

    }



    #customerSearchResults {

        position: absolute;

        z-index: 1000;

        width: 100%;

        max-height: 200px;

        overflow-y: auto;

        background: white;

        border: 1px solid #ddd;

        border-radius: 4px;

        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);

        display: none;

    }



    #customerSearchResults .result-item {

        padding: 8px 12px;

        cursor: pointer;

        border-bottom: 1px solid #eee;

    }



    #customerSearchResults .result-item:hover {

        background-color: #f5f5f5;

    }



    .service-categories {

        overflow-x: auto;

        white-space: nowrap;

        padding-bottom: 5px;

    }



    .card.bg-light {

        opacity: 0.6;

    }



    .card.bg-light .badge {

        opacity: 1;

    }

</style>



<!-- محتوى الصفحة -->

<div class="container-fluid">

    <div class="d-flex justify-content-between align-items-center mb-3">

        <div>

            <h1 class="mt-4">نقطة البيع</h1>

            <nav aria-label="breadcrumb">

                <ol class="breadcrumb">

                    <li class="breadcrumb-item"><a href="<?php echo BASE_URL; ?>pages/dashboard.php">الرئيسية</a></li>

                    <li class="breadcrumb-item active" aria-current="page">نقطة البيع</li>

                </ol>

            </nav>

        </div>



        <?php if ($isAdmin): ?>

        <!-- اختيار الفرع للمدير -->

        <div class="branch-selector">

            <form id="branchSelectorForm" method="get" class="d-flex align-items-center">

                <label for="branch_id" class="me-2">الفرع:</label>

                <select id="branch_id" name="branch_id" class="form-select" onchange="this.form.submit()">

                    <?php foreach ($branches as $branch): ?>

                    <option value="<?php echo $branch['id']; ?>" <?php echo ($currentBranchId == $branch['id']) ? 'selected' : ''; ?>>

                        <?php echo htmlspecialchars($branch['name']); ?>

                    </option>

                    <?php endforeach; ?>

                </select>

            </form>

        </div>

        <?php else: ?>

        <!-- عرض اسم الفرع للموظفين -->

        <div class="branch-info">

            <span class="badge bg-primary">الفرع: <?php

                $branchName = "";



                // استعلام مباشر للحصول على اسم الفرع

                if (!empty($currentBranchId)) {

                    $db->prepare("SELECT name FROM branches WHERE id = :id");

                    $db->bind(':id', $currentBranchId);

                    $currentBranch = $db->fetch();

                    $branchName = $currentBranch ? $currentBranch['name'] : 'غير محدد';

                } else {

                    $branchName = 'غير محدد';

                }



                echo htmlspecialchars($branchName);

            ?></span>

        </div>

        <?php endif; ?>

    </div>



    <?php include_once __DIR__ . '/../../includes/templates/alerts.php'; ?>



    <?php if (!$hasOpenEndDay): ?>

    <!-- رسالة تحذير عند عدم وجود نهاية يوم مفتوحة -->

    <div class="alert alert-danger" role="alert">

        <h4 class="alert-heading">لا يمكن إنشاء فواتير!</h4>

        <p>لا يوجد يوم عمل مفتوح للفرع الحالي. يجب بدء يوم العمل أولاً قبل إنشاء فواتير جديدة.</p>

        <hr>

        <p class="mb-0">يرجى الانتقال إلى <a href="<?php echo BASE_URL; ?>pages/endday/index.php" class="alert-link">صفحة نهاية اليوم</a> لبدء يوم العمل.</p>

    </div>

    <?php endif; ?>



    <!-- واجهة نقطة البيع -->

    <div class="card mb-4">

        <div class="card-body">

            <div class="pos-container">

                <!-- قسم عناصر الفاتورة (الخدمات والمنتجات) -->

                <div class="pos-items card">

                    <div class="card-header">

                        <ul class="nav nav-tabs card-header-tabs" id="itemTabs" role="tablist">

                            <li class="nav-item" role="presentation">

                                <button class="nav-link active" id="services-tab" data-bs-toggle="tab" data-bs-target="#services" type="button" role="tab" aria-controls="services" aria-selected="true">الخدمات</button>

                            </li>

                            <li class="nav-item" role="presentation">

                                <button class="nav-link" id="products-tab" data-bs-toggle="tab" data-bs-target="#products" type="button" role="tab" aria-controls="products" aria-selected="false">المنتجات</button>

                            </li>

                        </ul>

                    </div>

                    <div class="card-body">

                        <div class="tab-content" id="itemTabsContent">

                                <!-- تبويب الخدمات -->

                            <div class="tab-pane fade show active" id="services" role="tabpanel" aria-labelledby="services-tab">

                                <div class="input-group mb-3">

                                    <input type="text" class="form-control" id="searchService" placeholder="بحث عن خدمة...">

                                    <button class="btn btn-outline-secondary" type="button"><i class="fas fa-search"></i></button>

                                </div>



                                <!-- تصنيف الخدمات -->

                                <div class="mb-3 service-categories">

                                    <div class="btn-group w-100">

                                        <button type="button" class="btn btn-sm btn-outline-primary active category-filter" data-category="all">الكل</button>

                                        <?php foreach ($serviceCategories as $category): ?>

                                        <button type="button" class="btn btn-sm btn-outline-primary category-filter" data-category="<?php echo $category['id']; ?>"><?php echo $category['name']; ?></button>

                                        <?php endforeach; ?>

                                    </div>

                                </div>





                                <div class="row" id="servicesContainer">

                                    <?php foreach ($services as $service): ?>

                                    <div class="col-md-6 col-lg-4 service-item" data-id="<?php echo $service['id']; ?>" data-name="<?php echo htmlspecialchars($service['name']); ?>" data-price="<?php echo $service['price']; ?>" data-category="<?php echo $service['category_id'] ?? 'none'; ?>">

                                        <div class="card item-card" data-id="<?php echo $service['id']; ?>" data-type="service" data-name="<?php echo htmlspecialchars($service['name']); ?>" data-price="<?php echo $service['price']; ?>">

                                            <div class="card-body">

                                                <h5 class="card-title"><?php echo htmlspecialchars($service['name']); ?></h5>

                                                <p class="card-text"><?php echo number_format($service['price'], 2); ?> <?php echo $currencySymbol; ?></p>

                                                <p class="text-muted small"><?php echo $service['duration']; ?> دقيقة</p>

                                            </div>

                                        </div>

                                    </div>

                                    <?php endforeach; ?>

                                </div>



                                <?php if (empty($services)): ?>

                                <div class="alert alert-info">لا توجد خدمات متاحة حال<lemma></lemma></div>

                                <?php endif; ?>

                            </div>



                            <!-- تبويب المنتجات -->

                            <div class="tab-pane fade" id="products" role="tabpanel" aria-labelledby="products-tab">

                                <div class="input-group mb-3">

                                    <input type="text" class="form-control" id="searchProduct" placeholder="بحث عن منتج...">

                                    <button class="btn btn-outline-secondary" type="button"><i class="fas fa-search"></i></button>

                                </div>



                                <div class="row" id="productsContainer">

                                    <?php foreach ($products as $product): ?>

                                    <div class="col-md-6 col-lg-4 product-item">

                                        <div class="card item-card <?php echo $product['current_stock'] <= 0 ? 'bg-light text-muted' : ''; ?>"

                                             data-id="<?php echo $product['id']; ?>"

                                             data-type="product"

                                             data-name="<?php echo htmlspecialchars($product['name']); ?>"

                                             data-price="<?php echo $product['price']; ?>">

                                            <div class="card-body">

                                                <h5 class="card-title"><?php echo htmlspecialchars($product['name']); ?></h5>

                                                <p class="card-text"><?php echo number_format($product['price'], 2); ?> <?php echo $currencySymbol; ?></p>

                                                <p class="text-muted small mb-0">

                                                    المخزون:

                                                    <span class="<?php echo $product['current_stock'] <= ($product['min_quantity'] ?? 5) ? 'text-danger' : 'text-success'; ?>">

                                                        <?php echo $product['current_stock']; ?>

                                                    </span>

                                                </p>

                                                <?php if ($product['current_stock'] <= 0): ?>

                                                <div class="badge bg-danger mt-2">نفذت الكمية</div>

                                                <?php endif; ?>

                                            </div>

                                        </div>

                                    </div>

                                    <?php endforeach; ?>

                                </div>



                                <?php if (empty($products)): ?>

                                <div class="alert alert-info">لا توجد منتجات متاحة حال<|im_start|></div>

                                <?php endif; ?>

                            </div>

                        </div>

                    </div>

                </div>



                <!-- قسم الفاتورة -->

                <div class="pos-invoice card">

                    <div class="card-header">

                        <h5>تفاصيل الفاتورة <span class="badge bg-primary"><?php echo $newInvoiceNumber; ?></span></h5>

                    </div>

                    <div class="card-body">

                        <form id="invoiceForm" method="post" action="<?php echo API_URL; ?>invoices.php?action=create">

                            <input type="hidden" name="invoice_number" id="invoiceNumber" value="<?php echo $newInvoiceNumber; ?>">

                            <input type="hidden" name="branch_id" id="branchId" value="<?php echo $currentBranchId; ?>">

                            <input type="hidden" name="items" id="invoiceItems" value="">



                            <!-- بيانات العميل والموظف -->

                            <div class="row mb-3">

                                <div class="col-md-6 position-relative">

                                    <label for="customerSearch" class="form-label">العميل</label>

                                    <div class="input-group">

                                        <input type="text" class="form-control" id="customerSearch" placeholder="بحث عن عميل...">

                                        <button type="button" class="btn btn-outline-secondary" data-bs-toggle="modal" data-bs-target="#addCustomerModal"><i class="fas fa-plus"></i></button>

                                    </div>

                                    <input type="hidden" name="customer_id" id="customerId">

                                    <div id="customerSearchResults"></div>

                                </div>

                                <div class="col-md-6">

                                    <label for="employeeId" class="form-label">الحلاق / الموظف</label>

                                    <select class="form-select" id="employeeId" name="employee_id">

                                        <option value="">اختر الموظف</option>

                                        <?php foreach ($employees as $employee): ?>

                                        <option value="<?php echo $employee['id']; ?>"><?php echo $employee['name']; ?></option>

                                        <?php endforeach; ?>

                                    </select>

                                </div>

                            </div>



                            <!-- إضافة الأصناف -->

                            <div class="invoice-items mb-3">

                                <table class="table table-sm" id="invoiceItemsTable">

                                    <thead>

                                        <tr>

                                            <th>الصنف</th>

                                            <th>النوع</th>

                                            <th>الكمية</th>

                                            <th>السعر</th>

                                            <th>الإجمالي</th>

                                            <th></th>

                                        </tr>

                                    </thead>

                                    <tbody>

                                        <!-- سيتم إضافة الأصناف هنا ديناميكically -->

                                    </tbody>

                                    <tfoot>

                                        <tr class="table-active">

                                            <td colspan="4" class="text-left"><strong>المجموع قبل الضريبة والخصم</strong></td>

                                            <td colspan="2"><strong id="subtotalDisplay">0.00</strong></td>

                                        </tr>

                                    </tfoot>

                                </table>

                            </div>



                            <!-- الإجماليات والدفع -->

                            <div class="invoice-totals mb-3">

                                <div class="row">

                                    <div class="col-6">

                                        <div class="mb-2">

                                            <label class="form-label">المجموع</label>

                                            <input type="text" class="form-control" id="totalAmount" name="total_amount" value="0.00" readonly>

                                        </div>

                                        <div class="mb-2">

                                            <label class="form-label">الخصم</label>

                                            <div class="input-group">

                                                <input type="number" class="form-control" id="discountAmount" name="discount_amount" value="0" min="0" step="0.01">

                                                <select class="form-select" id="discountType" name="discount_type">

                                                    <option value="amount"><?php echo $currencySymbol; ?></option>

                                                    <option value="percentage">%</option>

                                                </select>

                                                <button type="button" class="btn btn-outline-success" id="showPromotionsBtn" data-bs-toggle="modal" data-bs-target="#promotionsModal">

                                                    <i class="fas fa-tags"></i> العروض

                                                </button>

                                            </div>

                                        </div>



                                        <div class="mb-2">

                                            <label class="form-label">كود الترويج</label>

                                            <div class="input-group">

                                                <input type="text" class="form-control" id="promoCode" name="promo_code" placeholder="أدخل كود الترويج">

                                                <button type="button" class="btn btn-outline-success" id="applyPromoCodeBtn">

                                                    <i class="fas fa-check"></i> تطبيق

                                                </button>

                                            </div>

                                            <div id="promoCodeMessage" class="small mt-1" style="display: none;"></div>

                                            <input type="hidden" id="appliedPromoCodeId" name="promo_code_id" value="">

                                        </div>



                                        <!-- إشعار العروض المتاحة -->

                                        <div id="availablePromotionsAlert" class="alert alert-info mt-2" style="display: none;">

                                            <i class="fas fa-gift me-2"></i> <span id="promotionsMessage"></span>

                                            <button type="button" class="btn btn-sm btn-info ms-2" data-bs-toggle="modal" data-bs-target="#promotionsModal">عرض التفاصيل</button>

                                        </div>

                                    </div>

                                    <div class="col-6">

                                        <div class="mb-2">

                                            <label class="form-label">الضريبة (<?php echo $taxRate * 100; ?>%)</label>

                                            <input type="text" class="form-control" id="taxAmount" name="tax_amount" value="0.00" readonly>

                                        </div>

                                        <div class="mb-2">

                                            <label class="form-label">الإجمالي النهائي</label>

                                            <input type="text" class="form-control" id="finalAmount" name="final_amount" value="0.00" readonly>

                                        </div>

                                    </div>

                                </div>



                                <div class="row mt-3">

                                    <div class="col-6">

                                        <label class="form-label">طريقة الدفع</label>

                                        <select class="form-select" id="paymentMethod" name="payment_method">

                                            <option value="cash">نقدي</option>

                                            <option value="card">بطاقة</option>

                                            <option value="other">أخرى</option>

                                        </select>

                                    </div>

                                    <div class="col-6">

                                        <label class="form-label">حالة الدفع</label>

                                        <select class="form-select" id="paymentStatus" name="payment_status">

                                            <option value="paid">مدفوع</option>

                                            <option value="unpaid">غير مدفوع</option>

                                            <option value="partial">مدفوع جزئي</option>

                                        </select>

                                    </div>

                                </div>



                                <!-- حقل المبلغ المدفوع للدفع الجزئي -->

                                <div class="row mt-3" id="paidAmountContainer" style="display: none;">

                                    <div class="col-12">

                                        <label class="form-label">المبلغ المدفوع</label>

                                        <input type="number" class="form-control" id="paidAmount" name="paid_amount" value="0" min="0" step="0.01">

                                        <small class="form-text text-muted">أدخل المبلغ المدفوع فعلياً من قيمة الفاتورة</small>

                                    </div>

                                </div>



                                <div class="mb-2 mt-3">

                                    <label class="form-label">ملاحظات</label>

                                    <textarea class="form-control" id="notes" name="notes" rows="2"></textarea>

                                </div>

                            </div>



                            <!-- أزرار العمليات -->

                            <div class="d-flex justify-content-between">

                                <button type="button" class="btn btn-danger" id="clearInvoice" <?php echo !$hasOpenEndDay ? 'disabled' : ''; ?>><i class="fas fa-trash-alt"></i> إلغاء الفاتورة</button>

                                <div>

                                    <button type="submit" class="btn btn-success" id="saveInvoice" <?php echo !$hasOpenEndDay ? 'disabled' : ''; ?>><i class="fas fa-save"></i> حفظ وطباعة</button>

                                    <button type="button" class="btn btn-primary" id="saveDraft" <?php echo !$hasOpenEndDay ? 'disabled' : ''; ?>><i class="fas fa-save"></i> حفظ كمسودة</button>

                                </div>

                            </div>



                            <!-- حقول خفية لتخزين العناصر -->

                            <input type="hidden" id="invoiceItems" name="invoice_items" value="[]">

                            <input type="hidden" id="items" name="items" value="[]">

                            <input type="hidden" id="appliedPromoCodeId" name="applied_promo_code_id" value="">

                            <input type="hidden" id="appliedPromotionId" name="applied_promotion_id" value="">

                        </form>

                    </div>

                </div>

            </div>

        </div>

    </div>

</div>



<!-- نافذة منبثقة لإضافة عميل جديد -->

<div class="modal fade" id="addCustomerModal" tabindex="-1" aria-labelledby="addCustomerModalLabel" aria-hidden="true">

    <div class="modal-dialog">

        <div class="modal-content">

            <div class="modal-header">

                <h5 class="modal-title" id="addCustomerModalLabel">إضافة عميل جديد</h5>

                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>

            </div>

            <div class="modal-body">

                <form id="addCustomerForm">

                    <div class="mb-3">

                        <label for="customerName" class="form-label">اسم العميل</label>

                        <input type="text" class="form-control" id="customerName" name="name" required>

                    </div>

                    <div class="mb-3">

                        <label for="customerPhone" class="form-label">رقم الجوال</label>

                        <input type="text" class="form-control" id="customerPhone" name="phone" required>

                    </div>

                    <div class="mb-3">

                        <label for="customerEmail" class="form-label">البريد الإلكتروني</label>

                        <input type="email" class="form-control" id="customerEmail" name="email">

                    </div>

                    <div class="mb-3">

                        <label for="customerAddress" class="form-label">العنوان</label>

                        <input type="text" class="form-control" id="customerAddress" name="address">

                    </div>

                    <div class="mb-3">

                        <label for="customerNotes" class="form-label">ملاحظات</label>

                        <textarea class="form-control" id="customerNotes" name="notes"></textarea>

                    </div>

                    <input type="hidden" name="branch_id" value="<?php echo $currentBranchId; ?>">

                </form>

            </div>

            <div class="modal-footer">

                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>

                <button type="button" class="btn btn-primary" id="saveCustomer">حفظ</button>

            </div>

        </div>

    </div>

</div>



<!-- نافذة منبثقة لعرض تفاصيل كود الترويج -->

<div class="modal fade" id="promoCodeDetailsModal" tabindex="-1" aria-labelledby="promoCodeDetailsModalLabel" aria-hidden="true">

    <div class="modal-dialog">

        <div class="modal-content">

            <div class="modal-header">

                <h5 class="modal-title" id="promoCodeDetailsModalLabel">تفاصيل كود الترويج</h5>

                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>

            </div>

            <div class="modal-body">

                <div class="row mb-3">

                    <div class="col-md-6">

                        <h6>الكود</h6>

                        <p id="promoCodeDetailsCode" class="badge bg-secondary"></p>

                    </div>

                    <div class="col-md-6">

                        <h6>اسم العرض</h6>

                        <p id="promoCodeDetailsName"></p>

                    </div>

                </div>

                <div class="row mb-3">

                    <div class="col-md-12">

                        <h6>الوصف</h6>

                        <p id="promoCodeDetailsDescription" class="text-muted"></p>

                    </div>

                </div>

                <div class="row mb-3">

                    <div class="col-md-6">

                        <h6>نوع الخصم</h6>

                        <p id="promoCodeDetailsDiscountType"></p>

                    </div>

                    <div class="col-md-6">

                        <h6>قيمة الخصم</h6>

                        <p id="promoCodeDetailsDiscountValue"></p>

                    </div>

                </div>

                <div class="row mb-3">

                    <div class="col-md-6">

                        <h6>الحد الأدنى للفاتورة</h6>

                        <p id="promoCodeDetailsMinAmount"></p>

                    </div>

                    <div class="col-md-6">

                        <h6>الحد الأقصى للفاتورة</h6>

                        <p id="promoCodeDetailsMaxAmount"></p>

                    </div>

                </div>

                <div class="row mb-3">

                    <div class="col-md-6">

                        <h6>عدد مرات الاستخدام</h6>

                        <p id="promoCodeDetailsUsage"></p>

                    </div>

                    <div class="col-md-6">

                        <h6>نقاط الولاء المطلوبة</h6>

                        <p id="promoCodeDetailsLoyaltyPoints"></p>

                    </div>

                </div>

                <div class="row mb-3">

                    <div class="col-md-12">

                        <h6>الصلاحية</h6>

                        <p id="promoCodeDetailsValidity"></p>

                    </div>

                </div>

            </div>

            <div class="modal-footer">

                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>

                <button type="button" class="btn btn-primary" id="confirmApplyPromoCode">تطبيق الكود</button>

            </div>

        </div>

    </div>

</div>



<!-- نافذة منبثقة لتحديد الموظف للخدمة -->

<div class="modal fade" id="selectEmployeeModal" tabindex="-1" aria-labelledby="selectEmployeeModalLabel" aria-hidden="true">

    <div class="modal-dialog">

        <div class="modal-content">

            <div class="modal-header">

                <h5 class="modal-title" id="selectEmployeeModalLabel">اختيار الحلاق للخدمة</h5>

                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>

            </div>

            <div class="modal-body">

                <div class="mb-3">

                    <label class="form-label">الخدمة</label>

                    <input type="text" class="form-control" id="modalServiceName" readonly>

                </div>

                <div class="mb-3">

                    <label for="modalEmployeeId" class="form-label">اختر الحلاق</label>

                    <select class="form-select" id="modalEmployeeId">

                        <option value="">اختر الحلاق</option>

                        <?php foreach ($employees as $employee): ?>

                        <option value="<?php echo $employee['id']; ?>"><?php echo $employee['name']; ?></option>

                        <?php endforeach; ?>

                    </select>

                </div>

                <input type="hidden" id="modalItemIndex">

                <input type="hidden" id="modalServiceId">

                <input type="hidden" id="modalServicePrice">

            </div>

            <div class="modal-footer">

                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>

                <button type="button" class="btn btn-primary" id="confirmEmployeeSelection">تأكيد</button>

            </div>

        </div>

    </div>

</div>



<?php include_once __DIR__ . '/../../includes/templates/footer.php'; ?>



<!-- سكريبت خاص بصفحة نقطة البيع -->

<script>

    // تعريف المتغيرات العامة

    // استخدام متغير API_URL المعرف مسبقًا في header.php

    const TAX_RATE = <?php echo $taxRate; ?>;

    const BRANCH_ID = <?php echo $currentBranchId; ?>;

    const CURRENCY_SYMBOL = '<?php echo $currencySymbol; ?>';

    const CURRENCY = '<?php echo $currency; ?>';

    const HAS_OPEN_END_DAY = <?php echo $hasOpenEndDay ? 'true' : 'false'; ?>;

    const userRole = '<?php echo $_SESSION['user_role']; ?>'; // دور المستخدم الحالي

    const isAdmin = userRole === '<?php echo ROLE_ADMIN; ?>'; // هل المستخدم مدير



    // تعطيل التفاعل مع العناصر إذا لم يكن هناك يوم مفتوح

    if (!HAS_OPEN_END_DAY) {

        // تعطيل النقر على العناصر

        $(document).on('click', '.item-card', function(e) {

            e.preventDefault();

            e.stopPropagation();

            alert('لا يمكن إضافة عناصر إلى الفاتورة. يجب بدء يوم العمل أولاً.');

            return false;

        });

    }



    // مصفوفة لتخزين عناصر الفاتورة

    let invoiceItems = [];

    // التحقق من وجود معرف فاتورة أو معرف موعد أو معرف عميل في الرابط

    const urlParams = new URLSearchParams(window.location.search);

    const invoiceId = urlParams.get('invoice_id');

    const appointmentId = urlParams.get('appointment_id');

    const customerId = urlParams.get('customer_id');



    // إذا كان هناك معرف فاتورة، قم بتحميل بيانات الفاتورة للتعديل

    if (invoiceId) {

        // تحميل بيانات الفاتورة للتعديل

        $.ajax({

            url: `${API_URL}invoices.php?action=get_invoice_details`,

            method: 'GET',

            data: { id: invoiceId },

            dataType: 'json',

            success: function(response) {

                if (response.status === 'success') {

                    const invoice = response.invoice;



                    // التحقق من أن يوم الفاتورة لم يتم إغلاقه بعد

                    if (invoice.is_end_day_closed && userRole !== 'admin') {

                        alert('لا يمكن تعديل هذه الفاتورة لأنها مرتبطة بيوم عمل مغلق');

                        window.location.href = 'index.php'; // العودة إلى صفحة نقطة البيع

                        return;

                    }



                    // تعبئة بيانات الفاتورة

                    $('#invoiceNumber').val(invoice.invoice_number);

                    $('#customerId').val(invoice.customer_id);

                    $('#customerSearch').val(invoice.customer_name || '');

                    $('#employeeId').val(invoice.employee_id);

                    $('#paymentMethod').val(invoice.payment_method);

                    $('#paymentStatus').val(invoice.payment_status);

                    $('#discountAmount').val(invoice.discount_amount);

                    $('#discountType').val(invoice.discount_type);

                    $('#notes').val(invoice.notes);



                    // إضافة معرف الفاتورة كحقل مخفي للإشارة إلى أن هذه عملية تعديل

                    if (!$('#editInvoiceId').length) {

                        $('<input>').attr({

                            type: 'hidden',

                            id: 'editInvoiceId',

                            name: 'edit_invoice_id',

                            value: invoiceId

                        }).appendTo('#invoiceForm');

                    } else {

                        $('#editInvoiceId').val(invoiceId);

                    }



                    // تحميل عناصر الفاتورة

                    invoiceItems = [];

                    response.items.forEach(item => {

                        const newItem = {

                            item_id: item.item_id,

                            item_type: item.item_type,

                            name: item.item_type === 'service' ? item.service_name : item.product_name,

                            price: parseFloat(item.price),

                            quantity: parseInt(item.quantity),

                            total: parseFloat(item.total),

                            employee_id: item.employee_id || null

                        };



                        // إضافة معلومات المخزون للمنتجات

                        if (item.item_type === 'product') {

                            // الحصول على المخزون الحالي للمنتج

                            $.ajax({

                                url: `${API_URL}inventory.php?action=get_product_stock`,

                                method: 'GET',

                                data: {

                                    product_id: item.item_id,

                                    branch_id: $('#branchId').val()

                                },

                                dataType: 'json',

                                async: false,

                                success: function(stockResponse) {

                                    if (stockResponse.status === 'success') {

                                        newItem.current_stock = parseInt(stockResponse.quantity) + parseInt(item.quantity);

                                    }

                                }

                            });

                        }



                        invoiceItems.push(newItem);

                    });



                    // عرض العناصر وحساب الإجماليات

                    renderInvoiceItems();

                    calculateTotals();



                    // تغيير نص زر الحفظ

                    $('#saveInvoice').text('تحديث الفاتورة');



                    // إضافة عنوان يشير إلى أن هذه عملية تعديل

                    $('.card-title:first').text('تعديل الفاتورة #' + invoice.invoice_number);

                } else {

                    alert(response.message || 'حدث خطأ أثناء تحميل بيانات الفاتورة');

                    window.location.href = 'index.php'; // العودة إلى صفحة نقطة البيع

                }

            },

            error: function() {

                alert('حدث خطأ في الاتصال بالخادم');

                window.location.href = 'index.php'; // العودة إلى صفحة نقطة البيع

            }

        });

    }

    // إذا كان هناك معرف موعد، قم بتحميل بيانات الموعد لإنشاء فاتورة جديدة

    else if (appointmentId) {

        loadAppointmentData(appointmentId);

    }

    // إذا كان هناك معرف عميل فقط، قم بتحميل بيانات العميل

    else if (customerId) {

        loadCustomerData(customerId);

    }







    // معالج حدث النقر على زر حفظ وطباعة

    $('#saveInvoice').on('click', function() {

        // التحقق من وجود عناصر في الفاتورة

        if (invoiceItems.length === 0) {

            alert('لا يمكن حفظ فاتورة بدون عناصر');

            return;

        }



        // التحقق من تعيين موظف لكل الخدمات إذا كانت هناك عناصر

        if (invoiceItems.length > 0) {

            let allServicesHaveEmployee = true;

            for (let i = 0; i < invoiceItems.length; i++) {

                if (invoiceItems[i].item_type === 'service' && !invoiceItems[i].employee_id) {

                    allServicesHaveEmployee = false;

                    alert('يجب تحديد الحلاق لجميع الخدمات');

                    break;

                }

            }



            if (!allServicesHaveEmployee) {

                return;

            }

        }



        // تعيين حالة الدفع إلى مدفوع

        $('#paymentStatus').val('paid');



        // تضمين العناصر في النموذج

        $('#invoiceItems').val(JSON.stringify(invoiceItems));

        $('#items').val(JSON.stringify(invoiceItems));



        // تحديد ما إذا كانت هذه عملية تعديل أم إنشاء جديد

        const isEdit = $('#editInvoiceId').length > 0 && $('#editInvoiceId').val() !== '';

        const action = isEdit ? 'update' : 'create';



        // إرسال النموذج باستخدام AJAX مباشرة

        let formData = $('#invoiceForm').serialize();



        // إضافة معلومات كود الترويج إذا كان مطبقًا

        const promoCodeId = $('#appliedPromoCodeId').val();

        if (promoCodeId) {

            formData += `&promo_code_id=${promoCodeId}&promo_code=${encodeURIComponent($('#promoCode').val().trim())}`;

        }



        $.ajax({

            url: `${API_URL}invoices.php?action=${action}`,

            method: 'POST',

            data: formData,

            dataType: 'json',

            success: function(response) {

                if (response.status === 'success') {

                    // عرض رسالة نجاح



                    // تعيين معرف الفاتورة الحالية لاستخدامه في الإشعارات

                    window.currentInvoiceId = response.invoice_id;



                    // إطلاق حدث إنشاء الفاتورة للاستخدام في إشعارات الواتساب

                    document.dispatchEvent(new CustomEvent('invoiceCreated', {

                        detail: {

                            invoiceId: response.invoice_id

                        }

                    }));



                    // تسجيل استخدام العرض الترويجي إذا كان مطبقًا

                    const promotionId = $('#appliedPromotionId').val();

                    if (promotionId) {

                        const customerId = $('#customerId').val();

                        const discountAmount = parseFloat($('#discountAmount').val()) || 0;



                        // استدعاء API لتسجيل استخدام العرض

                        $.ajax({

                            url: `${API_URL}promotions.php?action=record_usage`,

                            method: 'POST',

                            data: {

                                promotion_id: promotionId,

                                invoice_id: response.invoice_id,

                                customer_id: customerId || null,

                                discount_amount: discountAmount

                            },

                            dataType: 'json',

                            success: function(usageResponse) {

                                console.log('Promotion usage recorded:', usageResponse);

                            },

                            error: function(xhr, status, error) {

                                console.error('Error recording promotion usage:', error);

                            }

                        });

                    }



                    // فقط طباعة الفاتورة عند الإنشاء، وليس عند التعديل

                    if (!isEdit) {

                        // فتح نافذة منبثقة للطباعة

                        const printWindow = window.open(`print.php?id=${response.invoice_id}&type=thermal&size=80mm&mode=auto`, '_blank', 'width=400,height=600,toolbar=0,location=0,menubar=0');



                        // التركيز على النافذة المنبثقة

                        if (printWindow) {

                            printWindow.focus();

                        }

                    } else {

                        // إظهار رسالة نجاح عند التعديل

                        alert('تم تحديث الفاتورة بنجاح');

                        

                        // الرجوع إلى صفحة الفواتير

                        window.location.href = '<?php echo BASE_URL; ?>pages/invoices/index.php';

                        return;

                    }



                    // ملاحظة: تم إزالة حدث invoicePrinted لتجنب تكرار إرسال الإشعارات

                    // الإشعارات يتم إرسالها من خلال حدث invoiceCreated فقط



                    // إعادة تعيين الفاتورة

                    invoiceItems = [];

                    renderInvoiceItems();

                    calculateTotals();

                    $('#customerId').val('');

                    $('#customerSearch').val('');

                    $('#employeeId').val('');

                    $('#notes').val('');

                    $('#discountAmount').val('0');

                    $('#editInvoiceId').remove();



                    // إعادة تعيين كود الترويج

                    $('#promoCode').val('').prop('disabled', false);

                    $('#applyPromoCodeBtn').prop('disabled', false);

                    $('#appliedPromoCodeId').val('');

                    $('#promoCodeMessage').hide();



                    // إعادة تعيين عنوان الصفحة

                    $('.card-title:first').text('إنشاء فاتورة جديدة');

                    $('#saveInvoice').text('حفظ الفاتورة');



                    // إعادة توجيه المستخدم إلى صفحة نقطة البيع الرئيسية

                    if (isEdit) {

                        window.location.href = 'index.php';

                    } else {

                        // تحديث رقم الفاتورة الجديد

                        const lastInvoiceId = response.invoice_id;

                        $.get(`${API_URL}invoices.php?action=list&limit=1`, function(response) {

                            if (response.status === 'success' && response.invoices.length > 0) {

                                const lastInvoiceNumber = response.invoices[0].invoice_number;

                                $('#invoiceNumber').val(generateNewInvoiceNumber(lastInvoiceNumber));

                            }

                        });

                    }

                } else {

                    alert(response.message || 'حدث خطأ أثناء حفظ الفاتورة');

                }

            },

            error: function(xhr) {

                try {

                    const response = JSON.parse(xhr.responseText);

                    alert(response.message || 'حدث خطأ في الاتصال بالخادم');

                } catch (e) {

                    alert('حدث خطأ في الاتصال بالخادم');

                }

            }

        });

    });



    // معالج حدث النقر على زر حفظ كمسودة

    $('#saveDraft').on('click', function() {

        // التحقق من وجود عناصر في الفاتورة

        if (invoiceItems.length === 0) {

            alert('لا يمكن حفظ فاتورة بدون عناصر');

            return;

        }



        // التحقق من تعيين موظف لكل الخدمات إذا كانت هناك عناصر

        if (invoiceItems.length > 0) {

            let allServicesHaveEmployee = true;

            for (let i = 0; i < invoiceItems.length; i++) {

                if (invoiceItems[i].item_type === 'service' && !invoiceItems[i].employee_id) {

                    allServicesHaveEmployee = false;

                    alert('يجب تحديد الحلاق لجميع الخدمات');

                    break;

                }

            }



            if (!allServicesHaveEmployee) {

                return;

            }

        }



        // تعيين حالة الدفع إلى غير مدفوع

        $('#paymentStatus').val('unpaid');



        // تضمين العناصر في النموذج

        $('#invoiceItems').val(JSON.stringify(invoiceItems));

        $('#items').val(JSON.stringify(invoiceItems));



        // تحديد ما إذا كانت هذه عملية تعديل أم إنشاء جديد

        const isEdit = $('#editInvoiceId').length > 0 && $('#editInvoiceId').val() !== '';

        const action = isEdit ? 'update' : 'create';



        // إرسال النموذج باستخدام AJAX مباشرة

        let formData = $('#invoiceForm').serialize();



        // إضافة معلومات كود الترويج إذا كان مطبقًا

        const promoCodeId = $('#appliedPromoCodeId').val();

        if (promoCodeId) {

            formData += `&promo_code_id=${promoCodeId}&promo_code=${encodeURIComponent($('#promoCode').val().trim())}`;

        }



        $.ajax({

            url: `${API_URL}invoices.php?action=${action}`,

            method: 'POST',

            data: formData,

            dataType: 'json',

            success: function(response) {

                if (response.status === 'success') {

                    // عرض رسالة نجاح

                    alert('تم حفظ الفاتورة كمسودة بنجاح');



                    // إعادة تعيين الفاتورة

                    invoiceItems = [];

                    renderInvoiceItems();

                    calculateTotals();

                    $('#customerId').val('');

                    $('#customerSearch').val('');

                    $('#employeeId').val('');

                    $('#notes').val('');

                    $('#discountAmount').val('0');

                    $('#editInvoiceId').remove();



                    // إعادة تعيين كود الترويج

                    $('#promoCode').val('').prop('disabled', false);

                    $('#applyPromoCodeBtn').prop('disabled', false);

                    $('#appliedPromoCodeId').val('');

                    $('#promoCodeMessage').hide();



                    // إعادة تعيين عنوان الصفحة

                    $('.card-title:first').text('إنشاء فاتورة جديدة');

                    $('#saveInvoice').text('حفظ الفاتورة');



                    // تحديث رقم الفاتورة الجديد

                    const lastInvoiceId = response.invoice_id;

                    $.get(`${API_URL}invoices.php?action=list&limit=1`, function(response) {

                        if (response.status === 'success' && response.invoices.length > 0) {

                            const lastInvoiceNumber = response.invoices[0].invoice_number;

                            $('#invoiceNumber').val(generateNewInvoiceNumber(lastInvoiceNumber));

                        }

                    });

                } else {

                    alert(response.message || 'حدث خطأ أثناء حفظ الفاتورة');

                }

            },

            error: function(xhr) {

                try {

                    const response = JSON.parse(xhr.responseText);

                    alert(response.message || 'حدث خطأ في الاتصال بالخادم');

                } catch (e) {

                    alert('حدث خطأ في الاتصال بالخادم');

                }

            }

        });

    });





    $(document).ready(function() {

        // تهيئة عناصر الصفحة

        initializePage();



        // إضافة معالج لمنع السلوك الافتراضي للنموذج

        $('#invoiceForm').on('submit', function(e) {

            // منع السلوك الافتراضي للنموذج

            e.preventDefault();

            // لا نفعل شيئًا هنا لأن الأزرار لديها معالجات خاصة بها

        });

        // معالجة اختيار العنصر (منتج أو خدمة)

        $('#itemSelect').on('change', function() {

            const selectedOption = $(this).find('option:selected');

            const itemType = selectedOption.data('type');

            const itemId = selectedOption.val();



            if (!itemId) return;



            // تحديث حقول العنصر المختار

            $('#itemType').val(itemType);

            $('#itemId').val(itemId);

            $('#itemName').val(selectedOption.text());

            $('#itemPrice').val(selectedOption.data('price'));



            // تمكين زر الإضافة

            $('#addItemBtn').prop('disabled', false);

        });



        // اختيار الموظف للخدمة

        $('#confirmEmployeeSelection').click(function() {

            const employeeId = $('#modalEmployeeId').val();

            const itemIndex = $('#modalItemIndex').val();



            if (employeeId) {

                if (itemIndex !== '' && invoiceItems[itemIndex]) {

                    // تحديث الموظف لعنصر موجود

                    invoiceItems[itemIndex].employee_id = employeeId;

                    renderInvoiceItems();

                    calculateTotals();

                } else {

                    // إضافة عنصر جديد باستخدام البيانات المخزنة مؤقتًا

                    const serviceId = $('#modalServiceId').val();

                    const serviceName = $('#modalServiceName').val();

                    const servicePrice = parseFloat($('#modalServicePrice').val()) || 0;



                    if (serviceId && serviceName && servicePrice > 0) {

                        const newItem = {

                            item_id: serviceId,

                            item_type: 'service',

                            name: serviceName,

                            price: servicePrice,

                            quantity: 1,

                            total: servicePrice,

                            employee_id: employeeId,

                            discount: 0

                        };



                        invoiceItems.push(newItem);

                        renderInvoiceItems();

                        calculateTotals();

                    } else {

                        alert('بيانات الخدمة غير مكتملة');

                    }

                }



                // إغلاق النافذة المنبثقة

                $('#selectEmployeeModal').modal('hide');

            } else {

                alert('الرجاء اختيار الحلاق');

            }

        });



        // حذف عنصر من الفاتورة

        $(document).on('click', '.remove-item', function() {

            const index = $(this).data('index');

            invoiceItems.splice(index, 1);

            renderInvoiceItems();

            calculateTotals();

        });



        // زيادة الكمية

        $(document).on('click', '.increase-qty', function() {

            const index = $(this).data('index');

            invoiceItems[index].quantity += 1;

            updateInvoiceItem(index);

            calculateTotals();

        });



        // تقليل الكمية

        $(document).on('click', '.decrease-qty', function() {

            const index = $(this).data('index');

            if (invoiceItems[index].quantity > 1) {

                invoiceItems[index].quantity -= 1;

                updateInvoiceItem(index);

                calculateTotals();

            }

        });



        // تغيير الكمية

        $(document).on('change', '.item-qty', function() {

            const index = $(this).data('index');

            const qty = parseInt($(this).val()) || 1;

            invoiceItems[index].quantity = qty < 1 ? 1 : qty;

            updateInvoiceItem(index);

            calculateTotals();

        });



        // تحديث إجماليات الفاتورة عند تغيير قيمة الخصم

        $('#discountAmount, #discountType').on('change', function() {

            calculateTotals();

        });



        // إظهار/إخفاء حقل المبلغ المدفوع بناءً على حالة الدفع

        $('#paymentStatus').on('change', function() {

            const paymentStatus = $(this).val();



            if (paymentStatus === 'partial') {

                $('#paidAmountContainer').show();

                // التأكد من أن المبلغ المدفوع أقل من المبلغ الإجمالي

                const finalAmount = parseFloat($('#finalAmount').val());

                let paidAmount = parseFloat($('#paidAmount').val());



                if (paidAmount <= 0) {

                    $('#paidAmount').val('0');

                } else if (paidAmount >= finalAmount) {

                    $('#paidAmount').val((finalAmount * 0.5).toFixed(2)); // نصف المبلغ كقيمة افتراضية

                }

            } else if (paymentStatus === 'paid') {

                $('#paidAmountContainer').hide();

                // إذا كانت مدفوعة بالكامل، المبلغ المدفوع = المبلغ الإجمالي

                const finalAmount = parseFloat($('#finalAmount').val());

                $('#paidAmount').val(finalAmount.toFixed(2));

            } else {

                $('#paidAmountContainer').hide();

                // إذا كانت غير مدفوعة، المبلغ المدفوع = 0

                $('#paidAmount').val('0');

            }

        });



        // البحث عن خدمة

        $('#searchService').on('input', function() {

            const searchTerm = $(this).val().toLowerCase();

            $('.service-item').each(function() {

                const serviceName = $(this).find('.card-title').text().toLowerCase();

                $(this).toggle(serviceName.includes(searchTerm));

            });

        });



        // تصفية الخدمات حسب التصنيف

        $('.category-filter').on('click', function() {

            $('.category-filter').removeClass('active');

            $(this).addClass('active');



            const categoryId = $(this).data('category');



            if (categoryId === 'all') {

                $('.service-item').show();

            } else {

                $('.service-item').each(function() {

                    $(this).toggle($(this).data('category') == categoryId);

                });

            }

        });

        // عند النقر على زر إضافة منتج

$(document).on('click', '.product-item', function() {

    const productId = $(this).find('.card').data('id');

    const productName = $(this).find('.card').data('name');

    const productPrice = parseFloat($(this).find('.card').data('price'));

    const productCard = $(this).find('.card');



    // التحقق من توفر المنتج في المخزون باستخدام API

    $.ajax({

        url: `${API_URL}inventory.php?action=get_product_stock`,

        method: 'GET',

        data: {

            product_id: productId,

            branch_id: $('#branchId').val()

        },

        dataType: 'json',

        success: function(response) {

            if (response.status === 'success') {

                const currentStock = parseInt(response.quantity) || 0;



                // التحقق من توفر المنتج في المخزون

                if (currentStock <= 0) {

                    alert('عذراً، هذا المنتج غير متوفر في المخزون');

                    return;

                }



                // إضافة المنتج للفاتورة

                const newItem = {

                    item_id: productId,

                    item_type: 'product',

                    name: productName,

                    price: productPrice,

                    quantity: 1,

                    total: productPrice,

                    current_stock: currentStock

                };



                // التحقق من وجود العنصر مسبقًا

                const existingItemIndex = invoiceItems.findIndex(item =>

                    item.item_id === productId && item.item_type === 'product'

                );



                if (existingItemIndex >= 0) {

                    // التحقق من عدم تجاوز الكمية المتاحة في المخزون

                    if (invoiceItems[existingItemIndex].quantity + 1 > currentStock) {

                        alert(`الكمية المتاحة للمنتج هي ${currentStock} فقط`);

                        return;

                    }



                    // زيادة الكمية للعنصر الموجود

                    invoiceItems[existingItemIndex].quantity += 1;

                    invoiceItems[existingItemIndex].total =

                        parseFloat(invoiceItems[existingItemIndex].price) * invoiceItems[existingItemIndex].quantity;



                    // تحديث العرض

                    updateInvoiceItem(existingItemIndex);

                } else {

                    // إضافة عنصر جديد

                    invoiceItems.push(newItem);

                    renderInvoiceItems();

                }



                // حساب الإجماليات

                calculateTotals();

            } else {

                // في حالة فشل الاستجابة

                alert('حدث خطأ أثناء التحقق من المخزون');

            }

        },

        error: function(xhr, status, error) {

            console.error('Error checking inventory:', error);

            alert('حدث خطأ أثناء الاتصال بالخادم');

        }

    });

});

        // البحث عن منتج

        $('#searchProduct').on('input', function() {

            const searchTerm = $(this).val().toLowerCase();

            $('.product-item').each(function() {

                const productName = $(this).find('.card-title').text().toLowerCase();

                $(this).toggle(productName.includes(searchTerm));

            });

        });



        // البحث عن عميل

        $('#customerSearch').on('input', function() {

            const searchTerm = $(this).val();



            if (searchTerm.length < 2) {

                $('#customerSearchResults').hide();

                return;

            }



            $.ajax({

                url: `${API_URL}customers.php?action=search_customer`,

                method: 'POST',

                data: {

                    search_term: searchTerm,

                    branch_id: $('#branchId').val()

                },

                dataType: 'json',

                success: function(response) {

                    if (response.status === 'success' && response.customers.length > 0) {

                        let html = '';

                        response.customers.forEach(customer => {

                            html += `<div class="result-item" data-id="${customer.id}" data-name="${customer.name}">${customer.name} - ${customer.phone}</div>`;

                        });

                        $('#customerSearchResults').html(html).show();

                    } else {

                        $('#customerSearchResults').html('<div class="p-2">لا توجد نتائج</div>').show();

                    }

                },

                error: function() {

                    $('#customerSearchResults').html('<div class="p-2">حدث خطأ في البحث</div>').show();

                }

            });

        });



        // اختيار عميل من نتائج البحث

        $(document).on('click', '.result-item', function() {

            const customerId = $(this).data('id');

            const customerName = $(this).data('name');



            $('#customerId').val(customerId);

            $('#customerSearch').val(customerName);

            $('#customerSearchResults').hide();

        });



        // إخفاء نتائج البحث عند النقر خارجها

        $(document).on('click', function(e) {

            if (!$(e.target).closest('#customerSearch, #customerSearchResults').length) {

                $('#customerSearchResults').hide();

            }

        });



        // حفظ عميل جديد

        $('#saveCustomer').click(function() {

            const formData = $('#addCustomerForm').serialize();



            $.ajax({

                url: `${API_URL}customers.php?action=add_customer`,

                method: 'POST',

                data: formData,

                dataType: 'json',

                success: function(response) {

                    if (response.status === 'success') {

                        // إضافة العميل إلى الفاتورة

                        $('#customerId').val(response.customer_id);

                        $('#customerSearch').val($('#customerName').val());



                        // إغلاق النافذة المنبثقة وإعادة تعيين النموذج

                        $('#addCustomerModal').modal('hide');

                        $('#addCustomerForm')[0].reset();



                        // عرض رسالة نجاح

                        alert('تم إضافة العميل بنجاح');

                    } else {

                        alert(response.message || 'حدث خطأ أثناء إضافة العميل');

                    }

                },

                error: function() {

                    alert('حدث خطأ في الاتصال بالخادم');

                }

            });

        });



        // مسح الفاتورة

        $('#clearInvoice').click(function() {

            if (confirm('هل أنت متأكد من رغبتك في إلغاء الفاتورة الحالية؟')) {

                invoiceItems = [];

                renderInvoiceItems();

                calculateTotals();

                $('#customerId').val('');

                $('#customerSearch').val('');

                $('#employeeId').val('');

                $('#notes').val('');



                // إعادة تعيين كود الترويج

                $('#promoCode').val('').prop('disabled', false);

                $('#applyPromoCodeBtn').prop('disabled', false);

                $('#appliedPromoCodeId').val('');

                $('#promoCodeMessage').hide();

            }

        });







        // حفظ الفاتورة كمسودة

        $('#saveDraft').click(function() {

            // التحقق من وجود عناصر في الفاتورة

            if (invoiceItems.length === 0) {

                alert('لا يمكن حفظ فاتورة بدون عناصر');

                return;

            }



            // تحديث حالة الدفع إلى غير مدفوع

            $('#paymentStatus').val('unpaid');



            // إرسال النموذج

            $('#invoiceForm').submit();

        });

    });



    // دالة لعرض عناصر الفاتورة

    function renderInvoiceItems() {

        const tbody = $('#invoiceItemsTable tbody');

        tbody.empty();



        if (invoiceItems.length === 0) {

            tbody.append('<tr><td colspan="6" class="text-center">لا توجد عناصر في الفاتورة</td></tr>');

            return;

        }



        invoiceItems.forEach((item, index) => {

            const typeText = item.item_type === 'service' ? 'خدمة' : 'منتج';

            if (item.item_type === 'product' && item.current_stock) {

            const stockBadge = `<span class="badge bg-info">المخزون المتبقي: ${item.current_stock - item.quantity}</span>`;

            // أضف هذا الباج بجانب اسم المنتج أو في مكان مناسب

        }

            // التأكد من أن السعر والإجمالي أرقام

            const price = parseFloat(item.price) || 0;

            const originalTotal = parseFloat(item.original_total) || parseFloat(item.price) * item.quantity;

            const total = parseFloat(item.total) || originalTotal;

            const discount = parseFloat(item.discount) || 0;



            // عرض الخصم إذا كان موجوداً

            const discountBadge = discount > 0 ?

                `<span class="badge bg-success">خصم: ${discount.toFixed(2)}</span>` : '';



            // الحصول على اسم الموظف من القائمة المنسدلة

            let employeeName = '';

            if (item.employee_id) {

                employeeName = $('#modalEmployeeId option[value="'+item.employee_id+'"]').text() ||

                              $('#employeeId option[value="'+item.employee_id+'"]').text() ||

                              'موظف #' + item.employee_id;

            }



            const employeeBadge = item.employee_id ?

                `<span class="badge bg-info">${employeeName}</span>` :

                (item.item_type === 'service' ? '<span class="badge bg-warning">لم يتم تحديد الحلاق</span>' : '');



            const row = $(`

                <tr>

                    <td>${item.name}</td>

                    <td>${typeText} ${employeeBadge}</td>

                    <td>

                        <div class="quantity-control">

                            <button type="button" class="btn btn-sm btn-outline-secondary decrease-qty" data-index="${index}">-</button>

                            <input type="number" class="form-control form-control-sm item-qty" data-index="${index}" value="${item.quantity}" min="1">

                            <button type="button" class="btn btn-sm btn-outline-secondary increase-qty" data-index="${index}">+</button>

                        </div>

                    </td>

                    <td>${formatCurrency(price)}</td>

                    <td>

                        ${formatCurrency(total)}

                        ${discountBadge}

                    </td>

                    <td>

                        <div class="btn-group">

                            <button type="button" class="btn btn-sm btn-danger remove-item" data-index="${index}">

                                <i class="fas fa-times"></i>

                            </button>

                            ${item.item_type === 'service' ?

                                `<button type="button" class="btn btn-sm btn-info select-employee" data-index="${index}">

                                    <i class="fas fa-user"></i>

                                </button>` : ''}

                        </div>

                    </td>

                </tr>

            `);



            tbody.append(row);

        });



        // إضافة معالج الأحداث لأزرار اختيار الموظف

        $('.select-employee').on('click', function() {

            const index = $(this).data('index');

            const item = invoiceItems[index];



            $('#modalServiceName').val(item.name);

            $('#modalItemIndex').val(index);

            $('#modalEmployeeId').val(item.employee_id || '');

            $('#selectEmployeeModal').modal('show');

        });

    }



    // دالة لتنسيق المبالغ المالية

    function formatCurrency(amount) {

        return amount.toFixed(2) + ' ' + CURRENCY_SYMBOL;

    }



    // دالة لتحديث عنصر واحد في الفاتورة

    function updateInvoiceItem(index) {

        const item = invoiceItems[index];

        item.total = item.price * item.quantity;



        const row = $(`#invoiceItemsTable tbody tr:eq(${index})`);

        row.find('.item-qty').val(item.quantity);

        row.find('td:eq(4)').text(formatCurrency(item.total));



        // تحديث المجموع الكلي للفاتورة

        calculateTotals();

    }



    // دالة لحساب إجماليات الفاتورة

    function calculateTotals() {

        let subtotal = 0;



        // حساب المجموع الفرعي قبل الخصم

        invoiceItems.forEach(item => {

            // إعادة حساب الإجمالي بناءً على السعر والكمية فقط (بدون خصم)

            item.original_total = parseFloat(item.price) * item.quantity;

            subtotal += item.original_total;

        });



        // عرض المجموع الفرعي في الجدول

        $('#subtotalDisplay').text(subtotal.toFixed(2));



        // حساب الخصم

        const discountAmount = parseFloat($('#discountAmount').val()) || 0;

        const discountType = $('#discountType').val();

        let totalDiscount = 0;



        if (discountType === 'percentage') {

            // خصم بالنسبة المئوية

            totalDiscount = subtotal * (discountAmount / 100);

        } else {

            // خصم بالمبلغ

            totalDiscount = discountAmount;

        }



        // التأكد من أن الخصم لا يتجاوز إجمالي الفاتورة

        totalDiscount = Math.min(totalDiscount, subtotal);



        // توزيع الخصم على العناصر بالتناسب

        const discountPercentage = totalDiscount / subtotal;

        let afterDiscount = 0;



        invoiceItems.forEach(item => {

            // حساب الخصم لهذا العنصر

            item.discount = item.original_total * discountPercentage;



            // حساب المبلغ بعد الخصم

            item.total = item.original_total - item.discount;



            // إضافة المبلغ بعد الخصم إلى الإجمالي

            afterDiscount += item.total;

        });



        // حساب الضريبة

        const tax = afterDiscount * TAX_RATE;



        // حساب الإجمالي النهائي

        const finalTotal = afterDiscount + tax;



        // تحديث الحقول

        $('#totalAmount').val(subtotal.toFixed(2));

        $('#discountAmount').val(discountAmount.toFixed(2));

        $('#taxAmount').val(tax.toFixed(2));

        $('#finalAmount').val(finalTotal.toFixed(2));



        // التحقق من صلاحية كود الترويج المطبق بعد تغيير قيمة الفاتورة

        const promoCodeId = $('#appliedPromoCodeId').val();

        if (promoCodeId) {

            const promoCode = $('#promoCode').val().trim();

            const customerId = $('#customerId').val();



            // التحقق من صلاحية الكود مع القيمة الجديدة للفاتورة

            $.ajax({

                url: `${API_URL}promo_codes.php?action=validate`,

                method: 'POST',

                data: {

                    code: promoCode,

                    invoice_amount: subtotal,

                    customer_id: customerId || null

                },

                dataType: 'json',

                success: function(response) {

                    if (response.status !== 'success') {

                        // إلغاء تطبيق الكود إذا لم يعد صالحًا

                        $('#appliedPromoCodeId').val('');

                        $('#promoCode').prop('disabled', false);

                        $('#applyPromoCodeBtn').prop('disabled', false);



                        showPromoCodeMessage('warning', 'تم إلغاء تطبيق كود الترويج لأنه لم يعد صالحًا مع قيمة الفاتورة الجديدة');

                    }

                }

            });

        }



        // تحديث المبلغ المدفوع بناءً على حالة الدفع

        const paymentStatus = $('#paymentStatus').val();

        if (paymentStatus === 'paid') {

            $('#paidAmount').val(finalTotal.toFixed(2));

        } else if (paymentStatus === 'partial') {

            // التحقق من أن المبلغ المدفوع لا يتجاوز المبلغ الإجمالي

            const currentPaidAmount = parseFloat($('#paidAmount').val());

            if (currentPaidAmount > finalTotal) {

                $('#paidAmount').val((finalTotal * 0.5).toFixed(2)); // نصف المبلغ كقيمة افتراضية

            }

        } else {

            $('#paidAmount').val('0');

        }



        // طباعة قيم الإجماليات للتصحيح

        console.log('Totals calculated:');

        console.log('Subtotal:', subtotal.toFixed(2));

        console.log('Discount:', totalDiscount.toFixed(2));

        console.log('After Discount:', afterDiscount.toFixed(2));

        console.log('Tax:', tax.toFixed(2));

        console.log('Final Total:', finalTotal.toFixed(2));



        // تحديث عرض العناصر في الجدول لإظهار القيم الجديدة

        renderInvoiceItems();

    }



    // دالة لإنشاء رقم فاتورة جديد

    function generateNewInvoiceNumber(lastNumber) {

        if (!lastNumber) {

            const date = new Date();

            const year = date.getFullYear();

            const month = String(date.getMonth() + 1).padStart(2, '0');

            const day = String(date.getDate()).padStart(2, '0');

            const dateStr = `${year}${month}${day}`;

            return `${dateStr}0001`;

        }



        // استخراج الجزء الرقمي والتاريخ من رقم الفاتورة الأخير

        const datePart = lastNumber.substring(0, 8);

        const numPart = parseInt(lastNumber.substring(8));



        // الحصول على تاريخ اليوم

        const date = new Date();

        const year = date.getFullYear();

        const month = String(date.getMonth() + 1).padStart(2, '0');

        const day = String(date.getDate()).padStart(2, '0');

        const currentDateStr = `${year}${month}${day}`;



        if (currentDateStr === datePart) {

            // نفس اليوم، زيادة الرقم فقط

            return `${datePart}${String(numPart + 1).padStart(4, '0')}`;

        } else {

            // يوم جديد، البدء من 1

            return `${currentDateStr}0001`;

        }

    }



    // دالة لتحميل بيانات الموعد لإنشاء فاتورة جديدة

    function loadAppointmentData(appointmentId) {

        $.ajax({

            url: `${API_URL}appointments.php`,

            method: 'GET',

            data: {

                action: 'view',

                id: appointmentId

            },

            dataType: 'json',

            success: function(response) {

                if (response.status === 'success' && response.appointment) {

                    const appointment = response.appointment;



                    // تعبئة بيانات العميل

                    if (appointment.customer_id) {

                        $('#customerId').val(appointment.customer_id);

                        $('#customerSearch').val(appointment.customer_name || '');

                    }



                    // تعبئة بيانات الموظف

                    if (appointment.employee_id) {

                        $('#employeeId').val(appointment.employee_id);

                    }



                    // إضافة ملاحظات الموعد إلى ملاحظات الفاتورة

                    if (appointment.notes) {

                        $('#notes').val(`موعد: ${appointment.notes}`);

                    }



                    // إضافة الخدمة إلى الفاتورة

                    if (appointment.service_id) {

                        // الحصول على بيانات الخدمة الكاملة

                        $.ajax({

                            url: `${API_URL}services.php`,

                            method: 'GET',

                            data: {

                                action: 'view',

                                id: appointment.service_id

                            },

                            dataType: 'json',

                            success: function(serviceResponse) {

                                if (serviceResponse.status === 'success' && serviceResponse.data) {

                                    const service = serviceResponse.data;



                                    // إضافة الخدمة إلى الفاتورة

                                    const newItem = {

                                        item_id: service.id,

                                        item_type: 'service',

                                        name: service.name,

                                        price: parseFloat(service.price),

                                        quantity: 1,

                                        total: parseFloat(service.price),

                                        employee_id: appointment.employee_id || null

                                    };



                                    // إضافة الخدمة إلى قائمة عناصر الفاتورة

                                    invoiceItems.push(newItem);

                                    renderInvoiceItems();

                                    calculateTotals();

                                }

                            }

                        });

                    }

                }

            }

        });

    }



    // دالة لتحميل بيانات العميل

    function loadCustomerData(customerId) {

        console.log('Loading customer data for ID:', customerId);



        // عرض مؤشر التحميل

        $('#customerSearch').addClass('loading').attr('placeholder', 'جاري تحميل بيانات العميل...');



        $.ajax({

            url: `${API_URL}customers.php`,

            method: 'POST',

            data: {

                action: 'get_customer',

                id: customerId

            },

            dataType: 'json',

            success: function(response) {

                console.log('Customer data response:', response);



                if (response.status === 'success' && response.customer) {

                    // تعبئة بيانات العميل

                    $('#customerId').val(response.customer.id);

                    $('#customerSearch').val(response.customer.name || '');



                    // عرض رسالة نجاح

                    showAlert(`تم تحديد العميل: ${response.customer.name}`, 'success');

                } else {

                    // عرض رسالة خطأ

                    showAlert('لم يتم العثور على العميل', 'danger');

                    $('#customerId').val('');

                    $('#customerSearch').val('');

                }

            },

            error: function(xhr, status, error) {

                console.error('Error loading customer data:', error);

                showAlert('حدث خطأ أثناء تحميل بيانات العميل', 'danger');

            },

            complete: function() {

                // إزالة مؤشر التحميل

                $('#customerSearch').removeClass('loading').attr('placeholder', 'ابحث عن عميل...');

            }

        });

    }



    // دالة لتحميل بيانات الموظف

    function loadEmployeeData(employeeId) {

        console.log('Loading employee data for ID:', employeeId);



        $.ajax({

            url: `${API_URL}employees.php`,

            method: 'POST',

            data: {

                action: 'get_employee',

                id: employeeId

            },

            dataType: 'json',

            success: function(response) {

                console.log('Employee data response:', response);



                if (response.status === 'success' && response.employee) {

                    // تخزين معرف الموظف لاستخدامه لاحقًا عند إضافة خدمات

                    window.defaultEmployeeId = employeeId;

                    window.defaultEmployeeName = response.employee.name;



                    // عرض رسالة نجاح

                    showAlert(`تم تحديد الموظف: ${response.employee.name}`, 'success');

                } else {

                    // عرض رسالة خطأ

                    showAlert('لم يتم العثور على الموظف', 'danger');

                }

            },

            error: function(xhr, status, error) {

                console.error('Error loading employee data:', error);

                showAlert('حدث خطأ أثناء تحميل بيانات الموظف', 'danger');

            }

        });

    }



    // معالجة كود الترويج

    let currentPromoCode = null;



    // التحقق من صلاحية كود الترويج

    $('#applyPromoCodeBtn').on('click', function() {

        const promoCode = $('#promoCode').val().trim();

        const totalAmount = parseFloat($('#totalAmount').val());

        const customerId = $('#customerId').val();



        if (!promoCode) {

            showPromoCodeMessage('danger', 'الرجاء إدخال كود الترويج');

            return;

        }



        // إرسال طلب للتحقق من صلاحية الكود

        $.ajax({

            url: `${API_URL}promo_codes.php?action=validate`,

            method: 'POST',

            data: {

                code: promoCode,

                invoice_amount: totalAmount,

                customer_id: customerId || null

            },

            dataType: 'json',

            success: function(response) {

                if (response.status === 'success') {

                    // تخزين بيانات كود الترويج

                    currentPromoCode = response.promo_code;



                    // عرض تفاصيل كود الترويج

                    showPromoCodeDetails(currentPromoCode);



                    // فتح النافذة المنبثقة

                    $('#promoCodeDetailsModal').modal('show');

                } else {

                    showPromoCodeMessage('danger', response.message || 'كود الترويج غير صالح');

                }

            },

            error: function() {

                showPromoCodeMessage('danger', 'حدث خطأ أثناء التحقق من كود الترويج');

            }

        });

    });



    // تطبيق كود الترويج

    $('#confirmApplyPromoCode').on('click', function() {

        if (!currentPromoCode) {

            return;

        }



        // تطبيق الخصم على الفاتورة

        $('#discountType').val(currentPromoCode.discount_type);

        $('#discountAmount').val(currentPromoCode.discount_value);

        $('#appliedPromoCodeId').val(currentPromoCode.id);



        // عرض رسالة نجاح

        showPromoCodeMessage('success', `تم تطبيق كود الترويج "${currentPromoCode.code}" بنجاح`);



        // إعادة حساب الإجماليات

        calculateTotals();



        // إغلاق النافذة المنبثقة

        $('#promoCodeDetailsModal').modal('hide');



        // تعطيل حقل إدخال كود الترويج

        $('#promoCode').prop('disabled', true);

        $('#applyPromoCodeBtn').prop('disabled', true);

    });



    // عرض تفاصيل كود الترويج

    function showPromoCodeDetails(promoCode) {

        $('#promoCodeDetailsCode').text(promoCode.code);

        $('#promoCodeDetailsName').text(promoCode.name);

        $('#promoCodeDetailsDescription').text(promoCode.description || 'لا يوجد وصف');



        // نوع الخصم

        if (promoCode.discount_type === 'percentage') {

            $('#promoCodeDetailsDiscountType').text('نسبة مئوية');

            $('#promoCodeDetailsDiscountValue').text(`${promoCode.discount_value}%`);

        } else {

            $('#promoCodeDetailsDiscountType').text('مبلغ ثابت');

            $('#promoCodeDetailsDiscountValue').text(`${promoCode.discount_value} ${CURRENCY_SYMBOL}`);

        }



        // الحد الأدنى والأقصى للفاتورة

        $('#promoCodeDetailsMinAmount').text(promoCode.min_invoice_amount ? `${promoCode.min_invoice_amount} ${CURRENCY_SYMBOL}` : 'غير محدد');

        $('#promoCodeDetailsMaxAmount').text(promoCode.max_invoice_amount ? `${promoCode.max_invoice_amount} ${CURRENCY_SYMBOL}` : 'غير محدد');



        // عدد مرات الاستخدام

        if (promoCode.max_uses) {

            $('#promoCodeDetailsUsage').text(`${promoCode.current_uses} / ${promoCode.max_uses}`);

        } else {

            $('#promoCodeDetailsUsage').text(`${promoCode.current_uses} (غير محدود)`);

        }



        // نقاط الولاء

        $('#promoCodeDetailsLoyaltyPoints').text(promoCode.required_loyalty_points ? `${promoCode.required_loyalty_points} نقطة` : 'لا يتطلب نقاط');



        // الصلاحية

        let validityText = '';

        if (promoCode.start_date && promoCode.end_date) {

            validityText = `من ${promoCode.start_date} إلى ${promoCode.end_date}`;

        } else if (promoCode.start_date) {

            validityText = `يبدأ من ${promoCode.start_date}`;

        } else if (promoCode.end_date) {

            validityText = `ينتهي في ${promoCode.end_date}`;

        } else {

            validityText = 'غير محدد';

        }

        $('#promoCodeDetailsValidity').text(validityText);

    }



    // عرض رسالة تنبيه عامة

    function showAlert(message, type = 'info') {

        // إنشاء عنصر التنبيه

        const alertDiv = $(`

            <div class="alert alert-${type} alert-dismissible fade show" role="alert">

                ${message}

                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>

            </div>

        `);



        // إضافة التنبيه إلى حاوية التنبيهات

        $('#alerts-container').append(alertDiv);



        // إخفاء التنبيه بعد 5 ثواني

        setTimeout(function() {

            alertDiv.alert('close');

        }, 5000);

    }



    // عرض رسالة كود الترويج

    function showPromoCodeMessage(type, message) {

        $('#promoCodeMessage').removeClass('text-success text-danger text-warning')

            .addClass(type === 'success' ? 'text-success' : (type === 'warning' ? 'text-warning' : 'text-danger'))

            .text(message)

            .show();



        // إخفاء الرسالة بعد 5 ثواني إذا كانت خطأ

        if (type === 'danger' || type === 'warning') {

            setTimeout(function() {

                $('#promoCodeMessage').fadeOut();

            }, 5000);

        }

    }



    // دالة تهيئة الصفحة

    function initializePage() {

        // تهيئة البحث عن العملاء

        initializeCustomerSearch();



        // تهيئة عرض الفاتورة الفارغة

        renderInvoiceItems();



        // حساب الإجماليات الأولية

        calculateTotals();



        // التحقق من وجود معلمة customer_id في URL

        <?php if (isset($_GET['customer_id']) && !empty($_GET['customer_id'])): ?>

        // تحميل بيانات العميل

        loadCustomerData(<?php echo intval($_GET['customer_id']); ?>);

        <?php endif; ?>



        // التحقق من وجود معلمة employee_id في URL

        <?php if (isset($_GET['employee_id']) && !empty($_GET['employee_id'])): ?>

        // تحميل بيانات الموظف

        loadEmployeeData(<?php echo intval($_GET['employee_id']); ?>);

        <?php endif; ?>



        // التحقق من وجود معلمة appointment_id في URL

        <?php if (isset($_GET['appointment_id']) && !empty($_GET['appointment_id'])): ?>

        // تحميل بيانات الموعد

        loadAppointmentData(<?php echo intval($_GET['appointment_id']); ?>);

        <?php endif; ?>



        // تهيئة حقل المبلغ المدفوع بناءً على حالة الدفع الحالية

        const paymentStatus = $('#paymentStatus').val();

        if (paymentStatus === 'partial') {

            $('#paidAmountContainer').show();

        } else {

            $('#paidAmountContainer').hide();

            if (paymentStatus === 'paid') {

                const finalAmount = parseFloat($('#finalAmount').val());

                $('#paidAmount').val(finalAmount.toFixed(2));

            } else {

                $('#paidAmount').val('0');

            }

        }



        // تهيئة البحث عن المنتجات والخدمات

        $('#searchService, #searchProduct').on('input', function() {

            const searchTerm = $(this).val().toLowerCase();

            const type = $(this).attr('id') === 'searchService' ? '.service-item' : '.product-item';



            $(type).each(function() {

                const itemName = $(this).find('.card-title').text().toLowerCase();

                $(this).toggle(itemName.includes(searchTerm));

            });

        });

    }



    // دالة تهيئة البحث عن العملاء

    function initializeCustomerSearch() {

        $('#customerSearch').on('input', function() {

            const searchTerm = $(this).val().toLowerCase();

            if (searchTerm.length < 2) return;



            $.ajax({

                url: API_URL + 'customers.php?action=search',

                method: 'GET',

                data: { term: searchTerm },

                dataType: 'json',

                success: function(response) {

                    if (response.status === 'success') {

                        const results = response.customers;

                        const dropdown = $('#customerSearchResults');

                        dropdown.empty();



                        if (results.length === 0) {

                            dropdown.append('<div class="p-2">لا توجد نتائج</div>');

                        } else {

                            results.forEach(customer => {

                                dropdown.append(`

                                    <div class="customer-item p-2 border-bottom" data-id="${customer.id}" data-name="${customer.name}">

                                        ${customer.name} - ${customer.phone}

                                    </div>

                                `);

                            });

                        }



                        dropdown.show();

                    }

                }

            });

        });



        // اختيار العميل من نتائج البحث

        $(document).on('click', '.customer-item', function() {

            const customerId = $(this).data('id');

            const customerName = $(this).data('name');



            $('#customerId').val(customerId);

            $('#customerSearch').val(customerName);

            $('#customerSearchResults').hide();

        });



        // إخفاء نتائج البحث عند النقر خارجها

        $(document).on('click', function(e) {

            if (!$(e.target).closest('#customerSearchContainer').length) {

                $('#customerSearchResults').hide();

            }

        });

    }



    // إضافة معالج حدث لزر إضافة العنصر

    $(document).on('click', '.add-item-btn', function() {

        const itemType = $(this).data('type');

        const itemId = $(this).data('id');

        const itemName = $(this).data('name');

        const itemPrice = parseFloat($(this).data('price')) || 0;

        const itemQuantity = 1; // الكمية الافتراضية



        if (!itemId || !itemType || !itemName || itemPrice <= 0) {

            alert('بيانات العنصر غير صحيحة');

            return;

        }



        // إضافة العنصر للفاتورة

        const newItem = {

            item_id: itemId,

            item_type: itemType,

            name: itemName,

            price: itemPrice,

            quantity: itemQuantity,

            total: itemPrice * itemQuantity,

            employee_id: itemType === 'service' ? '' : null

        };



        // التحقق من وجود العنصر مسبقًا

        const existingItemIndex = invoiceItems.findIndex(item =>

            item.item_id === itemId && item.item_type === itemType

        );



        if (existingItemIndex >= 0) {

            // زيادة الكمية للعنصر الموجود

            invoiceItems[existingItemIndex].quantity += itemQuantity;

            invoiceItems[existingItemIndex].total =

                parseFloat(invoiceItems[existingItemIndex].price) * invoiceItems[existingItemIndex].quantity;



            // تحديث العرض

            updateInvoiceItem(existingItemIndex);

        } else {

            // إضافة عنصر جديد

            invoiceItems.push(newItem);

            renderInvoiceItems();

        }



        // حساب الإجماليات

        calculateTotals();

    });



    // عند النقر على زر إضافة خدمة

    $(document).on('click', '.service-item', function() {

        const serviceId = $(this).data('id');

        const serviceName = $(this).data('name');

        const servicePrice = $(this).data('price');



        // تعيين قيم الخدمة في النافذة المنبثقة

        $('#modalServiceName').val(serviceName);

        $('#modalServiceId').val(serviceId);

        $('#modalServicePrice').val(servicePrice);

        $('#modalItemIndex').val(''); // فارغ لإضافة عنصر جديد



        // استخدام الموظف الافتراضي إذا كان متاحًا

        if (window.defaultEmployeeId) {

            $('#modalEmployeeId').val(window.defaultEmployeeId);

        } else {

            $('#modalEmployeeId').val(''); // إعادة تعيين اختيار الموظف

        }



        // فتح النافذة المنبثقة

        $('#selectEmployeeModal').modal('show');

    });



    // عند النقر على زر تعيين موظف لخدمة موجودة

    $(document).on('click', '.set-employee', function() {

        const index = $(this).data('index');

        const item = invoiceItems[index];



        // تعيين قيم الخدمة في النافذة المنبثقة

        $('#modalServiceName').val(item.name);

        $('#modalServiceId').val(item.item_id);

        $('#modalServicePrice').val(item.price);

        $('#modalItemIndex').val(index);

        $('#modalEmployeeId').val(item.employee_id || '');



        // فتح النافذة المنبثقة

        $('#selectEmployeeModal').modal('show');

    });

    // التحقق من العروض المتاحة للفاتورة

    function checkAvailablePromotions() {

        // التحقق من وجود عناصر في الفاتورة

        if (invoiceItems.length === 0) {

            $('#availablePromotionsAlert').hide();

            window.availablePromotions = [];

            return;

        }



        // جمع بيانات الفاتورة

        const totalAmount = parseFloat($('#totalAmount').val());

        const itemsCount = invoiceItems.length;



        // التحقق من صحة قيمة totalAmount

        if (isNaN(totalAmount) || totalAmount <= 0) {

            $('#availablePromotionsAlert').hide();

            window.availablePromotions = [];

            return;

        }



        // تحويل عناصر الفاتورة إلى التنسيق المطلوب للعروض

        const formattedItems = invoiceItems.map(item => ({

            item_id: item.item_id,

            item_type: item.item_type,

            quantity: item.quantity,

            price: item.price

        }));



        // إرسال طلب للتحقق من العروض المتاحة

        const requestData = {

            total_amount: totalAmount,

            items_count: itemsCount,

            items: JSON.stringify(formattedItems),

            branch_id: <?php echo $_SESSION['user_branch_id'] ?? $_GET['branch_id'] ?? 1; ?>

        };



        $.ajax({

            url: '<?php echo API_URL; ?>promotions.php?action=get_available_promotions',

            type: 'POST',

            data: requestData,

            dataType: 'json',

            success: function(response) {

                if (response.status === 'success' && response.promotions && response.promotions.length > 0) {

                    // تخزين العروض المتاحة في متغير عالمي

                    window.availablePromotions = response.promotions;



                    // عرض إشعار بوجود عروض متاحة

                    const bestPromotion = response.promotions[0]; // أفضل عرض (الأول في القائمة)



                    // عرض رسالة مناسبة بناءً على نوع الخصم

                    let message = '';

                    if (bestPromotion.discount_type === 'percentage') {

                        message = `يمكنك الحصول على خصم ${bestPromotion.discount_value}% على الفاتورة!`;

                    } else {

                        message = `يمكنك الحصول على خصم ${bestPromotion.discount_value} ${CURRENCY_SYMBOL} على الفاتورة!`;

                    }



                    $('#promotionsMessage').text(message);

                    $('#availablePromotionsAlert').show();

                    displayPromotionsList();

                } else {

                    // إخفاء الإشعار إذا لم تكن هناك عروض متاحة

                    $('#availablePromotionsAlert').hide();

                    window.availablePromotions = [];

                }

            },

            error: function() {

                // إخفاء الإشعار في حالة حدوث خطأ

                $('#availablePromotionsAlert').hide();

                window.availablePromotions = [];

            }

        });

    }



    // عرض العروض المتاحة في النافذة المنبثقة

    $('#promotionsModal').on('shown.bs.modal', function() {

        console.log('Modal shown, checking for promotions');

        // إظهار مؤشر التحميل وإخفاء العناصر الأخرى

        $('#loadingPromotions').show();

        $('#promotionsList').hide().empty(); // إفراغ القائمة وإخفاؤها

        $('#noPromotionsMessage').hide();

        $('#errorMessage').hide();



        // استخدام setTimeout للسماح بعرض مؤشر التحميل أولاً

        setTimeout(function() {

            // التحقق من وجود عروض مخزنة مسبقًا

            if (window.availablePromotions && window.availablePromotions.length > 0) {

                console.log('Using cached promotions:', window.availablePromotions);

                displayPromotionsList();

            } else {

                // استدعاء دالة جلب العروض

                console.log('Fetching new promotions');

                fetchAndDisplayPromotions();

            }

        }, 100);

    });



    // إضافة معالج لإغلاق النافذة المنبثقة

    $('#promotionsModal').on('hidden.bs.modal', function() {

        // إيقاف أي طلبات AJAX جارية

        if (window.currentPromotionsRequest) {

            window.currentPromotionsRequest.abort();

            window.currentPromotionsRequest = null;

        }

    });



    // إضافة معالج لزر إلغاء التحميل

    $(document).on('click', '#cancelLoading', function() {

        // إيقاف أي طلبات AJAX جارية

        if (window.currentPromotionsRequest) {

            window.currentPromotionsRequest.abort();

            window.currentPromotionsRequest = null;

        }



        // إخفاء مؤشر التحميل وإظهار رسالة مناسبة

        $('#loadingPromotions').hide();

        $('#errorMessage').text('تم إلغاء التحميل بواسطة المستخدم.');

        $('#errorMessage').show();

    });



    // دالة لجلب وعرض العروض المتاحة

    function fetchAndDisplayPromotions() {

        console.log('fetchAndDisplayPromotions called');

        // التحقق من وجود عناصر في الفاتورة

        if (invoiceItems.length === 0) {

            console.log('No invoice items');

            $('#loadingPromotions').hide();

            $('#noPromotionsMessage').text('لا يمكن عرض العروض لأن الفاتورة لا تحتوي على أي عناصر.');

            $('#noPromotionsMessage').show();

            return;

        }



        // جمع بيانات الفاتورة

        const totalAmount = parseFloat($('#totalAmount').val());

        const itemsCount = invoiceItems.length;

        console.log('Invoice data:', { totalAmount, itemsCount, items: invoiceItems });



        // التحقق من صحة قيمة totalAmount

        if (isNaN(totalAmount) || totalAmount <= 0) {

            console.log('Invalid total amount');

            $('#loadingPromotions').hide();

            $('#noPromotionsMessage').text('لا يمكن عرض العروض لأن إجمالي الفاتورة غير صحيح.');

            $('#noPromotionsMessage').show();

            return;

        }



        // تحويل عناصر الفاتورة إلى التنسيق المطلوب للعروض

        const formattedItems = invoiceItems.map(item => ({

            item_id: item.item_id,

            item_type: item.item_type,

            quantity: item.quantity,

            price: item.price

        }));



        // إرسال طلب للتحقق من العروض المتاحة

        const requestData = {

            total_amount: totalAmount,

            items_count: itemsCount,

            items: JSON.stringify(formattedItems),

            branch_id: <?php echo $_SESSION['user_branch_id'] ?? $_GET['branch_id'] ?? 1; ?>

        };



        console.log('Sending API request with data:', requestData);



        // إلغاء أي طلب سابق لم يكتمل

        if (window.currentPromotionsRequest) {

            window.currentPromotionsRequest.abort();

            window.currentPromotionsRequest = null;

        }



        try {

            // إرسال طلب AJAX للحصول على العروض

            window.currentPromotionsRequest = $.ajax({

                url: '<?php echo API_URL; ?>promotions.php?action=get_available_promotions',

                type: 'POST',

                data: requestData,

                dataType: 'json',

                timeout: 10000, // تحديد مهلة زمنية للطلب (10 ثواني)

                cache: false, // منع التخزين المؤقت

                success: function(response) {

                    console.log('API response received:', response);

                    $('#loadingPromotions').hide();

                    window.currentPromotionsRequest = null;



                    if (response && response.status === 'success' && response.promotions && response.promotions.length > 0) {

                        console.log('Promotions found:', response.promotions.length);

                        // تخزين العروض المتاحة

                        window.availablePromotions = response.promotions;

                        // عرض العروض

                        displayPromotionsList();

                    } else {

                        console.log('No promotions found or invalid response');

                        // عرض رسالة عدم وجود عروض

                        $('#noPromotionsMessage').show();

                        $('#promotionsList').hide();

                        window.availablePromotions = [];

                    }

                },

                error: function(xhr, status, error) {

                    console.error('API error:', error, xhr.responseText, 'Status:', status);

                    $('#loadingPromotions').hide();

                    window.currentPromotionsRequest = null;



                    if (status === 'timeout') {

                        $('#errorMessage').text('انتهت مهلة الاتصال بالخادم. يرجى المحاولة مرة أخرى.');

                    } else if (status === 'abort') {

                        // تم إلغاء الطلب عمداً

                        console.log('Request was aborted');

                        return;

                    } else {

                        $('#errorMessage').text('حدث خطأ أثناء جلب العروض: ' + (xhr.responseText || error));

                    }



                    $('#errorMessage').show();

                    $('#noPromotionsMessage').hide();

                    $('#promotionsList').hide();

                    window.availablePromotions = [];

                },

                complete: function() {

                    // التأكد من إخفاء مؤشر التحميل في جميع الحالات

                    $('#loadingPromotions').hide();

                }

            });

        } catch (e) {

            console.error('Exception in AJAX request:', e);

            $('#loadingPromotions').hide();

            $('#errorMessage').text('حدث خطأ غير متوقع: ' + e.message);

            $('#errorMessage').show();

            $('#noPromotionsMessage').hide();

            $('#promotionsList').hide();

        }

    }



    // دالة لعرض قائمة العروض

    function displayPromotionsList() {

        console.log('displayPromotionsList called');

        // إخفاء مؤشر التحميل ورسالة الخطأ

        $('#loadingPromotions').hide();

        $('#errorMessage').hide();



        // إفراغ قائمة العروض

        $('#promotionsList').empty();



        try {

            // التحقق من وجود عروض

            if (!window.availablePromotions || window.availablePromotions.length === 0) {

                console.log('No promotions available to display');

                $('#noPromotionsMessage').show();

                $('#promotionsList').hide();

                return;

            }



            console.log('Displaying', window.availablePromotions.length, 'promotions');



            // إضافة كل عرض إلى القائمة

            window.availablePromotions.forEach(function(promotion, index) {

                console.log('Processing promotion', index, promotion);



                let discountText = '';

                let discountBadgeClass = 'bg-success';



                if (promotion.discount_type === 'percentage') {

                    discountText = `${promotion.discount_value}%`;

                } else {

                    discountText = `${promotion.discount_value} ${CURRENCY_SYMBOL}`;

                }



                // حساب المبلغ النهائي بعد الخصم إذا لم يكن موجودًا

                let finalAmountValue = promotion.final_amount;

                if (!finalAmountValue && promotion.discount_type && promotion.discount_value) {

                    const totalAmount = parseFloat($('#totalAmount').val());

                    if (promotion.discount_type === 'percentage') {

                        finalAmountValue = totalAmount * (1 - (promotion.discount_value / 100));

                    } else {

                        finalAmountValue = totalAmount - promotion.discount_value;

                        if (finalAmountValue < 0) finalAmountValue = 0;

                    }

                }



                let finalAmount = '';

                if (finalAmountValue) {

                    finalAmount = `<div class="text-success fw-bold">المبلغ بعد الخصم: ${finalAmountValue.toFixed(2)} ${CURRENCY_SYMBOL}</div>`;

                }



                // إضافة شروط العرض إذا كانت متوفرة

                let conditionsHtml = '';

                // استخدام الشروط من البيانات المستلمة

                if (promotion.condition_type && promotion.condition_value) {

                    conditionsHtml = '<div class="mt-2 small text-muted"><strong>شروط العرض:</strong><ul class="mb-0">';



                    if (promotion.condition_type === 'total_amount') {

                        // إضافة الحد الأدنى للفاتورة

                        conditionsHtml += `<li>الحد الأدنى للفاتورة: ${promotion.condition_value} ${CURRENCY_SYMBOL}</li>`;



                        // إضافة الحد الأقصى للفاتورة إذا كان محددًا

                        if (promotion.condition_max_value && promotion.condition_max_value !== 'NULL' && promotion.condition_max_value !== null) {

                            conditionsHtml += `<li>الحد الأقصى للفاتورة: ${promotion.condition_max_value} ${CURRENCY_SYMBOL}</li>`;

                        }



                        // إضافة معلومات عن ملائمة العرض للفاتورة الحالية

                        const totalAmount = parseFloat($('#totalAmount').val());

                        const minValue = parseFloat(promotion.condition_value);

                        const maxValue = promotion.condition_max_value ? parseFloat(promotion.condition_max_value) : null;



                        // تحديد ما إذا كان العرض ملائمًا للفاتورة الحالية

                        promotion.isApplicable = true;



                        if (totalAmount < minValue) {

                            conditionsHtml += `<li class="text-danger">الفاتورة الحالية أقل من الحد الأدنى</li>`;

                            promotion.isApplicable = false;

                            promotion.notApplicableReason = 'الفاتورة الحالية أقل من الحد الأدنى';

                        } else if (maxValue !== null && totalAmount > maxValue) {

                            conditionsHtml += `<li class="text-danger">الفاتورة الحالية أعلى من الحد الأقصى</li>`;

                            promotion.isApplicable = false;

                            promotion.notApplicableReason = 'الفاتورة الحالية أعلى من الحد الأقصى';

                        } else {

                            conditionsHtml += `<li class="text-success">الفاتورة الحالية ملائمة للعرض</li>`;

                        }

                    }



                    if (promotion.condition_type === 'items_count') {

                        conditionsHtml += `<li>الحد الأدنى لعدد العناصر: ${promotion.condition_value}</li>`;

                    }



                    if (promotion.condition_type === 'specific_product' || promotion.condition_type === 'specific_service') {

                        const itemType = promotion.condition_type === 'specific_product' ? 'منتج محدد' : 'خدمة محددة';

                        conditionsHtml += `<li>يتطلب وجود: ${itemType}</li>`;

                    }



                    conditionsHtml += '</ul></div>';

                }



                // إنشاء عنصر العرض

                const promotionItem = `

                    <div class="list-group-item list-group-item-action">

                        <div class="d-flex w-100 justify-content-between align-items-center">

                            <h5 class="mb-1">${promotion.name || 'عرض خصم'}</h5>

                            <span class="badge ${discountBadgeClass}">خصم ${discountText}</span>

                        </div>

                        <p class="mb-1">${promotion.description || 'لا يوجد وصف'}</p>

                        ${conditionsHtml}

                        ${finalAmount}

                        <div class="d-flex justify-content-end mt-2">

                            <button type="button" class="btn ${promotion.isApplicable ? 'btn-primary' : 'btn-secondary'} apply-promotion"

                                data-id="${promotion.id || 0}"

                                data-discount-type="${promotion.discount_type || 'amount'}"

                                data-discount-value="${promotion.discount_value || 0}"

                                data-condition-type="${promotion.condition_type || ''}"

                                data-condition-value="${promotion.condition_value || 0}"

                                data-condition-max-value="${promotion.condition_max_value || ''}"

                                ${!promotion.isApplicable ? 'disabled title="' + (promotion.notApplicableReason || 'العرض غير ملائم للفاتورة الحالية') + '"' : ''}>

                                <i class="fas ${promotion.isApplicable ? 'fa-check-circle' : 'fa-ban'} me-1"></i>

                                ${promotion.isApplicable ? 'تطبيق الخصم' : 'غير متاح'}

                            </button>

                        </div>

                    </div>

                `;



                // إضافة العنصر إلى القائمة

                $('#promotionsList').append(promotionItem);

            });



            // إظهار قائمة العروض

            $('#promotionsList').show();

            $('#noPromotionsMessage').hide();



            console.log('Promotions list displayed');

        } catch (e) {

            console.error('Error displaying promotions:', e);

            $('#loadingPromotions').hide();

            $('#errorMessage').text('حدث خطأ أثناء عرض العروض: ' + e.message);

            $('#errorMessage').show();

            $('#noPromotionsMessage').hide();

            $('#promotionsList').hide();

        }

    }



    // تطبيق الخصم عند اختيار عرض

    $(document).on('click', '.apply-promotion', function() {

        const promotionId = $(this).data('id');

        const discountType = $(this).data('discount-type');

        const discountValue = $(this).data('discount-value');

        const conditionType = $(this).data('condition-type');

        const conditionValue = $(this).data('condition-value');

        const conditionMaxValue = $(this).data('condition-max-value');

        const totalAmount = parseFloat($('#totalAmount').val());



        // التحقق من ملائمة العرض للفاتورة الحالية

        if (conditionType === 'total_amount') {

            const minValue = parseFloat(conditionValue);

            const maxValue = conditionMaxValue ? parseFloat(conditionMaxValue) : null;



            if (totalAmount < minValue) {

                alert('لا يمكن تطبيق هذا العرض: الفاتورة الحالية أقل من الحد الأدنى (' + minValue + ' ' + CURRENCY_SYMBOL + ')');

                return;

            }



            if (maxValue !== null && totalAmount > maxValue) {

                alert('لا يمكن تطبيق هذا العرض: الفاتورة الحالية أعلى من الحد الأقصى (' + maxValue + ' ' + CURRENCY_SYMBOL + ')');

                return;

            }

        }



        // تطبيق الخصم على الفاتورة

        $('#discountAmount').val(discountValue);

        $('#discountType').val(discountType === 'percentage' ? 'percentage' : 'amount');



        // تخزين معرف العرض الترويجي لاستخدامه لاحقًا عند إنشاء الفاتورة

        $('#appliedPromotionId').val(promotionId);



        // إضافة ملاحظة عن العرض المطبق

        const currentNotes = $('#notes').val();

        const promotionNote = `\n[PROMOTION_ID:${promotionId}] \u062aم تطبيق عرض: ${$('#promotionsList').find(`[data-id="${promotionId}"]`).closest('.list-group-item').find('h5').text()}`;

        $('#notes').val(currentNotes + promotionNote);



        // إعادة حساب الإجماليات

        calculateTotals();



        // إغلاق النافذة المنبثقة

        $('#promotionsModal').modal('hide');



        // عرض رسالة نجاح

        alert('تم تطبيق الخصم بنجاح!');

    });



    // زر تحديث العروض

    $('#refreshPromotions').on('click', function() {

        // إظهار مؤشر التحميل وإخفاء العناصر الأخرى

        $('#loadingPromotions').show();

        $('#promotionsList').hide();

        $('#noPromotionsMessage').hide();

        $('#errorMessage').hide();



        // إعادة جلب العروض

        fetchAndDisplayPromotions();

    });



    // إضافة استدعاء لدالة checkAvailablePromotions بعد حساب الإجماليات

    const originalCalculateTotals = calculateTotals;

    calculateTotals = function() {

        originalCalculateTotals();

        // التحقق من العروض المتاحة

        checkAvailablePromotions();

    };



    // إضافة زر للتحقق من العروض مباشرة

    $('#showPromotionsBtn').on('click', function() {

        // التحقق من العروض المتاحة قبل فتح النافذة المنبثقة

        checkAvailablePromotions();

    });



    // تهيئة المتغيرات العالمية

    window.availablePromotions = [];

    window.currentPromotionsRequest = null;



    // ملاحظة: تم إزالة مستمع رسائل النوافذ لتجنب تكرار إرسال الإشعارات

    // الإشعارات يتم إرسالها من خلال حدث invoiceCreated فقط



    // متغير لتتبع الفواتير التي تم إرسال إشعارات لها من خلال هذه الدالة

    const notifiedInvoices = new Set();



    // دالة لإرسال إشعارات الفاتورة

    function sendInvoiceNotifications(invoiceId) {

        // التحقق من أن الفاتورة لم يتم إرسال إشعار لها مسبقًا

        if (notifiedInvoices.has(invoiceId)) {

            console.log(`تم إرسال إشعار للفاتورة #${invoiceId} مسبقًا. تجنب التكرار.`);

            return;

        }



        // التحقق من وجود كائن WhatsAppInvoiceNotification أولاً

        if (typeof WhatsAppInvoiceNotification !== 'undefined') {

            console.log(`استخدام WhatsAppInvoiceNotification لإرسال إشعار للفاتورة #${invoiceId}`);



            // إذا كان موجودًا، استخدمه لإرسال الإشعار

            WhatsAppInvoiceNotification.sendInvoiceNotification(invoiceId)

                .then(function(result) {

                    console.log(`تم إرسال إشعار للفاتورة #${invoiceId} بنجاح`);

                    notifiedInvoices.add(invoiceId); // إضافة الفاتورة إلى قائمة الفواتير المشعرة

                })

                .catch(function(error) {

                    console.error(`خطأ في إرسال إشعار للفاتورة #${invoiceId}:`, error);

                });

            return;

        }



        // إذا لم يكن WhatsAppInvoiceNotification موجودًا، استخدم WhatsAppClient إذا كان متاحًا

        if (typeof WhatsAppClient !== 'undefined') {

            console.log(`استخدام WhatsAppClient لإرسال إشعار للفاتورة #${invoiceId}`);



            // الحصول على بيانات الرسالة من API

            $.ajax({

                url: `${API_URL}whatsapp_data.php?action=get_invoice_notification_data&invoice_id=${invoiceId}`,

                type: 'GET',

                dataType: 'json',

                success: function(response) {

                    if (response.status === 'success' && response.data) {

                        // التحقق من الاتصال بالخادم المحلي

                        WhatsAppClient.checkConnection()

                            .then(function(isConnected) {

                                // إرسال الرسالة

                                return WhatsAppClient.sendMessage(response.data.phone, response.data.message);

                            })

                            .then(function(result) {

                                console.log('WhatsApp notification to customer sent successfully');

                                notifiedInvoices.add(invoiceId); // إضافة الفاتورة إلى قائمة الفواتير المشعرة



                                // تسجيل الإشعار في قاعدة البيانات

                                $.ajax({

                                    url: `${API_URL}invoice_notification.php?action=log_notification`,

                                    type: 'POST',

                                    data: {

                                        invoice_id: invoiceId,

                                        success: 1

                                    },

                                    dataType: 'json',

                                    error: function(xhr, status, error) {

                                        console.error('Error al registrar la notificación:', error);

                                        // No mostrar error al usuario para no interrumpir el flujo

                                    }

                                });

                            })

                            .catch(function(error) {

                                console.error('Error sending WhatsApp notification to customer:', error);

                                // تسجيل الإشعار في قاعدة البيانات

                                $.ajax({

                                    url: `${API_URL}invoice_notification.php?action=log_notification`,

                                    type: 'POST',

                                    data: {

                                        invoice_id: invoiceId,

                                        success: 0

                                    },

                                    dataType: 'json',

                                    error: function(xhr, status, error) {

                                        console.error('Error al registrar la notificación de fallo:', error);

                                        // No mostrar error al usuario para no interrumpir el flujo

                                    }

                                });



                                console.error('WhatsApp notification failed. Please check the local server.');

                            });

                    } else {

                        console.error('Error getting notification data:', response.message);

                    }

                },

                error: function() {

                    console.error('Error connecting to the server');

                }

            });

        } else {

            console.warn('لا يوجد مكون لإرسال إشعارات WhatsApp');

        }

    }





</script>



<!-- نافذة العروض والخصومات المتاحة -->

<div class="modal fade" id="promotionsModal" tabindex="-1" aria-labelledby="promotionsModalLabel" aria-hidden="true">

    <div class="modal-dialog modal-lg">

        <div class="modal-content">

            <div class="modal-header bg-primary text-white">

                <h5 class="modal-title" id="promotionsModalLabel">العروض والخصومات المتاحة</h5>

                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>

            </div>

            <div class="modal-body">

                <div id="loadingPromotions" class="text-center py-3">

                    <div class="spinner-border text-primary" role="status">

                        <span class="visually-hidden">جاري التحميل...</span>

                    </div>

                    <p class="mt-2">جاري التحقق من العروض المتاحة...</p>

                    <button type="button" class="btn btn-sm btn-secondary mt-3" id="cancelLoading">

                        <i class="fas fa-times-circle me-1"></i> إلغاء التحميل

                    </button>

                </div>



                <div id="noPromotionsMessage" class="alert alert-info" style="display: none;">

                    <i class="fas fa-info-circle me-2"></i> لا توجد عروض متاحة للفاتورة الحالية.

                </div>



                <div id="errorMessage" class="alert alert-danger" style="display: none;">

                    <i class="fas fa-exclamation-triangle me-2"></i> حدث خطأ أثناء جلب العروض. يرجى المحاولة مرة أخرى.

                </div>



                <div id="promotionsList" class="list-group" style="display: none;">

                    <!-- سيتم ملء هذه القائمة ديناميكيًا بالعروض المتاحة -->

                </div>

            </div>

            <div class="modal-footer">

                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>

                <button type="button" class="btn btn-primary" id="refreshPromotions">

                    <i class="fas fa-sync-alt"></i> تحديث العروض

                </button>

            </div>

        </div>

    </div>

</div>



<!-- استدعاء قالب الفوتر -->

<?php include_once __DIR__ . '/../../includes/templates/footer.php'; ?>



<!-- سكريبت إشعارات الواتساب للفواتير -->

<script src="<?php echo BASE_URL; ?>assets/js/whatsapp-custom-message.js"></script>

<script src="<?php echo BASE_URL; ?>assets/js/whatsapp-invoice-notification.js"></script>



<script>

    // تعيين معرف الفاتورة الحالية لاستخدامه في الإشعارات

    window.currentInvoiceId = 0;

</script>

