<?php
/**
 * إعدادات WhatsApp
 * يمكن تعديل هذا الملف لتخصيص إعدادات WhatsApp حسب بيئة الاستضافة
 */

// تعريف ثابت لمنع الوصول المباشر للملف
if (!defined('BASEPATH')) {
    exit('لا يمكن الوصول المباشر لهذا الملف');
}

// إعدادات WhatsApp
return [
    // وضع تشغيل WhatsApp (server_side أو client_side)
    'mode' => 'client_side',
    
    // مسار Node.js (يمكن تغييره إلى المسار الكامل إذا لزم الأمر)
    'node_path' => 'node',
    
    // مسار المتصفح (اتركه فارغًا لاستخدام المتصفح الافتراضي)
    'browser_path' => '',
    
    // عنوان الخادم المحلي
    'local_server_url' => 'http://localhost:3000',
    
    // مسار مجلد جلسة WhatsApp
    'session_dir' => __DIR__ . '/../scripts/whatsapp/whatsapp-session',
    
    // مسار ملف السجل
    'log_file' => __DIR__ . '/../logs/whatsapp_automation.log',
    
    // مسار سكريبت إرسال رسائل WhatsApp
    'script_path' => __DIR__ . '/../scripts/whatsapp/send_whatsapp.js',
    
    // رمز البلد الافتراضي
    'default_country_code' => '+20',
    
    // مهلة الجلسة (بالدقائق)
    'session_timeout' => 60,
    
    // تفعيل تذكيرات المواعيد عبر WhatsApp
    'enable_appointment_reminders' => true,
    
    // عدد ساعات التذكير قبل الموعد
    'reminder_hours' => 24,
    
    // تفعيل إشعارات الفواتير
    'enable_invoice_notifications' => true,
    
    // تفعيل إشعارات المدراء
    'enable_admin_notifications' => true,
    
    // أرقام هواتف المدراء (مفصولة بسطر جديد)
    'admin_phone_numbers' => '',
    
    // تفعيل إشعارات الفواتير الجديدة للمدراء
    'notify_new_invoice' => true,
    
    // تفعيل إشعارات نهاية اليوم للمدراء
    'notify_end_day' => true,
    
    // تفعيل إشعارات التقارير للمدراء
    'notify_reports' => true
];
