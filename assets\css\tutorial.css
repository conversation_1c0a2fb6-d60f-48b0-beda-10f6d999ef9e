/* Tutorial System Styles */

/* Tutorial Button */
.tutorial-btn {
    position: fixed;
    top: 20px;
    left: 20px;
    z-index: 1050;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    border-radius: 50%;
    width: 60px;
    height: 60px;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
    transition: all 0.3s ease;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 24px;
}

.tutorial-btn:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.6);
    background: linear-gradient(135deg, #764ba2 0%, #667eea 100%);
}

.tutorial-btn:active {
    transform: scale(0.95);
}

/* Tutorial Modal */
.tutorial-modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    z-index: 1060;
    backdrop-filter: blur(5px);
}

.tutorial-modal.show {
    display: flex;
    align-items: center;
    justify-content: center;
    animation: fadeIn 0.3s ease;
}

.tutorial-content {
    background: white;
    border-radius: 15px;
    max-width: 600px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
    position: relative;
    animation: slideUp 0.3s ease;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.tutorial-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px;
    border-radius: 15px 15px 0 0;
    position: relative;
}

.tutorial-header h3 {
    margin: 0;
    font-size: 24px;
    font-weight: 600;
}

.tutorial-close {
    position: absolute;
    top: 15px;
    left: 15px;
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;
    width: 35px;
    height: 35px;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.tutorial-close:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: rotate(90deg);
}

.tutorial-body {
    padding: 30px;
    line-height: 1.8;
    color: #333;
}

.tutorial-body h4 {
    color: #667eea;
    margin-top: 25px;
    margin-bottom: 15px;
    font-weight: 600;
}

.tutorial-body p {
    margin-bottom: 15px;
    font-size: 16px;
}

.tutorial-body ul {
    margin-bottom: 20px;
    padding-right: 20px;
}

.tutorial-body li {
    margin-bottom: 8px;
    font-size: 15px;
}

.tutorial-highlight {
    background: linear-gradient(120deg, #a8edea 0%, #fed6e3 100%);
    padding: 15px;
    border-radius: 10px;
    margin: 20px 0;
    border-right: 4px solid #667eea;
}

.tutorial-steps {
    counter-reset: step-counter;
}

.tutorial-step {
    counter-increment: step-counter;
    position: relative;
    padding: 15px 15px 15px 50px;
    margin-bottom: 15px;
    background: #f8f9fa;
    border-radius: 10px;
    border-right: 3px solid #667eea;
}

.tutorial-step::before {
    content: counter(step-counter);
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    background: #667eea;
    color: white;
    width: 25px;
    height: 25px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 12px;
}

/* Animations */
@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .tutorial-btn {
        width: 50px;
        height: 50px;
        font-size: 20px;
        top: 15px;
        left: 15px;
    }
    
    .tutorial-content {
        width: 95%;
        max-height: 90vh;
    }
    
    .tutorial-header {
        padding: 15px;
    }
    
    .tutorial-header h3 {
        font-size: 20px;
    }
    
    .tutorial-body {
        padding: 20px;
    }
    
    .tutorial-step {
        padding: 12px 12px 12px 40px;
    }
    
    .tutorial-step::before {
        width: 20px;
        height: 20px;
        font-size: 10px;
        right: 12px;
    }
}

/* Tutorial Badge */
.tutorial-badge {
    position: absolute;
    top: -5px;
    left: -5px;
    background: #ff4757;
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    font-size: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        transform: scale(1);
        opacity: 1;
    }
    50% {
        transform: scale(1.1);
        opacity: 0.7;
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}
