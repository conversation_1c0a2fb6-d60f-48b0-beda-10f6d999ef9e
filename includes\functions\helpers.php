<?php
/**
 * ملف الدوال المساعدة
 * يحتوي على دوال عامة مستخدمة في عدة أماكن بالنظام
 */

// منع الوصول المباشر للملف
if (!defined('BASEPATH')) {
    exit('لا يمكن الوصول المباشر لهذا الملف');
}

/**
 * تنظيف المدخلات لمنع هجمات XSS
 * @param string $data النص المراد تنظيفه
 * @return string النص بعد التنظيف
 */
function sanitizeInput($data) {
    $data = trim($data);
    $data = stripslashes($data);
    $data = htmlspecialchars($data, ENT_QUOTES, 'UTF-8');
    return $data;
}

/**
 * تنظيف مصفوفة من المدخلات
 * @param array $array المصفوفة المراد تنظيفها
 * @return array المصفوفة بعد التنظيف
 */
function sanitizeArray($array) {
    foreach ($array as $key => $value) {
        if (is_array($value)) {
            $array[$key] = sanitizeArray($value);
        } else {
            $array[$key] = sanitizeInput($value);
        }
    }
    return $array;
}

/**
 * تحويل التاريخ من التنسيق الأمريكي إلى التنسيق العربي
 * @param string $date التاريخ بالتنسيق الأمريكي (Y-m-d)
 * @return string التاريخ بالتنسيق العربي (d/m/Y)
 */
function formatDate($date) {
    if (empty($date)) {
        return '';
    }

    return date('d/m/Y', strtotime($date));
}

/**
 * تحويل التاريخ من التنسيق العربي إلى التنسيق الأمريكي
 * @param string $date التاريخ بالتنسيق العربي (d/m/Y)
 * @return string التاريخ بالتنسيق الأمريكي (Y-m-d)
 */
function formatDateToDb($date) {
    if (empty($date)) {
        return null;
    }

    $dateParts = explode('/', $date);
    if (count($dateParts) !== 3) {
        return null;
    }

    return "{$dateParts[2]}-{$dateParts[1]}-{$dateParts[0]}";
}

/**
 * تنسيق الوقت
 * @param string $time الوقت بتنسيق قاعدة البيانات
 * @return string الوقت بالتنسيق المناسب للعرض
 */
function formatTime($time) {
    if (empty($time)) {
        return '';
    }

    return date('h:i A', strtotime($time));
}

/**
 * تنسيق المبلغ المالي
 * @param float $amount المبلغ
 * @param int $decimals عدد المنازل العشرية
 * @param bool $includeSymbol هل يتم تضمين رمز العملة
 * @return string المبلغ بعد التنسيق
 */
function formatMoney($amount, $decimals = 2, $includeSymbol = false) {
    global $db;

    $formattedAmount = number_format($amount, $decimals, '.', ',');

    if ($includeSymbol) {
        // الحصول على رمز العملة من قاعدة البيانات
        $settingsModel = new Settings($db);
        $currencySymbol = $settingsModel->get('system_currency_symbol', 'ر.س');
        return $formattedAmount . ' ' . $currencySymbol;
    }

    return $formattedAmount;
}

/**
 * الحصول على اسم الشهر بالعربية
 * @param int $month رقم الشهر (1-12)
 * @return string اسم الشهر بالعربية
 */
function getArabicMonthName($month) {
    $months = [
        1 => 'يناير',
        2 => 'فبراير',
        3 => 'مارس',
        4 => 'أبريل',
        5 => 'مايو',
        6 => 'يونيو',
        7 => 'يوليو',
        8 => 'أغسطس',
        9 => 'سبتمبر',
        10 => 'أكتوبر',
        11 => 'نوفمبر',
        12 => 'ديسمبر'
    ];

    return $months[intval($month)] ?? '';
}

/**
 * الحصول على اسم اليوم بالعربية
 * @param string $date التاريخ
 * @return string اسم اليوم بالعربية
 */
function getArabicDayName($date) {
    if (empty($date)) {
        return '';
    }

    $days = [
        'Sunday' => 'الأحد',
        'Monday' => 'الإثنين',
        'Tuesday' => 'الثلاثاء',
        'Wednesday' => 'الأربعاء',
        'Thursday' => 'الخميس',
        'Friday' => 'الجمعة',
        'Saturday' => 'السبت'
    ];

    $dayName = date('l', strtotime($date));
    return $days[$dayName] ?? '';
}

/**
 * إنشاء رقم فاتورة فريد
 * @param int $branchId معرف الفرع (اختياري)
 * @return string رقم الفاتورة
 */
function generateInvoiceNumber($branchId = null) {
    global $db;

    // الحصول على إعدادات ترقيم الفواتير
    $settings = new Settings($db);
    $prefix = $settings->get('invoice_prefix') ?: 'INV-';
    $numberLength = (int)$settings->get('invoice_number_length') ?: 5;

    // الحصول على آخر رقم فاتورة من قاعدة البيانات
    $invoiceModel = new Invoice($db);

    // البحث عن أعلى رقم فاتورة موجود
    $sql = "SELECT MAX(CAST(SUBSTRING(invoice_number, " . (strlen($prefix) + 1) . ") AS UNSIGNED)) as max_number
            FROM invoices
            WHERE invoice_number LIKE :prefix";

    $db->prepare($sql);
    $db->bind(':prefix', $prefix . '%');
    $result = $db->fetch(); // استخدام fetch بدلاً من single

    // تحديد الرقم التالي
    $lastNumber = !empty($result['max_number']) ? (int)$result['max_number'] : 0;
    $nextNumber = $lastNumber + 1;

    // تنسيق الرقم مع إضافة أصفار في البداية
    $formattedNumber = str_pad($nextNumber, $numberLength, '0', STR_PAD_LEFT);
    $newInvoiceNumber = $prefix . $formattedNumber;

    // التحقق من أن الرقم غير موجود بالفعل (للتأكد)
    while ($invoiceModel->isInvoiceNumberExists($newInvoiceNumber)) {
        $nextNumber++;
        $formattedNumber = str_pad($nextNumber, $numberLength, '0', STR_PAD_LEFT);
        $newInvoiceNumber = $prefix . $formattedNumber;
    }

    return $newInvoiceNumber;
}

/**
 * تحويل الرقم إلى النص العربي
 * @param float $number الرقم
 * @return string النص العربي للرقم
 */
function numberToArabicWords($number) {
    $ones = [
        0 => 'صفر',
        1 => 'واحد',
        2 => 'اثنان',
        3 => 'ثلاثة',
        4 => 'أربعة',
        5 => 'خمسة',
        6 => 'ستة',
        7 => 'سبعة',
        8 => 'ثمانية',
        9 => 'تسعة',
        10 => 'عشرة',
        11 => 'أحد عشر',
        12 => 'اثنا عشر',
        13 => 'ثلاثة عشر',
        14 => 'أربعة عشر',
        15 => 'خمسة عشر',
        16 => 'ستة عشر',
        17 => 'سبعة عشر',
        18 => 'ثمانية عشر',
        19 => 'تسعة عشر',
    ];

    $tens = [
        2 => 'عشرون',
        3 => 'ثلاثون',
        4 => 'أربعون',
        5 => 'خمسون',
        6 => 'ستون',
        7 => 'سبعون',
        8 => 'ثمانون',
        9 => 'تسعون',
    ];

    $hundreds = [
        1 => 'مائة',
        2 => 'مائتان',
        3 => 'ثلاثمائة',
        4 => 'أربعمائة',
        5 => 'خمسمائة',
        6 => 'ستمائة',
        7 => 'سبعمائة',
        8 => 'ثمانمائة',
        9 => 'تسعمائة',
    ];

    $thousands = [
        1 => 'ألف',
        2 => 'ألفان',
        3 => 'آلاف',
        10 => 'آلاف'
    ];

    $millions = [
        1 => 'مليون',
        2 => 'مليونان',
        3 => 'ملايين',
        10 => 'ملايين'
    ];

    // تقسيم الرقم إلى جزء صحيح وكسري
    $parts = explode('.', (string)$number);
    $integer = (int)$parts[0];
    $fraction = isset($parts[1]) ? (int)$parts[1] : 0;

    if ($integer == 0) {
        $result = $ones[0];
    } else {
        $result = '';

        // الملايين
        $millions_number = floor($integer / 1000000);
        if ($millions_number > 0) {
            if ($millions_number == 1) {
                $result .= $millions[1] . ' ';
            } elseif ($millions_number == 2) {
                $result .= $millions[2] . ' ';
            } elseif ($millions_number >= 3 && $millions_number <= 10) {
                $result .= $ones[$millions_number] . ' ' . $millions[3] . ' ';
            } else {
                $result .= numberToArabicWords($millions_number) . ' ' . $millions[10] . ' ';
            }
            $integer -= $millions_number * 1000000;
        }

        // الآلاف
        $thousands_number = floor($integer / 1000);
        if ($thousands_number > 0) {
            if ($thousands_number == 1) {
                $result .= $thousands[1] . ' ';
            } elseif ($thousands_number == 2) {
                $result .= $thousands[2] . ' ';
            } elseif ($thousands_number >= 3 && $thousands_number <= 10) {
                $result .= $ones[$thousands_number] . ' ' . $thousands[3] . ' ';
            } else {
                $result .= numberToArabicWords($thousands_number) . ' ' . $thousands[10] . ' ';
            }
            $integer -= $thousands_number * 1000;
        }

        // المئات
        $hundreds_number = floor($integer / 100);
        if ($hundreds_number > 0) {
            $result .= $hundreds[$hundreds_number] . ' ';
            $integer -= $hundreds_number * 100;
        }

        // العشرات والآحاد
        if ($integer > 0) {
            if ($integer < 20) {
                $result .= $ones[$integer] . ' ';
            } else {
                $ones_number = $integer % 10;
                $tens_number = floor($integer / 10);

                if ($ones_number > 0) {
                    $result .= $ones[$ones_number] . ' و';
                }

                $result .= $tens[$tens_number] . ' ';
            }
        }
    }

    // إضافة الجزء الكسري إذا وجد
    if ($fraction > 0) {
        $result .= 'و ' . $fraction . '/100';
    }

    return rtrim($result);
}

/**
 * تحويل المبلغ إلى نص عربي مع العملة
 * @param float $amount المبلغ
 * @param string $currency العملة (ريال، دينار، درهم، ...)
 * @return string النص العربي للمبلغ مع العملة
 */
function amountToArabicWords($amount, $currency = null) {
    global $db;

    // الحصول على العملة من قاعدة البيانات إذا لم يتم تمريرها
    if ($currency === null) {
        $settingsModel = new Settings($db);
        $currency = $settingsModel->get('system_currency', 'ريال سعودي');
        // استخراج الكلمة الأولى فقط من العملة (مثلاً: ريال سعودي -> ريال)
        $currencyParts = explode(' ', $currency);
        $currency = $currencyParts[0];
    }
    $fraction = $amount - floor($amount);
    $fraction = round($fraction * 100);

    $result = numberToArabicWords(floor($amount)) . ' ' . $currency;

    if ($fraction > 0) {
        $result .= ' و ' . numberToArabicWords($fraction) . ' هللة';
    }

    return $result;
}

/**
 * توليد كلمة مرور عشوائية
 * @param int $length طول كلمة المرور
 * @return string كلمة المرور العشوائية
 */
function generateRandomPassword($length = 8) {
    $chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*()_+';
    $password = '';

    for ($i = 0; $i < $length; $i++) {
        $password .= $chars[rand(0, strlen($chars) - 1)];
    }

    return $password;
}

/**
 * الحصول على عمر شخص بناءً على تاريخ الميلاد
 * @param string $birthdate تاريخ الميلاد بتنسيق Y-m-d
 * @return int العمر بالسنوات
 */
function getAge($birthdate) {
    if (empty($birthdate)) {
        return 0;
    }

    $dob = new DateTime($birthdate);
    $now = new DateTime();
    $diff = $now->diff($dob);

    return $diff->y;
}

/**
 * تحويل التاريخ والوقت إلى النص المناسب (اليوم، أمس، قبل س ساعة، ...)
 * @param string $datetime التاريخ والوقت بتنسيق Y-m-d H:i:s
 * @return string النص المناسب للتاريخ والوقت
 */
function timeAgo($datetime) {
    if (empty($datetime)) {
        return '';
    }

    $time = strtotime($datetime);
    $now = time();
    $diff = $now - $time;

    if ($diff < 60) {
        return 'منذ لحظات';
    } elseif ($diff < 3600) {
        $minutes = floor($diff / 60);
        return 'منذ ' . $minutes . ' دقيقة' . ($minutes > 1 ? '' : '');
    } elseif ($diff < 86400) {
        $hours = floor($diff / 3600);
        return 'منذ ' . $hours . ' ساعة' . ($hours > 1 ? '' : '');
    } elseif ($diff < 172800) {
        return 'الأمس';
    } elseif ($diff < 604800) {
        $days = floor($diff / 86400);
        return 'منذ ' . $days . ' يوم' . ($days > 1 ? '' : '');
    } else {
        return formatDate($datetime);
    }
}

/**
 * اختصار النص إذا كان طويلاً
 * @param string $text النص المراد اختصاره
 * @param int $length الطول المطلوب
 * @param string $suffix لاحقة النص المختصر
 * @return string النص بعد الاختصار
 */
function truncateText($text, $length = 50, $suffix = '...') {
    if (mb_strlen($text, 'UTF-8') <= $length) {
        return $text;
    }

    return mb_substr($text, 0, $length, 'UTF-8') . $suffix;
}

/**
 * التحقق من تنسيق البريد الإلكتروني
 * @param string $email البريد الإلكتروني
 * @return bool
 */
function isValidEmail($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
}

/**
 * التحقق من تنسيق رقم الهاتف
 * @param string $phone رقم الهاتف
 * @return bool
 */
function isValidPhone($phone) {
    // التحقق من أن رقم الهاتف يتكون من أرقام فقط ويبدأ بـ 05 أو +966 أو 966
    return preg_match('/^(05\d{8}|\+966\d{9}|966\d{9})$/', $phone);
}

/**
 * التحقق من أن المتغير فارغ بشكل صحيح
 * @param mixed $var المتغير المراد فحصه
 * @return bool
 */
function isReallyEmpty($var) {
    return empty($var) && $var !== '0' && $var !== 0;
}

/**
 * الحصول على عنوان IP الحقيقي للزائر
 * @return string عنوان IP
 */
function getRealIp() {
    if (!empty($_SERVER['HTTP_CLIENT_IP'])) {
        $ip = $_SERVER['HTTP_CLIENT_IP'];
    } elseif (!empty($_SERVER['HTTP_X_FORWARDED_FOR'])) {
        $ip = $_SERVER['HTTP_X_FORWARDED_FOR'];
    } else {
        $ip = $_SERVER['REMOTE_ADDR'];
    }

    return $ip;
}

/**
 * تحويل المسار إلى مسار مطلق
 * @param string $path المسار
 * @return string المسار المطلق
 */
function getAbsolutePath($path) {
    // استبدال الشرطات المزدوجة بشرطة واحدة
    $path = str_replace('//', '/', $path);

    // تحويل المسار النسبي إلى مسار مطلق
    if (strpos($path, '/') !== 0) {
        $path = '/' . $path;
    }

    return $path;
}

/**
 * تحويل النص إلى سبيكة نصية (slug)
 * @param string $text النص
 * @param string $separator الفاصل بين الكلمات
 * @return string السبيكة النصية
 */
function slugify($text, $separator = '-') {
    // إزالة الأحرف الخاصة
    $text = preg_replace('~[^\p{L}\p{N}]+~u', $separator, $text);

    // إزالة الفواصل المكررة
    $text = preg_replace('~[' . preg_quote($separator, '~') . ']{2,}~', $separator, $text);

    // اقتطاع الفواصل من البداية والنهاية
    $text = trim($text, $separator);

    // تحويل إلى أحرف صغيرة
    $text = mb_strtolower($text);

    return $text;
}

/**
 * إعادة توجيه إلى الصفحة المحددة
 * @param string $url عنوان الصفحة
 * @param int $status_code كود الحالة HTTP
 */
function redirect($url, $status_code = 302) {
    header('Location: ' . $url, true, $status_code);
    exit;
}

/**
 * طباعة رسالة نجاح في الجلسة
 * @param string $message الرسالة
 */
function setSuccessMessage($message) {
    $_SESSION['success_message'] = $message;
}

/**
 * طباعة رسالة خطأ في الجلسة
 * @param string $message الرسالة
 */
function setErrorMessage($message) {
    $_SESSION['error_message'] = $message;
}
/**
 * الحصول على النص الوصفي لدور المستخدم
 * @return string النص الوصفي لدور المستخدم
 */
function getUserRoleText() {
    if (!isset($_SESSION['user_role'])) {
        return 'غير معروف';
    }

    switch ($_SESSION['user_role']) {
        case 'admin':
            return 'مدير النظام';
        case 'manager':
            return 'مدير';
        case 'cashier':
            return 'كاشير';
        case 'employee':
            return 'موظف';
        default:
            return $_SESSION['user_role'];
    }
}
/**
 * عرض رسائل الجلسة (نجاح، خطأ)
 * @return string النص HTML للرسائل
 */
function displayMessages() {
    $output = '';

    if (isset($_SESSION['success_message'])) {
        $output .= '<div class="alert alert-success alert-dismissible fade show" role="alert">';
        $output .= $_SESSION['success_message'];
        $output .= '<button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>';
        $output .= '</div>';
        unset($_SESSION['success_message']);
    }

    if (isset($_SESSION['error_message'])) {
        $output .= '<div class="alert alert-danger alert-dismissible fade show" role="alert">';
        $output .= $_SESSION['error_message'];
        $output .= '<button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>';
        $output .= '</div>';
        unset($_SESSION['error_message']);
    }

    return $output;
}

/**
 * التحقق من نوع الطلب (GET، POST، ...)
 * @param string $type النوع المطلوب التحقق منه
 * @return bool
 */
function isRequestMethod($type) {
    return $_SERVER['REQUEST_METHOD'] === strtoupper($type);
}

/**
 * الحصول على القيمة من المتغيرات العامة (_GET, _POST) مع التنظيف التلقائي
 * @param string $key اسم المتغير
 * @param string $method طريقة الطلب (GET, POST)
 * @param mixed $default القيمة الافتراضية إذا لم يوجد المتغير
 * @return mixed قيمة المتغير بعد التنظيف
 */
function input($key, $method = 'POST', $default = null) {
    $method = strtoupper($method);

    if ($method === 'GET' && isset($_GET[$key])) {
        return sanitizeInput($_GET[$key]);
    } elseif ($method === 'POST' && isset($_POST[$key])) {
        return sanitizeInput($_POST[$key]);
    }

    return $default;
}

/**
 * الحصول على قيمة من مصفوفة بدون خطأ إذا لم يوجد المفتاح
 * @param array $array المصفوفة
 * @param string|int $key المفتاح
 * @param mixed $default القيمة الافتراضية
 * @return mixed
 */
function arrayGet($array, $key, $default = null) {
    return isset($array[$key]) ? $array[$key] : $default;
}
