<?php
/**
 * صفحة تقارير الموظفين
 */

// تعريف ثابت لمنع الوصول المباشر للملفات المرتبطة
define('BASEPATH', true);

// استدعاء ملفات التهيئة
require_once '../../config/init.php';

// التحقق من تسجيل الدخول
redirectIfNotLoggedIn();

// التحقق من الصلاحيات
if (!hasPermission('reports_employees')) {
    $_SESSION['error_message'] = 'ليس لديك صلاحية لعرض تقارير الموظفين';
    header('Location: ' . BASE_URL . 'pages/dashboard.php');
    exit;
}

// عنوان الصفحة
$pageTitle = 'تقارير الموظفين';

// استدعاء رأس الصفحة
include '../../includes/templates/header.php';

// إنشاء كائنات النماذج
$employeeModel = new Employee($db);
$branchModel = new Branch($db);

// الحصول على قائمة الفروع للفلتر
$branches = [];
if (hasPermission('admin')) {
    $branches = $branchModel->getBranches(['is_active' => 1]);
}

// تحديد نطاق تاريخ افتراضي (الشهر الحالي)
$today = date('Y-m-d');
$firstDayOfMonth = date('Y-m-01');
$lastDayOfMonth = date('Y-m-t');

// الحصول على إعدادات العملة من قاعدة البيانات
$settingsModel = new Settings($db);
$currencySymbol = $settingsModel->get('system_currency_symbol', 'ر.س');
$currency = $settingsModel->get('system_currency', 'ريال سعودي');

// البيانات الافتراضية للفلتر
$filters = [
    'start_date' => $firstDayOfMonth,
    'end_date' => $lastDayOfMonth,
    'branch_id' => $_SESSION['user_branch_id'] ?? null,
    'report_type' => 'performance'
];

// الحصول على السنة الحالية والشهر الحالي للفلتر
$currentYear = date('Y');
$currentMonth = date('m');

// قائمة الشهور للفلتر
$months = [
    '01' => 'يناير',
    '02' => 'فبراير',
    '03' => 'مارس',
    '04' => 'أبريل',
    '05' => 'مايو',
    '06' => 'يونيو',
    '07' => 'يوليو',
    '08' => 'أغسطس',
    '09' => 'سبتمبر',
    '10' => 'أكتوبر',
    '11' => 'نوفمبر',
    '12' => 'ديسمبر'
];

// جلب قائمة السنوات المتاحة
$years = [];
$startYear = 2024; // سنة بداية النظام
for ($year = $startYear; $year <= $currentYear; $year++) {
    $years[] = $year;
}
?>
<!-- المحتوى الرئيسي للصفحة -->
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- بطاقة نوع التقرير -->
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-white py-3">
                    <h5 class="mb-0">نوع التقرير</h5>
                </div>
                <div class="card-body">
                    <div class="btn-group w-100">
                        <button type="button" class="btn btn-outline-primary active" data-report-type="performance">أداء الموظفين</button>
                        <button type="button" class="btn btn-outline-primary" data-report-type="attendance">حضور الموظفين</button>
                        <button type="button" class="btn btn-outline-primary" data-report-type="salaries">رواتب الموظفين</button>
                    </div>
                </div>
            </div>
Copy        <!-- بطاقة فلاتر التقرير - الأداء -->
        <div class="card shadow-sm mb-4" id="performanceFilters">
            <div class="card-header bg-white py-3">
                <h5 class="mb-0">خيارات تقرير الأداء</h5>
            </div>
            <div class="card-body">
                <form id="performanceForm" method="post" class="row align-items-end">
                    <input type="hidden" name="report_type" value="performance">

                    <div class="col-md-3 mb-3">
                        <label for="performanceDateRange" class="form-label">نطاق التاريخ</label>
                        <select class="form-select" id="performanceDateRange" name="date_range">
                            <option value="custom">تخصيص</option>
                            <option value="today">اليوم</option>
                            <option value="yesterday">الأمس</option>
                            <option value="this_week">هذا الأسبوع</option>
                            <option value="last_week">الأسبوع الماضي</option>
                            <option value="this_month" selected>هذا الشهر</option>
                            <option value="last_month">الشهر الماضي</option>
                            <option value="this_year">هذه السنة</option>
                        </select>
                    </div>

                    <div class="col-md-3 mb-3">
                        <label for="performanceStartDate" class="form-label">من تاريخ</label>
                        <input type="date" class="form-control" id="performanceStartDate" name="start_date" value="<?php echo $firstDayOfMonth; ?>">
                    </div>

                    <div class="col-md-3 mb-3">
                        <label for="performanceEndDate" class="form-label">إلى تاريخ</label>
                        <input type="date" class="form-control" id="performanceEndDate" name="end_date" value="<?php echo $lastDayOfMonth; ?>">
                    </div>

                    <?php if (hasPermission('admin')): ?>
                    <div class="col-md-3 mb-3">
                        <label for="performanceBranchId" class="form-label">الفرع</label>
                        <select class="form-select" id="performanceBranchId" name="branch_id">
                            <option value="">جميع الفروع</option>
                            <?php foreach($branches as $branch): ?>
                            <option value="<?php echo $branch['id']; ?>" <?php echo ($branch['id'] == $_SESSION['user_branch_id']) ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($branch['name']); ?>
                            </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <?php else: ?>
                    <input type="hidden" name="branch_id" value="<?php echo $_SESSION['user_branch_id']; ?>">
                    <?php endif; ?>

                    <div class="col-md-3 mb-3">
                        <button type="submit" class="btn btn-primary w-100">
                            <i class="fas fa-filter me-1"></i> عرض التقرير
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- بطاقة فلاتر التقرير - الحضور -->
        <div class="card shadow-sm mb-4 d-none" id="attendanceFilters">
            <div class="card-header bg-white py-3">
                <h5 class="mb-0">خيارات تقرير الحضور</h5>
            </div>
            <div class="card-body">
                <form id="attendanceForm" method="post" class="row align-items-end">
                    <input type="hidden" name="report_type" value="attendance">

                    <div class="col-md-3 mb-3">
                        <label for="attendanceMonth" class="form-label">الشهر</label>
                        <select class="form-select" id="attendanceMonth" name="month">
                            <?php foreach($months as $key => $month): ?>
                            <option value="<?php echo $key; ?>" <?php echo ($key == $currentMonth) ? 'selected' : ''; ?>>
                                <?php echo $month; ?>
                            </option>
                            <?php endforeach; ?>
                        </select>
                    </div>

                    <div class="col-md-3 mb-3">
                        <label for="attendanceYear" class="form-label">السنة</label>
                        <select class="form-select" id="attendanceYear" name="year">
                            <?php foreach($years as $year): ?>
                            <option value="<?php echo $year; ?>" <?php echo ($year == $currentYear) ? 'selected' : ''; ?>>
                                <?php echo $year; ?>
                            </option>
                            <?php endforeach; ?>
                        </select>
                    </div>

                    <?php if (hasPermission('admin')): ?>
                    <div class="col-md-3 mb-3">
                        <label for="attendanceBranchId" class="form-label">الفرع</label>
                        <select class="form-select" id="attendanceBranchId" name="branch_id">
                            <option value="">جميع الفروع</option>
                            <?php foreach($branches as $branch): ?>
                            <option value="<?php echo $branch['id']; ?>" <?php echo ($branch['id'] == $_SESSION['user_branch_id']) ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($branch['name']); ?>
                            </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <?php else: ?>
                    <input type="hidden" name="branch_id" value="<?php echo $_SESSION['user_branch_id']; ?>">
                    <?php endif; ?>

                    <div class="col-md-3 mb-3">
                        <button type="submit" class="btn btn-primary w-100">
                            <i class="fas fa-filter me-1"></i> عرض التقرير
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- بطاقة فلاتر التقرير - الرواتب -->
        <div class="card shadow-sm mb-4 d-none" id="salariesFilters">
            <div class="card-header bg-white py-3">
                <h5 class="mb-0">خيارات تقرير الرواتب</h5>
            </div>
            <div class="card-body">
                <form id="salariesForm" method="post" class="row align-items-end">
                    <input type="hidden" name="report_type" value="salaries">

                    <div class="col-md-3 mb-3">
                        <label for="salariesMonth" class="form-label">الشهر</label>
                        <select class="form-select" id="salariesMonth" name="month">
                            <option value="all">جميع الشهور</option>
                            <?php foreach($months as $key => $month): ?>
                            <option value="<?php echo $key; ?>" <?php echo ($key == $currentMonth) ? 'selected' : ''; ?>>
                                <?php echo $month; ?>
                            </option>
                            <?php endforeach; ?>
                        </select>
                    </div>

                    <div class="col-md-3 mb-3">
                        <label for="salariesYear" class="form-label">السنة</label>
                        <select class="form-select" id="salariesYear" name="year">
                            <?php foreach($years as $year): ?>
                            <option value="<?php echo $year; ?>" <?php echo ($year == $currentYear) ? 'selected' : ''; ?>>
                                <?php echo $year; ?>
                            </option>
                            <?php endforeach; ?>
                        </select>
                    </div>

                    <div class="col-md-3 mb-3">
                        <label for="salariesPaymentStatus" class="form-label">حالة الدفع</label>
                        <select class="form-select" id="salariesPaymentStatus" name="payment_status">
                            <option value="">الكل</option>
                            <option value="paid">مدفوع</option>
                            <option value="unpaid">غير مدفوع</option>
                        </select>
                    </div>

                    <?php if (hasPermission('admin')): ?>
                    <div class="col-md-3 mb-3">
                        <label for="salariesBranchId" class="form-label">الفرع</label>
                        <select class="form-select" id="salariesBranchId" name="branch_id">
                            <option value="">جميع الفروع</option>
                            <?php foreach($branches as $branch): ?>
                            <option value="<?php echo $branch['id']; ?>" <?php echo ($branch['id'] == $_SESSION['user_branch_id']) ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($branch['name']); ?>
                            </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <?php else: ?>
                    <input type="hidden" name="branch_id" value="<?php echo $_SESSION['user_branch_id']; ?>">
                    <?php endif; ?>

                    <div class="col-md-3 mb-3">
                        <button type="submit" class="btn btn-primary w-100">
                            <i class="fas fa-filter me-1"></i> عرض التقرير
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <div class="alert alert-info d-none" id="loadingAlert">
            <i class="fas fa-spinner fa-spin me-1"></i> جاري تحميل التقرير...
        </div>

        <div class="alert alert-danger d-none" id="errorAlert"></div>

        <!-- ملخص تقرير الأداء -->
        <div class="row d-none" id="performanceSummary">
            <div class="col-md-3 mb-4">
                <div class="card shadow-sm">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="text-muted mb-1">الموظفين النشطين</h6>
                                <h3 class="display-6 fw-bold mb-0" id="activeEmployees">0</h3>
                            </div>
                            <div class="bg-primary bg-opacity-10 p-3 rounded">
                                <i class="fas fa-user-check text-primary fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-3 mb-4">
                <div class="card shadow-sm">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="text-muted mb-1">إجمالي الخدمات</h6>
                                <h3 class="display-6 fw-bold mb-0" id="totalServices">0</h3>
                            </div>
                            <div class="bg-success bg-opacity-10 p-3 rounded">
                                <i class="fas fa-cut text-success fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-3 mb-4">
                <div class="card shadow-sm">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="text-muted mb-1">إجمالي الإيرادات</h6>
                                <h3 class="display-6 fw-bold mb-0" id="totalRevenue">0.00</h3>
                            </div>
                            <div class="bg-info bg-opacity-10 p-3 rounded">
                                <i class="fas fa-money-bill-wave text-info fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-3 mb-4">
                <div class="card shadow-sm">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="text-muted mb-1">متوسط الخدمات</h6>
                                <h3 class="display-6 fw-bold mb-0" id="averageServices">0</h3>
                            </div>
                            <div class="bg-warning bg-opacity-10 p-3 rounded">
                                <i class="fas fa-calculator text-warning fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- ملخص تقرير الحضور -->
        <div class="row d-none" id="attendanceSummary">
            <div class="col-md-3 mb-4">
                <div class="card shadow-sm">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="text-muted mb-1">الموظفين النشطين</h6>
                                <h3 class="display-6 fw-bold mb-0" id="attendanceActiveEmployees">0</h3>
                            </div>
                            <div class="bg-primary bg-opacity-10 p-3 rounded">
                                <i class="fas fa-user-check text-primary fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-3 mb-4">
                <div class="card shadow-sm">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="text-muted mb-1">أيام الحضور</h6>
                                <h3 class="display-6 fw-bold mb-0" id="workingDays">0</h3>
                            </div>
                            <div class="bg-success bg-opacity-10 p-3 rounded">
                                <i class="fas fa-calendar-check text-success fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-3 mb-4">
                <div class="card shadow-sm">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="text-muted mb-1">أيام العمل الفعلية</h6>
                                <h3 class="display-6 fw-bold mb-0" id="actualWorkingDays">0</h3>
                            </div>
                            <div class="bg-warning bg-opacity-10 p-3 rounded">
                                <i class="fas fa-calendar-day text-warning fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-3 mb-4">
                <div class="card shadow-sm">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="text-muted mb-1">معدل حضور</h6>
                                <h3 class="display-6 fw-bold mb-0" id="attendanceRate">0%</h3>
                            </div>
                            <div class="bg-info bg-opacity-10 p-3 rounded">
                                <i class="fas fa-chart-pie text-info fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-3 mb-4">
                <div class="card shadow-sm">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="text-muted mb-1">متوسط ساعات العمل</h6>
                                <h3 class="display-6 fw-bold mb-0" id="averageHours">0</h3>
                            </div>
                            <div class="bg-warning bg-opacity-10 p-3 rounded">
                                <i class="fas fa-clock text-warning fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- ملخص تقرير الرواتب -->
        <div class="row d-none" id="salariesSummary">
            <div class="col-md-3 mb-4">
                <div class="card shadow-sm">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="text-muted mb-1">عدد الموظفين</h6>
                                <h3 class="display-6 fw-bold mb-0" id="salaryEmployeeCount">0</h3>
                            </div>
                            <div class="bg-primary bg-opacity-10 p-3 rounded">
                                <i class="fas fa-users text-primary fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-3 mb-4">
                <div class="card shadow-sm">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="text-muted mb-1">إجمالي الرواتب</h6>
                                <h3 class="display-6 fw-bold mb-0" id="totalSalaries">0.00</h3>
                            </div>
                            <div class="bg-success bg-opacity-10 p-3 rounded">
                                <i class="fas fa-money-bill-wave text-success fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-3 mb-4">
                <div class="card shadow-sm">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="text-muted mb-1">رواتب ثابتة</h6>
                                <h3 class="display-6 fw-bold mb-0" id="fixedSalaries">0.00</h3>
                            </div>
                            <div class="bg-info bg-opacity-10 p-3 rounded">
                                <i class="fas fa-hand-holding-usd text-info fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-3 mb-4">
                <div class="card shadow-sm">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="text-muted mb-1">عمولات</h6>
                                <h3 class="display-6 fw-bold mb-0" id="commissionSalaries">0.00</h3>
                            </div>
                            <div class="bg-warning bg-opacity-10 p-3 rounded">
                                <i class="fas fa-percentage text-warning fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- الرسوم البيانية للتقرير - الأداء -->
        <div class="row d-none" id="performanceCharts">
            <div class="col-md-6 mb-4">
                <div class="card shadow-sm">
                    <div class="card-header bg-white py-3">
                        <h5 class="mb-0">أعلى الموظفين أداءاً</h5>
                    </div>
                    <div class="card-body">
                        <canvas id="topEmployeesChart" height="300"></canvas>
                    </div>
                </div>
            </div>

            <div class="col-md-6 mb-4">
                <div class="card shadow-sm">
                    <div class="card-header bg-white py-3">
                        <h5 class="mb-0">توزيع الخدمات على الموظفين</h5>
                    </div>
                    <div class="card-body">
                        <canvas id="servicesDistributionChart" height="300"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- الرسوم البيانية للتقرير - الحضور -->
        <div class="row d-none" id="attendanceCharts">
            <div class="col-md-6 mb-4">
                <div class="card shadow-sm">
                    <div class="card-header bg-white py-3">
                        <h5 class="mb-0">معدل الحضور اليومي</h5>
                    </div>
                    <div class="card-body">
                        <canvas id="dailyAttendanceChart" height="300"></canvas>
                    </div>
                </div>
            </div>

            <div class="col-md-6 mb-4">
                <div class="card shadow-sm">
                    <div class="card-header bg-white py-3">
                        <h5 class="mb-0">ساعات العمل للموظفين</h5>
                    </div>
                    <div class="card-body">
                        <canvas id="workingHoursChart" height="300"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- الرسوم البيانية للتقرير - الرواتب -->
        <div class="row d-none" id="salariesCharts">
            <div class="col-md-6 mb-4">
                <div class="card shadow-sm">
                    <div class="card-header bg-white py-3">
                        <h5 class="mb-0">توزيع الرواتب</h5>
                    </div>
                    <div class="card-body">
                        <canvas id="salaryDistributionChart" height="300"></canvas>
                    </div>
                </div>
            </div>

            <div class="col-md-6 mb-4">
                <div class="card shadow-sm">
                    <div class="card-header bg-white py-3">
                        <h5 class="mb-0">توزيع أنواع الرواتب</h5>
                    </div>
                    <div class="card-body">
                        <canvas id="salaryTypesChart" height="300"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- جدول تفاصيل التقرير - الأداء -->
        <div class="card shadow-sm mb-4 d-none" id="performanceTable">
            <div class="card-header bg-white py-3 d-flex justify-content-between align-items-center">
                <h5 class="mb-0">تفاصيل أداء الموظفين</h5>
                <div>
                    <button type="button" class="btn btn-sm btn-outline-success" id="exportPerformanceExcel">
                        <i class="fas fa-file-excel me-1"></i> تصدير Excel
                    </button>
                    <button type="button" class="btn btn-sm btn-outline-danger" id="exportPerformancePdf">
                        <i class="fas fa-file-pdf me-1"></i> تصدير PDF
                    </button>
                    <button type="button" class="btn btn-sm btn-outline-secondary" id="printPerformanceReport">
                        <i class="fas fa-print me-1"></i> طباعة
                    </button>
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-bordered table-striped table-hover" id="performanceReportTable">
                        <thead class="table-light">
                            <tr>
                                <th>الموظف</th>
                                <th>الفرع</th>
                                <th>المنصب</th>
                                <th>عدد الخدمات</th>
                                <th>الإيرادات</th>
                                <th>النسبة من الإجمالي</th>
                            </tr>
                        </thead>
                        <tbody id="performanceTableBody">
                            <!-- ستتم تعبئة البيانات هنا عن طريق JavaScript -->
                        </tbody>
                        <tfoot class="table-light">
                            <tr>
                                <th colspan="3">الإجمالي</th>
                                <th id="footerTotalServices">0</th>
                                <th id="footerTotalRevenue">0.00</th>
                                <th>100%</th>
                            </tr>
                        </tfoot>
                    </table>
                </div>
            </div>
        </div>

        <!-- جدول تفاصيل التقرير - الحضور -->
        <div class="card shadow-sm mb-4 d-none" id="attendanceTable">
            <div class="card-header bg-white py-3 d-flex justify-content-between align-items-center">
                <h5 class="mb-0">تفاصيل حضور الموظفين</h5>
                <div>
                    <button type="button" class="btn btn-sm btn-outline-success" id="exportAttendanceExcel">
                        <i class="fas fa-file-excel me-1"></i> تصدير Excel
                    </button>
                    <button type="button" class="btn btn-sm btn-outline-danger" id="exportAttendancePdf">
                        <i class="fas fa-file-pdf me-1"></i> تصدير PDF
                    </button>
                    <button type="button" class="btn btn-sm btn-outline-secondary" id="printAttendanceReport">
                        <i class="fas fa-print me-1"></i> طباعة
                    </button>
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-bordered table-striped table-hover" id="attendanceReportTable">
                        <thead class="table-light">
                            <tr>
                                <th>الموظف</th>
                                <th>الفرع</th>
                                <th>أيام الحضور</th>
                                <th>أيام الغياب</th>
                                <th>إجمالي ساعات العمل</th>
                                <th>متوسط ساعات العمل</th>
                            </tr>
                        </thead>
                        <tbody id="attendanceTableBody">
                            <!-- ستتم تعبئة البيانات هنا عن طريق JavaScript -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- جدول تفاصيل التقرير - الرواتب -->
        <div class="card shadow-sm mb-4 d-none" id="salariesTable">
            <div class="card-header bg-white py-3 d-flex justify-content-between align-items-center">
                <h5 class="mb-0">تفاصيل رواتب الموظفين</h5>
                <div>
                    <button type="button" class="btn btn-sm btn-outline-success" id="exportSalariesExcel">
                        <i class="fas fa-file-excel me-1"></i> تصدير Excel
                    </button>
                    <button type="button" class="btn btn-sm btn-outline-danger" id="exportSalariesPdf">
                        <i class="fas fa-file-pdf me-1"></i> تصدير PDF
                    </button>
                    <button type="button" class="btn btn-sm btn-outline-secondary" id="printSalariesReport">
                        <i class="fas fa-print me-1"></i> طباعة
                    </button>
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-bordered table-striped table-hover" id="salariesReportTable">
                        <thead class="table-light">
                            <tr>
                                <th>الموظف</th>
                                <th>الفرع</th>
                                <th>نوع الراتب</th>
                                <th>راتب ثابت</th>
                                <th>عمولة</th>
                                <th>مكافآت</th>
                                <th>خصومات</th>
                                <th>الإجمالي</th>
                                <th>حالة الدفع</th>
                            </tr>
                        </thead>
                        <tbody id="salariesTableBody">
                            <!-- ستتم تعبئة البيانات هنا عن طريق JavaScript -->
                        </tbody>
                        <tfoot class="table-light">
                            <tr>
                                <th colspan="3">الإجمالي</th>
                                <th id="footerFixedSalaries">0.00</th>
                                <th id="footerCommission">0.00</th>
                                <th id="footerBonuses">0.00</th>
                                <th id="footerDeductions">0.00</th>
                                <th id="footerTotalSalaries">0.00</th>
                                <th></th>
                            </tr>
                        </tfoot>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
</div>
<!-- استدعاء قالب الفوتر -->
<?php include '../../includes/templates/footer.php'; ?>
<!-- مكتبة لتصدير الجداول -->
<script src="https://cdn.jsdelivr.net/npm/tableexport@5.2.0/dist/js/tableexport.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/xlsx@0.18.5/dist/xlsx.full.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/file-saver@2.0.5/dist/FileSaver.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf-autotable/3.5.28/jspdf.plugin.autotable.min.js"></script>
<script>
// تعريف المتغيرات العامة
const BASE_URL = '<?php echo BASE_URL; ?>';

$(document).ready(function() {
    // متغيرات المخططات
    let topEmployeesChart, servicesDistributionChart;
    let dailyAttendanceChart, workingHoursChart;
    let salaryDistributionChart, salaryTypesChart;

    // نوع التقرير الحالي
    let currentReportType = 'performance';

    // تبديل نوع التقرير
    $('.btn-group button').click(function() {
        $('.btn-group button').removeClass('active');
        $(this).addClass('active');

        const reportType = $(this).data('report-type');
        currentReportType = reportType;

        // إخفاء جميع أقسام التقارير
        $('#performanceFilters, #attendanceFilters, #salariesFilters').addClass('d-none');
        $('#performanceSummary, #attendanceSummary, #salariesSummary').addClass('d-none');
        $('#performanceCharts, #attendanceCharts, #salariesCharts').addClass('d-none');
        $('#performanceTable, #attendanceTable, #salariesTable').addClass('d-none');

        // إظهار القسم المناسب
        switch(reportType) {
            case 'performance':
                $('#performanceFilters').removeClass('d-none');
                $('#performanceForm').trigger('submit');
                break;

            case 'attendance':
                $('#attendanceFilters').removeClass('d-none');
                $('#attendanceForm').trigger('submit');
                break;

            case 'salaries':
                $('#salariesFilters').removeClass('d-none');
                $('#salariesForm').trigger('submit');
                break;
        }
    });

    // تغيير نطاق التاريخ - الأداء
    $('#performanceDateRange').change(function() {
        updateDateRange($(this).val(), 'performance');
    });

    // تنسيق التاريخ لـ YYYY-MM-DD
    function formatDate(date) {
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        return `${year}-${month}-${day}`;
    }

    // تحديث نطاق التاريخ
    function updateDateRange(value, prefix) {
        const today = new Date();

        switch(value) {
            case 'today':
                $(`#${prefix}StartDate`).val(formatDate(today));
                $(`#${prefix}EndDate`).val(formatDate(today));
                break;

            case 'yesterday':
                const yesterday = new Date(today);
                yesterday.setDate(yesterday.getDate() - 1);
                $(`#${prefix}StartDate`).val(formatDate(yesterday));
                $(`#${prefix}EndDate`).val(formatDate(yesterday));
                break;

            case 'this_week':
                const thisWeekStart = new Date(today);
                const day = thisWeekStart.getDay() || 7;
                if (day !== 1) {
                    thisWeekStart.setDate(thisWeekStart.getDate() - (day - 1));
                }
                $(`#${prefix}StartDate`).val(formatDate(thisWeekStart));
                $(`#${prefix}EndDate`).val(formatDate(today));
                break;

            case 'last_week':
                const lastWeekStart = new Date(today);
                lastWeekStart.setDate(lastWeekStart.getDate() - 7 - (lastWeekStart.getDay() - 1));
                const lastWeekEnd = new Date(lastWeekStart);
                lastWeekEnd.setDate(lastWeekEnd.getDate() + 6);
                $(`#${prefix}StartDate`).val(formatDate(lastWeekStart));
                $(`#${prefix}EndDate`).val(formatDate(lastWeekEnd));
                break;

            case 'this_month':
                const thisMonthStart = new Date(today.getFullYear(), today.getMonth(), 1);
                const thisMonthEnd = new Date(today.getFullYear(), today.getMonth() + 1, 0);
                $(`#${prefix}StartDate`).val(formatDate(thisMonthStart));
                $(`#${prefix}EndDate`).val(formatDate(thisMonthEnd));
                break;

            case 'last_month':
                const lastMonthStart = new Date(today.getFullYear(), today.getMonth() - 1, 1);
                const lastMonthEnd = new Date(today.getFullYear(), today.getMonth(), 0);
                $(`#${prefix}StartDate`).val(formatDate(lastMonthStart));
                $(`#${prefix}EndDate`).val(formatDate(lastMonthEnd));
                break;

            case 'this_year':
                const thisYearStart = new Date(today.getFullYear(), 0, 1);
                const thisYearEnd = new Date(today.getFullYear(), 11, 31);
                $(`#${prefix}StartDate`).val(formatDate(thisYearStart));
                $(`#${prefix}EndDate`).val(formatDate(thisYearEnd));
                break;

            case 'custom':
                // ابق على القيم الحالية
                break;
        }
    }

    // تقديم نماذج التقارير
    $('#performanceForm').on('submit', function(e) {
        e.preventDefault();
        loadPerformanceReport();
    });

    $('#attendanceForm').on('submit', function(e) {
        e.preventDefault();
        loadAttendanceReport();
    });

    $('#salariesForm').on('submit', function(e) {
        e.preventDefault();
        loadSalariesReport();
    });

    // تحميل تقرير الأداء
    function loadPerformanceReport() {
        try {
            const performanceForm = document.getElementById('performanceForm');
            if (!performanceForm) {
                console.error('لم يتم العثور على نموذج الأداء');
                $('#errorAlert').removeClass('d-none').text('لم يتم العثور على نموذج الأداء');
                return;
            }

            const formData = new FormData(performanceForm);

            // إظهار تنبيه التحميل
            $('#loadingAlert').removeClass('d-none');
            $('#errorAlert').addClass('d-none');

            // التحقق من وجود BASE_URL
            if (typeof BASE_URL === 'undefined' || BASE_URL === '') {
                console.error('BASE_URL غير معرف أو فارغ');
                $('#loadingAlert').addClass('d-none');
                $('#errorAlert').removeClass('d-none').text('خطأ في تهيئة النظام');
                return;
            }

            // طباعة بيانات النموذج للتحقق
            console.log('Performance form data:', {
                report_type: 'performance',
                start_date: formData.get('start_date'),
                end_date: formData.get('end_date'),
                branch_id: formData.get('branch_id')
            });

            // جلب البيانات من API
            $.ajax({
                url: `${BASE_URL}api/reports.php?type=employees`,
                type: 'POST',
                data: {
                    report_type: 'performance',
                    start_date: formData.get('start_date'),
                    end_date: formData.get('end_date'),
                    branch_id: formData.get('branch_id')
                },
                dataType: 'json',
                success: function(response) {
                    // إخفاء تنبيه التحميل
                    $('#loadingAlert').addClass('d-none');

                    // طباعة الاستجابة للتحقق
                    console.log('API response:', response);

                    if (response && response.success) {
                        // تحديث واجهة التقرير
                        updatePerformanceReport(response.data);
                    } else {
                        const errorMessage = response && response.message ? response.message : 'حدث خطأ أثناء تحميل التقرير';
                        $('#errorAlert').removeClass('d-none').text(errorMessage);
                    }
                },
                error: function(xhr, status, error) {
                    // إخفاء تنبيه التحميل وإظهار الخطأ
                    $('#loadingAlert').addClass('d-none');
                    $('#errorAlert').removeClass('d-none').text('حدث خطأ أثناء الاتصال بالخادم');
                    console.error('AJAX error:', status, error);
                    console.error('Response text:', xhr.responseText);
                }
            });
        } catch (e) {
            console.error('Error in loadPerformanceReport:', e);
            $('#loadingAlert').addClass('d-none');
            $('#errorAlert').removeClass('d-none').text('حدث خطأ غير متوقع');
        }
    }

    // تحميل تقرير الحضور
    function loadAttendanceReport() {
        try {
            const attendanceForm = document.getElementById('attendanceForm');
            if (!attendanceForm) {
                console.error('لم يتم العثور على نموذج الحضور');
                $('#errorAlert').removeClass('d-none').text('لم يتم العثور على نموذج الحضور');
                return;
            }

            const formData = new FormData(attendanceForm);

            // إظهار تنبيه التحميل
            $('#loadingAlert').removeClass('d-none');
            $('#errorAlert').addClass('d-none');

            // التحقق من وجود BASE_URL
            if (typeof BASE_URL === 'undefined' || BASE_URL === '') {
                console.error('BASE_URL غير معرف أو فارغ');
                $('#loadingAlert').addClass('d-none');
                $('#errorAlert').removeClass('d-none').text('خطأ في تهيئة النظام');
                return;
            }

            // طباعة بيانات النموذج للتحقق
            console.log('Attendance form data:', {
                month: formData.get('month'),
                year: formData.get('year'),
                branch_id: formData.get('branch_id')
            });

            // جلب البيانات من API
            $.ajax({
                url: `${BASE_URL}api/employees.php`,
                type: 'GET',
                data: {
                    action: 'get_attendance_report',
                    month: formData.get('month'),
                    year: formData.get('year'),
                    branch_id: formData.get('branch_id')
                },
                dataType: 'json',
                success: function(response) {
                    // إخفاء تنبيه التحميل
                    $('#loadingAlert').addClass('d-none');

                    // طباعة الاستجابة للتحقق
                    console.log('Attendance API response:', response);

                    if (response && response.status === 'success') {
                        // تحديث واجهة التقرير
                        updateAttendanceReport(response.data);
                    } else {
                        const errorMessage = response && response.message ? response.message : 'حدث خطأ أثناء تحميل التقرير';
                        $('#errorAlert').removeClass('d-none').text(errorMessage);
                    }
                },
                error: function(xhr, status, error) {
                    // إخفاء تنبيه التحميل وإظهار الخطأ
                    $('#loadingAlert').addClass('d-none');
                    $('#errorAlert').removeClass('d-none').text('حدث خطأ أثناء الاتصال بالخادم');
                    console.error('AJAX error:', status, error);
                    console.error('Response text:', xhr.responseText);
                }
            });
        } catch (e) {
            console.error('Error in loadAttendanceReport:', e);
            $('#loadingAlert').addClass('d-none');
            $('#errorAlert').removeClass('d-none').text('حدث خطأ غير متوقع');
        }
    }

    // تحميل تقرير الرواتب
    function loadSalariesReport() {
        const formData = new FormData(document.getElementById('salariesForm'));

        // إظهار تنبيه التحميل
        $('#loadingAlert').removeClass('d-none');
        $('#errorAlert').addClass('d-none');

        // جلب البيانات من API
        $.ajax({
            url: `${BASE_URL}api/employees.php`,
            type: 'GET',
            data: {
                action: 'get_employees_salaries',
                month: formData.get('month'),
                year: formData.get('year'),
                branch_id: formData.get('branch_id'),
                payment_status: formData.get('payment_status')
            },
            dataType: 'json',
            success: function(response) {
                // إخفاء تنبيه التحميل
                $('#loadingAlert').addClass('d-none');

                if (response.status === 'success') {
                    // تحديث واجهة التقرير
                    updateSalariesReport(response);
                } else {
                    $('#errorAlert').removeClass('d-none').text(response.message || 'حدث خطأ أثناء تحميل التقرير');
                }
            },
            error: function(xhr, status, error) {
                // إخفاء تنبيه التحميل وإظهار الخطأ
                $('#loadingAlert').addClass('d-none');
                $('#errorAlert').removeClass('d-none').text('حدث خطأ أثناء الاتصال بالخادم');
                console.error(error);
            }
        });
    }

    // تحديث تقرير الأداء
    function updatePerformanceReport(data) {
        // التحقق من وجود البيانات
        if (!data) {
            console.error('بيانات التقرير غير متوفرة');
            $('#errorAlert').removeClass('d-none').text('بيانات التقرير غير متوفرة');
            return;
        }

        // طباعة بيانات التقرير للتحقق
        console.log('Performance report data:', data);

        // التحقق من هيكل البيانات واستخدام الهيكل المناسب
        let employeesData = [];

        // التحقق من وجود employee_performance أو top_employees أو employees
        if (data.employee_performance && Array.isArray(data.employee_performance)) {
            console.log('استخدام employee_performance للبيانات');
            employeesData = data.employee_performance.map(emp => ({
                id: emp.id,
                name: emp.name,
                position: emp.position,
                branch_name: emp.branch_name || '',
                services_count: parseInt(emp.total_services || 0),
                revenue: parseFloat(emp.total_sales || 0)
            }));
        } else if (data.top_employees && Array.isArray(data.top_employees)) {
            console.log('استخدام top_employees للبيانات');
            employeesData = data.top_employees.map(emp => ({
                id: emp.id,
                name: emp.name,
                position: emp.position,
                branch_name: emp.branch_name || '',
                services_count: parseInt(emp.total_services || 0),
                revenue: parseFloat(emp.total_sales || 0)
            }));
        } else if (data.employees && Array.isArray(data.employees)) {
            console.log('استخدام employees للبيانات');
            employeesData = data.employees;
        } else {
            console.log('لا توجد بيانات للموظفين');
            employeesData = [];
        }

        // إنشاء هيكل بيانات افتراضي إذا لم يكن موجوداً
        if (!data.summary) {
            console.log('إنشاء ملخص افتراضي');
            data.summary = {
                active_employees: 0,
                total_services: 0,
                total_revenue: 0
            };
        }

        // حساب الإجماليات من بيانات الموظفين إذا لم تكن موجودة في الملخص
        if (data.summary.active_employees === 0 && employeesData.length > 0) {
            data.summary.active_employees = employeesData.length;
        }

        if (data.summary.total_services === 0 && employeesData.length > 0) {
            data.summary.total_services = employeesData.reduce((sum, emp) => sum + parseInt(emp.services_count || 0), 0);
        }

        if (data.summary.total_revenue === 0 && employeesData.length > 0) {
            data.summary.total_revenue = employeesData.reduce((sum, emp) => sum + parseFloat(emp.revenue || 0), 0);
        }

        // إظهار أقسام التقرير
        $('#performanceSummary, #performanceCharts, #performanceTable').removeClass('d-none');

        // تحديث ملخص التقرير
        const activeEmployees = data.summary.active_employees || 0;
        const totalServices = data.summary.total_services || 0;
        const totalRevenue = data.summary.total_revenue || 0;

        $('#activeEmployees').text(activeEmployees);
        $('#totalServices').text(totalServices);
        $('#totalRevenue').text(formatCurrency(totalRevenue));

        // حساب متوسط الخدمات إذا كان هناك موظفين
        const avgServices = activeEmployees > 0
            ? Math.round(totalServices / activeEmployees)
            : 0;
        $('#averageServices').text(avgServices);

        // تحديث المخططات
        updateTopEmployeesChart(employeesData);
        updateServicesDistributionChart(employeesData);

        // إنشاء نسخة من البيانات مع الموظفين المعدلين
        const dataWithEmployees = { ...data, employees: employeesData };

        // تحديث جدول التقرير
        updatePerformanceTable(dataWithEmployees);
    }

    // تحديث تقرير الحضور
    function updateAttendanceReport(data) {
        // التحقق من وجود البيانات
        if (!data) {
            console.error('بيانات تقرير الحضور غير متوفرة');
            $('#errorAlert').removeClass('d-none').text('بيانات تقرير الحضور غير متوفرة');
            return;
        }

        // طباعة بيانات التقرير للتحقق
        console.log('Attendance report data:', data);

        // إظهار أقسام التقرير
        $('#attendanceSummary, #attendanceCharts, #attendanceTable').removeClass('d-none');

        // تحديث ملخص التقرير
        if (data.summary) {
            $('#attendanceActiveEmployees').text(data.summary.total_employees || 0);
            $('#workingDays').text(data.summary.total_days || 0);
            $('#actualWorkingDays').text(data.summary.working_days || 0); // إضافة أيام العمل الفعلية
            $('#attendanceRate').text(`${data.summary.attendance_rate || 0}%`);
            $('#averageHours').text(data.summary.average_hours || 0);
        } else {
            $('#attendanceActiveEmployees').text(0);
            $('#workingDays').text(0);
            $('#actualWorkingDays').text(0); // إضافة أيام العمل الفعلية
            $('#attendanceRate').text('0%');
            $('#averageHours').text(0);
        }

        // تحديث المخططات
        updateDailyAttendanceChart(data.daily_attendance || []);
        updateWorkingHoursChart(data.employees || []);

        // تحديث جدول التقرير
        updateAttendanceTable(data);
    }

    // تحديث تقرير الرواتب
    function updateSalariesReport(response) {
        // إظهار أقسام التقرير
        $('#salariesSummary, #salariesCharts, #salariesTable').removeClass('d-none');

        const data = response.salaries || [];

        // حساب الإحصائيات
        let totalEmployees = data.length;
        let totalSalary = 0;
        let totalFixed = 0;
        let totalCommission = 0;
        let totalBonuses = 0;
        let totalDeductions = 0;

        data.forEach(item => {
            totalSalary += parseFloat(item.total_amount);
            totalFixed += parseFloat(item.fixed_amount);
            totalCommission += parseFloat(item.commission_amount);
            totalBonuses += parseFloat(item.bonuses || 0);
            totalDeductions += parseFloat(item.deductions || 0);
        });

        // تحديث ملخص التقرير
        $('#salaryEmployeeCount').text(totalEmployees);
        $('#totalSalaries').text(formatCurrency(totalSalary));
        $('#fixedSalaries').text(formatCurrency(totalFixed));
        $('#commissionSalaries').text(formatCurrency(totalCommission));

        // تحديث المخططات
        updateSalaryDistributionChart(data);
        updateSalaryTypesChart({
            fixed: totalFixed,
            commission: totalCommission,
            bonuses: totalBonuses,
            deductions: totalDeductions
        });

        // تحديث جدول التقرير
        updateSalariesTable(data, {
            totalFixed,
            totalCommission,
            totalBonuses,
            totalDeductions,
            totalSalary
        });
    }

    // تحديث مخطط أعلى الموظفين أداءاً
    function updateTopEmployeesChart(employees) {
        const ctx = document.getElementById('topEmployeesChart').getContext('2d');

        // تدمير المخطط السابق إذا كان موجودًا
        if (topEmployeesChart) {
            topEmployeesChart.destroy();
        }

        // التحقق من وجود البيانات
        if (!employees || !Array.isArray(employees) || employees.length === 0) {
            console.log('لا توجد بيانات للموظفين لعرضها في المخطط');
            // إنشاء مخطط فارغ
            topEmployeesChart = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: ['لا توجد بيانات'],
                    datasets: [{
                        label: 'عدد الخدمات',
                        data: [0],
                        backgroundColor: 'rgba(200, 200, 200, 0.8)',
                        borderColor: 'rgba(200, 200, 200, 1)',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    indexAxis: 'y',
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        x: {
                            beginAtZero: true
                        }
                    }
                }
            });
            return;
        }

        // ترتيب الموظفين حسب الخدمات وأخذ أعلى 10
        const sortedEmployees = [...employees]
            .sort((a, b) => (parseInt(b.services_count || 0) - parseInt(a.services_count || 0)))
            .slice(0, 10);

        const labels = sortedEmployees.map(emp => emp.name || 'غير محدد');
        const data = sortedEmployees.map(emp => parseInt(emp.services_count || 0));

        topEmployeesChart = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: labels,
                datasets: [{
                    label: 'عدد الخدمات',
                    data: data,
                    backgroundColor: 'rgba(52, 152, 219, 0.8)',
                    borderColor: 'rgba(52, 152, 219, 1)',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                indexAxis: 'y',
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    x: {
                        beginAtZero: true
                    }
                }
            }
        });
    }

    // تحديث مخطط توزيع الخدمات على الموظفين
    function updateServicesDistributionChart(employees) {
        const ctx = document.getElementById('servicesDistributionChart').getContext('2d');

        // تدمير المخطط السابق إذا كان موجودًا
        if (servicesDistributionChart) {
            servicesDistributionChart.destroy();
        }

        // التحقق من وجود البيانات
        if (!employees || !Array.isArray(employees) || employees.length === 0) {
            console.log('لا توجد بيانات للموظفين لعرضها في مخطط توزيع الخدمات');
            // إنشاء مخطط فارغ
            servicesDistributionChart = new Chart(ctx, {
                type: 'pie',
                data: {
                    labels: ['لا توجد بيانات'],
                    datasets: [{
                        data: [1],
                        backgroundColor: ['rgba(200, 200, 200, 0.8)'],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });
            return;
        }

        // ترتيب الموظفين حسب الإيرادات وأخذ أعلى 7
        const sortedEmployees = [...employees]
            .sort((a, b) => parseFloat(b.revenue || 0) - parseFloat(a.revenue || 0))
            .slice(0, 7); // نأخذ أعلى 7 للوضوح في الرسم البياني

        const labels = sortedEmployees.map(emp => emp.name || 'غير محدد');
        const data = sortedEmployees.map(emp => parseFloat(emp.revenue || 0));

        servicesDistributionChart = new Chart(ctx, {
            type: 'pie',
            data: {
                labels: labels,
                datasets: [{
                    data: data,
                    backgroundColor: [
                        'rgba(52, 152, 219, 0.8)',
                        'rgba(46, 204, 113, 0.8)',
                        'rgba(231, 76, 60, 0.8)',
                        'rgba(241, 196, 15, 0.8)',
                        'rgba(155, 89, 182, 0.8)',
                        'rgba(26, 188, 156, 0.8)',
                        'rgba(230, 126, 34, 0.8)'
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'right'
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const label = context.label || '';
                                const value = formatCurrency(context.raw);
                                return `${label}: ${value}`;
                            }
                        }
                    }
                }
            }
        });
    }

    // تحديث مخطط معدل الحضور اليومي
    function updateDailyAttendanceChart(dailyData) {
        const ctx = document.getElementById('dailyAttendanceChart').getContext('2d');

        // تدمير المخطط السابق إذا كان موجودًا
        if (dailyAttendanceChart) {
            dailyAttendanceChart.destroy();
        }

        // التحقق من وجود البيانات
        if (!dailyData || !Array.isArray(dailyData) || dailyData.length === 0) {
            console.log('لا توجد بيانات للحضور اليومي لعرضها في المخطط');
            // إنشاء مخطط فارغ
            dailyAttendanceChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: ['لا توجد بيانات'],
                    datasets: [{
                        label: 'معدل الحضور',
                        data: [0],
                        backgroundColor: 'rgba(200, 200, 200, 0.1)',
                        borderColor: 'rgba(200, 200, 200, 1)',
                        borderWidth: 2,
                        pointBackgroundColor: 'rgba(200, 200, 200, 1)',
                        tension: 0.4,
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            max: 100,
                            ticks: {
                                callback: function(value) {
                                    return `${value}%`;
                                }
                            }
                        }
                    }
                }
            });
            return;
        }

        const labels = dailyData.map(item => item.date);
        const data = dailyData.map(item => item.attendance_rate);

        dailyAttendanceChart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: labels,
                datasets: [{
                    label: 'معدل الحضور',
                    data: data,
                    backgroundColor: 'rgba(52, 152, 219, 0.1)',
                    borderColor: 'rgba(52, 152, 219, 1)',
                    borderWidth: 2,
                    pointBackgroundColor: 'rgba(52, 152, 219, 1)',
                    tension: 0.4,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                return `معدل الحضور: ${context.raw}%`;
                            }
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        max: 100,
                        ticks: {
                            callback: function(value) {
                                return `${value}%`;
                            }
                        }
                    }
                }
            }
        });
    }

    // تحديث مخطط ساعات العمل للموظفين
    function updateWorkingHoursChart(employees) {
        const ctx = document.getElementById('workingHoursChart').getContext('2d');

        // تدمير المخطط السابق إذا كان موجودًا
        if (workingHoursChart) {
            workingHoursChart.destroy();
        }

        // التحقق من وجود البيانات
        if (!employees || !Array.isArray(employees) || employees.length === 0) {
            console.log('لا توجد بيانات للموظفين لعرضها في مخطط ساعات العمل');
            // إنشاء مخطط فارغ
            workingHoursChart = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: ['لا توجد بيانات'],
                    datasets: [{
                        label: 'ساعات العمل',
                        data: [0],
                        backgroundColor: 'rgba(200, 200, 200, 0.8)',
                        borderColor: 'rgba(200, 200, 200, 1)',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    indexAxis: 'y',
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        x: {
                            beginAtZero: true
                        }
                    }
                }
            });
            return;
        }

        // ترتيب الموظفين حسب ساعات العمل وأخذ أعلى 10
        const sortedEmployees = [...employees]
            .sort((a, b) => parseFloat(b.working_hours || 0) - parseFloat(a.working_hours || 0))
            .slice(0, 10);

        const labels = sortedEmployees.map(emp => emp.name || 'غير محدد');
        const data = sortedEmployees.map(emp => parseFloat(emp.working_hours || 0));

        workingHoursChart = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: labels,
                datasets: [{
                    label: 'ساعات العمل',
                    data: data,
                    backgroundColor: 'rgba(46, 204, 113, 0.8)',
                    borderColor: 'rgba(46, 204, 113, 1)',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                indexAxis: 'y',
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    x: {
                        beginAtZero: true
                    }
                }
            }
        });
    }

    // تحديث مخطط توزيع الرواتب
    function updateSalaryDistributionChart(salaries) {
        const ctx = document.getElementById('salaryDistributionChart').getContext('2d');

        // ترتيب الموظفين حسب إجمالي الراتب وأخذ أعلى 7
        const sortedEmployees = [...salaries]
            .sort((a, b) => b.total_amount - a.total_amount)
            .slice(0, 7); // نأخذ أعلى 7 للوضوح في الرسم البياني

        const labels = sortedEmployees.map(item => item.employee_name);
        const data = sortedEmployees.map(item => item.total_amount);

        // تدمير المخطط السابق إذا كان موجودًا
        if (salaryDistributionChart) {
            salaryDistributionChart.destroy();
        }

        salaryDistributionChart = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: labels,
                datasets: [{
                    label: 'إجمالي الراتب',
                    data: data,
                    backgroundColor: 'rgba(52, 152, 219, 0.8)',
                    borderColor: 'rgba(52, 152, 219, 1)',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                return `إجمالي الراتب: ${formatCurrency(context.raw)}`;
                            }
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return formatCurrency(value);
                            }
                        }
                    }
                }
            }
        });
    }

    // تحديث مخطط توزيع أنواع الرواتب
    function updateSalaryTypesChart(data) {
        const ctx = document.getElementById('salaryTypesChart').getContext('2d');

        // تدمير المخطط السابق إذا كان موجودًا
        if (salaryTypesChart) {
            salaryTypesChart.destroy();
        }

        const salaryTypeData = {
            labels: ['راتب ثابت', 'عمولات', 'مكافآت', 'خصومات'],
            datasets: [{
                data: [
                    data.fixed,
                    data.commission,
                    data.bonuses,
                    data.deductions
                ],
                backgroundColor: [
                    'rgba(52, 152, 219, 0.8)',
                    'rgba(46, 204, 113, 0.8)',
                    'rgba(241, 196, 15, 0.8)',
                    'rgba(231, 76, 60, 0.8)'
                ],
                borderWidth: 1
            }]
        };

        salaryTypesChart = new Chart(ctx, {
            type: 'doughnut',
            data: salaryTypeData,
            options: {
                responsive: true,
                maintainAspectRatio: false,
                cutout: '50%',
                plugins: {
                    legend: {
                        position: 'bottom'
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const label = context.label || '';
                                const value = formatCurrency(context.raw);
                                return `${label}: ${value}`;
                            }
                        }
                    }
                }
            }
        });
    }

    // تحديث جدول الأداء
    function updatePerformanceTable(data) {
        const tableBody = $('#performanceTableBody');
        tableBody.empty();

        // التحقق من وجود البيانات
        if (!data || !data.employees || !Array.isArray(data.employees)) {
            console.error('بيانات الموظفين غير متوفرة أو ليست مصفوفة');
            $('#footerTotalServices').text('0');
            $('#footerTotalRevenue').text(formatCurrency(0));
            return;
        }

        let totalServices = 0;
        let totalRevenue = 0;
        const summaryTotalRevenue = data.summary && data.summary.total_revenue ? parseFloat(data.summary.total_revenue) : 0;

        data.employees.forEach(employee => {
            // التحقق من وجود البيانات المطلوبة
            const servicesCount = parseInt(employee.services_count || 0);
            const revenue = parseFloat(employee.revenue || 0);

            totalServices += servicesCount;
            totalRevenue += revenue;

            // حساب النسبة المئوية
            let percentage = '0.00';
            if (summaryTotalRevenue > 0) {
                percentage = ((revenue * 100) / summaryTotalRevenue).toFixed(2);
            }

            const row = `
                <tr>
                    <td>${employee.name || ''}</td>
                    <td>${employee.branch_name || 'غير محدد'}</td>
                    <td>${employee.position || 'غير محدد'}</td>
                    <td>${servicesCount}</td>
                    <td>${formatCurrency(revenue)}</td>
                    <td>${percentage}%</td>
                </tr>
            `;
            tableBody.append(row);
        });

        // تحديث الإجماليات في الجدول
        $('#footerTotalServices').text(totalServices);
        $('#footerTotalRevenue').text(formatCurrency(totalRevenue));
    }

    // تحديث جدول الحضور
    function updateAttendanceTable(data) {
        const tableBody = $('#attendanceTableBody');
        tableBody.empty();

        // طباعة بيانات الجدول للتحقق
        console.log('بيانات جدول الحضور:', data);

        // التحقق من وجود البيانات
        if (!data) {
            console.log('لا توجد بيانات للعرض في جدول الحضور');
            tableBody.append(`
                <tr>
                    <td colspan="6" class="text-center">لا توجد بيانات للعرض</td>
                </tr>
            `);
            return;
        }

        // التحقق من وجود بيانات الموظفين
        let employeesData = [];

        // استخدام الهيكل الجديد إذا كان موجوداً
        if (data.employees && Array.isArray(data.employees) && data.employees.length > 0) {
            employeesData = data.employees;
        }
        // استخدام السجلات الصالحة إذا كانت موجودة
        else if (data.valid_records && Array.isArray(data.valid_records) && data.valid_records.length > 0) {
            // إنشاء ملخص للموظفين من السجلات الصالحة
            const employeeSummary = {};

            data.valid_records.forEach(record => {
                const employeeId = record.employee_id;

                if (!employeeSummary[employeeId]) {
                    employeeSummary[employeeId] = {
                        id: employeeId,
                        name: record.employee_name || 'غير محدد',
                        branch_name: record.branch_name || 'غير محدد',
                        present_days: 0,
                        absent_days: 0,
                        working_hours: 0
                    };
                }

                employeeSummary[employeeId].present_days++;
                employeeSummary[employeeId].working_hours += parseFloat(record.hours_worked || 0);
            });

            employeesData = Object.values(employeeSummary);
        }
        // استخدام جميع السجلات إذا لم تكن هناك سجلات صالحة
        else if (data.records && Array.isArray(data.records) && data.records.length > 0) {
            // عرض السجلات الفردية بدلاً من الملخص
            data.records.forEach(record => {
                const isValid = record.is_valid !== false;
                const validationErrors = record.validation_errors || [];

                const row = `
                    <tr class="${isValid ? '' : 'table-danger'}">
                        <td>${record.employee_name || 'غير محدد'}</td>
                        <td>${record.branch_name || 'غير محدد'}</td>
                        <td>${record.date || ''}</td>
                        <td>${record.check_in || ''}</td>
                        <td>${record.check_out || ''}</td>
                        <td>${parseFloat(record.hours_worked || 0).toFixed(2)}</td>
                        <td>${isValid ? '' : validationErrors.join(', ')}</td>
                    </tr>
                `;
                tableBody.append(row);
            });

            // تغيير عناوين الأعمدة لتناسب عرض السجلات الفردية
            $('#attendanceTableHead').html(`
                <tr>
                    <th>الموظف</th>
                    <th>الفرع</th>
                    <th>التاريخ</th>
                    <th>وقت الدخول</th>
                    <th>وقت الخروج</th>
                    <th>ساعات العمل</th>
                    <th>ملاحظات</th>
                </tr>
            `);

            return;
        }

        // إذا لم تكن هناك بيانات للعرض
        if (employeesData.length === 0) {
            console.log('لا توجد بيانات للموظفين لعرضها في جدول الحضور');
            tableBody.append(`
                <tr>
                    <td colspan="6" class="text-center">لا توجد بيانات للعرض</td>
                </tr>
            `);
            return;
        }

        // التأكد من أن عناوين الأعمدة مناسبة لعرض ملخص الموظفين
        $('#attendanceTableHead').html(`
            <tr>
                <th>الموظف</th>
                <th>الفرع</th>
                <th>أيام الحضور</th>
                <th>أيام الغياب</th>
                <th>ساعات العمل</th>
                <th>متوسط الساعات/يوم</th>
            </tr>
        `);

        // عرض بيانات الموظفين
        employeesData.forEach(employee => {
            // التحقق من وجود البيانات المطلوبة
            const name = employee.name || 'غير محدد';
            const branchName = employee.branch_name || 'غير محدد';
            const presentDays = parseInt(employee.present_days || 0);
            const absentDays = parseInt(employee.absent_days || 0);
            const workingHours = parseFloat(employee.working_hours || 0);
            const avgHoursPerDay = presentDays > 0 ? (workingHours / presentDays).toFixed(2) : '0.00';

            const row = `
                <tr>
                    <td>${name}</td>
                    <td>${branchName}</td>
                    <td>${presentDays}</td>
                    <td>${absentDays}</td>
                    <td>${workingHours.toFixed(2)}</td>
                    <td>${avgHoursPerDay}</td>
                </tr>
            `;
            tableBody.append(row);
        });
    }

    // تحديث جدول الرواتب
    function updateSalariesTable(data, totals) {
        const tableBody = $('#salariesTableBody');
        tableBody.empty();

        data.forEach(salary => {
            // تنسيق حالة الدفع
            let statusBadge = '';
            if (salary.payment_status === 'paid') {
                statusBadge = '<span class="badge bg-success">مدفوع</span>';
            } else {
                statusBadge = '<span class="badge bg-danger">غير مدفوع</span>';
            }

            // تنسيق نوع الراتب
            let salaryType = '';
            switch(salary.salary_type) {
                case 'fixed':
                    salaryType = 'ثابت';
                    break;
                case 'percentage':
                    salaryType = 'عمولة';
                    break;
                case 'both':
                    salaryType = 'ثابت + عمولة';
                    break;
                default:
                    salaryType = 'غير محدد';
            }

            const row = `
                <tr>
                    <td>${salary.employee_name}</td>
                    <td>${salary.branch_name || 'غير محدد'}</td>
                    <td>${salaryType}</td>
                    <td>${formatCurrency(salary.fixed_amount)}</td>
                    <td>${formatCurrency(salary.commission_amount)}</td>
                    <td>${formatCurrency(salary.bonuses || 0)}</td>
                    <td>${formatCurrency(salary.deductions || 0)}</td>
                    <td>${formatCurrency(salary.total_amount)}</td>
                    <td>${statusBadge}</td>
                </tr>
            `;
            tableBody.append(row);
        });

        // تحديث الإجماليات في الجدول
        $('#footerFixedSalaries').text(formatCurrency(totals.totalFixed));
        $('#footerCommission').text(formatCurrency(totals.totalCommission));
        $('#footerBonuses').text(formatCurrency(totals.totalBonuses));
        $('#footerDeductions').text(formatCurrency(totals.totalDeductions));
        $('#footerTotalSalaries').text(formatCurrency(totals.totalSalary));
    }

    // تنسيق المبالغ المالية
    function formatCurrency(amount) {
        return parseFloat(amount).toFixed(2) + ' <?php echo $currencySymbol; ?>';
    }

    // تصدير التقارير إلى Excel و PDF
    $('#exportPerformanceExcel').click(function() {
        $('#performanceReportTable').tableExport({
            formats: ['xlsx'],
            filename: 'تقرير_أداء_الموظفين'
        });
    });

    $('#exportPerformancePdf').click(function() {
        $('#performanceReportTable').tableExport({
            formats: ['pdf'],
            filename: 'تقرير_أداء_الموظفين',
            RTL: true,
            jspdf: {
                orientation: 'l',
                margins: { left: 10, top: 10 },
                autotable: {
                    styles: { rtl: true }
                }
            }
        });
    });

    $('#exportAttendanceExcel').click(function() {
        $('#attendanceReportTable').tableExport({
            formats: ['xlsx'],
            filename: 'تقرير_حضور_الموظفين'
        });
    });

    $('#exportAttendancePdf').click(function() {
        $('#attendanceReportTable').tableExport({
            formats: ['pdf'],
            filename: 'تقرير_حضور_الموظفين',
            RTL: true,
            jspdf: {
                orientation: 'l',
                margins: { left: 10, top: 10 },
                autotable: {
                    styles: { rtl: true }
                }
            }
        });
    });

    $('#exportSalariesExcel').click(function() {
        $('#salariesReportTable').tableExport({
            formats: ['xlsx'],
            filename: 'تقرير_رواتب_الموظفين'
        });
    });

    $('#exportSalariesPdf').click(function() {
        $('#salariesReportTable').tableExport({
            formats: ['pdf'],
            filename: 'تقرير_رواتب_الموظفين',
            RTL: true,
            jspdf: {
                orientation: 'l',
                margins: { left: 10, top: 10 },
                autotable: {
                    styles: { rtl: true }
                }
            }
        });
    });

    // طباعة التقارير
    $('#printPerformanceReport, #printAttendanceReport, #printSalariesReport').click(function() {
        window.print();
    });

    // تحميل التقرير الافتراضي عند تحميل الصفحة
    loadPerformanceReport();
});
</script>