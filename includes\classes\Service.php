<?php
/**
 * فئة الخدمة
 * تتعامل مع إدارة خدمات صالون الحلاقة والكوافير
 */

// منع الوصول المباشر للملف
if (!defined('BASEPATH')) {
    exit('لا يمكن الوصول المباشر لهذا الملف');
}

class Service {
    /**
     * @var Database $db
     */
    private $db;

    public function __construct($db) {
        $this->db = $db;
    }

    /**
     * حذف خدمة
     * @param int $serviceId معرف الخدمة
     * @return bool نجاح أو فشل العملية
     */
    public function deleteService($serviceId) {
        try {
            $this->db->beginTransaction();

            // التحقق من عدم وجود فواتير مرتبطة بالخدمة
            $this->db->prepare("SELECT COUNT(*) FROM invoice_items
                              WHERE item_type = 'service' AND item_id = :service_id");
            $this->db->bind(':service_id', $serviceId);
            if ($this->db->fetchColumn() > 0) {
                throw new Exception('لا يمكن حذف الخدمة لوجود فواتير مرتبطة بها');
            }

            // التحقق من عدم وجود مواعيد مرتبطة بالخدمة
            $this->db->prepare("SELECT COUNT(*) FROM appointments WHERE service_id = :service_id");
            $this->db->bind(':service_id', $serviceId);
            if ($this->db->fetchColumn() > 0) {
                throw new Exception('لا يمكن حذف الخدمة لوجود مواعيد مرتبطة بها');
            }

            // حذف ارتباطات الموظفين
            $this->db->prepare("DELETE FROM service_employees WHERE service_id = :service_id");
            $this->db->bind(':service_id', $serviceId);
            $this->db->execute();

            // حذف الخدمة
            $this->db->prepare("DELETE FROM services WHERE id = :id");
            $this->db->bind(':id', $serviceId);
            $this->db->execute();

            $this->db->commit();
            return true;
        } catch (Exception $e) {
            $this->db->rollBack();
            error_log('خطأ أثناء حذف الخدمة: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * الحصول على بيانات خدمة بواسطة المعرف
     * @param int $serviceId معرف الخدمة
     * @return array|false بيانات الخدمة أو false إذا لم يتم العثور عليها
     */
    public function getServiceById($serviceId) {
        try {
            $this->db->prepare("SELECT s.*, c.name as category_name, b.name as branch_name
                              FROM services s
                              LEFT JOIN service_categories c ON s.category_id = c.id
                              LEFT JOIN branches b ON s.branch_id = b.id
                              WHERE s.id = :id");
            $this->db->bind(':id', $serviceId);
            $service = $this->db->fetch();

            if ($service) {
                // استرجاع الموظفين المرتبطين بالخدمة
                $service['employees'] = $this->getServiceEmployees($serviceId);
            }

            return $service;
        } catch (Exception $e) {
            error_log('خطأ أثناء استرجاع بيانات الخدمة: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * الحصول على قائمة الخدمات
     * @param array $filters فلاتر البحث (اختياري)
     * @return array قائمة الخدمات
     */
    public function getServices($filters = []) {
        try {
            $sql = "SELECT s.*, c.name as category_name, b.name as branch_name
                    FROM services s
                    LEFT JOIN service_categories c ON s.category_id = c.id
                    LEFT JOIN branches b ON s.branch_id = b.id";

            $whereConditions = [];
            $bindings = [];

            // تطبيق الفلاتر
            if (!empty($filters['search'])) {
                $whereConditions[] = "(s.name LIKE :search OR s.description LIKE :search)";
                $bindings[':search'] = '%' . $filters['search'] . '%';
            }

            if (!empty($filters['category_id'])) {
                $whereConditions[] = "s.category_id = :category_id";
                $bindings[':category_id'] = $filters['category_id'];
            }

            if (!empty($filters['branch_id'])) {
                $whereConditions[] = "s.branch_id = :branch_id OR s.branch_id IS NULL";
                $bindings[':branch_id'] = $filters['branch_id'];
            }

            if (isset($filters['is_active'])) {
                $whereConditions[] = "s.is_active = :is_active";
                $bindings[':is_active'] = $filters['is_active'];
            }

            if (isset($filters['min_price'])) {
                $whereConditions[] = "s.price >= :min_price";
                $bindings[':min_price'] = $filters['min_price'];
            }

            if (isset($filters['max_price'])) {
                $whereConditions[] = "s.price <= :max_price";
                $bindings[':max_price'] = $filters['max_price'];
            }

            if (!empty($filters['employee_id'])) {
                $sql .= " JOIN service_employees se ON s.id = se.service_id";
                $whereConditions[] = "se.employee_id = :employee_id";
                $bindings[':employee_id'] = $filters['employee_id'];
            }

            // إضافة شروط WHERE إذا وجدت
            if (!empty($whereConditions)) {
                $sql .= " WHERE " . implode(' AND ', $whereConditions);
            }

            // ترتيب النتائج
            if (!empty($filters['sort_by'])) {
                $sql .= " ORDER BY " . $filters['sort_by'];

                if (!empty($filters['sort_dir']) && in_array(strtoupper($filters['sort_dir']), ['ASC', 'DESC'])) {
                    $sql .= " " . strtoupper($filters['sort_dir']);
                } else {
                    $sql .= " ASC";
                }
            } else {
                $sql .= " ORDER BY s.name ASC";
            }

            // إضافة الحد والإزاحة إذا وجدت
            if (!empty($filters['limit'])) {
                $sql .= " LIMIT :limit";
                if (!empty($filters['offset'])) {
                    $sql .= " OFFSET :offset";
                }
            }

            $this->db->prepare($sql);

            // ربط القيم
            foreach ($bindings as $param => $value) {
                $this->db->bind($param, $value);
            }

            // ربط الحد والإزاحة إذا وجدت
            if (!empty($filters['limit'])) {
                $this->db->bind(':limit', $filters['limit'], PDO::PARAM_INT);
                if (!empty($filters['offset'])) {
                    $this->db->bind(':offset', $filters['offset'], PDO::PARAM_INT);
                }
            }

            $services = $this->db->fetchAll();

            // استرجاع الموظفين المرتبطين بكل خدمة
            foreach ($services as &$service) {
                $service['employees'] = $this->getServiceEmployees($service['id']);
            }

            return $services;
        } catch (Exception $e) {
            error_log('خطأ أثناء استرجاع قائمة الخدمات: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * الحصول على عدد الخدمات
     * @param array $filters فلاتر البحث (اختياري)
     * @return int عدد الخدمات
     */
    public function getServicesCount($filters = []) {
        try {
            $sql = "SELECT COUNT(*) FROM services s";

            // إضافة الجداول المرتبطة إذا كان هناك فلتر للموظف
            if (!empty($filters['employee_id'])) {
                $sql .= " JOIN service_employees se ON s.id = se.service_id";
            }

            $whereConditions = [];
            $bindings = [];

            // تطبيق الفلاتر
            if (!empty($filters['search'])) {
                $whereConditions[] = "(s.name LIKE :search OR s.description LIKE :search)";
                $bindings[':search'] = '%' . $filters['search'] . '%';
            }

            if (!empty($filters['category_id'])) {
                $whereConditions[] = "s.category_id = :category_id";
                $bindings[':category_id'] = $filters['category_id'];
            }

            if (!empty($filters['branch_id'])) {
                $whereConditions[] = "s.branch_id = :branch_id";
                $bindings[':branch_id'] = $filters['branch_id'];
            }

            if (isset($filters['is_active'])) {
                $whereConditions[] = "s.is_active = :is_active";
                $bindings[':is_active'] = $filters['is_active'];
            }

            if (isset($filters['min_price'])) {
                $whereConditions[] = "s.price >= :min_price";
                $bindings[':min_price'] = $filters['min_price'];
            }

            if (isset($filters['max_price'])) {
                $whereConditions[] = "s.price <= :max_price";
                $bindings[':max_price'] = $filters['max_price'];
            }

            if (!empty($filters['employee_id'])) {
                $whereConditions[] = "se.employee_id = :employee_id";
                $bindings[':employee_id'] = $filters['employee_id'];
            }

            // إضافة شروط WHERE إذا وجدت
            if (!empty($whereConditions)) {
                $sql .= " WHERE " . implode(' AND ', $whereConditions);
            }

            $this->db->prepare($sql);

            // ربط القيم
            foreach ($bindings as $param => $value) {
                $this->db->bind($param, $value);
            }

            return $this->db->fetchColumn();
        } catch (Exception $e) {
            error_log('خطأ أثناء حساب عدد الخدمات: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * الحصول على الموظفين المرتبطين بخدمة
     * @param int $serviceId معرف الخدمة
     * @return array قائمة الموظفين
     */
    public function getServiceEmployees($serviceId) {
        try {
            $this->db->prepare("SELECT e.*
                              FROM employees e
                              JOIN service_employees se ON e.id = se.employee_id
                              WHERE se.service_id = :service_id
                              ORDER BY e.name ASC");
            $this->db->bind(':service_id', $serviceId);
            return $this->db->fetchAll();
        } catch (Exception $e) {
            error_log('خطأ أثناء استرجاع موظفي الخدمة: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * الحصول على معرفات الموظفين المرتبطين بخدمة
     * @param int $serviceId معرف الخدمة
     * @return array قائمة معرفات الموظفين
     */
    public function getServiceEmployeeIds($serviceId) {
        try {
            $this->db->prepare("SELECT employee_id FROM service_employees WHERE service_id = :service_id");
            $this->db->bind(':service_id', $serviceId);
            $employees = $this->db->fetchAll();

            $employeeIds = [];
            foreach ($employees as $employee) {
                $employeeIds[] = $employee['employee_id'];
            }

            return $employeeIds;
        } catch (Exception $e) {
            error_log('خطأ أثناء استرجاع معرفات موظفي الخدمة: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * إضافة فئة خدمة جديدة
     * @param array $categoryData بيانات الفئة
     * @return int|false معرف الفئة الجديدة أو false إذا فشلت العملية
     */
    public function addServiceCategory($categoryData) {
        try {
            $this->db->prepare("INSERT INTO service_categories (name, description) VALUES (:name, :description)");
            $this->db->bind(':name', $categoryData['name']);
            $this->db->bind(':description', $categoryData['description'] ?? null);

            $this->db->execute();
            return (int)$this->db->lastInsertId();
        } catch (Exception $e) {
            error_log('خطأ أثناء إضافة فئة خدمة: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * تحديث بيانات فئة خدمة
     * @param int $categoryId معرف الفئة
     * @param array $categoryData البيانات المحدثة
     * @return bool نجاح أو فشل العملية
     */
    public function updateServiceCategory($categoryId, $categoryData) {
        try {
            $this->db->prepare("UPDATE service_categories
                              SET name = :name, description = :description
                              WHERE id = :id");
            $this->db->bind(':name', $categoryData['name']);
            $this->db->bind(':description', $categoryData['description'] ?? null);
            $this->db->bind(':id', $categoryId);

            return $this->db->execute();
        } catch (Exception $e) {
            error_log('خطأ أثناء تحديث بيانات فئة الخدمة: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * حذف فئة خدمة
     * @param int $categoryId معرف الفئة
     * @return bool نجاح أو فشل العملية
     */
    public function deleteServiceCategory($categoryId) {
        try {
            // التحقق من عدم وجود خدمات مرتبطة بالفئة
            $this->db->prepare("SELECT COUNT(*) FROM services WHERE category_id = :category_id");
            $this->db->bind(':category_id', $categoryId);
            if ($this->db->fetchColumn() > 0) {
                throw new Exception('لا يمكن حذف الفئة لوجود خدمات مرتبطة بها');
            }

            $this->db->prepare("DELETE FROM service_categories WHERE id = :id");
            $this->db->bind(':id', $categoryId);

            return $this->db->execute();
        } catch (Exception $e) {
            error_log('خطأ أثناء حذف فئة الخدمة: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * الحصول على بيانات فئة خدمة بواسطة المعرف
     * @param int $categoryId معرف الفئة
     * @return array|false بيانات الفئة أو false إذا لم يتم العثور عليها
     */
    public function getServiceCategoryById($categoryId) {
        try {
            $this->db->prepare("SELECT * FROM service_categories WHERE id = :id");
            $this->db->bind(':id', $categoryId);
            return $this->db->fetch();
        } catch (Exception $e) {
            error_log('خطأ أثناء استرجاع بيانات فئة الخدمة: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * الحصول على قائمة فئات الخدمات
     * @return array قائمة الفئات
     */
    public function getServiceCategories() {
        try {
            $this->db->prepare("SELECT * FROM service_categories ORDER BY name ASC");
            return $this->db->fetchAll();
        } catch (Exception $e) {
            error_log('خطأ أثناء استرجاع قائمة فئات الخدمات: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * الحصول على الخدمات الأكثر مبيعاً
     * @param int $limit عدد الخدمات
     * @param int $branchId معرف الفرع (اختياري)
     * @param string $period الفترة (month, year, all)
     * @return array قائمة الخدمات
     */
    public function getTopServices($limit = 5, $branchId = null, $period = 'month') {
        try {
            // تحديد نطاق التاريخ
            $startDate = '';
            $endDate = date('Y-m-d');

            switch ($period) {
                case 'month':
                    $startDate = date('Y-m-01'); // أول يوم في الشهر الحالي
                    break;
                case 'year':
                    $startDate = date('Y-01-01'); // أول يوم في السنة الحالية
                    break;
                case 'all':
                    $startDate = '2000-01-01'; // تاريخ في الماضي البعيد
                    break;
                default:
                    $startDate = date('Y-m-01'); // افتراضي: الشهر الحالي
            }

            // تسجيل نطاق التاريخ للتحقق
            error_log("Getting top services from $startDate to $endDate");

            // استعلام للحصول على الخدمات الأكثر مبيعاً مع حساب إجمالي المبيعات بشكل صحيح
            $sql = "SELECT s.id, s.name, s.price,
                           COUNT(ii.id) as count,
                           SUM(ii.total) as total_sales
                    FROM services s
                    JOIN invoice_items ii ON s.id = ii.item_id
                    JOIN invoices i ON ii.invoice_id = i.id
                    WHERE ii.item_type = 'service'
                    AND i.created_at BETWEEN :start_date AND :end_date";

            $bindings = [
                ':start_date' => $startDate . ' 00:00:00',
                ':end_date' => $endDate . ' 23:59:59'
            ];

            if ($branchId) {
                $sql .= " AND i.branch_id = :branch_id";
                $bindings[':branch_id'] = $branchId;
            }

            $sql .= " GROUP BY s.id, s.name, s.price
                      ORDER BY count DESC
                      LIMIT :limit";

            // تسجيل الاستعلام للتحقق
            error_log('SQL for getTopServices: ' . $sql);

            $this->db->prepare($sql);

            // ربط القيم
            foreach ($bindings as $param => $value) {
                $this->db->bind($param, $value);
                error_log("Binding $param = $value");
            }

            $this->db->bind(':limit', $limit, PDO::PARAM_INT);

            $results = $this->db->fetchAll();

            // تسجيل النتائج للتحقق
            error_log('Top services results: ' . json_encode($results));

            return $results;
        } catch (Exception $e) {
            error_log('خطأ أثناء استرجاع الخدمات الأكثر مبيعاً: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * إضافة خدمة جديدة
     * @param array $serviceData بيانات الخدمة
     * @return int|false معرف الخدمة الجديدة أو false إذا فشلت العملية
     */
    public function addService($serviceData) {
        try {
            $this->db->beginTransaction();

            // إضافة الخدمة
            $this->db->prepare("INSERT INTO services (name, description, price, duration, category_id, is_active, branch_id)
                              VALUES (:name, :description, :price, :duration, :category_id, :is_active, :branch_id)");

            $this->db->bind(':name', $serviceData['name']);
            $this->db->bind(':description', $serviceData['description'] ?? null);
            $this->db->bind(':price', $serviceData['price']);
            $this->db->bind(':duration', $serviceData['duration'] ?? 30);
            $this->db->bind(':category_id', $serviceData['category_id'] ?? null);
            $this->db->bind(':is_active', $serviceData['is_active'] ?? 1);
            $this->db->bind(':branch_id', $serviceData['branch_id'] ?? null);

            $this->db->execute();
            $serviceId = $this->db->lastInsertId();

            // إضافة الموظفين المرتبطين بالخدمة
            if (isset($serviceData['employees']) && is_array($serviceData['employees']) && !empty($serviceData['employees'])) {
                foreach ($serviceData['employees'] as $employeeId) {
                    $this->db->prepare("INSERT INTO service_employees (service_id, employee_id) VALUES (:service_id, :employee_id)");
                    $this->db->bind(':service_id', $serviceId);
                    $this->db->bind(':employee_id', $employeeId);
                    $this->db->execute();
                }
            }

            $this->db->commit();
            return (int)$serviceId;
        } catch (Exception $e) {
            $this->db->rollBack();
            error_log('خطأ أثناء إضافة خدمة: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * تحديث بيانات خدمة
     * @param int $serviceId معرف الخدمة
     * @param array $serviceData البيانات المحدثة
     * @return bool نجاح أو فشل العملية
     */
    public function updateService($serviceId, $serviceData) {
        try {
            $this->db->beginTransaction();

            // تحديث بيانات الخدمة
            $this->db->prepare("UPDATE services
                              SET name = :name,
                                  description = :description,
                                  price = :price,
                                  duration = :duration,
                                  category_id = :category_id,
                                  is_active = :is_active,
                                  branch_id = :branch_id
                              WHERE id = :id");

            $this->db->bind(':name', $serviceData['name']);
            $this->db->bind(':description', $serviceData['description'] ?? null);
            $this->db->bind(':price', $serviceData['price']);
            $this->db->bind(':duration', $serviceData['duration'] ?? 30);
            $this->db->bind(':category_id', $serviceData['category_id'] ?? null);
            $this->db->bind(':is_active', $serviceData['is_active'] ?? 1);
            $this->db->bind(':branch_id', $serviceData['branch_id'] ?? null);
            $this->db->bind(':id', $serviceId);

            $this->db->execute();

            // تحديث الموظفين المرتبطين بالخدمة
            if (isset($serviceData['employees'])) {
                // حذف الارتباطات الحالية
                $this->db->prepare("DELETE FROM service_employees WHERE service_id = :service_id");
                $this->db->bind(':service_id', $serviceId);
                $this->db->execute();

                // إضافة الارتباطات الجديدة
                if (is_array($serviceData['employees']) && !empty($serviceData['employees'])) {
                    foreach ($serviceData['employees'] as $employeeId) {
                        $this->db->prepare("INSERT INTO service_employees (service_id, employee_id) VALUES (:service_id, :employee_id)");
                        $this->db->bind(':service_id', $serviceId);
                        $this->db->bind(':employee_id', $employeeId);
                        $this->db->execute();
                    }
                }
            }

            $this->db->commit();
            return true;
        } catch (Exception $e) {
            $this->db->rollBack();
            error_log('خطأ أثناء تحديث بيانات الخدمة: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * إنشاء تقرير الخدمات
     * @param array $filters فلاتر البحث (اختياري)
     * @return array تقرير الخدمات
     */
    public function generateServicesReport($filters = []) {
        try {
            $report = [
                'filters' => $filters,
                'summary' => [],
                'top_services' => [],
                'categories' => [],
                'services_by_employee' => [],
                'services_performance' => []
            ];

            // عدد الخدمات
            $report['summary']['total_services'] = $this->getServicesCount($filters);

            // أفضل الخدمات
            $report['top_services'] = $this->getTopServices(
                5,
                $filters['branch_id'] ?? null,
                $filters['period'] ?? 'month'
            );

            // الخدمات حسب الفئة
            $report['categories'] = $this->getServiceCategoriesSummary($filters);

            // أداء الخدمات
            $report['services_performance'] = $this->getServicesPerformanceDetails($filters);

            // الخدمات حسب الموظفين
            $report['services_by_employee'] = $this->getServicesByEmployee($filters);

            return $report;
        } catch (Exception $e) {
            error_log('خطأ أثناء إنشاء تقرير الخدمات: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * الحصول على ملخص فئات الخدمات
     * @param array $filters فلاتر البحث (اختياري)
     * @return array ملخص فئات الخدمات
     */
    private function getServiceCategoriesSummary($filters = []) {
        try {
            $sql = "SELECT sc.id, sc.name,
                           COUNT(DISTINCT s.id) as services_count,
                           SUM(ii.total) as total_sales,
                           COUNT(DISTINCT ii.id) as total_bookings
                    FROM service_categories sc
                    LEFT JOIN services s ON sc.id = s.category_id
                    LEFT JOIN invoice_items ii ON s.id = ii.item_id AND ii.item_type = 'service'
                    LEFT JOIN invoices i ON ii.invoice_id = i.id";

            $whereConditions = [];
            $bindings = [];

            // تطبيق الفلاتر
            if (!empty($filters['branch_id'])) {
                $whereConditions[] = "s.branch_id = :branch_id";
                $bindings[':branch_id'] = $filters['branch_id'];
            }

            if (!empty($filters['start_date']) && !empty($filters['end_date'])) {
                $whereConditions[] = "i.created_at BETWEEN :start_date AND :end_date";
                $bindings[':start_date'] = $filters['start_date'] . ' 00:00:00';
                $bindings[':end_date'] = $filters['end_date'] . ' 23:59:59';
            } elseif (!empty($filters['month']) && !empty($filters['year'])) {
                $whereConditions[] = "MONTH(i.created_at) = :month AND YEAR(i.created_at) = :year";
                $bindings[':month'] = $filters['month'];
                $bindings[':year'] = $filters['year'];
            }

            // إضافة شروط WHERE
            $sql .= " WHERE " . implode(' AND ', $whereConditions);

            $sql .= " GROUP BY sc.id, sc.name
                      ORDER BY total_sales DESC";

            $this->db->prepare($sql);

            // ربط القيم
            foreach ($bindings as $param => $value) {
                $this->db->bind($param, $value);
            }

            return $this->db->fetchAll();
        } catch (Exception $e) {
            error_log('خطأ أثناء استرجاع ملخص فئات الخدمات: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * الحصول على تفاصيل أداء الخدمات
     * @param array $filters فلاتر البحث (اختياري)
     * @return array تفاصيل أداء الخدمات
     */
    private function getServicesPerformanceDetails($filters = []) {
        try {
            $sql = "SELECT s.id, s.name, s.price, s.duration,
                           COUNT(DISTINCT ii.id) as total_bookings,
                           SUM(ii.total) as total_sales,
                           AVG(ii.total) as avg_sales_price,
                           COUNT(DISTINCT i.id) as total_invoices,
                           sc.name as category_name
                    FROM services s
                    LEFT JOIN invoice_items ii ON s.id = ii.item_id AND ii.item_type = 'service'
                    LEFT JOIN invoices i ON ii.invoice_id = i.id
                    LEFT JOIN service_categories sc ON s.category_id = sc.id";

            $whereConditions = [];
            $bindings = [];

            // تطبيق الفلاتر
            if (!empty($filters['branch_id'])) {
                $whereConditions[] = "s.branch_id = :branch_id";
                $bindings[':branch_id'] = $filters['branch_id'];
            }

            if (!empty($filters['category_id'])) {
                $whereConditions[] = "s.category_id = :category_id";
                $bindings[':category_id'] = $filters['category_id'];
            }

            if (!empty($filters['start_date']) && !empty($filters['end_date'])) {
                $whereConditions[] = "i.created_at BETWEEN :start_date AND :end_date";
                $bindings[':start_date'] = $filters['start_date'] . ' 00:00:00';
                $bindings[':end_date'] = $filters['end_date'] . ' 23:59:59';
            } elseif (!empty($filters['month']) && !empty($filters['year'])) {
                $whereConditions[] = "MONTH(i.created_at) = :month AND YEAR(i.created_at) = :year";
                $bindings[':month'] = $filters['month'];
                $bindings[':year'] = $filters['year'];
            }

            // إضافة شروط WHERE
            $sql .= " WHERE " . implode(' AND ', $whereConditions);

            $sql .= " GROUP BY s.id, s.name, s.price, s.duration, sc.name
                      ORDER BY total_sales DESC";

            $this->db->prepare($sql);

            // ربط القيم
            foreach ($bindings as $param => $value) {
                $this->db->bind($param, $value);
            }

            return $this->db->fetchAll();
        } catch (Exception $e) {
            error_log('خطأ أثناء استرجاع تفاصيل أداء الخدمات: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * الحصول على الخدمات حسب الموظفين
     * @param array $filters فلاتر البحث (اختياري)
     * @return array الخدمات حسب الموظفين
     */
    private function getServicesByEmployee($filters = []) {
        try {
            $sql = "SELECT e.id as employee_id, e.name as employee_name,
                           s.id as service_id, s.name as service_name,
                           COUNT(DISTINCT ii.id) as total_bookings,
                           SUM(ii.total) as total_sales
                    FROM employees e
                    JOIN service_employees se ON e.id = se.employee_id
                    JOIN services s ON se.service_id = s.id
                    LEFT JOIN invoice_items ii ON s.id = ii.item_id AND ii.employee_id = e.id AND ii.item_type = 'service'
                    LEFT JOIN invoices i ON ii.invoice_id = i.id";

            $whereConditions = [];
            $bindings = [];

            // تطبيق الفلاتر
            if (!empty($filters['branch_id'])) {
                $whereConditions[] = "e.branch_id = :branch_id";
                $bindings[':branch_id'] = $filters['branch_id'];
            }

            if (!empty($filters['start_date']) && !empty($filters['end_date'])) {
                $whereConditions[] = "i.created_at BETWEEN :start_date AND :end_date";
                $bindings[':start_date'] = $filters['start_date'] . ' 00:00:00';
                $bindings[':end_date'] = $filters['end_date'] . ' 23:59:59';
            } elseif (!empty($filters['month']) && !empty($filters['year'])) {
                $whereConditions[] = "MONTH(i.created_at) = :month AND YEAR(i.created_at) = :year";
                $bindings[':month'] = $filters['month'];
                $bindings[':year'] = $filters['year'];
            }

            // إضافة شروط WHERE
            $sql .= " WHERE " . implode(' AND ', $whereConditions);

            $sql .= " GROUP BY e.id, e.name, s.id, s.name
                      ORDER BY total_sales DESC";

            $this->db->prepare($sql);

            // ربط القيم
            foreach ($bindings as $param => $value) {
                $this->db->bind($param, $value);
            }

            return $this->db->fetchAll();
        } catch (Exception $e) {
            error_log('خطأ أثناء استرجاع الخدمات حسب الموظفين: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * مقارنة أداء الخدمات بين فترتين
     * @param array $period1 الفترة الأولى (start_date, end_date)
     * @param array $period2 الفترة الثانية (start_date, end_date)
     * @param int $branchId معرف الفرع (اختياري)
     * @return array نتائج المقارنة
     */
    public function compareServicesPeriods($period1, $period2, $branchId = null) {
        try {
            // الفترة الأولى
            $filters1 = [
                'start_date' => $period1['start_date'],
                'end_date' => $period1['end_date']
            ];

            if ($branchId) {
                $filters1['branch_id'] = $branchId;
            }

            // الفترة الثانية
            $filters2 = [
                'start_date' => $period2['start_date'],
                'end_date' => $period2['end_date']
            ];

            if ($branchId) {
                $filters2['branch_id'] = $branchId;
            }

            // استرجاع مبيعات الخدمات للفترتين
            $serviceSales1 = $this->getServiceSalesByPeriod($period1, $branchId);
            $serviceSales2 = $this->getServiceSalesByPeriod($period2, $branchId);

            // حساب المبيعات الإجمالية
            $totalSales1 = array_sum(array_column($serviceSales1, 'total_sales'));
            $totalSales2 = array_sum(array_column($serviceSales2, 'total_sales'));

            // حساب الفرق والنسبة المئوية
            $difference = $totalSales2 - $totalSales1;
            $percentChange = $totalSales1 > 0 ? ($difference / $totalSales1) * 100 : 0;

            // مقارنة الخدمات
            $serviceComparison = [];
            $allServices = [];

            // تجميع كل الخدمات من الفترتين
            foreach ($serviceSales1 as $service) {
                $allServices[$service['service_id']] = $service['service_name'];
            }

            foreach ($serviceSales2 as $service) {
                $allServices[$service['service_id']] = $service['service_name'];
            }

            // مقارنة مبيعات كل خدمة
            foreach ($allServices as $serviceId => $serviceName) {
                $serviceTotal1 = 0;
                $serviceTotal2 = 0;
                $serviceCount1 = 0;
                $serviceCount2 = 0;

                // البحث عن المبيعات في الفترة الأولى
                foreach ($serviceSales1 as $service) {
                    if ($service['service_id'] == $serviceId) {
                        $serviceTotal1 = (float)$service['total_sales'];
                        $serviceCount1 = (int)$service['count'];
                        break;
                    }
                }

                // البحث عن المبيعات في الفترة الثانية
                foreach ($serviceSales2 as $service) {
                    if ($service['service_id'] == $serviceId) {
                        $serviceTotal2 = (float)$service['total_sales'];
                        $serviceCount2 = (int)$service['count'];
                        break;
                    }
                }

                $serviceDifference = $serviceTotal2 - $serviceTotal1;
                $servicePercentChange = $serviceTotal1 > 0 ?
                    ($serviceDifference / $serviceTotal1) * 100 : 0;

                $serviceComparison[] = [
                    'service_id' => $serviceId,
                    'service_name' => $serviceName,
                    'period1_total' => $serviceTotal1,
                    'period2_total' => $serviceTotal2,
                    'period1_count' => $serviceCount1,
                    'period2_count' => $serviceCount2,
                    'total_difference' => $serviceDifference,
                    'total_percent_change' => $servicePercentChange
                ];
            }

            // ترتيب التقرير حسب قيمة الفرق
            usort($serviceComparison, function($a, $b) {
                return abs($b['total_difference']) - abs($a['total_difference']);
            });

            return [
                'period1' => [
                    'start_date' => $period1['start_date'],
                    'end_date' => $period1['end_date'],
                    'total' => $totalSales1
                ],
                'period2' => [
                    'start_date' => $period2['start_date'],
                    'end_date' => $period2['end_date'],
                    'total' => $totalSales2
                ],
                'difference' => $difference,
                'percent_change' => $percentChange,
                'services' => $serviceComparison
            ];
        } catch (Exception $e) {
            error_log('خطأ أثناء مقارنة فترات الخدمات: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * استرجاع مبيعات الخدمات لفترة محددة
     * @param array $period الفترة (start_date, end_date)
     * @param int $branchId معرف الفرع (اختياري)
     * @return array مبيعات الخدمات
     */
    private function getServiceSalesByPeriod($period, $branchId = null) {
        try {
            $sql = "SELECT s.id as service_id, s.name as service_name,
                           SUM(ii.total) as total_sales,
                           COUNT(DISTINCT ii.id) as count
                    FROM services s
                    JOIN invoice_items ii ON s.id = ii.item_id AND ii.item_type = 'service'
                    JOIN invoices i ON ii.invoice_id = i.id";

            $whereConditions = [];
            $bindings = [];

            // تحديد نطاق التاريخ
            $whereConditions[] = "i.created_at BETWEEN :start_date AND :end_date";
            $bindings[':start_date'] = $period['start_date'] . ' 00:00:00';
            $bindings[':end_date'] = $period['end_date'] . ' 23:59:59';

            // تصفية حسب الفرع
            if ($branchId) {
                $whereConditions[] = "i.branch_id = :branch_id";
                $bindings[':branch_id'] = $branchId;
            }

            // إضافة شروط WHERE
            $sql .= " WHERE " . implode(' AND ', $whereConditions);

            $sql .= " GROUP BY s.id, s.name
                      ORDER BY total_sales DESC";

            $this->db->prepare($sql);

            // ربط القيم
            foreach ($bindings as $param => $value) {
                $this->db->bind($param, $value);
            }

            return $this->db->fetchAll();
        } catch (Exception $e) {
            error_log('خطأ أثناء استرجاع مبيعات الخدمات للفترة: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * التحقق من توفر موعد للخدمة
     * @param int $serviceId معرف الخدمة
     * @param string $date التاريخ
     * @param string $startTime وقت البداية
     * @param int $branchId معرف الفرع
     * @return bool توفر الموعد
     */
    public function checkServiceAvailability($serviceId, $date, $startTime, $branchId) {
        try {
            // الحصول على مدة الخدمة
            $this->db->prepare("SELECT duration FROM services WHERE id = :id");
            $this->db->bind(':id', $serviceId);
            $service = $this->db->fetch();

            if (!$service) {
                throw new Exception('الخدمة غير موجودة');
            }

            // حساب وقت النهاية
            $duration = $service['duration'];
            $startDateTime = new DateTime($date . ' ' . $startTime);
            $endDateTime = clone $startDateTime;
            $endDateTime->modify("+{$duration} minutes");

            // التحقق من تعارض المواعيد
            $this->db->prepare("SELECT COUNT(*) FROM appointments a
                              JOIN services s ON a.service_id = s.id
                              WHERE a.date = :date
                              AND a.branch_id = :branch_id
                              AND ((a.start_time BETWEEN :start_time AND :end_time)
                                   OR (a.end_time BETWEEN :start_time AND :end_time)
                                   OR (:start_time BETWEEN a.start_time AND a.end_time))");

            $this->db->bind(':date', $date);
            $this->db->bind(':branch_id', $branchId);
            $this->db->bind(':start_time', $startDateTime->format('H:i:s'));
            $this->db->bind(':end_time', $endDateTime->format('H:i:s'));

            $conflictCount = $this->db->fetchColumn();

            return $conflictCount == 0;
        } catch (Exception $e) {
            error_log('خطأ أثناء التحقق من توفر الموعد: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * الحصول على خدمات فرع محدد
     * @param int $branchId معرف الفرع
     * @param bool $activeOnly استرجاع الخدمات النشطة فقط (اختياري)
     * @return array قائمة الخدمات في الفرع
     */
    public function getBranchServices($branchId, $activeOnly = true) {
        try {
            $filters = [
                'branch_id' => $branchId
            ];

            if ($activeOnly) {
                $filters['is_active'] = 1;
            }

            return $this->getServices($filters);
        } catch (Exception $e) {
            error_log('خطأ في استرجاع خدمات الفرع: ' . $e->getMessage());
            return [];
        }
    }
}
