<?php
/**
 * ملف دوال الطباعة
 * يحتوي على دوال متخصصة في طباعة الفواتير والإيصالات الحرارية
 */

// منع الوصول المباشر للملف
if (!defined('BASEPATH')) {
    exit('لا يمكن الوصول المباشر لهذا الملف');
}


/**
 * دالة الطباعة باستخدام CUPS
 * تقوم بإرسال المحتوى إلى الطابعة المحددة
 * 
 * @param string $printerName اسم الطابعة
 * @param string $content محتوى الطباعة
 * @param array $options خيارات الطباعة الإضافية
 * @return bool نتيجة الطباعة
 */
function cups_print($printerName, $content, $options = []) {
    // التحقق من توفر امتداد CUPS
    if (!extension_loaded('cups')) {
        // محاولة محاكاة الطباعة إذا لم يكن الامتداد متوفرًا
        return cups_print_fallback($printerName, $content, $options);
    }
    
    try {
        // الخيارات الافتراضية
        $defaultOptions = [
            'copies' => 1,
            'media' => 'custom', // حجم الورق
            'page-ranges' => '1', // نطاق الصفحات
            'print-quality' => 'draft', // جودة الطباعة
        ];
        
        // دمج الخيارات الافتراضية مع الخيارات المخصصة
        $printOptions = array_merge($defaultOptions, $options);
        
        // إنشاء مهمة طباعة
        $job = cups_create_job($printerName, $printOptions);
        
        if ($job === false) {
            throw new Exception('فشل إنشاء مهمة الطباعة');
        }
        
        // إرسال المحتوى للطباعة
        $printResult = cups_write_job($job, $content);
        
        if ($printResult === false) {
            throw new Exception('فشل كتابة محتوى الطباعة');
        }
        
        // إغلاق المهمة وإرسالها للطباعة
        $closeResult = cups_close_job($job);
        
        if ($closeResult === false) {
            throw new Exception('فشل إغلاق مهمة الطباعة');
        }
        
        return true;
    } catch (Exception $e) {
        // تسجيل الخطأ
        error_log('خطأ في الطباعة عبر CUPS: ' . $e->getMessage());
        
        // محاولة الطباعة البديلة
        return cups_print_fallback($printerName, $content, $options);
    }
}
/**
 * طريقة بديلة للطباعة عند عدم توفر امتداد CUPS
 * 
 * @param string $printerName اسم الطابعة
 * @param string $content محتوى الطباعة
 * @param array $options خيارات الطباعة الإضافية
 * @return bool نتيجة الطباعة
 */
function cups_print_fallback($printerName, $content, $options = []) {
    try {
        // التحقق من وجود لينكس أو نظام يونكس
        if (!defined('PHP_OS_FAMILY') || PHP_OS_FAMILY !== 'Linux') {
            throw new Exception('نظام التشغيل غير مدعوم');
        }
        
        // تحضير الملف المؤقت للطباعة
        $tempFile = tempnam(sys_get_temp_dir(), 'print_');
        
        // التأكد من أن الملف تم إنشاؤه
        if ($tempFile === false) {
            throw new Exception('فشل إنشاء الملف المؤقت');
        }
        
        // كتابة المحتوى في الملف المؤقت
        $writeResult = file_put_contents($tempFile, $content);
        
        if ($writeResult === false) {
            throw new Exception('فشل كتابة المحتوى في الملف');
        }
        
        // بناء أمر الطباعة
        $copies = isset($options['copies']) ? intval($options['copies']) : 1;
        $printCommand = sprintf(
            'lp -d %s -n %d %s 2>&1', 
            escapeshellarg($printerName), 
            $copies, 
            escapeshellarg($tempFile)
        );
        
        // تنفيذ أمر الطباعة
        $output = [];
        $returnVar = 0;
        exec($printCommand, $output, $returnVar);
        
        // حذف الملف المؤقت
        unlink($tempFile);
        
        // التحقق من نجاح الطباعة
        if ($returnVar !== 0) {
            throw new Exception('فشل أمر الطباعة: ' . implode("\n", $output));
        }
        
        return true;
    } catch (Exception $e) {
        // تسجيل الخطأ
        error_log('خطأ في الطباعة البديلة: ' . $e->getMessage());
        
        // إنشاء سجل الطباعة
        $logFile = BASEPATH . '/logs/print_error_' . date('Y-m-d') . '.log';
        file_put_contents(
            $logFile, 
            date('Y-m-d H:i:s') . " - " . $e->getMessage() . "\n", 
            FILE_APPEND
        );
        
        return false;
    }
}

/**
 * الحصول على قائمة الطابعات المتاحة
 * 
 * @return array قائمة أسماء الطابعات
 */
function cups_list_printers() {
    try {
        // محاولة استخدام امتداد CUPS
        if (extension_loaded('cups')) {
            return cups_get_printers();
        }
        
        // طريقة بديلة للينكس
        if (defined('PHP_OS_FAMILY') && PHP_OS_FAMILY === 'Linux') {
            exec('lpstat -a', $output, $returnVar);
            
            if ($returnVar === 0) {
                // تحويل المخرجات إلى قائمة أسماء الطابعات
                $printers = [];
                foreach ($output as $line) {
                    $parts = explode(' ', $line);
                    if (!empty($parts[0])) {
                        $printers[] = $parts[0];
                    }
                }
                return $printers;
            }
        }
        
        // إرجاع قائمة فارغة إذا فشلت العملية
        return [];
    } catch (Exception $e) {
        // تسجيل الخطأ
        error_log('خطأ في استرجاع الطابعات: ' . $e->getMessage());
        return [];
    }
}
/**
 * الحصول على قائمة الطابعات المتاحة
 * 
 * @return array قائمة الطابعات المتصلة والمهيأة
 */
function cups_get_printers() {
    // التحقق من وجود امتداد CUPS
    if (extension_loaded('cups')) {
        // استخدام الدالة المدمجة إذا كان الامتداد متوفرًا
        return cups_get_printers_native();
    }
    
    // طرق بديلة للحصول على الطابعات
    return cups_print_fallback();
}

/**
 * الحصول على الطابعات باستخدام الامتداد المدمج
 * 
 * @return array قائمة الطابعات
 */
function cups_get_printers_native() {
    try {
        // استخدام الدالة المدمجة في امتداد CUPS
        $printers = extension_loaded('cups') ? cups_get_printers() : [];
        
        // التحقق من أن النتيجة مصفوفة
        return is_array($printers) ? $printers : [];
    } catch (Exception $e) {
        // تسجيل الخطأ
        error_log('خطأ في استرجاع الطابعات باستخدام CUPS: ' . $e->getMessage());
        return [];
    }
}
/**
 * التحقق من توفر طابعة محددة
 * 
 * @param string $printerName اسم الطابعة
 * @return bool توفر الطابعة
 */
function cups_printer_exists($printerName) {
    $printers = cups_list_printers();
    return in_array($printerName, $printers);
}
/**
 * توليد محتوى الإيصال الحراري
 * @param array $invoiceData بيانات الفاتورة
 * @return string محتوى الإيصال
 */
function generateThermalReceipt($invoiceData, $width = DEFAULT_RECEIPT_WIDTH) {
    // التحقق من وجود بيانات الفاتورة
    if (empty($invoiceData)) {
        return 'لا توجد بيانات للطباعة';
    }
    
    // استرجاع إعدادات الفرع
    $branchSettings = getBranchSettings($invoiceData['branch_id'] ?? null);
    
    // بدء بناء محتوى الإيصال
    $receipt = '';
    
    // شعار أو اسم المنشأة
    $receipt .= centerText($branchSettings['name'] ?? SYSTEM_NAME, $width) . "\n";
    $receipt .= centerText($branchSettings['address'] ?? '', $width) . "\n";
    $receipt .= centerText($branchSettings['phone'] ?? '', $width) . "\n";
    $receipt .= str_repeat('-', $width) . "\n";
    
    // معلومات الفاتورة الأساسية
    $receipt .= formatReceiptLine('رقم الفاتورة', $invoiceData['invoice_number'], $width);
    $receipt .= formatReceiptLine('التاريخ', formatDate($invoiceData['created_at']), $width);
    $receipt .= formatReceiptLine('الوقت', date('h:i A', strtotime($invoiceData['created_at'])), $width);
    
    // معلومات العميل
    if (!empty($invoiceData['customer_id'])) {
        $receipt .= str_repeat('-', $width) . "\n";
        $receipt .= formatReceiptLine('العميل', 
            !empty($invoiceData['customer_name']) ? $invoiceData['customer_name'] : 'عميل نقدي', 
            $width
        );
        
        if (!empty($invoiceData['customer_phone'])) {
            $receipt .= formatReceiptLine('رقم الهاتف', $invoiceData['customer_phone'], $width);
        }
    }
    
    $receipt .= str_repeat('-', $width) . "\n";
    
    // عنوان تفاصيل الفاتورة
    $receipt .= centerText('تفاصيل الفاتورة', $width) . "\n";
    
    // عرض العناصر (خدمات ومنتجات)
    if (!empty($invoiceData['items'])) {
        foreach ($invoiceData['items'] as $item) {
            // اسم العنصر (مقتطع إذا كان طويلًا)
            $itemName = truncateText($item['name'], floor($width * 0.6));
            
            // سعر العنصر والكمية
            $itemDetails = $item['quantity'] . ' × ' . formatMoney($item['price']);
            $itemTotal = formatMoney($item['total']);
            
            // سطر العنصر
            $receipt .= str_pad($itemName, floor($width * 0.4), ' ', STR_PAD_RIGHT);
            $receipt .= str_pad($itemDetails, floor($width * 0.3), ' ', STR_PAD_RIGHT);
            $receipt .= str_pad($itemTotal, floor($width * 0.3), ' ', STR_PAD_LEFT) . "\n";
        }
    }
    
    $receipt .= str_repeat('-', $width) . "\n";
    
    // حسابات الفاتورة
    $receipt .= formatReceiptLine('المجموع', formatMoney($invoiceData['total_amount']), $width);
    
    // الخصم (إن وجد)
    if (!empty($invoiceData['discount_amount']) && $invoiceData['discount_amount'] > 0) {
        $discountType = $invoiceData['discount_type'] === 'percentage' 
            ? '(' . $invoiceData['discount_amount'] . '%)' 
            : '';
        $receipt .= formatReceiptLine('الخصم ' . $discountType, 
            formatMoney($invoiceData['discount_amount']), 
            $width
        );
    }
    
    // الضريبة (إن وجدت)
    if (!empty($invoiceData['tax_amount']) && $invoiceData['tax_amount'] > 0) {
        $receipt .= formatReceiptLine('الضريبة', formatMoney($invoiceData['tax_amount']), $width);
    }
    
    // الإجمالي النهائي
    $receipt .= formatReceiptLine('الإجمالي', formatMoney($invoiceData['final_amount']), $width, true);
    
    $receipt .= str_repeat('-', $width) . "\n";
    
    // طريقة الدفع
    $paymentMethodText = [
        'cash' => 'نقدي',
        'card' => 'بطاقة',
        'other' => 'أخرى'
    ];
    $receipt .= formatReceiptLine('طريقة الدفع', 
        $paymentMethodText[$invoiceData['payment_method']] ?? $invoiceData['payment_method'], 
        $width
    );
    
    // الموظف والكاشير
    if (!empty($invoiceData['employee_name'])) {
        $receipt .= formatReceiptLine('الموظف', $invoiceData['employee_name'], $width);
    }
    
    if (!empty($invoiceData['cashier_name'])) {
        $receipt .= formatReceiptLine('الكاشير', $invoiceData['cashier_name'], $width);
    }
    
    // QR Code أو باركود (اختياري)
    $receipt .= str_repeat('-', $width) . "\n";
    $receipt .= centerText('يمكنك مسح الكود للتحقق', $width) . "\n";
    
    // التذييل
    $receipt .= str_repeat('-', $width) . "\n";
    $receipt .= centerText('شكرًا لتعاملكم معنا', $width) . "\n";
    $receipt .= centerText('نرحب بكم دائمًا', $width) . "\n";
    
    return $receipt;
}
/**
 * محاذاة النص في وسط الإيصال
 * @param string $text النص المراد محاذاته
 * @param int $width عرض الإيصال
 * @return string النص محاذى للوسط
 */
function centerText($text, $width) {
    $textLength = mb_strlen($text);
    $padLength = floor(($width - $textLength) / 2);
    return str_repeat(' ', max(0, $padLength)) . $text;
}

/**
 * تنسيق سطر في الإيصال
 * @param string $label العنوان
 * @param string $value القيمة
 * @param int $width عرض الإيصال
 * @param bool $highlight هل يتم تمييز السطر
 * @return string السطر المنسق
 */

function formatReceiptLine($label, $value, $width, $highlight = false) {
    $labelWidth = floor($width * 0.4);
    $valueWidth = floor($width * 0.6);
    
    $formattedLine = str_pad($label, $labelWidth, ' ', STR_PAD_RIGHT);
    $formattedLine .= str_pad($value, $valueWidth, ' ', STR_PAD_LEFT);
    
    return $highlight ? strtoupper($formattedLine) . "\n" : $formattedLine . "\n";
}

/**
 * طباعة الفاتورة على الطابعة الحرارية
 * @param array $invoiceData بيانات الفاتورة
 * @param string $printerName اسم الطابعة
 * @return bool نجاح أو فشل الطباعة
 */
function printThermalReceipt($invoiceData, $printerName = null) {
    try {
        // توليد محتوى الإيصال
        $receiptContent = generateThermalReceipt($invoiceData);
        
        // التحقق من وجود مكتبة CUPS أو محاكي طابعة
        if (!extension_loaded('cups')) {
            throw new Exception('مكتبة CUPS غير مثبتة');
        }
        
        // استخدام اسم الطابعة الافتراضي إذا لم يتم تحديده
        $printerName = $printerName ?? getDefaultPrinter();
        
        // إرسال الطباعة
        $printResult = cups_print($printerName, $receiptContent);
        
        return $printResult !== false;
    } catch (Exception $e) {
        // تسجيل الخطأ
        error_log('خطأ في الطباعة: ' . $e->getMessage());
        return false;
    }
}

/**
 * الحصول على الطابعة الافتراضية
 * @return string اسم الطابعة الافتراضية
 */
function getDefaultPrinter() {
    // يمكن تخزين اسم الطابعة الافتراضية في جدول الإعدادات
    global $db;
    
    try {
        $db->prepare("SELECT setting_value FROM settings WHERE setting_key = 'default_printer'");
        $printerName = $db->fetchColumn();
        
        return $printerName ?: 'default';
    } catch (Exception $e) {
        // إذا فشل استرجاع الإعداد، العودة للقيمة الافتراضية
        return 'default';
    }
}

/**
 * الحصول على إعدادات الفرع
 * @param int $branchId معرف الفرع
 * @return array إعدادات الفرع
 */
function getBranchSettings($branchId = null) {
    global $db;
    
    try {
        if ($branchId) {
            $db->prepare("SELECT name, address, phone FROM branches WHERE id = :id");
            $db->bind(':id', $branchId);
            $branchInfo = $db->fetch();
        }
        
        // إذا لم يتم العثور على الفرع أو لم يتم تمرير معرف الفرع
        if (!$branchInfo) {
            return [
                'name' => SYSTEM_NAME,
                'address' => '',
                'phone' => ''
            ];
        }
        
        return $branchInfo;
    } catch (Exception $e) {
        // في حالة الخطأ، إرجاع القيم الافتراضية
        error_log('خطأ في استرجاع إعدادات الفرع: ' . $e->getMessage());
        return [
            'name' => SYSTEM_NAME,
            'address' => '',
            'phone' => ''
        ];
    }
}

/**
 * توليد رابط تنزيل الفاتورة كـ PDF
 * @param int $invoiceId معرف الفاتورة
 * @return string رابط تنزيل الفاتورة
 */
function generateInvoicePDFLink($invoiceId) {
    return API_URL . 'invoices.php?action=pdf&id=' . $invoiceId;
}

/**
 * توليد رابط طباعة الفاتورة
 * @param int $invoiceId معرف الفاتورة
 * @return string رابط طباعة الفاتورة
 */
function generateInvoicePrintLink($invoiceId) {
    return API_URL . 'invoices.php?action=print&id=' . $invoiceId;
}

/**
 * إرسال الفاتورة عبر البريد الإلكتروني
 * @param int $invoiceId معرف الفاتورة
 * @param string $email البريد الإلكتروني المستهدف
 * @return bool نجاح أو فشل الإرسال
 */
function emailInvoice($invoiceId, $email) {
    // التحقق من صحة البريد الإلكتروني
    if (!isValidEmail($email)) {
        return false;
    }
    
    try {
        // جلب بيانات الفاتورة
        global $db;
        $db->prepare("SELECT * FROM invoices WHERE id = :id");
        $db->bind(':id', $invoiceId);
        $invoiceData = $db->fetch();
        
        if (!$invoiceData) {
            return false;
        }
        
        // توليد محتوى PDF
        $pdfContent = generateInvoicePDF($invoiceData);
        
        // إعداد بيانات البريد الإلكتروني
        $subject = 'فاتورة رقم ' . $invoiceData['invoice_number'];
        $body = 'مرفق الفاتورة الخاصة بكم';
        
        // إرسال البريد مع المرفق
        return sendEmailWithAttachment($email, $subject, $body, $pdfContent, 'invoice.pdf');
    } catch (Exception $e) {
        error_log('خطأ في إرسال الفاتورة: ' . $e->getMessage());
        return false;
    }
}

/**
 * توليد ملف PDF للفاتورة
 * @param array $invoiceData بيانات الفاتورة
 * @return string محتوى ملف PDF
 */
function generateInvoicePDF($invoiceData) {
    // استخدام مكتبة TCPDF أو FPDF لتوليد PDF
    // يتطلب تثبيت المكتبة مسبقًا
    require_once 'path/to/tcpdf/tcpdf.php';
    
    $pdf = new TCPDF(PDF_PAGE_ORIENTATION, PDF_UNIT, PDF_PAGE_FORMAT, true, 'UTF-8', false);
    
    // إعداد معلومات الوثيقة
    $pdf->SetCreator(SYSTEM_NAME);
    $pdf->SetTitle('فاتورة رقم ' . $invoiceData['invoice_number']);
    
    // إضافة صفحة
    $pdf->AddPage();
    
    // محتوى الفاتورة
    $html = '
        <h1>فاتورة</h1>
        <p>رقم الفاتورة: ' . $invoiceData['invoice_number'] . '</p>
        <p>التاريخ: ' . formatDate($invoiceData['created_at']) . '</p>
        <!-- إضافة المزيد من التفاصيل -->
    ';
    
    $pdf->writeHTML($html, true, false, true, false, '');
    
    // إرجاع محتوى PDF كسلسلة
    return $pdf->Output('invoice.pdf', 'S');
}

/**
 * إرسال بريد إلكتروني مع مرفق
 * @param string $email البريد الإلكتروني
 * @param string $subject الموضوع
 * @param string $body نص الرسالة
 * @param string $attachmentContent محتوى المرفق
 * @param string $attachmentName اسم المرفق
 * @return bool نجاح أو فشل الإرسال
 */
function sendEmailWithAttachment($email, $subject, $body, $attachmentContent, $attachmentName) {
    try {
        require_once 'path/to/PHPMailer/src/Exception.php';
        require_once 'path/to/PHPMailer/src/PHPMailer.php';
        require_once 'path/to/PHPMailer/src/SMTP.php';
        
        // إنشاء كائن PHPMailer
        $mail = new PHPMailer(true);
        
        // إعدادات الإرسال من جدول الإعدادات
        $smtpSettings = getSmtpSettings();
        
        // إعداد خادم SMTP
        $mail->isSMTP();
        $mail->Host       = $smtpSettings['host'];
        $mail->SMTPAuth   = true;
        $mail->Username   = $smtpSettings['username'];
        $mail->Password   = $smtpSettings['password'];
        $mail->SMTPSecure = $smtpSettings['encryption'];
        $mail->Port       = $smtpSettings['port'];
        
        // إعداد المرسل
        $mail->setFrom($smtpSettings['sender_email'], SYSTEM_NAME);
        
        // إضافة المستلم
        $mail->addAddress($email);
        
        // المرفق
        $mail->addStringAttachment($attachmentContent, $attachmentName);
        
        // محتوى الرسالة
        $mail->isHTML(true);
        $mail->Subject = $subject;
        $mail->Body    = $body;
        
        // إرسال البريد الإلكتروني
        return $mail->send();
    } catch (Exception $e) {
        // تسجيل خطأ الإرسال
        error_log('خطأ في إرسال البريد الإلكتروني: ' . $mail->ErrorInfo);
        return false;
    }
}

/**
 * الحصول على إعدادات SMTP من جدول الإعدادات
 * @return array إعدادات SMTP
 */
function getSmtpSettings() {
    global $db;
    
    try {
        // استرجاع إعدادات SMTP من جدول الإعدادات
        $settings = [];
        $smtpKeys = [
            'smtp_host' => 'host',
            'smtp_username' => 'username', 
            'smtp_password' => 'password',
            'smtp_port' => 'port',
            'smtp_encryption' => 'encryption',
            'smtp_sender_email' => 'sender_email'
        ];
        
        foreach ($smtpKeys as $dbKey => $arrayKey) {
            $db->prepare("SELECT setting_value FROM settings WHERE setting_key = :key");
            $db->bind(':key', $dbKey);
            $settings[$arrayKey] = $db->fetchColumn() ?: '';
        }
        
        // القيم الافتراضية إذا لم يتم العثور على الإعدادات
        $settings['host'] = $settings['host'] ?: 'localhost';
        $settings['port'] = $settings['port'] ?: 25;
        $settings['encryption'] = $settings['encryption'] ?: 'tls';
        $settings['sender_email'] = $settings['sender_email'] ?: 'noreply@' . parse_url(BASE_URL, PHP_URL_HOST);
        
        return $settings;
    } catch (Exception $e) {
        // في حالة الخطأ، إرجاع إعدادات افتراضية
        error_log('خطأ في استرجاع إعدادات SMTP: ' . $e->getMessage());
        return [
            'host' => 'localhost',
            'username' => '',
            'password' => '',
            'port' => 25,
            'encryption' => 'tls',
            'sender_email' => 'noreply@localhost'
        ];
    }
}

/**
 * توليد باركود للفاتورة
 * @param string $invoiceNumber رقم الفاتورة
 * @param string $type نوع الباركود (code128, qr, etc)
 * @return string مسار أو محتوى الباركود
 */
function generateInvoiceBarcode($invoiceNumber, $type = 'code128') {
    try {
        // مكتبات توليد الباركود
        require_once 'path/to/barcode/autoload.php';
        
        switch ($type) {
            case 'qr':
                $generator = new \Picqer\Barcode\BarcodeGeneratorQR();
                $barcodeContent = $generator->generate($invoiceNumber);
                break;
            
            case 'code128':
            default:
                $generator = new \Picqer\Barcode\BarcodeGeneratorPNG();
                $barcodeContent = $generator->getBarcode(
                    $invoiceNumber, 
                    $generator::TYPE_CODE_128, 
                    3, 
                    100
                );
                break;
        }
        
        // حفظ الباركود في مجلد مؤقت
        $uploadDir = BASEPATH . '/uploads/barcodes/';
        if (!is_dir($uploadDir)) {
            mkdir($uploadDir, 0755, true);
        }
        
        $filename = 'barcode_' . $invoiceNumber . '.png';
        $filepath = $uploadDir . $filename;
        
        file_put_contents($filepath, $barcodeContent);
        
        return BASE_URL . 'uploads/barcodes/' . $filename;
    } catch (Exception $e) {
        error_log('خطأ في توليد الباركود: ' . $e->getMessage());
        return '';
    }
}

/**
 * إنشاء رمز QR للفاتورة
 * @param array $invoiceData بيانات الفاتورة
 * @return string رابط أو محتوى رمز QR
 */
function generateInvoiceQRCode($invoiceData) {
    try {
        // إنشاء محتوى QR Code
        $qrContent = json_encode([
            'invoice_number' => $invoiceData['invoice_number'],
            'total_amount' => $invoiceData['final_amount'],
            'date' => $invoiceData['created_at'],
            'branch_name' => getBranchName($invoiceData['branch_id'])
        ]);
        
        // مكتبة توليد QR Code
        require_once 'path/to/phpqrcode/qrlib.php';
        
        // مجلد التخزين
        $uploadDir = BASEPATH . '/uploads/qrcodes/';
        if (!is_dir($uploadDir)) {
            mkdir($uploadDir, 0755, true);
        }
        
        $filename = 'qr_' . $invoiceData['invoice_number'] . '.png';
        $filepath = $uploadDir . $filename;
        
        // توليد QR Code
        QRcode::png($qrContent, $filepath, QR_ECLEVEL_L, 4);
        
        return BASE_URL . 'uploads/qrcodes/' . $filename;
    } catch (Exception $e) {
        error_log('خطأ في توليد رمز QR: ' . $e->getMessage());
        return '';
    }
}

/**
 * الحصول على اسم الفرع
 * @param int $branchId معرف الفرع
 * @return string اسم الفرع
 */
function getBranchName($branchId) {
    global $db;
    
    try {
        $db->prepare("SELECT name FROM branches WHERE id = :id");
        $db->bind(':id', $branchId);
        return $db->fetchColumn() ?: SYSTEM_NAME;
    } catch (Exception $e) {
        error_log('خطأ في استرجاع اسم الفرع: ' . $e->getMessage());
        return SYSTEM_NAME;
    }
}
