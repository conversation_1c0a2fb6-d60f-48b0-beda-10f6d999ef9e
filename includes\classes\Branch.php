<?php
/**
 * فئة Branch للتعامل مع الفروع
 */

// منع الوصول المباشر للملف
if (!defined('BASEPATH')) {
    exit('لا يمكن الوصول المباشر لهذا الملف');
}

class Branch {
    // كائن قاعدة البيانات
    private $db;

    /**
     * دالة البناء
     *
     * @param Database $db كائن قاعدة البيانات
     */
    public function __construct($db) {
        $this->db = $db;
    }

    /**
     * استرجاع قائمة الفروع
     *
     * @param array $filters معايير الفلترة
     * @return array قائمة الفروع
     */
    public function getBranches($filters = []) {
        // بناء استعلام SQL
        $sql = "SELECT b.*,
                       e.name AS manager_name
                FROM branches b
                LEFT JOIN employees e ON b.manager_id = e.id";

        $whereConditions = [];
        $bindings = [];

        // تطبيق الفلاتر
        if (!empty($filters['search'])) {
            $whereConditions[] = "(b.name LIKE :search OR b.address LIKE :search OR b.phone LIKE :search)";
            $bindings[':search'] = '%' . $filters['search'] . '%';
        }

        if (isset($filters['is_active']) && $filters['is_active'] !== '') {
            $whereConditions[] = "b.is_active = :is_active";
            $bindings[':is_active'] = $filters['is_active'];
        }

        // إضافة شروط WHERE
        if (!empty($whereConditions)) {
            $sql .= " WHERE " . implode(' AND ', $whereConditions);
        }

        // الترتيب
        $sortBy = isset($filters['sort_by']) ? $filters['sort_by'] : 'id';
        $sortDir = isset($filters['sort_dir']) ? $filters['sort_dir'] : 'DESC';

        // التحقق من صحة معايير الترتيب
        $allowedSortFields = ['id', 'name', 'created_at'];
        if (!in_array($sortBy, $allowedSortFields)) {
            $sortBy = 'id';
        }

        $allowedSortDirs = ['ASC', 'DESC'];
        if (!in_array(strtoupper($sortDir), $allowedSortDirs)) {
            $sortDir = 'DESC';
        }

        $sql .= " ORDER BY b.{$sortBy} {$sortDir}";

        // الحد والإزاحة
        if (isset($filters['limit']) && $filters['limit'] > 0) {
            $sql .= " LIMIT :limit";
            $bindings[':limit'] = (int)$filters['limit'];

            if (isset($filters['offset']) && $filters['offset'] >= 0) {
                $sql .= " OFFSET :offset";
                $bindings[':offset'] = (int)$filters['offset'];
            }
        }

        $this->db->prepare($sql);

        // ربط القيم
        foreach ($bindings as $param => $value) {
            $this->db->bind($param, $value);
        }

        return $this->db->fetchAll();
    }

    /**
     * استرجاع عدد الفروع
     *
     * @param array $filters معايير الفلترة
     * @return int عدد الفروع
     */
    public function getBranchesCount($filters = []) {
        // بناء استعلام SQL
        $sql = "SELECT COUNT(*) FROM branches b";

        $whereConditions = [];
        $bindings = [];

        // تطبيق الفلاتر
        if (!empty($filters['search'])) {
            $whereConditions[] = "(b.name LIKE :search OR b.address LIKE :search OR b.phone LIKE :search)";
            $bindings[':search'] = '%' . $filters['search'] . '%';
        }

        if (isset($filters['is_active']) && $filters['is_active'] !== '') {
            $whereConditions[] = "b.is_active = :is_active";
            $bindings[':is_active'] = $filters['is_active'];
        }

        // إضافة شروط WHERE
        if (!empty($whereConditions)) {
            $sql .= " WHERE " . implode(' AND ', $whereConditions);
        }

        $this->db->prepare($sql);

        // ربط القيم
        foreach ($bindings as $param => $value) {
            $this->db->bind($param, $value);
        }

        return $this->db->fetchColumn();
    }

    /**
     * استرجاع بيانات فرع محدد
     *
     * @param int $branchId معرف الفرع
     * @return array|false بيانات الفرع أو false إذا لم يتم العثور عليه
     */
    public function getBranchById($branchId) {
        $this->db->prepare("SELECT b.*,
                                   e.name AS manager_name
                            FROM branches b
                            LEFT JOIN employees e ON b.manager_id = e.id
                            WHERE b.id = :id");
        $this->db->bind(':id', $branchId);

        return $this->db->fetch();
    }

    /**
     * إضافة فرع جديد
     *
     * @param array $branchData بيانات الفرع
     * @return int معرف الفرع الجديد
     */
    public function addBranch($branchData) {
        // إضافة حقل country_code إذا لم يكن موجودًا في الجدول
        $this->ensureCountryCodeExists();

        $this->db->prepare("INSERT INTO branches (
                                name,
                                address,
                                phone,
                                country_code,
                                manager_id,
                                is_active,
                                created_at
                            ) VALUES (
                                :name,
                                :address,
                                :phone,
                                :country_code,
                                :manager_id,
                                :is_active,
                                NOW()
                            )");

        $this->db->bind(':name', $branchData['name']);
        $this->db->bind(':address', $branchData['address']);
        $this->db->bind(':phone', $branchData['phone']);
        $this->db->bind(':country_code', isset($branchData['country_code']) ? $branchData['country_code'] : '+20');
        $this->db->bind(':manager_id', $branchData['manager_id']);
        $this->db->bind(':is_active', $branchData['is_active']);

        $this->db->execute();

        return (int)$this->db->lastInsertId();
    }

    /**
     * تحديث بيانات فرع
     *
     * @param int $branchId معرف الفرع
     * @param array $branchData بيانات الفرع المحدثة
     * @return bool نجاح العملية
     */
    public function updateBranch($branchId, $branchData) {
        // إضافة حقل country_code إذا لم يكن موجودًا في الجدول
        $this->ensureCountryCodeExists();

        $this->db->prepare("UPDATE branches
                            SET name = :name,
                                address = :address,
                                phone = :phone,
                                country_code = :country_code,
                                manager_id = :manager_id,
                                is_active = :is_active,
                                updated_at = NOW()
                            WHERE id = :id");

        $this->db->bind(':id', $branchId);
        $this->db->bind(':name', $branchData['name']);
        $this->db->bind(':address', $branchData['address']);
        $this->db->bind(':phone', $branchData['phone']);
        $this->db->bind(':country_code', $branchData['country_code'] ?? '+20');
        $this->db->bind(':manager_id', $branchData['manager_id']);
        $this->db->bind(':is_active', $branchData['is_active']);

        return $this->db->execute();
    }

    /**
     * حذف فرع
     *
     * @param int $branchId معرف الفرع
     * @return bool نجاح العملية
     */
    public function deleteBranch($branchId) {
        $this->db->prepare("DELETE FROM branches WHERE id = :id");
        $this->db->bind(':id', $branchId);

        return $this->db->execute();
    }

    /**
     * إنشاء تقرير أداء الفرع
     *
     * @param int $branchId معرف الفرع
     * @param string $startDate تاريخ البداية
     * @param string $endDate تاريخ النهاية
     * @return array بيانات التقرير
     */
    public function generateBranchReport($branchId, $startDate, $endDate) {
        // استرجاع بيانات الفرع
        $branch = $this->getBranchById($branchId);

        if (!$branch) {
            return [];
        }

        // إنشاء كائنات النماذج
        $invoiceModel = new Invoice($this->db);
        $employeeModel = new Employee($this->db);
        $customerModel = new Customer($this->db);
        $expenseModel = new Expense($this->db);

        // إحصائيات المبيعات
        $salesStats = $invoiceModel->getTotalSales([
            'branch_id' => $branchId,
            'start_date' => $startDate,
            'end_date' => $endDate
        ]);

        // إحصائيات الموظفين
        $employeesCount = $employeeModel->getEmployeesCount(['branch_id' => $branchId]);
        $topEmployees = $employeeModel->getTopEmployees(5, 'month', $branchId);

        // إحصائيات العملاء
        $customersCount = $customerModel->getCustomersCount(['branch_id' => $branchId]);
        $topCustomers = $customerModel->getTopCustomers(5, $branchId);

        // إحصائيات المصروفات
        $expensesTotal = $expenseModel->getTotalExpenses([
            'branch_id' => $branchId,
            'start_date' => $startDate,
            'end_date' => $endDate
        ]);

        // تجميع البيانات في تقرير
        $report = [
            'branch' => $branch,
            'period' => [
                'start_date' => $startDate,
                'end_date' => $endDate
            ],
            'stats' => [
                'total_sales' => $salesStats,
                'total_expenses' => $expensesTotal,
                'profit' => $salesStats - $expensesTotal,
                'employees_count' => $employeesCount,
                'customers_count' => $customersCount
            ],
            'top_employees' => $topEmployees,
            'top_customers' => $topCustomers
        ];

        return $report;
    }

    /**
     * استرجاع ساعات عمل الفرع في يوم محدد
     *
     * @param int $branchId معرف الفرع
     * @param int $dayOfWeek رقم اليوم في الأسبوع (1 للاثنين، 7 للأحد)
     * @return array|false بيانات ساعات العمل أو false إذا لم يتم العثور عليها
     */
    public function getBranchWorkingHours($branchId, $dayOfWeek) {
        // استرجاع معلومات الفرع
        $this->db->prepare("SELECT id, name FROM branches WHERE id = :branch_id");
        $this->db->bind(':branch_id', $branchId);
        $branchInfo = $this->db->fetch();

        if (!$branchInfo) {
            return false;
        }

        // افتراض أيام العمل من الاثنين إلى الجمعة (1-5)
        $workingDays = [1, 2, 3, 4, 5, 6, 7]; // كل الأيام
        $isWorkingDay = in_array($dayOfWeek, $workingDays);

        // إذا كان اليوم ليس يوم عمل، نرجع مباشرة
        if (!$isWorkingDay) {
            return [
                'is_working_day' => false,
                'open_time' => null,
                'close_time' => null
            ];
        }

        // استخدام ساعات عمل ثابتة
        $openTime = '09:00:00';  // 9 صباحًا
        $closeTime = '21:00:00'; // 9 مساءً

        return [
            'is_working_day' => true,
            'open_time' => $openTime,
            'close_time' => $closeTime
        ];
    }

    /**
     * استرجاع أول فرع نشط
     *
     * @return array|false بيانات الفرع أو false إذا لم يتم العثور عليه
     */
    public function getFirstBranch() {
        $this->db->prepare("SELECT b.*,
                               e.name AS manager_name
                        FROM branches b
                        LEFT JOIN employees e ON b.manager_id = e.id
                        WHERE b.is_active = 1
                        ORDER BY b.id ASC
                        LIMIT 1");

        return $this->db->fetch();
    }

    /**
     * التأكد من وجود حقل country_code في جدول branches
     * وإضافته إذا لم يكن موجودًا
     *
     * @return bool نتيجة العملية
     */
    private function ensureCountryCodeExists() {
        try {
            // التحقق من وجود الحقل
            $query = "SHOW COLUMNS FROM branches LIKE 'country_code'";
            $result = $this->db->query($query);
            $exists = $result && $result->rowCount() > 0;

            if (!$exists) {
                // إضافة الحقل إذا لم يكن موجودًا
                $alterQuery = "ALTER TABLE `branches` ADD COLUMN `country_code` VARCHAR(10) DEFAULT '+20' AFTER `phone`";
                $this->db->query($alterQuery);

                // تحديث الفروع الحالية لاستخدام رمز البلد الافتراضي
                $updateQuery = "UPDATE `branches` SET `country_code` = '+20' WHERE `country_code` IS NULL";
                $this->db->query($updateQuery);

                return true;
            }

            return true;
        } catch (Exception $e) {
            // تسجيل الخطأ ولكن لا توقف التنفيذ
            error_log('Error ensuring country_code field exists: ' . $e->getMessage());
            return false;
        }
    }
}
