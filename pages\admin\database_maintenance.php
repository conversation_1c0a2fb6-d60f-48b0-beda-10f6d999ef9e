<?php
/**
 * صفحة صيانة قاعدة البيانات
 * تسمح للمدير بفحص وتنظيف قاعدة البيانات من السجلات اليتيمة
 */

// تعريف ثابت لمنع الوصول المباشر للملفات المرتبطة
define('BASEPATH', true);

// استدعاء ملفات التهيئة
require_once '../../config/init.php';

// التحقق من تسجيل الدخول
requireLogin();

// التحقق من صلاحيات المدير
if (!isAdmin()) {
    redirect(BASE_URL . 'pages/dashboard.php');
}

// عنوان الصفحة
$pageTitle = 'صيانة قاعدة البيانات';

// استدعاء رأس الصفحة
include '../../includes/templates/header.php';

// إنشاء كائن قاعدة البيانات
$db = new Database();

// معالجة طلب التنظيف
$message = '';
$messageType = '';

if (isset($_POST['cleanup_orphaned_items'])) {
    try {
        // بدء المعاملة
        $db->beginTransaction();
        
        // الحصول على عدد عناصر الفواتير اليتيمة
        $db->prepare("
            SELECT COUNT(*) 
            FROM invoice_items ii 
            LEFT JOIN invoices i ON ii.invoice_id = i.id 
            WHERE i.id IS NULL
        ");
        $orphanedCount = $db->fetchColumn();
        
        if ($orphanedCount > 0) {
            // حذف عناصر الفواتير اليتيمة
            $db->prepare("
                DELETE ii 
                FROM invoice_items ii 
                LEFT JOIN invoices i ON ii.invoice_id = i.id 
                WHERE i.id IS NULL
            ");
            $db->execute();
            
            $message = "تم حذف {$orphanedCount} عنصر فاتورة يتيم بنجاح.";
            $messageType = 'success';
        } else {
            $message = "لم يتم العثور على أي عناصر فواتير يتيمة.";
            $messageType = 'info';
        }
        
        // تأكيد المعاملة
        $db->commit();
    } catch (Exception $e) {
        // التراجع عن المعاملة في حالة حدوث خطأ
        $db->rollBack();
        $message = "حدث خطأ أثناء عملية التنظيف: " . $e->getMessage();
        $messageType = 'danger';
        error_log("خطأ في صفحة صيانة قاعدة البيانات: " . $e->getMessage());
    }
}

// الحصول على إحصائيات قاعدة البيانات
try {
    // عدد عناصر الفواتير اليتيمة
    $db->prepare("
        SELECT COUNT(*) 
        FROM invoice_items ii 
        LEFT JOIN invoices i ON ii.invoice_id = i.id 
        WHERE i.id IS NULL
    ");
    $orphanedItemsCount = $db->fetchColumn();
    
    // إجمالي عدد الفواتير
    $db->prepare("SELECT COUNT(*) FROM invoices");
    $invoicesCount = $db->fetchColumn();
    
    // إجمالي عدد عناصر الفواتير
    $db->prepare("SELECT COUNT(*) FROM invoice_items");
    $invoiceItemsCount = $db->fetchColumn();
    
    // إجمالي عدد العملاء
    $db->prepare("SELECT COUNT(*) FROM customers");
    $customersCount = $db->fetchColumn();
    
    // إجمالي عدد المنتجات
    $db->prepare("SELECT COUNT(*) FROM products");
    $productsCount = $db->fetchColumn();
    
    // إجمالي عدد الخدمات
    $db->prepare("SELECT COUNT(*) FROM services");
    $servicesCount = $db->fetchColumn();
} catch (Exception $e) {
    $message = "حدث خطأ أثناء استرجاع إحصائيات قاعدة البيانات: " . $e->getMessage();
    $messageType = 'danger';
    error_log("خطأ في صفحة صيانة قاعدة البيانات: " . $e->getMessage());
}
?>

<!-- المحتوى الرئيسي للصفحة -->
<div class="container-fluid">
    <!-- رأس الصفحة -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">صيانة قاعدة البيانات</h1>
    </div>

    <?php if (!empty($message)): ?>
        <div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show" role="alert">
            <?php echo $message; ?>
            <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                <span aria-hidden="true">&times;</span>
            </button>
        </div>
    <?php endif; ?>

    <!-- إحصائيات قاعدة البيانات -->
    <div class="row">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">إجمالي الفواتير</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo number_format($invoicesCount); ?></div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-file-invoice fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">إجمالي عناصر الفواتير</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo number_format($invoiceItemsCount); ?></div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-list fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">إجمالي العملاء</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo number_format($customersCount); ?></div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-users fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">المنتجات والخدمات</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo number_format($productsCount + $servicesCount); ?></div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-box fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- بطاقة عناصر الفواتير اليتيمة -->
    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
            <h6 class="m-0 font-weight-bold text-primary">عناصر الفواتير اليتيمة</h6>
        </div>
        <div class="card-body">
            <p>
                عناصر الفواتير اليتيمة هي سجلات في جدول <code>invoice_items</code> تشير إلى فواتير غير موجودة في جدول <code>invoices</code>.
                هذه السجلات قد تنشأ عند حذف الفواتير دون حذف عناصرها المرتبطة.
            </p>
            
            <div class="alert alert-<?php echo $orphanedItemsCount > 0 ? 'warning' : 'success'; ?>">
                <?php if ($orphanedItemsCount > 0): ?>
                    <i class="fas fa-exclamation-triangle"></i> تم العثور على <strong><?php echo number_format($orphanedItemsCount); ?></strong> عنصر فاتورة يتيم.
                <?php else: ?>
                    <i class="fas fa-check-circle"></i> لم يتم العثور على أي عناصر فواتير يتيمة.
                <?php endif; ?>
            </div>
            
            <?php if ($orphanedItemsCount > 0): ?>
                <form method="post" action="">
                    <button type="submit" name="cleanup_orphaned_items" class="btn btn-danger">
                        <i class="fas fa-trash"></i> تنظيف عناصر الفواتير اليتيمة
                    </button>
                </form>
            <?php endif; ?>
        </div>
    </div>

    <!-- بطاقة نصائح الصيانة -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">نصائح للصيانة الدورية</h6>
        </div>
        <div class="card-body">
            <ul>
                <li>قم بتنظيف عناصر الفواتير اليتيمة بشكل دوري للحفاظ على نظافة قاعدة البيانات.</li>
                <li>قم بعمل نسخة احتياطية من قاعدة البيانات قبل إجراء أي عمليات تنظيف.</li>
                <li>تأكد من أن جميع المستخدمين قد قاموا بتسجيل الخروج قبل إجراء عمليات الصيانة الكبيرة.</li>
            </ul>
        </div>
    </div>
</div>

<!-- استدعاء قالب الفوتر -->
<?php include '../../includes/templates/footer.php'; ?>
