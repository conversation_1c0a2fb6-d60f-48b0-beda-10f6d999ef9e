<?php
/**
 * صفحة إضافة موعد جديد
 * تتيح للمستخدم حجز موعد جديد في النظام
 */

// تعريف ثابت لمنع الوصول المباشر للملفات المرتبطة
define('BASEPATH', true);

// استدعاء ملفات التهيئة
require_once '../../config/init.php';

// التحقق من تسجيل الدخول
redirectIfNotLoggedIn();

// التحقق من وجود صلاحية إنشاء المواعيد
if (!hasPermission('appointments_create')) {
    setErrorMessage(ACCESS_DENIED_MSG);
    redirect(BASE_URL . 'pages/appointments/index.php');
    exit;
}

// إنشاء كائنات النماذج
$appointmentModel = new Appointment($db);
$customerModel = new Customer($db);
$serviceModel = new Service($db);
$employeeModel = new Employee($db);
$branchModel = new Branch($db);

// استرجاع قائمة العملاء
$customers = $customerModel->getCustomers([
    'is_active' => 1,
    'branch_id' => $_SESSION['user_branch_id']
]);

// استرجاع قائمة الخدمات
$services = $serviceModel->getServices([
    'is_active' => 1,
    'branch_id' => $_SESSION['user_branch_id']
]);

// استرجاع قائمة الفروع للمدير
$branches = [];
if ($_SESSION['user_role'] == ROLE_ADMIN) {
    $branches = $branchModel->getBranches(['is_active' => 1]);
}

// تحديد الفرع الافتراضي
$defaultBranchId = ($_SESSION['user_role'] == ROLE_ADMIN) ? '' : $_SESSION['user_branch_id'];

// الحصول على إعدادات العملة من قاعدة البيانات
$settingsModel = new Settings($db);
$currencySymbol = $settingsModel->get('system_currency_symbol', 'ر.س');
$currency = $settingsModel->get('system_currency', 'ريال سعودي');

// استرجاع قائمة الموظفين
$employees = [];
if ($_SESSION['user_role'] == ROLE_ADMIN) {
    // إذا كان المستخدم مديرًا، استرجع جميع الموظفين النشطين
    // سيتم تحديث هذه القائمة لاحقًا عند اختيار فرع
    $employees = $employeeModel->getEmployees(['is_active' => 1]);
} else {
    // إذا كان المستخدم ليس مديرًا، استرجع موظفي فرعه فقط
    $employees = $employeeModel->getEmployees([
        'is_active' => 1,
        'branch_id' => $_SESSION['user_branch_id']
    ]);
}

// متغيرات الصفحة
$pageTitle = 'حجز موعد جديد';
$formAction = 'add';

// التحقق من وجود معرف العميل في الرابط
$customerId = isset($_GET['customer_id']) ? intval($_GET['customer_id']) : '';

// إذا كان هناك معرف عميل في الرابط، تحقق من وجوده في قاعدة البيانات
if ($customerId) {
    $customer = $customerModel->getCustomerById($customerId);
    if (!$customer) {
        // إذا لم يتم العثور على العميل، إعادة تعيين معرف العميل
        $customerId = '';
    }
}

$appointmentData = [
    'customer_id' => $customerId,
    'service_id' => '',
    'employee_id' => '',
    'date' => date('Y-m-d'),
    'start_time' => '',
    'branch_id' => $_SESSION['user_branch_id'],
    'notes' => ''
];

// معالجة نموذج إضافة موعد
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        // التحقق من البيانات المطلوبة
        $requiredFields = ['customer_id', 'service_id', 'employee_id', 'date', 'start_time', 'branch_id'];
        foreach ($requiredFields as $field) {
            if (empty($_POST[$field])) {
                throw new Exception("الحقل $field مطلوب");
            }
        }

        // جمع بيانات الموعد
        $appointmentData = [
            'customer_id' => intval($_POST['customer_id']),
            'service_id' => intval($_POST['service_id']),
            'employee_id' => intval($_POST['employee_id']),
            'date' => sanitizeInput($_POST['date']),
            'start_time' => sanitizeInput($_POST['start_time']),
            'status' => 'booked',
            'notes' => isset($_POST['notes']) ? sanitizeInput($_POST['notes']) : null,
            'branch_id' => intval($_POST['branch_id'])
        ];

        // إنشاء الموعد
        $appointmentId = $appointmentModel->createAppointment($appointmentData);

        // رسالة نجاح وإعادة التوجيه
        setSuccessMessage('تم حجز الموعد بنجاح');
        redirect(BASE_URL . 'pages/appointments/index.php');
        exit;
    } catch (Exception $e) {
        setErrorMessage($e->getMessage());
    }
}

// استدعاء قالب الهيدر
include '../../includes/templates/header.php';
?>

<!-- تنسيقات خاصة بالصفحة -->
<style>
    .card {
        border: none;
        box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    }

    .card-header {
        background-color: #fff;
        border-bottom: 1px solid rgba(0, 0, 0, 0.125);
        padding: 1rem;
    }

    .form-label {
        font-weight: 500;
    }

    .required-field::after {
        content: ' *';
        color: #dc3545;
    }

    .customer-info {
        display: none;
        background-color: #f8f9fa;
        border-radius: 0.25rem;
        padding: 1rem;
        margin-top: 1rem;
    }

    .employee-info {
        display: none;
        background-color: #f8f9fa;
        border-radius: 0.25rem;
        padding: 1rem;
        margin-top: 1rem;
    }

    .service-info {
        display: none;
        background-color: #f8f9fa;
        border-radius: 0.25rem;
        padding: 1rem;
        margin-top: 1rem;
    }

    .time-slots {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
        gap: 0.5rem;
        margin-top: 1rem;
    }
    .time-group-header {
        font-weight: 600;
        margin-bottom: 0.5rem;
        padding-bottom: 0.25rem;
        border-bottom: 1px solid #eee;
        color: #495057;
    }
    .time-slot-btn {
        min-width: 80px;
        text-align: center;
    }

    .time-slot-btn.active {
        background-color: #007bff;
        color: white;
    }

    #modalTimeSlots {
        max-height: 60vh;
        overflow-y: auto;
    }
    .time-slot {
        padding: 0.5rem;
        border: 1px solid #dee2e6;
        border-radius: 0.25rem;
        text-align: center;
        cursor: pointer;
        transition: all 0.2s;
    }

    .time-slot:hover {
        background-color: #e9ecef;
    }

    .time-slot.selected {
        background-color: #007bff;
        color: #fff;
        border-color: #007bff;
    }

    .time-slot.unavailable {
        background-color: #f8d7da;
        color: #721c24;
        border-color: #f5c6cb;
        cursor: not-allowed;
        opacity: 0.6;
    }
</style>

<!-- المحتوى الرئيسي للصفحة -->
<div class="container-fluid">
    <!-- رأس الصفحة -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h5 mb-0">
            <i class="fas fa-calendar-plus"></i> حجز موعد جديد
        </h1>
        <a href="index.php" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-right ml-1"></i> العودة للمواعيد
        </a>
    </div>

    <!-- محتوى الصفحة -->
    <div class="row">
        <div class="col-md-8">
            <!-- نموذج إضافة موعد -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">بيانات الموعد</h5>
                </div>
                <div class="card-body">
                    <form id="appointmentForm" method="post" action="">
                        <?php if ($_SESSION['user_role'] == ROLE_ADMIN): ?>
                        <!-- بيانات الفرع (للمدير فقط) -->
                        <div class="mb-4">
                            <h6 class="mb-3">
                                <i class="fas fa-store"></i> بيانات الفرع
                            </h6>
                            <div class="row">
                                <div class="col-md-8">
                                    <div class="mb-3">
                                        <label for="branch_id" class="form-label required-field">الفرع</label>
                                        <select class="form-select" id="branch_id" name="branch_id" required>
                                            <option value="">اختر الفرع</option>
                                            <?php foreach ($branches as $branch): ?>
                                                <option value="<?php echo $branch['id']; ?>" <?php echo (isset($_GET['branch_id']) && $_GET['branch_id'] == $branch['id']) ? 'selected' : ''; ?>>
                                                    <?php echo htmlspecialchars($branch['name']); ?>
                                                </option>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <?php else: ?>
                        <!-- الفرع الحالي للمستخدم -->
                        <input type="hidden" name="branch_id" value="<?php echo $_SESSION['user_branch_id']; ?>">
                        <?php endif; ?>

                        <!-- بيانات العميل -->
                        <div class="mb-4">
                            <h6 class="mb-3">
                                <i class="fas fa-user"></i> بيانات العميل
                            </h6>
                            <div class="row">
                                <div class="col-md-8">
                                    <div class="mb-3">
                                        <label for="customer_id" class="form-label required-field">العميل</label>
                                        <select class="form-select" id="customer_id" name="customer_id" required>
                                            <option value="">اختر العميل</option>
                                            <?php foreach ($customers as $customer): ?>
                                                <option value="<?php echo $customer['id']; ?>" <?php echo ($appointmentData['customer_id'] == $customer['id']) ? 'selected' : ''; ?>>
                                                    <?php echo htmlspecialchars($customer['name']); ?>
                                                    <?php if (!empty($customer['phone'])): ?>
                                                        (<?php echo htmlspecialchars($customer['phone']); ?>)
                                                    <?php endif; ?>
                                                </option>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3 d-flex align-items-end h-100">
                                        <button type="button" class="btn btn-outline-primary add-customer-btn">
                                            <i class="fas fa-plus"></i> إضافة عميل جديد
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <!-- معلومات العميل -->
                            <div id="customerInfo" class="customer-info">
                                <!-- سيتم تعبئتها عبر AJAX -->
                            </div>
                        </div>

                        <!-- بيانات الخدمة -->
                        <div class="mb-4">
                            <h6 class="mb-3">
                                <i class="fas fa-cut"></i> بيانات الخدمة
                            </h6>
                            <div class="mb-3">
                                <label for="service_id" class="form-label required-field">الخدمة</label>
                                <select class="form-select" id="service_id" name="service_id" required>
                                    <option value="">اختر الخدمة</option>
                                    <?php if (!$_SESSION['user_role'] == ROLE_ADMIN || !empty($defaultBranchId)): ?>
                                        <?php foreach ($services as $service): ?>
                                            <option value="<?php echo $service['id']; ?>" <?php echo ($appointmentData['service_id'] == $service['id']) ? 'selected' : ''; ?> data-duration="<?php echo $service['duration']; ?>">
                                                <?php echo htmlspecialchars($service['name']); ?>
                                                (<?php echo formatMoney($service['price'], 2, true); ?>)
                                            </option>
                                        <?php endforeach; ?>
                                    <?php endif; ?>
                                </select>
                            </div>

                            <!-- معلومات الخدمة -->
                            <div id="serviceInfo" class="service-info">
                                <!-- سيتم تعبئتها عبر AJAX -->
                            </div>

                            <!-- الموظف -->
                            <div class="mb-3">
                                <label for="employee_id" class="form-label">الموظف</label>
                                <select class="form-select" id="employee_id" name="employee_id">
                                    <option value="">اختر الموظف</option>
                                    <?php foreach ($employees as $employee): ?>
                                        <option value="<?php echo $employee['id']; ?>" <?php echo ($appointmentData['employee_id'] == $employee['id']) ? 'selected' : ''; ?>>
                                            <?php echo htmlspecialchars($employee['name']); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>

                            <!-- معلومات الموظف -->
                            <div id="employeeInfo" class="employee-info">
                                <!-- سيتم تعبئتها عبر AJAX -->
                            </div>
                        </div>

                        <!-- بيانات الموعد -->
                        <div class="mb-4">
                            <h6 class="mb-3">
                                <i class="fas fa-calendar-alt"></i> بيانات الموعد
                            </h6>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="date" class="form-label required-field">التاريخ</label>
                                        <input type="text" class="form-control date-picker" id="date" name="date" value="<?php echo date('Y/m/d'); ?>" required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="start_time" class="form-label required-field">الوقت</label>
                                        <input type="hidden" id="start_time" name="start_time" value="<?php echo $appointmentData['start_time']; ?>" required>
                                        <div class="d-flex">
                                            <button type="button" class="btn btn-outline-primary me-2" id="showTimeSlots">
                                                <i class="fas fa-clock"></i> اختر وقت
                                            </button>
                                            <span id="selectedTime" class="form-control-plaintext"><?php echo !empty($appointmentData['start_time']) ? date('h:i A', strtotime($appointmentData['start_time'])) : 'لم يتم اختيار وقت'; ?></span>
                                        </div>
                                        <!-- فترات الوقت المتاحة -->
                                        <div id="timeSlots" class="time-slots" style="display: none;">
                                            <!-- سيتم تعبئتها عبر AJAX -->
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- الفرع (يظهر فقط للمدير) -->
                            <?php if ($_SESSION['user_role'] == ROLE_ADMIN): ?>

                            <?php else: ?>
                            <!-- إذا كان المستخدم ليس مديرًا، استخدم فرعه كقيمة افتراضية -->
                            <input type="hidden" id="branch_id" name="branch_id" value="<?php echo $_SESSION['user_branch_id']; ?>">
                            <?php endif; ?>

                            <!-- ملاحظات -->
                            <div class="mb-3">
                                <label for="notes" class="form-label">ملاحظات</label>
                                <textarea class="form-control" id="notes" name="notes" rows="3"><?php echo $appointmentData['notes']; ?></textarea>
                            </div>
                        </div>

                        <!-- أزرار النموذج -->
                        <div class="text-end">
                            <a href="index.php" class="btn btn-outline-secondary me-2">إلغاء</a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-1"></i> حفظ الموعد
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <!-- معلومات مساعدة -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-info-circle"></i> معلومات مساعدة
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <h6 class="fw-bold">إرشادات حجز المواعيد:</h6>
                        <ul class="mb-0">
                            <li>يجب اختيار عميل وخدمة وموظف لحجز الموعد.</li>
                            <li>تأكد من اختيار وقت مناسب لا يتعارض مع المواعيد الأخرى.</li>
                            <li>يمكنك تحديد ملاحظات إضافية للموعد عند الحاجة.</li>
                            <li>سيتم حجز الموعد بحالة "محجوز" افتراضliğ.</li>
                        </ul>
                    </div>

                    <div>
                        <h6 class="fw-bold">حالات المواعيد:</h6>
                        <ul class="mb-0">
                            <li><span class="badge bg-primary">محجوز</span> - تم حجز الموعد وفي انتظار وصول العميل.</li>
                            <li><span class="badge bg-warning">في الانتظار</span> - العميل وصل وفي انتظار دوره.</li>
                            <li><span class="badge bg-success">مكتمل</span> - تم تقديم الخدمة بنجاح.</li>
                            <li><span class="badge bg-danger">ملغي</span> - تم إلغاء الموعد.</li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- جدول المواعيد اليوم -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-calendar-day"></i> مواعيد اليوم
                    </h5>
                </div>
                <div class="card-body p-0">
                    <div id="todayAppointments" class="list-group list-group-flush">
                        <!-- سيتم تعبئتها عبر AJAX -->
                        <div class="text-center py-4">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">جاري التحميل...</span>
                            </div>
                            <p class="mt-2">جاري تحميل مواعيد اليوم...</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- نموذج فترات الوقت المتاحة -->
<div class="modal fade" id="timeSlotsModal" tabindex="-1" aria-labelledby="timeSlotsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="timeSlotsModalLabel">اختر وقت الموعد</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="إغلاق"></button>
            </div>
            <div class="modal-body">
                <div id="modalTimeSlots" class="time-slots">
                    <!-- سيتم تعبئتها عبر AJAX -->
                    <div class="text-center py-4">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">جاري التحميل...</span>
                        </div>
                        <p class="mt-2">جاري تحميل الأوقات المتاحة...</p>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                <button type="button" class="btn btn-primary" id="selectTimeBtn" disabled>اختيار الوقت</button>
            </div>
        </div>
    </div>
</div>

<!-- نافذة إضافة عميل جديد -->
<div class="modal fade" id="addCustomerModal" tabindex="-1" aria-labelledby="addCustomerModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addCustomerModalLabel">إضافة عميل جديد</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="إغلاق"></button>
            </div>
            <div class="modal-body">
                <form id="addCustomerForm">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="customer_name" class="form-label required-field">اسم العميل</label>
                                <input type="text" class="form-control" id="customer_name" name="name" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="customer_phone" class="form-label required-field">رقم الهاتف</label>
                                <input type="text" class="form-control" id="customer_phone" name="phone" required>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="customer_email" class="form-label">البريد الإلكتروني</label>
                                <input type="email" class="form-control" id="customer_email" name="email">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="customer_birthday" class="form-label">تاريخ الميلاد</label>
                                <input type="date" class="form-control" id="customer_birthday" name="birthday">
                            </div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="customer_notes" class="form-label">ملاحظات</label>
                        <textarea class="form-control" id="customer_notes" name="notes" rows="3"></textarea>
                    </div>
                    <input type="hidden" name="branch_id" value="<?php echo $_SESSION['user_branch_id']; ?>">
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" id="submitAddCustomer">حفظ</button>
            </div>
        </div>
    </div>
</div>
<!-- إضافة حاوية للتنبيهات في بداية نموذج الموعد -->
<div id="alertContainer"></div>

<!-- إضافة مؤشر التحميل -->
<div id="loadingOverlay" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background-color: rgba(0,0,0,0.5); z-index: 9999;">
    <div class="d-flex justify-content-center align-items-center h-100">
        <div class="spinner-border text-light" role="status">
            <span class="visually-hidden">جاري التحميل...</span>
        </div>
    </div>
</div>
<!-- استدعاء قالب الفوتر -->
<?php include '../../includes/templates/footer.php'; ?>

<script>
    $(document).ready(function() {
        // التحقق من وجود عميل محدد مسبقًا وتحميل بياناته
        const preSelectedCustomerId = $('#customer_id').val();
        if (preSelectedCustomerId) {
            // تشغيل حدث التغيير لتحميل بيانات العميل
            $('#customer_id').trigger('change');
        }

        // تهيئة اختيار التاريخ
        $('.date-picker').daterangepicker({
            singleDatePicker: true,
            showDropdowns: true,
            minDate: moment(),
            locale: {
                format: 'YYYY/MM/DD',
                applyLabel: 'تطبيق',
                cancelLabel: 'إلغاء',
                daysOfWeek: ['أح', 'إث', 'ثل', 'أر', 'خم', 'جم', 'سب'],
                monthNames: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو', 'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'],
                firstDay: 0
            }
        });

        // عرض معلومات العميل عند اختياره
        $('#customer_id').change(function() {
            const customerId = $(this).val();
            if (customerId) {
                // استرجاع بيانات العميل
                $.ajax({
                    url: '../../api/customers.php',
                    type: 'post',
                    data: {
                        action: 'get_customer',
                        id: customerId
                    },
                    dataType: 'json',

                    success: function(response) {
                        if (response.status === 'success' && response.customer) {
                            // عرض بيانات العميل
                            const customer = response.customer;
                            let infoHtml = `
                                <div class="row">
                                    <div class="col-md-6">
                                        <p class="mb-1"><strong>الاسم:</strong> ${customer.name}</p>
                                        <p class="mb-1"><strong>الهاتف:</strong> <span dir="ltr">${customer.phone || '-'}</span></p>
                                    </div>
                                    <div class="col-md-6">
                                        <p class="mb-1"><strong>البريد الإلكتروني:</strong> ${customer.email || '-'}</p>
                                        <p class="mb-1"><strong>تاريخ الميلاد:</strong> ${customer.birthday ? moment(customer.birthday).format('YYYY/MM/DD') : '-'}</p>
                                    </div>
                                </div>
                            `;

                            $('#customerInfo').html(infoHtml).slideDown();
                        } else {
                            $('#customerInfo').html('<p class="text-danger">تعذر استرجاع بيانات العميل.</p>').slideDown();
                        }
                    },
                    error: function() {
                        $('#customerInfo').html('<p class="text-danger">تعذر الاتصال بالخادم.</p>').slideDown();
                    }
                });
            } else {
                $('#customerInfo').slideUp();
            }
        });

        // عرض معلومات الخدمة عند اختيارها
        $('#service_id').change(function() {
            const serviceId = $(this).val();
            if (serviceId) {
                // استرجاع بيانات الخدمة
                $.ajax({
                    url: '../../api/services.php',
                    type: 'GET',
                    data: {
                        action: 'view',
                        id: serviceId
                    },
                    dataType: 'json',
                    success: function(response) {
                        console.log('استجابة بيانات الخدمة:', response);

                        if (response.status === 'success' && response.data) {
                            // عرض بيانات الخدمة
                            const service = response.data;
                            let infoHtml = `
                                <div class="row">
                                    <div class="col-md-6">
                                        <p class="mb-1"><strong>الخدمة:</strong> ${service.name}</p>
                                        <p class="mb-1"><strong>السعر:</strong> ${parseFloat(service.price).toFixed(2)} ${currencySymbol}</p>
                                    </div>
                                    <div class="col-md-6">
                                        <p class="mb-1"><strong>المدة:</strong> ${service.duration} دقيقة</p>
                                        <p class="mb-1"><strong>الفئة:</strong> ${service.category_name || '-'}</p>
                                    </div>
                                </div>
                            `;

                            $('#serviceInfo').html(infoHtml).slideDown();

                            // تحديث قائمة الموظفين المتاحين للخدمة
                            if (service.employees && service.employees.length > 0) {
                                updateEmployeesDropdown(service.employees);
                            } else {
                                updateEmployeesList(serviceId);
                            }
                        } else {
                            $('#serviceInfo').html('<p class="text-danger">تعذر استرجاع بيانات الخدمة.</p>').slideDown();
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error('خطأ في استرجاع بيانات الخدمة:', status, error);
                        $('#serviceInfo').html('<p class="text-danger">تعذر الاتصال بالخادم.</p>').slideDown();
                    }
                });
            } else {
                $('#serviceInfo').slideUp();
            }
        });

        // تحديث قائمة الموظفين من بيانات الخدمة
        function updateEmployeesDropdown(employees) {
            const currentEmployeeId = $('#employee_id').val();
            $('#employee_id').empty().append('<option value="">اختر الموظف</option>');

            if (employees.length > 0) {
                $.each(employees, function(index, employee) {
                    const selected = (employee.id == currentEmployeeId) ? 'selected' : '';
                    $('#employee_id').append(`<option value="${employee.id}" ${selected}>${employee.name}</option>`);
                });
            } else {
                $('#employee_id').append('<option value="" disabled>لا يوجد موظفين متاحين لهذه الخدمة</option>');
            }
        }
        $('#appointmentForm').on('submit', function(e) {
            e.preventDefault();

            // التحقق من صحة النموذج
            if (!validateForm()) {
                return false;
            }

            // عرض مؤشر التحميل
            showLoading();

            // جمع بيانات النموذج
            const formData = $(this).serialize();

            // إضافة action للبيانات
            const requestData = formData + '&action=create';

            // عرض البيانات المرسلة للتصحيح
            console.log('Enviando datos:', requestData);

            // إرسال البيانات عبر AJAX
            $.ajax({
                url: '../../api/appointments.php',
                type: 'POST',
                data: requestData,
                processData: true,
                contentType: 'application/x-www-form-urlencoded',
                dataType: 'json',
                success: function(response) {
                    hideLoading();
                    console.log('Respuesta exitosa:', response);

                    if (response.status === 'success') {
                        showAlert('تم حجز الموعد بنجاح', 'success');
                        // Redirigir a la página de citas después de 2 segundos
                        setTimeout(function() {
                            window.location.href = '../../pages/appointments/index.php';
                        }, 2000);
                    } else {
                        showAlert(response.message || 'حدث خطأ أثناء حفظ الموعد', 'danger');
                    }
                },
                error: function(xhr, status, error) {
                    hideLoading();

                    // Registrar información detallada del error
                    console.error('Error en la solicitud AJAX:', status, error);
                    console.error('Código de estado HTTP:', xhr.status);
                    console.error('Respuesta del servidor:', xhr.responseText);

                    // Intentar determinar el tipo de error
                    let errorMessage = 'فشل الاتصال بالخادم. يرجى المحاولة مرة أخرى لاحقًا.';

                    if (xhr.responseText) {
                        try {
                            const jsonResponse = JSON.parse(xhr.responseText);
                            if (jsonResponse && jsonResponse.message) {
                                errorMessage = jsonResponse.message;
                            }
                        } catch (e) {
                            if (xhr.responseText.length < 200) {
                                errorMessage = 'استجابة الخادم: ' + xhr.responseText;
                            }
                        }
                    }

                    showAlert(errorMessage, 'danger');
                }
            });
        });
            // التحقق من صحة النموذج
            function validateForm() {
            let isValid = true;

            // التحقق من اختيار العميل
            if (!$('#customer_id').val()) {
                showAlert('يرجى اختيار العميل', 'warning');
                isValid = false;
            }

            // التحقق من اختيار الخدمة
            if (!$('#service_id').val()) {
                showAlert('يرجى اختيار الخدمة', 'warning');
                isValid = false;
            }

            // التحقق من اختيار الموظف
            if (!$('#employee_id').val()) {
                showAlert('يرجى اختيار الموظف', 'warning');
                isValid = false;
            }

            // التحقق من اختيار التاريخ
            if (!$('#date').val()) {
                showAlert('يرجى اختيار التاريخ', 'warning');
                isValid = false;
            }

            // التحقق من اختيار الوقت
            if (!$('#start_time').val()) {
                showAlert('يرجى اختيار وقت الموعد', 'warning');
                isValid = false;
            }

            return isValid;
        }
        // عرض معلومات الموظف عند اختياره
        $('#employee_id').change(function() {
            const employeeId = $(this).val();
            if (employeeId) {
                // استرجاع بيانات الموظف
                $.ajax({
                    url: '../../api/employees.php',
                    type: 'POST',
                    data: {
                        action: 'get_employee',
                        employee_id: employeeId
                    },
                    dataType: 'json',
                    contentType: 'application/x-www-form-urlencoded; charset=UTF-8',
                    success: function(response) {
                        if (response.status === 'success' && response.employee) {
                            // عرض بيانات الموظف
                            const employee = response.employee;
                            let infoHtml = `
                                <div class="row">
                                    <div class="col-md-6">
                                        <p class="mb-1"><strong>الاسم:</strong> ${employee.name}</p>
                                        <p class="mb-1"><strong>المسمى الوظيفي:</strong> ${employee.position || '-'}</p>
                                    </div>
                                    <div class="col-md-6">
                                        <p class="mb-1"><strong>الهاتف:</strong> <span dir="ltr">${employee.phone || '-'}</span></p>
                                        <p class="mb-1"><strong>البريد الإلكتروني:</strong> ${employee.email || '-'}</p>
                                    </div>
                                </div>
                            `;

                            $('#employeeInfo').html(infoHtml).slideDown();

                            // تحديث فترات الوقت المتاحة
                            checkAvailableTimes();
                        } else {
                            $('#employeeInfo').html('<p class="text-danger">تعذر استرجاع بيانات الموظف.</p>').slideDown();
                        }
                    },
                    error: function() {
                        $('#employeeInfo').html('<p class="text-danger">تعذر الاتصال بالخادم.</p>').slideDown();
                    }
                });
            } else {
                $('#employeeInfo').slideUp();
            }
        });

        // تحديث قائمة الموظفين المتاحين للخدمة
        function updateEmployeesList(serviceId) {
            $.ajax({
                url: '../../api/services.php',
                type: 'GET',
                data: {
                    action: 'get_service_employees',
                    id: serviceId
                },
                success: function(response) {
                    if (response.status === 'success' && response.employees) {
                        // إعادة بناء قائمة الموظفين
                        const currentEmployeeId = $('#employee_id').val();
                        $('#employee_id').empty().append('<option value="">اختر الموظف</option>');

                        if (response.employees.length > 0) {
                            $.each(response.employees, function(index, employee) {
                                const selected = (employee.id == currentEmployeeId) ? 'selected' : '';
                                $('#employee_id').append(`<option value="${employee.id}" ${selected}>${employee.name}</option>`);
                            });
                        } else {
                            $('#employee_id').append('<option value="" disabled>لا يوجد موظفين متاحين لهذه الخدمة</option>');
                        }
                    }
                }
            });
        }

        // عند تغيير التاريخ
        $('#date').on('change', function() {
            checkAvailableTimes();
        });

        // عرض نموذج اختيار الوقت
        $('#showTimeSlots').on('click', function() {
            // التحقق من اختيار جميع البيانات المطلوبة
            const serviceId = $('#service_id').val();
            const branchId = $('#branch_id').val();
            const date = $('#date').val();

            if (!serviceId || !date || !branchId) {
                showAlert('يرجى اختيار الخدمة والتاريخ والفرع أولاً', 'warning');
                return;
            }

            // عرض مؤشر التحميل
            $('#modalTimeSlots').html('<div class="text-center py-4"><div class="spinner-border text-primary" role="status"><span class="visually-hidden">جاري التحميل...</span></div><p class="mt-2">جاري تحميل الأوقات المتاحة...</p></div>');

            // عرض مودال اختيار الوقت
            $('#timeSlotsModal').modal('show');

            // تحميل الأوقات المتاحة
            checkAvailableTimes();
        });

        // التحقق من الأوقات المتاحة
        function checkAvailableTimes() {
            const serviceId = $('#service_id').val();
            const employeeId = $('#employee_id').val() || 0; // استخدام 0 إذا لم يتم اختيار موظف
            const date = $('#date').val();
            const branchId = $('#branch_id').val();

            console.log('التحقق من الأوقات المتاحة:', {
                service_id: serviceId,
                employee_id: employeeId,
                date: date,
                branch_id: branchId
            });

            if (!serviceId || !date || !branchId) {
                $('#modalTimeSlots').html('<div class="alert alert-warning">يرجى اختيار الخدمة والتاريخ والفرع أولاً</div>');
                return;
            }

            // استرجاع الأوقات المتاحة
            $.ajax({
                url: '../../api/appointments.php',
                type: 'GET',
                data: {
                    action: 'get_available_times',
                    service_id: serviceId,
                    employee_id: employeeId,
                    date: moment(date).format('YYYY-MM-DD'),
                    branch_id: branchId
                },
                dataType: 'json',
                success: function(response) {
                    console.log('استجابة الأوقات المتاحة:', response);

                    if (response.status === 'success') {
                        // عرض الأوقات المتاحة
                        displayAvailableTimes(response.available_times || []);
                    } else {
                        $('#modalTimeSlots').html('<div class="alert alert-danger">' + (response.message || 'تعذر استرجاع الأوقات المتاحة.') + '</div>');
                    }
                },
                error: function(xhr, status, error) {
                    console.error('خطأ في طلب الأوقات المتاحة:', status, error);
                    $('#modalTimeSlots').html('<div class="alert alert-danger">تعذر الاتصال بالخادم.</div>');
                }
            });

        }
            // عرض مؤشر التحميل
    function showLoading() {
        $('#loadingOverlay').show();
    }

    // إخفاء مؤشر التحميل
    function hideLoading() {
        $('#loadingOverlay').hide();
    }
        // عرض الأوقات المتاحة
        // Replace your displayAvailableTimes function with this improved version
function displayAvailableTimes(availableTimes) {
    const container = $('#modalTimeSlots');
    container.empty();

    if (!Array.isArray(availableTimes) || availableTimes.length === 0) {
        container.html('<div class="alert alert-info">لا توجد أوقات متاحة في هذا اليوم. يرجى اختيار يوم آخر.</div>');
        $('#selectTimeBtn').prop('disabled', true);
        return;
    }

    // Group times by hour for better organization
    const timesByHour = {};
    availableTimes.forEach(function(timeSlot) {
        const hour = moment(timeSlot, 'HH:mm:ss').format('hA'); // Group by hour (9AM, 10AM, etc)
        if (!timesByHour[hour]) {
            timesByHour[hour] = [];
        }
        timesByHour[hour].push(timeSlot);
    });

    // Create a more organized UI with times grouped by hour
    let timeButtonsHtml = '';

    Object.keys(timesByHour).forEach(function(hour) {
        timeButtonsHtml += `<div class="mb-3">
            <h6 class="time-group-header">${hour}</h6>
            <div class="d-flex flex-wrap">`;

        timesByHour[hour].forEach(function(timeSlot) {
            timeButtonsHtml += `
                <button type="button" class="btn btn-outline-primary time-slot-btn m-1" data-time="${timeSlot}">
                    ${formatTime(timeSlot)}
                </button>
            `;
        });

        timeButtonsHtml += `</div></div>`;
    });

    container.html(timeButtonsHtml);
    // Improve the time selection confirmation
$('#selectTimeBtn').on('click', function() {
    const selectedTime = $('.time-slot-btn.active').data('time');
    if (selectedTime) {
        const formattedTime = formatTime(selectedTime);
        $('#start_time').val(selectedTime);
        $('#selectedTime').text(formattedTime).addClass('text-success fw-bold');
        $('#timeSlotsModal').modal('hide');

        // Add visual confirmation
        showAlert(`تم اختيار الوقت: ${formattedTime}`, 'success');
    }
});
    // Add improved selection behavior
    $('.time-slot-btn').on('click', function() {
        $('.time-slot-btn').removeClass('active');
        $(this).addClass('active');

        // Enable the select button and update it with the selected time
        $('#selectTimeBtn').prop('disabled', false).html(`اختيار الوقت (${$(this).text().trim()})`);

        // Pre-update the hidden input so we have it ready
        const selectedTime = $(this).data('time');
        $('#start_time').val(selectedTime);
    });
}

        // تنسيق الوقت
        function formatTime(timeString) {
            return moment(timeString, 'HH:mm:ss').format('hh:mm A');
        }

        // إضافة عميل جديد من خلال نافذة منبثقة
        $('.add-customer-btn').click(function(e) {
            e.preventDefault();
            $('#addCustomerModal').modal('show');
        });
// تحديث قائمة الخدمات عند تغيير الفرع
$('#branch_id').on('change', function() {
    const branchId = $(this).val();

    // إذا لم يتم اختيار فرع، قم بإفراغ قائمة الخدمات
    if (!branchId) {
        $('#service_id').empty().append('<option value="">اختر الخدمة</option>');
        $('#employee_id').empty().append('<option value="">اختر الموظف</option>');
        return;
    }

    // استرجاع قائمة الخدمات للفرع المختار
    $.ajax({
        url: '../../api/services.php',
        type: 'GET',
        data: {
            action: 'get_branch_services',
            branch_id: branchId
        },
        dataType: 'json',
        success: function(response) {
            if (response.status === 'success' && response.services) {
                // إعادة بناء قائمة الخدمات
                $('#service_id').empty().append('<option value="">اختر الخدمة</option>');

                if (response.services.length > 0) {
                    $.each(response.services, function(index, service) {
                        $('#service_id').append(
                            `<option value="${service.id}" data-duration="${service.duration}">
                                ${service.name} (${formatMoney(service.price)} ${currencySymbol})
                            </option>`
                        );
                    });
                } else {
                    $('#service_id').append('<option value="" disabled>لا توجد خدمات في هذا الفرع</option>');
                }
            }
        },
        error: function(xhr, status, error) {
            console.error('خطأ في استرجاع قائمة الخدمات:', status, error);
        }
    });
});

// دالة لتنسيق المبالغ المالية
function formatMoney(amount) {
    return parseFloat(amount).toFixed(2).replace(/\d(?=(\d{3})+\.)/g, '$&,');
}

// تعريف متغير لرمز العملة لاستخدامه في جافاسكريبت
const currencySymbol = '<?php echo $currencySymbol; ?>';
        // معالجة نموذج إضافة عميل جديد
        $('#submitAddCustomer').click(function() {
            const form = $('#addCustomerForm')[0];
            if (!form.checkValidity()) {
                form.reportValidity();
                return;
            }

            // التحقق من رقم الهاتف قبل الإرسال
            const phone = $('#customer_phone').val().trim();
            if (phone) {
                $.ajax({
                    url: '../../api/customers.php',
                    type: 'POST',
                    data: {
                        action: 'check_phone_exists',
                        phone: phone
                    },
                    dataType: 'json',
                    success: function(response) {
                        if (response.exists) {
                            // إذا كان الرقم موجود بالفعل
                            showAlert('رقم الهاتف مستخدم بالفعل لعميل آخر', 'warning');
                            $('#customer_phone').addClass('is-invalid');

                            // إضافة رسالة خطأ تحت حقل رقم الهاتف
                            if (!$('#phone-error-feedback').length) {
                                $('#customer_phone').after('<div id="phone-error-feedback" class="invalid-feedback">رقم الهاتف مستخدم بالفعل</div>');
                            }
                        } else {
                            // إذا كان الرقم غير موجود، قم بإرسال النموذج
                            $('#customer_phone').removeClass('is-invalid');
                            submitCustomerForm();
                        }
                    },
                    error: function() {
                        // في حالة حدوث خطأ، السماح بإرسال النموذج
                        submitCustomerForm();
                    }
                });
            } else {
                submitCustomerForm();
            }
        });

        // دالة إرسال نموذج العميل
        function submitCustomerForm() {
            const form = $('#addCustomerForm')[0];
            const formData = new FormData(form);
            formData.append('action', 'add_customer');

            showLoading(); // إظهار مؤشر التحميل

            $.ajax({
                url: '../../api/customers.php',
                type: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                dataType: 'json',
                success: function(response) {
                    hideLoading(); // إخفاء مؤشر التحميل

                    if (response.status === 'success') {
                        // إغلاق المودال وإعادة تحميل قائمة العملاء
                        $('#addCustomerModal').modal('hide');
                        form.reset();

                        // التحقق من وجود بيانات العميل في الاستجابة
                        if (response.customer && response.customer.id) {
                            // إضافة العميل الجديد للقائمة وتحديده
                            const customerName = response.customer.name || $('#customer_name').val();
                            const customerId = response.customer.id;
                            const newOption = new Option(customerName, customerId, true, true);
                            $('#customer_id').append(newOption).trigger('change');
                        } else if (response.customer_id) {
                            // إذا كان هناك معرف العميل فقط في الاستجابة
                            const customerName = $('#customer_name').val();
                            const customerId = response.customer_id;
                            const newOption = new Option(customerName, customerId, true, true);
                            $('#customer_id').append(newOption).trigger('change');
                        }

                        showAlert(response.message, 'success');
                    } else {
                        // معالجة رسائل الخطأ من API
                        handleApiError(response);
                    }
                },
                error: function(xhr, status, error) {
                    hideLoading(); // إخفاء مؤشر التحميل

                    // محاولة تحليل استجابة الخطأ
                    try {
                        const response = JSON.parse(xhr.responseText);
                        handleApiError(response);
                    } catch (e) {
                        // إذا لم يكن هناك استجابة JSON صالحة
                        showAlert('حدث خطأ أثناء إضافة العميل: ' + error, 'danger');
                    }
                }
            });
        }

        // دالة جديدة لمعالجة أخطاء API
        function handleApiError(response) {
            let errorMessage = response.message || 'حدث خطأ غير معروف';
            let errorType = 'danger';

            // معالجة أنواع محددة من رسائل الخطأ
            if (errorMessage.includes('ليس لديك صلاحية')) {
                errorType = 'warning';
                errorMessage = '<i class="fas fa-lock me-2"></i>' + errorMessage;
            } else if (errorMessage.includes('يرجى إدخال جميع البيانات')) {
                errorType = 'info';
                errorMessage = '<i class="fas fa-info-circle me-2"></i>' + errorMessage;
            } else if (errorMessage.includes('موجود بالفعل')) {
                errorType = 'warning';
                errorMessage = '<i class="fas fa-exclamation-triangle me-2"></i>' + errorMessage;
            }

            // عرض رسالة الخطأ
            showAlert(errorMessage, errorType);

            // إذا كانت مشكلة صلاحيات، يمكن إضافة إجراءات إضافية
            if (errorMessage.includes('ليس لديك صلاحية')) {
                // إظهار رسالة إضافية في النموذج
                $('#addCustomerForm').prepend(`
                    <div class="alert alert-warning alert-dismissible fade show mb-3" role="alert">
                        <strong>تنبيه!</strong> ليس لديك صلاحية لإضافة عملاء. يرجى التواصل مع مدير النظام.
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                `);

                // تعطيل زر الإرسال
                $('#submitAddCustomer').prop('disabled', true).addClass('btn-secondary').removeClass('btn-primary');
            }
        }

        // عرض رسالة تنبيه
        function showAlert(message, type = 'success') {
            const alertHtml = `
                <div class="alert alert-${type} alert-dismissible fade show" role="alert">
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            `;

            $('#alertContainer').html(alertHtml);

            // إخفاء التنبيه تلقائ<|im_start|> بعد 5 ثوان
            setTimeout(function() {
                $('.alert').alert('close');
            }, 5000);
        }

        // تحميل مواعيد اليوم
        function loadTodayAppointments() {
            const branchId = $('#branch_id').val();

            // إظهار مؤشر التحميل
            $('#todayAppointments').html('<div class="text-center py-4"><div class="spinner-border text-primary" role="status"><span class="visually-hidden">جاري التحميل...</span></div><p class="mt-2">جاري تحميل مواعيد اليوم...</p></div>');

            console.log('تحميل مواعيد اليوم للفرع:', branchId);

            $.ajax({
                url: '../../api/appointments.php',
                type: 'GET',
                data: {
                    action: 'get_today_appointments',
                    branch_id: branchId
                },
                dataType: 'json',
                success: function(response) {
                    console.log('استجابة API مواعيد اليوم:', response);

                    if (response.status === 'success' && Array.isArray(response.appointments)) {
                        displayTodayAppointments(response.appointments);
                    } else {
                        console.error('خطأ في استجابة API:', response);
                        $('#todayAppointments').html('<div class="text-center py-3"><p class="text-muted">تعذر تحميل مواعيد اليوم.</p></div>');
                    }
                },
                error: function(xhr, status, error) {
                    console.error('خطأ في طلب API:', status, error);
                    $('#todayAppointments').html('<div class="text-center py-3"><p class="text-danger">تعذر الاتصال بالخادم.</p></div>');
                }
            });
        }

        // عرض مواعيد اليوم
        function displayTodayAppointments(appointments) {
            const container = $('#todayAppointments');
            container.empty();

            console.log('عرض مواعيد اليوم:', appointments.length);

            if (!Array.isArray(appointments) || appointments.length === 0) {
                container.html('<div class="text-center py-3"><p class="text-muted">لا توجد مواعيد لهذا اليوم.</p></div>');
                return;
            }

            let html = '';
            appointments.forEach(function(appointment) {
                const statusClass = getStatusClass(appointment.status);
                const timeDisplay = formatTime(appointment.start_time);

                html += `
                    <a href="view.php?id=${appointment.id}" class="list-group-item list-group-item-action">
                        <div class="d-flex w-100 justify-content-between">
                            <h6 class="mb-1">${appointment.customer_name || 'بدون اسم'}</h6>
                            <small class="text-muted">${timeDisplay}</small>
                        </div>
                        <p class="mb-1">${appointment.service_name || 'بدون خدمة'}</p>
                        <div class="d-flex justify-content-between">
                            <small>${appointment.employee_name || 'بدون موظف'}</small>
                            <span class="badge ${statusClass}">${getStatusText(appointment.status)}</span>
                        </div>
                    </a>
                `;
            });

            container.html(html);
        }

        // الحصول على صنف حالة الموعد
        function getStatusClass(status) {
            switch (status) {
                case 'booked': return 'bg-primary';
                case 'confirmed': return 'bg-success';
                case 'completed': return 'bg-info';
                case 'cancelled': return 'bg-danger';
                case 'no_show': return 'bg-warning';
                default: return 'bg-secondary';
            }
        }

        // الحصول على نص حالة الموعد
        function getStatusText(status) {
            switch (status) {
                case 'booked': return 'محجوز';
                case 'confirmed': return 'مؤكد';
                case 'completed': return 'مكتمل';
                case 'cancelled': return 'ملغي';
                case 'no_show': return 'لم يحضر';
                default: return 'غير معروف';
            }
        }

        // تحميل مواعيد اليوم عند تحميل الصفحة
        $(document).ready(function() {
            console.log('تهيئة الصفحة...');

            // تأكد من تحميل مواعيد اليوم بعد تحميل الصفحة بالكامل
            setTimeout(function() {
                loadTodayAppointments();
            }, 500);

            // تحديث مواعيد اليوم عند تغيير الفرع
            $('#branch_id').on('change', function() {
                console.log('تم تغيير الفرع');
                loadTodayAppointments();
            });

            // إضافة زر تحديث
            $('#todayAppointments').closest('.card-header').append('<button type="button" class="btn btn-sm btn-outline-primary ms-2" id="refreshTodayBtn"><i class="fas fa-sync-alt"></i></button>');

            // تحديث عند النقر على زر التحديث
            $(document).on('click', '#refreshTodayBtn', function() {
                loadTodayAppointments();
            });
        });

        // التحقق من رقم الهاتف عند الخروج من الحقل
        $('#customer_phone').on('blur', function() {
            const phone = $(this).val().trim();
            if (!phone) return;

            $.ajax({
                url: '../../api/customers.php',
                type: 'POST',
                data: {
                    action: 'check_phone_exists',
                    phone: phone
                },
                dataType: 'json',
                success: function(response) {
                    if (response.exists) {
                        $('#customer_phone').addClass('is-invalid');
                        if (!$('#phone-error-feedback').length) {
                            $('#customer_phone').after('<div id="phone-error-feedback" class="invalid-feedback">رقم الهاتف مستخدم بالفعل</div>');
                        }
                    } else {
                        $('#customer_phone').removeClass('is-invalid');
                        $('#phone-error-feedback').remove();
                    }
                }
            });
        });
    });
</script>
