<?php
/**
 * فئة قاعدة البيانات
 * تتعامل مع الاتصال بقاعدة البيانات وتنفيذ الاستعلامات
 */

// منع الوصول المباشر للملف
if (!defined('BASEPATH')) {
    exit('لا يمكن الوصول المباشر لهذا الملف');
}

class DatabaseBackup {
    private $host;
    private $username;
    private $password;
    private $database;
    private $connection;

    public function __construct($host, $username, $password, $database) {
        $this->host = $host;
        $this->username = $username;
        $this->password = $password;
        $this->database = $database;
        
        // إنشاء الاتصال
        $this->connection = new mysqli($this->host, $this->username, $this->password, $this->database);
        
        if ($this->connection->connect_error) {
            die("فشل الاتصال: " . $this->connection->connect_error);
        }
        
        // تعيين الترميز
        $this->connection->set_charset("utf8mb4");
    }

    public function backup($filename = null) {
        if ($filename === null) {
            $filename = $this->database . '_backup_' . date('Y-m-d_H-i-s') . '.sql';
        }

        $sql_content = '';
        
        // إضافة header للملف
        $sql_content .= "-- MySQL Database Backup\n";
        $sql_content .= "-- Database: " . $this->database . "\n";
        $sql_content .= "-- Generated on: " . date('Y-m-d H:i:s') . "\n\n";
        
        $sql_content .= "SET FOREIGN_KEY_CHECKS=0;\n";
        $sql_content .= "SET SQL_MODE = \"NO_AUTO_VALUE_ON_ZERO\";\n";
        $sql_content .= "SET AUTOCOMMIT = 0;\n";
        $sql_content .= "START TRANSACTION;\n";
        $sql_content .= "SET time_zone = \"+00:00\";\n\n";

        // الحصول على قائمة الجداول
        $tables = $this->getTables();
        
        foreach ($tables as $table) {
            // echo "جاري نسخ الجدول: $table\n";
            $sql_content .= $this->getTableStructure($table);
            $sql_content .= $this->getTableData($table);
        }

        $sql_content .= "\nCOMMIT;\n";
        $sql_content .= "SET FOREIGN_KEY_CHECKS=1;\n";

        // حفظ الملف
        if (file_put_contents($filename, $sql_content)) {
            // echo "تم إنشاء النسخة الاحتياطية بنجاح: $filename\n";
            // echo "حجم الملف: " . $this->formatBytes(filesize($filename)) . "\n";
            return true;
        } else {
            // echo "خطأ في إنشاء النسخة الاحتياطية\n";
            return false;
        }
    }

    private function getTables() {
        $tables = array();
        $result = $this->connection->query("SHOW TABLES");
        
        while ($row = $result->fetch_array()) {
            $tables[] = $row[0];
        }
        
        return $tables;
    }

    private function getTableStructure($table) {
        $sql = "\n-- --------------------------------------------------------\n";
        $sql .= "-- Table structure for table `$table`\n";
        $sql .= "-- --------------------------------------------------------\n\n";
        
        $sql .= "DROP TABLE IF EXISTS `$table`;\n";
        
        $result = $this->connection->query("SHOW CREATE TABLE `$table`");
        $row = $result->fetch_array();
        
        $sql .= $row[1] . ";\n\n";
        
        return $sql;
    }

    private function getTableData($table) {
        $sql = "-- Dumping data for table `$table`\n\n";
        
        $result = $this->connection->query("SELECT * FROM `$table`");
        
        if ($result->num_rows > 0) {
            // الحصول على أسماء الأعمدة
            $fields = array();
            while ($field = $result->fetch_field()) {
                $fields[] = $field->name;
            }
            
            $sql .= "INSERT INTO `$table` (`" . implode('`, `', $fields) . "`) VALUES\n";
            
            $values = array();
            while ($row = $result->fetch_array()) {
                $value_parts = array();
                foreach ($row as $key => $value) {
                    if (is_numeric($key)) continue; // تجاهل المفاتيح الرقمية
                    
                    if ($value === null) {
                        $value_parts[] = 'NULL';
                    } else {
                        $value_parts[] = "'" . $this->connection->real_escape_string($value) . "'";
                    }
                }
                $values[] = "(" . implode(', ', $value_parts) . ")";
            }
            
            $sql .= implode(",\n", $values) . ";\n\n";
        } else {
            $sql .= "-- No data in table `$table`\n\n";
        }
        
        return $sql;
    }

    private function formatBytes($size, $precision = 2) {
        $units = array('B', 'KB', 'MB', 'GB', 'TB');
        
        $base = log($size, 1024);
        return round(pow(1024, $base - floor($base)), $precision) . ' ' . $units[floor($base)];
    }

    public function __destruct() {
        if ($this->connection) {
            $this->connection->close();
        }
    }
}
