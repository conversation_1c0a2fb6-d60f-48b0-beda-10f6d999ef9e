<?php
/**
 * صفحة عرض سجل استخدام العروض الترويجية
 */

// تعريف ثابت لمنع الوصول المباشر للملفات المرتبطة
define('BASEPATH', true);

// استدعاء ملفات التهيئة
require_once '../../config/init.php';

// التحقق من تسجيل الدخول
requireLogin();

// التحقق من الصلاحيات
if (!hasPermission('promotions_view')) {
    redirect('index.php');
}

// الحصول على معرف العرض من الرابط
$promotionId = isset($_GET['id']) ? intval($_GET['id']) : 0;

if (!$promotionId) {
    redirect('promotions/index.php');
}

// إنشاء كائن نموذج العروض
$promotionModel = new Promotion($db);

// الحصول على بيانات العرض
$promotion = $promotionModel->getPromotionById($promotionId);

if (!$promotion) {
    setFlash('error', 'العرض غير موجود');
    redirect('promotions/index.php');
}

// الحصول على سجل استخدام العرض
$usageHistory = $promotionModel->getPromotionUsageHistory($promotionId);

// استدعاء قالب الهيدر
include_once __DIR__ . '/../../includes/templates/header.php';
?>

<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <div class="card mb-4">
                <div class="card-header pb-0">
                    <div class="d-flex justify-content-between align-items-center">
                        <h6>سجل استخدام العرض: <?php echo htmlspecialchars($promotion['name']); ?></h6>
                        <a href="index.php" class="btn btn-sm btn-primary">
                            <i class="fas fa-arrow-right"></i> العودة للقائمة
                        </a>
                    </div>
                </div>
                <div class="card-body px-0 pt-0 pb-2">
                    <div class="row px-4 pt-3">
                        <div class="col-md-6">
                            <div class="card mb-3">
                                <div class="card-header bg-light">
                                    <h6 class="mb-0">معلومات العرض</h6>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <p class="text-sm mb-1">الاسم</p>
                                            <h6><?php echo htmlspecialchars($promotion['name']); ?></h6>
                                        </div>
                                        <div class="col-md-6">
                                            <p class="text-sm mb-1">نوع الشرط</p>
                                            <h6>
                                                <?php
                                                $conditionTypes = [
                                                    'total_amount' => 'إجمالي الفاتورة',
                                                    'items_count' => 'عدد العناصر',
                                                    'specific_product' => 'منتج محدد',
                                                    'specific_service' => 'خدمة محددة'
                                                ];
                                                echo $conditionTypes[$promotion['condition_type']] ?? $promotion['condition_type'];
                                                ?>
                                            </h6>
                                        </div>
                                    </div>
                                    <div class="row mt-3">
                                        <div class="col-md-6">
                                            <p class="text-sm mb-1">قيمة الشرط</p>
                                            <h6><?php echo htmlspecialchars($promotion['condition_value']); ?></h6>
                                        </div>
                                        <div class="col-md-6">
                                            <p class="text-sm mb-1">نوع الخصم</p>
                                            <h6>
                                                <?php echo $promotion['discount_type'] === 'percentage' ? 'نسبة مئوية' : 'مبلغ ثابت'; ?>
                                            </h6>
                                        </div>
                                    </div>
                                    <div class="row mt-3">
                                        <div class="col-md-6">
                                            <p class="text-sm mb-1">قيمة الخصم</p>
                                            <h6>
                                                <?php echo htmlspecialchars($promotion['discount_value']); ?>
                                                <?php echo $promotion['discount_type'] === 'percentage' ? '%' : $currencySymbol; ?>
                                            </h6>
                                        </div>
                                        <div class="col-md-6">
                                            <p class="text-sm mb-1">الحالة</p>
                                            <h6>
                                                <?php if ($promotion['is_active']): ?>
                                                    <span class="badge bg-success">نشط</span>
                                                <?php else: ?>
                                                    <span class="badge bg-secondary">غير نشط</span>
                                                <?php endif; ?>
                                            </h6>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card mb-3">
                                <div class="card-header bg-light">
                                    <h6 class="mb-0">إحصائيات الاستخدام</h6>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <p class="text-sm mb-1">عدد مرات الاستخدام</p>
                                            <h6><?php echo count($usageHistory); ?></h6>
                                        </div>
                                        <div class="col-md-6">
                                            <p class="text-sm mb-1">إجمالي قيمة الخصومات</p>
                                            <h6>
                                                <?php
                                                $totalDiscounts = 0;
                                                foreach ($usageHistory as $usage) {
                                                    $totalDiscounts += floatval($usage['discount_amount']);
                                                }
                                                echo number_format($totalDiscounts, 2) . ' ' . $currencySymbol;
                                                ?>
                                            </h6>
                                        </div>
                                    </div>
                                    <div class="row mt-3">
                                        <div class="col-md-6">
                                            <p class="text-sm mb-1">أول استخدام</p>
                                            <h6>
                                                <?php
                                                if (!empty($usageHistory)) {
                                                    $firstUsage = end($usageHistory);
                                                    echo formatDate($firstUsage['created_at']);
                                                } else {
                                                    echo 'لا يوجد';
                                                }
                                                ?>
                                            </h6>
                                        </div>
                                        <div class="col-md-6">
                                            <p class="text-sm mb-1">آخر استخدام</p>
                                            <h6>
                                                <?php
                                                if (!empty($usageHistory)) {
                                                    $lastUsage = reset($usageHistory);
                                                    echo formatDate($lastUsage['created_at']);
                                                } else {
                                                    echo 'لا يوجد';
                                                }
                                                ?>
                                            </h6>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="table-responsive p-0">
                        <table class="table align-items-center mb-0">
                            <thead>
                                <tr>
                                    <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">#</th>
                                    <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">رقم الفاتورة</th>
                                    <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">العميل</th>
                                    <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">قيمة الخصم</th>
                                    <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">تاريخ الاستخدام</th>
                                    <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (empty($usageHistory)): ?>
                                    <tr>
                                        <td colspan="6" class="text-center py-4">لا يوجد سجل استخدام لهذا العرض</td>
                                    </tr>
                                <?php else: ?>
                                    <?php foreach ($usageHistory as $index => $usage): ?>
                                        <tr>
                                            <td class="ps-4">
                                                <p class="text-xs font-weight-bold mb-0"><?php echo $index + 1; ?></p>
                                            </td>
                                            <td class="ps-4">
                                                <p class="text-xs font-weight-bold mb-0"><?php echo htmlspecialchars($usage['invoice_number']); ?></p>
                                            </td>
                                            <td class="ps-4">
                                                <p class="text-xs font-weight-bold mb-0">
                                                    <?php echo $usage['customer_name'] ? htmlspecialchars($usage['customer_name']) : 'غير محدد'; ?>
                                                </p>
                                            </td>
                                            <td class="ps-4">
                                                <p class="text-xs font-weight-bold mb-0">
                                                    <?php echo number_format($usage['discount_amount'], 2) . ' ' . $currencySymbol; ?>
                                                </p>
                                            </td>
                                            <td class="ps-4">
                                                <p class="text-xs font-weight-bold mb-0">
                                                    <?php echo formatDate($usage['created_at']); ?>
                                                </p>
                                            </td>
                                            <td class="ps-4">
                                                <a href="../invoices/view.php?id=<?php echo $usage['invoice_id']; ?>" class="btn btn-sm btn-info">
                                                    <i class="fas fa-eye"></i> عرض الفاتورة
                                                </a>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
// استدعاء قالب الفوتر
include_once __DIR__ . '/../../includes/templates/footer.php';
?>
