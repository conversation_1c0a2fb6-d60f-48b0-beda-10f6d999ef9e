# Salon Management System - Apache Configuration

# Prevent directory listing
Options -Indexes

# Secure .htaccess file
<Files .htaccess>
    Order Allow,<PERSON>y
    Den<PERSON> from all
</Files>

# Prevent viewing of sensitive files
<FilesMatch "^(config\.php|\.env|db\.sql)$">
    Order Allow,<PERSON>y
    Deny from all
</FilesMatch>

# Enable URL Rewriting
RewriteEngine On

# Set base path
RewriteBase /

# Redirect HTTP to HTTPS (uncomment if using SSL)
# RewriteCond %{HTTPS} off
# RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

# Remove www from URL
RewriteCond %{HTTP_HOST} ^www\.(.*)$ [NC]
RewriteRule ^(.*)$ https://%1/$1 [R=301,L]

# Block access to sensitive directories
RewriteRule ^(config|includes|logs)/ - [F]

# Clean URL for authentication pages
RewriteRule ^login/?$ pages/auth/login.php [L]
RewriteRule ^logout/?$ api/auth.php?action=logout [L]
RewriteRule ^register/?$ pages/auth/register.php [L]

# Clean URLs for main pages
RewriteRule ^dashboard/?$ pages/dashboard.php [L]
RewriteRule ^profile/?$ pages/profile.php [L]

# Clean URLs for specific modules
RewriteRule ^customers/?$ pages/customers/index.php [L]
RewriteRule ^customers/add/?$ pages/customers/add.php [L]
RewriteRule ^customers/edit/([0-9]+)/?$ pages/customers/edit.php?id=$1 [L]

RewriteRule ^employees/?$ pages/employees/index.php [L]
RewriteRule ^employees/add/?$ pages/employees/add.php [L]
RewriteRule ^employees/edit/([0-9]+)/?$ pages/employees/edit.php?id=$1 [L]

RewriteRule ^services/?$ pages/services/index.php [L]
RewriteRule ^services/add/?$ pages/services/add.php [L]
RewriteRule ^services/edit/([0-9]+)/?$ pages/services/edit.php?id=$1 [L]

RewriteRule ^products/?$ pages/products/index.php [L]
RewriteRule ^products/add/?$ pages/products/add.php [L]
RewriteRule ^products/edit/([0-9]+)/?$ pages/products/edit.php?id=$1 [L]

# Protect against SQL injection
RewriteCond %{QUERY_STRING} (\<|%3C).*script.*(\>|%3E) [NC,OR]
RewriteCond %{QUERY_STRING} GLOBALS(=|\[|\%[0-9A-Z]{0,2}) [OR]
RewriteCond %{QUERY_STRING} _REQUEST(=|\[|\%[0-9A-Z]{0,2})
RewriteRule ^(.*)$ index.php [F,L]

# Protect against Local File Inclusion (LFI)
RewriteCond %{QUERY_STRING} \.\./
RewriteRule ^(.*)$ index.php [F,L]

# Compression for better performance
<IfModule mod_deflate.c>
    # Compress HTML, CSS, JavaScript, Text, XML and fonts
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/vnd.ms-fontobject
    AddOutputFilterByType DEFLATE application/x-font
    AddOutputFilterByType DEFLATE application/x-font-opentype
    AddOutputFilterByType DEFLATE application/x-font-otf
    AddOutputFilterByType DEFLATE application/x-font-truetype
    AddOutputFilterByType DEFLATE application/x-font-ttf
    AddOutputFilterByType DEFLATE application/x-javascript
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE font/opentype
    AddOutputFilterByType DEFLATE font/otf
    AddOutputFilterByType DEFLATE font/ttf
    AddOutputFilterByType DEFLATE image/svg+xml
    AddOutputFilterByType DEFLATE image/x-icon
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/javascript
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/xml
</IfModule>

# Enable Keep-Alive
<IfModule mod_headers.c>
    Header set Connection keep-alive
</IfModule>

# Set default character encoding
AddDefaultCharset UTF-8

# Error documents
ErrorDocument 400 /pages/errors/400.php
ErrorDocument 401 /pages/errors/401.php
ErrorDocument 403 /pages/errors/403.php
ErrorDocument 404 /pages/errors/404.php
ErrorDocument 500 /pages/errors/500.php

# Security headers
<IfModule mod_headers.c>
    # Protect against XSS attacks
    Header set X-XSS-Protection "1; mode=block"
    
    # Prevent clickjacking
    Header always append X-Frame-Options SAMEORIGIN
    
    # Strict MIME type sniffing
    Header set X-Content-Type-Options nosniff
    
    # Referrer Policy
    Header set Referrer-Policy "strict-origin-when-cross-origin"
</IfModule>

# PHP settings (optional, can be managed via php.ini)
<IfModule mod_php.c>
    php_value upload_max_filesize 10M
    php_value post_max_size 10M
    php_value max_execution_time 300
    php_value max_input_time 300
</IfModule>