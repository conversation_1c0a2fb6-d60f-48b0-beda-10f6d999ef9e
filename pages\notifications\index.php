<?php
/**
 * صفحة عرض الإشعارات
 */

// تعريف ثابت لمنع الوصول المباشر للملفات المرتبطة
define('BASEPATH', true);

// استدعاء ملفات التهيئة
require_once '../../config/init.php';

// التحقق من تسجيل الدخول
requireLogin();

// عنوان الصفحة
$pageTitle = 'الإشعارات';

// إنشاء اتصال بقاعدة البيانات
$db = new Database();

// الحصول على معرف المستخدم الحالي
$userId = $_SESSION['user_id'];

// الحصول على الإشعارات
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$limit = 20;
$offset = ($page - 1) * $limit;

// استعلام عدد الإشعارات
$db->prepare("SELECT COUNT(*) FROM notifications 
            WHERE recipient_id = :user_id 
            AND recipient_type = 'user'");
$db->bind(':user_id', $userId);
$totalNotifications = $db->fetchColumn();

// حساب عدد الصفحات
$totalPages = ceil($totalNotifications / $limit);

// التأكد من أن رقم الصفحة صحيح
if ($page < 1) {
    $page = 1;
} elseif ($page > $totalPages && $totalPages > 0) {
    $page = $totalPages;
}

// استعلام الإشعارات
$db->prepare("SELECT * FROM notifications 
            WHERE recipient_id = :user_id 
            AND recipient_type = 'user' 
            ORDER BY created_at DESC 
            LIMIT :limit OFFSET :offset");
$db->bind(':user_id', $userId);
$db->bind(':limit', $limit, PDO::PARAM_INT);
$db->bind(':offset', $offset, PDO::PARAM_INT);
$notifications = $db->fetchAll();

// تعيين جميع الإشعارات كمقروءة
if (isset($_GET['mark_all_read']) && $_GET['mark_all_read'] == 1) {
    $db->prepare("UPDATE notifications 
                SET is_read = 1, updated_at = NOW() 
                WHERE recipient_id = :user_id 
                AND recipient_type = 'user' 
                AND is_read = 0");
    $db->bind(':user_id', $userId);
    $db->execute();
    
    // إعادة التوجيه لتجنب إعادة تحميل الصفحة
    header('Location: index.php');
    exit;
}

// استدعاء رأس الصفحة
include '../../includes/templates/header.php';
?>

<div class="container-fluid">
    <!-- العنوان وشريط التنقل -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-body d-flex justify-content-between align-items-center">
                    <h1 class="h3 mb-0">الإشعارات</h1>
                    <a href="?mark_all_read=1" class="btn btn-sm btn-outline-primary">تعيين الكل كمقروء</a>
                </div>
            </div>
        </div>
    </div>

    <!-- قائمة الإشعارات -->
    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-body p-0">
                    <?php if (empty($notifications)): ?>
                        <div class="text-center p-5">
                            <i class="fas fa-bell-slash text-muted mb-3" style="font-size: 3rem;"></i>
                            <h5 class="text-muted">لا توجد إشعارات</h5>
                        </div>
                    <?php else: ?>
                        <div class="list-group list-group-flush">
                            <?php foreach ($notifications as $notification): ?>
                                <?php
                                // تحديد أيقونة الإشعار بناءً على النوع
                                $iconClass = 'fas fa-bell';
                                $iconType = '';
                                
                                if ($notification['type'] === 'appointment_reminder') {
                                    $iconClass = 'fas fa-calendar-check';
                                    $iconType = 'appointment';
                                } elseif ($notification['type'] === 'system') {
                                    $iconClass = 'fas fa-cog';
                                    $iconType = 'system';
                                } elseif ($notification['type'] === 'alert') {
                                    $iconClass = 'fas fa-exclamation-triangle';
                                    $iconType = 'alert';
                                }
                                
                                // تنسيق وقت الإشعار
                                $createdAt = new DateTime($notification['created_at']);
                                $now = new DateTime();
                                $interval = $now->diff($createdAt);
                                
                                if ($interval->days == 0) {
                                    if ($interval->h == 0) {
                                        if ($interval->i == 0) {
                                            $timeAgo = 'الآن';
                                        } else {
                                            $timeAgo = 'منذ ' . $interval->i . ' دقيقة';
                                        }
                                    } else {
                                        $timeAgo = 'منذ ' . $interval->h . ' ساعة';
                                    }
                                } elseif ($interval->days < 7) {
                                    $timeAgo = 'منذ ' . $interval->days . ' يوم';
                                } else {
                                    $timeAgo = $createdAt->format('Y-m-d');
                                }
                                
                                // تحديد الرابط المرتبط بالإشعار
                                $link = '#';
                                if ($notification['related_type'] && $notification['related_id']) {
                                    switch ($notification['related_type']) {
                                        case 'appointment':
                                            $link = '../appointments/view.php?id=' . $notification['related_id'];
                                            break;
                                        case 'invoice':
                                            $link = '../invoices/view.php?id=' . $notification['related_id'];
                                            break;
                                        case 'customer':
                                            $link = '../customers/view.php?id=' . $notification['related_id'];
                                            break;
                                    }
                                }
                                ?>
                                <a href="<?php echo $link; ?>" class="list-group-item list-group-item-action <?php echo $notification['is_read'] == 0 ? 'list-group-item-light' : ''; ?>" data-id="<?php echo $notification['id']; ?>">
                                    <div class="d-flex align-items-start">
                                        <div class="notification-icon <?php echo $iconType; ?> me-3">
                                            <i class="<?php echo $iconClass; ?>"></i>
                                        </div>
                                        <div class="flex-grow-1">
                                            <div class="d-flex justify-content-between align-items-center">
                                                <h6 class="mb-1"><?php echo htmlspecialchars($notification['title']); ?></h6>
                                                <small class="text-muted"><?php echo $timeAgo; ?></small>
                                            </div>
                                            <p class="mb-1"><?php echo htmlspecialchars($notification['message']); ?></p>
                                        </div>
                                    </div>
                                </a>
                            <?php endforeach; ?>
                        </div>
                        
                        <!-- ترقيم الصفحات -->
                        <?php if ($totalPages > 1): ?>
                            <div class="d-flex justify-content-center p-3">
                                <nav aria-label="Page navigation">
                                    <ul class="pagination">
                                        <li class="page-item <?php echo $page <= 1 ? 'disabled' : ''; ?>">
                                            <a class="page-link" href="?page=<?php echo $page - 1; ?>" aria-label="Previous">
                                                <span aria-hidden="true">&laquo;</span>
                                            </a>
                                        </li>
                                        <?php for ($i = 1; $i <= $totalPages; $i++): ?>
                                            <li class="page-item <?php echo $i == $page ? 'active' : ''; ?>">
                                                <a class="page-link" href="?page=<?php echo $i; ?>"><?php echo $i; ?></a>
                                            </li>
                                        <?php endfor; ?>
                                        <li class="page-item <?php echo $page >= $totalPages ? 'disabled' : ''; ?>">
                                            <a class="page-link" href="?page=<?php echo $page + 1; ?>" aria-label="Next">
                                                <span aria-hidden="true">&raquo;</span>
                                            </a>
                                        </li>
                                    </ul>
                                </nav>
                            </div>
                        <?php endif; ?>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    $(document).ready(function() {
        // تعيين الإشعار كمقروء عند النقر عليه
        $('.list-group-item').on('click', function() {
            const notificationId = $(this).data('id');
            
            $.ajax({
                url: `${API_URL}notifications.php?action=mark_as_read`,
                method: 'POST',
                data: { notification_id: notificationId },
                dataType: 'json'
            });
            
            // إزالة تنسيق غير مقروء
            $(this).removeClass('list-group-item-light');
        });
    });
</script>

<?php
// استدعاء تذييل الصفحة
include '../../includes/templates/footer.php';
?>
