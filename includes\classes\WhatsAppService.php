<?php
/**
 * فئة خدمة WhatsApp
 * تستخدم لإرسال رسائل WhatsApp باستخدام Twilio أو خدمات مماثلة
 */

// منع الوصول المباشر للملف
if (!defined('BASEPATH')) {
    exit('لا يمكن الوصول المباشر لهذا الملف');
}

class WhatsAppService {
    private $db;
    private $settings;
    private $accountSid;
    private $authToken;
    private $fromNumber;
    private $enabled;
    private $logFile;

    /**
     * المُنشئ
     *
     * @param Database $db كائن قاعدة البيانات
     */
    public function __construct($db) {
        $this->db = $db;
        $this->logFile = dirname(__DIR__, 2) . '/logs/whatsapp_messages.log';
        
        // التأكد من وجود مجلد السجلات
        $logDir = dirname($this->logFile);
        if (!is_dir($logDir)) {
            mkdir($logDir, 0755, true);
        }
        
        // تحميل الإعدادات
        $this->loadSettings();
    }

    /**
     * تحميل إعدادات WhatsApp من قاعدة البيانات
     */
    private function loadSettings() {
        try {
            $settingsModel = new Settings($this->db);
            $allSettings = $settingsModel->getAllSettings();
            
            // استخراج إعدادات WhatsApp
            $this->enabled = isset($allSettings['whatsapp_enabled']) ? (bool)$allSettings['whatsapp_enabled'] : false;
            $this->accountSid = $allSettings['whatsapp_account_sid'] ?? '';
            $this->authToken = $allSettings['whatsapp_auth_token'] ?? '';
            $this->fromNumber = $allSettings['whatsapp_from_number'] ?? '';
            
            // تسجيل حالة التحميل
            $this->log("تم تحميل إعدادات WhatsApp. الحالة: " . ($this->enabled ? 'مفعل' : 'غير مفعل'));
        } catch (Exception $e) {
            $this->log("خطأ في تحميل إعدادات WhatsApp: " . $e->getMessage());
            $this->enabled = false;
        }
    }

    /**
     * إرسال رسالة WhatsApp
     *
     * @param string $to رقم الهاتف المستلم (بتنسيق دولي بدون +)
     * @param string $message نص الرسالة
     * @param array $options خيارات إضافية
     * @return bool نجاح أو فشل الإرسال
     */
    public function sendMessage($to, $message, $options = []) {
        // التحقق من تفعيل الخدمة
        if (!$this->enabled) {
            $this->log("محاولة إرسال رسالة لكن الخدمة غير مفعلة. الرقم: $to");
            return false;
        }
        
        // التحقق من وجود المعلومات المطلوبة
        if (empty($this->accountSid) || empty($this->authToken) || empty($this->fromNumber)) {
            $this->log("معلومات Twilio غير مكتملة. لا يمكن إرسال الرسالة إلى: $to");
            return false;
        }
        
        try {
            // تنسيق رقم الهاتف
            $to = $this->formatPhoneNumber($to);
            
            // تسجيل محاولة الإرسال
            $this->log("محاولة إرسال رسالة إلى $to: " . substr($message, 0, 50) . "...");
            
            // استخدام Twilio لإرسال الرسالة
            $result = $this->sendViaTwilio($to, $message);
            
            if ($result) {
                $this->log("تم إرسال الرسالة بنجاح إلى: $to");
                return true;
            } else {
                $this->log("فشل إرسال الرسالة إلى: $to");
                return false;
            }
        } catch (Exception $e) {
            $this->log("خطأ في إرسال رسالة WhatsApp إلى $to: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * إرسال رسالة عبر Twilio
     *
     * @param string $to رقم الهاتف المستلم
     * @param string $message نص الرسالة
     * @return bool نجاح أو فشل الإرسال
     */
    private function sendViaTwilio($to, $message) {
        try {
            // تحضير بيانات الطلب
            $url = "https://api.twilio.com/2010-04-01/Accounts/{$this->accountSid}/Messages.json";
            $data = [
                'From' => "whatsapp:{$this->fromNumber}",
                'To' => "whatsapp:$to",
                'Body' => $message
            ];
            
            // إعداد طلب cURL
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_POST, 1);
            curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($data));
            curl_setopt($ch, CURLOPT_USERPWD, "{$this->accountSid}:{$this->authToken}");
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            
            // تنفيذ الطلب
            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);
            
            // تحليل الاستجابة
            $responseData = json_decode($response, true);
            
            // تسجيل الاستجابة
            $this->log("استجابة Twilio (HTTP $httpCode): " . substr($response, 0, 100) . "...");
            
            // التحقق من نجاح الإرسال
            return $httpCode >= 200 && $httpCode < 300 && isset($responseData['sid']);
        } catch (Exception $e) {
            $this->log("خطأ في إرسال رسالة عبر Twilio: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * تنسيق رقم الهاتف للاستخدام مع WhatsApp
     *
     * @param string $phone رقم الهاتف
     * @param string $countryCode رمز البلد (اختياري)
     * @return string رقم الهاتف المنسق
     */
    public function formatPhoneNumber($phone, $countryCode = null) {
        // إزالة أي أحرف غير رقمية
        $phone = preg_replace('/[^0-9]/', '', $phone);
        
        // إزالة الصفر الأول إذا كان موجودًا
        if (substr($phone, 0, 1) === '0') {
            $phone = substr($phone, 1);
        }
        
        // إضافة رمز البلد إذا تم تمريره
        if ($countryCode) {
            // إزالة علامة + إذا كانت موجودة
            $countryCode = str_replace('+', '', $countryCode);
            
            // إضافة رمز البلد إذا لم يكن موجودًا بالفعل
            if (strpos($phone, $countryCode) !== 0) {
                $phone = $countryCode . $phone;
            }
        }
        
        return $phone;
    }
    
    /**
     * تسجيل رسالة في ملف السجل
     *
     * @param string $message الرسالة المراد تسجيلها
     */
    private function log($message) {
        $timestamp = date('Y-m-d H:i:s');
        file_put_contents($this->logFile, "[$timestamp] $message" . PHP_EOL, FILE_APPEND);
    }
}
