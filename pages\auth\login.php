<?php
/**
 * صفحة تسجيل الدخول
 * تتيح للمستخدم تسجيل الدخول إلى النظام
 */

// تعريف ثابت لمنع الوصول المباشر للملفات المرتبطة
define('BASEPATH', true);

// استدعاء ملفات التهيئة
require_once '../../config/init.php';

// التحقق مما إذا كان المستخدم مسجل الدخول بالفعل، إذا كان كذلك، إعادة توجيهه إلى لوحة التحكم
if (isLoggedIn()) {
    header('Location: ../../pages/dashboard.php');
    exit;
}

// تهيئة متغيرات رسائل الخطأ والنجاح
$errorMsg = '';
$successMsg = '';

// إذا كانت هناك رسالة نجاح في الجلسة، يتم استخراجها
if (isset($_SESSION['success_message'])) {
    $successMsg = $_SESSION['success_message'];
    unset($_SESSION['success_message']);
}

// معالجة طلب تسجيل الدخول
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['login'])) {
    // استخراج البيانات من النموذج
    $username = isset($_POST['username']) ? trim($_POST['username']) : '';
    $password = isset($_POST['password']) ? $_POST['password'] : '';
    $remember = isset($_POST['remember']) ? (bool)$_POST['remember'] : false;

    // التحقق من وجود اسم المستخدم وكلمة المرور
    if (empty($username) || empty($password)) {
        $errorMsg = 'يرجى إدخال اسم المستخدم وكلمة المرور';
    } else {
        try {
            // إنشاء كائن المستخدم
            $userModel = new User($db);

            // محاولة تسجيل الدخول
            $user = $userModel->login($username, $password);

            if ($user) {
                // إذا كان المستخدم معطلًا، منع تسجيل الدخول
                if (!$user['is_active']) {
                    $errorMsg = 'حسابك معطل. يرجى التواصل مع المدير';
                } else {
                    // تم تسجيل الدخول بنجاح، إنشاء متغيرات الجلسة
                    $_SESSION['user_id'] = $user['id'];
                    $_SESSION['user_name'] = $user['name'];
                    $_SESSION['user_role'] = $user['role'];
                    $_SESSION['user_branch_id'] = $user['branch_id'];
                    $_SESSION['last_activity'] = time();

                    // استرجاع صلاحيات المستخدم
                    $_SESSION['user_permissions'] = $userModel->getUserPermissions($user['id']);

                    // إذا تم اختيار "تذكرني"، يتم إنشاء كوكي للتذكر
                    if ($remember) {
                        // إنشاء رمز تذكر عشوائي
                        $token = bin2hex(random_bytes(32));

                        // حفظ الرمز في قاعدة البيانات
                        $userModel->saveRememberToken($user['id'], $token);

                        // حفظ الرمز في كوكي يستمر لمدة 30 يومًا
                        setcookie('remember_token', $token, time() + (86400 * 30), '/');
                    }

                    // تحديث وقت آخر تسجيل دخول
                    $userModel->updateLoginTime($user['id']);

                    // إعادة التوجيه إلى لوحة التحكم
                    header('Location: ../../pages/dashboard.php');
                    exit;
                }
            } else {
                $errorMsg = 'اسم المستخدم أو كلمة المرور غير صحيحة';
            }
        } catch (Exception $e) {
            $errorMsg = 'حدث خطأ أثناء تسجيل الدخول: ' . $e->getMessage();
        }
    }
}

// التحقق من وجود كوكي تذكر وتسجيل الدخول تلقائياً
if (!isLoggedIn() && isset($_COOKIE['remember_token']) && !empty($_COOKIE['remember_token'])) {
    $token = $_COOKIE['remember_token'];

    try {
        // إنشاء كائن المستخدم
        $userModel = new User($db);

        // محاولة تسجيل الدخول بالرمز
        $user = $userModel->loginByToken($token);

        if ($user && $user['is_active']) {
            // تم تسجيل الدخول بنجاح، إنشاء متغيرات الجلسة
            $_SESSION['user_id'] = $user['id'];
            $_SESSION['user_name'] = $user['name'];
            $_SESSION['user_role'] = $user['role'];
            $_SESSION['user_branch_id'] = $user['branch_id'];
            $_SESSION['last_activity'] = time();

            // استرجاع صلاحيات المستخدم
            $_SESSION['user_permissions'] = $userModel->getUserPermissions($user['id']);

            // تحديث وقت آخر تسجيل دخول
            $userModel->updateLoginTime($user['id']);

            // إعادة التوجيه إلى لوحة التحكم
            header('Location: ../../pages/dashboard.php');
            exit;
        } else {
            // إذا فشل تسجيل الدخول بالرمز، يتم حذف الكوكي
            setcookie('remember_token', '', time() - 3600, '/');
        }
    } catch (Exception $e) {
        // تجاهل الأخطاء هنا، سيتم عرض نموذج تسجيل الدخول بدلاً من ذلك
        error_log('خطأ في تسجيل الدخول التلقائي: ' . $e->getMessage());
    }
}

// عنوان الصفحة
$pageTitle = 'تسجيل الدخول';
?>
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo SYSTEM_NAME; ?> - <?php echo $pageTitle; ?></title>

    <!-- Bootstrap RTL via CDN -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.rtl.min.css">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    <!-- Google Font Tajawal -->
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap">

    <style>
        body {
            background-color: #f7f9fc;
            font-family: 'Tajawal', sans-serif;
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow-x: hidden;
            margin: 0;
            padding: 0;
        }
        .container {
            display: flex;
            justify-content: center;
            align-items: center;
            opacity: 0;
            transform: translateY(20px);
            transition: opacity 0.5s ease, transform 0.5s ease;
        }
        .container.show {
            opacity: 1;
            transform: translateY(0);
        }
        .login-card {
            background-color: #ffffff;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.05);
            width: 100%;
            max-width: 400px;
            padding: 30px;
        }
        .login-logo {
            text-align: center;
            margin-bottom: 15px;
        }
        .login-title {
            text-align: center;
            font-weight: 600;
            margin-bottom: 30px;
            color: #333;
        }
        .form-label {
            font-weight: 500;
        }
        .btn-login {
            background-color: #3498db;
            border: none;
            padding: 10px;
            font-weight: 500;
            transition: all 0.3s;
            width: 100%;
        }
        .btn-login:hover {
            background-color: #2980b9;
        }

        .form-control.is-invalid {
            border-color: #dc3545;
            box-shadow: 0 0 0 0.25rem rgba(220, 53, 69, 0.25);
        }

        .form-control:focus {
            border-color: #3498db;
            box-shadow: 0 0 0 0.25rem rgba(52, 152, 219, 0.25);
        }

        /* تنسيقات شاشة التحميل */
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: #f7f9fc;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            z-index: 9999;
            transition: opacity 0.5s ease, visibility 0.5s ease;
        }

        .loading-overlay.hide {
            opacity: 0;
            visibility: hidden;
        }

        .loading-spinner {
            position: relative;
            width: 80px;
            height: 80px;
            margin-bottom: 20px;
        }

        .loading-spinner:before {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            border-radius: 50%;
            border: 4px solid transparent;
            border-top-color: #3498db;
            border-bottom-color: #3498db;
            animation: spin 1.5s linear infinite;
        }

        .loading-spinner:after {
            content: "";
            position: absolute;
            top: 10px;
            left: 10px;
            right: 10px;
            bottom: 10px;
            border-radius: 50%;
            border: 4px solid transparent;
            border-left-color: #2980b9;
            border-right-color: #2980b9;
            animation: spin 1s linear infinite reverse;
        }

        .loading-logo {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: #3498db;
            font-size: 30px;
        }

        .loading-text {
            font-size: 18px;
            color: #333;
            font-weight: 500;
            text-align: center;
            margin-bottom: 5px;
        }

        .loading-brand {
            font-size: 22px;
            color: #3498db;
            font-weight: 700;
            text-align: center;
            letter-spacing: 1px;
            animation: pulse 1.5s infinite alternate;
        }

        @keyframes pulse {
            from {
                opacity: 0.8;
                transform: scale(0.98);
            }
            to {
                opacity: 1;
                transform: scale(1);
            }
        }

        @keyframes spin {
            0% {
                transform: rotate(0deg);
            }
            100% {
                transform: rotate(360deg);
            }
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
    </style>
</head>
<body>
    <!-- شاشة التحميل -->
    <div class="loading-overlay">
        <div class="loading-spinner">
            <div class="loading-logo">
                <i class="fas fa-cut"></i>
            </div>
        </div>
        <div class="loading-text">جاري تحميل النظام...</div>
        <div class="loading-brand mt-3">HOK SALON ERP</div>
    </div>

    <div class="container">
        <div class="login-card">
            <!-- لوجو النظام -->
            <div class="login-logo">
                <i class="fas fa-cut fa-3x" style="color: #3498db;"></i>
            </div>

            <!-- عنوان النظام -->
            <h2 class="login-title">HOK SALON ERP</h2>

            <!-- رسائل الخطأ والنجاح -->
            <?php if (!empty($errorMsg)): ?>
                <div class="alert alert-danger" role="alert">
                    <?php echo $errorMsg; ?>
                </div>
            <?php endif; ?>

            <?php if (!empty($successMsg)): ?>
                <div class="alert alert-success" role="alert">
                    <?php echo $successMsg; ?>
                </div>
            <?php endif; ?>

            <!-- نموذج تسجيل الدخول -->
            <form method="post">
                <div class="mb-3">
                    <label for="username" class="form-label">اسم المستخدم</label>
                    <input type="text" class="form-control" id="username" name="username" required>
                </div>

                <div class="mb-3">
                    <label for="password" class="form-label">كلمة المرور</label>
                    <input type="password" class="form-control" id="password" name="password" required>
                </div>

                <div class="mb-3 form-check">
                    <input type="checkbox" class="form-check-input" id="remember" name="remember" value="1">
                    <label class="form-check-label" for="remember">تذكرني</label>
                </div>

                <button type="submit" name="login" class="btn btn-primary btn-login">
                    تسجيل الدخول
                </button>
            </form>
        </div>
    </div>

    <!-- jQuery via CDN -->
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <!-- Bootstrap Bundle JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // تحديد حقل اسم المستخدم تلقائياً عند تحميل الصفحة
        $(document).ready(function() {
            // إخفاء شاشة التحميل بعد اكتمال تحميل الصفحة
            setTimeout(function() {
                $('.loading-overlay').addClass('hide');
                setTimeout(function() {
                    $('.container').addClass('show');
                    $('#username').focus();
                }, 300);
            }, 1500); // تأخير 1.5 ثانية لإظهار شاشة التحميل
        });

        // إضافة تأثير التحميل عند تقديم النموذج
        $('form').on('submit', function() {
            // التحقق من صحة النموذج
            var username = $('#username').val().trim();
            var password = $('#password').val().trim();

            if (username === '' || password === '') {
                // إذا كانت الحقول فارغة، لا تقم بإرسال النموذج
                if (username === '') {
                    $('#username').addClass('is-invalid').focus();
                }
                if (password === '') {
                    $('#password').addClass('is-invalid');
                }
                return false;
            }

            // إظهار شاشة التحميل
            $('.loading-overlay').removeClass('hide');
            $('.loading-text').text('جاري تسجيل الدخول...');
            return true;
        });

        // إزالة تنسيق الخطأ عند الكتابة
        $('#username, #password').on('input', function() {
            $(this).removeClass('is-invalid');
        });
    </script>
</body>
</html>