<?php
/**
 * API لإرسال إشعارات الفواتير
 * يستخدم لإرسال إشعارات للعملاء بعد إنشاء الفواتير
 */

// تعريف ثابت لمنع الوصول المباشر للملفات المرتبطة
define('BASEPATH', true);

// إعداد رؤوس CORS للسماح بالوصول من أي مصدر
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, X-Ignore-SSL');
header('Content-Type: application/json; charset=UTF-8');

// التعامل مع طلبات OPTIONS للتعامل مع CORS
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// استدعاء ملفات التهيئة
require_once '../config/init.php';

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    echo json_encode([
        'status' => 'error',
        'message' => 'يجب تسجيل الدخول أولاً'
    ]);
    exit;
}

// الحصول على الإجراء المطلوب
$action = isset($_GET['action']) ? $_GET['action'] : '';

// معالجة الطلبات
try {
    // إعداد رأس الاستجابة
    header('Content-Type: application/json');

    // إنشاء كائن إشعارات الفواتير
    $db = new Database();
    $invoiceNotification = new InvoiceNotification($db);

    switch ($action) {
        // إرسال إشعار فاتورة
        case 'send':
            // التأكد من أن الطلب عبر POST
            if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
                throw new Exception('الطريقة غير مسموح بها', 405);
            }

            // التحقق من وجود معرف الفاتورة
            $invoiceId = isset($_POST['invoice_id']) ? intval($_POST['invoice_id']) : 0;

            if (!$invoiceId) {
                throw new Exception('معرف الفاتورة مطلوب', 400);
            }

            // التحقق مما إذا كان الطلب في الخلفية
            $isBackground = isset($_POST['background']) && $_POST['background'] === 'true';

            // إذا كان الطلب في الخلفية، نقوم بإنهاء الاتصال مبكرًا ومتابعة المعالجة في الخلفية
            if ($isBackground) {
                // إنهاء الاتصال مع المتصفح ومتابعة المعالجة في الخلفية
                header('Content-Type: application/json');
                echo json_encode(['status' => 'processing', 'message' => 'جاري معالجة الطلب في الخلفية']);

                // إنهاء الاتصال مع المتصفح
                if (function_exists('fastcgi_finish_request')) {
                    fastcgi_finish_request(); // لل PHP-FPM
                } else {
                    // للخوادم الأخرى
                    ob_end_flush();
                    flush();
                }

                // تسجيل بدء المعالجة في الخلفية
                error_log('بدء معالجة إرسال إشعار الفاتورة رقم ' . $invoiceId . ' في الخلفية');
            }

            // إرسال إشعار الفاتورة
            try {
                // تفعيل إشعارات الفواتير مؤقتًا للإرسال اليدوي
                $settingsModel = new Settings($db);

                // حفظ الإعدادات الحالية لاستعادتها لاحقًا
                $currentSettings = $settingsModel->getAllSettings();
                $originalWhatsappEnabled = isset($currentSettings['whatsapp_enabled']) ? $currentSettings['whatsapp_enabled'] : 0;
                $originalNotificationEnabled = isset($currentSettings['notification_nvoice_notification']) ? $currentSettings['notification_nvoice_notification'] : 'off';

                // تفعيل الإعدادات مؤقتًا
                $settingsModel->updateSetting('notification_nvoice_notification', 'on');
                $settingsModel->updateSetting('whatsapp_enabled', 1);

                // في حالة الإرسال التلقائي، نتحقق من إعدادات إشعارات المدراء
                // لكن في حالة الإرسال اليدوي، لا نتحقق من هذه الإعدادات
                // لأن المستخدم يريد إرسال الإشعار بغض النظر عن الإعدادات

                $result = $invoiceNotification->sendInvoiceNotification($invoiceId);

                if ($result) {
                    echo json_encode([
                        'status' => 'success',
                        'message' => 'تم إرسال إشعار الفاتورة بنجاح'
                    ]);
                } else {
                    // التحقق من وجود رقم هاتف للعميل
                    $invoiceModel = new Invoice($db);
                    $invoice = $invoiceModel->getInvoiceById($invoiceId);

                    if (!$invoice || empty($invoice['customer_phone'])) {
                        echo json_encode([
                            'status' => 'error',
                            'message' => 'لا يوجد رقم هاتف للعميل'
                        ]);
                    } else {
                        echo json_encode([
                            'status' => 'error',
                            'message' => 'فشل إرسال إشعار الفاتورة. تحقق من إعدادات WhatsApp وسجلات النظام للمزيد من المعلومات.'
                        ]);
                    }
                }
            } catch (Exception $e) {
                // تسجيل الخطأ
                error_log('خطأ في إرسال إشعار الفاتورة: ' . $e->getMessage());

                echo json_encode([
                    'status' => 'error',
                    'message' => 'فشل إرسال إشعار الفاتورة: ' . $e->getMessage()
                ]);
            }
            break;

        // إرسال إشعار فاتورة يدويًا
        case 'send_manual':
            // التأكد من أن الطلب عبر POST
            if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
                throw new Exception('الطريقة غير مسموح بها', 405);
            }

            // التحقق من وجود معرف الفاتورة
            $invoiceId = isset($_POST['invoice_id']) ? intval($_POST['invoice_id']) : 0;

            if (!$invoiceId) {
                throw new Exception('معرف الفاتورة مطلوب', 400);
            }

            // إرسال إشعار الفاتورة
            try {
                // تفعيل إشعارات الفواتير مؤقتًا للإرسال اليدوي
                $settingsModel = new Settings($db);

                // حفظ الإعدادات الحالية لاستعادتها لاحقًا
                $currentSettings = $settingsModel->getAllSettings();
                $originalWhatsappEnabled = isset($currentSettings['whatsapp_enabled']) ? $currentSettings['whatsapp_enabled'] : 0;
                $originalNotificationEnabled = isset($currentSettings['notification_nvoice_notification']) ? $currentSettings['notification_nvoice_notification'] : 'off';

                // تفعيل الإعدادات مؤقتًا
                $settingsModel->updateSetting('notification_nvoice_notification', 'on');
                $settingsModel->updateSetting('whatsapp_enabled', 1);

                // في حالة الإرسال اليدوي، لا نتحقق من إعدادات إشعارات المدراء

                $result = $invoiceNotification->sendInvoiceNotification($invoiceId);

                // استعادة الإعدادات الأصلية
                $settingsModel->updateSetting('notification_nvoice_notification', $originalNotificationEnabled);
                $settingsModel->updateSetting('whatsapp_enabled', $originalWhatsappEnabled);

                if ($result) {
                    echo json_encode([
                        'status' => 'success',
                        'message' => 'تم إرسال إشعار الفاتورة بنجاح'
                    ]);
                } else {
                    // التحقق من وجود رقم هاتف للعميل
                    $invoiceModel = new Invoice($db);
                    $invoice = $invoiceModel->getInvoiceById($invoiceId);

                    if (!$invoice || empty($invoice['customer_phone'])) {
                        echo json_encode([
                            'status' => 'error',
                            'message' => 'لا يوجد رقم هاتف للعميل'
                        ]);
                    } else {
                        echo json_encode([
                            'status' => 'error',
                            'message' => 'فشل إرسال إشعار الفاتورة. تحقق من إعدادات WhatsApp وسجلات النظام للمزيد من المعلومات.'
                        ]);
                    }
                }
            } catch (Exception $e) {
                // استعادة الإعدادات الأصلية في حالة الخطأ
                if (isset($settingsModel) && isset($originalWhatsappEnabled) && isset($originalNotificationEnabled)) {
                    $settingsModel->updateSetting('notification_nvoice_notification', $originalNotificationEnabled);
                    $settingsModel->updateSetting('whatsapp_enabled', $originalWhatsappEnabled);
                }

                // تسجيل الخطأ
                error_log('خطأ في إرسال إشعار الفاتورة: ' . $e->getMessage());

                echo json_encode([
                    'status' => 'error',
                    'message' => 'فشل إرسال إشعار الفاتورة: ' . $e->getMessage()
                ]);
            }
            break;

        // إرسال إشعار فاتورة بعد الطباعة
        case 'send_after_print':
            // التأكد من أن الطلب عبر POST
            if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
                throw new Exception('الطريقة غير مسموح بها', 405);
            }

            // التحقق من وجود معرف الفاتورة
            $invoiceId = isset($_POST['invoice_id']) ? intval($_POST['invoice_id']) : 0;

            if (!$invoiceId) {
                throw new Exception('معرف الفاتورة مطلوب', 400);
            }

            // إرسال إشعار الفاتورة
            try {
                // تفعيل إشعارات الفواتير مؤقتًا للإرسال اليدوي
                $settingsModel = new Settings($db);

                // حفظ الإعدادات الحالية لاستعادتها لاحقًا
                $currentSettings = $settingsModel->getAllSettings();
                $originalWhatsappEnabled = isset($currentSettings['whatsapp_enabled']) ? $currentSettings['whatsapp_enabled'] : 0;
                $originalNotificationEnabled = isset($currentSettings['notification_nvoice_notification']) ? $currentSettings['notification_nvoice_notification'] : 'off';

                // تفعيل الإعدادات مؤقتًا
                $settingsModel->updateSetting('notification_nvoice_notification', 'on');
                $settingsModel->updateSetting('whatsapp_enabled', 1);

                // في حالة الإرسال بعد الطباعة، لا نتحقق من إعدادات إشعارات المدراء

                $result = $invoiceNotification->sendInvoiceNotification($invoiceId);

                // استعادة الإعدادات الأصلية
                $settingsModel->updateSetting('notification_nvoice_notification', $originalNotificationEnabled);
                $settingsModel->updateSetting('whatsapp_enabled', $originalWhatsappEnabled);

                if ($result) {
                    echo json_encode([
                        'status' => 'success',
                        'message' => 'تم إرسال إشعار الفاتورة بنجاح'
                    ]);
                } else {
                    // التحقق من وجود رقم هاتف للعميل
                    $invoiceModel = new Invoice($db);
                    $invoice = $invoiceModel->getInvoiceById($invoiceId);

                    if (!$invoice || empty($invoice['customer_phone'])) {
                        echo json_encode([
                            'status' => 'error',
                            'message' => 'لا يوجد رقم هاتف للعميل'
                        ]);
                    } else {
                        echo json_encode([
                            'status' => 'error',
                            'message' => 'فشل إرسال إشعار الفاتورة. تحقق من إعدادات WhatsApp وسجلات النظام للمزيد من المعلومات.'
                        ]);
                    }
                }
            } catch (Exception $e) {
                // استعادة الإعدادات الأصلية في حالة الخطأ
                if (isset($settingsModel) && isset($originalWhatsappEnabled) && isset($originalNotificationEnabled)) {
                    $settingsModel->updateSetting('notification_nvoice_notification', $originalNotificationEnabled);
                    $settingsModel->updateSetting('whatsapp_enabled', $originalWhatsappEnabled);
                }

                // تسجيل الخطأ
                error_log('خطأ في إرسال إشعار الفاتورة: ' . $e->getMessage());

                echo json_encode([
                    'status' => 'error',
                    'message' => 'فشل إرسال إشعار الفاتورة: ' . $e->getMessage()
                ]);
            }
            break;

        // إرسال إشعار فاتورة للمدراء
        case 'send_to_admin':
            // التأكد من أن الطلب عبر POST
            if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
                throw new Exception('الطريقة غير مسموح بها', 405);
            }

            // التحقق من وجود معرف الفاتورة
            $invoiceId = isset($_POST['invoice_id']) ? intval($_POST['invoice_id']) : 0;

            if (!$invoiceId) {
                throw new Exception('معرف الفاتورة مطلوب', 400);
            }

            // إرسال إشعار الفاتورة للمدراء
            try {
                // التحقق مما إذا كان الطلب في الخلفية
                $isBackground = isset($_POST['background']) && $_POST['background'] === 'true';

                // إذا كان الطلب في الخلفية، نقوم بإنهاء الاتصال مبكرًا ومتابعة المعالجة في الخلفية
                if ($isBackground) {
                    // إنهاء الاتصال مع المتصفح ومتابعة المعالجة في الخلفية
                    header('Content-Type: application/json');
                    echo json_encode(['status' => 'processing', 'message' => 'جاري معالجة الطلب في الخلفية']);

                    // إنهاء الاتصال مع المتصفح
                    if (function_exists('fastcgi_finish_request')) {
                        fastcgi_finish_request(); // لل PHP-FPM
                    } else {
                        // للخوادم الأخرى
                        ob_end_flush();
                        flush();
                    }

                    // تسجيل بدء المعالجة في الخلفية
                    error_log('بدء معالجة إرسال إشعار الفاتورة للمدراء رقم ' . $invoiceId . ' في الخلفية');
                }

                // تفعيل خدمة WhatsApp مؤقتًا للإرسال للمدراء
                $settingsModel = new Settings($db);

                // حفظ الإعدادات الحالية لاستعادتها لاحقًا
                $currentSettings = $settingsModel->getAllSettings();
                $originalWhatsappEnabled = isset($currentSettings['whatsapp_enabled']) ? $currentSettings['whatsapp_enabled'] : 0;

                // تفعيل الإعدادات مؤقتًا
                $settingsModel->updateSetting('whatsapp_enabled', 1);

                // إنشاء كائن إشعارات الفواتير للمدراء
                require_once __DIR__ . '/../includes/classes/AdminInvoiceNotification.php';
                $adminNotification = new AdminInvoiceNotification($db);
                $result = $adminNotification->sendNewInvoiceNotification($invoiceId);

                // استعادة الإعدادات الأصلية
                $settingsModel->updateSetting('whatsapp_enabled', $originalWhatsappEnabled);

                if ($result) {
                    echo json_encode([
                        'status' => 'success',
                        'message' => 'تم إرسال إشعار الفاتورة للمدراء بنجاح'
                    ]);
                } else {
                    echo json_encode([
                        'status' => 'error',
                        'message' => 'فشل إرسال إشعار الفاتورة للمدراء. تحقق من إعدادات WhatsApp وسجلات النظام للمزيد من المعلومات.'
                    ]);
                }
            } catch (Exception $e) {
                // استعادة الإعدادات الأصلية في حالة الخطأ
                if (isset($settingsModel) && isset($originalWhatsappEnabled)) {
                    $settingsModel->updateSetting('whatsapp_enabled', $originalWhatsappEnabled);
                }

                // تسجيل الخطأ
                error_log('خطأ في إرسال إشعار الفاتورة للمدراء: ' . $e->getMessage());

                echo json_encode([
                    'status' => 'error',
                    'message' => 'فشل إرسال إشعار الفاتورة للمدراء: ' . $e->getMessage()
                ]);
            }
            break;

        // الحصول على بيانات الفاتورة للإشعار
        case 'get_invoice_data':
            // التأكد من أن الطلب عبر POST
            if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
                throw new Exception('الطريقة غير مسموح بها', 405);
            }

            // التحقق من وجود معرف الفاتورة
            $invoiceId = isset($_POST['invoice_id']) ? intval($_POST['invoice_id']) : 0;

            if (!$invoiceId) {
                throw new Exception('معرف الفاتورة مطلوب', 400);
            }

            try {
                // الحصول على بيانات الفاتورة
                $invoiceModel = new Invoice($db);
                $invoice = $invoiceModel->getInvoiceById($invoiceId);

                if (!$invoice) {
                    throw new Exception('الفاتورة غير موجودة', 404);
                }

                // الحصول على بيانات العميل
                $customerModel = new Customer($db);
                $customer = $customerModel->getCustomerById($invoice['customer_id']);

                // الحصول على إعدادات النظام
                $settingsModel = new Settings($db);
                $settings = $settingsModel->getAllSettings();

                // الحصول على بيانات الفرع
                $branchModel = new Branch($db);
                $branch = $branchModel->getBranchById($invoice['branch_id']);

                // الحصول على عناصر الفاتورة
                $invoiceItems = $invoiceModel->getInvoiceItems($invoiceId);

                // إنشاء نص الرسالة
                $message = '';

                // استخدام قالب الرسالة من الإعدادات إذا كان متوفرًا
                if (isset($settings['invoice_notification_template']) && !empty($settings['invoice_notification_template'])) {
                    $template = $settings['invoice_notification_template'];

                    // إعداد معلومات الخصم
                    $discountInfo = '';
                    if (isset($invoice['discount_amount']) && $invoice['discount_amount'] > 0) {
                        $currencySymbol = $settings['currency_symbol'] ?? 'ج';

                        // تصحيح التعامل مع قيمة الخصم
                        if (isset($invoice['discount_type']) && $invoice['discount_type'] == 'percentage') {
                            // البحث عن قيمة الخصم الأصلية في حقل notes
                            $originalDiscountValue = null;
                            if (!empty($invoice['notes'])) {
                                preg_match('/\[ORIGINAL_DISCOUNT:(\d+(\.\d+)?)\]/', $invoice['notes'], $matches);
                                if (isset($matches[1])) {
                                    $originalDiscountValue = floatval($matches[1]);
                                }
                            }

                            // استخدام قيمة الخصم الأصلية إذا كانت موجودة
                            if ($originalDiscountValue !== null) {
                                $discountValue = $originalDiscountValue;
                                error_log("Found original discount value in notes: {$discountValue}");
                            } else {
                                // حساب النسبة المئوية كحل بديل
                                $discountValue = ($invoice['discount_amount'] / $invoice['total_amount'] * 100);
                                error_log("Calculated discount percentage as fallback: {$discountValue}");
                            }

                            $discountType = '%';
                            $discountInfo = "\nالخصم: " . number_format($discountValue, 2) . ' ' . $discountType;
                        } else {
                            // خصم بقيمة ثابتة
                            $discountValue = number_format($invoice['discount_amount'], 2);
                            $discountInfo = "\nالخصم: " . $discountValue . ' ' . $currencySymbol;
                        }

                        // إضافة المبلغ بعد الخصم
                        $discountInfo .= "\nالمبلغ بعد الخصم: " . number_format($invoice['final_amount'], 2) . ' ' . $currencySymbol;

                        // تسجيل معلومات الخصم للتصحيح
                        error_log("Discount info for invoice #{$invoiceId}: Type={$invoice['discount_type']}, Value={$discountValue}, Amount={$invoice['discount_amount']}");
                    }


                    // استبدال المتغيرات في القالب
                    $message = str_replace([
                        '{customer_name}',
                        '{invoice_number}',
                        '{invoice_date}',
                        '{invoice_total}',
                        '{branch_name}',
                        '{company_name}',
                        '{discount_info}'
                    ], [
                        $invoice['customer_name'],
                        $invoice['invoice_number'],
                        date('d/m/Y', strtotime($invoice['invoice_date'])),
                        number_format($invoice['total_amount'], 2) . ' ' . ($settings['currency_symbol'] ?? 'ج'),
                        $branch['name'] ?? '',
                        $settings['company_name'] ?? '',
                        $discountInfo
                    ], $template);
                } else {
                    // قالب افتراضي إذا لم يكن هناك قالب محدد
$message = "شكرًا لك {$invoice['customer_name']}\n";
$message .= "تم إنشاء فاتورتك رقم {$invoice['invoice_number']} بنجاح\n";
$message .= "التاريخ: " . date('d/m/Y') . "\n";
$message .= "المبلغ الإجمالي: " . number_format($invoice['total_amount'], 2) . " " . ($settings['currency_symbol'] ?? 'ج') . "\n\n";

$message .= "📢 شكراً لثقتك في البدراوي صالون!\n";
$message .= "يسعدنا دائماً خدمتك، ونتمنى أن تكون تجربتك معنا قد نالت رضاك 🌟\n";
$message .= "نحن نحب نسمع رأيك، ووجودك يهمنا!\n\n";
$message .= "📍 تابعنا على صفحاتنا لمزيد من العروض والخدمات: \n\n";
$message .= "🔹 فيسبوك:\n";
$message .= "https://www.facebook.com/elbadrawysalon \n\n";
$message .= "🔹 إنستجرام:\n";
$message .= "https://www.instagram.com/elbadrawysalon \n\n";
$message .= "وممكن تشوف باقي الخدمات الخاصة بينا علي الموقع بتعنا \n\n";
$message .= "https://elbadrawysalon.site/services.php \n\n";

$message .= "نورتنا، وفي انتظارك في زيارتك الجاية 💇‍♂️💈";


                    // إضافة معلومات الخصم إذا كان موجودًا
                    if (isset($invoice['discount_amount']) && $invoice['discount_amount'] > 0) {
                        $currencySymbol = $settings['currency_symbol'] ?? 'ج';

                        // تصحيح التعامل مع قيمة الخصم
                        if (isset($invoice['discount_type']) && $invoice['discount_type'] == 'percentage') {
                            // البحث عن قيمة الخصم الأصلية في حقل notes
                            $originalDiscountValue = null;
                            if (!empty($invoice['notes'])) {
                                preg_match('/\[ORIGINAL_DISCOUNT:(\d+(\.\d+)?)\]/', $invoice['notes'], $matches);
                                if (isset($matches[1])) {
                                    $originalDiscountValue = floatval($matches[1]);
                                }
                            }

                            // استخدام قيمة الخصم الأصلية إذا كانت موجودة
                            if ($originalDiscountValue !== null) {
                                $discountValue = $originalDiscountValue;
                                error_log("Found original discount value in notes: {$discountValue}");
                            } else {
                                // حساب النسبة المئوية كحل بديل
                                $discountValue = ($invoice['discount_amount'] / $invoice['total_amount'] * 100);
                                error_log("Calculated discount percentage as fallback: {$discountValue}");
                            }

                            $discountType = '%';
                            $message .= "\nالخصم: " . number_format($discountValue, 2) . ' ' . $discountType;
                        } else {
                            // خصم بقيمة ثابتة
                            $discountValue = number_format($invoice['discount_amount'], 2);
                            $message .= "\nالخصم: " . $discountValue . ' ' . $currencySymbol;
                        }

                        $message .= "\nالمبلغ بعد الخصم: " . number_format($invoice['final_amount'], 2) . ' ' . $currencySymbol;

                        // تسجيل معلومات الخصم للتصحيح
                        error_log("Default template discount info for invoice #{$invoiceId}: Type={$invoice['discount_type']}, Value={$discountValue}, Amount={$invoice['discount_amount']}");
                    }

                    // إضافة اسم الفرع إلى رسالة الشكر
                    $branchName = $branch ? $branch['name'] : '';
                    if (!empty($branchName)) {
                        error_log("Added branch name to thank you message: {$branchName}");
                    } else {
                        $message .= "\n\n نشكرك على تعاملك معنا مع تحيات {$settings['company_name']}";
                        error_log("No branch name available for thank you message");
                    }
                }

                // الحصول على قيمة الخصم الأصلية للإرسال إلى العميل
                $originalDiscountValue = null;

                // تسجيل معلومات الفاتورة للتصحيح
                error_log("Invoice #{$invoiceId} details: discount_type={$invoice['discount_type']}, discount_amount={$invoice['discount_amount']}, total_amount={$invoice['total_amount']}");
                error_log("Invoice #{$invoiceId} notes: {$invoice['notes']}");

                if (isset($invoice['discount_type']) && $invoice['discount_type'] == 'percentage') {
                    // البحث عن قيمة الخصم الأصلية في حقل notes
                    if (!empty($invoice['notes'])) {
                        preg_match('/\[ORIGINAL_DISCOUNT:(\d+(\.\d+)?)\]/', $invoice['notes'], $matches);
                        error_log("Regex matches for invoice #{$invoiceId}: " . print_r($matches, true));

                        if (isset($matches[1])) {
                            $originalDiscountValue = floatval($matches[1]);
                            error_log("Found original discount value in notes for API response: {$originalDiscountValue}");
                        } else {
                            error_log("No original discount value found in notes for invoice #{$invoiceId}");

                            // البحث عن معرف العرض الترويجي في الملاحظات
                            preg_match('/\[PROMOTION_ID:(\d+)\]/', $invoice['notes'], $promotionMatches);
                            if (isset($promotionMatches[1])) {
                                $promotionId = intval($promotionMatches[1]);
                                error_log("Found promotion ID in notes: {$promotionId}");

                                // الحصول على بيانات العرض الترويجي
                                $promotionModel = new Promotion($db);
                                $promotion = $promotionModel->getPromotionById($promotionId);

                                if ($promotion && $promotion['discount_type'] === 'percentage') {
                                    $originalDiscountValue = floatval($promotion['discount_value']);
                                    error_log("Found original discount value from promotion: {$originalDiscountValue}");

                                    // تحديث ملاحظات الفاتورة لتشمل قيمة الخصم الأصلية
                                    $invoiceModel = new Invoice($db);
                                    $updatedNotes = $invoice['notes'] . "\n[ORIGINAL_DISCOUNT:{$originalDiscountValue}]";
                                    $db->prepare("UPDATE invoices SET notes = :notes WHERE id = :id");
                                    $db->bind(':notes', $updatedNotes);
                                    $db->bind(':id', $invoiceId);
                                    $db->execute();
                                    error_log("Updated invoice notes with original discount value: {$updatedNotes}");
                                }
                            }
                        }
                    } else {
                        error_log("No notes found for invoice #{$invoiceId}");
                    }
                } else {
                    error_log("Not searching for original discount value: discount_type={$invoice['discount_type']}");
                }

                // إرجاع البيانات المطلوبة
                $responseData = [
                    'status' => 'success',
                    'data' => [
                        'invoice_id' => $invoiceId,
                        'invoice_number' => $invoice['invoice_number'],
                        'customer_name' => $invoice['customer_name'],
                        'phone' => !empty($customer['phone']) ? $customer['phone'] : $invoice['customer_phone'],
                        'message' => $message,
                        'country_code' => $branch['country_code'] ?? '+20',
                        'branch_id' => $invoice['branch_id'],
                        'discount_type' => $invoice['discount_type'] ?? null,
                        'discount_amount' => $invoice['discount_amount'] ?? 0,
                        'discount_value' => $originalDiscountValue,

                        'total_amount' => $invoice['total_amount'] ?? 0,
                        'final_amount' => $invoice['final_amount'] ?? 0
                    ]
                ];

                // تسجيل البيانات المرسلة للتصحيح
                error_log("API Response data for invoice #{$invoiceId}: " . json_encode($responseData['data']));

                echo json_encode($responseData);
            } catch (Exception $e) {
                // تسجيل الخطأ
                error_log('خطأ في الحصول على بيانات الفاتورة: ' . $e->getMessage());

                echo json_encode([
                    'status' => 'error',
                    'message' => 'فشل الحصول على بيانات الفاتورة: ' . $e->getMessage()
                ]);
            }
            break;

        // إرسال إشعار بعد الطباعة
        case 'send_after_print':
            // التأكد من أن الطلب عبر POST
            if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
                throw new Exception('الطريقة غير مسموح بها', 405);
            }

            // التحقق من وجود معرف الفاتورة
            $invoiceId = isset($_POST['invoice_id']) ? intval($_POST['invoice_id']) : 0;

            if (!$invoiceId) {
                throw new Exception('معرف الفاتورة مطلوب', 400);
            }

            try {
                // تفعيل إشعارات الفواتير مؤقتًا للإرسال اليدوي
                $settingsModel = new Settings($db);

                // حفظ الإعدادات الحالية لاستعادتها لاحقًا
                $currentSettings = $settingsModel->getAllSettings();
                $originalWhatsappEnabled = isset($currentSettings['whatsapp_enabled']) ? $currentSettings['whatsapp_enabled'] : 0;
                $originalNotificationEnabled = isset($currentSettings['notification_nvoice_notification']) ? $currentSettings['notification_nvoice_notification'] : 'off';

                // تفعيل الإعدادات مؤقتًا
                $settingsModel->updateSetting('notification_nvoice_notification', 'on');
                $settingsModel->updateSetting('whatsapp_enabled', 1);

                // إرسال إشعار الفاتورة
                $result = $invoiceNotification->sendInvoiceNotification($invoiceId);

                // استعادة الإعدادات الأصلية
                $settingsModel->updateSetting('notification_nvoice_notification', $originalNotificationEnabled);
                $settingsModel->updateSetting('whatsapp_enabled', $originalWhatsappEnabled);

                if ($result) {
                    echo json_encode([
                        'status' => 'success',
                        'message' => 'تم إرسال إشعار الفاتورة بنجاح من جانب الخادم'
                    ]);
                } else {
                    // التحقق من وجود رقم هاتف للعميل
                    $invoiceModel = new Invoice($db);
                    $invoice = $invoiceModel->getInvoiceById($invoiceId);

                    if (!$invoice || empty($invoice['customer_phone'])) {
                        echo json_encode([
                            'status' => 'error',
                            'message' => 'لا يوجد رقم هاتف للعميل'
                        ]);
                    } else {
                        echo json_encode([
                            'status' => 'error',
                            'message' => 'فشل إرسال إشعار الفاتورة من جانب الخادم. تحقق من إعدادات WhatsApp وسجلات النظام للمزيد من المعلومات.'
                        ]);
                    }
                }
            } catch (Exception $e) {
                // استعادة الإعدادات الأصلية في حالة الخطأ
                if (isset($settingsModel) && isset($originalWhatsappEnabled) && isset($originalNotificationEnabled)) {
                    $settingsModel->updateSetting('notification_nvoice_notification', $originalNotificationEnabled);
                    $settingsModel->updateSetting('whatsapp_enabled', $originalWhatsappEnabled);
                }

                // تسجيل الخطأ
                error_log('خطأ في إرسال إشعار الفاتورة من جانب الخادم: ' . $e->getMessage());

                echo json_encode([
                    'status' => 'error',
                    'message' => 'فشل إرسال إشعار الفاتورة من جانب الخادم: ' . $e->getMessage()
                ]);
            }
            break;

        // إرسال إشعار للمدراء
        case 'send_to_admin':
            // التأكد من أن الطلب عبر POST
            if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
                throw new Exception('الطريقة غير مسموح بها', 405);
            }

            // التحقق من وجود معرف الفاتورة
            $invoiceId = isset($_POST['invoice_id']) ? intval($_POST['invoice_id']) : 0;

            if (!$invoiceId) {
                throw new Exception('معرف الفاتورة مطلوب', 400);
            }

            try {
                // الحصول على بيانات الفاتورة
                $invoiceModel = new Invoice($db);
                $invoice = $invoiceModel->getInvoiceById($invoiceId);

                if (!$invoice) {
                    throw new Exception('الفاتورة غير موجودة', 404);
                }

                // الحصول على إعدادات النظام
                $settingsModel = new Settings($db);

                // الحصول على قائمة المدراء
                $userModel = new User($db);
                $admins = $userModel->getUsersByRole('admin');

                if (empty($admins)) {
                    throw new Exception('لا يوجد مدراء مسجلين في النظام', 404);
                }

                // الحصول على بيانات الفرع
                $branchModel = new Branch($db);
                $branch = $branchModel->getBranchById($invoice['branch_id']);

                // إنشاء نص الرسالة للمدراء
                $message = "تنبيه: تم إنشاء فاتورة جديدة\n";
                $message .= "رقم الفاتورة: {$invoice['invoice_number']}\n";
                $message .= "العميل: {$invoice['customer_name']}\n";
                $message .= "المبلغ: " . number_format($invoice['total_amount'], 2) . " ج\n";
                $message .= "الفرع: " . ($branch ? $branch['name'] : 'غير محدد') . "\n";
                $message .= "التاريخ: " . date('d/m/Y', strtotime($invoice['invoice_date'])) . "\n";
                $message .= "الوقت: " . date('h:i A', strtotime($invoice['created_at'])) . "\n";

                // تفعيل خدمة WhatsApp مؤقتًا للإرسال للمدراء
                $whatsappService = new WhatsAppAutomation($db);

                // حفظ الإعدادات الحالية لاستعادتها لاحقًا
                $currentSettings = $settingsModel->getAllSettings();
                $originalWhatsappEnabled = isset($currentSettings['whatsapp_enabled']) ? $currentSettings['whatsapp_enabled'] : 0;

                // تفعيل الإعدادات مؤقتًا
                $settingsModel->updateSetting('whatsapp_enabled', 1);

                // عدد الرسائل التي تم إرسالها بنجاح
                $successCount = 0;
                $failedCount = 0;

                // إرسال رسالة لكل مدير
                foreach ($admins as $admin) {
                    if (!empty($admin['phone'])) {
                        try {
                            // إرسال الرسالة
                            $result = $whatsappService->sendMessage(
                                $admin['phone'],
                                $message,
                                ['country_code' => $branch['country_code'] ?? '+20']
                            );

                            if ($result) {
                                $successCount++;
                            } else {
                                $failedCount++;
                            }
                        } catch (Exception $e) {
                            $failedCount++;
                            error_log("خطأ في إرسال إشعار للمدير {$admin['username']}: " . $e->getMessage());
                        }
                    }
                }

                // استعادة الإعدادات الأصلية
                $settingsModel->updateSetting('whatsapp_enabled', $originalWhatsappEnabled);

                echo json_encode([
                    'status' => 'success',
                    'message' => "تم إرسال {$successCount} إشعار للمدراء بنجاح، وفشل إرسال {$failedCount} إشعار",
                    'data' => [
                        'total' => count($admins),
                        'success' => $successCount,
                        'failed' => $failedCount
                    ]
                ]);
            } catch (Exception $e) {
                // استعادة الإعدادات الأصلية في حالة الخطأ
                if (isset($settingsModel) && isset($originalWhatsappEnabled)) {
                    $settingsModel->updateSetting('whatsapp_enabled', $originalWhatsappEnabled);
                }

                // تسجيل الخطأ
                error_log('خطأ في إرسال إشعارات للمدراء: ' . $e->getMessage());

                echo json_encode([
                    'status' => 'error',
                    'message' => 'فشل إرسال إشعارات للمدراء: ' . $e->getMessage()
                ]);
            }
            break;

        // تسجيل إشعار مرسل من جانب العميل
        case 'log_notification':
            // التأكد من أن الطلب عبر POST
            if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
                throw new Exception('الطريقة غير مسموح بها', 405);
            }

            // التحقق من وجود معرف الفاتورة
            $invoiceId = isset($_POST['invoice_id']) ? intval($_POST['invoice_id']) : 0;
            $success = isset($_POST['success']) ? (bool)$_POST['success'] : false;

            if (!$invoiceId) {
                throw new Exception('معرف الفاتورة مطلوب', 400);
            }

            try {
                // الحصول على بيانات الفاتورة
                $invoiceModel = new Invoice($db);
                $invoice = $invoiceModel->getInvoiceById($invoiceId);

                if (!$invoice) {
                    throw new Exception('الفاتورة غير موجودة', 404);
                }

                // تسجيل الإشعار في قاعدة البيانات
                // استخدام استعلام مباشر لإضافة الإشعار
                $notificationData = [
                    'recipient_id' => $_SESSION['user_id'],
                    'recipient_type' => 'user',
                    'type' => 'system',
                    'related_id' => $invoiceId,
                    'related_type' => 'invoice',
                    'title' => 'إشعار فاتورة',
                    'message' => $success ?
                        'تم إرسال إشعار الفاتورة رقم ' . $invoice['invoice_number'] . ' بنجاح إلى العميل ' . $invoice['customer_name'] :
                        'فشل إرسال إشعار الفاتورة رقم ' . $invoice['invoice_number'] . ' إلى العميل ' . $invoice['customer_name'],
                    'is_read' => 0,
                    'is_sent' => 1,
                    'branch_id' => isset($_SESSION['user_branch_id']) ? $_SESSION['user_branch_id'] : null
                ];

                // إضافة الإشعار باستخدام استعلام مباشر
                $db->prepare("INSERT INTO notifications (type, recipient_id, recipient_type, title, message, related_id, related_type, branch_id, is_read, is_sent)
                              VALUES (:type, :recipient_id, :recipient_type, :title, :message, :related_id, :related_type, :branch_id, :is_read, :is_sent)");
                $db->bind(':type', 'system'); // نوع الإشعار (نظام)
                $db->bind(':recipient_id', $notificationData['recipient_id']); // معرف المستلم (المستخدم)
                $db->bind(':recipient_type', 'user'); // نوع المستلم (مستخدم)
                $db->bind(':title', $notificationData['title']); // عنوان الإشعار
                $db->bind(':message', $notificationData['message']); // نص الإشعار
                $db->bind(':related_id', $notificationData['related_id']); // معرف العنصر المرتبط
                $db->bind(':related_type', 'invoice'); // نوع العنصر المرتبط (فاتورة)
                $db->bind(':branch_id', $notificationData['branch_id']); // معرف الفرع
                $db->bind(':is_read', 0); // غير مقروء
                $db->bind(':is_sent', 1); // تم الإرسال

                $result = $db->execute();
                $notificationId = $db->lastInsertId();

                // تسجيل نجاح العملية
                if ($result && $notificationId) {
                    echo json_encode([
                        'status' => 'success',
                        'message' => 'تم تسجيل الإشعار بنجاح',
                        'notification_id' => $notificationId
                    ]);
                } else {
                    echo json_encode([
                        'status' => 'error',
                        'message' => 'فشل تسجيل الإشعار'
                    ]);
                }
            } catch (Exception $e) {
                // تسجيل الخطأ
                error_log('خطأ في تسجيل إشعار الفاتورة: ' . $e->getMessage());

                echo json_encode([
                    'status' => 'error',
                    'message' => 'فشل تسجيل الإشعار: ' . $e->getMessage()
                ]);
            }
            break;

        default:
            throw new Exception('الإجراء غير معروف', 400);
    }
} catch (Exception $e) {
    // إرجاع رسالة الخطأ
    http_response_code($e->getCode() >= 400 && $e->getCode() < 600 ? $e->getCode() : 500);
    echo json_encode([
        'status' => 'error',
        'message' => $e->getMessage()
    ]);

    // تسجيل الخطأ
    error_log('خطأ في API إشعارات الفواتير: ' . $e->getMessage());
}

