<?php
/**
 * فئة المستخدم
 * تتعامل مع إدارة المستخدمين والصلاحيات في نظام إدارة صالونات الحلاقة
 */

// منع الوصول المباشر للملف
if (!defined('BASEPATH')) {
    exit('لا يمكن الوصول المباشر لهذا الملف');
}

class User {
    private $db;

    /**
     * إنشاء كائن من فئة المستخدم
     * @param Database $db كائن قاعدة البيانات
     */
    public function __construct($db) {
        $this->db = $db;
    }

    // ==================== طرق تسجيل الدخول والمصادقة ====================

    /**
     * التحقق من اسم المستخدم وكلمة المرور
     * @param string $username اسم المستخدم
     * @param string $password كلمة المرور
     * @return array|false بيانات المستخدم أو false إذا فشل تسجيل الدخول
     */
    public function login($username, $password) {
        try {
            $this->db->prepare("SELECT * FROM users WHERE username = :username AND is_active = 1");
            $this->db->bind(':username', $username);
            $user = $this->db->fetch();

            if (!$user) {
                return false;
            }

            // التحقق من كلمة المرور
            if (password_verify($password, $user['password'])) {
                // تحديث وقت آخر تسجيل دخول
                $this->updateLastLogin($user['id']);

                // استرجاع صلاحيات المستخدم
                $user['permissions'] = $this->getUserPermissions($user['id']);

                return $user;
            }

            return false;
        } catch (PDOException $e) {
            error_log('خطأ أثناء تسجيل الدخول: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * تحديث وقت آخر تسجيل دخول للمستخدم
     * @param int $userId معرف المستخدم
     * @return bool نجاح أو فشل العملية
     */
    private function updateLastLogin($userId) {
        try {
            $this->db->prepare("UPDATE users SET last_login = NOW() WHERE id = :id");
            $this->db->bind(':id', $userId);
            return $this->db->execute();
        } catch (PDOException $e) {
            error_log('خطأ أثناء تحديث وقت آخر تسجيل دخول: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * تغيير كلمة مرور المستخدم
     * @param int $userId معرف المستخدم
     * @param string $newPassword كلمة المرور الجديدة
     * @return bool نجاح أو فشل العملية
     */
    public function changePassword($userId, $newPassword) {
        try {
            $hashedPassword = password_hash($newPassword, PASSWORD_DEFAULT);

            $this->db->prepare("UPDATE users SET password = :password WHERE id = :id");
            $this->db->bind(':password', $hashedPassword);
            $this->db->bind(':id', $userId);

            return $this->db->execute();
        } catch (Exception $e) {
            error_log('خطأ أثناء تغيير كلمة المرور: ' . $e->getMessage());
            throw $e;
        }
    }

    // ==================== طرق إدارة المستخدمين ====================

    /**
     * إضافة مستخدم جديد
     * @param array $userData بيانات المستخدم
     * @return int|false معرف المستخدم الجديد أو false إذا فشلت العملية
     */
    public function addUser($userData) {
        try {
            // التحقق مما إذا كانت هناك معاملة نشطة بالفعل
            $startedTransaction = false;
            if (!$this->db->inTransaction()) {
                $this->db->beginTransaction();
                $startedTransaction = true;
            }

            // التحقق من وجود المستخدم
            $this->db->prepare("SELECT id FROM users WHERE username = :username");
            $this->db->bind(':username', $userData['username']);
            if ($this->db->fetch()) {
                throw new Exception('اسم المستخدم موجود بالفعل');
            }

            // تشفير كلمة المرور
            $hashedPassword = password_hash($userData['password'], PASSWORD_DEFAULT);

            // إضافة المستخدم
            $this->db->prepare("INSERT INTO users (username, password, name, email, role, branch_id, is_active)
                                VALUES (:username, :password, :name, :email, :role, :branch_id, :is_active)");
            $this->db->bind(':username', $userData['username']);
            $this->db->bind(':password', $hashedPassword);
            $this->db->bind(':name', $userData['name']);
            $this->db->bind(':email', $userData['email'] ?? null);
            $this->db->bind(':role', $userData['role']);
            $this->db->bind(':branch_id', $userData['branch_id'] ?? null);
            $this->db->bind(':is_active', $userData['is_active'] ?? 1);

            $this->db->execute();
            $userId = $this->db->lastInsertId();

            // إضافة صلاحيات المستخدم
            $this->updateUserPermissions($userId, $userData['permissions'] ?? []);

            // إنهاء المعاملة فقط إذا كنا قد بدأناها
            if ($startedTransaction) {
                $this->db->commit();
            }
            return (int)$userId;
        } catch (Exception $e) {
            // التراجع عن المعاملة فقط إذا كنا قد بدأناها
            if ($startedTransaction && $this->db->inTransaction()) {
                $this->db->rollBack();
            }
            error_log('خطأ أثناء إضافة مستخدم: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * تحديث بيانات مستخدم
     * @param int $userId معرف المستخدم
     * @param array $userData بيانات المستخدم المحدثة
     * @return bool نجاح أو فشل العملية
     */
    public function updateUser($userId, $userData) {
        try {
            // التحقق مما إذا كانت هناك معاملة نشطة بالفعل
            $startedTransaction = false;
            if (!$this->db->inTransaction()) {
                $this->db->beginTransaction();
                $startedTransaction = true;
            }

            // التحقق من وجود المستخدم بنفس اسم المستخدم
            $this->db->prepare("SELECT id FROM users WHERE username = :username AND id != :id");
            $this->db->bind(':username', $userData['username']);
            $this->db->bind(':id', $userId);
            if ($this->db->fetch()) {
                throw new Exception('اسم المستخدم موجود بالفعل');
            }

            // تحديث بيانات المستخدم
            $sql = "UPDATE users SET
                    username = :username,
                    name = :name,
                    email = :email,
                    role = :role,
                    branch_id = :branch_id,
                    is_active = :is_active";

            // تحديث كلمة المرور فقط إذا تم تقديمها
            if (!empty($userData['password'])) {
                $sql .= ", password = :password";
            }

            $sql .= " WHERE id = :id";

            $this->db->prepare($sql);
            $this->db->bind(':username', $userData['username']);
            $this->db->bind(':name', $userData['name']);
            $this->db->bind(':email', $userData['email'] ?? null);
            $this->db->bind(':role', $userData['role']);
            $this->db->bind(':branch_id', $userData['branch_id'] ?? null);
            $this->db->bind(':is_active', $userData['is_active'] ?? 1);
            $this->db->bind(':id', $userId);

            if (!empty($userData['password'])) {
                $hashedPassword = password_hash($userData['password'], PASSWORD_DEFAULT);
                $this->db->bind(':password', $hashedPassword);
            }

            $this->db->execute();

            // تحديث صلاحيات المستخدم
            if (isset($userData['permissions'])) {
                $this->updateUserPermissions($userId, $userData['permissions']);
            }

            // إنهاء المعاملة فقط إذا كنا قد بدأناها
            if ($startedTransaction) {
                $this->db->commit();
            }
            return true;
        } catch (Exception $e) {
            // التراجع عن المعاملة فقط إذا كنا قد بدأناها
            if ($startedTransaction && $this->db->inTransaction()) {
                $this->db->rollBack();
            }
            error_log('خطأ أثناء تحديث بيانات المستخدم: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * حذف مستخدم
     * @param int $userId معرف المستخدم
     * @return bool نجاح أو فشل العملية
     */
    public function deleteUser($userId) {
        try {
            // التحقق مما إذا كانت هناك معاملة نشطة بالفعل
            $startedTransaction = false;
            if (!$this->db->inTransaction()) {
                $this->db->beginTransaction();
                $startedTransaction = true;
            }

            // حذف صلاحيات المستخدم
            $this->db->prepare("DELETE FROM user_permissions WHERE user_id = :user_id");
            $this->db->bind(':user_id', $userId);
            $this->db->execute();

            // حذف المستخدم
            $this->db->prepare("DELETE FROM users WHERE id = :id");
            $this->db->bind(':id', $userId);
            $this->db->execute();

            // إنهاء المعاملة فقط إذا كنا قد بدأناها
            if ($startedTransaction) {
                $this->db->commit();
            }
            return true;
        } catch (Exception $e) {
            // التراجع عن المعاملة فقط إذا كنا قد بدأناها
            if ($startedTransaction && $this->db->inTransaction()) {
                $this->db->rollBack();
            }
            error_log('خطأ أثناء حذف المستخدم: ' . $e->getMessage());
            throw $e;
        }
    }

    // ==================== طرق استرجاع بيانات المستخدمين ====================

    /**
     * استرجاع بيانات مستخدم بواسطة المعرف
     * @param int $userId معرف المستخدم
     * @return array|false بيانات المستخدم أو false إذا لم يتم العثور عليه
     */
    public function getUserById($userId) {
        try {
            $this->db->prepare("SELECT u.*, b.name as branch_name
                               FROM users u
                               LEFT JOIN branches b ON u.branch_id = b.id
                               WHERE u.id = :id");
            $this->db->bind(':id', $userId);
            $user = $this->db->fetch();

            if ($user) {
                // استرجاع صلاحيات المستخدم
                $user['permissions'] = $this->getUserPermissions($user['id']);
            }

            return $user;
        } catch (PDOException $e) {
            error_log('خطأ أثناء استرجاع بيانات المستخدم: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * الحصول على قائمة المستخدمين
     * @param array $filters فلاتر البحث (اختياري)
     * @return array قائمة المستخدمين
     */
    public function getUsers($filters = []) {
        try {
            $sql = "SELECT u.*, b.name as branch_name
                    FROM users u
                    LEFT JOIN branches b ON u.branch_id = b.id";

            $whereConditions = [];
            $bindings = [];

            // تطبيق الفلاتر
            if (!empty($filters['search'])) {
                $whereConditions[] = "(u.username LIKE :search OR u.name LIKE :search OR u.email LIKE :search)";
                $bindings[':search'] = '%' . $filters['search'] . '%';
            }

            if (!empty($filters['role'])) {
                $whereConditions[] = "u.role = :role";
                $bindings[':role'] = $filters['role'];
            }

            if (!empty($filters['branch_id'])) {
                $whereConditions[] = "u.branch_id = :branch_id";
                $bindings[':branch_id'] = $filters['branch_id'];
            }

            if (isset($filters['is_active'])) {
                $whereConditions[] = "u.is_active = :is_active";
                $bindings[':is_active'] = $filters['is_active'];
            }

            // إضافة شروط WHERE إذا وجدت
            if (!empty($whereConditions)) {
                $sql .= " WHERE " . implode(' AND ', $whereConditions);
            }

            // ترتيب النتائج
            $sql .= " ORDER BY u.name ASC";

            // إضافة الحد والإزاحة إذا وجدت
            if (!empty($filters['limit'])) {
                $sql .= " LIMIT :limit";
                if (!empty($filters['offset'])) {
                    $sql .= " OFFSET :offset";
                }
            }

            $this->db->prepare($sql);

            // ربط القيم
            foreach ($bindings as $param => $value) {
                $this->db->bind($param, $value);
            }

            // ربط الحد والإزاحة إذا وجدت
            if (!empty($filters['limit'])) {
                $this->db->bind(':limit', $filters['limit'], PDO::PARAM_INT);
                if (!empty($filters['offset'])) {
                    $this->db->bind(':offset', $filters['offset'], PDO::PARAM_INT);
                }
            }

            $users = $this->db->fetchAll();

            // استرجاع صلاحيات كل مستخدم
            foreach ($users as &$user) {
                $user['permissions'] = $this->getUserPermissions($user['id']);
            }

            return $users;
        } catch (Exception $e) {
            error_log('خطأ أثناء استرجاع قائمة المستخدمين: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * الحصول على قائمة المستخدمين بحسب الدور
     * @param string $role دور المستخدم (مدير، مشرف، إلخ)
     * @param bool $activeOnly المستخدمين النشطين فقط
     * @return array قائمة المستخدمين
     */
    public function getUsersByRole($role, $activeOnly = true) {
        try {
            $sql = "SELECT u.*, b.name as branch_name
                   FROM users u
                   LEFT JOIN branches b ON u.branch_id = b.id
                   WHERE u.role = :role";

            if ($activeOnly) {
                $sql .= " AND u.is_active = 1";
            }

            $sql .= " ORDER BY u.name ASC";

            $this->db->prepare($sql);
            $this->db->bind(':role', $role);

            return $this->db->fetchAll();
        } catch (Exception $e) {
            error_log('خطأ أثناء استرجاع قائمة المستخدمين بحسب الدور: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * حساب عدد المستخدمين
     * @param array $filters فلاتر البحث (اختياري)
     * @return int عدد المستخدمين
     */
    public function getUsersCount($filters = []) {
        try {
            $sql = "SELECT COUNT(*) FROM users u";

            $whereConditions = [];
            $bindings = [];

            // تطبيق الفلاتر
            if (!empty($filters['search'])) {
                $whereConditions[] = "(u.username LIKE :search OR u.name LIKE :search OR u.email LIKE :search)";
                $bindings[':search'] = '%' . $filters['search'] . '%';
            }

            if (!empty($filters['role'])) {
                $whereConditions[] = "u.role = :role";
                $bindings[':role'] = $filters['role'];
            }

            if (!empty($filters['branch_id'])) {
                $whereConditions[] = "u.branch_id = :branch_id";
                $bindings[':branch_id'] = $filters['branch_id'];
            }

            if (isset($filters['is_active'])) {
                $whereConditions[] = "u.is_active = :is_active";
                $bindings[':is_active'] = $filters['is_active'];
            }

            // إضافة شروط WHERE إذا وجدت
            if (!empty($whereConditions)) {
                $sql .= " WHERE " . implode(' AND ', $whereConditions);
            }

            $this->db->prepare($sql);

            // ربط القيم
            foreach ($bindings as $param => $value) {
                $this->db->bind($param, $value);
            }

            return $this->db->fetchColumn();
        } catch (Exception $e) {
            error_log('خطأ أثناء حساب عدد المستخدمين: ' . $e->getMessage());
            throw $e;
        }
    }

    // ==================== طرق إدارة الصلاحيات ====================

    /**
     * استرجاع صلاحيات المستخدم
     * @param int $userId معرف المستخدم
     * @return array مصفوفة بالصلاحيات
     */
    public function getUserPermissions($userId) {
        try {
            $this->db->prepare("SELECT permission FROM user_permissions WHERE user_id = :user_id");
            $this->db->bind(':user_id', $userId);
            $results = $this->db->fetchAll();

            $permissions = [];
            foreach ($results as $row) {
                $permissions[] = $row['permission'];
            }

            return $permissions;
        } catch (PDOException $e) {
            error_log('خطأ أثناء استرجاع صلاحيات المستخدم: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * تحديث صلاحيات المستخدم
     * @param int $userId معرف المستخدم
     * @param array $permissions مصفوفة بالصلاحيات
     * @return bool نجاح أو فشل العملية
     */
    public function updateUserPermissions($userId, $permissions) {
        try {
            // حذف الصلاحيات الحالية
            $this->db->prepare("DELETE FROM user_permissions WHERE user_id = :user_id");
            $this->db->bind(':user_id', $userId);
            $this->db->execute();

            // إذا لم تكن هناك صلاحيات جديدة، فلا داعي للإضافة
            if (empty($permissions)) {
                return true;
            }

            // إضافة الصلاحيات الجديدة
            foreach ($permissions as $permission) {
                $this->db->prepare("INSERT INTO user_permissions (user_id, permission) VALUES (:user_id, :permission)");
                $this->db->bind(':user_id', $userId);
                $this->db->bind(':permission', $permission);
                $this->db->execute();
            }

            return true;
        } catch (PDOException $e) {
            error_log('خطأ أثناء تحديث صلاحيات المستخدم: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * التحقق مما إذا كان المستخدم يملك صلاحية معينة
     * @param int $userId معرف المستخدم
     * @param string $permission الصلاحية المطلوبة
     * @return bool
     */
    public function hasPermission($userId, $permission) {
        try {
            // التحقق من دور المستخدم أولاً
            $this->db->prepare("SELECT role FROM users WHERE id = :id");
            $this->db->bind(':id', $userId);
            $user = $this->db->fetch();

            // المدير يملك جميع الصلاحيات
            if ($user && $user['role'] === ROLE_ADMIN) {
                return true;
            }

            // التحقق من وجود الصلاحية
            $this->db->prepare("SELECT COUNT(*) FROM user_permissions WHERE user_id = :user_id AND permission = :permission");
            $this->db->bind(':user_id', $userId);
            $this->db->bind(':permission', $permission);

            return $this->db->fetchColumn() > 0;
        } catch (Exception $e) {
            error_log('خطأ أثناء التحقق من الصلاحية: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * الحصول على قائمة بجميع الصلاحيات المتاحة في النظام
     * @return array قائمة الصلاحيات
     */
    public function getAllPermissions() {
        // قائمة بجميع الصلاحيات المتاحة في النظام
        return [
            // صلاحيات العملاء
            'customers_view' => 'عرض العملاء',
            'customers_add' => 'إضافة عميل',
            'customers_edit' => 'تعديل عميل',
            'customers_delete' => 'حذف عميل',

            // صلاحيات الموظفين
            'employees_view' => 'عرض الموظفين',
            'employees_add' => 'إضافة موظف',
            'employees_edit' => 'تعديل موظف',
            'employees_delete' => 'حذف موظف',
            'employees_salaries' => 'إدارة رواتب الموظفين',

            // صلاحيات الخدمات
            'services_view' => 'عرض الخدمات',
            'services_add' => 'إضافة خدمة',
            'services_edit' => 'تعديل خدمة',
            'services_delete' => 'حذف خدمة',

            // صلاحيات المنتجات
            'products_view' => 'عرض المنتجات',
            'products_add' => 'إضافة منتج',
            'products_edit' => 'تعديل منتج',
            'products_delete' => 'حذف منتج',

            // صلاحيات العروض والخصومات
            'promotions_view' => 'عرض العروض والخصومات',
            'promotions_create' => 'إضافة عرض',
            'promotions_edit' => 'تعديل عرض',
            'promotions_delete' => 'حذف عرض',

            // صلاحيات المخزون
            'inventory_view' => 'عرض المخزون',
            'inventory_adjust' => 'تعديل المخزون',

            // صلاحيات المواعيد
            'appointments_view' => 'عرض المواعيد',
            'appointments_add' => 'إضافة موعد',
            'appointments_edit' => 'تعديل موعد',
            'appointments_delete' => 'حذف موعد',

            // صلاحيات الفواتير
            'invoices_view' => 'عرض الفواتير',
            'invoices_create' => 'إنشاء فاتورة',
            'invoices_edit' => 'تعديل فاتورة',
            'invoices_delete' => 'حذف فاتورة',
            'invoices_print' => 'طباعة الفواتير',

            // صلاحيات المصروفات
            'expenses_view' => 'عرض المصروفات',
            'expenses_add' => 'إضافة مصروف',
            'expenses_edit' => 'تعديل مصروف',
            'expenses_delete' => 'حذف مصروف',

            // صلاحيات التقارير
            'reports_sales' => 'تقارير المبيعات',
            'reports_services' => 'تقارير الخدمات',
            'reports_employees' => 'تقارير الموظفين',
            'reports_expenses' => 'تقارير المصروفات',

            // صلاحيات الفروع
            'branches_view' => 'عرض الفروع',
            'branches_add' => 'إضافة فرع',
            'branches_edit' => 'تعديل فرع',
            'branches_delete' => 'حذف فرع',

            // صلاحيات نهاية اليوم
            'endday_manage' => 'إدارة نهاية اليوم',

            // صلاحيات المستخدمين والإعدادات
            'users_view' => 'عرض المستخدمين',
            'users_add' => 'إضافة مستخدم',
            'users_edit' => 'تعديل مستخدم',
            'users_delete' => 'حذف مستخدم',
            'settings_view' => 'عرض الإعدادات',
            'settings_edit' => 'تعديل الإعدادات'
        ];
    }

    /**
     * التحقق مما إذا كان اسم المستخدم مستخدمًا بالفعل
     * @param string $username اسم المستخدم
     * @param int|null $excludeUserId معرف المستخدم المستثنى (اختياري، للتعديل)
     * @return bool
     */
    public function isUsernameTaken($username, $excludeUserId = null) {
        try {
            if ($excludeUserId) {
                // استثناء المستخدم الحالي عند التعديل
                $this->db->prepare("SELECT COUNT(*) FROM users WHERE username = :username AND id != :exclude_id");
                $this->db->bind(':username', $username);
                $this->db->bind(':exclude_id', $excludeUserId);
            } else {
                // التحقق عند الإضافة
                $this->db->prepare("SELECT COUNT(*) FROM users WHERE username = :username");
                $this->db->bind(':username', $username);
            }

            return $this->db->fetchColumn() > 0;
        } catch (Exception $e) {
            error_log('خطأ أثناء التحقق من اسم المستخدم: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * تحديث حالة المستخدم (تفعيل/تعطيل)
     * @param int $userId معرف المستخدم
     * @param int $isActive حالة التفعيل (1 = مفعل، 0 = معطل)
     * @return bool نجاح أو فشل العملية
     */
    public function updateUserStatus($userId, $isActive) {
        try {
            $this->db->prepare("UPDATE users SET is_active = :is_active WHERE id = :id");
            $this->db->bind(':id', $userId);
            $this->db->bind(':is_active', $isActive ? 1 : 0);

            return $this->db->execute();
        } catch (Exception $e) {
            error_log('خطأ أثناء تحديث حالة المستخدم: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * إعادة تعيين كلمة مرور المستخدم
     * @param int $userId معرف المستخدم
     * @param string $newPassword كلمة المرور الجديدة (اختياري، يتم توليدها تلقائيًا إذا لم يتم تحديدها)
     * @return string|bool كلمة المرور الجديدة في حالة النجاح، أو false في حالة الفشل
     */
    public function resetPassword($userId, $newPassword = null) {
        try {
            // توليد كلمة مرور عشوائية إذا لم يتم تحديدها
            if ($newPassword === null) {
                $newPassword = $this->generateRandomPassword();
            }

            // تشفير كلمة المرور
            $hashedPassword = password_hash($newPassword, PASSWORD_DEFAULT);

            // تحديث كلمة المرور في قاعدة البيانات
            $this->db->prepare("UPDATE users SET password = :password WHERE id = :id");
            $this->db->bind(':password', $hashedPassword);
            $this->db->bind(':id', $userId);

            if ($this->db->execute()) {
                return $newPassword; // إرجاع كلمة المرور الجديدة غير المشفرة
            }

            return false;
        } catch (Exception $e) {
            error_log('خطأ أثناء إعادة تعيين كلمة المرور: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * توليد كلمة مرور عشوائية
     * @param int $length طول كلمة المرور (افتراضي 8 أحرف)
     * @return string كلمة المرور العشوائية
     */
    private function generateRandomPassword($length = 8) {
        // الأحرف المسموح بها في كلمة المرور
        $chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*()_-=+;:,.?';
        $password = '';

        // توليد كلمة مرور عشوائية
        for ($i = 0; $i < $length; $i++) {
            $password .= $chars[random_int(0, strlen($chars) - 1)];
        }

        return $password;
    }

    /**
     * تحديث وقت خروج المستخدم من النظام
     * @param int $userId معرف المستخدم
     * @return bool نجاح أو فشل العملية
     */
    public function updateLogoutTime($userId) {
        try {
            $this->db->prepare("UPDATE users SET last_logout = NOW() WHERE id = :id");
            $this->db->bind(':id', $userId);
            return $this->db->execute();
        } catch (PDOException $e) {
            error_log('خطأ أثناء تحديث وقت الخروج: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * تحديث وقت تسجيل دخول المستخدم إلى النظام
     * @param int $userId معرف المستخدم
     * @return bool نجاح أو فشل العملية
     */
    public function updateLoginTime($userId) {
        try {
            $this->db->prepare("UPDATE users SET last_login = NOW() WHERE id = :id");
            $this->db->bind(':id', $userId);
            return $this->db->execute();
        } catch (PDOException $e) {
            error_log('خطأ أثناء تحديث وقت تسجيل الدخول: ' . $e->getMessage());
            return false;
        }
    }
}
