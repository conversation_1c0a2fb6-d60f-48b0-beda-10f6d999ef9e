/**
 * أنماط CSS للنسخة التجريبية
 * تحتوي على التصاميم الخاصة بالمؤشرات البصرية والتحذيرات
 */

/* شريط تحذير النسخة التجريبية */
.demo-warning-bar {
    background: linear-gradient(45deg, #ff6b6b, #ffa500);
    color: white;
    padding: 10px 20px;
    text-align: center;
    font-weight: bold;
    font-size: 14px;
    position: sticky;
    top: 0;
    z-index: 1000;
    box-shadow: 0 2px 5px rgba(0,0,0,0.2);
    animation: pulse 2s infinite;
}

.demo-warning-bar i {
    margin-right: 8px;
    animation: bounce 1s infinite;
}

/* تحذير النسخة التجريبية في الصفحات */
.demo-warning {
    background-color: #fff3cd !important;
    border: 2px solid #ffeaa7 !important;
    border-radius: 8px !important;
    color: #856404 !important;
    padding: 15px 20px !important;
    margin: 15px 0 !important;
    font-weight: 600;
    box-shadow: 0 2px 10px rgba(255, 193, 7, 0.3);
    position: relative;
    overflow: hidden;
}

.demo-warning::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
    animation: shimmer 3s infinite;
}

.demo-warning i {
    color: #ff6b6b;
    margin-right: 10px;
    font-size: 18px;
}

/* علامة مائية للنسخة التجريبية */
.demo-watermark {
    position: fixed !important;
    top: 50% !important;
    left: 50% !important;
    transform: translate(-50%, -50%) rotate(-45deg) !important;
    font-size: 72px !important;
    color: rgba(255, 0, 0, 0.08) !important;
    z-index: -1 !important;
    pointer-events: none !important;
    font-weight: bold !important;
    font-family: 'Arial', sans-serif !important;
    user-select: none !important;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
}

/* شارة النسخة التجريبية في الزاوية */
.demo-badge {
    position: fixed;
    top: 20px;
    right: 20px;
    background: linear-gradient(45deg, #ff6b6b, #ff4757);
    color: white;
    padding: 8px 15px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: bold;
    z-index: 1001;
    box-shadow: 0 3px 10px rgba(255, 71, 87, 0.4);
    animation: float 3s ease-in-out infinite;
}

/* تأثيرات الحركة */
@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.8; }
    100% { opacity: 1; }
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
    40% { transform: translateY(-5px); }
    60% { transform: translateY(-3px); }
}

@keyframes shimmer {
    0% { left: -100%; }
    100% { left: 100%; }
}

@keyframes float {
    0% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
    100% { transform: translateY(0px); }
}

/* تنسيق خاص للأزرار في النسخة التجريبية */
.demo-mode .btn-danger {
    background: linear-gradient(45deg, #ff6b6b, #ff4757) !important;
    border: none !important;
    position: relative;
    overflow: hidden;
}

.demo-mode .btn-danger::before {
    content: 'تجريبي';
    position: absolute;
    top: -2px;
    right: -2px;
    background: #ff4757;
    color: white;
    font-size: 8px;
    padding: 2px 4px;
    border-radius: 0 3px 0 8px;
}

/* تنسيق الجداول في النسخة التجريبية */
.demo-mode .table {
    position: relative;
}

.demo-mode .table::after {
    content: 'بيانات تجريبية';
    position: absolute;
    top: 10px;
    right: 10px;
    background: rgba(255, 107, 107, 0.9);
    color: white;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 10px;
    font-weight: bold;
    z-index: 10;
}

/* تنسيق النماذج في النسخة التجريبية */
.demo-mode .form-control {
    border-left: 3px solid #ffa500;
}

.demo-mode .form-control:focus {
    border-left-color: #ff6b6b;
    box-shadow: 0 0 0 0.2rem rgba(255, 107, 107, 0.25);
}

/* تنسيق البطاقات في النسخة التجريبية */
.demo-mode .card {
    border-top: 3px solid #ffa500;
    position: relative;
}

.demo-mode .card::before {
    content: 'DEMO';
    position: absolute;
    top: -1px;
    left: 15px;
    background: #ffa500;
    color: white;
    padding: 2px 8px;
    font-size: 10px;
    font-weight: bold;
    border-radius: 0 0 8px 8px;
}

/* تنسيق الإحصائيات في النسخة التجريبية */
.demo-mode .stat-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    position: relative;
    overflow: hidden;
}

.demo-mode .stat-card::after {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 200%;
    height: 200%;
    background: repeating-linear-gradient(
        45deg,
        transparent,
        transparent 10px,
        rgba(255,255,255,0.1) 10px,
        rgba(255,255,255,0.1) 20px
    );
    animation: move-stripes 20s linear infinite;
}

@keyframes move-stripes {
    0% { transform: translateX(-50px); }
    100% { transform: translateX(50px); }
}

/* تنسيق القوائم المنسدلة في النسخة التجريبية */
.demo-mode .dropdown-menu {
    border: 2px solid #ffa500;
}

.demo-mode .dropdown-item:hover {
    background-color: rgba(255, 165, 0, 0.1);
}

/* تنسيق التنبيهات في النسخة التجريبية */
.demo-mode .alert {
    border-left: 4px solid #ffa500;
    position: relative;
}

.demo-mode .alert::before {
    content: '⚠️';
    position: absolute;
    top: 5px;
    right: 10px;
    font-size: 16px;
}

/* تنسيق الأيقونات في النسخة التجريبية */
.demo-mode .fa, .demo-mode .fas, .demo-mode .far {
    color: #ff6b6b;
}

/* تنسيق النصوص في النسخة التجريبية */
.demo-mode h1, .demo-mode h2, .demo-mode h3 {
    color: #2c3e50;
    position: relative;
}

.demo-mode h1::after, .demo-mode h2::after, .demo-mode h3::after {
    content: ' (تجريبي)';
    color: #ff6b6b;
    font-size: 0.6em;
    font-weight: normal;
}

/* تنسيق الروابط في النسخة التجريبية */
.demo-mode a {
    color: #ff6b6b;
    text-decoration: none;
    position: relative;
}

.demo-mode a:hover {
    color: #ff4757;
    text-decoration: underline;
}

/* تنسيق الأزرار الأساسية في النسخة التجريبية */
.demo-mode .btn-primary {
    background: linear-gradient(45deg, #667eea, #764ba2);
    border: none;
    position: relative;
}

.demo-mode .btn-success {
    background: linear-gradient(45deg, #56ab2f, #a8e6cf);
    border: none;
}

/* تنسيق شريط التنقل في النسخة التجريبية */
.demo-mode .navbar {
    border-bottom: 3px solid #ffa500;
    position: relative;
}

.demo-mode .navbar::after {
    content: 'نسخة تجريبية - البيانات وهمية';
    position: absolute;
    bottom: -20px;
    left: 50%;
    transform: translateX(-50%);
    background: #ff6b6b;
    color: white;
    padding: 2px 10px;
    border-radius: 10px;
    font-size: 10px;
    white-space: nowrap;
}

/* تنسيق الجانب الجانبي في النسخة التجريبية */
.demo-mode .sidebar {
    border-right: 3px solid #ffa500;
}

/* تنسيق التذييل في النسخة التجريبية */
.demo-mode .footer {
    background: linear-gradient(45deg, #2c3e50, #34495e);
    color: white;
    text-align: center;
    padding: 20px;
    position: relative;
}

.demo-mode .footer::before {
    content: 'هذا النظام في وضع العرض التجريبي - جميع البيانات وهمية';
    display: block;
    background: #e74c3c;
    color: white;
    padding: 5px;
    margin-bottom: 10px;
    border-radius: 5px;
    font-weight: bold;
}

/* تأثيرات خاصة للنسخة التجريبية */
.demo-mode {
    position: relative;
}

.demo-mode::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: 
        repeating-linear-gradient(
            45deg,
            transparent,
            transparent 100px,
            rgba(255, 107, 107, 0.02) 100px,
            rgba(255, 107, 107, 0.02) 200px
        );
    pointer-events: none;
    z-index: -2;
}

/* تنسيق خاص للشاشات الصغيرة */
@media (max-width: 768px) {
    .demo-watermark {
        font-size: 48px !important;
    }
    
    .demo-badge {
        top: 10px;
        right: 10px;
        font-size: 10px;
        padding: 6px 12px;
    }
    
    .demo-warning-bar {
        font-size: 12px;
        padding: 8px 15px;
    }
}
