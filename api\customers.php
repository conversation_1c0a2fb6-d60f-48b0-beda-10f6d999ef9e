<?php

/**

 * واجهة برمجة التطبيقات (API) لإدارة العملاء

 */



// تحديد المسار الأساسي

define('BASEPATH', true);



// استدعاء ملف التهيئة

require_once __DIR__ . '/../config/init.php';



// التأكد من استخدام طلب AJAX فقط

if (!isAjaxRequest()) {

    header('HTTP/1.0 403 Forbidden');

    exit('غير مسموح بالوصول المباشر');

}



// التحقق من تسجيل الدخول

if (!isLoggedIn()) {

    echo json_encode([

        'status' => 'error',

        'message' => 'يجب تسجيل الدخول أولاً'

    ]);

    exit;

}



// الحصول على الإجراء المطلوب

$action = isset($_POST['action']) ? $_POST['action'] : (isset($_GET['action']) ? $_GET['action'] : '');



// التحقق من وجود إجراء

if (empty($action)) {

    echo json_encode([

        'status' => 'error',

        'message' => 'لم يتم تحديد الإجراء المطلوب'

    ]);

    exit;

}



// إنشاء كائن العميل

$customerModel = new Customer($db);



// معالجة الطلب بناءً على الإجراء

try {

    switch ($action) {

        case 'get_customers':

            // التحقق من صلاحية العرض

            if (!hasPermission('customers_view')) {

                throw new Exception('ليس لديك صلاحية لعرض العملاء');

            }



            // تسجيل كل البيانات المرسلة للتصحيح

            error_log('All POST data in get_customers: ' . json_encode($_POST, JSON_UNESCAPED_UNICODE));



            // تحديد الفلاتر الأساسية

            $filters = [];



            // إذا كان المستخدم ليس مديرًا، يتم تقييد العرض بالفرع الخاص به

            if ($_SESSION['user_role'] !== ROLE_ADMIN) {

                // للمشرف أو أي مستخدم آخر، عرض عملاء فرعه فقط

                $filters['branch_id'] = $_SESSION['user_branch_id'];

                error_log('Non-admin user, branch_id filter applied: ' . $_SESSION['user_branch_id']);

            } else if (isset($_POST['branch_id']) && !empty($_POST['branch_id'])) {

                // إذا كان مديرًا وتم تحديد فرع، يتم تطبيق الفلتر

                $filters['branch_id'] = intval($_POST['branch_id']);

                error_log('Admin user with branch filter, branch_id filter applied: ' . $_POST['branch_id']);

            } else {

                error_log('Admin user without branch filter, no branch_id filter applied');

            }



            if (isset($_POST['search']) && !empty($_POST['search'])) {

                $filters['search'] = $_POST['search'];

                error_log('Search filter applied: ' . $_POST['search']);

            } else {

                error_log('No search filter applied');

            }



            // إضافة الفلاتر المتقدمة

            // فلتر تاريخ التسجيل

            if (isset($_POST['date_from']) && !empty($_POST['date_from']) && $_POST['date_from'] !== 'null') {

                try {

                    // محاولة تنسيق التاريخ

                    $dateFrom = trim($_POST['date_from']);

                    error_log('Date from before formatting: "' . $dateFrom . '"');



                    // التحقق من أن التاريخ ليس فارغًا

                    if ($dateFrom === '') {

                        error_log('Date from is empty string, skipping filter');

                    } else {

                        // استخدام التاريخ كما هو بدون تنسيق

                        $filters['date_from'] = $dateFrom;

                        error_log('Using date_from as is: "' . $filters['date_from'] . '"');

                    }

                } catch (Exception $e) {

                    error_log('Error handling date_from: ' . $e->getMessage());

                    // استخدم التاريخ كما هو في حالة الخطأ

                    $filters['date_from'] = $_POST['date_from'];

                    error_log('Using original date_from due to error: "' . $filters['date_from'] . '"');

                }

            } else {

                error_log('No date_from filter applied. POST value: ' . (isset($_POST['date_from']) ? '"' . $_POST['date_from'] . '"' : 'not set'));

            }



            if (isset($_POST['date_to']) && !empty($_POST['date_to']) && $_POST['date_to'] !== 'null') {

                try {

                    // محاولة تنسيق التاريخ

                    $dateTo = trim($_POST['date_to']);

                    error_log('Date to before formatting: "' . $dateTo . '"');



                    // التحقق من أن التاريخ ليس فارغًا

                    if ($dateTo === '') {

                        error_log('Date to is empty string, skipping filter');

                    } else {

                        // استخدام التاريخ كما هو بدون تنسيق

                        $filters['date_to'] = $dateTo;

                        error_log('Using date_to as is: "' . $filters['date_to'] . '"');

                    }

                } catch (Exception $e) {

                    error_log('Error handling date_to: ' . $e->getMessage());

                    // استخدم التاريخ كما هو في حالة الخطأ

                    $filters['date_to'] = $_POST['date_to'];

                    error_log('Using original date_to due to error: "' . $filters['date_to'] . '"');

                }

            } else {

                error_log('No date_to filter applied. POST value: ' . (isset($_POST['date_to']) ? '"' . $_POST['date_to'] . '"' : 'not set'));

            }



            // فلتر نقاط الولاء

            if (isset($_POST['loyalty_points_min']) && $_POST['loyalty_points_min'] !== '') {

                $filters['loyalty_points_min'] = intval($_POST['loyalty_points_min']);

                error_log('Loyalty points min filter applied: ' . $_POST['loyalty_points_min']);

            } else {

                error_log('No loyalty points min filter applied');

            }



            if (isset($_POST['loyalty_points_max']) && $_POST['loyalty_points_max'] !== '') {

                $filters['loyalty_points_max'] = intval($_POST['loyalty_points_max']);

                error_log('Loyalty points max filter applied: ' . $_POST['loyalty_points_max']);

            } else {

                error_log('No loyalty points max filter applied');

            }



            // فلتر عدد الزيارات

            if (isset($_POST['visits_min']) && $_POST['visits_min'] !== '') {

                $filters['visits_min'] = intval($_POST['visits_min']);

                error_log('Visits min filter applied: ' . $_POST['visits_min']);

            } else {

                error_log('No visits min filter applied');

            }



            if (isset($_POST['visits_max']) && $_POST['visits_max'] !== '') {

                $filters['visits_max'] = intval($_POST['visits_max']);

                error_log('Visits max filter applied: ' . $_POST['visits_max']);

            } else {

                error_log('No visits max filter applied');

            }



            // فلتر الحد والإزاحة

            if (isset($_POST['limit']) && $_POST['limit'] !== '') {

                $filters['limit'] = intval($_POST['limit']);

                error_log('Limit filter applied: ' . $_POST['limit']);

            } else {

                error_log('No limit filter applied');

            }



            if (isset($_POST['offset']) && $_POST['offset'] !== '') {

                $filters['offset'] = intval($_POST['offset']);

                error_log('Offset filter applied: ' . $_POST['offset']);

            } else {

                error_log('No offset filter applied');

            }



            // تسجيل الفلاتر للتصحيح

            error_log('Filters applied: ' . json_encode($filters, JSON_UNESCAPED_UNICODE));



            // تسجيل البيانات المرسلة من النموذج

            error_log('POST data: ' . json_encode($_POST, JSON_UNESCAPED_UNICODE));



            // إذا كان هناك طلب للتصحيح

            if (isset($_POST['_debug']) && $_POST['_debug']) {

                error_log('Debug mode enabled for customer filters');



                // تسجيل المزيد من المعلومات

                error_log('SERVER variables: ' . json_encode($_SERVER, JSON_UNESCAPED_UNICODE));

                error_log('GET variables: ' . json_encode($_GET, JSON_UNESCAPED_UNICODE));

                error_log('REQUEST variables: ' . json_encode($_REQUEST, JSON_UNESCAPED_UNICODE));

            }



            // استرجاع العملاء وعددهم

            $customers = $customerModel->getCustomers($filters);

            $totalCustomers = $customerModel->getCustomersCount($filters);



            // تسجيل نتائج الاستعلام

            error_log('Query results: ' . count($customers) . ' customers found out of ' . $totalCustomers . ' total');



            // إذا كان هناك طلب للتصحيح ولم يتم العثور على نتائج

            if (isset($_POST['_debug']) && $_POST['_debug'] && count($customers) === 0) {

                error_log('No customers found with the applied filters. Trying to diagnose the issue...');



                // محاولة استرجاع العملاء بدون فلاتر

                $allCustomers = $customerModel->getCustomers([]);

                error_log('Total customers without filters: ' . count($allCustomers));



                // التحقق من كل فلتر على حدة

                if (!empty($filters['search'])) {

                    $searchResults = $customerModel->getCustomers(['search' => $filters['search']]);

                    error_log('Search filter only: ' . count($searchResults) . ' results');

                }



                if (!empty($filters['branch_id'])) {

                    $branchResults = $customerModel->getCustomers(['branch_id' => $filters['branch_id']]);

                    error_log('Branch filter only: ' . count($branchResults) . ' results');

                }



                if (!empty($filters['date_from']) || !empty($filters['date_to'])) {

                    $dateFilters = [];

                    if (!empty($filters['date_from'])) $dateFilters['date_from'] = $filters['date_from'];

                    if (!empty($filters['date_to'])) $dateFilters['date_to'] = $filters['date_to'];

                    $dateResults = $customerModel->getCustomers($dateFilters);

                    error_log('Date filters only: ' . count($dateResults) . ' results');

                }



                if (!empty($filters['loyalty_points_min']) || !empty($filters['loyalty_points_max'])) {

                    $loyaltyFilters = [];

                    if (!empty($filters['loyalty_points_min'])) $loyaltyFilters['loyalty_points_min'] = $filters['loyalty_points_min'];

                    if (!empty($filters['loyalty_points_max'])) $loyaltyFilters['loyalty_points_max'] = $filters['loyalty_points_max'];

                    $loyaltyResults = $customerModel->getCustomers($loyaltyFilters);

                    error_log('Loyalty points filters only: ' . count($loyaltyResults) . ' results');

                }



                if (!empty($filters['visits_min']) || !empty($filters['visits_max'])) {

                    $visitsFilters = [];

                    if (!empty($filters['visits_min'])) $visitsFilters['visits_min'] = $filters['visits_min'];

                    if (!empty($filters['visits_max'])) $visitsFilters['visits_max'] = $filters['visits_max'];

                    $visitsResults = $customerModel->getCustomers($visitsFilters);

                    error_log('Visits filters only: ' . count($visitsResults) . ' results');

                }

            }



            // إعداد الاستجابة

            $response = [

                'status' => 'success',

                'customers' => $customers,

                'total' => $totalCustomers,

                'filtered' => count($customers)

            ];



            // إضافة معلومات التصحيح إذا كان مطلوبًا

            if (isset($_POST['_debug']) && $_POST['_debug']) {

                $response['debug'] = [

                    'filters' => $filters,

                    'timestamp' => date('Y-m-d H:i:s'),

                    'execution_time' => microtime(true) - $_SERVER['REQUEST_TIME_FLOAT']

                ];

            }



            // إرسال الاستجابة

            echo json_encode($response);

            break;



        case 'get_customer':

            // التحقق من صلاحية العرض

            if (!hasPermission('customers_view')) {

                throw new Exception('ليس لديك صلاحية لعرض العملاء');

            }



            if (!isset($_POST['id'])) {

                throw new Exception('لم يتم تحديد العميل');

            }



            $customerId = intval($_POST['id']);

            $customer = $customerModel->getCustomerById($customerId);



            if (!$customer) {

                throw new Exception('لم يتم العثور على العميل');

            }



            // التحقق من أن العميل ينتمي لفرع المستخدم إذا لم يكن مديرًا

            if ($_SESSION['user_role'] !== ROLE_ADMIN && $customer['branch_id'] != $_SESSION['user_branch_id']) {

                throw new Exception('ليس لديك صلاحية لعرض هذا العميل');

            }



            // استرجاع زيارات العميل إذا كان مطلوبًا

            if (isset($_POST['with_visits']) && $_POST['with_visits']) {

                $limit = isset($_POST['visits_limit']) ? intval($_POST['visits_limit']) : 10;

                $customer['visits'] = $customerModel->getCustomerVisits($customerId, $limit);

            }



            echo json_encode([

                'status' => 'success',

                'customer' => $customer

            ]);

            break;



        case 'search_customer':

            // التحقق من صلاحية العرض

            if (!hasPermission('customers_view')) {

                throw new Exception('ليس لديك صلاحية لعرض العملاء');

            }



            if (!isset($_POST['search_term'])) {

                throw new Exception('لم يتم تحديد معيار البحث');

            }



            $searchTerm = $_POST['search_term'];



            // البحث بواسطة رقم الهاتف أوالاسم

            $filters = ['search' => $searchTerm];



            // إذا كان المستخدم ليس مديرًا، يتم تقييد البحث بالفرع الخاص به

            if ($_SESSION['user_role'] !== ROLE_ADMIN) {

                $filters['branch_id'] = $_SESSION['user_branch_id'];

            } else if (isset($_POST['branch_id']) && !empty($_POST['branch_id'])) {

                $filters['branch_id'] = intval($_POST['branch_id']);

            }



            // تحديد عدد النتائج

            $filters['limit'] = isset($_POST['limit']) ? intval($_POST['limit']) : 10;



            $customers = $customerModel->getCustomers($filters);



            echo json_encode([

                'status' => 'success',

                'customers' => $customers,

                'count' => count($customers)

            ]);

            break;



        case 'add_customer':

            // التحقق من صلاحية الإضافة

            if (!hasPermission('customers_add')) {

                throw new Exception('ليس لديك صلاحية لإضافة عملاء');

            }



            // التحقق من البيانات المطلوبة

            $requiredFields = ['name', 'phone'];

            foreach ($requiredFields as $field) {

                if (!isset($_POST[$field]) || empty($_POST[$field])) {

                    throw new Exception('يرجى إدخال جميع البيانات المطلوبة');

                }

            }



            // جمع بيانات العميل

            $customerData = [

                'name' => sanitizeInput($_POST['name']),

                'phone' => sanitizeInput($_POST['phone']),

                'email' => isset($_POST['email']) ? sanitizeInput($_POST['email']) : null,

                'address' => isset($_POST['address']) ? sanitizeInput($_POST['address']) : null,

                'birthday' => isset($_POST['birthday']) ? sanitizeInput($_POST['birthday']) : null,

                'notes' => isset($_POST['notes']) ? sanitizeInput($_POST['notes']) : null,

                'branch_id' => isset($_POST['branch_id']) && $_SESSION['user_role'] === ROLE_ADMIN ?

                    intval($_POST['branch_id']) : $_SESSION['user_branch_id']

            ];



            // تنسيق تاريخ الميلاد إذا كان موجودًا

            if (!empty($customerData['birthday']) && strpos($customerData['birthday'], '/') !== false) {

                $customerData['birthday'] = formatDateToDb($customerData['birthday']);

            }



            // إضافة العميل

            $customerId = $customerModel->addCustomer($customerData);



            echo json_encode([

                'status' => 'success',

                'message' => 'تم إضافة العميل بنجاح',

                'customer_id' => $customerId

            ]);

            break;



        case 'update_customer':

            // التحقق من صلاحية التعديل

            if (!hasPermission('customers_edit')) {

                throw new Exception('ليس لديك صلاحية لتعديل العملاء');

            }



            // التحقق من البيانات المطلوبة

            if (!isset($_POST['customer_id']) || empty($_POST['customer_id'])) {

                throw new Exception('لم يتم تحديد العميل');

            }



            $requiredFields = ['name', 'phone'];

            foreach ($requiredFields as $field) {

                if (!isset($_POST[$field]) || empty($_POST[$field])) {

                    throw new Exception('يرجى إدخال جميع البيانات المطلوبة');

                }

            }



            $customerId = intval($_POST['customer_id']);



            // التحقق من أن العميل ينتمي لفرع المستخدم إذا لم يكن مديرًا

            $customer = $customerModel->getCustomerById($customerId);

            if (!$customer) {

                throw new Exception('لم يتم العثور على العميل');

            }



            if ($_SESSION['user_role'] !== ROLE_ADMIN && $customer['branch_id'] != $_SESSION['user_branch_id']) {

                throw new Exception('ليس لديك صلاحية لتعديل هذا العميل');

            }



            // جمع بيانات العميل

            $customerData = [

                'name' => sanitizeInput($_POST['name']),

                'phone' => sanitizeInput($_POST['phone']),

                'email' => isset($_POST['email']) ? sanitizeInput($_POST['email']) : null,

                'address' => isset($_POST['address']) ? sanitizeInput($_POST['address']) : null,

                'birthday' => isset($_POST['birthday']) ? sanitizeInput($_POST['birthday']) : null,

                'notes' => isset($_POST['notes']) ? sanitizeInput($_POST['notes']) : null,

                'branch_id' => $_SESSION['user_role'] === ROLE_ADMIN && isset($_POST['branch_id']) ?

                    intval($_POST['branch_id']) : $customer['branch_id']

            ];



            // تنسيق تاريخ الميلاد إذا كان موجودًا

            if (!empty($customerData['birthday']) && strpos($customerData['birthday'], '/') !== false) {

                $customerData['birthday'] = formatDateToDb($customerData['birthday']);

            }



            // تحديث العميل

            $customerModel->updateCustomer($customerId, $customerData);



            echo json_encode([

                'status' => 'success',

                'message' => 'تم تحديث بيانات العميل بنجاح'

            ]);

            break;



        case 'delete_customer':

            // التحقق من صلاحية الحذف

            if (!hasPermission('customers_delete')) {

                throw new Exception('ليس لديك صلاحية لحذف العملاء');

            }



            // التحقق من البيانات المطلوبة

            if (!isset($_POST['customer_id']) || empty($_POST['customer_id'])) {

                throw new Exception('لم يتم تحديد العميل');

            }



            $customerId = intval($_POST['customer_id']);



            // حذف العميل

            $customerModel->deleteCustomer($customerId);



            echo json_encode([

                'status' => 'success',

                'message' => 'تم حذف العميل بنجاح'

            ]);

            break;



        case 'add_visit':

            // التحقق من صلاحية التعديل

            if (!hasPermission('customers_edit')) {

                throw new Exception('ليس لديك صلاحية لتسجيل زيارات العملاء');

            }



            // التحقق من البيانات المطلوبة

            if (!isset($_POST['customer_id']) || empty($_POST['customer_id'])) {

                throw new Exception('لم يتم تحديد العميل');

            }



            $customerId = intval($_POST['customer_id']);



            // جمع بيانات الزيارة

            $visitData = [

                'notes' => isset($_POST['notes']) ? sanitizeInput($_POST['notes']) : null,

                'branch_id' => isset($_POST['branch_id']) ? intval($_POST['branch_id']) : $_SESSION['user_branch_id'] ?? null

            ];



            // إضافة الزيارة

            $visitId = $customerModel->addCustomerVisit($customerId, $visitData);



            echo json_encode([

                'status' => 'success',

                'message' => 'تم تسجيل زيارة العميل بنجاح',

                'visit_id' => $visitId

            ]);

            break;



        case 'get_customer_visits':

            // التحقق من صلاحية العرض

            if (!hasPermission('customers_view')) {

                throw new Exception('ليس لديك صلاحية لعرض زيارات العملاء');

            }



            // التحقق من البيانات المطلوبة

            if (!isset($_POST['customer_id']) || empty($_POST['customer_id'])) {

                throw new Exception('لم يتم تحديد العميل');

            }



            $customerId = intval($_POST['customer_id']);

            $startDate = isset($_POST['date_from']) ? sanitizeInput($_POST['date_from']) : null;

            $endDate = isset($_POST['date_to']) ? sanitizeInput($_POST['date_to']) : null;

            $limit = isset($_POST['limit']) ? intval($_POST['limit']) : 50;



            // استرجاع زيارات العميل

            $visits = $customerModel->getCustomerVisits($customerId, $startDate, $endDate, $limit);



            echo json_encode([

                'status' => 'success',

                'visits' => $visits,

                'count' => count($visits)

            ]);

            break;



        case 'add_loyalty_points':

            // التحقق من صلاحية التعديل

            if (!hasPermission('customers_edit')) {

                throw new Exception('ليس لديك صلاحية لإضافة نقاط ولاء للعملاء');

            }



            // التحقق من البيانات المطلوبة

            if (!isset($_POST['customer_id']) || empty($_POST['customer_id'])) {

                throw new Exception('لم يتم تحديد العميل');

            }



            if (!isset($_POST['points']) || !is_numeric($_POST['points'])) {

                throw new Exception('يرجى إدخال عدد النقاط بشكل صحيح');

            }



            $customerId = intval($_POST['customer_id']);

            $points = intval($_POST['points']);



            if ($points <= 0) {

                throw new Exception('يجب أن يكون عدد النقاط أكبر من صفر');

            }



            // إضافة نقاط الولاء

            $customerModel->addLoyaltyPoints($customerId, $points);



            echo json_encode([

                'status' => 'success',

                'message' => "تم إضافة {$points} نقطة ولاء للعميل بنجاح"

            ]);

            break;



        case 'use_loyalty_points':

            // التحقق من صلاحية التعديل

            if (!hasPermission('customers_edit')) {

                throw new Exception('ليس لديك صلاحية لاستخدام نقاط ولاء العملاء');

            }



            // التحقق من البيانات المطلوبة

            if (!isset($_POST['customer_id']) || empty($_POST['customer_id'])) {

                throw new Exception('لم يتم تحديد العميل');

            }



            if (!isset($_POST['points']) || !is_numeric($_POST['points'])) {

                throw new Exception('يرجى إدخال عدد النقاط بشكل صحيح');

            }



            $customerId = intval($_POST['customer_id']);

            $points = intval($_POST['points']);



            if ($points <= 0) {

                throw new Exception('يجب أن يكون عدد النقاط أكبر من صفر');

            }



            // استخدام نقاط الولاء

            $customerModel->useLoyaltyPoints($customerId, $points);



            echo json_encode([

                'status' => 'success',

                'message' => "تم استخدام {$points} نقطة ولاء للعميل بنجاح"

            ]);

            break;



        case 'get_top_customers':

            // التحقق من صلاحية العرض

            if (!hasPermission('customers_view')) {

                throw new Exception('ليس لديك صلاحية لعرض العملاء');

            }



            $limit = isset($_POST['limit']) ? intval($_POST['limit']) : 5;

            $branchId = isset($_POST['branch_id']) ? intval($_POST['branch_id']) : null;



            // استرجاع أفضل العملاء

            $topCustomers = $customerModel->getTopCustomers($limit, $branchId);



            echo json_encode([

                'status' => 'success',

                'customers' => $topCustomers

            ]);

            break;



        case 'get_customer_invoices':

            // التحقق من صلاحية العرض

            if (!hasPermission('customers_view')) {

                throw new Exception('ليس لديك صلاحية لعرض فواتير العملاء');

            }



            // تسجيل الاستدعاء للتصحيح

            error_log("API get_customer_invoices - POST data: " . json_encode($_POST));



            // التحقق من البيانات المطلوبة

            if (!isset($_POST['customer_id']) || empty($_POST['customer_id'])) {

                error_log("API get_customer_invoices - Missing customer_id");

                throw new Exception('لم يتم تحديد العميل');

            }



            $customerId = intval($_POST['customer_id']);

            $startDate = isset($_POST['date_from']) ? sanitizeInput($_POST['date_from']) : null;

            $endDate = isset($_POST['date_to']) ? sanitizeInput($_POST['date_to']) : null;

            $limit = isset($_POST['limit']) ? intval($_POST['limit']) : 50;



            error_log("API get_customer_invoices - Processing request for customer $customerId");



            // استرجاع فواتير العميل

            $invoices = $customerModel->getCustomerInvoices($customerId, $startDate, $endDate, $limit);
            
            error_log("API get_customer_invoices - Returned " . count($invoices) . " invoices");
            
            // تسجيل أول 5 فواتير للتصحيح (إذا وجدت)

            if (count($invoices) > 0) {

                error_log("API get_customer_invoices - First invoice: " . json_encode(array_slice($invoices, 0, 1)));

            } else {

                // التحقق من وجود فواتير بدون فلاتر

                $allInvoices = $customerModel->getCustomerInvoices($customerId, null, null, 5);

                error_log("API get_customer_invoices - Without date filters, found " . count($allInvoices) . " invoices");

                if (count($allInvoices) > 0) {

                    error_log("API get_customer_invoices - First invoice without filters: " . json_encode(array_slice($allInvoices, 0, 1)));

                }

            }



            echo json_encode([

                'status' => 'success',

                'invoices' => $invoices,

                'count' => count($invoices),

                'debug' => [

                    'customer_id' => $customerId,

                    'date_from' => $startDate,

                    'date_to' => $endDate,

                    'limit' => $limit

                ]

            ]);

            break;



        case 'check_phone_exists':

            // التحقق من وجود رقم الهاتف

            if (!isset($_POST['phone']) || empty($_POST['phone'])) {

                throw new Exception('يرجى إدخال رقم الهاتف');

            }



            $phone = sanitizeInput($_POST['phone']);

            $customerId = isset($_POST['customer_id']) ? intval($_POST['customer_id']) : null;



            // استثناء العميل الحالي في حالة التعديل

            $customer = $customerModel->getCustomerByPhone($phone);

            $exists = $customer && (!$customerId || $customer['id'] != $customerId);



            echo json_encode([

                'status' => 'success',

                'exists' => $exists,

                'customer' => $exists ? $customer : null

            ]);

            break;



        case 'get_customer_spending_by_category':

            // التحقق من صلاحية العرض

            if (!hasPermission('customers_view')) {

                throw new Exception('ليس لديك صلاحية لعرض بيانات مشتريات العملاء');

            }



            // التحقق من البيانات المطلوبة

            if (!isset($_POST['customer_id']) || empty($_POST['customer_id'])) {

                throw new Exception('لم يتم تحديد العميل');

            }



            $customerId = intval($_POST['customer_id']);

            $startDate = isset($_POST['date_from']) ? sanitizeInput($_POST['date_from']) : null;

            $endDate = isset($_POST['date_to']) ? sanitizeInput($_POST['date_to']) : null;



            // استرجاع مشتريات العميل حسب الفئة

            $spendingByCategory = $customerModel->getCustomerSpendingByCategory($customerId, $startDate, $endDate);



            echo json_encode([

                'status' => 'success',

                'data' => $spendingByCategory

            ]);

            break;



        case 'get_customer_purchases_details':

            // التحقق من صلاحية العرض

            if (!hasPermission('customers_view')) {

                throw new Exception('ليس لديك صلاحية لعرض بيانات مشتريات العملاء');

            }



            // التحقق من البيانات المطلوبة

            if (!isset($_POST['customer_id']) || empty($_POST['customer_id'])) {

                throw new Exception('لم يتم تحديد العميل');

            }



            $customerId = intval($_POST['customer_id']);

            $startDate = isset($_POST['date_from']) ? sanitizeInput($_POST['date_from']) : null;

            $endDate = isset($_POST['date_to']) ? sanitizeInput($_POST['date_to']) : null;



            // تسجيل الاستدعاء للتصحيح

            error_log("API get_customer_purchases_details - Customer ID: $customerId, Start Date: $startDate, End Date: $endDate");



            // استرجاع مشتريات العميل مفصلة

            $sql = "SELECT 

                    i.invoice_number,

                    i.created_at as invoice_date,

                    ii.item_type as type,

                    CASE 

                        WHEN ii.item_type = 'service' THEN s.name

                        WHEN ii.item_type = 'product' THEN p.name

                        ELSE ii.item_type

                    END as item_name,

                    CASE 

                        WHEN ii.item_type = 'service' THEN COALESCE(sc.name, 'خدمات أخرى')

                        WHEN ii.item_type = 'product' THEN COALESCE(pc.name, 'منتجات أخرى')

                        ELSE ii.item_type

                    END as category_name,

                    ii.quantity,

                    ii.price,

                    ii.discount,

                    ii.total

                FROM invoices i

                JOIN invoice_items ii ON i.id = ii.invoice_id

                LEFT JOIN services s ON ii.item_type = 'service' AND ii.item_id = s.id

                LEFT JOIN products p ON ii.item_type = 'product' AND ii.item_id = p.id

                LEFT JOIN service_categories sc ON s.category_id = sc.id

                LEFT JOIN product_categories pc ON p.category_id = pc.id

                WHERE i.customer_id = :customer_id";
            
            $bindings = [':customer_id' => $customerId];
            
            // إضافة فلتر التاريخ إذا تم تحديده

            if ($startDate && $endDate) {

                $sql .= " AND i.created_at BETWEEN :start_date AND :end_date";

                $bindings[':start_date'] = $startDate . ' 00:00:00';

                $bindings[':end_date'] = $endDate . ' 23:59:59';

                error_log("Filtering purchases by date range: {$startDate} to {$endDate}");

            }
            
            $sql .= " ORDER BY i.created_at DESC, ii.item_type";
            
            try {
                $db->prepare($sql);
                foreach ($bindings as $key => $value) {
                    $db->bind($key, $value);
                }
                
                $results = $db->fetchAll();
                error_log("get_customer_purchases_details - Found " . count($results) . " items");
                
                echo json_encode([
                    'status' => 'success',
                    'data' => $results
                ]);
            } catch (Exception $e) {
                error_log('خطأ أثناء استرجاع تفاصيل مشتريات العميل: ' . $e->getMessage());
                error_log('Stack trace: ' . $e->getTraceAsString());
                throw $e;
            }
            break;



        case 'get_customer_monthly_spending':

            // التحقق من صلاحية العرض

            if (!hasPermission('customers_view')) {

                throw new Exception('ليس لديك صلاحية لعرض بيانات مشتريات العملاء');

            }



            // التحقق من البيانات المطلوبة

            if (!isset($_POST['customer_id']) || empty($_POST['customer_id'])) {

                throw new Exception('لم يتم تحديد العميل');

            }



            $customerId = intval($_POST['customer_id']);

            $limit = isset($_POST['limit']) ? intval($_POST['limit']) : 12;



            // استرجاع المشتريات الشهرية للعميل

            $monthlySpending = $customerModel->getCustomerMonthlySpending($customerId, $limit);



            echo json_encode([

                'status' => 'success',

                'data' => $monthlySpending

            ]);

            break;



        case 'get_customer_total_spending':

            // التحقق من صلاحية العرض

            if (!hasPermission('customers_view')) {

                throw new Exception('ليس لديك صلاحية لعرض بيانات مشتريات العملاء');

            }



            // تسجيل الاستدعاء للتصحيح

            error_log("API get_customer_total_spending - POST data: " . json_encode($_POST));



            // التحقق من البيانات المطلوبة

            if (!isset($_POST['customer_id']) || empty($_POST['customer_id'])) {

                throw new Exception('لم يتم تحديد العميل');

            }



            $customerId = intval($_POST['customer_id']);

            $startDate = isset($_POST['date_from']) && !empty($_POST['date_from']) ? sanitizeInput($_POST['date_from']) : null;

            $endDate = isset($_POST['date_to']) && !empty($_POST['date_to']) ? sanitizeInput($_POST['date_to']) : null;



            error_log("API get_customer_total_spending - Processing request: Customer ID: $customerId, Start Date: $startDate, End Date: $endDate");



            // استرجاع إجمالي مشتريات العميل للفترة المحددة

            $totalSpending = $customerModel->getCustomerTotalSpending($customerId, $startDate, $endDate);
            
            error_log("API get_customer_total_spending - Total Spending: $totalSpending");



            echo json_encode([

                'status' => 'success',

                'total_spending' => $totalSpending

            ]);

            break;



        case 'get_customer_total_discounts':

            // التحقق من الصلاحيات
            requirePermission('customers_view');
            
            // التحقق من وجود معرّف العميل
            if (!isset($_POST['customer_id']) || empty($_POST['customer_id'])) {
                echo json_encode([
                    'status' => 'error',
                    'message' => 'لم يتم تحديد العميل'
                ]);
                exit;
            }
            
            $customerId = intval($_POST['customer_id']);
            
            // تحديد الفترة الزمنية
            $dateFrom = isset($_POST['date_from']) && !empty($_POST['date_from']) ? sanitizeInput($_POST['date_from']) : null;
            $dateTo = isset($_POST['date_to']) && !empty($_POST['date_to']) ? sanitizeInput($_POST['date_to']) : null;
            
            error_log("API get_customer_total_discounts - Cleaned params: Customer ID: $customerId, Start Date: $dateFrom, End Date: $dateTo");
            
            try {
                // استرجاع إجمالي الخصومات
                $totalDiscounts = $customerModel->getCustomerTotalDiscounts($customerId, $dateFrom, $dateTo);
                
                error_log("API get_customer_total_discounts - Result: $totalDiscounts");
                
                echo json_encode([
                    'status' => 'success',
                    'total_discounts' => $totalDiscounts
                ]);
            } catch (Exception $e) {
                error_log("API get_customer_total_discounts - Error: " . $e->getMessage());
                echo json_encode([
                    'status' => 'error',
                    'message' => 'حدث خطأ أثناء حساب إجمالي الخصومات: ' . $e->getMessage()
                ]);
            }
            exit;



        default:

            throw new Exception('الإجراء المطلوب غير صالح');

    }

} catch (Exception $e) {

    echo json_encode([

        'status' => 'error',

        'message' => $e->getMessage()

    ]);

}

