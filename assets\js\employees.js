/**
 * ملف JavaScript للتعامل مع وظائف إدارة الموظفين
 */

// تنفيذ الكود عند اكتمال تحميل الصفحة
$(document).ready(function() {
    // تهيئة عناصر الصفحة
    EmployeesHandler.initializeComponents();

    // إعداد الأحداث
    EmployeesHandler.setupEventListeners();

    // تحميل البيانات الأولية
    EmployeesHandler.loadInitialData();
});

/**
 * المعالج الرئيسي لوظائف إدارة الموظفين
 */
const EmployeesHandler = {
    /**
     * تهيئة مكونات الصفحة
     */
    initializeComponents: function() {
        // تهيئة عناصر التاريخ
        if ($.fn.datepicker) {
            $('.datepicker').datepicker({
                format: 'yyyy-mm-dd',
                autoclose: true,
                todayHighlight: true,
                rtl: true
            });
        }

        // تهيئة عناصر الوقت
        if ($.fn.timepicker) {
            $('.timepicker').timepicker({
                showMeridian: false,
                defaultTime: false,
                minuteStep: 5
            });
        }

        // تهيئة عناصر القائمة المنسدلة
        if ($.fn.select2) {
            $('.select2').select2({
                dir: "rtl",
                width: '100%'
            });

            // تهيئة القائمة المنسدلة للخدمات مع إمكانية البحث
            $('.select-services').select2({
                dir: "rtl",
                width: '100%',
                placeholder: 'اختر الخدمات',
                allowClear: true,
                multiple: true
            });
        }

        // تهيئة جدول الموظفين إذا كان موجودًا
        if ($.fn.DataTable && $('#employees-table').length) {
            $('#employees-table').DataTable({
                processing: true,
                serverSide: false,
                ordering: true,
                paging: true,
                pageLength: 25,
                responsive: true,
                language: {
                    url: '/assets/plugins/datatables/ar.json'
                },
                columns: [
                    { data: 'id' },
                    { data: 'name' },
                    { data: 'phone' },
                    { data: 'position' },
                    { data: 'salary_type' },
                    { data: 'branch_name' },
                    { data: 'is_active' },
                    { data: 'actions', orderable: false }
                ]
            });
        }

        // تهيئة جدول الرواتب إذا كان موجودًا
        if ($.fn.DataTable && $('#salaries-table').length) {
            $('#salaries-table').DataTable({
                processing: true,
                serverSide: false,
                ordering: true,
                paging: true,
                pageLength: 25,
                responsive: true,
                language: {
                    url: '/assets/plugins/datatables/ar.json'
                },
                columns: [
                    { data: 'id' },
                    { data: 'employee_name' },
                    { data: 'month_year' },
                    { data: 'fixed_amount' },
                    { data: 'commission_amount' },
                    { data: 'total_amount' },
                    { data: 'payment_status' },
                    { data: 'actions', orderable: false }
                ]
            });
        }

        // تهيئة مخططات الإحصائيات إذا كان موجودًا
        if ($('#employee-stats-container').length) {
            this.initializeCharts();
        }
    },

    /**
     * إعداد مستمعي الأحداث
     */
    setupEventListeners: function() {
        // النموذج الرئيسي للموظف (إضافة/تعديل)
        $('#employee-form').on('submit', function(e) {
            e.preventDefault();
            EmployeesHandler.saveEmployee();
        });

        // حدث تغيير نوع الراتب
        $('#salary_type').on('change', function() {
            EmployeesHandler.toggleSalaryFields();
        });

        // حدث النقر على زر الحذف
        $(document).on('click', '.btn-delete-employee', function() {
            const employeeId = $(this).data('id');
            EmployeesHandler.confirmDeleteEmployee(employeeId);
        });

        // حدث النقر على زر تسجيل الحضور
        $(document).on('click', '.btn-record-attendance', function() {
            const employeeId = $(this).data('id');
            EmployeesHandler.showAttendanceModal(employeeId);
        });

        // نموذج تسجيل الحضور
        $('#attendance-form').on('submit', function(e) {
            e.preventDefault();
            EmployeesHandler.saveAttendance();
        });

        // حدث النقر على زر حساب الراتب
        $(document).on('click', '.btn-calculate-salary', function() {
            const employeeId = $(this).data('id');
            EmployeesHandler.calculateSalary(employeeId);
        });

        // نموذج صرف الراتب
        $('#salary-form').on('submit', function(e) {
            e.preventDefault();
            EmployeesHandler.saveSalary();
        });

        // حدث تغيير الشهر أو السنة في تقرير الراتب
        $('#salary-month, #salary-year').on('change', function() {
            if ($('#employee_id').val()) {
                EmployeesHandler.calculateSalary($('#employee_id').val());
            }
        });

        // حدث النقر على زر عرض الإحصائيات
        $(document).on('click', '.btn-view-stats', function() {
            const employeeId = $(this).data('id');
            EmployeesHandler.showEmployeeStats(employeeId);
        });

        // مستمع حدث تبديل إنشاء حساب مستخدم
        $('#create_user').on('change', function() {
            $('#password-container').toggleClass('d-none', !$(this).prop('checked'));
        });

        // مستمع أحداث فلاتر الموظفين
        $('#branch_filter, #position_filter, #status_filter').on('change', function() {
            EmployeesHandler.loadEmployees();
        });

        // حدث النقر على زر تحديث حالة الدفع للراتب
        $(document).on('click', '.btn-update-salary-status', function() {
            const salaryId = $(this).data('id');
            EmployeesHandler.showUpdateSalaryStatusModal(salaryId);
        });

        // مستمع أحداث للبحث عن الموظفين
        let searchTimeout;
        $('#employee-search').on('keyup', function() {
            const searchTerm = $(this).val().trim();
            clearTimeout(searchTimeout);

            if (searchTerm.length >= 2 || searchTerm.length === 0) {
                searchTimeout = setTimeout(function() {
                    EmployeesHandler.loadEmployees(searchTerm);
                }, 500);
            }
        });

        // حدث النقر على زر عرض سجل الحضور
        $('#btn-show-attendance-history').on('click', function() {
            const employeeId = $('#employee_id').val();
            if (employeeId) {
                EmployeesHandler.showAttendanceHistory(employeeId);
            }
        });
    },

    /**
     * تحميل البيانات الأولية للصفحة
     */
    loadInitialData: function() {
        // تفعيل حقول الراتب المناسبة حسب النوع
        this.toggleSalaryFields();

        // إذا كانت صفحة قائمة الموظفين
        if ($('#employees-table').length) {
            this.loadEmployees();
        }

        // إذا كانت صفحة تعديل موظف
        if ($('#employee-form').length && $('#employee_id').length && $('#employee_id').val() !== '') {
            const employeeId = $('#employee_id').val();
            this.loadEmployeeDetails(employeeId);
        }

        // إذا كانت صفحة الرواتب
        if ($('#salaries-table').length) {
            this.loadSalaries();
        }

        // تحميل قائمة الخدمات للاختيار إذا كان موجودًا
        if ($('.select-services').length) {
            this.loadServices();
        }

        // إذا كانت صفحة إحصائيات الموظفين
        if ($('#employee-stats-container').length && $('#employee_id').length && $('#employee_id').val() !== '') {
            const employeeId = $('#employee_id').val();
            this.showEmployeeStats(employeeId);
        }
    },

    /**
     * تبديل ظهور حقول الراتب حسب نوع الراتب
     */
    toggleSalaryFields: function() {
        const salaryType = $('#salary_type').val();

        // إخفاء جميع الحقول أولاً
        $('.salary-field').addClass('d-none');

        // إظهار الحقول المناسبة حسب النوع
        switch (salaryType) {
            case 'fixed':
                $('.fixed-salary-field').removeClass('d-none');
                break;
            case 'percentage':
                $('.percentage-salary-field').removeClass('d-none');
                break;
            case 'both':
                $('.fixed-salary-field, .percentage-salary-field').removeClass('d-none');
                break;
        }
    },

    /**
     * تنسيق نوع الراتب لعرضه في الجدول
     * @param {string} salaryType نوع الراتب
     * @returns {string} النص المنسق
     */
    formatSalaryType: function(salaryType) {
        switch (salaryType) {
            case 'fixed':
                return 'راتب ثابت';
            case 'percentage':
                return 'نسبة';
            case 'both':
                return 'ثابت + نسبة';
            default:
                return salaryType || '-';
        }
    },

    /**
     * تنسيق حالة الموظف لعرضها في الجدول
     * @param {number|string} status حالة الموظف
     * @returns {string} النص المنسق مع التنسيق HTML
     */
    formatStatus: function(status) {
        if (status == 1) {
            return '<span class="badge badge-success">نشط</span>';
        } else {
            return '<span class="badge badge-danger">غير نشط</span>';
        }
    },

    /**
     * تنسيق حالة دفع الراتب
     * @param {string} status حالة الدفع
     * @returns {string} النص المنسق مع التنسيق HTML
     */
    formatPaymentStatus: function(status) {
        if (status === 'paid') {
            return '<span class="badge badge-success">مدفوع</span>';
        } else {
            return '<span class="badge badge-warning">غير مدفوع</span>';
        }
    },

    /**
     * إنشاء أزرار الإجراءات للموظف
     * @param {Object} employee بيانات الموظف
     * @returns {string} HTML للأزرار
     */
    generateEmployeeActions: function(employee) {
        let actionsHtml = '<div class="btn-group" role="group">';

        // زر عرض التفاصيل
        actionsHtml += `<a href="view.php?id=${employee.id}" class="btn btn-sm btn-info" title="عرض التفاصيل">
                          <i class="fa fa-eye"></i>
                        </a>`;

        // زر التعديل
        actionsHtml += `<a href="edit.php?id=${employee.id}" class="btn btn-sm btn-primary" title="تعديل">
                          <i class="fa fa-edit"></i>
                        </a>`;

        // زر حذف
        actionsHtml += `<button type="button" class="btn btn-sm btn-danger btn-delete-employee"
                         data-id="${employee.id}" title="حذف">
                          <i class="fa fa-trash"></i>
                        </button>`;

        // زر تسجيل الحضور
        actionsHtml += `<button type="button" class="btn btn-sm btn-success btn-record-attendance"
                         data-id="${employee.id}" title="تسجيل الحضور">
                          <i class="fa fa-calendar-check"></i>
                        </button>`;

        // زر حساب الراتب
        actionsHtml += `<button type="button" class="btn btn-sm btn-warning btn-calculate-salary"
                         data-id="${employee.id}" title="حساب الراتب">
                          <i class="fa fa-money-bill"></i>
                        </button>`;

        // زر عرض الإحصائيات
        actionsHtml += `<button type="button" class="btn btn-sm btn-info btn-view-stats"
                         data-id="${employee.id}" title="عرض الإحصائيات">
                          <i class="fa fa-chart-line"></i>
                        </button>`;

        actionsHtml += '</div>';

        return actionsHtml;
    },

    /**
     * إنشاء أزرار الإجراءات للراتب
     * @param {Object} salary بيانات الراتب
     * @returns {string} HTML للأزرار
     */
    generateSalaryActions: function(salary) {
        let actionsHtml = '<div class="btn-group" role="group">';

        // زر طباعة الراتب
        actionsHtml += `<button type="button" class="btn btn-sm btn-info" onclick="EmployeesHandler.printSalary(${salary.id})" title="طباعة">
                          <i class="fa fa-print"></i>
                        </button>`;

        // زر تحديث حالة الدفع
        actionsHtml += `<button type="button" class="btn btn-sm btn-primary btn-update-salary-status"
                         data-id="${salary.id}" title="تحديث حالة الدفع">
                          <i class="fa fa-edit"></i>
                        </button>`;

        actionsHtml += '</div>';

        return actionsHtml;
    },

    /**
     * تحميل قائمة الموظفين
     * @param {string} searchTerm - كلمة البحث (اختياري)
     */
    loadEmployees: function(searchTerm = '') {
        const tableElement = $('#employees-table');
        if (!tableElement.length) return;

        const dataTable = tableElement.DataTable();

        // عرض مؤشر التحميل
        showLoading();

        // جمع معلمات الفلترة
        const data = {
            action: 'get_employees',
            branch_id: $('#branch_filter').val() || '',
            position: $('#position_filter').val() || '',
            is_active: $('#status_filter').val() || ''
        };

        // إضافة مصطلح البحث إذا وجد
        if (searchTerm) {
            data.search = searchTerm;
        }

        // استخدام AjaxHandler للحصول على البيانات
        AjaxHandler.get('employees.php', data)
            .then(response => {
                // إخفاء مؤشر التحميل
                hideLoading();

                // مسح الجدول وإعادة تعبئته
                dataTable.clear();

                if (response.employees && response.employees.length > 0) {
                    const employees = response.employees.map(employee => {
                        // تعديل بيانات الموظف لعرضها في الجدول
                        return {
                            id: employee.id,
                            name: employee.name,
                            phone: employee.phone || '-',
                            position: employee.position || '-',
                            salary_type: this.formatSalaryType(employee.salary_type),
                            branch_name: employee.branch_name || '-',
                            is_active: this.formatStatus(employee.is_active),
                            actions: this.generateEmployeeActions(employee)
                        };
                    });

                    // إضافة البيانات للجدول
                    dataTable.rows.add(employees).draw();

                    // تحديث عداد الموظفين
                    $('#employees-count').text(response.total || 0);
                } else {
                    dataTable.draw();
                    $('#employees-count').text('0');
                    if (searchTerm) {
                        showMessage('لا توجد نتائج تطابق البحث', 'info');
                    } else {
                        showMessage('لا يوجد موظفين للعرض', 'info');
                    }
                }
            })
            .catch(error => {
                hideLoading();
                showMessage('حدث خطأ أثناء تحميل بيانات الموظفين: ' + error.message, 'error');
            });
    },

    /**
     * تحميل تفاصيل موظف
     * @param {number} employeeId معرف الموظف
     */
    loadEmployeeDetails: function(employeeId) {
        showLoading();

        const data = {
            action: 'get_employee',
            employee_id: employeeId
        };

        AjaxHandler.get('employees.php', data)
            .then(response => {
                hideLoading();

                if (response.employee) {
                    this.fillEmployeeForm(response.employee);

                    // تحميل الخدمات المرتبطة بالموظف
                    this.loadEmployeeServices(employeeId);
                } else {
                    showMessage('لم يتم العثور على بيانات الموظف', 'error');
                }
            })
            .catch(error => {
                hideLoading();
                showMessage('حدث خطأ أثناء تحميل بيانات الموظف: ' + error.message, 'error');
            });
    },

    /**
     * ملء نموذج الموظف ببيانات موظف
     * @param {Object} employee بيانات الموظف
     */
    fillEmployeeForm: function(employee) {
        // تعبئة حقول النموذج بالبيانات
        $('#name').val(employee.name);
        $('#phone').val(employee.phone);
        $('#email').val(employee.email);
        $('#position').val(employee.position);
        $('#salary_type').val(employee.salary_type);
        $('#fixed_salary').val(employee.fixed_salary);
        $('#commission_percentage').val(employee.commission_percentage);
        $('#branch_id').val(employee.branch_id);
        $('#is_active').prop('checked', employee.is_active == 1);

        // تحديث الحقول المرئية حسب نوع الراتب
        this.toggleSalaryFields();

        // إذا كان هناك معرف مستخدم، فقم بتحديد خانة إنشاء حساب
        if (employee.user_id) {
            $('#create_user').prop('checked', true);
            $('#password-container').removeClass('d-none');
        } else {
            $('#create_user').prop('checked', false);
            $('#password-container').addClass('d-none');
        }
    },

    /**
     * تحميل الخدمات المرتبطة بالموظف
     * @param {number} employeeId معرف الموظف
     */
    loadEmployeeServices: function(employeeId) {
        if (!$('.select-services').length) return;

        const data = {
            action: 'get_employee_services',
            employee_id: employeeId
        };

        AjaxHandler.get('employees.php', data)
            .then(response => {
                if (response.services && response.services.length > 0) {
                    // استخراج معرفات الخدمات
                    const serviceIds = response.services.map(service => service.id);

                    // تحديد الخدمات في القائمة المنسدلة
                    $('.select-services').val(serviceIds).trigger('change');
                }
            })
            .catch(error => {
                console.error('خطأ في تحميل خدمات الموظف:', error);
            });
    },

    /**
     * تحميل قائمة الخدمات
     */
    loadServices: function() {
        // فحص إذا كان هناك قائمة منسدلة للخدمات
        if (!$('.select-services').length) return;

        // إفراغ القائمة أولاً
        $('.select-services').empty();

        const data = {
            action: 'get_all_services',
            branch_id: $('#branch_id').val() || ''
        };

        AjaxHandler.get('employees.php', data)
            .then(response => {
                if (response.services) {
                    // إضافة الخدمات للقائمة المنسدلة
                    response.services.forEach(service => {
                        const option = new Option(service.name, service.id, false, false);
                        $('.select-services').append(option);
                    });

                    // تحديث القائمة المنسدلة
                    $('.select-services').trigger('change');

                    // إذا كنا في صفحة تعديل، قم بتحميل خدمات الموظف
                    if ($('#employee_id').length && $('#employee_id').val()) {
                        this.loadEmployeeServices($('#employee_id').val());
                    }
                }
            })
            .catch(error => {
                console.error('خطأ في تحميل الخدمات:', error);
            });
    },

    /**
     * حفظ بيانات موظف (إضافة/تعديل)
     */
    saveEmployee: function() {
        // جمع بيانات النموذج
        const formData = new FormData(document.getElementById('employee-form'));
        const employeeId = $('#employee_id').val();

        // تحديد الإجراء حسب وجود معرف الموظف
        const action = employeeId ? 'update_employee' : 'add_employee';
        formData.append('action', action);

        if (employeeId) {
            formData.append('employee_id', employeeId);
        }

        // إضافة الخدمات المحددة
        if ($('.select-services').length) {
            const selectedServices = $('.select-services').val();
            if (selectedServices && selectedServices.length > 0) {
                formData.append('services', selectedServices);
            }
        }

        // إضافة حالة الموظف
        formData.append('is_active', $('#is_active').is(':checked') ? 1 : 0);

        // إضافة حالة إنشاء حساب
        formData.append('create_user', $('#create_user').is(':checked') ? 1 : 0);

        // التحقق من صحة البيانات
        if (!this.validateEmployeeForm()) {
            return;
        }

        // عرض مؤشر التحميل
        showLoading();

        // تهيئة إعدادات AJAX للتعامل مع FormData
        const config = {
            processData: false,
            contentType: false
        };

        // إرسال البيانات للخادم
        AjaxHandler.postFormData('employees.php', formData, config)
            .then(response => {
                hideLoading();

                if (response.status === 'success') {
                    showMessage(response.message, 'success');

                    // إذا تم إنشاء حساب وتم توليد كلمة مرور
                    if (response.generated_password) {
                        this.showGeneratedPassword(response.generated_password);
                    }

                    // إذا كان إضافة، انتقل لصفحة التعديل بعد ثانيتين
                    if (!employeeId && response.employee_id) {
                        setTimeout(function() {
                            window.location.href = 'edit.php?id=' + response.employee_id;
                        }, 2000);
                    }
                } else {
                    showMessage(response.message || 'حدث خطأ أثناء حفظ بيانات الموظف', 'error');
                }
            })
            .catch(error => {
                hideLoading();
                showMessage('حدث خطأ في الاتصال بالخادم: ' + error.message, 'error');
            });
    },

    /**
     * التحقق من صحة بيانات نموذج الموظف
     * @returns {boolean} نتيجة التحقق
     */
    validateEmployeeForm: function() {
        // الحقول المطلوبة
        const name = $('#name').val().trim();
        const phone = $('#phone').val().trim();
        const position = $('#position').val().trim();
        const salaryType = $('#salary_type').val();

        let isValid = true;

        // التحقق من الاسم
        if (!name) {
            this.showFieldError('name', 'الاسم مطلوب');
            isValid = false;
        } else {
            this.clearFieldError('name');
        }

        // التحقق من رقم الهاتف
        if (!phone) {
            this.showFieldError('phone', 'رقم الهاتف مطلوب');
            isValid = false;
        } else {
            this.clearFieldError('phone');
        }

        // التحقق من المسمى الوظيفي
        if (!position) {
            this.showFieldError('position', 'المسمى الوظيفي مطلوب');
            isValid = false;
        } else {
            this.clearFieldError('position');
        }

        // التحقق من قيم الراتب حسب النوع
        if (salaryType === 'fixed' || salaryType === 'both') {
            const fixedSalary = parseFloat($('#fixed_salary').val() || 0);
            if (fixedSalary <= 0) {
                this.showFieldError('fixed_salary', 'الراتب الثابت يجب أن يكون أكبر من صفر');
                isValid = false;
            } else {
                this.clearFieldError('fixed_salary');
            }
        }

        if (salaryType === 'percentage' || salaryType === 'both') {
            const commissionPercentage = parseFloat($('#commission_percentage').val() || 0);
            if (commissionPercentage <= 0 || commissionPercentage > 100) {
                this.showFieldError('commission_percentage', 'النسبة يجب أن تكون بين 1 و 100');
                isValid = false;
            } else {
                this.clearFieldError('commission_percentage');
            }
        }

        // التحقق من كلمة المرور إذا تم تحديد إنشاء حساب
        if ($('#create_user').is(':checked') && !$('#employee_id').val()) {
            const password = $('#password').val().trim();
            if (!password) {
                this.showFieldError('password', 'كلمة المرور مطلوبة');
                isValid = false;
            } else if (password.length < 6) {
                this.showFieldError('password', 'كلمة المرور يجب أن تكون 6 أحرف على الأقل');
                isValid = false;
            } else {
                this.clearFieldError('password');
            }
        }

        return isValid;
    },

/**
     * عرض خطأ في حقل معين
     * @param {string} fieldId معرف الحقل
     * @param {string} errorMessage رسالة الخطأ
     */
showFieldError: function(fieldId, errorMessage) {
    const field = $('#' + fieldId);
    field.addClass('is-invalid');

    // إضافة رسالة الخطأ
    let errorDiv = field.next('.invalid-feedback');
    if (errorDiv.length === 0) {
        field.after('<div class="invalid-feedback">' + errorMessage + '</div>');
    } else {
        errorDiv.text(errorMessage);
    }
},

/**
 * إزالة خطأ من حقل معين
 * @param {string} fieldId معرف الحقل
 */
clearFieldError: function(fieldId) {
    const field = $('#' + fieldId);
    field.removeClass('is-invalid');
},

/**
 * عرض كلمة المرور المولدة للمستخدم
 * @param {string} password كلمة المرور المولدة
 */
showGeneratedPassword: function(password) {
    // إنشاء نافذة عرض كلمة المرور
    const modalHtml = `
    <div class="modal fade" id="passwordModal" tabindex="-1" role="dialog" aria-labelledby="passwordModalLabel" aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="passwordModalLabel">كلمة المرور المولدة</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <p>تم إنشاء حساب للموظف بكلمة المرور التالية:</p>
                    <div class="form-group">
                        <input type="text" class="form-control text-center" value="${password}" readonly id="generated-password">
                    </div>
                    <div class="alert alert-warning">
                        <i class="fa fa-exclamation-triangle"></i>
                        هذه هي المرة الوحيدة التي سيتم فيها عرض كلمة المرور. يرجى حفظها في مكان آمن.
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-primary" onclick="EmployeesHandler.copyPassword()">نسخ كلمة المرور</button>
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">إغلاق</button>
                </div>
            </div>
        </div>
    </div>
    `;

    // إضافة النافذة للصفحة
    $('body').append(modalHtml);

    // عرض النافذة
    $('#passwordModal').modal('show');

    // إزالة النافذة عند إغلاقها
    $('#passwordModal').on('hidden.bs.modal', function() {
        $(this).remove();
    });
},

/**
 * نسخ كلمة المرور المولدة
 */
copyPassword: function() {
    const passwordField = document.getElementById('generated-password');
    passwordField.select();
    document.execCommand('copy');

    // إظهار رسالة النسخ
    showMessage('تم نسخ كلمة المرور', 'success');
},

/**
 * تأكيد حذف موظف
 * @param {number} employeeId معرف الموظف
 */
confirmDeleteEmployee: function(employeeId) {
    if (confirm('هل أنت متأكد من حذف هذا الموظف؟ لا يمكن التراجع عن هذا الإجراء.')) {
        this.deleteEmployee(employeeId);
    }
},

/**
 * حذف موظف
 * @param {number} employeeId معرف الموظف
 */
deleteEmployee: function(employeeId) {
    showLoading();

    const data = {
        action: 'delete_employee',
        employee_id: employeeId
    };

    AjaxHandler.post('employees.php', data)
        .then(response => {
            hideLoading();

            if (response.status === 'success') {
                showMessage(response.message, 'success');

                // تحديث قائمة الموظفين
                this.loadEmployees();
            } else {
                showMessage(response.message || 'حدث خطأ أثناء حذف الموظف', 'error');
            }
        })
        .catch(error => {
            hideLoading();
            showMessage('حدث خطأ في الاتصال بالخادم: ' + error.message, 'error');
        });
},

/**
 * عرض نافذة تسجيل الحضور
 * @param {number} employeeId معرف الموظف
 */
showAttendanceModal: function(employeeId) {
    // استرجاع اسم الموظف
    let employeeName = '';

    // إذا كنا في صفحة قائمة الموظفين، نستطيع استرجاع الاسم من الجدول
    if ($('#employees-table').length) {
        const table = $('#employees-table').DataTable();
        const row = table.row(function(idx, data) {
            return data.id == employeeId;
        });

        if (row.length) {
            employeeName = row.data().name;
        }
    } else {
        // استرجاع الاسم من العنصر الذي تم النقر عليه
        employeeName = $('#employee-name').text() || 'الموظف';
    }

    // إنشاء نافذة تسجيل الحضور
    const modalHtml = `
    <div class="modal fade" id="attendanceModal" tabindex="-1" role="dialog" aria-labelledby="attendanceModalLabel" aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="attendanceModalLabel">تسجيل حضور ${employeeName}</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <form id="attendance-form">
                        <input type="hidden" name="employee_id" value="${employeeId}">

                        <div class="form-group">
                            <label for="date">التاريخ</label>
                            <input type="text" class="form-control datepicker" id="attendance-date" name="date" value="${formatDate(new Date())}" required>
                        </div>

                        <div class="form-group">
                            <label for="check_in">وقت الحضور</label>
                            <input type="text" class="form-control timepicker" id="check_in" name="check_in" required>
                        </div>

                        <div class="form-group">
                            <label for="check_out">وقت الانصراف</label>
                            <input type="text" class="form-control timepicker" id="check_out" name="check_out">
                        </div>

                        <div class="form-group">
                            <label for="notes">ملاحظات</label>
                            <textarea class="form-control" id="notes" name="notes" rows="2"></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-primary" onclick="EmployeesHandler.saveAttendance()">حفظ</button>
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">إلغاء</button>
                </div>
            </div>
        </div>
    </div>
    `;

    // إضافة النافذة للصفحة
    $('body').append(modalHtml);

    // تهيئة عناصر التاريخ والوقت
    $('#attendance-date').datepicker({
        format: 'yyyy-mm-dd',
        autoclose: true,
        todayHighlight: true,
        rtl: true
    });

    $('.timepicker').timepicker({
        showMeridian: false,
        defaultTime: false,
        minuteStep: 5
    });

    // عرض النافذة
    $('#attendanceModal').modal('show');

    // إزالة النافذة عند إغلاقها
    $('#attendanceModal').on('hidden.bs.modal', function() {
        $(this).remove();
    });
},

/**
 * حفظ بيانات الحضور
 */
saveAttendance: function() {
    // جمع بيانات النموذج
    const formData = $('#attendance-form').serialize();

    // إضافة الإجراء
    const data = formData + '&action=record_attendance';

    // عرض مؤشر التحميل
    showLoading();

    // إرسال البيانات للخادم
    AjaxHandler.post('employees.php', data, {
        contentType: 'application/x-www-form-urlencoded',
    })
        .then(response => {
            hideLoading();

            if (response.status === 'success') {
                // إغلاق النافذة
                $('#attendanceModal').modal('hide');

                showMessage(response.message, 'success');

                // إذا كنا في صفحة التفاصيل، قم بتحديث سجل الحضور
                if ($('#attendance-history').length) {
                    this.showAttendanceHistory($('#employee_id').val());
                }
            } else {
                showMessage(response.message || 'حدث خطأ أثناء حفظ بيانات الحضور', 'error');
            }
        })
        .catch(error => {
            hideLoading();
            showMessage('حدث خطأ في الاتصال بالخادم: ' + error.message, 'error');
        });
},

/**
 * عرض سجل الحضور للموظف
 * @param {number} employeeId معرف الموظف
 */
showAttendanceHistory: function(employeeId) {
    const attendanceContainer = $('#attendance-history');
    if (!attendanceContainer.length) return;

    // عرض مؤشر التحميل
    showLoading();

    const data = {
        action: 'get_employee_attendance',
        employee_id: employeeId
    };

    // إضافة فلتر الشهر والسنة إذا تم تحديدهما
    const month = $('#attendance-month').val();
    const year = $('#attendance-year').val();
    if (month && year) {
        data.month = month;
        data.year = year;
    }

    AjaxHandler.get('employees.php', data)
        .then(response => {
            hideLoading();

            if (response.status === 'success') {
                let html = '';

                if (response.attendance && response.attendance.length > 0) {
                    html = '<div class="table-responsive"><table class="table table-bordered table-striped">';
                    html += '<thead><tr><th>التاريخ</th><th>الحضور</th><th>الانصراف</th><th>ساعات العمل</th><th>ملاحظات</th></tr></thead>';
                    html += '<tbody>';

                    response.attendance.forEach(record => {
                        html += '<tr>';
                        html += '<td>' + formatDate(new Date(record.date)) + '</td>';
                        html += '<td>' + (record.check_in || '-') + '</td>';
                        html += '<td>' + (record.check_out || '-') + '</td>';
                        html += '<td>' + (record.work_hours || '-') + '</td>';
                        html += '<td>' + (record.notes || '-') + '</td>';
                        html += '</tr>';
                    });

                    html += '</tbody></table></div>';
                } else {
                    html = '<div class="alert alert-info">لا توجد سجلات حضور للفترة المحددة</div>';
                }

                attendanceContainer.html(html);
            } else {
                showMessage(response.message || 'حدث خطأ أثناء تحميل سجلات الحضور', 'error');
            }
        })
        .catch(error => {
            hideLoading();
            showMessage('حدث خطأ في الاتصال بالخادم: ' + error.message, 'error');
        });
},

/**
 * حساب الراتب
 * @param {number} employeeId معرف الموظف
 */
calculateSalary: function(employeeId) {
    // عرض نافذة حساب الراتب
    this.showSalaryCalculationModal(employeeId);
},

/**
 * عرض نافذة حساب الراتب
 * @param {number} employeeId معرف الموظف
 */
showSalaryCalculationModal: function(employeeId) {
    // استرجاع اسم الموظف
    let employeeName = '';

    // إذا كنا في صفحة قائمة الموظفين، نستطيع استرجاع الاسم من الجدول
    if ($('#employees-table').length) {
        const table = $('#employees-table').DataTable();
        const row = table.row(function(idx, data) {
            return data.id == employeeId;
        });

        if (row.length) {
            employeeName = row.data().name;
        }
    } else {
        // استرجاع الاسم من العنصر الذي تم النقر عليه
        employeeName = $('#employee-name').text() || 'الموظف';
    }

    // إعداد الشهر والسنة الحاليين
    const now = new Date();
    const currentMonth = now.getMonth() + 1; // getMonth() يبدأ من 0
    const currentYear = now.getFullYear();

    // إنشاء نافذة حساب الراتب
    const modalHtml = `
    <div class="modal fade" id="salaryModal" tabindex="-1" role="dialog" aria-labelledby="salaryModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="salaryModalLabel">حساب راتب ${employeeName}</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <form id="salary-form">
                        <input type="hidden" name="employee_id" value="${employeeId}">

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="salary-month">الشهر</label>
                                    <select class="form-control" id="salary-month" name="month">
                                        <option value="1" ${currentMonth === 1 ? 'selected' : ''}>يناير</option>
                                        <option value="2" ${currentMonth === 2 ? 'selected' : ''}>فبراير</option>
                                        <option value="3" ${currentMonth === 3 ? 'selected' : ''}>مارس</option>
                                        <option value="4" ${currentMonth === 4 ? 'selected' : ''}>أبريل</option>
                                        <option value="5" ${currentMonth === 5 ? 'selected' : ''}>مايو</option>
                                        <option value="6" ${currentMonth === 6 ? 'selected' : ''}>يونيو</option>
                                        <option value="7" ${currentMonth === 7 ? 'selected' : ''}>يوليو</option>
                                        <option value="8" ${currentMonth === 8 ? 'selected' : ''}>أغسطس</option>
                                        <option value="9" ${currentMonth === 9 ? 'selected' : ''}>سبتمبر</option>
                                        <option value="10" ${currentMonth === 10 ? 'selected' : ''}>أكتوبر</option>
                                        <option value="11" ${currentMonth === 11 ? 'selected' : ''}>نوفمبر</option>
                                        <option value="12" ${currentMonth === 12 ? 'selected' : ''}>ديسمبر</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="salary-year">السنة</label>
                                    <select class="form-control" id="salary-year" name="year">
                                        <option value="${currentYear-1}">${currentYear-1}</option>
                                        <option value="${currentYear}" selected>${currentYear}</option>
                                        <option value="${currentYear+1}">${currentYear+1}</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="mt-3 mb-3">
                            <div class="form-check mb-2">
                                <input class="form-check-input" type="checkbox" id="calculate-until-today" checked>
                                <label class="form-check-label" for="calculate-until-today">
                                    حساب الراتب حتى اليوم الحالي (للشهر الحالي فقط)
                                </label>
                            </div>
                            <div class="text-center">
                                <button type="button" class="btn btn-primary" id="btn-calculate-salary-now">حساب الراتب</button>
                            </div>
                        </div>

                        <div id="salary-result" class="d-none">
                            <hr>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="fixed_amount">الراتب الثابت</label>
                                        <input type="number" class="form-control" id="fixed_amount" name="fixed_amount" readonly>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="commission_amount">عمولة المبيعات</label>
                                        <input type="number" class="form-control" id="commission_amount" name="commission_amount" readonly>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="bonuses">المكافآت</label>
                                        <input type="number" class="form-control" id="bonuses" name="bonuses" value="0" step="0.01">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="deductions">الخصومات</label>
                                        <input type="number" class="form-control" id="deductions" name="deductions" value="0" step="0.01">
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="total_amount">إجمالي الراتب</label>
                                        <input type="number" class="form-control" id="total_amount" name="total_amount" readonly>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="payment_status">حالة الدفع</label>
                                        <select class="form-control" id="payment_status" name="payment_status">
                                            <option value="unpaid">غير مدفوع</option>
                                            <option value="paid">مدفوع</option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="payment_date">تاريخ الدفع</label>
                                <input type="text" class="form-control datepicker" id="payment_date" name="payment_date">
                            </div>

                            <div class="form-group">
                                <label for="salary-notes">ملاحظات</label>
                                <textarea class="form-control" id="salary-notes" name="notes" rows="2"></textarea>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer" id="salary-modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">إغلاق</button>
                </div>
            </div>
        </div>
    </div>
    `;

    // إضافة النافذة للصفحة
    $('body').append(modalHtml);

    // تهيئة عناصر التاريخ
    $('.datepicker').datepicker({
        format: 'yyyy-mm-dd',
        autoclose: true,
        todayHighlight: true,
        rtl: true
    });

    // إضافة حدث حساب الراتب
    $('#btn-calculate-salary-now').on('click', () => {
        this.calculateSalaryNow(employeeId);
    });

    // إضافة حدث تغيير المكافآت والخصومات
    $('#bonuses, #deductions').on('change', () => {
        this.updateTotalSalary();
    });

    // عرض النافذة
    $('#salaryModal').modal('show');

    // إزالة النافذة عند إغلاقها
    $('#salaryModal').on('hidden.bs.modal', function() {
        $(this).remove();
    });
},

/**
 * حساب الراتب الآن
 * @param {number} employeeId معرف الموظف
 */
calculateSalaryNow: function(employeeId) {
    // عرض مؤشر التحميل
    showLoading();

    const month = $('#salary-month').val();
    const year = $('#salary-year').val();

    // التحقق مما إذا كان الشهر والسنة هما الشهر والسنة الحاليين
    const now = new Date();
    const currentMonth = now.getMonth() + 1; // getMonth() يبدأ من 0
    const currentYear = now.getFullYear();
    const currentDay = now.getDate();

    // إعداد بيانات الطلب
    const data = {
        action: 'calculate_salary',
        employee_id: employeeId,
        month: month,
        year: year
    };

    // التحقق من خيار حساب الراتب حتى اليوم الحالي
    const calculateUntilToday = $('#calculate-until-today').is(':checked');

    // إذا كان الشهر والسنة هما الشهر والسنة الحاليين وتم تحديد خيار حساب الراتب حتى اليوم الحالي
    if (parseInt(month) === currentMonth && parseInt(year) === currentYear && calculateUntilToday) {
        data.current_day = currentDay;
    }

    AjaxHandler.post('employees.php', data)
        .then(response => {
            hideLoading();

            if (response.status === 'success' && response.salary) {
                // تعبئة بيانات الراتب
                $('#fixed_amount').val(response.salary.fixed_amount);
                $('#commission_amount').val(response.salary.commission_amount);
                $('#total_amount').val(response.salary.total_amount);

                // إظهار نتيجة الراتب
                $('#salary-result').removeClass('d-none');

                // إضافة زر الحفظ
                if ($('#btn-save-salary').length === 0) {
                    $('#salary-modal-footer').prepend('<button type="button" class="btn btn-primary" id="btn-save-salary">حفظ الراتب</button>');

                    // إضافة حدث حفظ الراتب
                    $('#btn-save-salary').on('click', () => {
                        this.saveSalary();
                    });
                }
            } else {
                showMessage(response.message || 'حدث خطأ أثناء حساب الراتب', 'error');
            }
        })
        .catch(error => {
            hideLoading();
            showMessage('حدث خطأ في الاتصال بالخادم: ' + error.message, 'error');
        });
},

/**
 * تحديث إجمالي الراتب عند تغيير المكافآت أو الخصومات
 */
updateTotalSalary: function() {
    const fixedAmount = parseFloat($('#fixed_amount').val()) || 0;
    const commissionAmount = parseFloat($('#commission_amount').val()) || 0;
    const bonuses = parseFloat($('#bonuses').val()) || 0;
    const deductions = parseFloat($('#deductions').val()) || 0;

    const totalAmount = fixedAmount + commissionAmount + bonuses - deductions;

    $('#total_amount').val(totalAmount.toFixed(2));
},

/**
 * حفظ بيانات الراتب
 */
saveSalary: function() {
    // جمع بيانات النموذج
    const formData = $('#salary-form').serialize();

    // التحقق من خيار حساب الراتب حتى اليوم الحالي
    const calculateUntilToday = $('#calculate-until-today').is(':checked');

    // التحقق مما إذا كان الشهر والسنة هما الشهر والسنة الحاليين
    const month = $('#salary-month').val();
    const year = $('#salary-year').val();
    const now = new Date();
    const currentMonth = now.getMonth() + 1; // getMonth() يبدأ من 0
    const currentYear = now.getFullYear();
    const currentDay = now.getDate();

    // إضافة الإجراء
    let data = formData + '&action=save_salary';

    // إذا كان الشهر والسنة هما الشهر والسنة الحاليين وتم تحديد خيار حساب الراتب حتى اليوم الحالي
    if (parseInt(month) === currentMonth && parseInt(year) === currentYear && calculateUntilToday) {
        data += '&current_day=' + currentDay;
    }

    // عرض مؤشر التحميل
    showLoading();

    // إرسال البيانات للخادم
    AjaxHandler.post('employees.php', data, {
        contentType: 'application/x-www-form-urlencoded',
    })
        .then(response => {
            hideLoading();

            if (response.status === 'success') {
                // إغلاق النافذة
                $('#salaryModal').modal('hide');

                showMessage(response.message, 'success');

                // إذا كنا في صفحة الرواتب، قم بتحديث القائمة
                if ($('#salaries-table').length) {
                    this.loadSalaries();
                }
            } else {
                showMessage(response.message || 'حدث خطأ أثناء حفظ بيانات الراتب', 'error');
            }
        })
        .catch(error => {
            hideLoading();
            showMessage('حدث خطأ في الاتصال بالخادم: ' + error.message, 'error');
        });
},

/**
 * تحميل قائمة الرواتب
 */
loadSalaries: function() {
    const tableElement = $('#salaries-table');
    if (!tableElement.length) return;

    const dataTable = tableElement.DataTable();

    // عرض مؤشر التحميل
    showLoading();

    // جمع معلمات الفلترة
    const month = $('#salary-month-filter').val() || '';
    const year = $('#salary-year-filter').val() || '';
    const branchId = $('#branch_filter').val() || '';

    const data = {
        action: 'get_employees_salaries',
        month: month,
        year: year,
        branch_id: branchId
    };

    // إرسال الطلب لجلب الرواتب
    AjaxHandler.get('employees.php', data)
        .then(response => {
            // إخفاء مؤشر التحميل
            hideLoading();

            // مسح الجدول وإعادة تعبئته
            dataTable.clear();

            if (response.status === 'success' && response.salaries && response.salaries.length > 0) {
                const salaries = response.salaries.map(salary => {
                    // تعديل بيانات الراتب لعرضها في الجدول
                    return {
                        id: salary.id,
                        employee_name: salary.employee_name,
                        month_year: getMonthName(parseInt(salary.month)) + ' ' + salary.year,
                        fixed_amount: formatCurrency(salary.fixed_amount),
                        commission_amount: formatCurrency(salary.commission_amount),
                        total_amount: formatCurrency(salary.total_amount),
                        payment_status: this.formatPaymentStatus(salary.payment_status),
                        actions: this.generateSalaryActions(salary)
                    };
                });

                // إضافة البيانات للجدول
                dataTable.rows.add(salaries).draw();

                // تحديث إجمالي الرواتب
                let totalSalaries = 0;
                response.salaries.forEach(salary => {
                    totalSalaries += parseFloat(salary.total_amount);
                });
                $('#total-salaries').text(formatCurrency(totalSalaries));
            } else {
                dataTable.draw();
                $('#total-salaries').text('0.00');
                showMessage('لا توجد رواتب للفترة المحددة', 'info');
            }
        })
        .catch(error => {
            hideLoading();
            showMessage('حدث خطأ أثناء تحميل بيانات الرواتب: ' + error.message, 'error');
        });
},

/**
 * عرض نافذة تحديث حالة دفع الراتب
 * @param {number} salaryId معرف الراتب
 */
showUpdateSalaryStatusModal: function(salaryId) {
    // إنشاء نافذة تحديث حالة الدفع
    const modalHtml = `
    <div class="modal fade" id="updateSalaryStatusModal" tabindex="-1" role="dialog" aria-labelledby="updateSalaryStatusModalLabel" aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="updateSalaryStatusModalLabel">تحديث حالة دفع الراتب</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <form id="update-salary-status-form">
                        <input type="hidden" name="salary_id" value="${salaryId}">

                        <div class="form-group">
                            <label for="status">حالة الدفع</label>
                            <select class="form-control" id="status" name="status">
                                <option value="unpaid">غير مدفوع</option>
                                <option value="paid">مدفوع</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="payment_date">تاريخ الدفع</label>
                            <input type="text" class="form-control datepicker" id="payment_date" name="payment_date" value="${formatDate(new Date())}">
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-primary" onclick="EmployeesHandler.updateSalaryStatus()">حفظ</button>
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">إلغاء</button>
                </div>
            </div>
        </div>
    </div>
    `;

    // إضافة النافذة للصفحة
    $('body').append(modalHtml);

    // تهيئة عناصر التاريخ
    $('.datepicker').datepicker({
        format: 'yyyy-mm-dd',
        autoclose: true,
        todayHighlight: true,
        rtl: true
    });

    // عرض النافذة
    $('#updateSalaryStatusModal').modal('show');

    // إزالة النافذة عند إغلاقها
    $('#updateSalaryStatusModal').on('hidden.bs.modal', function() {
        $(this).remove();
    });
},

/**
 * تحديث حالة دفع الراتب
 */
updateSalaryStatus: function() {
    // جمع بيانات النموذج
    const formData = $('#update-salary-status-form').serialize();

    // إضافة الإجراء
    const data = formData + '&action=update_salary_status';

    // عرض مؤشر التحميل
    showLoading();

    // إرسال البيانات للخادم
    AjaxHandler.post('employees.php', data, {
        contentType: 'application/x-www-form-urlencoded',
    })
        .then(response => {
            hideLoading();

            if (response.status === 'success') {
                // إغلاق النافذة
                $('#updateSalaryStatusModal').modal('hide');

                showMessage(response.message, 'success');

                // إذا كنا في صفحة الرواتب، قم بتحديث القائمة
                if ($('#salaries-table').length) {
                    this.loadSalaries();
                }
            } else {
                showMessage(response.message || 'حدث خطأ أثناء تحديث حالة دفع الراتب', 'error');
            }
        })
        .catch(error => {
            hideLoading();
            showMessage('حدث خطأ في الاتصال بالخادم: ' + error.message, 'error');
        });
},

/**
 * طباعة الراتب
 * @param {number} salaryId معرف الراتب
 */
printSalary: function(salaryId) {
    // فتح صفحة الطباعة في نافذة جديدة
    window.open('print_salary.php?id=' + salaryId, '_blank');
},

/**
 * عرض إحصائيات الموظف
 * @param {number} employeeId معرف الموظف
 */
showEmployeeStats: function(employeeId) {
    const statsContainer = $('#employee-stats-container');
    if (!statsContainer.length) {
        // إذا كنا في صفحة قائمة الموظفين، نفتح صفحة الإحصائيات في نافذة جديدة
        window.open('stats.php?id=' + employeeId, '_blank');
        return;
    }

    // عرض مؤشر التحميل
    showLoading();

    const data = {
        action: 'get_employee_stats',
        employee_id: employeeId,
        period: $('#stats-period').val() || 'month'
    };

    AjaxHandler.get('employees.php', data)
        .then(response => {
            hideLoading();

            if (response.status === 'success' && response.stats) {
                // عرض إحصائيات الموظف
                this.renderEmployeeStats(response.stats);
            } else {
                showMessage(response.message || 'حدث خطأ أثناء تحميل إحصائيات الموظف', 'error');
            }
        })
        .catch(error => {
            hideLoading();
            showMessage('حدث خطأ في الاتصال بالخادم: ' + error.message, 'error');
        });
},

/**
 * عرض إحصائيات الموظف في الصفحة
 * @param {Object} stats إحصائيات الموظف
 */
renderEmployeeStats: function(stats) {
    // تحديث البطاقات الإحصائية
    $('#total-services').text(stats.total_services || 0);
    $('#total-sales').text(formatCurrency(stats.total_sales || 0));
    $('#total-commission').text(formatCurrency(stats.total_commission || 0));
    $('#average-daily-services').text((stats.average_daily_services || 0).toFixed(2));

    // تهيئة مخططات الإحصائيات
    this.initializeCharts(stats);
},

/**
 * تهيئة المخططات البيانية
 * @param {Object} stats إحصائيات الموظف
 */
initializeCharts: function(stats) {
    if (!stats) return;

    // تهيئة مخطط الخدمات اليومية
    if (stats.daily_services && $('#daily-services-chart').length) {
        const ctx = document.getElementById('daily-services-chart').getContext('2d');

        new Chart(ctx, {
            type: 'bar',
            data: {
                labels: stats.daily_services.map(item => item.date),
                datasets: [{
                    label: 'عدد الخدمات',
                    data: stats.daily_services.map(item => item.count),
                    backgroundColor: 'rgba(54, 162, 235, 0.5)',
                    borderColor: 'rgba(54, 162, 235, 1)',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
    }

    // تهيئة مخطط المبيعات الشهرية
    if (stats.monthly_sales && $('#monthly-sales-chart').length) {
        const ctx = document.getElementById('monthly-sales-chart').getContext('2d');

        new Chart(ctx, {
            type: 'line',
            data: {
                labels: stats.monthly_sales.map(item => getMonthName(parseInt(item.month))),
                datasets: [{
                    label: 'المبيعات',
                    data: stats.monthly_sales.map(item => item.amount),
                    backgroundColor: 'rgba(255, 99, 132, 0.5)',
                    borderColor: 'rgba(255, 99, 132, 1)',
                    borderWidth: 2,
                    fill: false
                }]
            },
            options: {
                responsive: true,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
    }

    // تهيئة مخطط توزيع الخدمات
    if (stats.services_breakdown && $('#services-breakdown-chart').length) {
        const ctx = document.getElementById('services-breakdown-chart').getContext('2d');

        new Chart(ctx, {
            type: 'pie',
            data: {
                labels: stats.services_breakdown.map(item => item.service_name),
                datasets: [{
                    data: stats.services_breakdown.map(item => item.count),
                    backgroundColor: [
                        'rgba(255, 99, 132, 0.7)',
                        'rgba(54, 162, 235, 0.7)',
                        'rgba(255, 206, 86, 0.7)',
                        'rgba(75, 192, 192, 0.7)',
                        'rgba(153, 102, 255, 0.7)',
                        'rgba(255, 159, 64, 0.7)'
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true
            }
        });
    }
}
};

/**
* تنسيق التاريخ
* @param {Date} date التاريخ
* @returns {string} التاريخ المنسق
*/
function formatDate(date) {
const year = date.getFullYear();
const month = String(date.getMonth() + 1).padStart(2, '0');
const day = String(date.getDate()).padStart(2, '0');

return `${year}-${month}-${day}`;
}

/**
* تنسيق المبالغ المالية
* @param {number} amount المبلغ
* @returns {string} المبلغ المنسق
*/
function formatCurrency(amount) {
    return parseFloat(amount).toFixed(2) + ' ' + (typeof currencySymbol !== 'undefined' ? currencySymbol : 'ر.س');
}

/**
* الحصول على اسم الشهر
* @param {number} month رقم الشهر (1-12)
* @returns {string} اسم الشهر
*/
function getMonthName(month) {
const months = [
    'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
    'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
];

return months[month - 1] || '';
}

/**
* عرض مؤشر التحميل
*/
function showLoading() {
// تحقق مما إذا كان هناك مؤشر تحميل موجود بالفعل
if ($('#loading-spinner').length === 0) {
    const spinner = `
    <div id="loading-spinner" class="loading-overlay">
        <div class="spinner-border text-primary" role="status">
            <span class="sr-only">جاري التحميل...</span>
        </div>
    </div>
    `;

    $('body').append(spinner);
}

$('#loading-spinner').show();
}

/**
* إخفاء مؤشر التحميل
*/
function hideLoading() {
$('#loading-spinner').hide();
}

/**
* عرض رسالة للمستخدم
* @param {string} message نص الرسالة
* @param {string} type نوع الرسالة (success, error, info, warning)
*/
function showMessage(message, type = 'info') {
// تحويل النوع إلى صنف Bootstrap
let alertClass = 'alert-info';

switch (type) {
    case 'success':
        alertClass = 'alert-success';
        break;
    case 'error':
        alertClass = 'alert-danger';
        break;
    case 'warning':
        alertClass = 'alert-warning';
        break;
}

// إنشاء عنصر التنبيه
const alertHtml = `
<div class="alert ${alertClass} alert-dismissible fade show">
    ${message}
    <button type="button" class="close" data-dismiss="alert" aria-label="Close">
        <span aria-hidden="true">&times;</span>
    </button>
</div>
`;

// إضافة التنبيه للصفحة
const alertContainer = $('#alerts-container');

if (alertContainer.length) {
    alertContainer.append(alertHtml);

    // إزالة التنبيه بعد 5 ثوانٍ
    setTimeout(() => {
        alertContainer.find('.alert').first().alert('close');
    }, 5000);
} else {
    // إذا لم يكن هناك حاوية للتنبيهات، استخدم تنبيه منبثق من Toastr إن كان متاح
    if (typeof toastr !== 'undefined') {
        toastr[type](message);
    } else {
        alert(message);
    }
}
}

// دالة لفتح مودال تعديل الموظف
function openEditEmployeeModal(employeeId) {
    // عرض مؤشر التحميل
    showLoading();

    // طلب بيانات الموظف من الخادم
    $.ajax({
        url: '../../api/employees.php',
        type: 'POST',
        data: {
            action: 'get_employee',
            employee_id: employeeId
        },
        dataType: 'json',
        success: function(response) {
            hideLoading();

            if (response.status === 'success' && response.employee) {
                const employee = response.employee;

                // تعبئة النموذج ببيانات الموظف
                $('#edit_employee_id').val(employee.id);
                $('#edit_name').val(employee.name);
                $('#edit_phone').val(employee.phone);
                $('#edit_email').val(employee.email || '');
                $('#edit_position').val(employee.position);
                $('#edit_salary_type').val(employee.salary_type).trigger('change');
                $('#edit_fixed_salary').val(employee.fixed_salary || 0);
                $('#edit_commission_percentage').val(employee.commission_percentage || 0);

                // إذا كان المستخدم مدير، قم بتعبئة الفرع والحالة
                if ($('#edit_branch_id').length) {
                    $('#edit_branch_id').val(employee.branch_id);
                    $('#edit_is_active').val(employee.is_active);
                }

                // تعبئة الخدمات
                if (employee.services && employee.services.length > 0) {
                    $('#edit_services').val(employee.services).trigger('change');
                } else {
                    $('#edit_services').val(null).trigger('change');
                }

                // إعادة ضبط حالة حساب المستخدم
                $('#edit_create_user').prop('checked', false);
                $('#edit_userAccountSection').hide();
                $('#edit_username').val('');
                $('#edit_password').val('');

                // إذا كان لديه حساب مستخدم، املأ اسم المستخدم
                if (employee.has_user_account) {
                    $('#edit_username').val(employee.username || '');
                }

                // عرض المودال
                $('#editEmployeeModal').modal('show');
            } else {
                showAlert(response.message || 'حدث خطأ أثناء تحميل بيانات الموظف', 'danger');
            }
        },
        error: function() {
            hideLoading();
            showAlert('حدث خطأ في الاتصال بالخادم', 'danger');
        }
    });
}

// إضافة معالج النقر على زر التعديل في الجدول
$(document).on('click', '.btn-edit-employee', function() {
    const employeeId = $(this).data('id');
    openEditEmployeeModal(employeeId);
}
