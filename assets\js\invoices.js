/**
 * ملف التعامل مع الفواتير
 * يحتوي على دوال الاستعلام وإنشاء وتعديل وحذف الفواتير
 * @version 1.0.0
 */

/**
 * كائن إدارة الفواتير
 */
const InvoiceManager = {
    // ثوابت API
    endpoints: {
        LIST: 'invoices.php?action=list',
        VIEW: 'invoices.php?action=view',
        CREATE: 'invoices.php?action=create',
        UPDATE: 'invoices.php?action=update',
        DELETE: 'invoices.php?action=delete',
        CANCEL: 'invoices.php?action=cancel',
        PRINT: 'invoices.php?action=print',
        APPLY_DISCOUNT: 'invoices.php?action=apply-discount',
        REPORT: 'invoices.php?action=report'
    },

    /**
     * تهيئة الصفحة
     */
    init: function() {
        // تهيئة الجداول
        if ($("#invoices-table").length) {
            this.initInvoicesTable();
        }

        // تهيئة نماذج الفواتير
        if ($("#invoice-form").length) {
            this.initInvoiceForms();
        }

        // تحميل الفاتورة للطباعة
        if ($("#print-invoice").length) {
            const invoiceId = $("#print-invoice").data("id");
            this.loadInvoiceForPrinting(invoiceId);
        }

        // إذا كنا في صفحة تعديل الفاتورة
        if ($("#invoice-form").length && $("#invoice-id").val()) {
            this.loadInvoiceForEdit($("#invoice-id").val());
        }
    },

    /**
     * تهيئة جدول الفواتير
     */
    initInvoicesTable: function() {
        // تهيئة عناصر التصفية
        $("#filter-form").on("submit", (e) => {
            e.preventDefault();
            this.loadInvoices();
        });

        // تهيئة عنصر البحث
        $("#search-input").on("keyup", this.debounce(() => {
            this.loadInvoices();
        }, 500));

        // تحميل الفواتير عند التحميل الأولي
        this.loadInvoices();

        // معالجة أحداث الحذف والعرض
        $("#invoices-table").on("click", ".btn-delete", (e) => {
            const invoiceId = $(e.currentTarget).data("id");
            this.confirmDeleteInvoice(invoiceId);
        });

        $("#invoices-table").on("click", ".btn-view", (e) => {
            const invoiceId = $(e.currentTarget).data("id");
            this.viewInvoice(invoiceId);
        });

        $("#invoices-table").on("click", ".btn-print", (e) => {
            const invoiceId = $(e.currentTarget).data("id");
            this.printInvoice(invoiceId);
        });

        $("#invoices-table").on("click", ".btn-cancel", (e) => {
            const invoiceId = $(e.currentTarget).data("id");
            this.confirmCancelInvoice(invoiceId);
        });
    },

    /**
     * تحميل قائمة الفواتير
     */
    loadInvoices: function() {
        const filters = {
            search: $("#search-input").val(),
            customer_id: $("#filter-customer").val(),
            employee_id: $("#filter-employee").val(),
            branch_id: $("#filter-branch").val(),
            payment_method: $("#filter-payment-method").val(),
            payment_status: $("#filter-payment-status").val(),
            start_date: $("#filter-start-date").val(),
            end_date: $("#filter-end-date").val()
        };

        // إظهار رسالة التحميل
        $("#invoices-table tbody").html('<tr><td colspan="9" class="text-center">جاري التحميل...</td></tr>');

        // استدعاء API لاسترجاع الفواتير
        AjaxHandler.get(this.endpoints.LIST, filters)
            .then(response => {
                this.displayInvoices(response.invoices);
                this.updateInvoiceSummary(response.total_count, response.total_sales);
            })
            .catch(() => {
                $("#invoices-table tbody").html('<tr><td colspan="9" class="text-center">حدث خطأ أثناء تحميل البيانات</td></tr>');
            });
    },

    /**
     * عرض الفواتير في الجدول
     * @param {Array} invoices مصفوفة الفواتير
     */
    displayInvoices: function(invoices) {
        if (!invoices || invoices.length === 0) {
            $("#invoices-table tbody").html('<tr><td colspan="9" class="text-center">لا توجد فواتير</td></tr>');
            return;
        }

        let html = '';

        invoices.forEach(function(invoice) {
            html += `
                <tr>
                    <td>${invoice.invoice_number}</td>
                    <td>${InvoiceManager.formatDate(invoice.created_at)}</td>
                    <td>${invoice.customer_name || 'زبون عام'}</td>
                    <td>${invoice.employee_name || '-'}</td>
                    <td>${InvoiceManager.formatCurrency(invoice.final_amount)}</td>
                    <td><span class="badge ${InvoiceManager.getPaymentMethodClass(invoice.payment_method)}">${InvoiceManager.getPaymentMethodText(invoice.payment_method)}</span></td>
                    <td><span class="badge ${InvoiceManager.getPaymentStatusClass(invoice.payment_status)}">${InvoiceManager.getPaymentStatusText(invoice.payment_status)}</span></td>
                    <td>${invoice.branch_name}</td>
                    <td>
                        <div class="btn-group">
                            <button type="button" class="btn btn-sm btn-info btn-view" data-id="${invoice.id}" title="عرض"><i class="fas fa-eye"></i></button>
                            <button type="button" class="btn btn-sm btn-primary btn-print" data-id="${invoice.id}" title="طباعة"><i class="fas fa-print"></i></button>
                            ${invoice.can_edit ? `<button type="button" class="btn btn-sm btn-warning" onclick="location.href='edit.php?id=${invoice.id}'" title="تعديل"><i class="fas fa-edit"></i></button>` : ''}
                            ${invoice.can_cancel ? `<button type="button" class="btn btn-sm btn-danger btn-cancel" data-id="${invoice.id}" title="إلغاء"><i class="fas fa-ban"></i></button>` : ''}
                            ${invoice.can_delete ? `<button type="button" class="btn btn-sm btn-danger btn-delete" data-id="${invoice.id}" title="حذف"><i class="fas fa-trash"></i></button>` : ''}
                        </div>
                    </td>
                </tr>
            `;
        });

        $("#invoices-table tbody").html(html);
    },

    /**
     * تحديث ملخص الفواتير
     * @param {Number} totalCount إجمالي عدد الفواتير
     * @param {Number} totalSales إجمالي المبيعات
     */
    updateInvoiceSummary: function(totalCount, totalSales) {
        $("#total-invoices").text(totalCount);
        $("#total-sales").text(this.formatCurrency(totalSales));
    },

    /**
     * عرض تفاصيل فاتورة
     * @param {Number} invoiceId معرف الفاتورة
     */
    viewInvoice: function(invoiceId) {
        // استدعاء API لاسترجاع تفاصيل الفاتورة
        AjaxHandler.get(this.endpoints.VIEW, { id: invoiceId })
            .then(response => {
                this.displayInvoiceDetails(response.invoice);
                $("#invoice-details-modal").modal("show");
            });
    },

    /**
     * عرض تفاصيل الفاتورة في نافذة منبثقة
     * @param {Object} invoice كائن الفاتورة
     */
    displayInvoiceDetails: function(invoice) {
        // عرض معلومات الفاتورة الرئيسية
        $("#modal-invoice-number").text(invoice.invoice_number);
        $("#modal-invoice-date").text(this.formatDate(invoice.created_at));
        $("#modal-customer-name").text(invoice.customer_name || 'زبون عام');
        $("#modal-employee-name").text(invoice.employee_name || '-');
        $("#modal-cashier-name").text(invoice.cashier_name);
        $("#modal-branch-name").text(invoice.branch_name);

        // عرض المبالغ
        $("#modal-total-amount").text(this.formatCurrency(invoice.total_amount));
        $("#modal-discount-amount").text(this.formatCurrency(invoice.discount_amount));
        $("#modal-tax-amount").text(this.formatCurrency(invoice.tax_amount));
        $("#modal-final-amount").text(this.formatCurrency(invoice.final_amount));
        $("#modal-payment-method").html(`<span class="badge ${this.getPaymentMethodClass(invoice.payment_method)}">${this.getPaymentMethodText(invoice.payment_method)}</span>`);
        $("#modal-payment-status").html(`<span class="badge ${this.getPaymentStatusClass(invoice.payment_status)}">${this.getPaymentStatusText(invoice.payment_status)}</span>`);

        // عرض العناصر في جدول
        let itemsHtml = '';
        invoice.items.forEach(function(item, index) {
            itemsHtml += `
                <tr>
                    <td>${index + 1}</td>
                    <td>${item.name}</td>
                    <td>${item.item_type === 'service' ? 'خدمة' : 'منتج'}</td>
                    <td>${InvoiceManager.formatCurrency(item.price)}</td>
                    <td>${item.quantity}</td>
                    <td>${InvoiceManager.formatCurrency(item.discount || 0)}</td>
                    <td>${InvoiceManager.formatCurrency(item.total)}</td>
                </tr>
            `;
        });

        $("#modal-invoice-items").html(itemsHtml);

        // عرض الملاحظات
        $("#modal-invoice-notes").text(invoice.notes || '-');
    },

    /**
     * تأكيد حذف فاتورة
     * @param {Number} invoiceId معرف الفاتورة
     */
    confirmDeleteInvoice: function(invoiceId) {
        if (confirm("هل أنت متأكد من حذف هذه الفاتورة؟ هذا الإجراء لا يمكن التراجع عنه.")) {
            this.deleteInvoice(invoiceId);
        }
    },

    /**
     * حذف فاتورة
     * @param {Number} invoiceId معرف الفاتورة
     */
    deleteInvoice: function(invoiceId) {
        AjaxHandler.delete(this.endpoints.DELETE, { id: invoiceId })
            .then(response => {
                AjaxHandler.showAlert(response.message, 'success');
                this.loadInvoices();
            });
    },

    /**
     * تأكيد إلغاء فاتورة
     * @param {Number} invoiceId معرف الفاتورة
     */
    confirmCancelInvoice: function(invoiceId) {
        if (confirm("هل أنت متأكد من إلغاء هذه الفاتورة؟ هذا سيؤدي إلى إعادة المنتجات للمخزون.")) {
            this.cancelInvoice(invoiceId);
        }
    },

    /**
     * إلغاء فاتورة
     * @param {Number} invoiceId معرف الفاتورة
     */
    cancelInvoice: function(invoiceId) {
        AjaxHandler.post(this.endpoints.CANCEL, { invoice_id: invoiceId })
            .then(response => {
                AjaxHandler.showAlert(response.message, 'success');
                this.loadInvoices();
            });
    },

    /**
     * طباعة الفاتورة
     * @param {Number} invoiceId معرف الفاتورة
     */
    printInvoice: function(invoiceId) {
        // فتح صفحة الطباعة في نافذة جديدة
        window.open(`print.php?id=${invoiceId}`, '_blank', 'width=800,height=600');
    },

    /**
     * تهيئة نماذج الفواتير (إنشاء/تعديل)
     */
    initInvoiceForms: function() {
        // تهيئة عناصر الاختيار
        this.initSelectElements();

        // تهيئة جدول العناصر
        if ($("#invoice-items-table").length) {
            this.initItemsTable();
        }

        // معالجة إضافة عنصر جديد
        $("#add-item-form").on("submit", (e) => {
            e.preventDefault();
            this.addItemToInvoice();
        });

        // معالجة حفظ الفاتورة
        $("#invoice-form").on("submit", (e) => {
            e.preventDefault();
            this.saveInvoice();
        });

        // التحقق من توفر المنتج عند التغيير
        $("#item-select").on("change", () => {
            this.checkProductAvailability();
        });

        // تحديث الإجمالي عند تغير الكمية أو السعر
        $("#item-price, #item-quantity").on("input", () => {
            this.updateItemTotal();
        });

        // تحديث المبلغ النهائي عند تغير الخصم أو الضريبة
        $("#discount-amount, #tax-amount").on("input", () => {
            this.calculateFinalAmount();
        });

        // معالجة تغيير نوع الخصم
        $("#discount-type").on("change", () => {
            this.calculateFinalAmount();
        });
    },

    /**
     * تهيئة عناصر الاختيار
     */
    initSelectElements: function() {
        // تهيئة اختيار العميل
        if ($("#customer-select").length) {
            $("#customer-select").select2({
                placeholder: "اختر العميل أو اتركه فارغًا لزبون عام",
                allowClear: true,
                ajax: {
                    url: AjaxHandler.defaultConfig.baseUrl + "customers.php?action=search",
                    dataType: "json",
                    delay: 250,
                    data: function(params) {
                        return {
                            search: params.term
                        };
                    },
                    processResults: function(data) {
                        return {
                            results: data.customers.map(function(customer) {
                                return { id: customer.id, text: customer.name + " - " + customer.phone };
                            })
                        };
                    },
                    cache: true
                }
            });
        }

        // تهيئة اختيار الموظف
        if ($("#employee-select").length) {
            $("#employee-select").select2({
                placeholder: "اختر الموظف",
                allowClear: true,
                ajax: {
                    url: AjaxHandler.defaultConfig.baseUrl + "employees.php?action=search",
                    dataType: "json",
                    delay: 250,
                    data: function(params) {
                        return {
                            search: params.term,
                            is_active: 1
                        };
                    },
                    processResults: function(data) {
                        return {
                            results: data.employees.map(function(employee) {
                                return { id: employee.id, text: employee.name };
                            })
                        };
                    },
                    cache: true
                }
            });
        }

        // تهيئة اختيار الخدمات والمنتجات
        if ($("#item-select").length) {
            $("#item-select").select2({
                placeholder: "اختر خدمة أو منتج",
                allowClear: false,
                ajax: {
                    url: AjaxHandler.defaultConfig.baseUrl + "services.php?action=search-items",
                    dataType: "json",
                    delay: 250,
                    data: function(params) {
                        return {
                            search: params.term,
                            type: $("#item-type").val()
                        };
                    },
                    processResults: function(data) {
                        let results = [];

                        // إضافة الخدمات
                        if (data.services) {
                            results = results.concat(data.services.map(function(service) {
                                return {
                                    id: service.id,
                                    text: service.name,
                                    price: service.price,
                                    type: 'service'
                                };
                            }));
                        }

                        // إضافة المنتجات
                        if (data.products) {
                            results = results.concat(data.products.map(function(product) {
                                return {
                                    id: product.id,
                                    text: product.name,
                                    price: product.price,
                                    type: 'product',
                                    stock: product.stock
                                };
                            }));
                        }

                        return { results: results };
                    },
                    cache: true
                }
            }).on('select2:select', function(e) {
                const data = e.params.data;
                $("#item-type").val(data.type);
                $("#item-price").val(data.price);
                InvoiceManager.updateItemTotal();
            });
        }
    },

    /**
     * تهيئة جدول عناصر الفاتورة
     */
    initItemsTable: function() {
        // معالجة إزالة عنصر
        $("#invoice-items-table").on("click", ".btn-remove-item", function() {
            $(this).closest("tr").remove();
            InvoiceManager.calculateTotalAmount();
        });

        // معالجة تحديث كمية العنصر
        $("#invoice-items-table").on("change", ".item-quantity", function() {
            const row = $(this).closest("tr");
            const quantity = parseInt($(this).val()) || 1;
            const price = parseFloat(row.find(".item-price").text().replace(/[^\d.-]/g, ''));
            const total = quantity * price;

            row.find(".item-total").text(InvoiceManager.formatCurrency(total));
            row.find("input[name='items[][total]']").val(total);
            row.find("input[name='items[][quantity]']").val(quantity);

            InvoiceManager.calculateTotalAmount();
        });

        // معالجة تحديث سعر العنصر
        $("#invoice-items-table").on("change", ".item-price-input", function() {
            const row = $(this).closest("tr");
            const price = parseFloat($(this).val()) || 0;
            const quantity = parseInt(row.find(".item-quantity").val()) || 1;
            const total = price * quantity;

            row.find(".item-price").text(InvoiceManager.formatCurrency(price));
            row.find(".item-total").text(InvoiceManager.formatCurrency(total));
            row.find("input[name='items[][price]']").val(price);
            row.find("input[name='items[][total]']").val(total);

            InvoiceManager.calculateTotalAmount();
        });
    },

    /**
     * إضافة عنصر للفاتورة
     */
    addItemToInvoice: function() {
        const itemSelect = $("#item-select");
        const itemId = itemSelect.val();
        const itemType = $("#item-type").val();
        const employeeId = $("#employee-select").val();
        const quantity = parseInt($("#item-quantity").val()) || 1;
        const price = parseFloat($("#item-price").val()) || 0;
        const total = price * quantity;

        if (!itemId || !itemType) {
            AjaxHandler.showAlert("يرجى اختيار عنصر", "warning");
            return;
        }

        if (price <= 0) {
            AjaxHandler.showAlert("يرجى إدخال سعر صحيح", "warning");
            return;
        }

        if (quantity <= 0) {
            AjaxHandler.showAlert("يرجى إدخال كمية صحيحة", "warning");
            return;
        }

        // التحقق من عدم وجود العنصر مسبقًا
        const existingItem = $(`input[name='items[][item_id]'][value='${itemId}'][data-type='${itemType}']`);
        if (existingItem.length && itemType === 'service') {
            // زيادة الكمية للعنصر الموجود مسبقًا (للخدمات فقط)
            const row = existingItem.closest("tr");
            const existingQuantity = parseInt(row.find(".item-quantity").val()) || 1;
            const newQuantity = existingQuantity + quantity;

            row.find(".item-quantity").val(newQuantity);
            const newTotal = price * newQuantity;

            row.find(".item-total").text(this.formatCurrency(newTotal));
            row.find("input[name='items[][quantity]']").val(newQuantity);
            row.find("input[name='items[][total]']").val(newTotal);
        } else {
            // إضافة صف جديد للجدول
            const itemData = itemSelect.select2('data')[0];
            const employeeData = $("#employee-select").select2('data')[0];
            const employeeName = employeeData ? employeeData.text : '-';

            const row = `
                <tr>
                    <td>
                        ${itemData.text}
                        <input type="hidden" name="items[][item_id]" value="${itemId}" data-type="${itemType}">
                        <input type="hidden" name="items[][item_type]" value="${itemType}">
                    </td>
                    <td>
                        ${itemType === 'service' ? 'خدمة' : 'منتج'}
                    </td>
                    <td>
                        <span class="item-price">${this.formatCurrency(price)}</span>
                        <input type="hidden" name="items[][price]" value="${price}">
                    </td>
                    <td>
                        <input type="number" class="form-control form-control-sm item-quantity" name="items[][quantity]" value="${quantity}" min="1" max="${itemType === 'product' ? itemData.stock || 999 : 999}">
                    </td>
                    <td>
                        <span class="item-total">${this.formatCurrency(total)}</span>
                        <input type="hidden" name="items[][total]" value="${total}">
                    </td>
                    <td>
                        ${itemType === 'service' ? employeeName : '-'}
                        ${itemType === 'service' ? `<input type="hidden" name="items[][employee_id]" value="${employeeId || ''}">` : ''}
                    </td>
                    <td>
                        <button type="button" class="btn btn-sm btn-danger btn-remove-item"><i class="fas fa-times"></i></button>
                    </td>
                </tr>
            `;

            $("#invoice-items-table tbody").append(row);
        }

        // إعادة ضبط نموذج إضافة العنصر
        itemSelect.val(null).trigger('change');
        $("#item-price").val('');
        $("#item-quantity").val('1');
        $("#item-type").val('');

        // حساب الإجمالي
        this.calculateTotalAmount();

        // التركيز على اختيار عنصر جديد
        itemSelect.focus();
    },

    /**
     * التحقق من توفر المنتج في المخزون
     */
    checkProductAvailability: function() {
        const itemSelect = $("#item-select");
        if (!itemSelect.val()) return;

        const itemData = itemSelect.select2('data')[0];

        // التحقق فقط إذا كان المنتج
        if (itemData && itemData.type === 'product') {
            const quantity = parseInt($("#item-quantity").val()) || 1;
            const branchId = $("#branch-id").val();

            AjaxHandler.get('inventory.php?action=check-availability', {
                product_id: itemData.id,
                quantity: quantity,
                branch_id: branchId
            })
            .then(response => {
                if (!response.available) {
                    AjaxHandler.showAlert(`المنتج غير متوفر بالكمية المطلوبة. الكمية المتاحة: ${response.available_quantity}`, "warning");
                    $("#item-quantity").val(response.available_quantity);
                    this.updateItemTotal();
                }
            });
        }
    },

    /**
     * تحديث إجمالي العنصر
     */
    updateItemTotal: function() {
        const price = parseFloat($("#item-price").val()) || 0;
        const quantity = parseInt($("#item-quantity").val()) || 1;
        const total = price * quantity;

        $("#item-total").text(this.formatCurrency(total));
    },

    /**
     * حساب المبلغ الإجمالي للفاتورة
     */
    calculateTotalAmount: function() {
        let totalAmount = 0;

        // جمع مبالغ كل العناصر
        $("#invoice-items-table tbody tr").each(function() {
            const total = parseFloat($(this).find("input[name='items[][total]']").val()) || 0;
            totalAmount += total;
        });

        // تحديث حقل المبلغ الإجمالي
        $("#total-amount").val(totalAmount.toFixed(2));
        $("#total-amount-display").text(this.formatCurrency(totalAmount));

        // حساب المبلغ النهائي
        this.calculateFinalAmount();
    },

    /**
     * حساب المبلغ النهائي بعد الخصم والضريبة
     */
    calculateFinalAmount: function() {
        const totalAmount = parseFloat($("#total-amount").val()) || 0;
        const discountAmount = parseFloat($("#discount-amount").val()) || 0;
        const discountType = $("#discount-type").val();
        const taxAmount = parseFloat($("#tax-amount").val()) || 0;

        let discount = 0;
        if (discountType === 'percentage') {
            discount = (totalAmount * discountAmount / 100);
            $("#discount-amount-display").text(`${discountAmount}% (${this.formatCurrency(discount)})`);
        } else {
            discount = discountAmount;
            $("#discount-amount-display").text(this.formatCurrency(discount));
        }

        const afterDiscount = totalAmount - discount;
        const tax = (afterDiscount * taxAmount / 100);
        const finalAmount = afterDiscount + tax;

        $("#tax-amount-display").text(`${taxAmount}% (${this.formatCurrency(tax)})`);
        $("#final-amount").val(finalAmount.toFixed(2));
        $("#final-amount-display").text(this.formatCurrency(finalAmount));
    },

    /**
     * حفظ الفاتورة (إنشاء أو تحديث)
     */
    saveInvoice: function() {
        // التحقق من وجود عناصر في الفاتورة
        if ($("#invoice-items-table tbody tr").length === 0) {
            AjaxHandler.showAlert("يجب إضافة عناصر للفاتورة", "warning");
            return false;
        }

        // جمع بيانات الفاتورة
        const invoiceId = $("#invoice-id").val();
        const isEdit = invoiceId ? true : false;

        // تجميع بيانات النموذج
        const formData = $("#invoice-form").serializeArray();
        const invoiceData = {};

        // تحويل البيانات إلى كائن
        formData.forEach(item => {
            if (item.name.includes('[]')) {
                // معالجة العناصر المصفوفة
                const name = item.name.replace('[]', '');
                if (!invoiceData[name]) {
                    invoiceData[name] = [];
                }
                invoiceData[name].push(item.value);
            }
            else {
                invoiceData[item.name] = item.value;
            }
        });

        // تحويل عناصر الفاتورة إلى الصيغة المطلوبة
        const items = [];
        $("#invoice-items-table tbody tr").each(function() {
            const item = {
                item_id: $(this).find("input[name='items[][item_id]']").val(),
                item_type: $(this).find("input[name='items[][item_type]']").val(),
                price: parseFloat($(this).find("input[name='items[][price]']").val()),
                quantity: parseInt($(this).find("input[name='items[][quantity]']").val()),
                total: parseFloat($(this).find("input[name='items[][total]']").val())
            };

            if (item.item_type === 'service') {
                item.employee_id = $(this).find("input[name='items[][employee_id]']").val() || null;
            }

            items.push(item);
        });

        // تحديث المصفوفة
        invoiceData.items = items;

        // إظهار مؤشر التحميل
        $("#save-invoice-btn").prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> جاري الحفظ...');

        // تحديد نوع الطلب ومساره
        const endpoint = isEdit ? InvoiceManager.endpoints.UPDATE : InvoiceManager.endpoints.CREATE;
        const method = isEdit ? 'put' : 'post';

        // إرسال الطلب
        AjaxHandler[method](endpoint, invoiceData)
            .then(response => {
                AjaxHandler.showAlert(response.message, "success");

                if (!isEdit) {
                    // إعادة التوجيه لطباعة الفاتورة
                    window.location.href = `print.php?id=${response.invoice_id}&auto_print=1`;
                } else {
                    // إعادة التوجيه لصفحة قائمة الفواتير
                    window.location.href = "index.php";
                }
            })
            .catch(error => {
                $("#save-invoice-btn").prop('disabled', false).html('<i class="fas fa-save"></i> حفظ الفاتورة');
            });
    },

    /**
     * تحميل الفاتورة للطباعة
     * @param {Number} invoiceId معرف الفاتورة
     */
    loadInvoiceForPrinting: function(invoiceId) {
        AjaxHandler.get(this.endpoints.PRINT, { id: invoiceId })
            .then(response => {
                this.renderInvoiceForPrinting(response.invoice);
            });
    },

    /**
     * عرض الفاتورة للطباعة
     * @param {Object} invoice كائن الفاتورة
     */
    renderInvoiceForPrinting: function(invoice) {
        // عرض معلومات الفاتورة الرئيسية
        $("#print-invoice-number").text(invoice.invoice_number);
        $("#print-invoice-date").text(this.formatDate(invoice.created_at));
        $("#print-customer-name").text(invoice.customer_name || 'زبون عام');
        $("#print-customer-phone").text(invoice.customer_phone || '-');
        $("#print-employee-name").text(invoice.employee_name || '-');
        $("#print-cashier-name").text(invoice.cashier_name);

        // عرض العناصر في جدول
        let itemsHtml = '';
        invoice.items.forEach(function(item, index) {
            itemsHtml += `
                <tr>
                    <td>${index + 1}</td>
                    <td>${item.name}</td>
                    <td>${InvoiceManager.formatCurrency(item.price)}</td>
                    <td>${item.quantity}</td>
                    <td>${InvoiceManager.formatCurrency(item.total)}</td>
                </tr>
            `;
        });

        $("#print-invoice-items").html(itemsHtml);

        // عرض المبالغ
        $("#print-total-amount").text(this.formatCurrency(invoice.total_amount));
        $("#print-discount-amount").text(this.formatCurrency(invoice.discount_amount));
        $("#print-tax-amount").text(this.formatCurrency(invoice.tax_amount));
        $("#print-final-amount").text(this.formatCurrency(invoice.final_amount));
        $("#print-payment-method").text(this.getPaymentMethodText(invoice.payment_method));

        // عرض الملاحظات
        if (invoice.notes) {
            $("#print-notes").text(invoice.notes).parent().show();
        } else {
            $("#print-notes").parent().hide();
        }

        // إظهار زر الطباعة
        $("#print-button").show();

        // طباعة تلقائية إذا كان مطلوبًا
        if ($("#print-invoice").data("auto-print")) {
            setTimeout(function() {
                window.print();
            }, 500);
        }
    },

    /**
     * تحميل الفاتورة للتعديل
     * @param {Number} invoiceId معرف الفاتورة
     */
    loadInvoiceForEdit: function(invoiceId) {
        AjaxHandler.get(this.endpoints.VIEW, { id: invoiceId })
            .then(response => {
                this.populateInvoiceForm(response.invoice);
            });
    },

    /**
     * ملء نموذج الفاتورة للتعديل
     * @param {Object} invoice كائن الفاتورة
     */
    populateInvoiceForm: function(invoice) {
        // ملء حقول النموذج
        $("#invoice-id").val(invoice.id);

        // ملء بيانات العميل
        if (invoice.customer_id) {
            // إضافة خيار العميل
            const customerOption = new Option(invoice.customer_name, invoice.customer_id, true, true);
            $("#customer-select").append(customerOption).trigger('change');
        }

        // ملء بيانات الموظف
        if (invoice.employee_id) {
            // إضافة خيار الموظف
            const employeeOption = new Option(invoice.employee_name, invoice.employee_id, true, true);
            $("#employee-select").append(employeeOption).trigger('change');
        }

        // ملء بيانات الفرع
        $("#branch-id").val(invoice.branch_id);

        // ملء بيانات الدفع
        $("#payment-method").val(invoice.payment_method);
        $("#payment-status").val(invoice.payment_status);

        // ملء بيانات المبالغ
        $("#total-amount").val(invoice.total_amount);
        $("#discount-amount").val(invoice.discount_type === 'percentage' ? invoice.discount_amount : invoice.discount_amount.toFixed(2));
        $("#discount-type").val(invoice.discount_type || 'amount');
        $("#tax-amount").val(invoice.tax_amount);
        $("#final-amount").val(invoice.final_amount);

        // تحديث العرض
        $("#total-amount-display").text(this.formatCurrency(invoice.total_amount));
        this.calculateFinalAmount();

        // ملء الملاحظات
        $("#notes").val(invoice.notes);

        // ملء جدول العناصر
        $("#invoice-items-table tbody").empty();

        if (invoice.items && invoice.items.length > 0) {
            invoice.items.forEach(function(item) {
                // بناء صف في الجدول
                const row = `
                    <tr>
                        <td>
                            ${item.name}
                            <input type="hidden" name="items[][item_id]" value="${item.item_id}" data-type="${item.item_type}">
                            <input type="hidden" name="items[][item_type]" value="${item.item_type}">
                        </td>
                        <td>
                            ${item.item_type === 'service' ? 'خدمة' : 'منتج'}
                        </td>
                        <td>
                            <span class="item-price">${InvoiceManager.formatCurrency(item.price)}</span>
                            <input type="hidden" name="items[][price]" value="${item.price}">
                        </td>
                        <td>
                            <input type="number" class="form-control form-control-sm item-quantity" name="items[][quantity]" value="${item.quantity}" min="1">
                        </td>
                        <td>
                            <span class="item-total">${InvoiceManager.formatCurrency(item.total)}</span>
                            <input type="hidden" name="items[][total]" value="${item.total}">
                        </td>
                        <td>
                            ${item.employee_name || '-'}
                            ${item.item_type === 'service' && item.employee_id ? `<input type="hidden" name="items[][employee_id]" value="${item.employee_id}">` : ''}
                        </td>
                        <td>
                            <button type="button" class="btn btn-sm btn-danger btn-remove-item"><i class="fas fa-times"></i></button>
                        </td>
                    </tr>
                `;

                // إضافة الصف للجدول
                $("#invoice-items-table tbody").append(row);
            });
        }
    },

    /**
     * تطبيق خصم على فاتورة
     * @param {Number} invoiceId معرف الفاتورة
     * @param {Number} discountAmount مبلغ الخصم
     * @param {String} discountType نوع الخصم (amount, percentage)
     */
    applyDiscount: function(invoiceId, discountAmount, discountType) {
        AjaxHandler.post(this.endpoints.APPLY_DISCOUNT, {
            invoice_id: invoiceId,
            discount_amount: discountAmount,
            discount_type: discountType
        })
        .then(response => {
            AjaxHandler.showAlert(response.message, 'success');
            this.viewInvoice(invoiceId); // إعادة تحميل تفاصيل الفاتورة
        });
    },

    /**
     * إنشاء تقرير المبيعات
     * @param {Object} filters معلمات التصفية
     */
    generateSalesReport: function(filters) {
        AjaxHandler.get(this.endpoints.REPORT, filters)
            .then(response => {
                this.displaySalesReport(response.report);
            });
    },

    /**
     * عرض تقرير المبيعات
     * @param {Object} report كائن التقرير
     */
    displaySalesReport: function(report) {
        // عرض الإحصائيات العامة
        $("#total-sales-amount").text(this.formatCurrency(report.total_sales));
        $("#total-invoices-count").text(report.total_invoices);
        $("#average-invoice-amount").text(this.formatCurrency(report.average_invoice));

        // عرض توزيع طرق الدفع
        const paymentMethodsData = [];
        for (const method in report.payment_methods) {
            if (report.payment_methods.hasOwnProperty(method)) {
                paymentMethodsData.push({
                    name: this.getPaymentMethodText(method),
                    value: report.payment_methods[method]
                });
            }
        }

        // عرض رسم بياني لطرق الدفع
        if ($("#payment-methods-chart").length) {
            this.renderPieChart("#payment-methods-chart", paymentMethodsData, "طرق الدفع");
        }

        // عرض المبيعات حسب الفترة
        if (report.sales_by_period && $("#sales-by-period-chart").length) {
            this.renderLineChart("#sales-by-period-chart", report.sales_by_period, "المبيعات حسب الفترة");
        }

        // عرض أفضل المنتجات مبيعًا
        if (report.top_products && $("#top-products").length) {
            let html = '';
            report.top_products.forEach((product, index) => {
                html += `
                    <tr>
                        <td>${index + 1}</td>
                        <td>${product.name}</td>
                        <td>${product.quantity}</td>
                        <td>${this.formatCurrency(product.total)}</td>
                    </tr>
                `;
            });
            $("#top-products tbody").html(html);
        }

        // عرض أفضل الخدمات مبيعًا
        if (report.top_services && $("#top-services").length) {
            let html = '';
            report.top_services.forEach((service, index) => {
                html += `
                    <tr>
                        <td>${index + 1}</td>
                        <td>${service.name}</td>
                        <td>${service.quantity}</td>
                        <td>${this.formatCurrency(service.total)}</td>
                    </tr>
                `;
            });
            $("#top-services tbody").html(html);
        }

        // عرض أفضل الموظفين
        if (report.top_employees && $("#top-employees").length) {
            let html = '';
            report.top_employees.forEach((employee, index) => {
                html += `
                    <tr>
                        <td>${index + 1}</td>
                        <td>${employee.name}</td>
                        <td>${employee.invoices_count}</td>
                        <td>${this.formatCurrency(employee.total)}</td>
                    </tr>
                `;
            });
            $("#top-employees tbody").html(html);
        }
    },

    /**
     * رسم مخطط دائري
     * @param {String} selector محدد عنصر HTML
     * @param {Array} data بيانات المخطط
     * @param {String} title عنوان المخطط
     */
    renderPieChart: function(selector, data, title) {
        // التحقق من وجود مكتبة Chart.js
        if (typeof Chart !== 'undefined') {
            // تدمير المخطط السابق إن وجد
            const chartContainer = $(selector)[0];
            const existingChart = Chart.getChart(chartContainer);
            if (existingChart) {
                existingChart.destroy();
            }

            // إنشاء مخطط جديد
            new Chart(chartContainer, {
                type: 'pie',
                data: {
                    labels: data.map(item => item.name),
                    datasets: [{
                        data: data.map(item => item.value),
                        backgroundColor: [
                            'rgba(75, 192, 192, 0.7)',
                            'rgba(54, 162, 235, 0.7)',
                            'rgba(255, 206, 86, 0.7)',
                            'rgba(255, 99, 132, 0.7)',
                            'rgba(153, 102, 255, 0.7)'
                        ],
                        borderColor: [
                            'rgba(75, 192, 192, 1)',
                            'rgba(54, 162, 235, 1)',
                            'rgba(255, 206, 86, 1)',
                            'rgba(255, 99, 132, 1)',
                            'rgba(153, 102, 255, 1)'
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: {
                            position: 'right',
                        },
                        title: {
                            display: true,
                            text: title
                        }
                    }
                }
            });
        }
    },

    /**
     * رسم مخطط خطي
     * @param {String} selector محدد عنصر HTML
     * @param {Array} data بيانات المخطط
     * @param {String} title عنوان المخطط
     */
    renderLineChart: function(selector, data, title) {
        // التحقق من وجود مكتبة Chart.js
        if (typeof Chart !== 'undefined') {
            // تدمير المخطط السابق إن وجد
            const chartContainer = $(selector)[0];
            const existingChart = Chart.getChart(chartContainer);
            if (existingChart) {
                existingChart.destroy();
            }

            // إنشاء مخطط جديد
            new Chart(chartContainer, {
                type: 'line',
                data: {
                    labels: data.map(item => item.label),
                    datasets: [{
                        label: 'المبيعات',
                        data: data.map(item => item.value),
                        backgroundColor: 'rgba(54, 162, 235, 0.2)',
                        borderColor: 'rgba(54, 162, 235, 1)',
                        borderWidth: 2,
                        fill: true,
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        title: {
                            display: true,
                            text: title
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
        }
    },

    // دوال المساعدة

    /**
     * تنسيق التاريخ بالصيغة المحلية
     * @param {String} dateString سلسلة التاريخ
     * @return {String} التاريخ المنسق
     */
    formatDate: function(dateString) {
        if (!dateString) return '';

        const date = new Date(dateString);
        return date.toLocaleDateString('ar-SA', {
            year: 'numeric',
            month: 'numeric',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
    },

    /**
     * تنسيق المبلغ بصيغة العملة
     * @param {Number} amount المبلغ
     * @return {String} المبلغ المنسق
     */
    formatCurrency: function(amount) {
        return parseFloat(amount).toFixed(2) + ' ' + (typeof currencySymbol !== 'undefined' ? currencySymbol : 'ر.س');
    },

    /**
     * الحصول على نص طريقة الدفع
     * @param {String} method طريقة الدفع
     * @return {String} النص المقابل
     */
    getPaymentMethodText: function(method) {
        switch (method) {
            case 'cash': return 'نقدًا';
            case 'card': return 'بطاقة';
            case 'other': return 'أخرى';
            default: return method;
        }
    },

    /**
     * الحصول على صنف طريقة الدفع
     * @param {String} method طريقة الدفع
     * @return {String} صنف CSS
     */
    getPaymentMethodClass: function(method) {
        switch (method) {
            case 'cash': return 'badge-success';
            case 'card': return 'badge-info';
            case 'other': return 'badge-warning';
            default: return 'badge-secondary';
        }
    },

    /**
     * الحصول على نص حالة الدفع
     * @param {String} status حالة الدفع
     * @return {String} النص المقابل
     */
    getPaymentStatusText: function(status) {
        switch (status) {
            case 'paid': return 'مدفوع';
            case 'partial': return 'مدفوع جزئيًا';
            case 'unpaid': return 'غير مدفوع';
            default: return status;
        }
    },

    /**
     * الحصول على صنف حالة الدفع
     * @param {String} status حالة الدفع
     * @return {String} صنف CSS
     */
    getPaymentStatusClass: function(status) {
        switch (status) {
            case 'paid': return 'badge-success';
            case 'partial': return 'badge-warning';
            case 'unpaid': return 'badge-danger';
            default: return 'badge-secondary';
        }
    },

    /**
     * دالة تأخير لتقليل عدد الاستدعاءات المتكررة
     * @param {Function} func الدالة المراد تأخيرها
     * @param {Number} wait وقت الانتظار بالمللي ثانية
     * @return {Function} دالة مؤجلة
     */
    debounce: function(func, wait) {
        let timeout;
        return function() {
            const context = this, args = arguments;
            clearTimeout(timeout);
            timeout = setTimeout(function() {
                func.apply(context, args);
            }, wait);
        };
    },

    /**
     * دالة تحقق هل اليوم هو آخر الشهر أم لا
     * @return {Boolean} صحيح إذا كان اليوم هو آخر يوم في الشهر
     */
    isLastDayOfMonth: function() {
        const today = new Date();
        const lastDay = new Date(today.getFullYear(), today.getMonth() + 1, 0).getDate();
        return today.getDate() === lastDay;
    }
};

// تهيئة مدير الفواتير عند تحميل الصفحة
$(document).ready(function() {
    InvoiceManager.init();

    // التحقق إذا كان آخر يوم في الشهر وتنبيه المستخدم
    if (InvoiceManager.isLastDayOfMonth()) {
        const now = new Date();
        const month = now.toLocaleDateString('ar-SA', { month: 'long' });
        AjaxHandler.showAlert(`تنبيه: اليوم هو آخر يوم في شهر ${month}، يرجى التأكد من إغلاق الشهر وطباعة التقارير الشهرية.`, "warning", 10000);
    }
});