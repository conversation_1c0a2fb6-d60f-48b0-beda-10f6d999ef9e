<?php
/**
 * صفحة تقارير المصروفات
 */

// تعريف ثابت لمنع الوصول المباشر للملفات المرتبطة
define('BASEPATH', true);

// استدعاء ملفات التهيئة
require_once '../../config/init.php';

// التحقق من تسجيل الدخول
redirectIfNotLoggedIn();

// التحقق من الصلاحيات
if (!hasPermission('reports_expenses')) {
    $_SESSION['error_message'] = 'ليس لديك صلاحية لعرض تقارير المصروفات';
    header('Location: ' . BASE_URL . 'pages/dashboard.php');
    exit;
}

// عنوان الصفحة
$pageTitle = 'تقارير المصروفات';

// استدعاء رأس الصفحة
include '../../includes/templates/header.php';

// إنشاء كائنات النماذج
$expenseModel = new Expense($db);
$branchModel = new Branch($db);

// الحصول على قائمة الفروع للفلتر
$branches = [];
if (hasPermission('admin')) {
    $branches = $branchModel->getBranches(['is_active' => 1]);
}

// الحصول على فئات المصروفات
$expenseCategories = $expenseModel->getExpenseCategories();

// تحديد نطاق تاريخ افتراضي (الشهر الحالي)
$today = date('Y-m-d');
$firstDayOfMonth = date('Y-m-01');
$lastDayOfMonth = date('Y-m-t');

// الحصول على إعدادات العملة من قاعدة البيانات
$settingsModel = new Settings($db);
$currencySymbol = $settingsModel->get('system_currency_symbol', 'ر.س');
$currency = $settingsModel->get('system_currency', 'ريال سعودي');

// البيانات الافتراضية للفلتر
$filters = [
    'start_date' => $firstDayOfMonth,
    'end_date' => $lastDayOfMonth,
    'branch_id' => $_SESSION['user_branch_id'] ?? null,
    'category_id' => '',
    'payment_method' => '',
    'sort_by' => 'date',
    'sort_dir' => 'DESC'
];
?>

<!-- المحتوى الرئيسي للصفحة -->
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-white py-3">
                    <h5 class="mb-0">خيارات التقرير</h5>
                </div>
                <div class="card-body">
                    <!-- علامات التبويب للفلاتر -->
                    <ul class="nav nav-tabs" id="reportTabs" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="date-range-tab" data-bs-toggle="tab" data-bs-target="#date-range-content" type="button" role="tab" aria-controls="date-range-content" aria-selected="true">
                                <i class="fas fa-calendar-alt me-1"></i> نطاق التاريخ
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="month-year-tab" data-bs-toggle="tab" data-bs-target="#month-year-content" type="button" role="tab" aria-controls="month-year-content" aria-selected="false">
                                <i class="fas fa-calendar-day me-1"></i> الشهر / السنة
                            </button>
                        </li>
                    </ul>
                    
                    <?php include 'expenses.php'; ?>
                </div>
            </div>
        </div>
    </div>
    
    <!-- تنبيه التحميل -->
    <div class="alert alert-info d-flex align-items-center" id="loadingAlert">
        <div class="spinner-border spinner-border-sm me-2" role="status">
            <span class="visually-hidden">جاري التحميل...</span>
        </div>
        <div>
            جاري تحميل التقرير، يرجى الانتظار...
        </div>
    </div>
    
    <!-- تنبيه الخطأ -->
    <div class="alert alert-danger d-none" id="errorAlert"></div>
    
    <!-- ملخص التقرير -->
    <div class="row" id="reportSummary">
        <div class="col-md-3 mb-4">
            <div class="card shadow-sm">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="text-muted mb-1">إجمالي المصروفات</h6>
                            <h3 class="display-6 fw-bold mb-0" id="totalExpenses">0.00</h3>
                        </div>
                        <div class="bg-danger bg-opacity-10 p-3 rounded">
                            <i class="fas fa-money-bill-wave text-danger fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3 mb-4">
            <div class="card shadow-sm">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="text-muted mb-1">عدد المصروفات</h6>
                            <h3 class="display-6 fw-bold mb-0" id="expenseCount">0</h3>
                        </div>
                        <div class="bg-primary bg-opacity-10 p-3 rounded">
                            <i class="fas fa-receipt text-primary fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3 mb-4">
            <div class="card shadow-sm">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="text-muted mb-1">متوسط المصروف</h6>
                            <h3 class="display-6 fw-bold mb-0" id="averageExpense">0.00</h3>
                        </div>
                        <div class="bg-warning bg-opacity-10 p-3 rounded">
                            <i class="fas fa-calculator text-warning fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3 mb-4">
            <div class="card shadow-sm">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="text-muted mb-1">أعلى فئة مصروفات</h6>
                            <h3 class="display-6 fw-bold mb-0" id="topCategory">-</h3>
                        </div>
                        <div class="bg-success bg-opacity-10 p-3 rounded">
                            <i class="fas fa-tag text-success fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- الرسوم البيانية -->
    <div class="row">
        <div class="col-md-6 mb-4">
            <div class="card shadow-sm">
                <div class="card-header bg-white py-3">
                    <h5 class="mb-0">المصروفات حسب الفئة</h5>
                </div>
                <div class="card-body">
                    <canvas id="categoriesChart" height="300"></canvas>
                </div>
            </div>
        </div>
        
        <div class="col-md-6 mb-4">
            <div class="card shadow-sm">
                <div class="card-header bg-white py-3">
                    <h5 class="mb-0">المصروفات اليومية</h5>
                </div>
                <div class="card-body">
                    <canvas id="dailyExpensesChart" height="300"></canvas>
                </div>
            </div>
        </div>
    </div>
    
    <!-- تفاصيل التقرير -->
    <div class="card shadow-sm mb-4" id="reportDetailsCard">
        <div class="card-header bg-white py-3 d-flex justify-content-between align-items-center">
            <h5 class="mb-0">تفاصيل المصروفات</h5>
            <div class="btn-group">
                <button type="button" class="btn btn-sm btn-outline-secondary" id="exportExcel">
                    <i class="fas fa-file-excel me-1"></i> Excel
                </button>
                <button type="button" class="btn btn-sm btn-outline-secondary" id="exportPdf">
                    <i class="fas fa-file-pdf me-1"></i> PDF
                </button>
                <button type="button" class="btn btn-sm btn-outline-secondary" id="printReport">
                    <i class="fas fa-print me-1"></i> طباعة
                </button>
            </div>
        </div>
        <div class="card-body">
            <div class="table-responsive" id="reportDetails">
                <table class="table table-bordered table-striped table-hover" id="reportTable">
                    <thead class="table-light">
                        <tr>
                            <th>التاريخ</th>
                            <th>الوصف</th>
                            <th>الفئة</th>
                            <th>المبلغ</th>
                            <th>طريقة الدفع</th>
                            <th>ملاحظات</th>
                        </tr>
                    </thead>
                    <tbody id="reportTableBody">
                        <!-- ستتم تعبئة البيانات هنا عن طريق JavaScript -->
                    </tbody>
                    <tfoot class="table-light">
                        <tr>
                            <th colspan="3">الإجمالي</th>
                            <th id="footerTotalAmount">0.00</th>
                            <th colspan="2"></th>
                        </tr>
                    </tfoot>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- استدعاء قالب الفوتر -->
<?php include '../../includes/templates/footer.php'; ?>
<!-- مكتبة لتصدير الجداول -->
<script src="https://cdn.jsdelivr.net/npm/tableexport@5.2.0/dist/js/tableexport.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/xlsx@0.18.5/dist/xlsx.full.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/file-saver@2.0.5/dist/FileSaver.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf-autotable/3.5.28/jspdf.plugin.autotable.min.js"></script>
<script>
// تعريف المتغيرات العامة
const BASE_URL = '<?php echo BASE_URL; ?>';

$(document).ready(function() {
    // تهيئة المخططات البيانية
    let categoriesChart, dailyExpensesChart;

    // تغيير نطاق التاريخ
    $('#dateRange').change(function() {
        const today = new Date();
        const value = $(this).val();
        
        switch(value) {
            case 'today':
                $('#startDate').val(formatDate(today));
                $('#endDate').val(formatDate(today));
                break;
                
            case 'yesterday':
                const yesterday = new Date(today);
                yesterday.setDate(yesterday.getDate() - 1);
                $('#startDate').val(formatDate(yesterday));
                $('#endDate').val(formatDate(yesterday));
                break;
                
            case 'this_week':
                const thisWeekStart = new Date(today);
                thisWeekStart.setDate(thisWeekStart.getDate() - (thisWeekStart.getDay() - 1));
                $('#startDate').val(formatDate(thisWeekStart));
                $('#endDate').val(formatDate(today));
                break;
                
            case 'last_week':
                const lastWeekStart = new Date(today);
                lastWeekStart.setDate(lastWeekStart.getDate() - 7 - (lastWeekStart.getDay() - 1));
                const lastWeekEnd = new Date(lastWeekStart);
                lastWeekEnd.setDate(lastWeekEnd.getDate() + 6);
                $('#startDate').val(formatDate(lastWeekStart));
                $('#endDate').val(formatDate(lastWeekEnd));
                break;
                
            case 'this_month':
                const thisMonthStart = new Date(today.getFullYear(), today.getMonth(), 1);
                const thisMonthEnd = new Date(today.getFullYear(), today.getMonth() + 1, 0);
                $('#startDate').val(formatDate(thisMonthStart));
                $('#endDate').val(formatDate(thisMonthEnd));
                break;
                
            case 'last_month':
                const lastMonthStart = new Date(today.getFullYear(), today.getMonth() - 1, 1);
                const lastMonthEnd = new Date(today.getFullYear(), today.getMonth(), 0);
                $('#startDate').val(formatDate(lastMonthStart));
                $('#endDate').val(formatDate(lastMonthEnd));
                break;
                
            case 'this_year':
                const thisYearStart = new Date(today.getFullYear(), 0, 1);
                const thisYearEnd = new Date(today.getFullYear(), 11, 31);
                $('#startDate').val(formatDate(thisYearStart));
                $('#endDate').val(formatDate(thisYearEnd));
                break;
                
            case 'custom':
                // ابق على القيم الحالية
                break;
        }
    });
    
    // تنسيق التاريخ لـ YYYY-MM-DD
    function formatDate(date) {
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        return `${year}-${month}-${day}`;
    }
    
    // تقديم نموذج التقرير
    $('#dateRangeForm, #monthYearForm').on('submit', function(e) {
        e.preventDefault();
        loadReport();
    });
    
    // تحميل التقرير
    function loadReport() {
        // إظهار تنبيه التحميل
        $('#loadingAlert').removeClass('d-none');
        $('#errorAlert').addClass('d-none');
        
        // تحديد النموذج النشط
        const activeTab = $('.tab-pane.active').attr('id');
        let formData;
        
        if (activeTab === 'date-range-content') {
            formData = new FormData(document.getElementById('dateRangeForm'));
        } else {
            formData = new FormData(document.getElementById('monthYearForm'));
        }
        
        // جلب البيانات من API
        $.ajax({
            url: `${BASE_URL}api/reports.php?type=expenses`,
            type: 'POST',
            data: {
                start_date: formData.get('start_date'),
                end_date: formData.get('end_date'),
                month: formData.get('month'),
                year: formData.get('year'),
                branch_id: formData.get('branch_id'),
                category_id: formData.get('category_id'),
                payment_method: formData.get('payment_method'),
                sort_by: formData.get('sort_by'),
                sort_dir: formData.get('sort_dir')
            },
            dataType: 'json',
            success: function(response) {
                // إخفاء تنبيه التحميل
                $('#loadingAlert').addClass('d-none');
                
                if (response && response.success) {
                    const data = response.data || {};
                    
                    // تحديث ملخص التقرير
                    updateReportSummary(data);
                    
                    // تحديث الرسوم البيانية
                    updateCharts(data);
                    
                    // تحديث جدول التقرير
                    updateReportTable(data);
                } else {
                    const errorMessage = response && response.message ? response.message : 'حدث خطأ أثناء تحميل التقرير';
                    $('#errorAlert').removeClass('d-none').text(errorMessage);
                }
            },
            error: function(xhr, status, error) {
                // إخفاء تنبيه التحميل وإظهار رسالة الخطأ
                $('#loadingAlert').addClass('d-none');
                $('#errorAlert').removeClass('d-none').text('حدث خطأ أثناء الاتصال بالخادم: ' + error);
            }
        });
    }
    
    // تحديث ملخص التقرير
    function updateReportSummary(data) {
        // التحقق من وجود بيانات الملخص
        if (!data || !data.summary) {
            $('#totalExpenses').text(formatCurrency(0));
            $('#expenseCount').text('0');
            $('#averageExpense').text(formatCurrency(0));
            $('#topCategory').text('-');
            return;
        }
        
        const totalExpenses = data.summary.total_expenses || 0;
        const expenseCount = data.summary.total_expense_count || 0;
        
        // حساب متوسط المصروف
        let averageExpense = 0;
        if (expenseCount > 0) {
            averageExpense = totalExpenses / expenseCount;
        }
        
        // تحديث قيم الملخص
        $('#totalExpenses').text(formatCurrency(totalExpenses));
        $('#expenseCount').text(expenseCount);
        $('#averageExpense').text(formatCurrency(averageExpense));
        
        // تحديد أعلى فئة مصروفات
        if (data.top_expense_categories && data.top_expense_categories.length > 0) {
            $('#topCategory').text(data.top_expense_categories[0].name);
        } else {
            $('#topCategory').text('-');
        }
    }
    
    // تحديث الرسوم البيانية
    function updateCharts(data) {
        // تحديث مخطط الفئات
        updateCategoriesChart(data.categories || []);
        
        // تحديث مخطط المصروفات اليومية
        updateDailyExpensesChart(data.daily_expenses || []);
    }
    
    // تحديث مخطط الفئات
    function updateCategoriesChart(categories) {
        const ctx = document.getElementById('categoriesChart').getContext('2d');
        
        // تدمير المخطط السابق إذا كان موجوداً
        if (categoriesChart) {
            categoriesChart.destroy();
        }
        
        // التحقق من وجود بيانات
        if (!categories || categories.length === 0) {
            ctx.font = '16px Arial';
            ctx.fillText('لا توجد بيانات متاحة', 10, 50);
            return;
        }
        
        // إعداد بيانات المخطط
        const labels = categories.map(category => category.name);
        const data = categories.map(category => parseFloat(category.total || 0));
        
        // إنشاء المخطط
        categoriesChart = new Chart(ctx, {
            type: 'pie',
            data: {
                labels: labels,
                datasets: [{
                    data: data,
                    backgroundColor: [
                        'rgba(52, 152, 219, 0.8)',
                        'rgba(46, 204, 113, 0.8)',
                        'rgba(231, 76, 60, 0.8)',
                        'rgba(241, 196, 15, 0.8)',
                        'rgba(155, 89, 182, 0.8)',
                        'rgba(26, 188, 156, 0.8)',
                        'rgba(230, 126, 34, 0.8)'
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const label = context.label || '';
                                const value = formatCurrency(context.raw);
                                return `${label}: ${value}`;
                            }
                        }
                    }
                }
            }
        });
    }
    
    // تحديث مخطط المصروفات اليومية
    function updateDailyExpensesChart(dailyExpenses) {
        const ctx = document.getElementById('dailyExpensesChart').getContext('2d');
        
        // تدمير المخطط السابق إذا كان موجوداً
        if (dailyExpensesChart) {
            dailyExpensesChart.destroy();
        }
        
        // التحقق من وجود بيانات
        if (!dailyExpenses || dailyExpenses.length === 0) {
            ctx.font = '16px Arial';
            ctx.fillText('لا توجد بيانات متاحة', 10, 50);
            return;
        }
        
        // ترتيب البيانات حسب التاريخ
        dailyExpenses.sort((a, b) => new Date(a.date) - new Date(b.date));
        
        // إعداد بيانات المخطط
        const labels = dailyExpenses.map(day => day.date);
        const data = dailyExpenses.map(day => parseFloat(day.total || 0));
        
        // إنشاء المخطط
        dailyExpensesChart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: labels,
                datasets: [{
                    label: 'المصروفات اليومية',
                    data: data,
                    backgroundColor: 'rgba(231, 76, 60, 0.2)',
                    borderColor: 'rgba(231, 76, 60, 1)',
                    borderWidth: 2,
                    tension: 0.4,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                },
                plugins: {
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                return `المصروفات: ${formatCurrency(context.raw)}`;
                            }
                        }
                    }
                }
            }
        });
    }
    
    // تحديث جدول التقرير
    function updateReportTable(data) {
        const tableBody = $('#reportTableBody');
        tableBody.empty();
        
        // التحقق من وجود بيانات المصروفات
        if (!data || !data.expenses || !Array.isArray(data.expenses) || data.expenses.length === 0) {
            $('#reportDetailsCard').addClass('d-none');
            return;
        }
        
        // إظهار بطاقة التفاصيل
        $('#reportDetailsCard').removeClass('d-none');
        
        let totalAmount = 0;
        
        // إضافة صفوف الجدول
        data.expenses.forEach(expense => {
            const amount = parseFloat(expense.amount || 0);
            totalAmount += amount;
            
            const row = `
                <tr>
                    <td>${expense.date}</td>
                    <td>${expense.description || ''}</td>
                    <td>${expense.category_name || ''}</td>
                    <td>${formatCurrency(amount)}</td>
                    <td>${getPaymentMethodName(expense.payment_method)}</td>
                    <td>${expense.notes || ''}</td>
                </tr>
            `;
            tableBody.append(row);
        });
        
        // تحديث إجمالي المبلغ في تذييل الجدول
        $('#footerTotalAmount').text(formatCurrency(totalAmount));
    }
    
    // تنسيق المبالغ المالية
    function formatCurrency(amount) {
        return parseFloat(amount).toFixed(2) + ' <?php echo $currencySymbol; ?>';
    }
    
    // الحصول على اسم طريقة الدفع بالعربية
    function getPaymentMethodName(method) {
        switch(method) {
            case 'cash': return 'نقدي';
            case 'card': return 'بطاقة';
            case 'other': return 'أخرى';
            default: return method;
        }
    }
    
    // تصدير إلى Excel
    $('#exportExcel').click(function() {
        $('#reportTable').tableExport({
            headers: true,
            footers: true,
            formats: ['xlsx'],
            filename: 'تقرير_المصروفات',
            bootstrap: true,
            exportButtons: false,
            position: 'bottom',
            ignoreRows: null,
            ignoreCols: null,
            trimWhitespace: true
        });
    });
    
    // تصدير إلى PDF
    $('#exportPdf').click(function() {
        $('#reportTable').tableExport({
            headers: true,
            footers: true,
            formats: ['pdf'],
            filename: 'تقرير_المصروفات',
            bootstrap: true,
            exportButtons: false,
            position: 'bottom',
            ignoreRows: null,
            ignoreCols: null,
            trimWhitespace: true,
            RTL: true,
            jspdf: {
                orientation: 'p',
                margins: { left: 20, top: 10 },
                autotable: {
                    styles: {
                        rtl: true,
                        overflow: 'linebreak',
                        fontSize: 10,
                        cellPadding: 2
                    },
                    headerStyles: { fillColor: [41, 128, 185], textColor: 255 }
                }
            }
        });
    });
    
    // طباعة التقرير
    $('#printReport').click(function() {
        window.print();
    });
    
    // تحميل التقرير الافتراضي عند تحميل الصفحة
    loadReport();
});
</script>
