<?php
/**
 * ملف التكوين للنسخة التجريبية
 * يحتوي على إعدادات قاعدة البيانات التجريبية والثوابت الخاصة بالنسخة التجريبية
 */

// منع الوصول المباشر للملف
if (!defined('BASEPATH')) {
    exit('لا يمكن الوصول المباشر لهذا الملف');
}

// معلومات الاتصال بقاعدة البيانات التجريبية
define('DB_HOST', 'localhost');
define('DB_USER', 'root');
define('DB_PASS', '');
define('DB_NAME', 'salon_demo');
define('DB_CHARSET', 'utf8mb4');

// إعدادات النظام للنسخة التجريبية
define('SYSTEM_NAME', 'صالون الجمال الراقي - نسخة تجريبية');
define('SYSTEM_VERSION', '1.0.0-DEMO');
define('DEFAULT_TIMEZONE', 'Asia/Riyadh');
define('DEVELOPMENT_MODE', true);
define('DEMO_MODE', true);

// إعدادات المسارات للنسخة التجريبية
define('BASE_URL', 'http://localhost/demo/');
define('API_URL', BASE_URL . 'api/');
define('ASSETS_URL', BASE_URL . 'assets/');

// إعدادات الرسائل
define('SUCCESS_MSG', 'تمت العملية بنجاح');
define('ERROR_MSG', 'حدث خطأ أثناء تنفيذ العملية');
define('DELETE_MSG', 'تم الحذف بنجاح');
define('ACCESS_DENIED_MSG', 'ليس لديك صلاحية للوصول لهذه الصفحة');
define('DEMO_WARNING_MSG', 'تحذير: هذه نسخة تجريبية - جميع البيانات وهمية لأغراض العرض فقط');

// إعدادات الأدوار
define('ROLE_ADMIN', 'admin');
define('ROLE_MANAGER', 'manager');
define('ROLE_CASHIER', 'cashier');
define('ROLE_EMPLOYEE', 'employee');

// إعدادات الطباعة الحرارية
define('RECEIPT_WIDTH_80', 80);
define('RECEIPT_WIDTH_58', 58);
define('DEFAULT_RECEIPT_WIDTH', RECEIPT_WIDTH_80);

// إعدادات خاصة بالنسخة التجريبية
define('DEMO_RESET_INTERVAL', 24); // إعادة تعيين البيانات كل 24 ساعة
define('DEMO_MAX_RECORDS', 100); // الحد الأقصى للسجلات في النسخة التجريبية
define('DEMO_DISABLE_DELETE', true); // منع حذف البيانات الأساسية
define('DEMO_DISABLE_BACKUP', true); // منع إنشاء نسخ احتياطية
define('DEMO_WATERMARK', true); // إضافة علامة مائية للتقارير

// بيانات المستخدمين التجريبيين
define('DEMO_USERS', [
    'admin' => [
        'username' => 'admin',
        'password' => 'password',
        'name' => 'مدير النظام',
        'role' => 'admin'
    ],
    'manager' => [
        'username' => 'manager', 
        'password' => 'password',
        'name' => 'مدير الفرع',
        'role' => 'manager'
    ],
    'cashier' => [
        'username' => 'cashier',
        'password' => 'password', 
        'name' => 'أمين الصندوق',
        'role' => 'cashier'
    ],
    'demo' => [
        'username' => 'demo',
        'password' => 'password',
        'name' => 'مستخدم تجريبي',
        'role' => 'manager'
    ]
]);

// ضبط المنطقة الزمنية
date_default_timezone_set(DEFAULT_TIMEZONE);

/**
 * دالة للحصول على رسالة الخطأ المفصلة أو العامة حسب وضع التطوير
 * @param Exception $e الاستثناء
 * @return string رسالة الخطأ
 */
function getErrorMessage($e) {
    // في وضع التطوير، أظهر كافة تفاصيل الخطأ
    if (DEVELOPMENT_MODE) {
        return $e->getMessage() . ' في الملف ' . $e->getFile() . ' على السطر ' . $e->getLine();
    } else {
        return ERROR_MSG;
    }
}

/**
 * دالة للتحقق من وضع النسخة التجريبية
 * @return bool
 */
function isDemoMode() {
    return defined('DEMO_MODE') && DEMO_MODE === true;
}

/**
 * دالة لإظهار تحذير النسخة التجريبية
 * @return string
 */
function getDemoWarning() {
    if (isDemoMode()) {
        return '<div class="alert alert-warning demo-warning" style="margin: 10px; padding: 15px; background-color: #fff3cd; border: 1px solid #ffeaa7; border-radius: 5px; color: #856404;">
                    <i class="fas fa-exclamation-triangle"></i> ' . DEMO_WARNING_MSG . '
                </div>';
    }
    return '';
}

/**
 * دالة لإضافة علامة مائية للنسخة التجريبية
 * @return string
 */
function getDemoWatermark() {
    if (isDemoMode() && DEMO_WATERMARK) {
        return '<div class="demo-watermark" style="position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%) rotate(-45deg); 
                font-size: 72px; color: rgba(255, 0, 0, 0.1); z-index: -1; pointer-events: none; font-weight: bold;">
                نسخة تجريبية
                </div>';
    }
    return '';
}

/**
 * دالة للتحقق من صلاحية العمليات في النسخة التجريبية
 * @param string $operation نوع العملية
 * @return bool
 */
function isDemoOperationAllowed($operation) {
    if (!isDemoMode()) {
        return true;
    }
    
    $restrictedOperations = [];
    
    if (DEMO_DISABLE_DELETE) {
        $restrictedOperations[] = 'delete_core_data';
    }
    
    if (DEMO_DISABLE_BACKUP) {
        $restrictedOperations[] = 'backup';
        $restrictedOperations[] = 'restore';
    }
    
    return !in_array($operation, $restrictedOperations);
}
