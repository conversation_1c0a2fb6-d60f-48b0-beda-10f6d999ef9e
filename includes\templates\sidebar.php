<?php
/**
 * Sidebar Template for Salon Management System
 * Provides navigation menu for different sections of the application
 */
if (!defined('BASEPATH')) {
    exit('No direct script access allowed');
}
?>
<!-- زر التبديل للموبايل -->
<button type="button" id="sidebarToggle" class="btn btn-primary d-md-none mobile-toggle-btn">
    <i class="fas fa-bars"></i>
    <span class="d-none">القائمة</span>
</button>

<!-- طبقة التغطية للموبايل -->
<div class="sidebar-overlay"></div>

<nav class="sidebar">
    <div class="sidebar-header d-flex justify-content-between align-items-center">
        <h4 class="mb-0"><i class="fas fa-cut me-2"></i> نظام الصالون</h4>
        <button type="button" id="sidebarCollapseBtn" class="btn btn-sm btn-link text-white d-md-none">
            <i class="fas fa-times"></i>
        </button>
    </div>

    <ul class="list-unstyled components">
        <li <?php echo basename($_SERVER['PHP_SELF']) === 'dashboard.php' ? 'class="active"' : ''; ?>>
            <a href="<?= BASE_URL ?>pages/dashboard.php"><i class="fas fa-tachometer-alt me-2"></i> لوحة التحكم</a>
        </li>

        <?php if (hasPermission('appointments_view')): ?>
        <li <?php echo (basename(dirname($_SERVER['PHP_SELF'])) === 'appointments') ? 'class="active"' : ''; ?>>
            <a href="<?= BASE_URL ?>pages/appointments/index.php"><i class="fas fa-calendar-alt me-2"></i> المواعيد</a>
        </li>
        <?php endif; ?>

        <?php if (hasPermission('customers_view')): ?>
        <li <?php echo (basename(dirname($_SERVER['PHP_SELF'])) === 'customers') ? 'class="active"' : ''; ?>>
            <a href="<?= BASE_URL ?>pages/customers/index.php"><i class="fas fa-users me-2"></i> العملاء</a>
        </li>
        <?php endif; ?>

        <?php if (hasPermission('pos_view')): ?>
        <li <?php echo (basename(dirname($_SERVER['PHP_SELF'])) === 'pos') ? 'class="active"' : ''; ?>>
            <a href="<?= BASE_URL ?>pages/pos/index.php"><i class="fas fa-cash-register me-2"></i> نقطة البيع</a>
        </li>
        <?php endif; ?>

        <?php if (hasPermission('invoices_view')): ?>
        <li <?php echo (basename(dirname($_SERVER['PHP_SELF'])) === 'invoices') ? 'class="active"' : ''; ?>>
            <a href="<?= BASE_URL ?>pages/invoices/index.php"><i class="fas fa-file-invoice-dollar me-2"></i> الفواتير</a>
        </li>
        <?php endif; ?>

        <?php if (hasPermission('services_view')): ?>
        <li <?php echo (basename(dirname($_SERVER['PHP_SELF'])) === 'services') ? 'class="active"' : ''; ?>>
            <a href="<?= BASE_URL ?>pages/services/index.php"><i class="fas fa-spa me-2"></i> الخدمات</a>
        </li>
        <?php endif; ?>

        <?php if (hasPermission('products_view')): ?>
        <li <?php echo (basename(dirname($_SERVER['PHP_SELF'])) === 'products') ? 'class="active"' : ''; ?>>
            <a href="<?= BASE_URL ?>pages/products/index.php"><i class="fas fa-box me-2"></i> المنتجات</a>
        </li>
        <?php endif; ?>

        <?php if (hasPermission('promotions_view')): ?>
        <li <?php echo (basename(dirname($_SERVER['PHP_SELF'])) === 'promotions') ? 'class="active"' : ''; ?>>
            <a href="#promotionsSubmenu" data-bs-toggle="collapse" aria-expanded="false" class="dropdown-toggle">
                <i class="fas fa-tags me-2"></i> العروض والخصومات
            </a>
            <ul class="collapse list-unstyled <?php echo (basename(dirname($_SERVER['PHP_SELF'])) === 'promotions') ? 'show' : ''; ?>" id="promotionsSubmenu">
                <li>
                    <a href="<?= BASE_URL ?>pages/promotions/index.php" class="<?php echo (basename($_SERVER['PHP_SELF']) === 'index.php') ? 'active' : ''; ?>">
                        <i class="fas fa-percent me-2"></i> العروض
                    </a>
                </li>
                <li>
                    <a href="<?= BASE_URL ?>pages/promotions/promo_codes.php" class="<?php echo (basename($_SERVER['PHP_SELF']) === 'promo_codes.php') ? 'active' : ''; ?>">
                        <i class="fas fa-ticket-alt me-2"></i> أكواد الترويج
                    </a>
                </li>
            </ul>
        </li>
        <?php endif; ?>

        <?php if (hasPermission('inventory_view')): ?>
        <li <?php echo (basename(dirname($_SERVER['PHP_SELF'])) === 'inventory') ? 'class="active"' : ''; ?>>
            <a href="#inventorySubmenu" data-bs-toggle="collapse" aria-expanded="false" class="dropdown-toggle">
                <i class="fas fa-warehouse me-2"></i> المخزون
            </a>
            <ul class="collapse list-unstyled <?php echo (basename(dirname($_SERVER['PHP_SELF'])) === 'inventory') ? 'show' : ''; ?>" id="inventorySubmenu">
                <li>
                    <a href="<?= BASE_URL ?>pages/inventory/index.php" class="<?php echo (basename($_SERVER['PHP_SELF']) === 'index.php') ? 'active' : ''; ?>">
                        <i class="fas fa-boxes me-2"></i> المخزون
                    </a>
                </li>
                <li>
                    <a href="<?= BASE_URL ?>pages/inventory/transactions.php" class="<?php echo (basename($_SERVER['PHP_SELF']) === 'transactions.php') ? 'active' : ''; ?>">
                        <i class="fas fa-exchange-alt me-2"></i> حركات المخزون
                    </a>
                </li>
                <li>
                    <a href="<?= BASE_URL ?>pages/inventory/adjust.php" class="<?php echo (basename($_SERVER['PHP_SELF']) === 'adjust.php') ? 'active' : ''; ?>">
                        <i class="fas fa-edit me-2"></i> تعديل المخزون
                    </a>
                </li>
            </ul>
        </li>
        <?php endif; ?>

        <?php if (hasPermission('employees_edit')): ?>
        <li <?php echo (basename(dirname($_SERVER['PHP_SELF'])) === 'employees') ? 'class="active"' : ''; ?>>
            <a href="<?= BASE_URL ?>pages/employees/index.php"><i class="fas fa-users-cog me-2"></i> الموظفين</a>
        </li>
        <?php endif; ?>

        <?php if (hasPermission('branches_view')): ?>
        <li <?php echo (basename(dirname($_SERVER['PHP_SELF'])) === 'branches') ? 'class="active"' : ''; ?>>
            <a href="<?= BASE_URL ?>pages/branches/index.php"><i class="fas fa-store me-2"></i> الفروع</a>
        </li>
        <?php endif; ?>

        <?php if (hasPermission('expenses_view')): ?>
        <li <?php echo (basename(dirname($_SERVER['PHP_SELF'])) === 'expenses') ? 'class="active"' : ''; ?>>
            <a href="<?= BASE_URL ?>pages/expenses/index.php"><i class="fas fa-money-bill-wave me-2"></i> المصروفات</a>
        </li>
        <?php endif; ?>

        <?php if (hasPermission('reports_view')): ?>
        <li <?php echo (basename(dirname($_SERVER['PHP_SELF'])) === 'reports') ? 'class="active"' : ''; ?>>
            <a href="#reportsSubmenu" data-bs-toggle="collapse" aria-expanded="false" class="dropdown-toggle">
                <i class="fas fa-chart-bar me-2"></i> التقارير
            </a>
            <ul class="collapse list-unstyled <?php echo (basename(dirname($_SERVER['PHP_SELF'])) === 'reports') ? 'show' : ''; ?>" id="reportsSubmenu">
                <li>
                    <a href="<?= BASE_URL ?>pages/reports/reports.php" class="<?php echo (basename($_SERVER['PHP_SELF']) === 'reports.php') ? 'active' : ''; ?>">
                        <i class="fas fa-chart-line me-2"></i> التقرير الشامل
                    </a>
                </li>
                <li>
                    <a href="<?= BASE_URL ?>pages/reports/inventory.php" class="<?php echo (basename($_SERVER['PHP_SELF']) === 'inventory.php') ? 'active' : ''; ?>">
                        <i class="fas fa-boxes me-2"></i> تقرير المخزون
                    </a>
                </li>

            </ul>
        </li>
        <?php endif; ?>

        <?php if (hasPermission('endday_manage')): ?>
        <li <?php echo (basename(dirname($_SERVER['PHP_SELF'])) === 'endday') ? 'class="active"' : ''; ?>>
            <a href="<?= BASE_URL ?>pages/endday/index.php"><i class="fas fa-calendar-day me-2"></i> نهاية اليوم</a>
        </li>
        <?php endif; ?>

        <?php if (hasPermission('settings_view') || hasPermission('users_manage')): ?>
        <li <?php echo (basename(dirname($_SERVER['PHP_SELF'])) === 'settings') ? 'class="active"' : ''; ?>>
            <a href="#settingsSubmenu" data-bs-toggle="collapse" aria-expanded="false" class="dropdown-toggle">
                <i class="fas fa-cogs me-2"></i> الإعدادات
            </a>
            <ul class="collapse list-unstyled <?php echo (basename(dirname($_SERVER['PHP_SELF'])) === 'settings') ? 'show' : ''; ?>"  id="settingsSubmenu">
                <?php if (hasPermission('settings_view')): ?>
                <li>
                    <a href="<?= BASE_URL ?>pages/settings/system.php" class="<?php echo (basename($_SERVER['PHP_SELF']) === 'system.php') ? 'active' : ''; ?>">
                        <i class="fas fa-sliders-h me-2"></i> إعدادات النظام
                    </a>
                </li>
                <li>
                    <a href="<?= BASE_URL ?>pages/settings/whatsapp.php" class="<?php echo (basename($_SERVER['PHP_SELF']) === 'whatsapp.php') ? 'active' : ''; ?>">
                        <i class="fab fa-whatsapp me-2"></i> إعدادات WhatsApp
                    </a>
                </li>
                <li>
                    <a href="<?= BASE_URL ?>pages/settings/whatsapp_test.php" class="<?php echo (basename($_SERVER['PHP_SELF']) === 'whatsapp_test.php') ? 'active' : ''; ?>">
                        <i class="fas fa-vial me-2"></i> اختبار WhatsApp
                    </a>
                </li>
                <?php endif; ?>
                <?php if (hasPermission('users_manage')): ?>
                <li>
                    <a href="<?= BASE_URL ?>pages/settings/users.php" class="<?php echo (basename($_SERVER['PHP_SELF']) === 'users.php') ? 'active' : ''; ?>">
                        <i class="fas fa-user-cog me-2"></i> إدارة المستخدمين
                    </a>
                </li>
                <?php endif; ?>
                <?php if (isAdmin()): ?>
                <li>
                    <a href="<?= BASE_URL ?>pages/admin/database_maintenance.php" class="<?php echo (basename($_SERVER['PHP_SELF']) === 'database_maintenance.php') ? 'active' : ''; ?>">
                        <i class="fas fa-database me-2"></i> صيانة قاعدة البيانات
                    </a>
                </li>
                <?php endif; ?>
            </ul>
        </li>
        <?php endif; ?>
    </ul>
</nav>
