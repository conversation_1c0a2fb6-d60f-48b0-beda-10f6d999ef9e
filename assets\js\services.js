/**
 * Salon Management System - Services Page
 * 
 * @version 1.0.0
 * <AUTHOR> Management System Team
 */
$(document).ready(function() {
    // Initialize services page components
    initServicesPage();

    // Add Service Modal Handling
    $('#add-service-form').on('submit', function(e) {
        e.preventDefault();
        addService();
    });

    // Edit Service Modal Handling
    $('#edit-service-form').on('submit', function(e) {
        e.preventDefault();
        updateService();
    });

    // Delete Service Confirmation
    $(document).on('click', '.delete-service-btn', function() {
        const serviceId = $(this).data('service-id');
        confirmDeleteService(serviceId);
    });

    // Service Category Management
    $('#add-service-category-form').on('submit', function(e) {
        e.preventDefault();
        addServiceCategory();
    });

    $('#edit-service-category-form').on('submit', function(e) {
        e.preventDefault();
        updateServiceCategory();
    });

    /**
     * Initialize services page components
     */
    function initServicesPage() {
        // Load services list
        loadServicesList();

        // Load service categories
        loadServiceCategories();

        // Initialize branch select
        populateBranchSelect();

        // Initialize employee multiselect
        initEmployeeMultiSelect();

        // Bind modal reset events
        bindModalResetEvents();
    }

    /**
     * Populate branch select dropdown
     */
    function populateBranchSelect() {
        AjaxHandler.get('branches', {}, {
            dataType: 'json'
        }).then(function(response) {
            if (response.success && response.data) {
                const $branchSelects = $('.branch-select');
                $branchSelects.each(function() {
                    const $select = $(this);
                    $select.append('<option value="">اختر الفرع</option>');
                    response.data.forEach(function(branch) {
                        $select.append(`<option value="${branch.id}">${branch.name}</option>`);
                    });
                });
            }
        }).catch(function(error) {
            console.error('Error loading branches:', error);
        });
    }

    /**
     * Initialize employee multiselect
     */
    function initEmployeeMultiSelect() {
        AjaxHandler.get('employees', {}, {
            dataType: 'json'
        }).then(function(response) {
            if (response.success && response.data) {
                const $employeeSelects = $('.employees-multiselect');
                $employeeSelects.each(function() {
                    const $select = $(this);
                    $select.html('');  // Clear existing options
                    response.data.forEach(function(employee) {
                        $select.append(`<option value="${employee.id}">${employee.name}</option>`);
                    });
                    
                    // Initialize select2 if available
                    if ($.fn.select2) {
                        $select.select2({
                            placeholder: 'اختر الموظفين',
                            allowClear: true,
                            multiple: true
                        });
                    }
                });
            }
        }).catch(function(error) {
            console.error('Error loading employees:', error);
        });
    }

    /**
     * Load services list
     * @param {Object} [filters] - Optional filters for services
     */
    function loadServicesList(filters = {}) {
        // Default filters
        const defaultFilters = {
            limit: 10,
            page: 1
        };

        // Merge filters
        const requestFilters = { ...defaultFilters, ...filters };

        AjaxHandler.get('services', requestFilters)
            .then(renderServicesList)
            .catch(function(error) {
                AjaxHandler.showAlert('حدث خطأ في تحميل الخدمات', 'danger');
                console.error('Error loading services:', error);
            });
    }

    /**
     * Render services list
     * @param {Object} response - API response with services data
     */
    function renderServicesList(response) {
        const $tableBody = $('#services-table-body');
        $tableBody.empty();

        if (!response.data || response.data.length === 0) {
            $tableBody.append(`
                <tr>
                    <td colspan="6" class="text-center">لا توجد خدمات</td>
                </tr>
            `);
            return;
        }

        response.data.forEach(function(service) {
            const employeesHtml = service.employees 
                ? service.employees.map(emp => emp.name).join(', ') 
                : 'غير محدد';

            const row = `
                <tr>
                    <td>${service.name}</td>
                    <td>${service.description || '-'}</td>
                    <td>${service.price.toFixed(2)} ر.س</td>
                    <td>${service.duration} دقيقة</td>
                    <td>${service.category_name || 'غير مصنف'}</td>
                    <td>${employeesHtml}</td>
                    <td>
                        <div class="btn-group" role="group">
                            <button type="button" class="btn btn-sm btn-info edit-service-btn" 
                                data-toggle="modal" 
                                data-target="#edit-service-modal"
                                data-service-id="${service.id}"
                                data-service-name="${service.name}"
                                data-service-description="${service.description || ''}"
                                data-service-price="${service.price}"
                                data-service-duration="${service.duration}"
                                data-service-category="${service.category_id || ''}"
                                data-service-branch="${service.branch_id || ''}"
                                data-service-employees="${service.employees ? service.employees.map(e => e.id).join(',') : ''}"
                            >
                                <i class="fas fa-edit"></i>
                            </button>
                            <button type="button" class="btn btn-sm btn-danger delete-service-btn" 
                                data-service-id="${service.id}">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </td>
                </tr>
            `;
            $tableBody.append(row);
        });

        // Handle pagination
        if (response.pagination) {
            renderPagination(response.pagination);
        }
    }

    /**
     * Render pagination controls
     * @param {Object} pagination - Pagination data
     */
    function renderPagination(pagination) {
        const $paginationContainer = $('#services-pagination');
        $paginationContainer.empty();

        // Previous button
        if (pagination.current_page > 1) {
            $paginationContainer.append(`
                <li class="page-item">
                    <a class="page-link pagination-link" href="#" data-page="${pagination.current_page - 1}">السابق</a>
                </li>
            `);
        }

        // Page numbers
        for (let i = 1; i <= pagination.total_pages; i++) {
            $paginationContainer.append(`
                <li class="page-item ${i === pagination.current_page ? 'active' : ''}">
                    <a class="page-link pagination-link" href="#" data-page="${i}">${i}</a>
                </li>
            `);
        }

        // Next button
        if (pagination.current_page < pagination.total_pages) {
            $paginationContainer.append(`
                <li class="page-item">
                    <a class="page-link pagination-link" href="#" data-page="${pagination.current_page + 1}">التالي</a>
                </li>
            `);
        }

        // Bind pagination click events
        $('.pagination-link').on('click', function(e) {
            e.preventDefault();
            const page = $(this).data('page');
            loadServicesList({ page: page });
        });
    }

    /**
     * Add a new service
     */
    function addService() {
        // Collect form data
        const formData = {
            name: $('#add-service-name').val(),
            description: $('#add-service-description').val(),
            price: $('#add-service-price').val(),
            duration: $('#add-service-duration').val(),
            category_id: $('#add-service-category').val(),
            branch_id: $('#add-service-branch').val(),
            is_active: $('#add-service-active').is(':checked') ? 1 : 0
        };

        // Get selected employees
        const selectedEmployees = $('#add-service-employees').val();
        if (selectedEmployees) {
            formData.employees = selectedEmployees.join(',');
        }

        // Validate required fields
        if (!formData.name || !formData.price) {
            AjaxHandler.showAlert('يرجى إدخال اسم الخدمة والسعر', 'warning');
            return;
        }

        // Send request to add service
        AjaxHandler.post('services?action=add', formData)
            .then(function(response) {
                AjaxHandler.showAlert('تمت إضافة الخدمة بنجاح', 'success');
                
                // Close modal
                $('#add-service-modal').modal('hide');
                
                // Reload services list
                loadServicesList();
                
                // Reset form
                $('#add-service-form')[0].reset();
            })
            .catch(function(error) {
                AjaxHandler.showAlert('حدث خطأ أثناء إضافة الخدمة', 'danger');
                console.error('Error adding service:', error);
            });
    }

    /**
     * Update an existing service
     */
    function updateService() {
        // Collect form data
        const formData = {
            id: $('#edit-service-id').val(),
            name: $('#edit-service-name').val(),
            description: $('#edit-service-description').val(),
            price: $('#edit-service-price').val(),
            duration: $('#edit-service-duration').val(),
            category_id: $('#edit-service-category').val(),
            branch_id: $('#edit-service-branch').val(),
            is_active: $('#edit-service-active').is(':checked') ? 1 : 0
        };

        // Get selected employees
        const selectedEmployees = $('#edit-service-employees').val();
        if (selectedEmployees) {
            formData.employees = selectedEmployees.join(',');
        }

        // Validate required fields
        if (!formData.name || !formData.price) {
            AjaxHandler.showAlert('يرجى إدخال اسم الخدمة والسعر', 'warning');
            return;
        }

        // Send request to update service
        AjaxHandler.post('services?action=update', formData)
            .then(function(response) {
                AjaxHandler.showAlert('تم تحديث الخدمة بنجاح', 'success');
                
                // Close modal
                $('#edit-service-modal').modal('hide');
                
                // Reload services list
                loadServicesList();
            })
            .catch(function(error) {
                AjaxHandler.showAlert('حدث خطأ أثناء تحديث الخدمة', 'danger');
                console.error('Error updating service:', error);
            });
    }

    /**
     * Confirm and delete a service
     * @param {number} serviceId - ID of the service to delete
     */
    function confirmDeleteService(serviceId) {
        // Show confirmation modal
        $('#delete-service-confirm-modal').modal('show');

        // Bind delete confirmation
        $('#confirm-delete-service-btn').off('click').on('click', function() {
            // Send delete request
            AjaxHandler.post('services?action=delete', { id: serviceId })
                .then(function(response) {
                    AjaxHandler.showAlert('تم حذف الخدمة بنجاح', 'success');
                    
                    // Close confirmation modal
                    $('#delete-service-confirm-modal').modal('hide');
                    
                    // Reload services list
                    loadServicesList();
                })
                .catch(function(error) {
                    AjaxHandler.showAlert('حدث خطأ أثناء حذف الخدمة', 'danger');
                    console.error('Error deleting service:', error);
                });
        });
    }

    /**
     * Load service categories
     */
    function loadServiceCategories() {
        AjaxHandler.get('services?action=categories')
            .then(renderServiceCategories)
            .catch(function(error) {
                AjaxHandler.showAlert('حدث خطأ في تحميل فئات الخدمات', 'danger');
                console.error('Error loading service categories:', error);
            });
    }

    /**
     * Render service categories
     * @param {Object} response - API response with categories
     */
    function renderServiceCategories(response) {
        const $categorySelects = $('.service-category-select');
        const $categoriesTableBody = $('#service-categories-table-body');

        // Populate category selects
        $categorySelects.each(function() {
            const $select = $(this);
            $select.html('<option value="">اختر الفئة</option>');
            
            response.data.forEach(function(category) {
                $select.append(`<option value="${category.id}">${category.name}</option>`);
            });
        });

        // Render categories table
        $categoriesTableBody.empty();
        if (!response.data || response.data.length === 0) {
            $categoriesTableBody.append(`
                <tr>
                    <td colspan="3" class="text-center">لا توجد فئات</td>
                </tr>
            `);
            return;
        }

        response.data.forEach(function(category) {
            const row = `
                <tr>
                    <td>${category.name}</td>
                    <td>${category.description || '-'}</td>
                    <td>
                        <div class="btn-group" role="group">
                            <button type="button" class="btn btn-sm btn-info edit-category-btn" 
                                data-toggle="modal" 
                                data-target="#edit-service-category-modal"
                                data-category-id="${category.id}"
                                data-category-name="${category.name}"
                                data-category-description="${category.description || ''}"
                            >
                                <i class="fas fa-edit"></i>
                            </button>
                            <button type="button" class="btn btn-sm btn-danger delete-category-btn" 
                                data-category-id="${category.id}">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </td>
                </tr>
            `;
            $categoriesTableBody.append(row);
        });
    }

    /**
     * Add a new service category
     */
    function addServiceCategory() {
        const formData = {
            name: $('#add-service-category-description').val()
        };

        // Validate required fields
        if (!formData.name) {
            AjaxHandler.showAlert('يرجى إدخال اسم الفئة', 'warning');
            return;
        }

        // Send request to add category
        AjaxHandler.post('services?action=categories&category_action=add', formData)
            .then(function(response) {
                AjaxHandler.showAlert('تمت إضافة الفئة بنجاح', 'success');
                
                // Close modal
                $('#add-service-category-modal').modal('hide');
                
                // Reload service categories
                loadServiceCategories();
                
                // Reset form
                $('#add-service-category-form')[0].reset();
            })
            .catch(function(error) {
                AjaxHandler.showAlert('حدث خطأ أثناء إضافة الفئة', 'danger');
                console.error('Error adding service category:', error);
            });
    }

    /**
     * Update an existing service category
     */
    function updateServiceCategory() {
        const formData = {
            id: $('#edit-service-category-id').val(),
            name: $('#edit-service-category-name').val(),
            description: $('#edit-service-category-description').val()
        };

        // Validate required fields
        if (!formData.name) {
            AjaxHandler.showAlert('يرجى إدخال اسم الفئة', 'warning');
            return;
        }

        // Send request to update category
        AjaxHandler.post('services?action=categories&category_action=update', formData)
            .then(function(response) {
                AjaxHandler.showAlert('تم تحديث الفئة بنجاح', 'success');
                
                // Close modal
                $('#edit-service-category-modal').modal('hide');
                
                // Reload service categories
                loadServiceCategories();
            })
            .catch(function(error) {
                AjaxHandler.showAlert('حدث خطأ أثناء تحديث الفئة', 'danger');
                console.error('Error updating service category:', error);
            });
    }

    /**
     * Delete a service category
     * @param {number} categoryId - ID of the category to delete
     */
    function deleteServiceCategory(categoryId) {
        // Show confirmation modal
        $('#delete-service-category-confirm-modal').modal('show');

        // Bind delete confirmation
        $('#confirm-delete-service-category-btn').off('click').on('click', function() {
            // Send delete request
            AjaxHandler.post('services?action=categories&category_action=delete', { id: categoryId })
                .then(function(response) {
                    AjaxHandler.showAlert('تم حذف الفئة بنجاح', 'success');
                    
                    // Close confirmation modal
                    $('#delete-service-category-confirm-modal').modal('hide');
                    
                    // Reload service categories
                    loadServiceCategories();
                })
                .catch(function(error) {
                    AjaxHandler.showAlert('حدث خطأ أثناء حذف الفئة', 'danger');
                    console.error('Error deleting service category:', error);
                });
        });
    }

    /**
     * Bind modal reset events to clear form and errors
     */
    function bindModalResetEvents() {
        // Edit Service Modal
        $('#edit-service-modal').on('show.bs.modal', function(event) {
            const button = $(event.relatedTarget);
            const serviceId = button.data('service-id');
            const serviceName = button.data('service-name');
            const serviceDescription = button.data('service-description');
            const servicePrice = button.data('service-price');
            const serviceDuration = button.data('service-duration');
            const serviceCategoryId = button.data('service-category');
            const serviceBranchId = button.data('service-branch');
            const serviceEmployees = button.data('service-employees');

            const modal = $(this);
            modal.find('#edit-service-id').val(serviceId);
            modal.find('#edit-service-name').val(serviceName);
            modal.find('#edit-service-description').val(serviceDescription);
            modal.find('#edit-service-price').val(servicePrice);
            modal.find('#edit-service-duration').val(serviceDuration);
            modal.find('#edit-service-category').val(serviceCategoryId);
            modal.find('#edit-service-branch').val(serviceBranchId);
            
            // Reset and set employees multiselect
            if (serviceEmployees) {
                const employeeIds = serviceEmployees.split(',').map(id => id.trim());
                $('#edit-service-employees').val(employeeIds).trigger('change');
            } else {
                $('#edit-service-employees').val(null).trigger('change');
            }
        });

        // Edit Service Category Modal
        $('#edit-service-category-modal').on('show.bs.modal', function(event) {
            const button = $(event.relatedTarget);
            const categoryId = button.data('category-id');
            const categoryName = button.data('category-name');
            const categoryDescription = button.data('category-description');

            const modal = $(this);
            modal.find('#edit-service-category-id').val(categoryId);
            modal.find('#edit-service-category-name').val(categoryName);
            modal.find('#edit-service-category-description').val(categoryDescription);
        });

        // Category Delete Button Binding
        $(document).on('click', '.delete-category-btn', function() {
            const categoryId = $(this).data('category-id');
            deleteServiceCategory(categoryId);
        });

        // Search and Filter Handling
        $('#services-search-form').on('submit', function(e) {
            e.preventDefault();
            const filters = {
                search: $('#services-search-input').val(),
                category_id: $('#services-category-filter').val(),
                branch_id: $('#services-branch-filter').val(),
                is_active: $('#services-active-filter').val()
            };

            // Remove empty filters
            Object.keys(filters).forEach(key => {
                if (!filters[key]) delete filters[key];
            });

            loadServicesList(filters);
        });
    }

    /**
     * Check service availability
     * @param {number} serviceId - ID of the service
     * @param {string} date - Appointment date
     * @param {string} startTime - Appointment start time
     * @param {number} branchId - ID of the branch
     * @returns {Promise} - Promise resolving with availability status
     */
    function checkServiceAvailability(serviceId, date, startTime, branchId) {
        return AjaxHandler.get('services', {
            action: 'check_availability',
            service_id: serviceId,
            date: date,
            start_time: startTime,
            branch_id: branchId
        });
    }

    // Export public methods for potential external use
    window.ServicesManager = {
        loadServicesList: loadServicesList,
        addService: addService,
        updateService: updateService,
        checkServiceAvailability: checkServiceAvailability
    };
});