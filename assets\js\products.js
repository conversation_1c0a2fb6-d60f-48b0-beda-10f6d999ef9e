/**
 * ملف إدارة المنتجات
 * يحتوي على الدوال اللازمة للتعامل مع إدارة المنتجات في النظام
 *
 * @version 1.0.0
 */

// كائن إدارة المنتجات
const ProductManager = {
    // ثوابت API
    endpoints: {
        LIST: 'products.php?action=list',
        GET: 'products.php?action=get',
        ADD: 'products.php?action=add',
        UPDATE: 'products.php?action=update',
        DELETE: 'products.php?action=delete',
        CATEGORIES: 'products.php?action=categories',
        ADD_CATEGORY: 'products.php?action=add_category',
        UPDATE_CATEGORY: 'products.php?action=update_category',
        DELETE_CATEGORY: 'products.php?action=delete_category',
        REPORT: 'products.php?action=report'
    },

    /**
     * تهيئة الصفحة
     */
    init: function() {
        // تهيئة صفحة قائمة المنتجات
        if ($("#products-table").length) {
            this.initProductsListPage();
        }

        // تهيئة صفحة إضافة/تعديل منتج
        if ($("#product-form").length) {
            this.initProductForm();
        }

        // تهيئة صفحة فئات المنتجات
        if ($("#categories-table").length) {
            this.initCategoriesPage();
        }

        // تهيئة عناصر مشتركة
        this.initCommonElements();
    },

    /**
     * تهيئة العناصر المشتركة
     */
    initCommonElements: function() {
        // أزرار الحذف
        $(document).on('click', '.delete-product-btn', function(e) {
            e.preventDefault();
            const productId = $(this).data('id');
            const productName = $(this).data('name');
            ProductManager.confirmDeleteProduct(productId, productName);
        });

        // أزرار حذف الفئات
        $(document).on('click', '.delete-category-btn', function(e) {
            e.preventDefault();
            const categoryId = $(this).data('id');
            const categoryName = $(this).data('name');
            ProductManager.confirmDeleteCategory(categoryId, categoryName);
        });
    },

    /**
     * تهيئة صفحة قائمة المنتجات
     */
    initProductsListPage: function() {
        // تهيئة فلاتر البحث
        this.initProductFilters();

        // تحميل قائمة المنتجات
        this.loadProducts();

        // إضافة الأحداث للأزرار
        this.initProductButtons();

        // تهيئة الجدول
        this.initProductsTable();
    },

    /**
     * تهيئة فلاتر البحث
     */
    initProductFilters: function() {
        // تحميل الفئات للفلتر
        this.loadCategories('category-filter');

        // حدث تغيير الفلتر
        $("#filter-form").on('submit', function(e) {
            e.preventDefault();
            ProductManager.loadProducts();
        });

        // تهيئة فلتر البحث
        $("#search-input").on('keyup', this.debounce(function() {
            ProductManager.loadProducts();
        }, 500));

        // زر إعادة ضبط الفلاتر
        $("#reset-filters").on('click', function() {
            $("#filter-form")[0].reset();
            $("#category-filter").val(null).trigger('change');
            ProductManager.loadProducts();
        });
    },

    /**
     * تحميل قائمة المنتجات
     */
    loadProducts: function() {
        // استخراج قيم الفلاتر
        const filters = {
            search: $("#search-input").val(),
            category_id: $("#category-filter").val(),
            branch_id: $("#branch-filter").val(),
            is_for_sale: $("#for-sale-filter").val(),
            is_active: $("#status-filter").val(),
            page: $("#current-page").val() || 1,
            limit: $("#page-size").val() || 20
        };

        // إظهار مؤشر التحميل
        $("#products-table tbody").html('<tr><td colspan="8" class="text-center py-5"><div class="spinner-border text-primary" role="status"><span class="sr-only">جارِ التحميل...</span></div></td></tr>');

        // استدعاء API لجلب المنتجات
        AjaxHandler.get(this.endpoints.LIST, filters)
            .then(response => {
                if (response.success) {
                    this.displayProducts(response.data.products);
                    this.setupPagination(response.data.pagination);
                } else {
                    // عرض رسالة الخطأ
                    $("#products-table tbody").html('<tr><td colspan="8" class="text-center text-danger">حدث خطأ أثناء تحميل البيانات</td></tr>');
                    AjaxHandler.showAlert(response.message, 'danger');
                }
            })
            .catch(error => {
                // عرض رسالة الخطأ
                $("#products-table tbody").html('<tr><td colspan="8" class="text-center text-danger">حدث خطأ في الاتصال بالخادم</td></tr>');
                AjaxHandler.showAlert("تعذر الاتصال بالخادم", 'danger');
            });
    },

    /**
     * عرض قائمة المنتجات في الجدول
     * @param {Array} products قائمة المنتجات
     */
    displayProducts: function(products) {
        let html = '';

        if (products.length === 0) {
            html = '<tr><td colspan="8" class="text-center">لا توجد منتجات للعرض</td></tr>';
        } else {
            products.forEach(product => {
                html += `
                    <tr>
                        <td>${product.id}</td>
                        <td>${product.name}</td>
                        <td>${product.category_name || '-'}</td>
                        <td class="text-left">${this.formatCurrency(product.price)}</td>
                        <td class="text-left">${this.formatCurrency(product.cost || 0)}</td>
                        <td>${product.stock !== undefined ? product.stock : '-'}</td>
                        <td>
                            <span class="badge badge-${product.is_active == 1 ? 'success' : 'danger'}">
                                ${product.is_active == 1 ? 'نشط' : 'غير نشط'}
                            </span>
                        </td>
                        <td>
                            <div class="btn-group">
                                <a href="edit.php?id=${product.id}" class="btn btn-sm btn-primary" title="تعديل">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <button type="button" class="btn btn-sm btn-info view-product-btn" data-id="${product.id}" title="عرض">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button type="button" class="btn btn-sm btn-danger delete-product-btn" data-id="${product.id}" data-name="${product.name}" title="حذف">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                `;
            });
        }

        $("#products-table tbody").html(html);
    },

    /**
     * إعداد عناصر التصفح (الترقيم)
     * @param {Object} pagination معلومات الترقيم
     */
    setupPagination: function(pagination) {
        // تحديث عدد النتائج
        $("#total-products").text(pagination.total);

        // إعداد ترقيم الصفحات
        let paginationHtml = '';

        // زر الصفحة السابقة
        paginationHtml += `
            <li class="page-item ${pagination.current_page <= 1 ? 'disabled' : ''}">
                <a class="page-link" href="#" data-page="${pagination.current_page - 1}">&raquo;</a>
            </li>
        `;

        // أزرار الصفحات
        for (let i = 1; i <= pagination.total_pages; i++) {
            if (
                i === 1 ||
                i === pagination.total_pages ||
                (i >= pagination.current_page - 2 && i <= pagination.current_page + 2)
            ) {
                paginationHtml += `
                    <li class="page-item ${i === pagination.current_page ? 'active' : ''}">
                        <a class="page-link" href="#" data-page="${i}">${i}</a>
                    </li>
                `;
            } else if (
                i === 2 ||
                i === pagination.total_pages - 1
            ) {
                paginationHtml += `
                    <li class="page-item disabled">
                        <a class="page-link" href="#">...</a>
                    </li>
                `;
            }
        }

        // زر الصفحة التالية
        paginationHtml += `
            <li class="page-item ${pagination.current_page >= pagination.total_pages ? 'disabled' : ''}">
                <a class="page-link" href="#" data-page="${pagination.current_page + 1}">&laquo;</a>
            </li>
        `;

        // تحديث HTML الترقيم
        $("#pagination").html(paginationHtml);

        // تحديث القيمة المخزنة للصفحة الحالية
        $("#current-page").val(pagination.current_page);

        // إضافة الحدث لأزرار الترقيم
        $("#pagination").off('click', '.page-link').on('click', '.page-link', function(e) {
            e.preventDefault();
            const page = $(this).data('page');

            if (!isNaN(page) && page > 0) {
                $("#current-page").val(page);
                ProductManager.loadProducts();

                // للتمرير لأعلى الصفحة
                $('html, body').animate({ scrollTop: $('#products-table').offset().top - 100 }, 200);
            }
        });
    },

    /**
     * تهيئة أزرار المنتجات
     */
    initProductButtons: function() {
        // عرض تفاصيل المنتج
        $(document).on('click', '.view-product-btn', function() {
            const productId = $(this).data('id');
            ProductManager.viewProductDetails(productId);
        });

        // تصدير المنتجات إلى Excel
        $("#export-excel").on('click', function() {
            ProductManager.exportProductsToExcel();
        });

        // طباعة قائمة المنتجات
        $("#print-products").on('click', function() {
            ProductManager.printProducts();
        });
    },

    /**
     * عرض تفاصيل المنتج
     * @param {number} productId معرف المنتج
     */
    viewProductDetails: function(productId) {
        AjaxHandler.get(this.endpoints.GET, { id: productId })
            .then(response => {
                if (response.success) {
                    this.displayProductDetailsModal(response.data);
                } else {
                    AjaxHandler.showAlert(response.message, 'danger');
                }
            })
            .catch(error => {
                AjaxHandler.showAlert('حدث خطأ في الاتصال بالخادم', 'danger');
            });
    },

    /**
     * عرض نافذة تفاصيل المنتج
     * @param {Object} product بيانات المنتج
     */
    displayProductDetailsModal: function(product) {
        // إعداد محتوى النافذة المنبثقة
        const modalContent = `
            <div class="modal-header">
                <h5 class="modal-title">${product.name}</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label>الاسم:</label>
                            <p>${product.name}</p>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label>الفئة:</label>
                            <p>${product.category_name || '-'}</p>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label>السعر:</label>
                            <p>${this.formatCurrency(product.price)}</p>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label>التكلفة:</label>
                            <p>${this.formatCurrency(product.cost || 0)}</p>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label>الحالة:</label>
                            <p>
                                <span class="badge badge-${product.is_active == 1 ? 'success' : 'danger'}">
                                    ${product.is_active == 1 ? 'نشط' : 'غير نشط'}
                                </span>
                            </p>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label>للبيع:</label>
                            <p>
                                <span class="badge badge-${product.is_for_sale == 1 ? 'info' : 'secondary'}">
                                    ${product.is_for_sale == 1 ? 'متاح للبيع' : 'للاستخدام الداخلي'}
                                </span>
                            </p>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label>الكمية المتوفرة:</label>
                            <p>${product.stock !== undefined ? product.stock : '-'}</p>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label>الحد الأدنى للكمية:</label>
                            <p>${product.min_quantity || 0}</p>
                        </div>
                    </div>
                </div>
                <div class="form-group">
                    <label>الوصف:</label>
                    <p>${product.description || '-'}</p>
                </div>

                ${product.transactions ? `
                <hr>
                <h6>حركات المخزون الأخيرة</h6>
                <table class="table table-sm table-bordered">
                    <thead>
                        <tr>
                            <th>التاريخ</th>
                            <th>النوع</th>
                            <th>الكمية</th>
                            <th>الملاحظات</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${product.transactions.map(trans => `
                            <tr>
                                <td>${this.formatDate(trans.created_at)}</td>
                                <td>
                                    <span class="badge badge-${trans.transaction_type === 'in' ? 'success' : 'danger'}">
                                        ${trans.transaction_type === 'in' ? 'وارد' : 'صادر'}
                                    </span>
                                </td>
                                <td>${trans.quantity}</td>
                                <td>${trans.notes || '-'}</td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
                ` : ''}
            </div>
            <div class="modal-footer">
                <a href="edit.php?id=${product.id}" class="btn btn-primary">تعديل</a>
                <button type="button" class="btn btn-secondary" data-dismiss="modal">إغلاق</button>
            </div>
        `;

        // تحديث محتوى النافذة وعرضها
        $("#productDetailsModal .modal-content").html(modalContent);
        $("#productDetailsModal").modal('show');
    },

    /**
     * تأكيد حذف منتج
     * @param {number} productId معرف المنتج
     * @param {string} productName اسم المنتج
     */
    confirmDeleteProduct: function(productId, productName) {
        if (confirm(`هل أنت متأكد من حذف المنتج "${productName}"؟`)) {
            this.deleteProduct(productId);
        }
    },

    /**
     * حذف منتج
     * @param {number} productId معرف المنتج
     */
    deleteProduct: function(productId) {
        AjaxHandler.delete(this.endpoints.DELETE, { id: productId })
            .then(response => {
                if (response.success) {
                    AjaxHandler.showAlert(response.message, 'success');
                    this.loadProducts();
                } else {
                    AjaxHandler.showAlert(response.message, 'danger');
                }
            })
            .catch(error => {
                AjaxHandler.showAlert('حدث خطأ في الاتصال بالخادم', 'danger');
            });
    },

    /**
     * تهيئة جدول المنتجات
     */
    initProductsTable: function() {
        // تهيئة جدول البيانات إذا كان متاحًا
        if ($.fn.DataTable && $("#products-table").length) {
            // وضع الإعدادات المخصصة في حالة استخدام DataTables
        }
    },

    /**
     * تصدير المنتجات إلى ملف Excel
     */
    exportProductsToExcel: function() {
        // استخراج قيم الفلاتر
        const filters = {
            search: $("#search-input").val(),
            category_id: $("#category-filter").val(),
            branch_id: $("#branch-filter").val(),
            is_for_sale: $("#for-sale-filter").val(),
            is_active: $("#status-filter").val(),
            export: 'excel'
        };

        // إنشاء URL للتصدير
        const queryString = Object.keys(filters)
            .filter(key => filters[key] !== null && filters[key] !== undefined && filters[key] !== '')
            .map(key => `${key}=${encodeURIComponent(filters[key])}`)
            .join('&');

        // فتح نافذة جديدة للتصدير
        window.open(`${AjaxHandler.defaultConfig.baseUrl}${this.endpoints.LIST}&${queryString}`, '_blank');
    },

    /**
     * طباعة قائمة المنتجات
     */
    printProducts: function() {
        // استخراج قيم الفلاتر
        const filters = {
            search: $("#search-input").val(),
            category_id: $("#category-filter").val(),
            branch_id: $("#branch-filter").val(),
            is_for_sale: $("#for-sale-filter").val(),
            is_active: $("#status-filter").val(),
            print: 'true'
        };

        // إنشاء URL للطباعة
        const queryString = Object.keys(filters)
            .filter(key => filters[key] !== null && filters[key] !== undefined && filters[key] !== '')
            .map(key => `${key}=${encodeURIComponent(filters[key])}`)
            .join('&');

        // فتح نافذة جديدة للطباعة
        const printWindow = window.open(`${AjaxHandler.defaultConfig.baseUrl}${this.endpoints.LIST}&${queryString}`, '_blank');

        // طباعة تلقائية بعد تحميل الصفحة
        printWindow.onload = function() {
            setTimeout(function() {
                printWindow.print();
            }, 500);
        };
    },

    /**
     * تهيئة صفحة إضافة/تعديل منتج
     */
    initProductForm: function() {
        // تحميل الفئات
        this.loadCategories('category_id');

        // تهيئة النموذج للإرسال
        $("#product-form").on('submit', function(e) {
            e.preventDefault();
            ProductManager.saveProduct();
        });

        // إذا كان في وضع التعديل، قم بتحميل بيانات المنتج
        const productId = $("input[name='id']").val();
        if (productId) {
            this.loadProductForEdit(productId);
        }

        // إضافة فئة جديدة
        $("#add-category-btn").on('click', function() {
            ProductManager.showAddCategoryModal();
        });
    },

    /**
     * تحميل بيانات منتج للتعديل
     * @param {number} productId معرف المنتج
     */
    loadProductForEdit: function(productId) {
        AjaxHandler.get(this.endpoints.GET, { id: productId })
            .then(response => {
                if (response.success) {
                    this.fillProductForm(response.data);
                } else {
                    AjaxHandler.showAlert(response.message, 'danger');
                }
            })
            .catch(error => {
                AjaxHandler.showAlert('حدث خطأ في الاتصال بالخادم', 'danger');
            });
    },

    /**
     * ملء نموذج المنتج ببيانات منتج
     * @param {Object} product بيانات المنتج
     */
    fillProductForm: function(product) {
        // ملء حقول النموذج
        $("input[name='name']").val(product.name);
        $("textarea[name='description']").val(product.description);
        $("input[name='price']").val(product.price);
        $("input[name='cost']").val(product.cost);
        $("input[name='min_quantity']").val(product.min_quantity);

        // تحديد حالة المنتج
        if (product.is_active == 1) {
            $("input[name='is_active']").prop('checked', true);
        } else {
            $("input[name='is_active']").prop('checked', false);
        }

        // تحديد نوع المنتج
        if (product.is_for_sale == 1) {
            $("input[name='is_for_sale']").prop('checked', true);
        } else {
            $("input[name='is_for_sale']").prop('checked', false);
        }

        // تحديد الفئة
        if (product.category_id) {
            // إنشاء خيار جديد إذا لم يكن موجودًا
            if ($("#category_id option[value='" + product.category_id + "']").length === 0) {
                const newOption = new Option(product.category_name, product.category_id, true, true);
                $("#category_id").append(newOption);
            }

            $("#category_id").val(product.category_id).trigger('change');
        }

        // تحديد الفرع
        if (product.branch_id) {
            $("#branch_id").val(product.branch_id);
        }

        // عرض الكمية الحالية في المخزون
        if (product.stock !== undefined) {
            $(".current-stock-container").removeClass('d-none');
            $("#current-stock").text(product.stock);
        }
    },

    /**
     * حفظ المنتج (إضافة أو تعديل)
     */
    saveProduct: function() {
        // التحقق من صحة النموذج
        if (!this.validateProductForm()) {
            return;
        }

        // جمع بيانات النموذج
        const formData = new FormData($("#product-form")[0]);

        // تحويل القيم المنطقية
        formData.set('is_active', $("input[name='is_active']").is(':checked') ? 1 : 0);
        formData.set('is_for_sale', $("input[name='is_for_sale']").is(':checked') ? 1 : 0);

        // تحديد نوع العملية (إضافة أو تعديل)
        const isEdit = formData.get('id') ? true : false;
        const endpoint = isEdit ? this.endpoints.UPDATE : this.endpoints.ADD;

        // إرسال البيانات
        AjaxHandler.post(endpoint, Object.fromEntries(formData), {
            contentType: 'application/x-www-form-urlencoded',
            processData: true
        })
            .then(response => {
                if (response.success) {
                    AjaxHandler.showAlert(response.message, 'success');

                    // إعادة التوجيه بعد الحفظ
                    setTimeout(() => {
                        window.location.href = 'index.php';
                    }, 1500);
                } else {
                    AjaxHandler.showAlert(response.message, 'danger');
                }
            })
            .catch(error => {
                AjaxHandler.showAlert('حدث خطأ في الاتصال بالخادم', 'danger');
            });
    },

    /**
     * التحقق من صحة نموذج المنتج
     * @returns {boolean} نتيجة التحقق
     */
    validateProductForm: function() {
        let isValid = true;

        // التحقق من الاسم
        const name = $("input[name='name']").val().trim();
        if (!name) {
            isValid = false;
            this.showFieldError("input[name='name']", 'اسم المنتج مطلوب');
        } else {
            this.clearFieldError("input[name='name']");
        }

        // التحقق من السعر
        const price = parseFloat($("input[name='price']").val());
        if (isNaN(price) || price <= 0) {
            isValid = false;
            this.showFieldError("input[name='price']", 'يرجى إدخال سعر صحيح للمنتج');
        } else {
            this.clearFieldError("input[name='price']");
        }

        return isValid;
    },

    /**
     * عرض خطأ في حقل معين
     * @param {string} selector محدد الحقل
     * @param {string} message رسالة الخطأ
     */
    showFieldError: function(selector, message) {
        $(selector).addClass('is-invalid');

        // إنشاء عنصر رسالة الخطأ إذا لم يكن موجودًا
        if ($(selector).next('.invalid-feedback').length === 0) {
            $(selector).after(`<div class="invalid-feedback">${message}</div>`);
        } else {
            $(selector).next('.invalid-feedback').text(message);
        }
    },

    /**
     * إزالة خطأ من حقل معين
     * @param {string} selector محدد الحقل
     */
    clearFieldError: function(selector) {
        $(selector).removeClass('is-invalid');
    },

    /**
     * تحميل فئات المنتجات
     * @param {string} targetSelector محدد القائمة المنسدلة للفئات
     */
    loadCategories: function(targetSelector) {
        AjaxHandler.get(this.endpoints.CATEGORIES)
            .then(response => {
                if (response.success) {
                    this.populateCategoriesDropdown(response.data, targetSelector);
                }
            })
            .catch(error => {
                console.error('خطأ في تحميل الفئات:', error);
            });
    },

/**
     * ملء القائمة المنسدلة بالفئات
     * @param {Array} categories قائمة الفئات
     * @param {string} targetSelector محدد القائمة المنسدلة
     */
populateCategoriesDropdown: function(categories, targetSelector) {
    const $select = $('#' + targetSelector);

    // تفريغ القائمة، مع الحفاظ على الخيار الافتراضي إذا وجد
    $select.find('option:not([value=""])').remove();

    // إضافة الفئات
    if (categories && categories.length > 0) {
        categories.forEach(category => {
            $select.append(`<option value="${category.id}">${category.name}</option>`);
        });
    }

    // تحديث Select2 إذا كان مستخدمًا
    if ($.fn.select2 && $select.hasClass('select2')) {
        $select.trigger('change');
    }
},

/**
 * عرض نافذة إضافة فئة جديدة
 */
showAddCategoryModal: function() {
    // تهيئة النموذج
    $("#category-form")[0].reset();
    $("#category-id").val('');

    // تغيير عنوان النافذة وزر الحفظ
    $("#categoryModalLabel").text('إضافة فئة جديدة');
    $("#save-category-btn").text('إضافة');

    // عرض النافذة
    $("#categoryModal").modal('show');

    // إضافة حدث الحفظ
    $("#category-form").off('submit').on('submit', function(e) {
        e.preventDefault();
        ProductManager.saveCategory();
    });
},

/**
 * عرض نافذة تعديل فئة
 * @param {number} categoryId معرف الفئة
 * @param {string} categoryName اسم الفئة
 * @param {string} categoryDescription وصف الفئة
 */
showEditCategoryModal: function(categoryId, categoryName, categoryDescription) {
    // ملء النموذج
    $("#category-id").val(categoryId);
    $("#category-name").val(categoryName);
    $("#category-description").val(categoryDescription);

    // تغيير عنوان النافذة وزر الحفظ
    $("#categoryModalLabel").text('تعديل الفئة');
    $("#save-category-btn").text('حفظ التغييرات');

    // عرض النافذة
    $("#categoryModal").modal('show');

    // إضافة حدث الحفظ
    $("#category-form").off('submit').on('submit', function(e) {
        e.preventDefault();
        ProductManager.saveCategory();
    });
},

/**
 * حفظ الفئة (إضافة أو تعديل)
 */
saveCategory: function() {
    // جمع بيانات النموذج
    const formData = {
        name: $("#category-name").val(),
        description: $("#category-description").val()
    };

    // التحقق من الاسم
    if (!formData.name.trim()) {
        this.showFieldError("#category-name", "اسم الفئة مطلوب");
        return;
    }

    // تحديد نوع العملية (إضافة أو تعديل)
    const categoryId = $("#category-id").val();
    const isEdit = categoryId ? true : false;

    if (isEdit) {
        formData.id = categoryId;

        // تحديث الفئة
        AjaxHandler.post(this.endpoints.UPDATE_CATEGORY, formData, {
            contentType: 'application/x-www-form-urlencoded',
            processData: true
        })
            .then(response => {
                if (response.success) {
                    AjaxHandler.showAlert(response.message, 'success');
                    $("#categoryModal").modal('hide');

                    // إعادة تحميل الفئات
                    this.loadCategoriesTable();
                    this.loadCategories('category_id');
                    this.loadCategories('category-filter');
                } else {
                    AjaxHandler.showAlert(response.message, 'danger');
                }
            })
            .catch(error => {
                AjaxHandler.showAlert('حدث خطأ في الاتصال بالخادم', 'danger');
            });
    } else {
        // إضافة فئة جديدة
        AjaxHandler.post(this.endpoints.ADD_CATEGORY, formData, {
            contentType: 'application/x-www-form-urlencoded',
            processData: true
        })
            .then(response => {
                if (response.success) {
                    AjaxHandler.showAlert(response.message, 'success');
                    $("#categoryModal").modal('hide');

                    // إعادة تحميل الفئات
                    this.loadCategoriesTable();
                    this.loadCategories('category_id');
                    this.loadCategories('category-filter');

                    // تحديد الفئة الجديدة في القائمة
                    setTimeout(() => {
                        $("#category_id").val(response.data.id).trigger('change');
                    }, 500);
                } else {
                    AjaxHandler.showAlert(response.message, 'danger');
                }
            })
            .catch(error => {
                AjaxHandler.showAlert('حدث خطأ في الاتصال بالخادم', 'danger');
            });
    }
},

/**
 * تهيئة صفحة فئات المنتجات
 */
initCategoriesPage: function() {
    // تحميل الفئات
    this.loadCategoriesTable();

    // إضافة حدث زر إضافة فئة
    $("#add-category-button").on('click', function() {
        ProductManager.showAddCategoryModal();
    });

    // إضافة أحداث أزرار التعديل
    $(document).on('click', '.edit-category-btn', function() {
        const categoryId = $(this).data('id');
        const categoryName = $(this).data('name');
        const categoryDescription = $(this).data('description');
        ProductManager.showEditCategoryModal(categoryId, categoryName, categoryDescription);
    });
},

/**
 * تحميل جدول فئات المنتجات
 */
loadCategoriesTable: function() {
    AjaxHandler.get(this.endpoints.CATEGORIES)
        .then(response => {
            if (response.success) {
                this.displayCategoriesTable(response.data);
            } else {
                AjaxHandler.showAlert(response.message, 'danger');
            }
        })
        .catch(error => {
            AjaxHandler.showAlert('حدث خطأ في الاتصال بالخادم', 'danger');
        });
},

/**
 * عرض جدول فئات المنتجات
 * @param {Array} categories قائمة الفئات
 */
displayCategoriesTable: function(categories) {
    let html = '';

    if (!categories || categories.length === 0) {
        html = '<tr><td colspan="4" class="text-center">لا توجد فئات للعرض</td></tr>';
    } else {
        categories.forEach(category => {
            html += `
                <tr>
                    <td>${category.id}</td>
                    <td>${category.name}</td>
                    <td>${category.description || '-'}</td>
                    <td>
                        <div class="btn-group">
                            <button type="button" class="btn btn-sm btn-primary edit-category-btn"
                                data-id="${category.id}"
                                data-name="${category.name}"
                                data-description="${category.description || ''}"
                                title="تعديل">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button type="button" class="btn btn-sm btn-danger delete-category-btn"
                                data-id="${category.id}"
                                data-name="${category.name}"
                                title="حذف">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </td>
                </tr>
            `;
        });
    }

    $("#categories-table tbody").html(html);
},

/**
 * تأكيد حذف فئة
 * @param {number} categoryId معرف الفئة
 * @param {string} categoryName اسم الفئة
 */
confirmDeleteCategory: function(categoryId, categoryName) {
    if (confirm(`هل أنت متأكد من حذف الفئة "${categoryName}"؟ سيتم إزالة تصنيف جميع المنتجات المرتبطة بهذه الفئة.`)) {
        this.deleteCategory(categoryId);
    }
},

/**
 * حذف فئة
 * @param {number} categoryId معرف الفئة
 */
deleteCategory: function(categoryId) {
    AjaxHandler.delete(this.endpoints.DELETE_CATEGORY, { id: categoryId })
        .then(response => {
            if (response.success) {
                AjaxHandler.showAlert(response.message, 'success');
                this.loadCategoriesTable();

                // إعادة تحميل قوائم الفئات في الصفحة
                this.loadCategories('category_id');
                this.loadCategories('category-filter');
            } else {
                AjaxHandler.showAlert(response.message, 'danger');
            }
        })
        .catch(error => {
            AjaxHandler.showAlert('حدث خطأ في الاتصال بالخادم', 'danger');
        });
},

/**
 * تنسيق التاريخ
 * @param {string} dateString سلسلة التاريخ
 * @returns {string} التاريخ المنسق
 */
formatDate: function(dateString) {
    if (!dateString) return '-';

    const date = new Date(dateString);
    if (isNaN(date.getTime())) return dateString;

    return date.toLocaleDateString('ar-SA', {
        year: 'numeric',
        month: 'numeric',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    });
},

/**
 * تنسيق المبلغ بصيغة العملة
 * @param {number} amount المبلغ
 * @returns {string} المبلغ المنسق
 */
formatCurrency: function(amount) {
    if (amount === null || amount === undefined) return '-';

    // استخدام رمز العملة من قاعدة البيانات إذا كان متاحًا
    return parseFloat(amount).toFixed(2) + ' ' + (typeof currencySymbol !== 'undefined' ? currencySymbol : 'ر.س');
},

/**
 * دالة تأخير
 * @param {Function} func الدالة المراد تأخيرها
 * @param {number} wait وقت الانتظار بالمللي ثانية
 * @returns {Function} الدالة المؤخرة
 */
debounce: function(func, wait) {
    let timeout;
    return function() {
        const context = this, args = arguments;
        clearTimeout(timeout);
        timeout = setTimeout(function() {
            func.apply(context, args);
        }, wait);
    };
}
};

// تهيئة مدير المنتجات عند تحميل الصفحة
$(document).ready(function() {
ProductManager.init();
});