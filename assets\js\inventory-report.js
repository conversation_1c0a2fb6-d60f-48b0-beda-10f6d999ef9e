/**
 * Salon Management System - Inventory Report Page Functionality
 *
 * @version 1.0.0
 * <AUTHOR> Management System Team
 */

// Currency symbol from database
const CURRENCY_SYMBOL = typeof currencySymbol !== 'undefined' ? currencySymbol : 'ر.س';

$(document).ready(function() {
    // Initialize date range picker
    $('#inventory-date-range').daterangepicker({
        locale: {
            format: 'YYYY/MM/DD',
            applyLabel: 'تطبيق',
            cancelLabel: 'إلغاء',
            fromLabel: 'من',
            toLabel: 'إلى',
            customRangeLabel: 'مخصص',
            daysOfWeek: ['أحد', 'إثنين', 'ثلاثاء', 'أربعاء', 'خميس', 'جمعة', 'سبت'],
            monthNames: ['يناير', 'فبراير', 'مارس', 'إبريل', 'مايو', 'يونيو', 'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'],
            firstDay: 6
        },
        startDate: moment().subtract(30, 'days'),
        endDate: moment(),
        ranges: {
            'اليوم': [moment(), moment()],
            'أمس': [moment().subtract(1, 'days'), moment().subtract(1, 'days')],
            'آخر 7 أيام': [moment().subtract(6, 'days'), moment()],
            'آخر 30 يوم': [moment().subtract(29, 'days'), moment()],
            'هذا الشهر': [moment().startOf('month'), moment().endOf('month')],
            'الشهر الماضي': [moment().subtract(1, 'month').startOf('month'), moment().subtract(1, 'month').endOf('month')]
        }
    });

    // Inventory Report Form Handling
    $('#inventory-report-form').on('submit', function(e) {
        e.preventDefault();
        generateInventoryReport();
    });

    // Load report on page load
    generateInventoryReport();

    /**
     * Generate Inventory Report
     */
    function generateInventoryReport() {
        // Show loading alert
        $('#loadingAlert').removeClass('d-none');
        $('#errorAlert').addClass('d-none');
        $('#inventory-report-results').addClass('d-none');

        // Get filter values
        const dateRange = $('#inventory-date-range').data('daterangepicker');
        const formData = {
            type: 'inventory',
            branch_id: $('#inventory-branch-select').val(),
            category_id: $('#inventory-category-select').val(),
            start_date: dateRange.startDate.format('YYYY-MM-DD'),
            end_date: dateRange.endDate.format('YYYY-MM-DD')
        };

        // Send AJAX request
        $.ajax({
            url: BASE_URL + 'api/reports.php',
            type: 'GET',
            data: formData,
            dataType: 'json',
            success: function(response) {
                // Hide loading alert
                $('#loadingAlert').addClass('d-none');

                if (response.success) {
                    renderInventoryReport(response.data);
                } else {
                    // Show error message
                    $('#errorAlert').removeClass('d-none').text(response.message || 'حدث خطأ أثناء تحميل التقرير');
                }
            },
            error: function(xhr, status, error) {
                // Hide loading alert and show error message
                $('#loadingAlert').addClass('d-none');
                $('#errorAlert').removeClass('d-none').text('حدث خطأ أثناء الاتصال بالخادم: ' + error);
            }
        });
    }

    /**
     * Render Inventory Report
     * @param {Object} report - Report data
     */
    function renderInventoryReport(report) {
        // Show report results container
        $('#inventory-report-results').removeClass('d-none');

        // Format numbers as currency
        const formatCurrency = (value) => {
            return parseFloat(value || 0).toLocaleString('ar-EG', {
                style: 'currency',
                currency: 'EGP'
            });
        };

        // Fill summary data
        $('#total-inventory-cost').text(formatCurrency(report.stock_value.total_cost));
        $('#total-inventory-value').text(formatCurrency(report.stock_value.total_value));
        $('#expected-profit').text(formatCurrency(report.stock_value.expected_profit));
        $('#total-products-count').text(report.summary.total_products || 0);

        // Fill sold products data
        $('#sold-products-quantity').text(report.sold_products.total_quantity || 0);
        $('#sold-products-value').text(formatCurrency(report.sold_products.total_value));
        $('#sold-products-cost').text(formatCurrency(report.sold_products.total_cost));
        $('#sold-products-profit').text(formatCurrency(report.sold_products.total_profit));

        // Fill consumed products data
        $('#consumed-products-quantity').text(report.consumed_products.total_quantity || 0);
        $('#consumed-products-value').text(formatCurrency(report.consumed_products.total_value));

        // Fill top selling products table
        const topSellingTable = $('#top-selling-products-table tbody');
        topSellingTable.empty();

        if (report.top_selling_products && report.top_selling_products.length > 0) {
            report.top_selling_products.forEach((product, index) => {
                topSellingTable.append(`
                    <tr>
                        <td>${index + 1}</td>
                        <td>${product.product_name}</td>
                        <td>${product.quantity}</td>
                        <td>${formatCurrency(product.total_value)}</td>
                    </tr>
                `);
            });
        } else {
            topSellingTable.append('<tr><td colspan="4" class="text-center">لا توجد بيانات</td></tr>');
        }

        // Fill top consumed products table
        const topConsumedTable = $('#top-consumed-products-table tbody');
        topConsumedTable.empty();

        if (report.top_consumed_products && report.top_consumed_products.length > 0) {
            report.top_consumed_products.forEach((product, index) => {
                topConsumedTable.append(`
                    <tr>
                        <td>${index + 1}</td>
                        <td>${product.product_name}</td>
                        <td>${product.quantity}</td>
                        <td>${formatCurrency(product.total_value)}</td>
                    </tr>
                `);
            });
        } else {
            topConsumedTable.append('<tr><td colspan="4" class="text-center">لا توجد بيانات</td></tr>');
        }

        // Fill low stock products table
        const lowStockTable = $('#low-stock-products-table tbody');
        lowStockTable.empty();

        if (report.low_stock_products && report.low_stock_products.length > 0) {
            report.low_stock_products.forEach((product, index) => {
                lowStockTable.append(`
                    <tr>
                        <td>${index + 1}</td>
                        <td>${product.product_name}</td>
                        <td>${product.category_name || 'غير مصنف'}</td>
                        <td>${product.quantity}</td>
                        <td>${product.min_quantity}</td>
                        <td>${formatCurrency(product.cost)}</td>
                        <td>${formatCurrency(product.price)}</td>
                        <td>${formatCurrency(product.quantity * product.price)}</td>
                    </tr>
                `);
            });
        } else {
            lowStockTable.append('<tr><td colspan="8" class="text-center">لا توجد منتجات منخفضة المخزون</td></tr>');
        }
    }

    /**
     * Export Report to CSV
     */
    $('.export-report-btn').on('click', function() {
        const tables = {
            'top-selling-products': $('#top-selling-products-table'),
            'top-consumed-products': $('#top-consumed-products-table'),
            'low-stock-products': $('#low-stock-products-table')
        };
        
        const tableId = $(this).data('table') || 'low-stock-products';
        const $table = tables[tableId];
        
        if (!$table || $table.length === 0) {
            alert('لا يوجد بيانات للتصدير');
            return;
        }
        
        const filename = `inventory_report_${tableId}_${new Date().toISOString().split('T')[0]}.csv`;
        
        // Convert table to CSV
        const csv = [];
        const $headers = $table.find('thead tr th');
        const headerRow = [];
        
        $headers.each(function() {
            headerRow.push($(this).text());
        });
        csv.push(headerRow.join(','));
        
        // Add table rows
        const $rows = $table.find('tbody tr');
        $rows.each(function() {
            const row = [];
            $(this).find('td').each(function() {
                // Remove commas and escape special characters
                let cellText = $(this).text().trim().replace(/,/g, '').replace(/\n/g, ' ');
                row.push(cellText);
            });
            csv.push(row.join(','));
        });
        
        // Create and download CSV file
        const csvContent = csv.join('\n');
        const blob = new Blob(['\uFEFF' + csvContent], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        
        if (navigator.msSaveBlob) { // For IE 10+
            navigator.msSaveBlob(blob, filename);
        } else {
            link.href = URL.createObjectURL(blob);
            link.download = filename;
            link.style.display = 'none';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }
    });

    /**
     * Print Report
     */
    $('.print-report-btn').on('click', function() {
        const $container = $('#inventory-report-results');
        
        // Check if report exists
        if ($container.children().length === 0) {
            alert('لا يوجد تقرير للطباعة');
            return;
        }
        
        // Create a new window for printing
        const printWindow = window.open('', '_blank', 'width=800,height=600');
        
        // Basic print styling
        printWindow.document.write(`
            <html>
                <head>
                    <title>تقرير المخزون</title>
                    <style>
                        body { font-family: Arial, sans-serif; direction: rtl; }
                        .row { display: flex; flex-wrap: wrap; margin-bottom: 20px; }
                        .col-md-3, .col-md-6 { width: 25%; padding: 10px; box-sizing: border-box; }
                        .col-md-6 { width: 50%; }
                        .card { border: 1px solid #ddd; margin-bottom: 10px; padding: 10px; }
                        .card-header { font-weight: bold; margin-bottom: 10px; padding-bottom: 5px; border-bottom: 1px solid #eee; }
                        .card-body { padding: 10px 0; }
                        h3, h4, h5 { margin-top: 10px; margin-bottom: 10px; }
                        table { width: 100%; border-collapse: collapse; margin-top: 20px; }
                        table, th, td { border: 1px solid #ddd; }
                        th, td { padding: 8px; text-align: right; }
                        th { background-color: #f2f2f2; }
                    </style>
                </head>
                <body>
                    <div class="report-header">
                        <h1>تقرير المخزون</h1>
                        <p>تاريخ التقرير: ${new Date().toLocaleDateString()}</p>
                    </div>
                    ${$container.html()}
                </body>
            </html>
        `);
        
        printWindow.document.close();
        printWindow.print();
    });
});
