<?php
/**
 * API para obtener datos necesarios para enviar mensajes de WhatsApp
 * Esta API solo proporciona los datos necesarios, el envío se realiza desde el cliente
 */

// Definir constante para prevenir acceso directo a archivos incluidos
define('BASEPATH', true);

// Configurar encabezados CORS para permitir acceso desde cualquier origen
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, X-Ignore-SSL');
header('Content-Type: application/json; charset=UTF-8');

// Manejar solicitudes OPTIONS para CORS
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// Incluir archivos de inicialización
require_once '../config/init.php';

// Verificar si el usuario ha iniciado sesión
if (!isLoggedIn()) {
    echo json_encode([
        'status' => 'error',
        'message' => 'Debe iniciar sesión primero'
    ]);
    exit;
}

// Obtener la acción solicitada
$action = isset($_GET['action']) ? $_GET['action'] : '';

// Procesar la solicitud
try {
    $db = new Database();
    
    switch ($action) {
        // Obtener datos para enviar notificación de factura
        case 'get_invoice_notification_data':
            // Verificar si se proporcionó el ID de la factura
            if (!isset($_GET['invoice_id']) || empty($_GET['invoice_id'])) {
                throw new Exception('Se requiere el ID de la factura');
            }
            
            $invoiceId = intval($_GET['invoice_id']);
            
            // Obtener datos de la factura
            $invoiceModel = new Invoice($db);
            $invoice = $invoiceModel->getInvoiceById($invoiceId);
            
            if (!$invoice) {
                throw new Exception('Factura no encontrada');
            }
            
            // Verificar si la factura tiene un número de teléfono de cliente
            if (empty($invoice['customer_phone'])) {
                throw new Exception('La factura no tiene un número de teléfono de cliente');
            }
            
            // Obtener configuración de notificaciones
            $settingsModel = new Settings($db);
            $settings = $settingsModel->getAllSettings();
            
            // Verificar si las notificaciones de facturas están habilitadas
            $notificationsEnabled = isset($settings['notification_nvoice_notification']) && 
                                   $settings['notification_nvoice_notification'] === 'on';
            
            if (!$notificationsEnabled) {
                // Activar temporalmente para esta solicitud
                $settingsModel->updateSetting('notification_nvoice_notification', 'on');
            }
            
            // Crear el mensaje de la notificación
            $invoiceDate = date('d/m/Y', strtotime($invoice['invoice_date']));
            $invoiceTime = date('h:i A', strtotime($invoice['created_at']));
            $invoiceTotal = number_format($invoice['total_amount'], 2);
            
            $message = "شكراً لزيارتكم " . $settings['company_name'] . "\n\n";
            $message .= "تفاصيل الفاتورة رقم: " . $invoice['invoice_number'] . "\n";
            $message .= "التاريخ: " . $invoiceDate . "\n";
            $message .= "الوقت: " . $invoiceTime . "\n";
            $message .= "المبلغ الإجمالي: " . $invoiceTotal . " " . $settings['currency_symbol'] . "\n\n";
            $message .= "نتطلع لزيارتكم مرة أخرى!";
            
            // Formatear el número de teléfono
            $phone = $invoice['customer_phone'];
            
            // Eliminar el primer 0 si existe
            if (substr($phone, 0, 1) === '0') {
                $phone = substr($phone, 1);
            }
            
            // Agregar el código de país si no existe
            if (!preg_match('/^20/', $phone)) {
                $phone = '20' . $phone;
            }
            
            // Devolver los datos necesarios para enviar el mensaje
            echo json_encode([
                'status' => 'success',
                'data' => [
                    'phone' => $phone,
                    'message' => $message,
                    'invoice_id' => $invoiceId,
                    'customer_name' => $invoice['customer_name']
                ]
            ]);
            
            // Restaurar la configuración original si se cambió
            if (!$notificationsEnabled) {
                $settingsModel->updateSetting('notification_nvoice_notification', 'off');
            }
            break;
            
        // Obtener datos para enviar notificación de factura a administradores
        case 'get_admin_notification_data':
            // Verificar si se proporcionó el ID de la factura
            if (!isset($_GET['invoice_id']) || empty($_GET['invoice_id'])) {
                throw new Exception('Se requiere el ID de la factura');
            }
            
            $invoiceId = intval($_GET['invoice_id']);
            
            // Obtener datos de la factura
            $invoiceModel = new Invoice($db);
            $invoice = $invoiceModel->getInvoiceById($invoiceId);
            
            if (!$invoice) {
                throw new Exception('Factura no encontrada');
            }
            
            // Obtener números de teléfono de administradores
            $userModel = new User($db);
            $admins = $userModel->getUsersByRole('admin');
            
            if (empty($admins)) {
                throw new Exception('No se encontraron administradores');
            }
            
            // Filtrar administradores con número de teléfono
            $adminPhones = [];
            foreach ($admins as $admin) {
                if (!empty($admin['phone'])) {
                    $phone = $admin['phone'];
                    
                    // Eliminar el primer 0 si existe
                    if (substr($phone, 0, 1) === '0') {
                        $phone = substr($phone, 1);
                    }
                    
                    // Agregar el código de país si no existe
                    if (!preg_match('/^20/', $phone)) {
                        $phone = '20' . $phone;
                    }
                    
                    $adminPhones[] = [
                        'name' => $admin['name'],
                        'phone' => $phone
                    ];
                }
            }
            
            if (empty($adminPhones)) {
                throw new Exception('No se encontraron administradores con número de teléfono');
            }
            
            // Obtener configuración
            $settingsModel = new Settings($db);
            $settings = $settingsModel->getAllSettings();
            
            // Crear el mensaje de la notificación
            $invoiceDate = date('d/m/Y', strtotime($invoice['invoice_date']));
            $invoiceTime = date('h:i A', strtotime($invoice['created_at']));
            $invoiceTotal = number_format($invoice['total_amount'], 2);
            
            $message = "تنبيه: فاتورة جديدة\n\n";
            $message .= "رقم الفاتورة: " . $invoice['invoice_number'] . "\n";
            $message .= "العميل: " . $invoice['customer_name'] . "\n";
            $message .= "التاريخ: " . $invoiceDate . "\n";
            $message .= "الوقت: " . $invoiceTime . "\n";
            $message .= "المبلغ الإجمالي: " . $invoiceTotal . " " . $settings['currency_symbol'] . "\n";
            
            // Obtener el nombre del usuario que creó la factura
            $createdBy = $userModel->getUserById($invoice['created_by']);
            if ($createdBy) {
                $message .= "تم إنشاؤها بواسطة: " . $createdBy['name'] . "\n";
            }
            
            // Devolver los datos necesarios para enviar el mensaje
            echo json_encode([
                'status' => 'success',
                'data' => [
                    'admins' => $adminPhones,
                    'message' => $message,
                    'invoice_id' => $invoiceId
                ]
            ]);
            break;
            
        default:
            throw new Exception('Acción no válida');
    }
} catch (Exception $e) {
    echo json_encode([
        'status' => 'error',
        'message' => $e->getMessage()
    ]);
}
