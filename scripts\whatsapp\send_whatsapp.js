/**
 * سكريبت إرسال رسائل WhatsApp باستخدام Puppeteer
 *
 * استخدام:
 * node send_whatsapp.js [مسار ملف JSON يحتوي على بيانات الرسالة]
 *
 * بنية ملف JSON:
 * {
 *   "phone": "201234567890",
 *   "message": "نص الرسالة",
 *   "options": {
 *     "waitForLogin": true,
 *     "clickSend": true
 *   }
 * }
 */

const puppeteer = require('puppeteer');
const fs = require('fs');
const path = require('path');

// الحصول على مسار ملف بيانات الرسالة
const messageFilePath = process.argv[2];

if (!messageFilePath) {
    console.error('يرجى تحديد مسار ملف بيانات الرسالة');
    process.exit(1);
}

// قراءة بيانات الرسالة
let messageData;
try {
    const fileContent = fs.readFileSync(messageFilePath, 'utf8');
    messageData = JSON.parse(fileContent);
} catch (error) {
    console.error('خطأ في قراءة ملف بيانات الرسالة:', error.message);
    process.exit(1);
}

const { phone, message, options = {} } = messageData;

// التحقق من وجود البيانات المطلوبة
if (!phone || !message) {
    console.error('يجب توفير رقم الهاتف ونص الرسالة');
    process.exit(1);
}

// تعيين الخيارات الافتراضية
const defaultOptions = {
    waitForLogin: true,
    clickSend: true,
    timeout: 60000,
    userDataDir: path.join(__dirname, 'whatsapp-session')
};

// دمج الخيارات المخصصة مع الخيارات الافتراضية
const finalOptions = { ...defaultOptions, ...options };

// تسجيل بداية العملية
console.log(`بدء إرسال رسالة WhatsApp إلى ${phone}`);
console.log(`الرسالة: ${message.substring(0, 50)}${message.length > 50 ? '...' : ''}`);

(async () => {
    let browser;
    try {
        // إنشاء مجلد جلسة WhatsApp إذا لم يكن موجودًا
        if (!fs.existsSync(finalOptions.userDataDir)) {
            fs.mkdirSync(finalOptions.userDataDir, { recursive: true });
        }

        // تحديد User-Agent لتحسين التوافق مع WhatsApp Web
        const userAgent = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36';

        // إطلاق المتصفح مع خيارات محسنة لتجنب مشكلة الكراش
        browser = await puppeteer.launch({
            headless: false, // يجب أن يكون false للتفاعل مع WhatsApp Web
            args: [
                '--no-sandbox',
                '--disable-setuid-sandbox',
                '--disable-dev-shm-usage',
                '--disable-accelerated-2d-canvas',
                '--no-first-run',
                '--no-zygote',
                '--disable-gpu',
                '--disable-extensions',
                '--disable-background-networking',
                '--disable-default-apps',
                '--disable-sync',
                '--disable-translate',
                '--hide-scrollbars',
                '--metrics-recording-only',
                '--mute-audio',
                '--no-first-run',
                '--safebrowsing-disable-auto-update',
                '--js-flags=--max-old-space-size=2048',
                `--user-agent=${userAgent}`
            ],
            userDataDir: finalOptions.userDataDir, // حفظ بيانات الجلسة
            defaultViewport: null // السماح للمتصفح باستخدام حجم النافذة الكامل
        });

        // فتح صفحة جديدة
        const page = await browser.newPage();

        // تعيين User-Agent للصفحة
        await page.setUserAgent(userAgent);

        // ضبط حجم النافذة
        await page.setViewport({ width: 1280, height: 800 });

        // إعدادات لتحسين الأداء وتجنب الكراش
        page.setDefaultNavigationTimeout(120000); // زيادة مهلة التصفح

        // تعطيل بعض الميزات لتحسين الأداء
        await page.setRequestInterception(true);
        page.on('request', (req) => {
            const resourceType = req.resourceType();
            if (resourceType === 'image' || resourceType === 'stylesheet' || resourceType === 'font') {
                req.abort();
            } else {
                req.continue();
            }
        });

        // فتح رابط WhatsApp مع رقم الهاتف والرسالة
        const url = `https://web.whatsapp.com/send?phone=${phone}&text=${encodeURIComponent(message)}`;
        console.log(`جاري فتح الرابط: ${url}`);

        await page.goto(url, { waitUntil: 'networkidle2', timeout: 120000 });
        console.log('تم تحميل الصفحة بنجاح');

        // التحقق مما إذا كان المستخدم بحاجة إلى تسجيل الدخول
        const needsLogin = await page.evaluate(() => {
            return document.querySelector('div[data-ref]') !== null ||
                   document.querySelector('div[data-testid="qr-code-canvas"]') !== null;
        });

        if (needsLogin) {
            console.log('يرجى مسح رمز QR لتسجيل الدخول إلى WhatsApp Web');

            if (finalOptions.waitForLogin) {
                // انتظار تسجيل الدخول (ظهور واجهة المحادثات)
                try {
                    await page.waitForSelector('div[data-testid="chat-list"]', { timeout: 120000 });
                    console.log('تم تسجيل الدخول بنجاح');
                } catch (loginError) {
                    console.error('فشل انتظار تسجيل الدخول:', loginError.message);
                    process.exit(1);
                }
            } else {
                console.log('تم تخطي انتظار تسجيل الدخول');
                process.exit(0);
            }
        }

        console.log('جاري انتظار ظهور زر الإرسال...');

        // محاولة العثور على زر الإرسال بعدة طرق
        try {
            // محاولة العثور على زر الإرسال باستخدام المحدد data-icon="send"
            await page.waitForSelector('span[data-icon="send"]', { timeout: finalOptions.timeout });
            console.log('تم العثور على زر الإرسال');

            // انتظار قليلاً للتأكد من تحميل الصفحة بالكامل
            // استخدام setTimeout بدلاً من page.waitForTimeout للتوافق مع الإصدار الجديد من Puppeteer
            await new Promise(resolve => setTimeout(resolve, 2000));

            if (finalOptions.clickSend) {
                // النقر على زر الإرسال
                await page.click('span[data-icon="send"]');
                console.log('تم النقر على زر الإرسال');

                // انتظار إرسال الرسالة
                await new Promise(resolve => setTimeout(resolve, 3000));
            } else {
                console.log('تم تخطي النقر على زر الإرسال');
            }
        } catch (selectorError) {
            console.log('لم يتم العثور على زر الإرسال بالطريقة الأولى، جاري المحاولة بطريقة أخرى...');

            try {
                // محاولة العثور على حقل الرسالة
                await page.waitForSelector('div[contenteditable="true"]', { timeout: finalOptions.timeout });
                console.log('تم العثور على حقل الرسالة');

                // انتظار قليلاً للتأكد من تحميل الصفحة بالكامل
                // استخدام setTimeout للتوافق مع الإصدار الجديد من Puppeteer
                await new Promise(resolve => setTimeout(resolve, 2000));

                if (finalOptions.clickSend) {
                    // محاولة بديلة باستخدام الضغط على Enter
                    await page.keyboard.press('Enter');
                    console.log('تم الضغط على مفتاح Enter لإرسال الرسالة');

                    // انتظار إرسال الرسالة
                    await new Promise(resolve => setTimeout(resolve, 3000));
                } else {
                    console.log('تم تخطي النقر على زر الإرسال');
                }
            } catch (inputError) {
                console.error('لم يتم العثور على حقل الرسالة:', inputError.message);
                throw new Error('لم يتم العثور على زر الإرسال أو حقل الرسالة');
            }
        }

        // التقاط لقطة شاشة للتوثيق (اختياري)
        try {
            // التأكد من وجود مجلد السجلات
            const logsDir = path.join(__dirname, 'logs');
            if (!fs.existsSync(logsDir)) {
                fs.mkdirSync(logsDir, { recursive: true });
            }

            const screenshotPath = path.join(logsDir, `whatsapp_${Date.now()}.png`);
            await page.screenshot({ path: screenshotPath });
            console.log(`تم حفظ لقطة شاشة في: ${screenshotPath}`);
        } catch (screenshotError) {
            console.error('خطأ في التقاط لقطة شاشة:', screenshotError.message);
        }

        console.log('تم إرسال الرسالة بنجاح');

        // إغلاق الصفحة بدلاً من إغلاق المتصفح بالكامل
        await page.close();
        console.log('تم إغلاق الصفحة');

        // لا نغلق المتصفح للحفاظ على الجلسة
        process.exit(0);
    } catch (error) {
        console.error('حدث خطأ أثناء إرسال الرسالة:', error.message);

        // محاولة إغلاق الصفحة إذا كانت مفتوحة
        if (page) {
            try {
                await page.close();
                console.log('تم إغلاق الصفحة بعد حدوث خطأ');
            } catch (closeError) {
                console.error('خطأ في إغلاق الصفحة:', closeError.message);
            }
        }

        // لا نغلق المتصفح للحفاظ على الجلسة
        process.exit(1);
    }
})();
