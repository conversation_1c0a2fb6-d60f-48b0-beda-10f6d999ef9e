<?php
/**
 * صفحة إضافة منتج جديد
 * تسمح للمستخدمين بإضافة منتجات جديدة إلى النظام
 */

// تعريف ثابت لمنع الوصول المباشر للملفات المرتبطة
define('BASEPATH', true);

// استدعاء ملف التهيئة
require_once '../../config/init.php';

// التحقق من تسجيل الدخول
redirectIfNotLoggedIn();

// التحقق من صلاحية الوصول
if (!hasPermission('products_add')) {
    $_SESSION['error_message'] = 'ليس لديك صلاحية لإضافة منتجات جديدة';
    header('Location: ' . BASE_URL . 'pages/products/index.php');
    exit;
}

// تحديد الفلاتر الافتراضية
$currentBranchId = $_SESSION['user_branch_id'];
$isAdmin = ($_SESSION['user_role'] === ROLE_ADMIN);

// الحصول على فئات المنتجات
$productObj = new Product($db);
$categories = $productObj->getProductCategories();

// الحصول على رمز العملة من الإعدادات
$settingsModel = new Settings($db);
$currencySymbol = $settingsModel->get('system_currency_symbol', 'ر.س');
$currencyName = $settingsModel->get('system_currency', 'ريال سعودي');

// الحصول على الفروع (للمدير فقط)
$branches = [];
if ($isAdmin) {
    $branchObj = new Branch($db);
    $branches = $branchObj->getBranches();
}

// عنوان الصفحة
$pageTitle = 'إضافة منتج جديد';

// استدعاء قالب الرأس
include_once '../../includes/templates/header.php';
?>

<!-- بداية محتوى الصفحة -->
<div class="page-content">
    <div class="container-fluid">

        <!-- عنوان الصفحة والأزرار -->
        <div class="row mb-4 align-items-center">
            <div class="col-md-6">
                <h4 class="fw-bold mb-2"><?php echo $pageTitle; ?></h4>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb mb-0">
                        <li class="breadcrumb-item">
                            <a href="<?php echo BASE_URL . 'pages/dashboard.php'; ?>" class="text-decoration-none">
                                <i class="fas fa-home me-1"></i>الرئيسية
                            </a>
                        </li>
                        <li class="breadcrumb-item">
                            <a href="<?php echo BASE_URL . 'pages/products/index.php'; ?>" class="text-decoration-none">
                                المنتجات
                            </a>
                        </li>
                        <li class="breadcrumb-item active" aria-current="page">إضافة منتج</li>
                    </ol>
                </nav>
            </div>
            <div class="col-md-6 text-md-end">
                <a href="<?php echo BASE_URL . 'pages/products/index.php'; ?>" class="btn btn-secondary">
                    <i class="fas fa-arrow-right me-1"></i> العودة للمنتجات
                </a>
            </div>
        </div>

        <!-- نموذج إضافة منتج -->
        <div class="card">
            <div class="card-header bg-light">
                <h5 class="mb-0">معلومات المنتج</h5>
            </div>
            <div class="card-body">
                <form id="add-product-form" method="post">
                    <!-- البيانات الأساسية -->
                    <div class="row mb-4">
                        <div class="col-12 mb-3">
                            <h6 class="fw-bold text-primary border-bottom pb-2">البيانات الأساسية</h6>
                        </div>

                        <div class="col-md-6 mb-3">
                            <label for="name" class="form-label">اسم المنتج <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="name" name="name" required>
                        </div>

                        <div class="col-md-6 mb-3">
                            <label for="category_id" class="form-label">الفئة</label>
                            <select class="form-select" id="category_id" name="category_id">
                                <option value="">-- اختر الفئة --</option>
                                <?php foreach ($categories as $category): ?>
                                    <option value="<?php echo $category['id']; ?>"><?php echo $category['name']; ?></option>
                                <?php endforeach; ?>
                            </select>
                            <?php if (hasPermission('products_edit')): ?>
                                <div class="form-text">
                                    <a href="<?php echo BASE_URL . 'pages/products/categories.php'; ?>" target="_blank">
                                        <i class="fas fa-plus-circle"></i> إضافة فئة جديدة
                                    </a>
                                </div>
                            <?php endif; ?>
                        </div>

                        <div class="col-md-6 mb-3">
                            <label for="is_for_sale" class="form-label">نوع المنتج</label>
                            <select class="form-select" id="is_for_sale" name="is_for_sale">
                                <option value="1" selected>للبيع</option>
                                <option value="0">للاستخدام الداخلي</option>
                            </select>
                            <div class="form-text">
                                المنتجات المخصصة للبيع هي التي يمكن إضافتها للفواتير، أما المخصصة للاستخدام الداخلي فهي للاستخدام في تقديم الخدمات.
                            </div>
                        </div>

                        <div class="col-md-6 mb-3">
                            <label for="is_active" class="form-label">الحالة</label>
                            <select class="form-select" id="is_active" name="is_active">
                                <option value="1" selected>نشط</option>
                                <option value="0">غير نشط</option>
                            </select>
                        </div>

                        <?php if ($isAdmin): ?>
                            <div class="col-md-6 mb-3">
                                <label for="branch_id" class="form-label">الفرع</label>
                                <select class="form-select" id="branch_id" name="branch_id">
                                    <option value="">جميع الفروع</option>
                                    <?php foreach ($branches as $branch): ?>
                                        <option value="<?php echo $branch['id']; ?>" <?php echo ($branch['id'] == $currentBranchId) ? 'selected' : ''; ?>>
                                            <?php echo $branch['name']; ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                                <div class="form-text">
                                    إذا تم اختيار "جميع الفروع" سيكون المنتج متاحٍ في جميع الفروع، وإلا سيكون متاحٍ في الفرع المحدد فقط.
                                </div>
                            </div>
                        <?php else: ?>
                            <input type="hidden" name="branch_id" value="<?php echo $currentBranchId; ?>">
                        <?php endif; ?>
                    </div>

                    <!-- معلومات السعر والتكلفة -->
                    <div class="row mb-4">
                        <div class="col-12 mb-3">
                            <h6 class="fw-bold text-primary border-bottom pb-2">معلومات السعر والتكلفة</h6>
                        </div>

                        <div class="col-md-6 mb-3">
                            <label for="price" class="form-label">سعر البيع <span class="text-danger">*</span></label>
                            <div class="input-group">
                                <input type="number" class="form-control" id="price" name="price" min="0" step="0.01" required>
                                <span class="input-group-text"><?php echo $currencySymbol; ?></span>
                            </div>
                        </div>

                        <div class="col-md-6 mb-3">
                            <label for="cost" class="form-label">تكلفة المنتج</label>
                            <div class="input-group">
                                <input type="number" class="form-control" id="cost" name="cost" min="0" step="0.01" value="0">
                                <span class="input-group-text"><?php echo $currencySymbol; ?></span>
                            </div>
                            <div class="form-text">
                                تكلفة شراء المنتج من المورد، تستخدم لحساب الأرباح.
                            </div>
                        </div>

                        <div class="col-md-12 mb-3">
                            <div class="alert alert-info">
                                <div class="d-flex">
                                    <div class="me-3">
                                        <i class="fas fa-info-circle fa-2x"></i>
                                    </div>
                                    <div>
                                        <h6 class="alert-heading">معلومات الربح</h6>
                                        <p class="mb-0">الربح المتوقع: <span id="profit-amount">0.00</span> <?php echo $currencySymbol; ?></p>
                                        <p class="mb-0">نسبة الربح: <span id="profit-percentage">0%</span></p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- معلومات المخزون -->
                    <div class="row mb-4">
                        <div class="col-12 mb-3">
                            <h6 class="fw-bold text-primary border-bottom pb-2">معلومات المخزون</h6>
                        </div>

                        <div class="col-md-6 mb-3">
                            <label for="initial_quantity" class="form-label">الكمية الأولية</label>
                            <input type="number" class="form-control" id="initial_quantity" name="initial_quantity" min="0" value="0">
                            <div class="form-text">
                                الكمية المتوفرة حالٍ من المنتج، اتركها 0 إذا كان المخزون فارغاً.
                            </div>
                        </div>

                        <div class="col-md-6 mb-3">
                            <label for="min_quantity" class="form-label">الحد الأدنى للمخزون</label>
                            <input type="number" class="form-control" id="min_quantity" name="min_quantity" min="0" value="5">
                            <div class="form-text">
                                عندما تنخفض الكمية عن هذا الحد سيظهر تنبيه بأن المخزون منخفض.
                            </div>
                        </div>
                    </div>

                    <!-- الوصف -->
                    <div class="row mb-4">
                        <div class="col-12 mb-3">
                            <h6 class="fw-bold text-primary border-bottom pb-2">وصف المنتج</h6>
                        </div>

                        <div class="col-12">
                            <label for="description" class="form-label">الوصف</label>
                            <textarea class="form-control" id="description" name="description" rows="4"></textarea>
                        </div>
                    </div>

                    <!-- أزرار الإجراءات -->
                    <div class="row mt-4">
                        <div class="col-12 text-end">
                            <hr>
                            <button type="reset" class="btn btn-outline-secondary me-2">
                                <i class="fas fa-redo me-1"></i> إعادة تعيين
                            </button>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-1"></i> حفظ المنتج
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>

    </div>
</div>

<!-- جافاسكربت خاص بالصفحة -->
<script>
    // التحقق من وجود jQuery وتحميلها إذا لم تكن موجودة
    if (typeof jQuery === 'undefined') {
        document.write('<script src="https://code.jquery.com/jquery-3.6.0.min.js"><\/script>');
    }

    // استخدام window.onload بدلاً من $(document).ready للتأكد من تحميل jQuery
    window.onload = function() {
        // التحقق مرة أخرى من وجود jQuery
        if (typeof jQuery === 'undefined') {
            console.error('فشل تحميل jQuery. بعض الوظائف قد لا تعمل بشكل صحيح.');
            return;
        }

        // حساب الربح عند تغيير السعر أو التكلفة
        function calculateProfit() {
            const price = parseFloat($('#price').val()) || 0;
            const cost = parseFloat($('#cost').val()) || 0;

            const profit = price - cost;
            let profitPercentage = 0;

            if (cost > 0) {
                profitPercentage = (profit / cost) * 100;
            } else if (price > 0) {
                profitPercentage = 100;
            }

            $('#profit-amount').text(profit.toFixed(2));
            $('#profit-percentage').text(profitPercentage.toFixed(2) + '%');

            // تغيير لون الربح حسب القيمة
            if (profit > 0) {
                $('#profit-amount').removeClass('text-danger').addClass('text-success');
                $('#profit-percentage').removeClass('text-danger').addClass('text-success');
            } else if (profit < 0) {
                $('#profit-amount').removeClass('text-success').addClass('text-danger');
                $('#profit-percentage').removeClass('text-success').addClass('text-danger');
            } else {
                $('#profit-amount').removeClass('text-success text-danger');
                $('#profit-percentage').removeClass('text-success text-danger');
            }
        }

        // حساب الربح في البداية
        calculateProfit();

        // حساب الربح عند تغيير السعر أو التكلفة
        $('#price, #cost').on('input', calculateProfit);

        // معالجة تقديم النموذج
        $('#add-product-form').on('submit', function(e) {
            e.preventDefault();

            // الحصول على البيانات من النموذج
            const formData = {
                name: $('#name').val(),
                description: $('#description').val(),
                price: $('#price').val(),
                cost: $('#cost').val(),
                category_id: $('#category_id').val(),
                is_for_sale: $('#is_for_sale').val(),
                min_quantity: $('#min_quantity').val(),
                is_active: $('#is_active').val(),
                initial_quantity: $('#initial_quantity').val()
            };

            // إضافة برانش آي دي إذا كان موجود
            if ($('#branch_id').length > 0) {
                formData.branch_id = $('#branch_id').val();
            } else if ($('input[name="branch_id"]').length > 0) {
                formData.branch_id = $('input[name="branch_id"]').val();
            }

            // إرسال البيانات باستخدام AJAX
            $.ajax({
                url: '<?php echo API_URL; ?>products.php?action=add',
                type: 'POST',
                data: formData,
                dataType: 'json',
                beforeSend: function() {
                    // إظهار مؤشر التحميل
                    showLoading();
                },
                success: function(response) {
                    hideLoading();

                    if (response.success) {
                        // عرض رسالة النجاح
                        showToast('نجاح', response.message, 'success');

                        // إعادة توجيه المستخدم إلى صفحة المنتجات بعد ثانيتين
                        setTimeout(function() {
                            window.location.href = '<?php echo BASE_URL; ?>pages/products/index.php';
                        }, 2000);
                    } else {
                        // عرض رسالة الخطأ
                        showToast('خطأ', response.message, 'error');
                    }
                },
                error: function(xhr, status, error) {
                    hideLoading();

                    let errorMessage = 'حدث خطأ أثناء إضافة المنتج';
                    try {
                        const response = JSON.parse(xhr.responseText);
                        if (response.message) {
                            errorMessage = response.message;
                        }
                    } catch (e) {
                        console.error(e);
                    }

                    showToast('خطأ', errorMessage, 'error');
                }
            });
        });

        // إظهار مؤشر التحميل
        function showLoading() {
            // يمكن إضافة كود لعرض مؤشر التحميل هنا
            $('button[type="submit"]')
                .prop('disabled', true)
                .html('<span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span> جاري الحفظ...');
        }

        // إخفاء مؤشر التحميل
        function hideLoading() {
            $('button[type="submit"]')
                .prop('disabled', false)
                .html('<i class="fas fa-save me-1"></i> حفظ المنتج');
        }

        // عرض رسالة تنبيه
        function showToast(title, message, type) {
            // تحويل نوع التنبيه إلى صنف Bootstrap
            let alertClass = 'alert-info';
            if (type === 'success') alertClass = 'alert-success';
            if (type === 'error') alertClass = 'alert-danger';
            if (type === 'warning') alertClass = 'alert-warning';

            // إنشاء حاوية للتنبيهات إذا لم تكن موجودة
            if (!$('#alertContainer').length) {
                $('<div id="alertContainer" class="alert-container position-fixed top-0 end-0 p-3" style="z-index: 1050;"></div>').appendTo('body');
            }

            const alertHtml = `
                <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
                    <strong>${title}</strong> ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            `;

            $('#alertContainer').html(alertHtml);

            // إخفاء التنبيه تلقائيًا بعد 5 ثوانٍ
            setTimeout(function() {
                $('#alertContainer .alert').alert('close');
            }, 5000);
        }
    };
</script>

<?php
// استدعاء قالب الذيل
include_once '../../includes/templates/footer.php';
?>
