ذ<?php
/**
 * نقطة النهاية الخاصة بخدمة WhatsApp
 * تتعامل مع العمليات المتعلقة بإرسال رسائل WhatsApp
 */

// تعريف ثابت لمنع الوصول المباشر للملف
define('BASEPATH', true);

// إعداد رؤوس CORS للسماح بالوصول من أي مصدر
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, X-Ignore-SSL');
header('Content-Type: application/json; charset=UTF-8');

// التعامل مع طلبات OPTIONS للتعامل مع CORS
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// التأكد من ضبط ترميز UTF-8 للمخرجات
mb_internal_encoding('UTF-8');

// تسجيل معالج الأخطاء القاتلة
register_shutdown_function(function() {
    $error = error_get_last();
    if ($error !== null && in_array($error['type'], [E_ERROR, E_PARSE, E_CORE_ERROR, E_COMPILE_ERROR])) {
        // إنهاء المخزن المؤقت والتخلص من أي مخرجات غير مرغوبة
        if (ob_get_length()) ob_end_clean();

        // تسجيل الخطأ في ملف السجل
        error_log("WhatsApp API Fatal Error: " . $error['message'] . " in " . $error['file'] . " on line " . $error['line']);

        // إرسال رسالة خطأ JSON
        header('Content-Type: application/json; charset=UTF-8');
        echo json_encode([
            'status' => 'error',
            'message' => 'حدث خطأ غير متوقع في النظام. يرجى الاتصال بمسؤول النظام.',
            'error_details' => $error['message']
        ]);
    }
});

// استدعاء ملفات التهيئة
require_once __DIR__ . '/../config/init.php';

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    echo json_encode([
        'status' => 'error',
        'message' => 'يجب تسجيل الدخول أولاً'
    ]);
    exit;
}

// الحصول على الإجراء المطلوب
$action = isset($_POST['action']) ? $_POST['action'] : (isset($_GET['action']) ? $_GET['action'] : '');

// التحقق من وجود إجراء
if (empty($action)) {
    echo json_encode([
        'status' => 'error',
        'message' => 'لم يتم تحديد الإجراء المطلوب'
    ]);
    exit;
}

// معالجة الطلب بناءً على الإجراء
try {
    // التأكد من أن المخرجات لم يتم إرسالها بعد
    ob_start();
    switch ($action) {
        // تسجيل الدخول إلى WhatsApp Web
        case 'login':
            // التحقق من صلاحية إدارة الإعدادات
            if (!hasPermission('settings_manage')) {
                throw new Exception('ليس لديك صلاحية لإدارة إعدادات WhatsApp');
            }

            // تم تحديث النظام لاستخدام جانب العميل فقط
            // يجب فتح الخادم المحلي مباشرة من صفحة الإعدادات
            echo json_encode([
                'status' => 'success',
                'message' => 'يرجى استخدام زر تسجيل الدخول في صفحة الإعدادات لفتح الخادم المحلي'
            ]);
            break;

        // إرسال رسالة اختبار
        case 'send_test':
            // التحقق من صلاحية إدارة الإعدادات
            if (!hasPermission('settings_manage')) {
                throw new Exception('ليس لديك صلاحية لإدارة إعدادات WhatsApp');
            }

            // التحقق من وجود البيانات المطلوبة
            if (!isset($_POST['phone']) || empty($_POST['phone'])) {
                throw new Exception('رقم الهاتف مطلوب');
            }

            if (!isset($_POST['message']) || empty($_POST['message'])) {
                throw new Exception('نص الرسالة مطلوب');
            }

            // تم تحديث النظام لاستخدام جانب العميل فقط
            // يجب استخدام زر الاختبار في صفحة الإعدادات
            echo json_encode([
                'status' => 'success',
                'message' => 'يرجى استخدام زر الاختبار في صفحة الإعدادات لإرسال رسالة اختبار من خلال الخادم المحلي'
            ]);
            break;

        // إرسال تذكير بموعد
        case 'send_appointment_reminder':
            // التحقق من صلاحية إدارة المواعيد
            if (!hasPermission('appointments_edit')) {
                throw new Exception('ليس لديك صلاحية لإرسال تذكيرات المواعيد');
            }

            // التحقق من وجود معرف الموعد
            if (!isset($_POST['appointment_id']) || empty($_POST['appointment_id'])) {
                throw new Exception('معرف الموعد مطلوب');
            }

            $appointmentId = intval($_POST['appointment_id']);

            // إنشاء كائن المواعيد
            $appointmentModel = new Appointment($db);

            // الحصول على بيانات الموعد
            $appointment = $appointmentModel->getAppointmentById($appointmentId);

            if (!$appointment) {
                throw new Exception('الموعد غير موجود');
            }

            // التحقق من وجود رقم هاتف للعميل
            if (empty($appointment['customer_phone'])) {
                throw new Exception('لا يوجد رقم هاتف للعميل');
            }

            // التحقق من وجود اسم العميل
            if (empty($appointment['customer_name'])) {
                $appointment['customer_name'] = 'العميل'; // استخدام اسم افتراضي إذا لم يكن موجودًا
            }

            // معرف العميل ليس مطلوبًا للإرسال
            // نقوم بتسجيل ملاحظة فقط للتصحيح
            if (empty($appointment['customer_id'])) {
                error_log("تحذير: إرسال تذكير لموعد بدون معرف عميل. معرف الموعد: {$appointmentId}");
                // لا نقوم برمي استثناء هنا
            }

            // إنشاء كائن الفرع
            $branchModel = new Branch($db);

            // الحصول على بيانات الفرع
            $branch = $branchModel->getBranchById($appointment['branch_id']);

            if (!$branch) {
                throw new Exception('الفرع غير موجود');
            }

            // تنسيق التاريخ والوقت
            $appointmentDate = date('d/m/Y', strtotime($appointment['date']));
            $appointmentTime = date('h:i A', strtotime($appointment['start_time']));

            // إنشاء نص الرسالة
            $message = "مرحبا {$appointment['customer_name']}\n";
            $message .= "نود تذكيركم بموعدكم القادم في {$branch['name']}\n";
            $message .= "التاريخ: {$appointmentDate}\n";
            $message .= "الوقت: {$appointmentTime}\n";
            $message .= "نتطلع لرؤيتكم!";

            // إنشاء كائن خدمة WhatsApp
            $whatsappService = new WhatsAppAutomation($db);

            // إرسال الرسالة
            // التحقق من إعدادات استخدام العميل
            $settingsModel = new Settings($db);
            $settings = $settingsModel->getAllSettings();
            $useClientSide = isset($settings['whatsapp_use_client_side']) && $settings['whatsapp_use_client_side'] == '1';

            if ($useClientSide) {
                // إذا كان الإعداد مفعلاً، نقوم بفتح نافذة WhatsApp مباشرة
                // تنسيق رقم الهاتف
                $formattedPhone = $appointment['customer_phone'];
                if (strpos($formattedPhone, '0') === 0) {
                    $formattedPhone = substr($formattedPhone, 1);
                }

                // إزالة علامة + من رمز البلد
                $countryCode = $branch['country_code'] ?? '+20';
                $cleanCountryCode = str_replace('+', '', $countryCode);

                // دمج رمز البلد مع رقم الهاتف
                $formattedPhone = "{$cleanCountryCode}{$formattedPhone}";

                // إرجاع البيانات للعميل لفتح نافذة WhatsApp
                echo json_encode([
                    'status' => 'success',
                    'message' => 'تم فتح نافذة WhatsApp',
                    'data' => [
                        'phone' => $formattedPhone,
                        'message' => $message,
                        'url' => "https://web.whatsapp.com/send?phone={$formattedPhone}&text=" . urlencode($message)
                    ]
                ]);
                exit;
            } else {
                // تم تحديث النظام لاستخدام جانب العميل فقط
                // تنسيق رقم الهاتف
                $formattedPhone = $appointment['customer_phone'];
                if (strpos($formattedPhone, '0') === 0) {
                    $formattedPhone = substr($formattedPhone, 1);
                }

                // إزالة علامة + من رمز البلد
                $countryCode = $branch['country_code'] ?? '+20';
                $cleanCountryCode = str_replace('+', '', $countryCode);

                // دمج رمز البلد مع رقم الهاتف
                $formattedPhone = "{$cleanCountryCode}{$formattedPhone}";

                // إرجاع البيانات للعميل لفتح نافذة WhatsApp
                echo json_encode([
                    'status' => 'success',
                    'message' => 'تم فتح نافذة WhatsApp',
                    'data' => [
                        'phone' => $formattedPhone,
                        'message' => $message,
                        'url' => "https://web.whatsapp.com/send?phone={$formattedPhone}&text=" . urlencode($message)
                    ]
                ]);
            }
            break;

        // إرسال تذكيرات المواعيد المجدولة
        case 'send_scheduled_reminders':
            try {
                // التحقق من صلاحية إدارة المواعيد
                if (!hasPermission('appointments_edit') && !isset($_SERVER['HTTP_X_CRON_KEY'])) {
                    throw new Exception('ليس لديك صلاحية لإرسال تذكيرات المواعيد');
                }

                // الحصول على إعدادات التذكيرات
                $settingsModel = new Settings($db);
                $settings = $settingsModel->getAllSettings();

                // التحقق من تفعيل تذكيرات WhatsApp
                if (!isset($settings['notification_enable_whatsapp']) || $settings['notification_enable_whatsapp'] != '1') {
                    throw new Exception('تذكيرات WhatsApp غير مفعلة في الإعدادات');
                }

                // الحصول على عدد ساعات التذكير قبل الموعد
                $reminderHours = isset($settings['notification_reminder_hours']) ? intval($settings['notification_reminder_hours']) : 24;

                // إنشاء كائن المواعيد
                $appointmentModel = new Appointment($db);

                // إنشاء كائن الفرع
                $branchModel = new Branch($db);

                // الحصول على المواعيد القادمة التي تحتاج إلى تذكير
                $startTime = date('Y-m-d H:i:s');
                $endTime = date('Y-m-d H:i:s', strtotime("+{$reminderHours} hours"));

                $upcomingAppointments = $appointmentModel->getUpcomingAppointmentsForReminder($startTime, $endTime);

                // عدد التذكيرات التي تم إرسالها بنجاح
                $successCount = 0;
                $failedCount = 0;

                // إنشاء كائن خدمة WhatsApp
                $whatsappService = new WhatsAppAutomation($db);

                // معالجة كل موعد وإرسال تذكير
                foreach ($upcomingAppointments as $appointment) {
                    try {
                        // التحقق من وجود رقم هاتف للعميل
                        if (empty($appointment['customer_phone'])) {
                            continue;
                        }

                        // الحصول على معلومات الفرع
                        $branch = $branchModel->getBranchById($appointment['branch_id']);
                        if (!$branch) {
                            continue;
                        }

                        // تنسيق التاريخ والوقت
                        $appointmentDate = date('d/m/Y', strtotime($appointment['date']));
                        $appointmentTime = date('h:i A', strtotime($appointment['start_time']));

                        // إنشاء نص الرسالة
                        $message = "مرحبا {$appointment['customer_name']}\n";
                        $message .= "نود تذكيركم بموعدكم القادم في {$branch['name']}\n";
                        $message .= "التاريخ: {$appointmentDate}\n";
                        $message .= "الوقت: {$appointmentTime}\n";
                        $message .= "نتطلع لرؤيتكم!";

                        // في حالة التذكيرات المجدولة، نستخدم الخادم المحلي لإرسال الرسائل
                        // لا يمكن استخدام نافذة المتصفح مباشرة لأن هذه العملية تتم بشكل تلقائي
                        $result = $whatsappService->sendMessage(
                            $appointment['customer_phone'],
                            $message,
                            ['country_code' => $branch['country_code'] ?? '+20']
                        );

                        if ($result) {
                            // تحديث حالة التذكير في قاعدة البيانات
                            $appointmentModel->updateAppointment($appointment['id'], [
                                'reminder_sent' => 1,
                                'reminder_sent_at' => date('Y-m-d H:i:s')
                            ]);

                            $successCount++;
                        } else {
                            $failedCount++;
                        }
                    } catch (Exception $e) {
                        $failedCount++;
                        error_log("خطأ في إرسال تذكير للموعد #{$appointment['id']}: " . $e->getMessage());
                    }
                }

                echo json_encode([
                    'status' => 'success',
                    'message' => "تم إرسال {$successCount} تذكير بنجاح، وفشل إرسال {$failedCount} تذكير",
                    'data' => [
                        'total' => count($upcomingAppointments),
                        'success' => $successCount,
                        'failed' => $failedCount
                    ]
                ]);
            } catch (Exception $e) {
                // تسجيل الخطأ في ملف السجل
                error_log("WhatsApp API Error in send_scheduled_reminders: " . $e->getMessage());

                // إرسال رسالة خطأ JSON
                echo json_encode([
                    'status' => 'error',
                    'message' => $e->getMessage(),
                    'action' => 'send_scheduled_reminders'
                ]);
            }
            break;

        // إعادة تعيين جلسة WhatsApp
        case 'reset_session':
            // التحقق من صلاحية إدارة الإعدادات
            if (!hasPermission('settings_manage')) {
                throw new Exception('ليس لديك صلاحية لإدارة إعدادات WhatsApp');
            }

            // تم تحديث النظام لاستخدام جانب العميل فقط
            // يجب إعادة تعيين الجلسة من خلال الخادم المحلي
            echo json_encode([
                'status' => 'success',
                'message' => 'يرجى استخدام زر إعادة التعيين في صفحة الإعدادات لإرسال طلب إعادة التعيين إلى الخادم المحلي'
            ]);
            break;

        default:
            throw new Exception('الإجراء غير معروف');
    }
    // إنهاء المخزن المؤقت والتخلص من أي مخرجات غير مرغوبة
    ob_end_clean();

} catch (Exception $e) {
    // إنهاء المخزن المؤقت والتخلص من أي مخرجات غير مرغوبة
    if (ob_get_length()) ob_end_clean();

    // تسجيل الخطأ في ملف السجل
    error_log("WhatsApp API Error: " . $e->getMessage() . " in " . $e->getFile() . " on line " . $e->getLine());

    // إرسال رسالة خطأ JSON
    echo json_encode([
        'status' => 'error',
        'message' => $e->getMessage(),
        'action' => $action
    ]);
} catch (Error $e) {
    // إنهاء المخزن المؤقت والتخلص من أي مخرجات غير مرغوبة
    if (ob_get_length()) ob_end_clean();

    // تسجيل الخطأ في ملف السجل
    error_log("WhatsApp API Fatal Error: " . $e->getMessage() . " in " . $e->getFile() . " on line " . $e->getLine());

    // إرسال رسالة خطأ JSON
    echo json_encode([
        'status' => 'error',
        'message' => 'حدث خطأ غير متوقع في النظام. يرجى الاتصال بمسؤول النظام.',
        'action' => $action
    ]);
}
