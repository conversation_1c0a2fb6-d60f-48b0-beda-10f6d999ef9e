<?php
/**
 * سكريبت تهيئة النسخة التجريبية
 * يقوم بتحويل النظام إلى نسخة تجريبية مع قاعدة بيانات تجريبية
 */

// منع الوصول المباشر إلا من خلال سطر الأوامر أو المدير
if (!defined('DEMO_SETUP_ALLOWED')) {
    if (php_sapi_name() !== 'cli' && (!isset($_GET['admin_key']) || $_GET['admin_key'] !== 'demo_setup_2025')) {
        die('غير مسموح بالوصول المباشر لهذا الملف');
    }
}

define('BASEPATH', __DIR__);
require_once 'config/config.php';

class DemoSetup {
    private $pdo;
    private $logFile;
    
    public function __construct() {
        $this->logFile = __DIR__ . '/logs/demo_setup_' . date('Y-m-d_H-i-s') . '.log';
        $this->log("بدء تهيئة النسخة التجريبية");
    }
    
    /**
     * تشغيل عملية التهيئة الكاملة
     */
    public function run() {
        try {
            $this->log("=== بدء عملية تهيئة النسخة التجريبية ===");
            
            // 1. إنشاء نسخة احتياطية من الإعدادات الحالية
            $this->backupCurrentConfig();
            
            // 2. إنشاء قاعدة البيانات التجريبية
            $this->createDemoDatabase();
            
            // 3. استيراد البيانات التجريبية
            $this->importDemoData();
            
            // 4. تحديث ملفات الإعدادات
            $this->updateConfigFiles();
            
            // 5. إنشاء ملفات النسخة التجريبية
            $this->createDemoFiles();
            
            // 6. تحديث الصفحة الرئيسية
            $this->updateIndexPage();
            
            // 7. إعداد مهام الصيانة التلقائية
            $this->setupMaintenanceTasks();
            
            $this->log("=== تمت تهيئة النسخة التجريبية بنجاح ===");
            
            return [
                'success' => true,
                'message' => 'تم تحويل النظام إلى نسخة تجريبية بنجاح',
                'demo_url' => $this->getDemoUrl(),
                'admin_credentials' => [
                    'username' => 'admin',
                    'password' => 'password'
                ]
            ];
            
        } catch (Exception $e) {
            $this->log("خطأ: " . $e->getMessage());
            return [
                'success' => false,
                'message' => 'فشل في تهيئة النسخة التجريبية: ' . $e->getMessage()
            ];
        }
    }
    
    /**
     * إنشاء نسخة احتياطية من الإعدادات الحالية
     */
    private function backupCurrentConfig() {
        $this->log("إنشاء نسخة احتياطية من الإعدادات الحالية...");
        
        $backupDir = __DIR__ . '/backups/config_backup_' . date('Y-m-d_H-i-s');
        if (!is_dir($backupDir)) {
            mkdir($backupDir, 0755, true);
        }
        
        // نسخ ملفات الإعدادات
        $configFiles = [
            'config/config.php',
            'index.php'
        ];
        
        foreach ($configFiles as $file) {
            if (file_exists($file)) {
                copy($file, $backupDir . '/' . basename($file) . '.backup');
                $this->log("تم نسخ الملف: $file");
            }
        }
    }
    
    /**
     * إنشاء قاعدة البيانات التجريبية
     */
    private function createDemoDatabase() {
        $this->log("إنشاء قاعدة البيانات التجريبية...");
        
        try {
            // الاتصال بـ MySQL بدون تحديد قاعدة بيانات
            $pdo = new PDO(
                "mysql:host=localhost;charset=utf8mb4",
                'root',
                '',
                [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]
            );
            
            // إنشاء قاعدة البيانات التجريبية
            $pdo->exec("CREATE DATABASE IF NOT EXISTS salon_demo CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
            $this->log("تم إنشاء قاعدة البيانات: salon_demo");
            
            // الاتصال بقاعدة البيانات التجريبية
            $this->pdo = new PDO(
                "mysql:host=localhost;dbname=salon_demo;charset=utf8mb4",
                'root',
                '',
                [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]
            );
            
        } catch (PDOException $e) {
            throw new Exception("فشل في إنشاء قاعدة البيانات: " . $e->getMessage());
        }
    }
    
    /**
     * استيراد البيانات التجريبية
     */
    private function importDemoData() {
        $this->log("استيراد البيانات التجريبية...");
        
        $sqlFile = __DIR__ . '/demo_database.sql';
        if (!file_exists($sqlFile)) {
            throw new Exception("ملف البيانات التجريبية غير موجود: $sqlFile");
        }
        
        $sql = file_get_contents($sqlFile);
        
        // تقسيم الاستعلامات
        $queries = array_filter(array_map('trim', explode(';', $sql)));
        
        foreach ($queries as $query) {
            if (!empty($query) && !preg_match('/^(--|\/\*|\*)/', $query)) {
                try {
                    $this->pdo->exec($query);
                } catch (PDOException $e) {
                    $this->log("تحذير: فشل في تنفيذ الاستعلام: " . substr($query, 0, 100) . "...");
                }
            }
        }
        
        $this->log("تم استيراد البيانات التجريبية بنجاح");
    }
    
    /**
     * تحديث ملفات الإعدادات
     */
    private function updateConfigFiles() {
        $this->log("تحديث ملفات الإعدادات...");
        
        // إنشاء ملف إعدادات جديد للنسخة التجريبية
        $configContent = file_get_contents(__DIR__ . '/config/config_demo.php');
        
        // تحديث ملف الإعدادات الرئيسي
        $newConfigFile = __DIR__ . '/config/config_demo_active.php';
        file_put_contents($newConfigFile, $configContent);
        
        $this->log("تم إنشاء ملف الإعدادات التجريبية: config_demo_active.php");
    }
    
    /**
     * إنشاء ملفات النسخة التجريبية
     */
    private function createDemoFiles() {
        $this->log("إنشاء ملفات النسخة التجريبية...");
        
        // إنشاء ملف التحقق من النسخة التجريبية
        $demoCheckFile = __DIR__ . '/includes/demo_check.php';
        $demoCheckContent = '<?php
/**
 * ملف التحقق من النسخة التجريبية
 */

define("DEMO_MODE", true);
define("DEMO_SETUP_DATE", "' . date('Y-m-d H:i:s') . '");

function isDemoMode() {
    return defined("DEMO_MODE") && DEMO_MODE === true;
}

function getDemoWarning() {
    if (isDemoMode()) {
        return \'<div class="alert alert-warning demo-warning">
                    <i class="fas fa-exclamation-triangle"></i> 
                    تحذير: هذه نسخة تجريبية - جميع البيانات وهمية لأغراض العرض فقط
                </div>\';
    }
    return "";
}

// تضمين ملفات CSS و JS الخاصة بالنسخة التجريبية
function includeDemoAssets() {
    if (isDemoMode()) {
        echo \'<link rel="stylesheet" href="assets/css/demo-mode.css">\';
        echo \'<script src="assets/js/demo-mode.js"></script>\';
    }
}
?>';
        
        file_put_contents($demoCheckFile, $demoCheckContent);
        $this->log("تم إنشاء ملف التحقق من النسخة التجريبية");
        
        // إنشاء ملف معلومات النسخة التجريبية
        $demoInfoFile = __DIR__ . '/demo_info.php';
        $demoInfoContent = '<?php
/**
 * صفحة معلومات النسخة التجريبية
 */

require_once "includes/demo_check.php";

if (!isDemoMode()) {
    header("Location: index.php");
    exit;
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>معلومات النسخة التجريبية</title>
    <link href="assets/css/bootstrap.min.css" rel="stylesheet">
    <link href="assets/css/demo-mode.css" rel="stylesheet">
</head>
<body class="demo-mode">
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header bg-warning text-dark">
                        <h3><i class="fas fa-flask"></i> معلومات النسخة التجريبية</h3>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info">
                            <h5>مرحباً بك في النسخة التجريبية لنظام إدارة الصالونات</h5>
                            <p>تم إعداد هذه النسخة في: ' . date('Y-m-d H:i:s') . '</p>
                        </div>
                        
                        <h6>بيانات الدخول التجريبية:</h6>
                        <ul>
                            <li><strong>المدير:</strong> admin / password</li>
                            <li><strong>مدير الفرع:</strong> manager / password</li>
                            <li><strong>أمين الصندوق:</strong> cashier / password</li>
                            <li><strong>مستخدم تجريبي:</strong> demo / password</li>
                        </ul>
                        
                        <div class="mt-4">
                            <a href="index.php" class="btn btn-primary">دخول النظام</a>
                            <a href="mailto:<EMAIL>" class="btn btn-outline-info">طلب النسخة الكاملة</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>';
        
        file_put_contents($demoInfoFile, $demoInfoContent);
        $this->log("تم إنشاء صفحة معلومات النسخة التجريبية");
    }
    
    /**
     * تحديث الصفحة الرئيسية
     */
    private function updateIndexPage() {
        $this->log("تحديث الصفحة الرئيسية...");
        
        $indexFile = __DIR__ . '/index.php';
        if (file_exists($indexFile)) {
            $content = file_get_contents($indexFile);
            
            // إضافة تضمين ملف التحقق من النسخة التجريبية
            if (strpos($content, 'demo_check.php') === false) {
                $demoInclude = "<?php require_once 'includes/demo_check.php'; ?>\n";
                $content = $demoInclude . $content;
            }
            
            // إضافة تضمين ملفات CSS و JS
            if (strpos($content, 'includeDemoAssets()') === false) {
                $content = str_replace('</head>', "<?php includeDemoAssets(); ?>\n</head>", $content);
            }
            
            // إضافة تحذير النسخة التجريبية
            if (strpos($content, 'getDemoWarning()') === false) {
                $content = str_replace('<body', "<?php echo getDemoWarning(); ?>\n<body", $content);
            }
            
            file_put_contents($indexFile, $content);
            $this->log("تم تحديث الصفحة الرئيسية");
        }
    }
    
    /**
     * إعداد مهام الصيانة التلقائية
     */
    private function setupMaintenanceTasks() {
        $this->log("إعداد مهام الصيانة التلقائية...");

        // إنشاء مجلد السكريبتات
        if (!is_dir(__DIR__ . '/scripts')) {
            mkdir(__DIR__ . '/scripts', 0755, true);
        }

        // إنشاء ملف تسجيل آخر إعادة تعيين
        file_put_contents(__DIR__ . '/logs/last_demo_reset.txt', time());

        $this->log("تم إعداد مهام الصيانة التلقائية");
    }
    
    /**
     * الحصول على رابط النسخة التجريبية
     */
    private function getDemoUrl() {
        $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
        $host = $_SERVER['HTTP_HOST'] ?? 'localhost';
        $path = dirname($_SERVER['REQUEST_URI'] ?? '/demo');
        
        return $protocol . '://' . $host . $path . '/demo_info.php';
    }
    
    /**
     * تسجيل الأحداث
     */
    private function log($message) {
        $timestamp = date('Y-m-d H:i:s');
        $logMessage = "[$timestamp] $message\n";
        
        // إنشاء مجلد السجلات إذا لم يكن موجوداً
        $logDir = dirname($this->logFile);
        if (!is_dir($logDir)) {
            mkdir($logDir, 0755, true);
        }
        
        file_put_contents($this->logFile, $logMessage, FILE_APPEND | LOCK_EX);
        
        // طباعة الرسالة إذا كان التشغيل من سطر الأوامر
        if (php_sapi_name() === 'cli') {
            echo $logMessage;
        }
    }
}

// تشغيل السكريبت
if (php_sapi_name() === 'cli' || (isset($_GET['admin_key']) && $_GET['admin_key'] === 'demo_setup_2025')) {
    $setup = new DemoSetup();
    $result = $setup->run();
    
    if (php_sapi_name() === 'cli') {
        echo json_encode($result, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n";
    } else {
        header('Content-Type: application/json; charset=utf-8');
        echo json_encode($result, JSON_UNESCAPED_UNICODE);
    }
}
?>
