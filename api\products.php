<?php
/**
 * API Products
 * نقطة الوصول للتعامل مع المنتجات في النظام
 */

// تعريف ثابت لمنع الوصول المباشر للملفات المرتبطة
define('BASEPATH', true);

// استدعاء ملفات التهيئة
require_once '../config/init.php';

// التحقق من تسجيل الدخول
requireLogin();

// تحديد العملية المطلوبة
$action = $_GET['action'] ?? 'list';

// الاستجابة الافتراضية
$response = [
    'success' => false,
    'message' => '',
    'data' => null
];

// معالجة الطلب حسب نوع العملية
try {
    switch ($action) {
        case 'list':
            // التحقق من الصلاحية
            if (!hasPermission('products_view')) {
                throw new Exception('ليس لديك صلاحية لعرض المنتجات');
            }
            
            // استرجاع قائمة المنتجات
            $response = getProductsList();
            break;
            
        case 'get':
            // التحقق من الصلاحية
            if (!hasPermission('products_view')) {
                throw new Exception('ليس لديك صلاحية لعرض المنتجات');
            }
            
            // استرجاع بيانات منتج
            $response = getProductDetails();
            break;
            
        case 'add':
            // التحقق من الصلاحية
            if (!hasPermission('products_add')) {
                throw new Exception('ليس لديك صلاحية لإضافة منتجات');
            }
            
            // التحقق من نوع الطلب
            if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
                throw new Exception('طريقة الطلب غير صحيحة');
            }
            
            // إضافة منتج جديد
            $response = addNewProduct();
            break;
            
        case 'update':
            // التحقق من الصلاحية
            if (!hasPermission('products_edit')) {
                throw new Exception('ليس لديك صلاحية لتعديل المنتجات');
            }
            
            // التحقق من نوع الطلب
            if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
                throw new Exception('طريقة الطلب غير صحيحة');
            }
            
            // تحديث بيانات منتج
            $response = updateProduct();
            break;
            
        case 'delete':
            // التحقق من الصلاحية
            if (!hasPermission('products_delete')) {
                throw new Exception('ليس لديك صلاحية لحذف المنتجات');
            }
            
            // حذف منتج
            $response = deleteProduct();
            break;
            
        case 'categories':
            // التحقق من الصلاحية
            if (!hasPermission('products_view')) {
                throw new Exception('ليس لديك صلاحية لعرض فئات المنتجات');
            }
            
            // استرجاع فئات المنتجات
            $response = getProductCategories();
            break;
            
        case 'add_category':
            // التحقق من الصلاحية
            if (!hasPermission('products_add')) {
                throw new Exception('ليس لديك صلاحية لإضافة فئات المنتجات');
            }
            
            // التحقق من نوع الطلب
            if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
                throw new Exception('طريقة الطلب غير صحيحة');
            }
            
            // إضافة فئة منتجات جديدة
            $response = addProductCategory();
            break;
            
        case 'update_category':
            // التحقق من الصلاحية
            if (!hasPermission('products_edit')) {
                throw new Exception('ليس لديك صلاحية لتعديل فئات المنتجات');
            }
            
            // التحقق من نوع الطلب
            if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
                throw new Exception('طريقة الطلب غير صحيحة');
            }
            
            // تحديث فئة منتجات
            $response = updateProductCategory();
            break;
            
        case 'delete_category':
            // التحقق من الصلاحية
            if (!hasPermission('products_delete')) {
                throw new Exception('ليس لديك صلاحية لحذف فئات المنتجات');
            }
            
            // حذف فئة منتجات
            $response = deleteProductCategory();
            break;
            
        case 'report':
            // التحقق من الصلاحية
            if (!hasPermission('products_view')) {
                throw new Exception('ليس لديك صلاحية لعرض تقارير المنتجات');
            }
            
            // استرجاع تقرير المنتجات
            $response = getProductReport();
            break;
            
        default:
            $response['message'] = 'العملية غير معروفة';
            break;
    }
} catch (Exception $e) {
    $response['message'] = $e->getMessage();
}

// إرجاع النتيجة بتنسيق JSON
header('Content-Type: application/json; charset=UTF-8');
echo json_encode($response, JSON_UNESCAPED_UNICODE);
exit;

/**
 * استرجاع قائمة المنتجات
 * @return array استجابة API
 */
function getProductsList($filters = []) {
    global $db;
    
    try {
        // إنشاء نموذج المنتجات
        $productModel = new Product($db);
        
        // جلب فلاتر البحث
        $filters = [];
        
        if (!empty($_GET['search'])) {
            $filters['search'] = sanitizeInput($_GET['search']);
        }
        
        if (!empty($_GET['category_id'])) {
            $filters['category_id'] = (int)$_GET['category_id'];
        }
        
        if (!empty($_GET['branch_id'])) {
            $filters['branch_id'] = (int)$_GET['branch_id'];
        } elseif (!isAdmin() && !empty($_SESSION['user_branch_id'])) {
            // استخدام فرع المستخدم الحالي إذا لم يتم تحديد فرع وكان المستخدم ليس مديرًا
            $filters['branch_id'] = $_SESSION['user_branch_id'];
        }
        
        if (isset($_GET['is_for_sale']) && $_GET['is_for_sale'] !== '') {
            $filters['is_for_sale'] = (int)$_GET['is_for_sale'];
        }
        
        if (isset($_GET['is_active']) && $_GET['is_active'] !== '') {
            $filters['is_active'] = (int)$_GET['is_active'];
        }
        
        // إعدادات التصفح
        $page = isset($_GET['page']) ? max(1, (int)$_GET['page']) : 1;
        $limit = isset($_GET['limit']) ? max(1, (int)$_GET['limit']) : 20;
        $offset = ($page - 1) * $limit;
        
        $filters['limit'] = $limit;
        $filters['offset'] = $offset;
        
        // الحصول على المنتجات والعدد الإجمالي
        $products = $productModel->getProducts($filters);
        $totalProducts = $productModel->getProductsCount($filters);
        
        // تعبئة حركات المخزون
        if (!empty($products) && isset($filters['branch_id'])) {
            $inventoryModel = new Inventory($db);
            foreach ($products as &$product) {
                // إضافة معلومات المخزون الحالي
                $product['stock'] = $inventoryModel->getProductStock($product['id'], $filters['branch_id']);
                
                // إضافة آخر 5 حركات مخزون للمنتج
                $product['inventory_transactions'] = $inventoryModel->getProductTransactions(
                    $product['id'], 
                    $filters['branch_id'], 
                    5 // عدد الحركات المطلوبة
                );
                
                // إضافة إجمالي الحركات الواردة والصادرة
                $product['inventory_summary'] = $inventoryModel->getProductTransactionSummary(
                    $product['id'], 
                    $filters['branch_id']
                );
            }
        }
        
        // حساب عدد الصفحات
        $totalPages = ceil($totalProducts / $limit);
        
        return [
            'success' => true,
            'message' => 'تم جلب المنتجات بنجاح',
            'data' => [
                'products' => $products,
                'pagination' => [
                    'total' => $totalProducts,
                    'count' => count($products),
                    'per_page' => $limit,
                    'current_page' => $page,
                    'total_pages' => $totalPages
                ]
            ]
        ];
    } catch (Exception $e) {
        error_log('خطأ في استرجاع قائمة المنتجات: ' . $e->getMessage());
        throw new Exception('حدث خطأ أثناء استرجاع المنتجات');
    }
}

/**
 * استرجاع بيانات منتج
 * @return array استجابة API
 */
function getProductDetails() {
    global $db;
    
    try {
        // التحقق من وجود معرف المنتج
        if (empty($_GET['id'])) {
            throw new Exception('معرف المنتج مطلوب');
        }
        
        $productId = (int)$_GET['id'];
        
        // إنشاء نموذج المنتجات
        $productModel = new Product($db);
        
        // الحصول على بيانات المنتج
        $product = $productModel->getProductById($productId);
        
        if (!$product) {
            throw new Exception('المنتج غير موجود');
        }
        
        // استرجاع بيانات المخزون
        if (!empty($product)) {
            $inventoryModel = new Inventory($db);
            $branchId = !empty($_GET['branch_id']) ? (int)$_GET['branch_id'] : null;
            
            // إذا لم يتم تحديد فرع وكان المستخدم ليس مديرًا، استخدم فرع المستخدم الحالي
            if (!$branchId && !isAdmin() && !empty($_SESSION['user_branch_id'])) {
                $branchId = $_SESSION['user_branch_id'];
            }
            
            if ($branchId) {
                $product['stock'] = $inventoryModel->getProductStock($productId, $branchId);
                
                // الحصول على حركات المخزون
                $transactions = $inventoryModel->getProductTransactions($productId, $branchId, 10);
                $product['transactions'] = $transactions;
            }
        }
        
        return [
            'success' => true,
            'message' => 'تم جلب بيانات المنتج بنجاح',
            'data' => $product
        ];
    } catch (Exception $e) {
        error_log('خطأ في استرجاع تفاصيل المنتج: ' . $e->getMessage());
        throw new Exception('حدث خطأ أثناء استرجاع بيانات المنتج');
    }
}

/**
 * إضافة منتج جديد
 * @return array استجابة API
 */
function addNewProduct() {
    global $db;
    
    try {
        // Check if there's already an active transaction
        $transactionStartedHere = false;
        if (!$db->inTransaction()) {
            $db->beginTransaction();
            $transactionStartedHere = true;
        }
        
        // استرجاع بيانات المنتج من الطلب
        $productData = [
            'name' => sanitizeInput($_POST['name'] ?? ''),
            'description' => sanitizeInput($_POST['description'] ?? ''),
            'price' => floatval($_POST['price'] ?? 0),
            'cost' => floatval($_POST['cost'] ?? 0),
            'category_id' => !empty($_POST['category_id']) ? (int)$_POST['category_id'] : null,
            'is_for_sale' => isset($_POST['is_for_sale']) ? (int)$_POST['is_for_sale'] : 1,
            'min_quantity' => isset($_POST['min_quantity']) ? (int)$_POST['min_quantity'] : 5,
            'is_active' => isset($_POST['is_active']) ? (int)$_POST['is_active'] : 1,
            'branch_id' => isset($_POST['branch_id']) ? (int)$_POST['branch_id'] : null
        ];
        
        // تحقق من الحقول المطلوبة
        if (empty($productData['name'])) {
            throw new Exception('اسم المنتج مطلوب');
        }
        
        if ($productData['price'] <= 0) {
            throw new Exception('سعر المنتج يجب أن يكون أكبر من صفر');
        }
        
        // إذا لم يتم تحديد فرع وكان المستخدم ليس مديرًا، استخدم فرع المستخدم الحالي
        if (empty($productData['branch_id']) && !isAdmin() && !empty($_SESSION['user_branch_id'])) {
            $productData['branch_id'] = $_SESSION['user_branch_id'];
        }
        
        // إنشاء نموذج المنتجات
        $productModel = new Product($db);
        
        // إضافة المنتج
        $productId = $productModel->addProduct($productData);
        
        // إضافة الكمية الأولية للمخزون (إذا تم تحديدها)
        if (isset($_POST['initial_quantity']) && $_POST['initial_quantity'] > 0) {
            $initialQuantity = (int)$_POST['initial_quantity'];
            $branchId = $productData['branch_id'] ?? $_SESSION['user_branch_id'];
            $notes = 'الكمية الأولية عند إضافة المنتج';
            
            // إضافة المخزون الأولي
            $inventoryModel = new Inventory($db);
            
            // تسجيل حركة دخول للمخزون ثم تحديث رصيد المخزون
            // نستخدم false لمنع تسجيل حركة مخزون مرة أخرى داخل setProductStock
            $inventoryModel->setProductStock($productId, $initialQuantity, $branchId, $notes, false);
            
            // إضافة حركة المخزون بشكل منفصل
            $transactionData = [
                'product_id' => $productId,
                'transaction_type' => 'in',
                'quantity' => $initialQuantity,
                'previous_quantity' => 0,
                'current_quantity' => $initialQuantity,
                'notes' => $notes,
                'user_id' => $_SESSION['user_id'] ?? null,
                'branch_id' => $branchId
            ];
            
            $inventoryModel->addInventoryTransaction($transactionData);
        }
        
        // تأكيد المعاملة فقط إذا بدأناها هنا
        if ($transactionStartedHere && $db->inTransaction()) {
            $db->commit();
        }
        
        // استرجاع بيانات المنتج المضاف
        $product = $productModel->getProductById($productId);
        
        return [
            'success' => true,
            'message' => 'تم إضافة المنتج بنجاح',
            'data' => $product
        ];
    } catch (Exception $e) {
        // التراجع عن المعاملة في حالة حدوث خطأ (فقط إذا بدأناها هنا)
        if ($transactionStartedHere && $db->inTransaction()) {
            $db->rollBack();
        }
        
        error_log('خطأ في إضافة منتج جديد: ' . $e->getMessage());
        throw new Exception($e->getMessage());
    }
}

/**
 * تحديث بيانات منتج
 * @return array استجابة API
 */
function updateProduct() {
    global $db;
    
    try {
        // بدء المعاملة إذا لم تكن هناك معاملة نشطة بالفعل
        $transactionStartedHere = false;
        if (!$db->inTransaction()) {
            $db->beginTransaction();
            $transactionStartedHere = true;
        }
        
        // التحقق من وجود معرف المنتج
        if (empty($_POST['id'])) {
            throw new Exception('معرف المنتج مطلوب');
        }
        
        $productId = (int)$_POST['id'];
        
        // استرجاع بيانات المنتج من الطلب
        $productData = [
            'name' => sanitizeInput($_POST['name'] ?? ''),
            'description' => sanitizeInput($_POST['description'] ?? ''),
            'price' => floatval($_POST['price'] ?? 0),
            'cost' => floatval($_POST['cost'] ?? 0),
            'category_id' => !empty($_POST['category_id']) ? (int)$_POST['category_id'] : null,
            'is_for_sale' => isset($_POST['is_for_sale']) ? (int)$_POST['is_for_sale'] : 1,
            'min_quantity' => isset($_POST['min_quantity']) ? (int)$_POST['min_quantity'] : 5,
            'is_active' => isset($_POST['is_active']) ? (int)$_POST['is_active'] : 1,
            'branch_id' => isset($_POST['branch_id']) ? (int)$_POST['branch_id'] : null
        ];
        
        // تحقق من الحقول المطلوبة
        if (empty($productData['name'])) {
            throw new Exception('اسم المنتج مطلوب');
        }
        
        if ($productData['price'] <= 0) {
            throw new Exception('سعر المنتج يجب أن يكون أكبر من صفر');
        }
        
        // إنشاء نموذج المنتجات
        $productModel = new Product($db);
        
        // تحديث المنتج
        $result = $productModel->updateProduct($productId, $productData);
        
        // إذا تم تحديد كمية مخزون جديدة
        if (isset($_POST['new_quantity']) && $_POST['new_quantity'] !== '') {
            $branchId = $productData['branch_id'] ?? $_SESSION['user_branch_id'];
            $newQuantity = (int)$_POST['new_quantity'];
            $notes = 'تعديل المخزون من واجهة تعديل المنتج';
            
            $inventoryModel = new Inventory($db);
            $inventoryModel->setProductStock($productId, $newQuantity, $branchId, $notes);
        }
        
        // تأكيد المعاملة فقط إذا بدأناها هنا
        if ($transactionStartedHere && $db->inTransaction()) {
            $db->commit();
        }
        
        // استرجاع بيانات المنتج المحدث
        $product = $productModel->getProductById($productId);
        
        return [
            'success' => true,
            'message' => 'تم تحديث المنتج بنجاح',
            'data' => $product
        ];
    } catch (Exception $e) {
        // التراجع عن المعاملة في حالة حدوث خطأ (فقط إذا بدأناها هنا)
        if ($transactionStartedHere && $db->inTransaction()) {
            $db->rollBack();
        }
        
        error_log('خطأ في تحديث المنتج: ' . $e->getMessage());
        throw new Exception($e->getMessage());
    }
}

/**
 * حذف منتج
 * @return array استجابة API
 */
function deleteProduct() {
    global $db;
    
    try {
        // التحقق من وجود معرف المنتج
        if (empty($_GET['id'])) {
            throw new Exception('معرف المنتج مطلوب');
        }
        
        $productId = (int)$_GET['id'];
        
        // إنشاء نموذج المنتجات
        $productModel = new Product($db);
        
        // التحقق من وجود المنتج
        $product = $productModel->getProductById($productId);
        
        if (!$product) {
            throw new Exception('المنتج غير موجود');
        }
        
        // حذف المنتج
        $result = $productModel->deleteProduct($productId);
        
        return [
            'success' => true,
            'message' => 'تم حذف المنتج بنجاح',
            'data' => null
        ];
    } catch (Exception $e) {
        error_log('خطأ في حذف المنتج: ' . $e->getMessage());
        throw new Exception($e->getMessage());
    }
}

/**
 * استرجاع فئات المنتجات
 * @return array استجابة API
 */
function getProductCategories() {
    global $db;
    
    try {
        // إنشاء نموذج المنتجات
        $productModel = new Product($db);
        
        // الحصول على فئات المنتجات
        $categories = $productModel->getProductCategories();
        
        return [
            'success' => true,
            'message' => 'تم جلب فئات المنتجات بنجاح',
            'data' => $categories
        ];
    } catch (Exception $e) {
        error_log('خطأ في استرجاع فئات المنتجات: ' . $e->getMessage());
        throw new Exception('حدث خطأ أثناء استرجاع فئات المنتجات');
    }
}

/**
 * إضافة فئة منتجات جديدة
 * @return array استجابة API
 */
function addProductCategory() {
    global $db;
    
    try {
        // استرجاع بيانات الفئة من الطلب
        $categoryData = [
            'name' => sanitizeInput($_POST['name'] ?? ''),
            'description' => sanitizeInput($_POST['description'] ?? '')
        ];
        
        // تحقق من الحقول المطلوبة
        if (empty($categoryData['name'])) {
            throw new Exception('اسم الفئة مطلوب');
        }
        
        // إنشاء نموذج المنتجات
        $productModel = new Product($db);
        
        // إضافة الفئة
        $categoryId = $productModel->addProductCategory($categoryData);
        
        // استرجاع بيانات الفئة المضافة
        $category = $productModel->getProductCategoryById($categoryId);
        
        return [
            'success' => true,
            'message' => 'تم إضافة فئة المنتجات بنجاح',
            'data' => $category
        ];
    } catch (Exception $e) {
        error_log('خطأ في إضافة فئة المنتجات: ' . $e->getMessage());
        throw new Exception($e->getMessage());
    }
}

/**
 * تحديث فئة منتجات
 * @return array استجابة API
 */
function updateProductCategory() {
    global $db;
    
    try {
        // التحقق من وجود معرف الفئة
        if (empty($_POST['id'])) {
            throw new Exception('معرف الفئة مطلوب');
        }
        
        $categoryId = (int)$_POST['id'];
        
        // استرجاع بيانات الفئة من الطلب
        $categoryData = [
            'name' => sanitizeInput($_POST['name'] ?? ''),
            'description' => sanitizeInput($_POST['description'] ?? '')
        ];
        
        // تحقق من الحقول المطلوبة
        if (empty($categoryData['name'])) {
            throw new Exception('اسم الفئة مطلوب');
        }
        
        // إنشاء نموذج المنتجات
        $productModel = new Product($db);
        
        // تحديث الفئة
        $result = $productModel->updateProductCategory($categoryId, $categoryData);
        
        // استرجاع بيانات الفئة المحدثة
        $category = $productModel->getProductCategoryById($categoryId);
        
        return [
            'success' => true,
            'message' => 'تم تحديث فئة المنتجات بنجاح',
            'data' => $category
        ];
    } catch (Exception $e) {
        error_log('خطأ في تحديث فئة المنتجات: ' . $e->getMessage());
        throw new Exception($e->getMessage());
    }
}

/**
 * حذف فئة منتجات
 * @return array استجابة API
 */
function deleteProductCategory() {
    global $db;
    
    try {
        // التحقق من وجود معرف الفئة
        if (empty($_GET['id'])) {
            throw new Exception('معرف الفئة مطلوب');
        }
        
        $categoryId = (int)$_GET['id'];
        
        // إنشاء نموذج المنتجات
        $productModel = new Product($db);
        
        // التحقق من وجود الفئة
        $category = $productModel->getProductCategoryById($categoryId);
        
        if (!$category) {
            throw new Exception('فئة المنتجات غير موجودة');
        }
        
        // حذف الفئة
        $result = $productModel->deleteProductCategory($categoryId);
        
        return [
            'success' => true,
            'message' => 'تم حذف فئة المنتجات بنجاح',
            'data' => null
        ];
    } catch (Exception $e) {
        error_log('خطأ في حذف فئة المنتجات: ' . $e->getMessage());
        throw new Exception($e->getMessage());
    }
}

/**
 * استرجاع تقرير المنتجات
 * @return array استجابة API
 */
function getProductReport() {
    global $db;
    
    try {
        // جلب فلاتر البحث
        $filters = [];
        
        if (!empty($_GET['category_id'])) {
            $filters['category_id'] = (int)$_GET['category_id'];
        }
        
        if (!empty($_GET['branch_id'])) {
            $filters['branch_id'] = (int)$_GET['branch_id'];
        } elseif (!isAdmin() && !empty($_SESSION['user_branch_id'])) {
            // استخدام فرع المستخدم الحالي إذا لم يتم تحديد فرع وكان المستخدم ليس مديرًا
            $filters['branch_id'] = $_SESSION['user_branch_id'];
        }
        
        if (isset($_GET['is_for_sale']) && $_GET['is_for_sale'] !== '') {
            $filters['is_for_sale'] = (int)$_GET['is_for_sale'];
        }
        
        if (isset($_GET['is_active']) && $_GET['is_active'] !== '') {
            $filters['is_active'] = (int)$_GET['is_active'];
        }
        
        // إنشاء نموذج المنتجات
        $productModel = new Product($db);
        
        // جلب تقرير المنتجات
        $report = $productModel->generateProductReport($filters);
        
        return [
            'success' => true,
            'message' => 'تم جلب تقرير المنتجات بنجاح',
            'data' => $report
        ];
    } catch (Exception $e) {
        error_log('خطأ في استرجاع تقرير المنتجات: ' . $e->getMessage());
        throw new Exception('حدث خطأ أثناء استرجاع تقرير المنتجات');
    }
}
?>
