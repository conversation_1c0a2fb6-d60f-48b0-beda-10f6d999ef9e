/**
 * أنماط مشتركة للنظام
 * يتم استخدام هذا الملف لتوحيد مظهر العناصر المشتركة في جميع الصفحات
 */

/* أنماط رسائل التنبيه */
.alert {
    padding: 15px;
    margin-bottom: 20px;
    border: 1px solid transparent;
    border-radius: 4px;
    position: relative;
}

.alert-dismissible {
    padding-right: 35px;
}

.alert-dismissible .close {
    position: absolute;
    top: 0;
    right: 0;
    padding: 15px;
    color: inherit;
    cursor: pointer;
    background: transparent;
    border: 0;
    font-size: 1.5rem;
    font-weight: 700;
    line-height: 1;
    opacity: .5;
}

.alert-success {
    color: #0f5132;
    background-color: #d1e7dd;
    border-color: #badbcc;
}

.alert-info {
    color: #055160;
    background-color: #cff4fc;
    border-color: #b6effb;
}

.alert-warning {
    color: #664d03;
    background-color: #fff3cd;
    border-color: #ffecb5;
}

.alert-danger {
    color: #842029;
    background-color: #f8d7da;
    border-color: #f5c2c7;
}

/* أنماط الجداول */
.table-container {
    overflow-x: auto;
    margin-bottom: 20px;
    border-radius: 5px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.05);
}

.table {
    width: 100%;
    margin-bottom: 0;
    color: #212529;
    border-collapse: collapse;
}

.table th,
.table td {
    padding: 12px 15px;
    vertical-align: middle;
    border-top: 1px solid #dee2e6;
}

.table thead th {
    vertical-align: bottom;
    border-bottom: 2px solid #dee2e6;
    background-color: #f8f9fa;
    color: #495057;
    font-weight: 600;
}

.table tbody + tbody {
    border-top: 2px solid #dee2e6;
}

.table-sm th,
.table-sm td {
    padding: 8px 10px;
}

.table-bordered {
    border: 1px solid #dee2e6;
}

.table-bordered th,
.table-bordered td {
    border: 1px solid #dee2e6;
}

.table-striped tbody tr:nth-of-type(odd) {
    background-color: rgba(0, 0, 0, 0.02);
}

.table-hover tbody tr:hover {
    background-color: rgba(0, 0, 0, 0.05);
}

.table-primary,
.table-primary > th,
.table-primary > td {
    background-color: #cfe2ff;
}

.table-secondary,
.table-secondary > th,
.table-secondary > td {
    background-color: #e2e3e5;
}

.table-success,
.table-success > th,
.table-success > td {
    background-color: #d1e7dd;
}

.table-info,
.table-info > th,
.table-info > td {
    background-color: #cff4fc;
}

.table-warning,
.table-warning > th,
.table-warning > td {
    background-color: #fff3cd;
}

.table-danger,
.table-danger > th,
.table-danger > td {
    background-color: #f8d7da;
}

/* أنماط الأزرار في الجداول */
.table .btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
    line-height: 1.5;
    border-radius: 0.2rem;
}

.table .btn-group {
    display: flex;
    gap: 5px;
}

/* أنماط الصفحات */
.pagination {
    display: flex;
    padding-left: 0;
    list-style: none;
    border-radius: 0.25rem;
    justify-content: center;
    margin-top: 20px;
}

.pagination .page-item {
    margin: 0 2px;
}

.pagination .page-link {
    position: relative;
    display: block;
    padding: 0.5rem 0.75rem;
    margin-left: -1px;
    line-height: 1.25;
    color: #007bff;
    background-color: #fff;
    border: 1px solid #dee2e6;
    text-decoration: none;
}

.pagination .page-item.active .page-link {
    z-index: 3;
    color: #fff;
    background-color: #007bff;
    border-color: #007bff;
}

.pagination .page-item.disabled .page-link {
    color: #6c757d;
    pointer-events: none;
    cursor: auto;
    background-color: #fff;
    border-color: #dee2e6;
}

/* أنماط البحث والفلترة */
.search-container {
    margin-bottom: 20px;
}

.search-form {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.search-form .form-group {
    flex: 1;
    min-width: 200px;
}

.search-form .btn {
    align-self: flex-end;
}

/* أنماط الفورم */
.form-container {
    background-color: #fff;
    padding: 20px;
    border-radius: 5px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.05);
    margin-bottom: 20px;
}

/* أنماط الكروت */
.card {
    position: relative;
    display: flex;
    flex-direction: column;
    min-width: 0;
    word-wrap: break-word;
    background-color: #fff;
    background-clip: border-box;
    border: 1px solid rgba(0, 0, 0, 0.125);
    border-radius: 0.25rem;
    margin-bottom: 20px;
}

.card-header {
    padding: 0.75rem 1.25rem;
    margin-bottom: 0;
    background-color: rgba(0, 0, 0, 0.03);
    border-bottom: 1px solid rgba(0, 0, 0, 0.125);
}

.card-body {
    flex: 1 1 auto;
    padding: 1.25rem;
}

.card-footer {
    padding: 0.75rem 1.25rem;
    background-color: rgba(0, 0, 0, 0.03);
    border-top: 1px solid rgba(0, 0, 0, 0.125);
}

/* أنماط الأيقونات */
.icon {
    display: inline-block;
    width: 1em;
    height: 1em;
    vertical-align: -0.125em;
    fill: currentColor;
}

/* أنماط الإشعارات */
.notification-item {
    padding: 10px 15px;
    border-bottom: 1px solid #f1f1f1;
    transition: background-color 0.2s;
    cursor: pointer;
}

.notification-item:hover {
    background-color: #f8f9fa;
}

.notification-item.unread {
    background-color: #e8f4fd;
}

.notification-item .notification-title {
    font-weight: 600;
    margin-bottom: 5px;
    font-size: 0.9rem;
}

.notification-item .notification-text {
    color: #6c757d;
    font-size: 0.85rem;
    margin-bottom: 5px;
}

.notification-item .notification-time {
    color: #adb5bd;
    font-size: 0.75rem;
}

.notification-item .notification-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: #e9ecef;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 10px;
}

.notification-item .notification-icon.appointment {
    background-color: #d1e7ff;
    color: #0d6efd;
}

.notification-item .notification-icon.system {
    background-color: #d1e6dd;
    color: #198754;
}

.notification-item .notification-icon.alert {
    background-color: #f8d7da;
    color: #dc3545;
}

/* أنماط الشاشات الصغيرة */
@media (max-width: 768px) {
    .table-responsive {
        display: block;
        width: 100%;
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
    }

    .search-form {
        flex-direction: column;
    }

    .search-form .form-group {
        width: 100%;
    }
}
