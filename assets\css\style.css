/* 
 * Salon Management System - Main Stylesheet
 * Version: 1.0
 * Last Updated: 2024
 */

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* Color Palette */
    --primary-color: #4a4a4a;
    --secondary-color: #6c757d;
    --accent-color: #007bff;
    --background-light: #f4f4f4;
    --text-color: #333;
    --white: #ffffff;
    --success-color: #28a745;
    --danger-color: #dc3545;
    --warning-color: #ffc107;

    /* Typography */
    --font-main: 'Arial', sans-serif;
    --font-heading: 'Helvetica', sans-serif;
}

body {
    font-family: var(--font-main);
    line-height: 1.6;
    color: var(--text-color);
    background-color: var(--background-light);
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-family: var(--font-heading);
    margin-bottom: 15px;
    color: var(--primary-color);
}

/* Layout */
.container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 15px;
}

.row {
    display: flex;
    flex-wrap: wrap;
    margin: 0 -15px;
}

.col {
    padding: 0 15px;
    flex: 1;
}

/* Main Layout Styles */
.wrapper {
    display: flex;
    width: 100%;
    align-items: stretch;
}

/* Sidebar Styles */
.sidebar {
    min-width: 250px;
    max-width: 250px;
    background: #343a40;
    color: #fff;
    transition: all 0.3s;
    height: 100vh;
    position: sticky;
    top: 0;
    overflow-y: auto;
}

.sidebar.active {
    margin-right: -250px;
}

.sidebar .sidebar-header {
    padding: 20px;
    background: #2c3136;
}

.sidebar ul.components {
    padding: 20px 0;
    border-bottom: 1px solid #47748b;
}

.sidebar ul li {
    padding: 0;
}

.sidebar ul li a {
    padding: 10px 20px;
    font-size: 1.1em;
    display: block;
    color: #fff;
    text-decoration: none;
    transition: all 0.3s;
}

.sidebar ul li a:hover {
    color: #fff;
    background: #007bff;
}

.sidebar ul li.active > a {
    color: #fff;
    background: #007bff;
}

/* Content Styles */
.content {
    width: 100%;
    min-height: 100vh;
    transition: all 0.3s;
    padding: 0;
    background-color: #f8f9fa;
}

.content.active {
    margin-right: 0;
}

/* Responsive Styles */
@media (max-width: 768px) {
    .sidebar {
        margin-right: -250px;
    }
    
    .sidebar.active {
        margin-right: 0;
    }
    
    .content {
        margin-right: 0;
    }
    
    .content.active {
        margin-right: 250px;
    }
}

/* Alerts Container */
#alerts-container {
    position: fixed;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 9999;
    width: 80%;
    max-width: 500px;
}

/* Forms */
.form-group {
    margin-bottom: 15px;
}

.form-control {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    transition: border-color 0.3s ease;
}

.form-control:focus {
    outline: none;
    border-color: var(--accent-color);
    box-shadow: 0 0 5px rgba(0,123,255,0.3);
}

/* Buttons */
.btn {
    display: inline-block;
    padding: 10px 15px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.btn-primary {
    background-color: var(--accent-color);
    color: var(--white);
}

.btn-primary:hover {
    background-color: #0056b3;
}

.btn-success {
    background-color: var(--success-color);
    color: var(--white);
}

.btn-danger {
    background-color: var(--danger-color);
    color: var(--white);
}

.btn-warning {
    background-color: var(--warning-color);
    color: var(--text-color);
}

/* Tables */
.table {
    width: 100%;
    margin-bottom: 20px;
    border-collapse: collapse;
}

.table th, 
.table td {
    border: 1px solid #ddd;
    padding: 12px;
    text-align: left;
}

.table thead {
    background-color: var(--background-light);
}

.table-striped tbody tr:nth-child(even) {
    background-color: rgba(0,0,0,0.05);
}

/* Cards */
.card {
    background-color: var(--white);
    border-radius: 5px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    margin-bottom: 20px;
}

.card-header {
    background-color: var(--background-light);
    padding: 15px;
    border-bottom: 1px solid #ddd;
}

.card-body {
    padding: 15px;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .sidebar {
        width: 100%;
        height: auto;
        position: relative;
    }

    .row {
        flex-direction: column;
    }

    .col {
        flex: 0 0 100%;
        max-width: 100%;
    }
}

/* Utility Classes */
.text-center {
    text-align: center;
}

.text-right {
    text-align: right;
}

.text-primary {
    color: var(--accent-color);
}

.text-success {
    color: var(--success-color);
}

.text-danger {
    color: var(--danger-color);
}

.mt-1 { margin-top: 0.25rem; }
.mt-2 { margin-top: 0.5rem; }
.mt-3 { margin-top: 1rem; }
.mt-4 { margin-top: 1.5rem; }
.mt-5 { margin-top: 3rem; }

.mb-1 { margin-bottom: 0.25rem; }
.mb-2 { margin-bottom: 0.5rem; }
.mb-3 { margin-bottom: 1rem; }
.mb-4 { margin-bottom: 1.5rem; }
.mb-5 { margin-bottom: 3rem; }

/* Alerts */
.alert {
    padding: 15px;
    margin-bottom: 20px;
    border-radius: 4px;
}

.alert-success {
    background-color: #dff0d8;
    border-color: #d6e9c6;
    color: #3c763d;
}

.alert-danger {
    background-color: #f2dede;
    border-color: #ebccd1;
    color: #a94442;
}

.alert-warning {
    background-color: #fcf8e3;
    border-color: #faebcc;
    color: #8a6d3b;
}

/* Dashboard Specific */
.dashboard-stats {
    display: flex;
    justify-content: space-between;
    margin-bottom: 20px;
}

.dashboard-stat {
    background-color: var(--white);
    border-radius: 5px;
    padding: 20px;
    text-align: center;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    flex: 1;
    margin: 0 10px;
}

.dashboard-stat-value {
    font-size: 2rem;
    font-weight: bold;
    color: var(--accent-color);
}

.dashboard-stat-label {
    color: var(--secondary-color);
}
