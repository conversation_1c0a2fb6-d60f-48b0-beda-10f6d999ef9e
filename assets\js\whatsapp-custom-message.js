/**
 * WhatsApp Custom Message
 * Este script maneja la creación de mensajes personalizados para WhatsApp
 * con soporte para información de descuentos
 */

const WhatsAppCustomMessage = {
    /**
     * Crear un mensaje personalizado para WhatsApp con información de descuento
     * @param {Object} invoiceData Datos de la factura
     * @returns {string} Mensaje formateado
     */
    createInvoiceMessage: function(invoiceData) {
        console.log('Creando mensaje personalizado para WhatsApp con datos:', invoiceData);
        console.log('DISCOUNT INFO - type:', invoiceData.discount_type, 'value:', invoiceData.discount_value, 'amount:', invoiceData.discount_amount, 'total:', invoiceData.total_amount);

        let message = '';

        // Verificar si hay una plantilla en los datos
        if (invoiceData.message && invoiceData.message.trim() !== '') {
            message = invoiceData.message;

            // Si hay descuento, actualizar o agregar la información de descuento
            if (invoiceData.discount_amount > 0) {
                // Si ya existe información de descuento, eliminarla para actualizarla
                if (message.includes('الخصم')) {
                    // Eliminar la información de descuento existente
                    // Buscar el patrón de descuento y el monto después del descuento
                    console.log('Original message with discount:', message);

                    // Primero intentar con un patrón más específico
                    const discountPattern = /\nالخصم:.*\nالمبلغ بعد الخصم:.*(?=\n)/;
                    const messageBeforeReplace = message;
                    message = message.replace(discountPattern, '');

                    // Verificar si se realizó algún cambio
                    if (message === messageBeforeReplace) {
                        console.log('First pattern did not match, trying alternative pattern');
                        // Intentar con un patrón alternativo
                        const alternativePattern = /\nالخصم:.*\nالمبلغ بعد الخصم:.*/;
                        message = message.replace(alternativePattern, '');
                    }

                    console.log('Message after removing discount info:', message);
                    console.log('Removed existing discount info from message');
                }
                const currencySymbol = 'ج'; // Símbolo de moneda predeterminado
                // معالجة محسنة لقيمة الخصم
                const discountType = invoiceData.discount_type === 'percentage' ? '%' : currencySymbol;
                let discountValue;

                if (invoiceData.discount_type === 'percentage') {
                    // استخدام قيمة الخصم الأصلية المرسلة من الخادم
                    console.log('PERCENTAGE DISCOUNT - checking for original value');
                    console.log('Discount data received:', {
                        discount_value: invoiceData.discount_value,
                        discount_amount: invoiceData.discount_amount,
                        total_amount: invoiceData.total_amount,
                        type: invoiceData.discount_type
                    });

                    if (invoiceData.discount_value !== null && invoiceData.discount_value !== undefined && invoiceData.discount_value !== '') {
                        // Usar el valor original enviado desde el servidor
                        discountValue = this._formatNumber(invoiceData.discount_value);
                        console.log('Using original discount value from server:', invoiceData.discount_value, 'formatted as:', discountValue);
                    } else {
                        // حساب النسبة المئوية من قيمة الخصم والمجموع كحل بديل
                        // Buscar en las notas si hay un valor original de descuento
                        const originalDiscountMatch = invoiceData.message && invoiceData.message.match(/\[ORIGINAL_DISCOUNT:(\d+(\.\d+)?)\]/);
                        if (originalDiscountMatch && originalDiscountMatch[1]) {
                            discountValue = this._formatNumber(parseFloat(originalDiscountMatch[1]));
                            console.log('Found original discount in notes:', originalDiscountMatch[1], 'formatted as:', discountValue);
                        } else {
                            // Calcular como último recurso
                            const percentage = (parseFloat(invoiceData.discount_amount) / parseFloat(invoiceData.total_amount)) * 100;
                            discountValue = this._formatNumber(percentage);
                            console.log('Calculated discount percentage as last resort:', percentage, 'formatted as:', discountValue);
                        }
                    }
                } else {
                    // خصم بقيمة ثابتة
                    discountValue = this._formatNumber(invoiceData.discount_amount);
                }

                console.log('Discount info (template):', {
                    type: invoiceData.discount_type,
                    value: discountValue,
                    amount: invoiceData.discount_amount,
                    total: invoiceData.total_amount
                });

                // Buscar dónde insertar la información de descuento (después del total)
                const totalPattern = /المبلغ الإجمالي:.*$/m;
                const match = message.match(totalPattern);

                console.log('Preparing to insert new discount info - value:', discountValue, 'type:', discountType);
                const discountInfo = `\nالخصم: ${discountValue} ${discountType}` +
                                    `\nالمبلغ بعد الخصم: ${this._formatNumber(invoiceData.final_amount)} ${currencySymbol}`;
                console.log('New discount info to insert:', discountInfo);

                if (match) {
                    console.log('Found total pattern at position:', match.index, 'with text:', match[0]);
                    const insertPosition = match.index + match[0].length;

                    // Insertar la información de descuento después del total
                    message = message.substring(0, insertPosition) +
                              discountInfo +
                              message.substring(insertPosition);
                    console.log('Inserted discount info after total amount');
                } else {
                    // Si no se encuentra el patrón, agregar al final
                    console.log('Total pattern not found, adding discount info at the end');
                    message += discountInfo;
                }
            }
        } else {
            // Crear un mensaje predeterminado si no hay plantilla
            message = `شكرًا لك ${invoiceData.customer_name}\n`;
            message += `تم إنشاء فاتورتك رقم ${invoiceData.invoice_number} بنجاح\n`;
            message += `التاريخ: ${this._formatDate(invoiceData.invoice_date || new Date())}\n`;
            message += `المبلغ الإجمالي: ${this._formatNumber(invoiceData.total_amount)} ج\n`;

            // Agregar información de descuento si existe
            if (invoiceData.discount_amount > 0) {
                const currencySymbol = 'ج';
                // معالجة محسنة لقيمة الخصم
                const discountType = invoiceData.discount_type === 'percentage' ? '%' : currencySymbol;
                let discountValue;

                if (invoiceData.discount_type === 'percentage') {
                    // استخدام قيمة الخصم الأصلية المرسلة من الخادم
                    console.log('PERCENTAGE DISCOUNT (default template) - checking for original value');
                    console.log('Discount data received (default template):', {
                        discount_value: invoiceData.discount_value,
                        discount_amount: invoiceData.discount_amount,
                        total_amount: invoiceData.total_amount,
                        type: invoiceData.discount_type
                    });

                    if (invoiceData.discount_value !== null && invoiceData.discount_value !== undefined && invoiceData.discount_value !== '') {
                        // Usar el valor original enviado desde el servidor
                        discountValue = this._formatNumber(invoiceData.discount_value);
                        console.log('Using original discount value from server:', invoiceData.discount_value, 'formatted as:', discountValue);
                    } else {
                        // حساب النسبة المئوية من قيمة الخصم والمجموع كحل بديل
                        // Buscar en las notas si hay un valor original de descuento
                        const originalDiscountMatch = invoiceData.message && invoiceData.message.match(/\[ORIGINAL_DISCOUNT:(\d+(\.\d+)?)\]/);
                        if (originalDiscountMatch && originalDiscountMatch[1]) {
                            discountValue = this._formatNumber(parseFloat(originalDiscountMatch[1]));
                            console.log('Found original discount in notes (default template):', originalDiscountMatch[1], 'formatted as:', discountValue);
                        } else {
                            // Calcular como último recurso
                            const percentage = (parseFloat(invoiceData.discount_amount) / parseFloat(invoiceData.total_amount)) * 100;
                            discountValue = this._formatNumber(percentage);
                            console.log('Calculated discount percentage as last resort (default template):', percentage, 'formatted as:', discountValue);
                        }
                    }
                } else {
                    // خصم بقيمة ثابتة
                    discountValue = this._formatNumber(invoiceData.discount_amount);
                }

                console.log('Discount info:', {
                    type: invoiceData.discount_type,
                    value: discountValue,
                    amount: invoiceData.discount_amount,
                    total: invoiceData.total_amount
                });

                console.log('Adding discount info to default template - value:', discountValue, 'type:', discountType);
                const discountInfo = `الخصم: ${discountValue} ${discountType}\n` +
                                    `المبلغ بعد الخصم: ${this._formatNumber(invoiceData.final_amount)} ${currencySymbol}\n`;
                console.log('Discount info for default template:', discountInfo);
                message += discountInfo;
            }

            message += '\nنشكرك على تعاملك معنا!';
        }

        console.log('Mensaje personalizado creado:', message);
        return message;
    },

    /**
     * Formatear un número con dos decimales
     * @private
     * @param {number} number Número a formatear
     * @returns {string} Número formateado
     */
    _formatNumber: function(number) {
        return number ? parseFloat(number).toFixed(2) : '0.00';
    },

    /**
     * Formatear una fecha en formato DD/MM/YYYY
     * @private
     * @param {string|Date} date Fecha a formatear
     * @returns {string} Fecha formateada
     */
    _formatDate: function(date) {
        if (!date) return '';

        const d = new Date(date);
        const day = String(d.getDate()).padStart(2, '0');
        const month = String(d.getMonth() + 1).padStart(2, '0');
        const year = d.getFullYear();

        return `${day}/${month}/${year}`;
    }
};
