<?php
/**
 * مكونات واجهة المستخدم المشتركة
 * يحتوي هذا الملف على دوال لعرض مكونات واجهة المستخدم المشتركة مثل الجداول ورسائل التنبيه
 */

// تعريف ثابت لمنع الوصول المباشر للملف
defined('BASEPATH') or exit('No direct script access allowed');

/**
 * عرض رسالة تنبيه
 * 
 * @param string $message نص الرسالة
 * @param string $type نوع الرسالة (success, info, warning, danger)
 * @param bool $dismissible هل يمكن إغلاق الرسالة
 * @return string HTML الرسالة
 */
function displayAlert($message, $type = 'info', $dismissible = true) {
    $alertClass = 'alert alert-' . $type;
    if ($dismissible) {
        $alertClass .= ' alert-dismissible fade show';
    }
    
    $closeButton = '';
    if ($dismissible) {
        $closeButton = '<button type="button" class="close" data-dismiss="alert" aria-label="إغلاق">
                            <span aria-hidden="true">&times;</span>
                        </button>';
    }
    
    return '<div class="' . $alertClass . '" role="alert">
                ' . $message . '
                ' . $closeButton . '
            </div>';
}

/**
 * عرض رسالة نجاح
 * 
 * @param string $message نص الرسالة
 * @param bool $dismissible هل يمكن إغلاق الرسالة
 * @return string HTML الرسالة
 */
function displaySuccessAlert($message, $dismissible = true) {
    return displayAlert($message, 'success', $dismissible);
}

/**
 * عرض رسالة معلومات
 * 
 * @param string $message نص الرسالة
 * @param bool $dismissible هل يمكن إغلاق الرسالة
 * @return string HTML الرسالة
 */
function displayInfoAlert($message, $dismissible = true) {
    return displayAlert($message, 'info', $dismissible);
}

/**
 * عرض رسالة تحذير
 * 
 * @param string $message نص الرسالة
 * @param bool $dismissible هل يمكن إغلاق الرسالة
 * @return string HTML الرسالة
 */
function displayWarningAlert($message, $dismissible = true) {
    return displayAlert($message, 'warning', $dismissible);
}

/**
 * عرض رسالة خطأ
 * 
 * @param string $message نص الرسالة
 * @param bool $dismissible هل يمكن إغلاق الرسالة
 * @return string HTML الرسالة
 */
function displayErrorAlert($message, $dismissible = true) {
    return displayAlert($message, 'danger', $dismissible);
}

/**
 * عرض رسائل الجلسة
 * يستخدم لعرض الرسائل المخزنة في الجلسة
 * 
 * @return string HTML الرسائل
 */
function displaySessionAlerts() {
    $html = '';
    
    // عرض رسائل النجاح
    if (isset($_SESSION['success_message'])) {
        $html .= displaySuccessAlert($_SESSION['success_message']);
        unset($_SESSION['success_message']);
    }
    
    // عرض رسائل المعلومات
    if (isset($_SESSION['info_message'])) {
        $html .= displayInfoAlert($_SESSION['info_message']);
        unset($_SESSION['info_message']);
    }
    
    // عرض رسائل التحذير
    if (isset($_SESSION['warning_message'])) {
        $html .= displayWarningAlert($_SESSION['warning_message']);
        unset($_SESSION['warning_message']);
    }
    
    // عرض رسائل الخطأ
    if (isset($_SESSION['error_message'])) {
        $html .= displayErrorAlert($_SESSION['error_message']);
        unset($_SESSION['error_message']);
    }
    
    return $html;
}

/**
 * تخزين رسالة في الجلسة
 * 
 * @param string $message نص الرسالة
 * @param string $type نوع الرسالة (success, info, warning, error)
 */
function setSessionAlert($message, $type = 'info') {
    $_SESSION[$type . '_message'] = $message;
}

/**
 * عرض جدول بيانات
 * 
 * @param array $headers عناوين الأعمدة
 * @param array $data بيانات الجدول
 * @param array $options خيارات إضافية
 * @return string HTML الجدول
 */
function displayTable($headers, $data, $options = []) {
    // الخيارات الافتراضية
    $defaultOptions = [
        'id' => 'dataTable',
        'class' => 'table table-striped table-hover table-bordered data-table',
        'responsive' => true,
        'actions' => true,
        'actionButtons' => [
            'view' => true,
            'edit' => true,
            'delete' => true
        ],
        'actionUrls' => [
            'view' => '#',
            'edit' => '#',
            'delete' => '#'
        ],
        'actionColumn' => 'الإجراءات',
        'emptyMessage' => 'لا توجد بيانات للعرض',
        'idColumn' => 'id'
    ];
    
    // دمج الخيارات المخصصة مع الخيارات الافتراضية
    $options = array_merge($defaultOptions, $options);
    
    // بداية الحاوية
    $html = '<div class="table-container">';
    
    // بداية الجدول
    $html .= '<table id="' . $options['id'] . '" class="' . $options['class'] . '">';
    
    // رأس الجدول
    $html .= '<thead><tr>';
    foreach ($headers as $header) {
        $html .= '<th>' . $header . '</th>';
    }
    if ($options['actions']) {
        $html .= '<th>' . $options['actionColumn'] . '</th>';
    }
    $html .= '</tr></thead>';
    
    // جسم الجدول
    $html .= '<tbody>';
    if (empty($data)) {
        $colSpan = count($headers) + ($options['actions'] ? 1 : 0);
        $html .= '<tr><td colspan="' . $colSpan . '" class="text-center">' . $options['emptyMessage'] . '</td></tr>';
    } else {
        foreach ($data as $row) {
            $html .= '<tr>';
            foreach ($headers as $key => $header) {
                $value = isset($row[$key]) ? $row[$key] : '';
                $html .= '<td>' . $value . '</td>';
            }
            
            // أزرار الإجراءات
            if ($options['actions']) {
                $html .= '<td class="text-center">';
                $html .= '<div class="btn-group">';
                
                $id = isset($row[$options['idColumn']]) ? $row[$options['idColumn']] : '';
                
                // زر العرض
                if ($options['actionButtons']['view']) {
                    $viewUrl = str_replace('{id}', $id, $options['actionUrls']['view']);
                    $html .= '<a href="' . $viewUrl . '" class="btn btn-sm btn-info view-btn" title="عرض"><i class="fas fa-eye"></i></a>';
                }
                
                // زر التعديل
                if ($options['actionButtons']['edit']) {
                    $editUrl = str_replace('{id}', $id, $options['actionUrls']['edit']);
                    $html .= '<a href="' . $editUrl . '" class="btn btn-sm btn-primary edit-btn" title="تعديل"><i class="fas fa-edit"></i></a>';
                }
                
                // زر الحذف
                if ($options['actionButtons']['delete']) {
                    $deleteUrl = str_replace('{id}', $id, $options['actionUrls']['delete']);
                    $html .= '<a href="javascript:void(0);" class="btn btn-sm btn-danger delete-btn" data-url="' . $deleteUrl . '" data-id="' . $id . '" title="حذف"><i class="fas fa-trash"></i></a>';
                }
                
                $html .= '</div>';
                $html .= '</td>';
            }
            
            $html .= '</tr>';
        }
    }
    $html .= '</tbody>';
    
    // نهاية الجدول
    $html .= '</table>';
    
    // نهاية الحاوية
    $html .= '</div>';
    
    return $html;
}

/**
 * عرض عناصر الصفحات
 * 
 * @param int $currentPage الصفحة الحالية
 * @param int $totalPages إجمالي عدد الصفحات
 * @param string $url رابط الصفحة
 * @return string HTML عناصر الصفحات
 */
function displayPagination($currentPage, $totalPages, $url = '?page={page}') {
    if ($totalPages <= 1) {
        return '';
    }
    
    $html = '<nav aria-label="صفحات">';
    $html .= '<ul class="pagination">';
    
    // زر الصفحة السابقة
    $prevDisabled = ($currentPage <= 1) ? ' disabled' : '';
    $prevUrl = str_replace('{page}', $currentPage - 1, $url);
    $html .= '<li class="page-item' . $prevDisabled . '"><a class="page-link" href="' . $prevUrl . '" aria-label="السابق"><span aria-hidden="true">&laquo;</span></a></li>';
    
    // أرقام الصفحات
    $startPage = max(1, $currentPage - 2);
    $endPage = min($totalPages, $currentPage + 2);
    
    // إضافة الصفحة الأولى إذا لم تكن ضمن النطاق
    if ($startPage > 1) {
        $firstUrl = str_replace('{page}', 1, $url);
        $html .= '<li class="page-item"><a class="page-link" href="' . $firstUrl . '">1</a></li>';
        if ($startPage > 2) {
            $html .= '<li class="page-item disabled"><a class="page-link" href="#">...</a></li>';
        }
    }
    
    // الصفحات الوسطى
    for ($i = $startPage; $i <= $endPage; $i++) {
        $active = ($i == $currentPage) ? ' active' : '';
        $pageUrl = str_replace('{page}', $i, $url);
        $html .= '<li class="page-item' . $active . '"><a class="page-link" href="' . $pageUrl . '">' . $i . '</a></li>';
    }
    
    // إضافة الصفحة الأخيرة إذا لم تكن ضمن النطاق
    if ($endPage < $totalPages) {
        if ($endPage < $totalPages - 1) {
            $html .= '<li class="page-item disabled"><a class="page-link" href="#">...</a></li>';
        }
        $lastUrl = str_replace('{page}', $totalPages, $url);
        $html .= '<li class="page-item"><a class="page-link" href="' . $lastUrl . '">' . $totalPages . '</a></li>';
    }
    
    // زر الصفحة التالية
    $nextDisabled = ($currentPage >= $totalPages) ? ' disabled' : '';
    $nextUrl = str_replace('{page}', $currentPage + 1, $url);
    $html .= '<li class="page-item' . $nextDisabled . '"><a class="page-link" href="' . $nextUrl . '" aria-label="التالي"><span aria-hidden="true">&raquo;</span></a></li>';
    
    $html .= '</ul>';
    $html .= '</nav>';
    
    return $html;
}

/**
 * عرض نموذج البحث
 * 
 * @param array $fields حقول البحث
 * @param array $values القيم الحالية
 * @param string $action رابط إرسال النموذج
 * @return string HTML نموذج البحث
 */
function displaySearchForm($fields, $values = [], $action = '') {
    $html = '<div class="search-container">';
    $html .= '<form class="search-form" action="' . $action . '" method="GET">';
    
    foreach ($fields as $field) {
        $fieldName = $field['name'];
        $fieldLabel = isset($field['label']) ? $field['label'] : $fieldName;
        $fieldType = isset($field['type']) ? $field['type'] : 'text';
        $fieldValue = isset($values[$fieldName]) ? $values[$fieldName] : '';
        $fieldOptions = isset($field['options']) ? $field['options'] : [];
        
        $html .= '<div class="form-group">';
        $html .= '<label for="' . $fieldName . '">' . $fieldLabel . '</label>';
        
        switch ($fieldType) {
            case 'select':
                $html .= '<select class="form-control" id="' . $fieldName . '" name="' . $fieldName . '">';
                $html .= '<option value="">الكل</option>';
                foreach ($fieldOptions as $optionValue => $optionLabel) {
                    $selected = ($fieldValue == $optionValue) ? ' selected' : '';
                    $html .= '<option value="' . $optionValue . '"' . $selected . '>' . $optionLabel . '</option>';
                }
                $html .= '</select>';
                break;
                
            case 'date':
                $html .= '<input type="date" class="form-control" id="' . $fieldName . '" name="' . $fieldName . '" value="' . $fieldValue . '">';
                break;
                
            default:
                $html .= '<input type="text" class="form-control" id="' . $fieldName . '" name="' . $fieldName . '" value="' . $fieldValue . '" placeholder="' . $fieldLabel . '">';
                break;
        }
        
        $html .= '</div>';
    }
    
    $html .= '<button type="submit" class="btn btn-primary">بحث</button>';
    $html .= '<button type="reset" class="btn btn-secondary">إعادة تعيين</button>';
    $html .= '</form>';
    $html .= '</div>';
    
    return $html;
}

/**
 * عرض بطاقة
 * 
 * @param string $title عنوان البطاقة
 * @param string $content محتوى البطاقة
 * @param array $options خيارات إضافية
 * @return string HTML البطاقة
 */
function displayCard($title, $content, $options = []) {
    // الخيارات الافتراضية
    $defaultOptions = [
        'id' => '',
        'class' => '',
        'headerClass' => '',
        'bodyClass' => '',
        'footerContent' => '',
        'footerClass' => ''
    ];
    
    // دمج الخيارات المخصصة مع الخيارات الافتراضية
    $options = array_merge($defaultOptions, $options);
    
    $id = !empty($options['id']) ? ' id="' . $options['id'] . '"' : '';
    $class = !empty($options['class']) ? ' ' . $options['class'] : '';
    $headerClass = !empty($options['headerClass']) ? ' ' . $options['headerClass'] : '';
    $bodyClass = !empty($options['bodyClass']) ? ' ' . $options['bodyClass'] : '';
    
    $html = '<div' . $id . ' class="card' . $class . '">';
    
    // رأس البطاقة
    if (!empty($title)) {
        $html .= '<div class="card-header' . $headerClass . '">' . $title . '</div>';
    }
    
    // جسم البطاقة
    $html .= '<div class="card-body' . $bodyClass . '">' . $content . '</div>';
    
    // تذييل البطاقة
    if (!empty($options['footerContent'])) {
        $footerClass = !empty($options['footerClass']) ? ' ' . $options['footerClass'] : '';
        $html .= '<div class="card-footer' . $footerClass . '">' . $options['footerContent'] . '</div>';
    }
    
    $html .= '</div>';
    
    return $html;
}

/**
 * عرض زر
 * 
 * @param string $text نص الزر
 * @param string $url رابط الزر
 * @param string $type نوع الزر (primary, secondary, success, danger, warning, info)
 * @param array $options خيارات إضافية
 * @return string HTML الزر
 */
function displayButton($text, $url = 'javascript:void(0);', $type = 'primary', $options = []) {
    // الخيارات الافتراضية
    $defaultOptions = [
        'id' => '',
        'class' => '',
        'icon' => '',
        'attributes' => []
    ];
    
    // دمج الخيارات المخصصة مع الخيارات الافتراضية
    $options = array_merge($defaultOptions, $options);
    
    $id = !empty($options['id']) ? ' id="' . $options['id'] . '"' : '';
    $class = 'btn btn-' . $type;
    $class .= !empty($options['class']) ? ' ' . $options['class'] : '';
    
    $attributes = '';
    foreach ($options['attributes'] as $key => $value) {
        $attributes .= ' ' . $key . '="' . $value . '"';
    }
    
    $icon = !empty($options['icon']) ? '<i class="' . $options['icon'] . '"></i> ' : '';
    
    $html = '<a href="' . $url . '"' . $id . ' class="' . $class . '"' . $attributes . '>' . $icon . $text . '</a>';
    
    return $html;
}
