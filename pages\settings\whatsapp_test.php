<?php
/**
 * صفحة اختبار اتصال WhatsApp
 * تستخدم لاختبار الاتصال بخادم WhatsApp المحلي وإرسال رسائل اختبار
 */

// تعريف ثابت لمنع الوصول المباشر للملفات المرتبطة
define('BASEPATH', true);

// استدعاء ملفات التهيئة
require_once '../../config/init.php';

// التحقق من تسجيل الدخول
redirectIfNotLoggedIn();

// التحقق من صلاحيات المستخدم
if (!hasPermission('settings_view')) {
    $_SESSION['error_message'] = 'ليس لديك صلاحية للوصول إلى هذه الصفحة';
    header('Location: ' . BASE_URL . 'pages/dashboard.php');
    exit;
}

// عنوان الصفحة
$pageTitle = 'اختبار اتصال WhatsApp';

// استدعاء رأس الصفحة
include '../../includes/templates/header.php';
?>

<div class="container-fluid py-4">
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <h1 class="h3 mb-2">
                        اختبار اتصال WhatsApp
                        <span class="badge bg-warning text-dark ms-2">النسخة التجريبية</span>
                    </h1>
                    <p class="text-muted">هذه الصفحة متوفرة فقط في النسخة المدفوعة لاختبار الاتصال بخادم WhatsApp المحلي</p>
                </div>
            </div>
        </div>
    </div>

    <!-- تحذير النسخة التجريبية -->
    <div class="alert alert-warning border-0 shadow-sm mb-4">
        <div class="d-flex align-items-center">
            <i class="fas fa-exclamation-triangle fa-2x text-warning me-3"></i>
            <div>
                <h5 class="alert-heading mb-2">ميزة غير متاحة في النسخة التجريبية</h5>
                <p class="mb-2">اختبار اتصال WhatsApp متوفر فقط في النسخة المدفوعة من النظام.</p>
                <hr>
                <div class="row">
                    <div class="col-md-6">
                        <h6 class="mb-2">المميزات المتوفرة في النسخة المدفوعة:</h6>
                        <ul class="mb-0">
                            <li>اختبار الاتصال بخادم WhatsApp</li>
                            <li>إرسال رسائل اختبار فورية</li>
                            <li>مراقبة حالة الاتصال</li>
                            <li>سجل مفصل للأحداث</li>
                            <li>تشخيص المشاكل تلقائياً</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6 class="mb-2">للحصول على النسخة الكاملة:</h6>
                        <div class="d-grid gap-2">
                            <a href="https://wa.me/201556262660" target="_blank" class="btn btn-success">
                                <i class="fab fa-whatsapp me-2"></i>تواصل عبر WhatsApp
                            </a>
                            <a href="tel:+201556262660" class="btn btn-outline-primary">
                                <i class="fas fa-phone me-2"></i>اتصال مباشر
                            </a>
                            <a href="mailto:<EMAIL>" class="btn btn-outline-secondary">
                                <i class="fas fa-envelope me-2"></i>إرسال بريد إلكتروني
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- رسائل النجاح والخطأ -->
    <?php include '../../includes/templates/alerts.php'; ?>

    <div class="row">
        <div class="col-md-6">
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-header bg-light py-3">
                    <h5 class="mb-0 text-muted">
                        <i class="fas fa-lock me-2"></i>اختبار الاتصال بالخادم المحلي (متوفر في النسخة المدفوعة فقط)
                    </h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>في النسخة المدفوعة:</strong> يمكن اختبار الاتصال بخادم WhatsApp المحلي بنقرة واحدة.
                    </div>
                    <button class="btn btn-secondary" disabled>
                        <i class="fas fa-plug"></i> اختبار الاتصال
                    </button>
                    <div class="mt-3">
                        <div class="alert alert-secondary">
                            <i class="fas fa-crown me-2"></i>
                            <strong>للحصول على هذه الميزة:</strong> تواصل معنا للحصول على النسخة المدفوعة.
                        </div>
                    </div>
                </div>
            </div>

            <div class="card border-0 shadow-sm mb-4">
                <div class="card-header bg-light py-3">
                    <h5 class="mb-0 text-muted">
                        <i class="fas fa-lock me-2"></i>إرسال رسالة اختبار (متوفر في النسخة المدفوعة فقط)
                    </h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>في النسخة المدفوعة:</strong> يمكن إرسال رسائل اختبار مباشرة من لوحة التحكم.
                    </div>

                    <form class="demo-disabled-form">
                        <div class="mb-3">
                            <label for="phoneNumber" class="form-label text-muted">رقم الهاتف</label>
                            <input type="text" class="form-control" id="phoneNumber" name="phoneNumber" placeholder="أدخل رقم الهاتف (مثال: 01234567890)" disabled>
                            <small class="form-text text-muted">سيتم إضافة رمز البلد تلقائيًا إذا لم يكن موجودًا</small>
                        </div>
                        <div class="mb-3">
                            <label for="message" class="form-label text-muted">الرسالة</label>
                            <textarea class="form-control" id="message" name="message" rows="3" placeholder="أدخل نص الرسالة" disabled>هذه رسالة اختبار من نظام إدارة الصالون</textarea>
                        </div>
                        <button type="button" class="btn btn-secondary" disabled>
                            <i class="fab fa-whatsapp"></i> إرسال رسالة اختبار
                        </button>
                    </form>

                    <div class="mt-3">
                        <div class="alert alert-warning">
                            <i class="fas fa-crown me-2"></i>
                            <strong>احصل على النسخة الكاملة:</strong>
                            <div class="mt-2">
                                <a href="https://wa.me/201556262660" target="_blank" class="btn btn-success btn-sm">
                                    <i class="fab fa-whatsapp me-1"></i>واتساب
                                </a>
                                <a href="tel:+201556262660" class="btn btn-primary btn-sm">
                                    <i class="fas fa-phone me-1"></i>اتصال
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-6">
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-header bg-light py-3">
                    <h5 class="mb-0 text-muted">
                        <i class="fas fa-lock me-2"></i>معلومات التكوين (متوفرة في النسخة المدفوعة فقط)
                    </h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>في النسخة المدفوعة:</strong> يتم عرض معلومات مفصلة عن حالة الاتصال والإعدادات.
                    </div>

                    <h6 class="text-muted">عنوان الخادم المحلي:</h6>
                    <p><code class="text-muted">http://localhost:3000</code></p>

                    <h6 class="text-muted">حالة الاتصال:</h6>
                    <p>
                        <span class="badge bg-secondary">غير متاح في النسخة التجريبية</span>
                    </p>

                    <h6 class="text-muted">إعدادات WhatsApp:</h6>
                    <ul class="text-muted">
                        <li>تفعيل WhatsApp:
                            <span class="badge bg-secondary">غير متاح</span>
                        </li>
                        <li>استخدام جانب العميل:
                            <span class="badge bg-secondary">غير متاح</span>
                        </li>
                    </ul>

                    <h6 class="text-muted">متطلبات التشغيل في النسخة المدفوعة:</h6>
                    <ul class="text-muted">
                        <li>تشغيل خادم WhatsApp المحلي على جهاز الكمبيوتر</li>
                        <li>تسجيل الدخول إلى WhatsApp Web في المتصفح</li>
                        <li>السماح بالاتصال من المتصفح إلى الخادم المحلي</li>
                    </ul>

                    <div class="alert alert-warning">
                        <h6><i class="fas fa-crown"></i> للحصول على هذه المميزات:</h6>
                        <p class="mb-2">تواصل معنا للحصول على النسخة المدفوعة مع جميع مميزات WhatsApp.</p>
                        <div class="d-grid gap-2">
                            <a href="https://wa.me/201556262660" target="_blank" class="btn btn-success btn-sm">
                                <i class="fab fa-whatsapp me-1"></i>واتساب
                            </a>
                            <a href="tel:+201556262660" class="btn btn-primary btn-sm">
                                <i class="fas fa-phone me-1"></i>اتصال
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <div class="card border-0 shadow-sm">
                <div class="card-header bg-light py-3">
                    <h5 class="mb-0 text-muted">
                        <i class="fas fa-lock me-2"></i>سجل الأحداث (متوفر في النسخة المدفوعة فقط)
                    </h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>في النسخة المدفوعة:</strong> يتم عرض سجل مفصل لجميع أحداث WhatsApp.
                    </div>
                    <div class="bg-light p-3 rounded" style="height: 200px; overflow-y: auto;">
                        <div class="text-muted small">سجل الأحداث متوفر في النسخة المدفوعة فقط...</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
// استدعاء تذييل الصفحة
include '../../includes/templates/footer.php';
?>

<!-- إضافة CSS للنموذج المعطل -->
<style>
.demo-disabled-form input:disabled,
.demo-disabled-form textarea:disabled,
.demo-disabled-form select:disabled {
    background-color: #f8f9fa;
    opacity: 0.6;
}

.demo-disabled-form .form-check-input:disabled {
    opacity: 0.5;
}

.text-muted {
    color: #6c757d !important;
}
</style>

<!-- رسالة للمستخدمين الذين يحاولون استخدام الميزات المعطلة -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // إضافة رسالة عند النقر على العناصر المعطلة
    const disabledElements = document.querySelectorAll('input:disabled, textarea:disabled, button:disabled');

    disabledElements.forEach(element => {
        element.addEventListener('click', function(e) {
            e.preventDefault();

            // إنشاء نافذة تنبيه
            const modal = document.createElement('div');
            modal.className = 'modal fade';
            modal.innerHTML = `
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header bg-warning text-dark">
                            <h5 class="modal-title">
                                <i class="fas fa-crown me-2"></i>ميزة متوفرة في النسخة المدفوعة فقط
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <p>هذه الميزة متوفرة فقط في النسخة المدفوعة من النظام.</p>
                            <p><strong>للحصول على النسخة الكاملة مع جميع مميزات اختبار WhatsApp:</strong></p>
                            <ul>
                                <li>اختبار الاتصال بالخادم المحلي</li>
                                <li>إرسال رسائل اختبار فورية</li>
                                <li>مراقبة حالة الاتصال المباشرة</li>
                                <li>سجل مفصل للأحداث</li>
                                <li>تشخيص المشاكل تلقائياً</li>
                            </ul>
                            <div class="d-grid gap-2 mt-3">
                                <a href="https://wa.me/201556262660" target="_blank" class="btn btn-success">
                                    <i class="fab fa-whatsapp me-2"></i>تواصل عبر WhatsApp
                                </a>
                                <a href="tel:+201556262660" class="btn btn-primary">
                                    <i class="fas fa-phone me-2"></i>اتصال مباشر: +201556262660
                                </a>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                        </div>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);
            const bsModal = new bootstrap.Modal(modal);
            bsModal.show();

            modal.addEventListener('hidden.bs.modal', () => {
                modal.remove();
            });
        });
    });
});
</script>
