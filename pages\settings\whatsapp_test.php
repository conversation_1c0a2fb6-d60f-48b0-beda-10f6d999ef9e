<?php
/**
 * صفحة اختبار اتصال WhatsApp
 * تستخدم لاختبار الاتصال بخادم WhatsApp المحلي وإرسال رسائل اختبار
 */

// تعريف ثابت لمنع الوصول المباشر للملفات المرتبطة
define('BASEPATH', true);

// استدعاء ملفات التهيئة
require_once '../../config/init.php';

// التحقق من تسجيل الدخول
redirectIfNotLoggedIn();

// التحقق من صلاحيات المستخدم
if (!hasPermission('settings_view')) {
    $_SESSION['error_message'] = 'ليس لديك صلاحية للوصول إلى هذه الصفحة';
    header('Location: ' . BASE_URL . 'pages/dashboard.php');
    exit;
}

// عنوان الصفحة
$pageTitle = 'اختبار اتصال WhatsApp';

// استدعاء رأس الصفحة
include '../../includes/templates/header.php';
?>

<div class="container-fluid py-4">
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <h1 class="h3 mb-2">اختبار اتصال WhatsApp</h1>
                    <p class="text-muted">استخدم هذه الصفحة لاختبار الاتصال بخادم WhatsApp المحلي وإرسال رسائل اختبار</p>
                </div>
            </div>
        </div>
    </div>

    <!-- رسائل النجاح والخطأ -->
    <?php include '../../includes/templates/alerts.php'; ?>

    <div class="row">
        <div class="col-md-6">
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-header bg-white py-3">
                    <h5 class="mb-0">اختبار الاتصال بالخادم المحلي</h5>
                </div>
                <div class="card-body">
                    <p>انقر على الزر أدناه لاختبار الاتصال بخادم WhatsApp المحلي:</p>
                    <button id="testConnectionBtn" class="btn btn-primary">
                        <i class="fas fa-plug"></i> اختبار الاتصال
                    </button>
                    <div id="connectionResult" class="mt-3" style="display: none;"></div>
                </div>
            </div>

            <div class="card border-0 shadow-sm mb-4">
                <div class="card-header bg-white py-3">
                    <h5 class="mb-0">إرسال رسالة اختبار</h5>
                </div>
                <div class="card-body">
                    <form id="testMessageForm">
                        <div class="mb-3">
                            <label for="phoneNumber" class="form-label">رقم الهاتف</label>
                            <input type="text" class="form-control" id="phoneNumber" name="phoneNumber" placeholder="أدخل رقم الهاتف (مثال: 01234567890)" required>
                            <small class="form-text text-muted">سيتم إضافة رمز البلد تلقائيًا إذا لم يكن موجودًا</small>
                        </div>
                        <div class="mb-3">
                            <label for="message" class="form-label">الرسالة</label>
                            <textarea class="form-control" id="message" name="message" rows="3" placeholder="أدخل نص الرسالة" required>هذه رسالة اختبار من نظام إدارة الصالون</textarea>
                        </div>
                        <button type="submit" class="btn btn-success">
                            <i class="fab fa-whatsapp"></i> إرسال رسالة اختبار
                        </button>
                    </form>
                    <div id="messageResult" class="mt-3" style="display: none;"></div>
                </div>
            </div>
        </div>

        <div class="col-md-6">
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-header bg-white py-3">
                    <h5 class="mb-0">معلومات التكوين</h5>
                </div>
                <div class="card-body">
                    <h6>عنوان الخادم المحلي:</h6>
                    <p><code id="serverUrl">http://localhost:3000</code></p>

                    <h6>حالة الاتصال:</h6>
                    <p id="connectionStatus">
                        <span class="badge bg-secondary">غير معروف</span>
                    </p>

                    <h6>إعدادات WhatsApp:</h6>
                    <ul>
                        <?php
                        $settings = new Settings($db);
                        $whatsappEnabled = $settings->get('whatsapp_enabled', '0');
                        $whatsappClientSide = $settings->get('whatsapp_client_side', '0');
                        ?>
                        <li>تفعيل WhatsApp:
                            <span class="badge <?php echo $whatsappEnabled == '1' ? 'bg-success' : 'bg-danger'; ?>">
                                <?php echo $whatsappEnabled == '1' ? 'مفعل' : 'غير مفعل'; ?>
                            </span>
                        </li>
                        <li>استخدام جانب العميل:
                            <span class="badge <?php echo $whatsappClientSide == '1' ? 'bg-success' : 'bg-danger'; ?>">
                                <?php echo $whatsappClientSide == '1' ? 'مفعل' : 'غير مفعل'; ?>
                            </span>
                        </li>
                    </ul>

                    <h6>متطلبات التشغيل:</h6>
                    <ul>
                        <li>تشغيل خادم WhatsApp المحلي على جهاز الكمبيوتر</li>
                        <li>تسجيل الدخول إلى WhatsApp Web في المتصفح</li>
                        <li>السماح بالاتصال من المتصفح إلى الخادم المحلي</li>
                    </ul>

                    <div class="alert alert-info">
                        <h6><i class="fas fa-info-circle"></i> ملاحظة:</h6>
                        <p class="mb-0">إذا كنت تواجه مشاكل في الاتصال، تأكد من تشغيل خادم WhatsApp المحلي وتسجيل الدخول إلى WhatsApp Web.</p>
                    </div>
                </div>
            </div>

            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white py-3">
                    <h5 class="mb-0">سجل الأحداث</h5>
                </div>
                <div class="card-body">
                    <div id="eventLog" class="bg-light p-3 rounded" style="height: 200px; overflow-y: auto;">
                        <div class="text-muted small">سيتم عرض سجل الأحداث هنا...</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
// استدعاء تذييل الصفحة
include '../../includes/templates/footer.php';
?>

<!-- استدعاء ملف JavaScript لاختبار WhatsApp من جانب العميل -->
<script src="<?php echo BASE_URL; ?>assets/js/whatsapp-test.js"></script>
