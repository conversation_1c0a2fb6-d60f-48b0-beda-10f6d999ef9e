<?php
/**
 * واجهة برمجة التطبيقات (API) لإدارة الموظفين
 */

// تحديد المسار الأساسي
define('BASEPATH', true);

// استدعاء ملف التهيئة
require_once __DIR__ . '/../config/init.php';



// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    echo json_encode([
        'status' => 'error',
        'message' => 'يجب تسجيل الدخول أولاً'
    ]);
    exit;
}

// الحصول على البيانات المرسلة (دعم JSON و POST)
$requestData = [];
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $requestData = $_POST;
} else {
    // محاولة قراءة البيانات من JSON
    $jsonInput = file_get_contents('php://input');
    if (!empty($jsonInput)) {
        $jsonData = json_decode($jsonInput, true);
        if (json_last_error() === JSON_ERROR_NONE) {
            $requestData = $jsonData;
        }
    }

    // دعم بيانات GET
    if (empty($requestData) && !empty($_GET)) {
        $requestData = $_GET;
    }
}

// التعامل مع طلبات JSON المرسلة عبر POST
if (empty($requestData) && isset($_SERVER['HTTP_X_HTTP_METHOD_OVERRIDE']) && $_SERVER['HTTP_X_HTTP_METHOD_OVERRIDE'] === 'JSON') {
    $jsonInput = file_get_contents('php://input');
    parse_str($jsonInput, $requestData);
}

// الحصول على الإجراء المطلوب
$action = $requestData['action'] ?? '';

// التحقق من وجود إجراء
if (empty($action)) {
    echo json_encode([
        'status' => 'error',
        'message' => 'لم يتم تحديد الإجراء المطلوب'
    ]);
    exit;
}

// إنشاء كائن الموظف
$employeeModel = new Employee($db);
$settingsModel = new Settings($db);

// إنشاء كائن الخدمة (للحصول على الخدمات المرتبطة بالموظفين)
$serviceModel = new Service($db);
function convertArabicToEnglishNumbers($str) {
    if (!$str) return $str;

    $arabicNumbers = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
    $englishNumbers = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'];

    return str_replace($arabicNumbers, $englishNumbers, $str);
}
// معالجة الطلب بناءً على الإجراء
try {
    switch ($action) {
        case 'get_employees':
            // التحقق من صلاحية العرض
            if (!hasPermission('employees_view')) {
                throw new Exception('ليس لديك صلاحية لعرض الموظفين');
            }

            // استرجاع الموظفين
            $filters = [];

            if (isset($requestData['search'])) {
                $filters['search'] = $requestData['search'];
            }

            if (isset($requestData['branch_id'])) {
                $filters['branch_id'] = $requestData['branch_id'];
            }

            if (isset($requestData['position'])) {
                $filters['position'] = $requestData['position'];
            }

            if (isset($requestData['is_active'])) {
                $filters['is_active'] = $requestData['is_active'];
            }

            if (isset($requestData['salary_type'])) {
                $filters['salary_type'] = $requestData['salary_type'];
            }

            if (isset($requestData['limit'])) {
                $filters['limit'] = intval($requestData['limit']);
            }

            if (isset($requestData['offset'])) {
                $filters['offset'] = intval($requestData['offset']);
            }

            if (isset($requestData['with_services']) && $requestData['with_services']) {
                $filters['with_services'] = true;
            }

            $employees = $employeeModel->getEmployees($filters);
            $totalEmployees = $employeeModel->getEmployeesCount($filters);

            echo json_encode([
                'status' => 'success',
                'employees' => $employees,
                'total' => $totalEmployees
            ]);
            break;

        case 'get_employee':
            // التحقق من صلاحية العرض
            if (!hasPermission('employees_view')) {
                throw new Exception('ليس لديك صلاحية لعرض الموظفين');
            }

            if (!isset($requestData['employee_id']) || empty($requestData['employee_id'])) {
                throw new Exception('لم يتم تحديد الموظف');
            }

            $employeeId = intval($requestData['employee_id']);
            $employee = $employeeModel->getEmployeeById($employeeId);

            if (!$employee) {
                throw new Exception('لم يتم العثور على الموظف');
            }

            echo json_encode([
                'status' => 'success',
                'employee' => $employee
            ]);
            break;

        case 'get_employee_services':
            // التحقق من صلاحية العرض
            if (!hasPermission('employees_view')) {
                throw new Exception('ليس لديك صلاحية لعرض الموظفين');
            }

            if (!isset($requestData['employee_id'])) {
                throw new Exception('لم يتم تحديد الموظف');
            }

            $employeeId = intval($requestData['employee_id']);
            $services = $employeeModel->getEmployeeServices($employeeId);

            echo json_encode([
                'status' => 'success',
                'services' => $services,
                'count' => count($services)
            ]);
            break;

        case 'add_employee':
            // التحقق من صلاحية الإضافة
            if (!hasPermission('employees_add')) {
                throw new Exception('ليس لديك صلاحية لإضافة موظفين');
            }

            // التحقق من البيانات المطلوبة
            $requiredFields = ['name', 'phone', 'position', 'salary_type'];
            foreach ($requiredFields as $field) {
                if (!isset($requestData[$field]) || empty($requestData[$field])) {
                    throw new Exception('يرجى إدخال جميع البيانات المطلوبة');
                }
            }

            // جمع بيانات الموظف
            $employeeData = [
                'name' => sanitizeInput($requestData['name']),
                'phone' => sanitizeInput($requestData['phone']),
                'email' => isset($requestData['email']) ? sanitizeInput($requestData['email']) : null,
                'position' => sanitizeInput($requestData['position']),
                'salary_type' => sanitizeInput($requestData['salary_type']),
                'fixed_salary' => isset($requestData['fixed_salary']) ? floatval($requestData['fixed_salary']) : 0,
                'commission_percentage' => isset($requestData['commission_percentage']) ? floatval($requestData['commission_percentage']) : 0,
                'branch_id' => isset($requestData['branch_id']) ? intval($requestData['branch_id']) : $_SESSION['user_branch_id'] ?? null,
                'is_active' => isset($requestData['is_active']) ? intval($requestData['is_active']) : 1,
                'create_user' => isset($requestData['create_user']) ? (bool)$requestData['create_user'] : false,
                'password' => isset($requestData['password']) ? $requestData['password'] : null
            ];

            // إضافة الخدمات إذا كانت موجودة
            if (isset($requestData['services']) && is_array($requestData['services'])) {
                $employeeData['services'] = array_map('intval', $requestData['services']);
            }

            // إضافة الموظف
            $employeeId = $employeeModel->addEmployee($employeeData);

            echo json_encode([
                'status' => 'success',
                'message' => 'تم إضافة الموظف بنجاح',
                'employee_id' => $employeeId,
                'generated_password' => $employeeData['create_user'] ? ($employeeData['generated_password'] ?? null) : null
            ]);
            break;

        case 'update_employee':
            // التحقق من صلاحية التعديل
            if (!hasPermission('employees_edit')) {
                throw new Exception('ليس لديك صلاحية لتعديل الموظفين');
            }

            // التحقق من البيانات المطلوبة
            if (!isset($requestData['employee_id']) || empty($requestData['employee_id'])) {
                throw new Exception('لم يتم تحديد الموظف');
            }

            $requiredFields = ['name', 'phone', 'position', 'salary_type'];
            foreach ($requiredFields as $field) {
                if (!isset($requestData[$field]) || empty($requestData[$field])) {
                    throw new Exception('يرجى إدخال جميع البيانات المطلوبة');
                }
            }

            $employeeId = intval($requestData['employee_id']);

            // جمع بيانات الموظف
            $employeeData = [
                'name' => sanitizeInput($requestData['name']),
                'phone' => sanitizeInput($requestData['phone']),
                'email' => isset($requestData['email']) ? sanitizeInput($requestData['email']) : null,
                'position' => sanitizeInput($requestData['position']),
                'salary_type' => sanitizeInput($requestData['salary_type']),
                'fixed_salary' => isset($requestData['fixed_salary']) ? floatval($requestData['fixed_salary']) : 0,
                'commission_percentage' => isset($requestData['commission_percentage']) ? floatval($requestData['commission_percentage']) : 0,
                'branch_id' => isset($requestData['branch_id']) ? intval($requestData['branch_id']) : null,
                'is_active' => isset($requestData['is_active']) ? intval($requestData['is_active']) : 1,
                'create_user' => isset($requestData['create_user']) ? (bool)$requestData['create_user'] : false,
                'password' => isset($requestData['password']) ? $requestData['password'] : null,
                'username' => isset($requestData['username']) ? sanitizeInput($requestData['username']) : null
            ];

            // إضافة الخدمات إذا كانت موجودة
            if (isset($requestData['services']) && is_array($requestData['services'])) {
                $employeeData['services'] = array_map('intval', $requestData['services']);
            }

            // تحديث الموظف
            $employeeModel->updateEmployee($employeeId, convertArabicToEnglishNumbers($employeeData));

            echo json_encode([
                'status' => 'success',
                'message' => 'تم تحديث بيانات الموظف بنجاح',
                'generated_password' => ($employeeData['create_user'] && isset($employeeData['generated_password'])) ? $employeeData['generated_password'] : null
            ]);
            break;

        case 'delete_employee':
            // التحقق من صلاحية الحذف
            if (!hasPermission('employees_delete')) {
                throw new Exception('ليس لديك صلاحية لحذف الموظفين');
            }

            // التحقق من البيانات المطلوبة
            if (!isset($requestData['employee_id']) || empty($requestData['employee_id'])) {
                throw new Exception('لم يتم تحديد الموظف');
            }

            $employeeId = intval($requestData['employee_id']);

            // حذف الموظف
            $employeeModel->deleteEmployee($employeeId);

            echo json_encode([
                'status' => 'success',
                'message' => 'تم حذف الموظف بنجاح'
            ]);
            break;

        case 'record_attendance':
            // التحقق من صلاحية التعديل
            if (!hasPermission('employees_edit')) {
                throw new Exception('ليس لديك صلاحية لتسجيل حضور الموظفين');
            }

            // التحقق من البيانات المطلوبة
            if (!isset($requestData['employee_id']) || empty($requestData['employee_id'])) {
                throw new Exception('لم يتم تحديد الموظف');
            }

            if (!isset($requestData['date']) || empty($requestData['date'])) {
                throw new Exception('لم يتم تحديد التاريخ');
            }

            $employeeId = intval($requestData['employee_id']);
            $date = sanitizeInput(convertArabicToEnglishNumbers($requestData['date']));

            // تنسيق التاريخ
            if (strpos($date, '/') !== false) {
                $date = formatDateToDb($date);
            }

            // جمع بيانات الحضور
            $attendanceData = [
                'employee_id' => $employeeId,
                'date' => $date,
                'check_in' => isset($requestData['check_in']) ? sanitizeInput($requestData['check_in']) : null,
                'check_out' => isset($requestData['check_out']) ? sanitizeInput($requestData['check_out']) : null,
                'notes' => isset($requestData['notes']) ? sanitizeInput($requestData['notes']) : null
            ];

            // تسجيل الحضور
            $attendanceId = $employeeModel->manageAttendance($attendanceData);

            echo json_encode([
                'status' => 'success',
                'message' => 'تم تسجيل حضور الموظف بنجاح',
                'attendance_id' => $attendanceId
            ]);
            break;

        case 'get_employee_attendance':
            // التحقق من صلاحية العرض
            if (!hasPermission('employees_view')) {
                throw new Exception('ليس لديك صلاحية لعرض سجلات حضور الموظفين');
            }

            // التحقق من البيانات المطلوبة
            if (!isset($requestData['employee_id']) || empty($requestData['employee_id'])) {
                throw new Exception('لم يتم تحديد الموظف');
            }

            $employeeId = intval($requestData['employee_id']);
            $filters = [];

            if (isset($requestData['month']) && isset($requestData['year'])) {
                $filters['month'] = intval($requestData['month']);
                $filters['year'] = intval($requestData['year']);
            } elseif (isset($requestData['start_date']) && isset($requestData['end_date'])) {
                $filters['start_date'] = sanitizeInput($requestData['start_date']);
                $filters['end_date'] = sanitizeInput($requestData['end_date']);

                // تنسيق التواريخ
                if (strpos($filters['start_date'], '/') !== false) {
                    $filters['start_date'] = formatDateToDb($filters['start_date']);
                }

                if (strpos($filters['end_date'], '/') !== false) {
                    $filters['end_date'] = formatDateToDb($filters['end_date']);
                }
            }

            // استرجاع سجلات الحضور
            $attendance = $employeeModel->getEmployeeAttendance($employeeId, $filters);

            echo json_encode([
                'status' => 'success',
                'request' => $filters,
                'attendance' => $attendance,
                'count' => count($attendance)
            ]);
            break;

        case 'calculate_salary':
            // التحقق من صلاحية الرواتب
            if (!hasPermission('employees_salaries')) {
                throw new Exception('ليس لديك صلاحية لحساب رواتب الموظفين');
            }

            // التحقق من البيانات المطلوبة
            if (!isset($requestData['employee_id']) || empty($requestData['employee_id'])) {
                throw new Exception('لم يتم تحديد الموظف');
            }

            if (!isset($requestData['month']) || !isset($requestData['year'])) {
                throw new Exception('لم يتم تحديد الشهر والسنة');
            }

            $employeeId = intval($requestData['employee_id']);
            $month = intval($requestData['month']);
            $year = intval($requestData['year']);
            $recalculate = isset($requestData['recalculate']) && ($requestData['recalculate'] === 'true' || $requestData['recalculate'] === true);
            $currentDay = isset($requestData['current_day']) ? intval($requestData['current_day']) : 0;

            // إذا كان إعادة حساب لراتب موجود
            if ($recalculate && isset($requestData['salary_id'])) {
                $salaryId = intval($requestData['salary_id']);

                // التحقق من وجود الراتب وأنه غير مدفوع
                $existingSalary = $employeeModel->getSalaryById($salaryId);

                if (!$existingSalary) {
                    throw new Exception('الراتب غير موجود');
                }

                if ($existingSalary['payment_status'] === 'paid') {
                    throw new Exception('لا يمكن إعادة حساب راتب مدفوع');
                }

                // حذف الراتب القديم وحساب راتب جديد
                $employeeModel->deleteSalary($salaryId);
            }

            // حساب الراتب
            $salaryData = $employeeModel->calculateEmployeeSalary($employeeId, $month, $year, $currentDay);

            echo json_encode([
                'status' => 'success',
                'salary' => $salaryData
            ]);
            break;

        case 'save_salary':
            // التحقق من صلاحية الرواتب
            if (!hasPermission('employees_salaries')) {
                throw new Exception('ليس لديك صلاحية لحفظ رواتب الموظفين');
            }

            // التحقق من البيانات المطلوبة
            $requiredFields = ['employee_id', 'month', 'year', 'fixed_amount', 'commission_amount', 'total_amount'];
            foreach ($requiredFields as $field) {
                if (!isset($requestData[$field])) {
                    throw new Exception('يرجى إدخال جميع البيانات المطلوبة');
                }
            }

            // جمع بيانات الراتب
            $salaryData = [
                'employee_id' => intval($requestData['employee_id']),
                'month' => intval($requestData['month']),
                'year' => intval($requestData['year']),
                'fixed_amount' => floatval($requestData['fixed_amount']),
                'commission_amount' => floatval($requestData['commission_amount']),
                'bonuses' => isset($requestData['bonuses']) ? floatval($requestData['bonuses']) : 0,
                'deductions' => isset($requestData['deductions']) ? floatval($requestData['deductions']) : 0,
                'total_amount' => floatval($requestData['total_amount']),
                'payment_date' => isset($requestData['payment_date']) ? sanitizeInput($requestData['payment_date']) : null,
                'payment_status' => isset($requestData['payment_status']) ? sanitizeInput($requestData['payment_status']) : 'unpaid',
                'notes' => isset($requestData['notes']) ? sanitizeInput($requestData['notes']) : null
            ];

            // إذا تم تحديد اليوم الحالي، نقوم بإعادة حساب الراتب بناءً على اليوم الحالي
            if (isset($requestData['current_day']) && intval($requestData['current_day']) > 0) {
                $currentDay = intval($requestData['current_day']);

                // إعادة حساب الراتب باستخدام اليوم الحالي
                $recalculatedSalary = $employeeModel->calculateEmployeeSalary(
                    $salaryData['employee_id'],
                    $salaryData['month'],
                    $salaryData['year'],
                    $currentDay
                );

                // تحديث قيمة الراتب الثابت بالقيمة المحسوبة حتى اليوم الحالي
                if ($recalculatedSalary && isset($recalculatedSalary['fixed_amount'])) {
                    $salaryData['fixed_amount'] = $recalculatedSalary['fixed_amount'];
                    $salaryData['total_amount'] = $salaryData['fixed_amount'] + $salaryData['commission_amount'] + $salaryData['bonuses'] - $salaryData['deductions'];

                    // تخزين عدد الأيام المحسوبة في حقل الملاحظات
                    $notesData = ['calculated_days' => $currentDay];
                    if (!empty($salaryData['notes'])) {
                        // محاولة دمج البيانات الحالية مع البيانات الجديدة
                        $existingNotes = json_decode($salaryData['notes'], true);
                        if (is_array($existingNotes)) {
                            $notesData = array_merge($existingNotes, $notesData);
                        }
                    }
                    $salaryData['notes'] = json_encode($notesData);
                }
            }

            // تنسيق تاريخ الدفع إذا كان موجودًا
            if (!empty($salaryData['payment_date']) && strpos($salaryData['payment_date'], '/') !== false) {
                $salaryData['payment_date'] = formatDateToDb($salaryData['payment_date']);
            }

            // حفظ الراتب
            $salaryId = $employeeModel->saveSalary($salaryData);

            echo json_encode([
                'status' => 'success',
                'message' => 'تم حفظ راتب الموظف بنجاح',
                'salary_id' => $salaryId
            ]);
            break;

        case 'get_employee_salary':
            // التحقق من صلاحية الرواتب
            if (!hasPermission('employees_salaries')) {
                throw new Exception('ليس لديك صلاحية لعرض رواتب الموظفين');
            }

            // التحقق من البيانات المطلوبة
            if (!isset($requestData['employee_id']) || empty($requestData['employee_id'])) {
                throw new Exception('لم يتم تحديد الموظف');
            }

            if (!isset($requestData['month']) || !isset($requestData['year'])) {
                throw new Exception('لم يتم تحديد الشهر والسنة');
            }

            $employeeId = intval($requestData['employee_id']);
            $month = intval($requestData['month']);
            $year = intval($requestData['year']);

            // استرجاع الراتب
            $salary = $employeeModel->getEmployeeSalary($employeeId, $month, $year);
            echo json_encode([
                'status' => 'success',
                'salary' => $salary,
                'message' => 'لا يوجد راتب محسوب لهذا الموظف في الشهر والسنة المحددين'
            ]);
            break;

        case 'get_employees_salaries':
            // التحقق من صلاحية الرواتب
            if (!hasPermission('employees_salaries')) {
                throw new Exception('ليس لديك صلاحية لعرض رواتب الموظفين');
            }

            // التحقق من البيانات المطلوبة
            if (!isset($requestData['year'])) {
                throw new Exception('لم يتم تحديد السنة');
            }

            $year = intval($requestData['year']);
            $branchId = isset($requestData['branch_id']) ? intval($requestData['branch_id']) : null;
            $employeeId = isset($requestData['employee_id']) ? intval($requestData['employee_id']) : null;

            // التحقق مما إذا كان المطلوب جميع الشهور أو شهر محدد
            $month = isset($requestData['month']) ? $requestData['month'] : null;
            $allMonths = ($month === 'all');

            if (!$allMonths && $month !== null) {
                $month = intval($month);
                convertArabicToEnglishNumbers($year);
                $year = intval($year);
            }

            // استرجاع الرواتب
            if ($allMonths) {
                // استرجاع الرواتب لجميع الشهور في السنة المحددة
                $salaries = $employeeModel->getEmployeesSalariesForYear($year, $branchId, $employeeId);
            } else {
                // استرجاع الرواتب للشهر المحدد
                $salaries = $employeeModel->getEmployeesSalaries($month, $year, $branchId, $employeeId);
            }

            echo json_encode([
                'status' => 'success',
                'salaries' => $salaries,
                'count' => count($salaries),
                'request' => [
                    'month' => $month,
                    'year' => $year,
                    'branch_id' => $branchId,
                    'employee_id' => $employeeId,
                    'all_months' => $allMonths
                ]
            ]);
            break;

        case 'update_salary_status':
            // التحقق من صلاحية الرواتب
            if (!hasPermission('employees_salaries')) {
                throw new Exception('ليس لديك صلاحية لتحديث حالة دفع رواتب الموظفين');
            }

            // التحقق من البيانات المطلوبة
            if (!isset($requestData['salary_id']) || empty($requestData['salary_id'])) {
                throw new Exception('لم يتم تحديد الراتب');
            }

            if (!isset($requestData['status']) || empty($requestData['status'])) {
                throw new Exception('لم يتم تحديد حالة الدفع');
            }

            $salaryId = intval($requestData['salary_id']);
            $status = sanitizeInput($requestData['status']);
            $paymentDate = isset($requestData['payment_date']) ? sanitizeInput($requestData['payment_date']) : null;

            // تنسيق تاريخ الدفع إذا كان موجودًا
            if (!empty($paymentDate) && strpos($paymentDate, '/') !== false) {
                $paymentDate = formatDateToDb($paymentDate);
            }

            // تحديث حالة الدفع
            $employeeModel->updateSalaryStatus($salaryId, $status, convertArabicToEnglishNumbers($paymentDate));

            echo json_encode([
                'status' => 'success',
                'message' => 'تم تحديث حالة دفع الراتب بنجاح'
            ]);
            break;

        case 'update_salary':
            // التحقق من صلاحية الرواتب
            if (!hasPermission('employees_salaries')) {
                throw new Exception('ليس لديك صلاحية لتعديل رواتب الموظفين');
            }

            // التحقق من البيانات المطلوبة
            if (!isset($requestData['salary_id']) || empty($requestData['salary_id'])) {
                throw new Exception('لم يتم تحديد الراتب');
            }

            // جمع بيانات الراتب
            $salaryData = [
                'id' => intval($requestData['salary_id']),
                'fixed_amount' => floatval($requestData['fixed_amount']),
                'commission_amount' => floatval($requestData['commission_amount']),
                'bonuses' => isset($requestData['bonuses']) ? floatval($requestData['bonuses']) : 0,
                'deductions' => isset($requestData['deductions']) ? floatval($requestData['deductions']) : 0,
                'total_amount' => floatval($requestData['total_amount']),
                'payment_status' => isset($requestData['payment_status']) ? sanitizeInput($requestData['payment_status']) : 'unpaid',
                'notes' => isset($requestData['notes']) ? sanitizeInput($requestData['notes']) : null
            ];

            // إضافة تاريخ الدفع إذا كانت الحالة مدفوعة
            if ($salaryData['payment_status'] === 'paid' && isset($requestData['payment_date'])) {
                $paymentDate = sanitizeInput($requestData['payment_date']);

                // تنسيق تاريخ الدفع إذا كان بتنسيق مختلف
                if (!empty($paymentDate) && strpos($paymentDate, '/') !== false) {
                    $paymentDate = formatDateToDb($paymentDate);
                }

                $salaryData['payment_date'] = convertArabicToEnglishNumbers($paymentDate);
            }

            // تحديث الراتب
            $employeeModel->updateSalary($salaryData);

            echo json_encode([
                'status' => 'success',
                'message' => 'تم تحديث بيانات الراتب بنجاح'
            ]);
            break;

            case 'get_salaries_summary':
                // التحقق من صلاحية الرواتب
                if (!hasPermission('employees_salaries')) {
                    throw new Exception('ليس لديك صلاحية لعرض ملخص الرواتب');
                }

                // التحقق من البيانات المطلوبة
                if (!isset($requestData['month']) || !isset($requestData['year'])) {
                    throw new Exception('لم يتم تحديد الشهر والسنة');
                }

                $month = intval($requestData['month']);
                $year = intval($requestData['year']);
                $branchId = isset($requestData['branch_id']) ? intval($requestData['branch_id']) : null;

                // الحصول على ملخص الرواتب للشهر الحالي
                $currentMonthSummary = $employeeModel->getSalariesSummary($month, $year, $branchId);

                // حساب الشهر السابق
                $previousMonth = $month - 1;
                $previousYear = $year;
                if ($previousMonth <= 0) {
                    $previousMonth = 12;
                    $previousYear--;
                }

                // الحصول على ملخص الرواتب للشهر السابق
                $previousMonthSummary = $employeeModel->getSalariesSummary($previousMonth, $previousYear, $branchId);

                // التحقق مما إذا كان اليوم هو يوم الدفع
                $isPayday = false;
                $paydaySettings = $settingsModel->getAll('salary_payday');

                if ($paydaySettings && isset($paydaySettings['day_of_month'])) {
                    $payday = intval($paydaySettings['day_of_month']);
                    $today = intval(date('d'));
                    $currentMonth = intval(date('m'));
                    $currentYear = intval(date('Y'));

                    // التحقق مما إذا كان اليوم هو يوم الدفع للشهر الحالي
                    $isPayday = ($today === $payday && $currentMonth === $month && $currentYear === $year);
                }

                echo json_encode([
                    'status' => 'success',
                    'current_month' => $currentMonthSummary,
                    'previous_month' => $previousMonthSummary,
                    'is_payday' => $isPayday,
                    'request' => [
                        'month' => $month,
                        'year' => $year,
                        'branch_id' => $branchId
                    ]
                ]);
            break;
        case 'get_employee_stats':
            // التحقق من صلاحية العرض
            if (!hasPermission('employees_view')) {
                throw new Exception('ليس لديك صلاحية لعرض إحصائيات الموظفين');
            }

            // التحقق من البيانات المطلوبة
            if (!isset($requestData['employee_id']) || empty($requestData['employee_id'])) {
                throw new Exception('لم يتم تحديد الموظف');
            }

            $employeeId = intval($requestData['employee_id']);
            $period = isset($requestData['period']) ? sanitizeInput($requestData['period']) : 'month';

            // استرجاع الإحصائيات
            $stats = $employeeModel->getEmployeeStats($employeeId, $period);

            echo json_encode([
                'status' => 'success',
                'stats' => $stats
            ]);
            break;

        case 'get_top_employees':
            // التحقق من صلاحية العرض
            if (!hasPermission('employees_view')) {
                throw new Exception('ليس لديك صلاحية لعرض الموظفين');
            }

            $limit = isset($requestData['limit']) ? intval($requestData['limit']) : 5;
            $period = isset($requestData['period']) ? sanitizeInput($requestData['period']) : 'month';
            $branchId = isset($requestData['branch_id']) ? intval($requestData['branch_id']) : null;

            // استرجاع أفضل الموظفين
            $topEmployees = $employeeModel->getTopEmployees($limit, $period, $branchId);

            echo json_encode([
                'status' => 'success',
                'employees' => $topEmployees
            ]);
            break;

        case 'get_attendance_report':
            // التحقق من صلاحية العرض
            if (!hasPermission('employees_view')) {
                throw new Exception('ليس لديك صلاحية لعرض تقارير حضور الموظفين');
            }

            // جلب بيانات الفلترة
            $filters = [];

            // التحقق من صحة الفلاتر
            if (!empty($requestData['month']) && !empty($requestData['year'])) {
                // استخدام الشهر والسنة مباشرة في الاستعلام
                $month = intval($requestData['month']);
                $year = intval($requestData['year']);

                // التحقق من صحة الشهر والسنة
                if ($month < 1 || $month > 12 || $year < 2000 || $year > 2100) {
                    throw new Exception('الشهر أو السنة غير صالحين');
                }

                // إضافة الشهر والسنة مباشرة إلى الفلاتر
                $filters['month'] = $month;
                $filters['year'] = $year;

                // حساب نطاق التاريخ للعرض فقط (لا يستخدم في الاستعلام)
                $startDate = sprintf('%04d-%02d-01', $year, $month);
                $endDate = date('Y-m-t', strtotime($startDate));
                $filters['display_start_date'] = $startDate;
                $filters['display_end_date'] = $endDate;
            } else if (!empty($requestData['start_date']) && !empty($requestData['end_date'])) {
                // استخدام نطاق التاريخ المحدد
                $filters['start_date'] = sanitizeInput($requestData['start_date']);
                $filters['end_date'] = sanitizeInput($requestData['end_date']);

                // نسخ نفس القيم للعرض
                $filters['display_start_date'] = $filters['start_date'];
                $filters['display_end_date'] = $filters['end_date'];
            } else {
                // إذا لم يتم تحديد نطاق تاريخ، استخدم الشهر الحالي
                $currentMonth = date('n'); // الشهر الحالي
                $currentYear = date('Y');  // السنة الحالية

                $filters['month'] = $currentMonth;
                $filters['year'] = $currentYear;

                // حساب نطاق التاريخ للعرض فقط
                $startDate = date('Y-m-01'); // أول يوم في الشهر الحالي
                $endDate = date('Y-m-t');   // آخر يوم في الشهر الحالي
                $filters['display_start_date'] = $startDate;
                $filters['display_end_date'] = $endDate;
            }

            // إضافة فلتر الفرع إذا كان موجوداً
            if (!empty($requestData['branch_id'])) {
                $filters['branch_id'] = intval($requestData['branch_id']);
            }

            // إضافة فلتر الموظف إذا كان موجوداً
            if (!empty($requestData['employee_id'])) {
                $filters['employee_id'] = intval($requestData['employee_id']);
            }

            // جلب تقرير الحضور
            $report = $employeeModel->getAttendanceReport($filters);

            // معالجة البيانات لتكون ملائمة لصفحة التقارير
            $processedData = processAttendanceReportData($report, $filters);

            echo json_encode([
                'status' => 'success',
                'message' => 'تم جلب تقرير الحضور بنجاح',
                'data' => $processedData,
                'filters' => $filters
            ]);
            break;

        case 'get_employees_by_service':
            // التحقق من صلاحية العرض
            if (!hasPermission('employees_view')) {
                throw new Exception('ليس لديك صلاحية لعرض الموظفين');
            }

            // التحقق من البيانات المطلوبة
            if (!isset($requestData['service_id']) || empty($requestData['service_id'])) {
                throw new Exception('لم يتم تحديد الخدمة');
            }

            $serviceId = intval($requestData['service_id']);
            $branchId = isset($requestData['branch_id']) ? intval($requestData['branch_id']) : null;

            // استرجاع الموظفين حسب الخدمة
            $employees = $employeeModel->getEmployeesByService($serviceId, $branchId);

            echo json_encode([
                'status' => 'success',
                'employees' => $employees,
                'count' => count($employees)
            ]);
            break;

        case 'get_all_services':
            // التحقق من صلاحية العرض
            if (!hasPermission('employees_view') || !hasPermission('services_view')) {
                throw new Exception('ليس لديك صلاحية لعرض الخدمات');
            }

            $branchId = isset($requestData['branch_id']) ? intval($requestData['branch_id']) : null;

            // استرجاع جميع الخدمات
            $services = $serviceModel->getServices(['branch_id' => $branchId, 'is_active' => 1]);

            echo json_encode([
                'status' => 'success',
                'services' => $services,
                'count' => count($services)
            ]);
            break;

        case 'get_salary_by_id':
                // التحقق من صلاحية الرواتب
                if (!hasPermission('employees_salaries')) {
                    throw new Exception('ليس لديك صلاحية لعرض رواتب الموظفين');
                }

                // التحقق من البيانات المطلوبة
                if (!isset($requestData['salary_id']) || empty($requestData['salary_id'])) {
                    throw new Exception('لم يتم تحديد الراتب');
                }

                $salaryId = intval($requestData['salary_id']);

                // استرجاع الراتب
                $salary = $employeeModel->getSalaryById($salaryId);

                echo json_encode([
                    'status' => 'success',
                    'salary' => $salary
                ]);
            break;

        case 'get_branch_employees':
            // التحقق من صلاحية العرض
            if (!hasPermission('employees_view')) {
                throw new Exception('ليس لديك صلاحية لعرض الموظفين');
            }

            // التحقق من وجود معرف الفرع
            if (!isset($requestData['branch_id']) || empty($requestData['branch_id'])) {
                throw new Exception('لم يتم تحديد الفرع');
            }

            $branchId = intval($requestData['branch_id']);

            // إعداد الفلاتر
            $filters = [
                'branch_id' => $branchId,
                'is_active' => isset($requestData['is_active']) ? intval($requestData['is_active']) : 1
            ];

            // إضافة فلتر الخدمة إذا كان موجودًا
            if (isset($requestData['service_id']) && !empty($requestData['service_id'])) {
                $filters['service_id'] = intval($requestData['service_id']);
            }

            // استرجاع الموظفين حسب الفرع
            $employees = $employeeModel->getEmployees($filters);

            echo json_encode([
                'status' => 'success',
                'employees' => $employees,
                'count' => count($employees)
            ]);
            break;

        default:
            throw new Exception('الإجراء المطلوب غير صالح');
    }
} catch (Exception $e) {
    echo json_encode([
        'status' => 'error',
        'message' => $e->getMessage()
    ]);
}

/**
 * حساب عدد أيام العمل الفعلية في الشهر بعد خصم الإجازات
 * @param int $month الشهر (1-12)
 * @param int $year السنة
 * @return int عدد أيام العمل الفعلية
 */
function calculateWorkingDays($month, $year) {
    // حساب عدد أيام الشهر
    $totalDays = cal_days_in_month(CAL_GREGORIAN, $month, $year);

    // عدد أيام العطلة الأسبوعية (الجمعة والسبت مثلاً)
    $weekendDays = 0;

    // حساب عدد أيام العطلة الأسبوعية في الشهر
    for ($day = 1; $day <= $totalDays; $day++) {
        $date = sprintf('%04d-%02d-%02d', $year, $month, $day);
        $dayOfWeek = date('N', strtotime($date)); // 1 (الاثنين) إلى 7 (الأحد)

        // اعتبار الجمعة (5) والسبت (6) أيام عطلة
        if ($dayOfWeek == 5 || $dayOfWeek == 6) {
            $weekendDays++;
        }
    }

    // عدد أيام الإجازات الرسمية في الشهر
    // يمكن إضافة منطق لاسترجاع الإجازات الرسمية من قاعدة البيانات
    $holidays = 0; // للتبسيط، نفترض أنه لا توجد إجازات رسمية

    // حساب أيام العمل الفعلية
    $workingDays = $totalDays - $weekendDays - $holidays;

    return $workingDays;
}

/**
 * معالجة بيانات تقرير الحضور لتكون ملائمة لصفحة التقارير
 * @param array $report بيانات التقرير الأصلية
 * @param array $filters فلاتر التقرير (الشهر، السنة، الفرع)
 * @return array بيانات التقرير المعالجة
 */
function processAttendanceReportData($report, $filters = []) {
    // التحقق من وجود البيانات
    if (!$report || !is_array($report)) {
        return [
            'records' => [],
            'stats' => [
                'total_records' => 0,
                'total_hours' => 0,
                'employees_summary' => []
            ],
            'summary' => [
                'total_employees' => 0,
                'total_days' => 0,
                'attendance_rate' => 0,
                'average_hours' => 0
            ]
        ];
    }

    // التحقق من وجود السجلات الصالحة
    $validRecords = [];
    $allRecords = [];

    // استخدام السجلات الصالحة إذا كانت موجودة
    if (isset($report['valid_records']) && is_array($report['valid_records'])) {
        $validRecords = $report['valid_records'];
    } else if (isset($report['records']) && is_array($report['records'])) {
        // فلترة السجلات الصالحة من جميع السجلات
        $validRecords = array_filter($report['records'], function($record) {
            return isset($record['is_valid']) ? $record['is_valid'] : true;
        });
    }

    // استخدام جميع السجلات إذا كانت موجودة
    if (isset($report['records']) && is_array($report['records'])) {
        $allRecords = $report['records'];
    }

    // استخراج نطاق التاريخ من الفلاتر للعرض فقط
    $startDate = null;
    $endDate = null;
    $totalDays = 0;

    // استخدام نطاق التاريخ للعرض إذا كان متوفراً
    if (!empty($filters['display_start_date']) && !empty($filters['display_end_date'])) {
        $startDate = $filters['display_start_date'];
        $endDate = $filters['display_end_date'];

        // حساب عدد الأيام بين التاريخين
        $start = new DateTime($startDate);
        $end = new DateTime($endDate);
        $interval = $start->diff($end);
        $totalDays = $interval->days + 1; // إضافة 1 لشمول اليوم الأخير
    }
    // استخدام الشهر والسنة لحساب نطاق التاريخ
    else if (!empty($filters['month']) && !empty($filters['year'])) {
        $month = intval($filters['month']);
        $year = intval($filters['year']);

        $startDate = sprintf('%04d-%02d-01', $year, $month);
        $endDate = date('Y-m-t', strtotime($startDate));

        // حساب عدد أيام الشهر
        $totalDays = date('t', strtotime($startDate));
    }
    // استخدام نطاق التاريخ المحدد
    else if (!empty($filters['start_date']) && !empty($filters['end_date'])) {
        $startDate = $filters['start_date'];
        $endDate = $filters['end_date'];

        // حساب عدد الأيام بين التاريخين
        $start = new DateTime($startDate);
        $end = new DateTime($endDate);
        $interval = $start->diff($end);
        $totalDays = $interval->days + 1; // إضافة 1 لشمول اليوم الأخير
    }
    // استخدام الشهر الحالي كقيمة افتراضية
    else {
        $startDate = date('Y-m-01');
        $endDate = date('Y-m-t');
        $totalDays = date('t');
    }

    // طباعة نطاق التاريخ للتحقق
    error_log("نطاق التاريخ للتقرير: $startDate إلى $endDate (إجمالي الأيام: $totalDays)");

    // طباعة الفلاتر للتحقق
    error_log("الفلاتر المستخدمة في processAttendanceReportData: " . json_encode($filters));

    // طباعة بيانات التقرير للتحقق
    error_log("بيانات التقرير الأصلية: " . json_encode([
        'records_count' => isset($report['records']) ? count($report['records']) : 0,
        'valid_records_count' => isset($report['valid_records']) ? count($report['valid_records']) : 0,
        'has_stats' => isset($report['stats']),
        'has_summary' => isset($report['summary'])
    ]));

    // إعداد بيانات الموظفين للجدول
    $employeesData = [];
    $employeesMap = [];

    // الحصول على قائمة الموظفين النشطين في الفترة المحددة
    $activeEmployees = [];
    $branchFilter = !empty($filters['branch_id']) ? intval($filters['branch_id']) : null;

    // استخراج الموظفين الفريدين من السجلات
    foreach ($validRecords as $record) {
        $employeeId = $record['employee_id'];
        $employeeName = $record['employee_name'] ?? '';
        $branchName = $record['branch_name'] ?? '';
        $recordBranchId = isset($record['branch_id']) ? intval($record['branch_id']) : null;

        // التحقق من فلتر الفرع إذا كان موجوداً
        if ($branchFilter !== null && $recordBranchId !== null && $recordBranchId != $branchFilter) {
            continue; // تخطي السجلات من فروع أخرى
        }

        // إضافة الموظف إلى قائمة الموظفين النشطين
        if (!isset($activeEmployees[$employeeId])) {
            $activeEmployees[$employeeId] = [
                'id' => $employeeId,
                'name' => $employeeName,
                'branch_name' => $branchName,
                'branch_id' => $recordBranchId
            ];
        }
    }

    // إنشاء قائمة التواريخ الفريدة في النطاق
    $uniqueDates = [];
    $currentDate = new DateTime($startDate);
    $lastDate = new DateTime($endDate);

    while ($currentDate <= $lastDate) {
        $dateStr = $currentDate->format('Y-m-d');
        $uniqueDates[$dateStr] = true;
        $currentDate->modify('+1 day');
    }

    // إنشاء خريطة لتتبع أيام حضور كل موظف
    $attendanceDays = [];

    // تجميع بيانات الموظفين من السجلات الصالحة
    foreach ($validRecords as $record) {
        $employeeId = $record['employee_id'];
        $date = $record['date'];
        $recordBranchId = isset($record['branch_id']) ? intval($record['branch_id']) : null;

        // التحقق من فلتر الفرع إذا كان موجوداً
        if ($branchFilter !== null && $recordBranchId !== null && $recordBranchId != $branchFilter) {
            continue; // تخطي السجلات من فروع أخرى
        }

        // التحقق من أن التاريخ ضمن النطاق
        if ($date >= $startDate && $date <= $endDate) {
            // تسجيل يوم الحضور لهذا الموظف
            if (!isset($attendanceDays[$employeeId])) {
                $attendanceDays[$employeeId] = [];
            }
            $attendanceDays[$employeeId][$date] = true;

            // إنشاء أو تحديث بيانات الموظف
            if (!isset($employeesMap[$employeeId])) {
                $employeesMap[$employeeId] = [
                    'id' => $employeeId,
                    'name' => $record['employee_name'] ?? '',
                    'branch_name' => $record['branch_name'] ?? '',
                    'branch_id' => $recordBranchId,
                    'present_days' => 0,
                    'absent_days' => 0,
                    'working_hours' => 0
                ];
            }

            // زيادة عدد أيام الحضور وساعات العمل
            $employeesMap[$employeeId]['present_days']++;
            $employeesMap[$employeeId]['working_hours'] += floatval($record['hours_worked'] ?? 0);
        }
    }

    // حساب أيام الغياب لكل موظف
    foreach ($activeEmployees as $employeeId => $employee) {
        if (!isset($employeesMap[$employeeId])) {
            // إذا لم يكن للموظف أي سجلات حضور صالحة
            $employeesMap[$employeeId] = [
                'id' => $employeeId,
                'name' => $employee['name'],
                'branch_name' => $employee['branch_name'],
                'present_days' => 0,
                'absent_days' => $totalDays,
                'working_hours' => 0
            ];
        } else {
            // حساب أيام الغياب بناءً على إجمالي الأيام وأيام الحضور
            $presentDays = count($attendanceDays[$employeeId] ?? []);
            $absentDays = $totalDays - $presentDays;
            $employeesMap[$employeeId]['absent_days'] = max(0, $absentDays);
        }
    }

    // تحويل الخريطة إلى مصفوفة
    $employeesData = array_values($employeesMap);

    // إعداد بيانات الحضور اليومي
    $dailyAttendanceData = [];
    $uniqueDates = [];

    // استخراج التواريخ الفريدة من السجلات الصالحة
    foreach ($validRecords as $record) {
        if (!empty($record['date']) && $record['date'] != '0000-00-00') {
            $uniqueDates[$record['date']] = true;
        }
    }

    // إنشاء بيانات الحضور اليومي
    foreach (array_keys($uniqueDates) as $date) {
        // فلترة السجلات لهذا اليوم مع مراعاة فلتر الفرع
        $recordsForDate = array_filter($validRecords, function($record) use ($date, $branchFilter) {
            // التحقق من تطابق التاريخ
            if ($record['date'] !== $date) {
                return false;
            }

            // التحقق من فلتر الفرع إذا كان موجوداً
            if ($branchFilter !== null) {
                $recordBranchId = isset($record['branch_id']) ? intval($record['branch_id']) : null;
                if ($recordBranchId !== null && $recordBranchId != $branchFilter) {
                    return false;
                }
            }

            return true;
        });

        $dailyAttendanceData[] = [
            'date' => $date,
            'attendance_rate' => 100, // نفترض أن الحضور كامل
            'records_count' => count($recordsForDate)
        ];
    }

    // إعداد الإحصائيات
    $stats = [
        'total_records' => count($validRecords),
        'total_hours' => array_sum(array_column($validRecords, 'hours_worked')),
        'employees_summary' => array_map(function($emp) {
            return [
                'employee_id' => $emp['id'],
                'employee_name' => $emp['name'],
                'days_present' => $emp['present_days'],
                'total_hours' => $emp['working_hours']
            ];
        }, $employeesData)
    ];

    // حساب أيام العمل الفعلية في الشهر
    $workingDays = 0;
    if (!empty($filters['month']) && !empty($filters['year'])) {
        $workingDays = calculateWorkingDays(intval($filters['month']), intval($filters['year']));
    } else if (!empty($filters['display_start_date']) && !empty($filters['display_end_date'])) {
        // إذا كان لدينا نطاق تاريخ، نستخرج الشهر والسنة من تاريخ البداية
        $startDate = new DateTime($filters['display_start_date']);
        $workingDays = calculateWorkingDays(intval($startDate->format('m')), intval($startDate->format('Y')));
    } else {
        // استخدام الشهر الحالي كقيمة افتراضية
        $workingDays = calculateWorkingDays(intval(date('m')), intval(date('Y')));
    }

    // إعداد الملخص
    $summary = [
        'total_employees' => count($employeesData),
        'total_days' => count($uniqueDates),
        'working_days' => $workingDays, // إضافة أيام العمل الفعلية
        'attendance_rate' => count($employeesData) > 0 ? 100 : 0, // نفترض أن الحضور كامل
        'average_hours' => count($validRecords) > 0 ? round(array_sum(array_column($validRecords, 'hours_worked')) / count($validRecords), 2) : 0
    ];

    // إرجاع البيانات المعالجة
    return [
        'records' => $allRecords,
        'valid_records' => $validRecords,
        'employees' => $employeesData,
        'daily_attendance' => $dailyAttendanceData,
        'stats' => $stats,
        'summary' => $summary
    ];
}
