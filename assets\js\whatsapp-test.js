/**
 * اختبار اتصال WhatsApp
 * يتعامل هذا الملف مع اختبار الاتصال بخادم WhatsApp المحلي وإرسال رسائل اختبار
 */

const WhatsAppTest = {
    // عنوان الخادم المحلي
    serverUrl: 'http://localhost:3000',

    // سجل الأحداث
    eventLog: [],

    // حالة آخر عملية
    lastOperationStatus: null,

    // خيارات التكوين
    options: {
        debug: true,                // تمكين رسائل التصحيح
        maxRetries: 2,              // الحد الأقصى لعدد إعادة المحاولات
        retryDelay: 2000,           // الوقت بين إعادة المحاولات (بالمللي ثانية)
        showSuccessMessage: true    // عرض رسالة نجاح للمستخدم
    },

    /**
     * تهيئة الوحدة
     * @param {Object} options خيارات التكوين
     */
    init: function(options) {
        // دمج الخيارات المقدمة مع الخيارات الافتراضية
        if (options) {
            this.options = { ...this.options, ...options };
        }

        if (this.options.debug) {
            console.log('تم تهيئة وحدة اختبار WhatsApp');
        }

        // تحديث عنوان الخادم المحلي في واجهة المستخدم
        this._updateServerUrl();

        // تسجيل الأحداث
        this._registerEvents();

        // إضافة حدث بدء التشغيل إلى السجل
        this.logEvent('تم تحميل صفحة اختبار WhatsApp');

        // اختبار الاتصال تلقائيًا عند تحميل الصفحة
        setTimeout(() => {
            this.testConnection();
        }, 500);
    },

    /**
     * تحديث عنوان الخادم المحلي في واجهة المستخدم
     * @private
     */
    _updateServerUrl: function() {
        const serverUrlElement = document.getElementById('serverUrl');
        if (serverUrlElement) {
            serverUrlElement.textContent = this.serverUrl;
        }
    },

    /**
     * تسجيل الأحداث للتفاعل مع واجهة المستخدم
     * @private
     */
    _registerEvents: function() {
        // زر اختبار الاتصال
        const testConnectionBtn = document.getElementById('testConnectionBtn');
        if (testConnectionBtn) {
            testConnectionBtn.addEventListener('click', (e) => {
                e.preventDefault();
                this.testConnection();
            });
        }

        // نموذج إرسال رسالة اختبار
        const testMessageForm = document.getElementById('testMessageForm');
        if (testMessageForm) {
            testMessageForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.sendTestMessage();
            });
        }
    },

    /**
     * إضافة حدث إلى سجل الأحداث
     * @param {string} message نص الحدث
     * @param {string} type نوع الحدث (info, success, error)
     */
    logEvent: function(message, type = 'info') {
        const timestamp = new Date().toLocaleTimeString();
        const event = {
            timestamp,
            message,
            type
        };

        // إضافة الحدث إلى السجل
        this.eventLog.unshift(event);

        // تحديث واجهة المستخدم
        const eventLogElement = document.getElementById('eventLog');
        if (eventLogElement) {
            const eventClass = type === 'error' ? 'text-danger' :
                              (type === 'success' ? 'text-success' : 'text-info');

            const eventHtml = `
                <div class="mb-1 ${eventClass} small">
                    <strong>[${timestamp}]</strong> ${message}
                </div>
            `;

            eventLogElement.innerHTML = eventHtml + eventLogElement.innerHTML;
        }

        if (this.options.debug) {
            console.log(`[${type.toUpperCase()}] ${message}`);
        }
    },

    /**
     * اختبار الاتصال بالخادم المحلي
     * @returns {Promise<boolean>} نتيجة الاختبار
     */
    testConnection: function() {
        return new Promise((resolve, reject) => {
            // تغيير حالة الزر
            const testConnectionBtn = document.getElementById('testConnectionBtn');
            if (testConnectionBtn) {
                testConnectionBtn.disabled = true;
                testConnectionBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الاختبار...';
            }

            // إعادة تعيين نتيجة الاختبار
            const resultElement = document.getElementById('connectionResult');
            if (resultElement) {
                resultElement.style.display = 'none';
            }

            // إضافة حدث إلى السجل
            this.logEvent('بدء اختبار الاتصال بالخادم المحلي');

            try {
                // إرسال طلب إلى الخادم المحلي
                const xhr = new XMLHttpRequest();
                xhr.open('GET', `${this.serverUrl}/status`, true);
                xhr.timeout = 5000; // 5 ثوانٍ كحد أقصى
                xhr.responseType = 'json';

                xhr.onload = () => {
                    if (xhr.status === 200) {
                        const response = xhr.response;
                        if (response && response.status === 'success') {
                            const isLoggedIn = response.isLoggedIn;

                            if (resultElement) {
                                resultElement.innerHTML = `
                                    <div class="alert alert-success">
                                        <i class="fas fa-check-circle"></i> تم الاتصال بالخادم المحلي بنجاح
                                        ${isLoggedIn ? '<br>WhatsApp Web: <span class="badge bg-success">مسجل الدخول</span>' : '<br>WhatsApp Web: <span class="badge bg-warning">غير مسجل الدخول</span>'}
                                    </div>
                                `;
                                resultElement.style.display = 'block';
                            }

                            // تحديث حالة الاتصال
                            const connectionStatusElement = document.getElementById('connectionStatus');
                            if (connectionStatusElement) {
                                connectionStatusElement.innerHTML = isLoggedIn 
                                    ? '<span class="badge bg-success">متصل</span>'
                                    : '<span class="badge bg-warning">متصل جزئيًا</span>';
                            }

                            // إضافة حدث إلى السجل
                            this.logEvent(`تم الاتصال بالخادم المحلي بنجاح. WhatsApp Web: ${isLoggedIn ? 'مسجل الدخول' : 'غير مسجل الدخول'}`, 'success');

                            resolve(true);
                        } else {
                            if (resultElement) {
                                resultElement.innerHTML = `
                                    <div class="alert alert-warning">
                                        <i class="fas fa-exclamation-triangle"></i> الخادم المحلي متاح ولكن هناك خطأ في الاستجابة
                                    </div>
                                `;
                                resultElement.style.display = 'block';
                            }

                            // تحديث حالة الاتصال
                            const connectionStatusElement = document.getElementById('connectionStatus');
                            if (connectionStatusElement) {
                                connectionStatusElement.innerHTML = '<span class="badge bg-warning">متصل جزئيًا</span>';
                            }

                            // إضافة حدث إلى السجل
                            this.logEvent('الخادم المحلي متاح ولكن هناك خطأ في الاستجابة', 'error');

                            resolve(false);
                        }
                    } else {
                        if (resultElement) {
                            resultElement.innerHTML = `
                                <div class="alert alert-danger">
                                    <i class="fas fa-times-circle"></i> فشل الاتصال بالخادم المحلي: خطأ في الاستجابة (${xhr.status})
                                </div>
                            `;
                            resultElement.style.display = 'block';
                        }

                        // تحديث حالة الاتصال
                        const connectionStatusElement = document.getElementById('connectionStatus');
                        if (connectionStatusElement) {
                            connectionStatusElement.innerHTML = '<span class="badge bg-danger">غير متصل</span>';
                        }

                        // إضافة حدث إلى السجل
                        this.logEvent(`فشل الاتصال بالخادم المحلي: خطأ في الاستجابة (${xhr.status})`, 'error');

                        reject(new Error(`خطأ في الاستجابة (${xhr.status})`));
                    }
                };

                xhr.ontimeout = () => {
                    if (resultElement) {
                        resultElement.innerHTML = `
                            <div class="alert alert-danger">
                                <i class="fas fa-times-circle"></i> فشل الاتصال بالخادم المحلي: انتهت مهلة الاتصال
                            </div>
                        `;
                        resultElement.style.display = 'block';
                    }

                    // تحديث حالة الاتصال
                    const connectionStatusElement = document.getElementById('connectionStatus');
                    if (connectionStatusElement) {
                        connectionStatusElement.innerHTML = '<span class="badge bg-danger">غير متصل</span>';
                    }

                    // إضافة حدث إلى السجل
                    this.logEvent('فشل الاتصال بالخادم المحلي: انتهت مهلة الاتصال', 'error');

                    reject(new Error('انتهت مهلة الاتصال'));
                };

                xhr.onerror = () => {
                    if (resultElement) {
                        resultElement.innerHTML = `
                            <div class="alert alert-danger">
                                <i class="fas fa-times-circle"></i> فشل الاتصال بالخادم المحلي: تأكد من تشغيل الخادم المحلي
                            </div>
                        `;
                        resultElement.style.display = 'block';
                    }

                    // تحديث حالة الاتصال
                    const connectionStatusElement = document.getElementById('connectionStatus');
                    if (connectionStatusElement) {
                        connectionStatusElement.innerHTML = '<span class="badge bg-danger">غير متصل</span>';
                    }

                    // إضافة حدث إلى السجل
                    this.logEvent('فشل الاتصال بالخادم المحلي: تأكد من تشغيل الخادم المحلي', 'error');

                    reject(new Error('تأكد من تشغيل الخادم المحلي'));
                };

                xhr.send();
            } catch (error) {
                if (resultElement) {
                    resultElement.innerHTML = `
                        <div class="alert alert-danger">
                            <i class="fas fa-times-circle"></i> فشل الاتصال بالخادم المحلي: ${error.message}
                        </div>
                    `;
                    resultElement.style.display = 'block';
                }

                // تحديث حالة الاتصال
                const connectionStatusElement = document.getElementById('connectionStatus');
                if (connectionStatusElement) {
                    connectionStatusElement.innerHTML = '<span class="badge bg-danger">غير متصل</span>';
                }

                // إضافة حدث إلى السجل
                this.logEvent(`فشل الاتصال بالخادم المحلي: ${error.message}`, 'error');

                reject(error);
            } finally {
                // إعادة تفعيل الزر
                if (testConnectionBtn) {
                    testConnectionBtn.disabled = false;
                    testConnectionBtn.innerHTML = '<i class="fas fa-plug"></i> اختبار الاتصال';
                }
            }
        });
    },

    /**
     * إرسال رسالة اختبار
     * @returns {Promise<Object>} نتيجة الإرسال
     */
    sendTestMessage: function() {
        return new Promise((resolve, reject) => {
            // الحصول على بيانات النموذج
            const phoneNumber = document.getElementById('phoneNumber').value.trim();
            const message = document.getElementById('message').value.trim();

            // التحقق من صحة البيانات
            if (!phoneNumber || !message) {
                const resultElement = document.getElementById('messageResult');
                if (resultElement) {
                    resultElement.innerHTML = `
                        <div class="alert alert-danger">
                            <i class="fas fa-times-circle"></i> يرجى ملء جميع الحقول المطلوبة
                        </div>
                    `;
                    resultElement.style.display = 'block';
                }

                reject(new Error('يرجى ملء جميع الحقول المطلوبة'));
                return;
            }

            // تغيير حالة الزر
            const submitBtn = document.querySelector('#testMessageForm button[type="submit"]');
            if (submitBtn) {
                submitBtn.disabled = true;
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الإرسال...';
            }

            // إعادة تعيين نتيجة الإرسال
            const resultElement = document.getElementById('messageResult');
            if (resultElement) {
                resultElement.style.display = 'none';
            }

            // إضافة حدث إلى السجل
            this.logEvent(`بدء إرسال رسالة اختبار إلى ${phoneNumber}`);

            try {
                // تنسيق رقم الهاتف
                const formattedPhone = this._formatPhoneNumber(phoneNumber);

                // إرسال طلب إلى الخادم المحلي
                const xhr = new XMLHttpRequest();
                xhr.open('POST', `${this.serverUrl}/send`, true);
                xhr.setRequestHeader('Content-Type', 'application/json');
                xhr.responseType = 'json';
                xhr.timeout = 30000; // 30 ثانية كحد أقصى

                xhr.onload = () => {
                    if (xhr.status === 200) {
                        const response = xhr.response;
                        if (response && response.status === 'success') {
                            if (resultElement) {
                                resultElement.innerHTML = `
                                    <div class="alert alert-success">
                                        <i class="fas fa-check-circle"></i> تم إرسال الرسالة بنجاح
                                    </div>
                                `;
                                resultElement.style.display = 'block';
                            }

                            // إضافة حدث إلى السجل
                            this.logEvent(`تم إرسال الرسالة بنجاح إلى ${formattedPhone}`, 'success');

                            resolve(response);
                        } else {
                            if (resultElement) {
                                resultElement.innerHTML = `
                                    <div class="alert alert-danger">
                                        <i class="fas fa-times-circle"></i> فشل إرسال الرسالة: ${response ? response.message : 'خطأ غير معروف'}
                                    </div>
                                `;
                                resultElement.style.display = 'block';
                            }

                            // إضافة حدث إلى السجل
                            this.logEvent(`فشل إرسال الرسالة إلى ${formattedPhone}: ${response ? response.message : 'خطأ غير معروف'}`, 'error');

                            reject(new Error(response ? response.message : 'خطأ غير معروف'));
                        }
                    } else {
                        if (resultElement) {
                            resultElement.innerHTML = `
                                <div class="alert alert-danger">
                                    <i class="fas fa-times-circle"></i> فشل إرسال الرسالة: خطأ في الاستجابة (${xhr.status})
                                </div>
                            `;
                            resultElement.style.display = 'block';
                        }

                        // إضافة حدث إلى السجل
                        this.logEvent(`فشل إرسال الرسالة إلى ${formattedPhone}: خطأ في الاستجابة (${xhr.status})`, 'error');

                        reject(new Error(`خطأ في الاستجابة (${xhr.status})`));
                    }
                };

                xhr.ontimeout = () => {
                    if (resultElement) {
                        resultElement.innerHTML = `
                            <div class="alert alert-danger">
                                <i class="fas fa-times-circle"></i> فشل إرسال الرسالة: انتهت مهلة الاتصال
                            </div>
                        `;
                        resultElement.style.display = 'block';
                    }

                    // إضافة حدث إلى السجل
                    this.logEvent(`فشل إرسال الرسالة إلى ${formattedPhone}: انتهت مهلة الاتصال`, 'error');

                    reject(new Error('انتهت مهلة الاتصال'));
                };

                xhr.onerror = () => {
                    if (resultElement) {
                        resultElement.innerHTML = `
                            <div class="alert alert-danger">
                                <i class="fas fa-times-circle"></i> فشل إرسال الرسالة: تأكد من تشغيل الخادم المحلي
                            </div>
                        `;
                        resultElement.style.display = 'block';
                    }

                    // إضافة حدث إلى السجل
                    this.logEvent(`فشل إرسال الرسالة إلى ${formattedPhone}: تأكد من تشغيل الخادم المحلي`, 'error');

                    reject(new Error('تأكد من تشغيل الخادم المحلي'));
                };

                // إرسال البيانات
                const sendData = {
                    phone: formattedPhone,
                    message: message
                };

                if (this.options.debug) {
                    console.log('إرسال بيانات إلى الخادم:', sendData);
                }

                xhr.send(JSON.stringify(sendData));
            } catch (error) {
                if (resultElement) {
                    resultElement.innerHTML = `
                        <div class="alert alert-danger">
                            <i class="fas fa-times-circle"></i> فشل إرسال الرسالة: ${error.message}
                        </div>
                    `;
                    resultElement.style.display = 'block';
                }

                // إضافة حدث إلى السجل
                this.logEvent(`فشل إرسال الرسالة: ${error.message}`, 'error');

                reject(error);
            } finally {
                // إعادة تفعيل الزر
                if (submitBtn) {
                    submitBtn.disabled = false;
                    submitBtn.innerHTML = '<i class="fab fa-whatsapp"></i> إرسال رسالة اختبار';
                }
            }
        });
    },

    /**
     * تنسيق رقم الهاتف لاستخدامه مع WhatsApp
     * @private
     * @param {string} phone رقم الهاتف
     * @returns {string} رقم الهاتف المنسق
     */
    _formatPhoneNumber: function(phone) {
        if (!phone) return '';

        // إزالة جميع الأحرف غير الرقمية باستثناء علامة +
        let cleaned = phone.replace(/[^\d+]/g, '');

        // إذا كان الرقم لا يبدأ بـ +، إضافة رمز البلد الافتراضي (+20 لمصر)
        if (!cleaned.startsWith('+')) {
            // إذا كان يبدأ بـ 0، إزالته
            if (cleaned.startsWith('0')) {
                cleaned = cleaned.substring(1);
            }

            // إضافة رمز البلد
            cleaned = '+20' + cleaned;
        }

        // التأكد من أن الرقم له التنسيق الصحيح لـ WhatsApp
        // WhatsApp يتوقع التنسيق الدولي بدون علامة +
        return cleaned.replace(/^\+/, '');
    }
};

// تهيئة وحدة اختبار WhatsApp عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    WhatsAppTest.init();
});
