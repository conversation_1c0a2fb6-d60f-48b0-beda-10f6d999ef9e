<?php
/**
 * API لإعدادات النظام
 * يتعامل مع طلبات AJAX المتعلقة بإعدادات النظام
 */

// تعريف ثابت لمنع الوصول المباشر للملفات المرتبطة
define('BASEPATH', true);

// إعداد رؤوس CORS للسماح بالوصول من أي مصدر
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, X-Ignore-SSL');
header('Content-Type: application/json; charset=UTF-8');

// التعامل مع طلبات OPTIONS للتعامل مع CORS
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// التأكد من ضبط ترميز UTF-8 للمخرجات
mb_internal_encoding('UTF-8');

// استدعاء ملفات التهيئة
require_once '../config/init.php';

// التحقق من تسجيل الدخول
requireLogin();

// التحقق من صلاحيات المستخدم للإجراءات التي تتطلب صلاحيات المدير
// سيتم التحقق من الصلاحيات لكل إجراء على حدة
// للسماح للكاشير بالوصول إلى إعدادات الطباعة

// معالجة الطلبات
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';

    switch ($action) {
        case 'upload-logo':
            handleLogoUpload();
            break;
        case 'remove-logo':
            handleLogoRemoval();
            break;
        default:
            echo json_encode([
                'status' => 'error',
                'message' => 'إجراء غير معروف'
            ]);
            break;
    }
} elseif ($_SERVER['REQUEST_METHOD'] === 'GET') {
    $action = $_GET['action'] ?? '';

    switch ($action) {
        case 'get_invoice_settings':
            getInvoiceSettings();
            break;
        case 'get_background_tasks_settings':
            getBackgroundTasksSettings();
            break;
        default:
            echo json_encode([
                'status' => 'error',
                'message' => 'إجراء غير معروف'
            ]);
            break;
    }
} else {
    echo json_encode([
        'status' => 'error',
        'message' => 'طريقة طلب غير مدعومة'
    ]);
}

/**
 * معالجة رفع شعار الشركة
 */
function handleLogoUpload() {
    // التحقق من صلاحيات المستخدم (المدير فقط)
    if (!hasPermission('settings_manage')) {
        echo json_encode([
            'status' => 'error',
            'message' => 'ليس لديك صلاحية للوصول إلى هذه الصفحة'
        ]);
        return;
    }
    // التحقق من وجود الملف
    if (!isset($_FILES['logo']) || $_FILES['logo']['error'] !== UPLOAD_ERR_OK) {
        echo json_encode([
            'status' => 'error',
            'message' => 'لم يتم تحميل الملف بشكل صحيح'
        ]);
        return;
    }

    // التحقق من نوع الملف
    $allowedTypes = ['image/jpeg', 'image/png', 'image/gif'];
    $fileType = $_FILES['logo']['type'];

    if (!in_array($fileType, $allowedTypes)) {
        echo json_encode([
            'status' => 'error',
            'message' => 'نوع الملف غير مدعوم. الأنواع المدعومة: JPG, PNG, GIF'
        ]);
        return;
    }

    // التحقق من حجم الملف (الحد الأقصى 2 ميجابايت)
    $maxFileSize = 2 * 1024 * 1024; // 2MB
    if ($_FILES['logo']['size'] > $maxFileSize) {
        echo json_encode([
            'status' => 'error',
            'message' => 'حجم الملف كبير جدًا. الحد الأقصى هو 2 ميجابايت'
        ]);
        return;
    }

    // إنشاء مجلد الصور إذا لم يكن موجودًا
    $uploadDir = '../uploads/logos/';
    if (!file_exists($uploadDir)) {
        mkdir($uploadDir, 0755, true);
    }

    // إنشاء اسم فريد للملف
    $fileName = 'company_logo_' . time() . '_' . uniqid();

    // تحديد امتداد الملف
    $fileExtension = '';
    switch ($fileType) {
        case 'image/jpeg':
            $fileExtension = '.jpg';
            break;
        case 'image/png':
            $fileExtension = '.png';
            break;
        case 'image/gif':
            $fileExtension = '.gif';
            break;
    }

    $filePath = $uploadDir . $fileName . $fileExtension;
    $relativePath = 'uploads/logos/' . $fileName . $fileExtension;

    // نقل الملف المرفوع
    if (move_uploaded_file($_FILES['logo']['tmp_name'], $filePath)) {
        // تحديث إعدادات النظام
        $settingsModel = new Settings($GLOBALS['db']);
        $settingsModel->updateSettings(['invoice_company_logo' => $relativePath]);

        // تحديث الجلسة للتأكد من أن التغييرات ستظهر فورًا
        $_SESSION['settings']['invoice_company_logo'] = $relativePath;

        echo json_encode([
            'status' => 'success',
            'message' => 'تم رفع الشعار بنجاح',
            'logo_path' => $relativePath
        ]);
    } else {
        echo json_encode([
            'status' => 'error',
            'message' => 'فشل في رفع الملف'
        ]);
    }
}

/**
 * معالجة إزالة شعار الشركة
 */
function handleLogoRemoval() {
    // التحقق من صلاحيات المستخدم (المدير فقط)
    if (!hasPermission('settings_manage')) {
        echo json_encode([
            'status' => 'error',
            'message' => 'ليس لديك صلاحية للوصول إلى هذه الصفحة'
        ]);
        return;
    }
    $logoPath = $_POST['logo_path'] ?? '';

    if (empty($logoPath)) {
        echo json_encode([
            'status' => 'error',
            'message' => 'مسار الشعار غير صحيح'
        ]);
        return;
    }

    // التحقق من وجود الملف
    $fullPath = '../' . $logoPath;
    if (file_exists($fullPath)) {
        // حذف الملف
        if (unlink($fullPath)) {
            // تحديث إعدادات النظام
            $settingsModel = new Settings($GLOBALS['db']);
            $settingsModel->updateSettings(['invoice_company_logo' => '']);

            // تحديث الجلسة للتأكد من أن التغييرات ستظهر فورًا
            if (isset($_SESSION['settings']['invoice_company_logo'])) {
                unset($_SESSION['settings']['invoice_company_logo']);
            }

            echo json_encode([
                'status' => 'success',
                'message' => 'تم إزالة الشعار بنجاح'
            ]);
        } else {
            echo json_encode([
                'status' => 'error',
                'message' => 'فشل في حذف الملف'
            ]);
        }
    } else {
        // إذا لم يكن الملف موجودًا، نقوم بتحديث الإعدادات فقط
        $settingsModel = new Settings($GLOBALS['db']);
        $settingsModel->updateSettings(['invoice_company_logo' => '']);

        // تحديث الجلسة للتأكد من أن التغييرات ستظهر فورًا
        if (isset($_SESSION['settings']['invoice_company_logo'])) {
            unset($_SESSION['settings']['invoice_company_logo']);
        }

        echo json_encode([
            'status' => 'success',
            'message' => 'تم إزالة الشعار بنجاح'
        ]);
    }
}

/**
 * استرجاع إعدادات الفاتورة
 */
function getInvoiceSettings() {
    // التحقق من تسجيل الدخول
    if (!isset($_SESSION['user_id'])) {
        echo json_encode([
            'status' => 'error',
            'message' => 'يجب تسجيل الدخول للوصول إلى الإعدادات'
        ]);
        return;
    }

    // استرجاع إعدادات الفاتورة
    $settingsModel = new Settings($GLOBALS['db']);
    $invoiceSettings = [];
    $allSettings = $settingsModel->getAllSettings();

    // فرز إعدادات الفاتورة
    foreach ($allSettings as $key => $value) {
        if (strpos($key, 'invoice_') === 0) {
            $shortKey = str_replace('invoice_', '', $key);
            $invoiceSettings[$shortKey] = $value;
        }
    }

    // التأكد من وجود الإعدادات الأساسية
    if (!isset($invoiceSettings['receipt_width'])) {
        $invoiceSettings['receipt_width'] = '80mm';
    }

    if (!isset($invoiceSettings['copies'])) {
        $invoiceSettings['copies'] = 1;
    }

    if (!isset($invoiceSettings['print_without_preview'])) {
        $invoiceSettings['print_without_preview'] = '0';
    }

    if (!isset($invoiceSettings['logo_width'])) {
        $invoiceSettings['logo_width'] = 200;
    }

    if (!isset($invoiceSettings['logo_height'])) {
        $invoiceSettings['logo_height'] = 100;
    }

    if (!isset($invoiceSettings['logo_print_quality'])) {
        $invoiceSettings['logo_print_quality'] = 'normal';
    }

    echo json_encode([
        'status' => 'success',
        'settings' => $invoiceSettings
    ]);
}

/**
 * استرجاع إعدادات المهام في الخلفية
 */
function getBackgroundTasksSettings() {
    try {
        // التحقق من تسجيل الدخول
        if (!isset($_SESSION['user_id'])) {
            echo json_encode([
                'status' => 'error',
                'message' => 'يجب تسجيل الدخول للوصول إلى الإعدادات'
            ]);
            return;
        }

        // استرجاع إعدادات المهام في الخلفية
        $settingsModel = new Settings($GLOBALS['db']);
        $allSettings = $settingsModel->getAllSettings();

        // الإعدادات المطلوبة
        $backgroundTasksSettings = [
            'enable_background_tasks' => $allSettings['enable_background_tasks'] ?? '1',
            'notification_appointment_reminder' => $allSettings['notification_appointment_reminder'] ?? '0',
            'notification_enable_whatsapp' => $allSettings['notification_enable_whatsapp'] ?? '0',
            'whatsapp_enabled' => $allSettings['whatsapp_enabled'] ?? '0'
        ];

        // تسجيل الإعدادات للتشخيص
        error_log("Background Tasks Settings: " . json_encode($backgroundTasksSettings));

        echo json_encode([
            'status' => 'success',
            'settings' => $backgroundTasksSettings
        ]);
    } catch (Exception $e) {
        // تسجيل الخطأ
        error_log("Error in getBackgroundTasksSettings: " . $e->getMessage());

        echo json_encode([
            'status' => 'error',
            'message' => 'حدث خطأ أثناء استرجاع إعدادات المهام في الخلفية',
            'error' => $e->getMessage()
        ]);
    }
}
