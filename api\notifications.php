<?php
/**
 * API للإشعارات
 */

// تعريف ثابت لمنع الوصول المباشر للملفات المرتبطة
define('BASEPATH', true);

// استدعاء ملفات التهيئة
require_once '../config/init.php';

// التحقق من تسجيل الدخول للوصول إلى API
requireLogin();

// تحديد الإجراء المطلوب
$action = isset($_GET['action']) ? $_GET['action'] : '';

// إنشاء اتصال بقاعدة البيانات
$db = new Database();

// إنشاء كائن الإشعارات
$notificationModel = new Notification($db);

// إنشاء كائن الإعدادات
$settingsModel = new Settings($db);

// الاستجابة الافتراضية
$response = [
    'status' => 'error',
    'message' => 'إجراء غير صالح',
    'code' => 400
];

try {
    switch ($action) {
        case 'process_reminders':
            // التحقق من تفعيل تذكير المواعيد
            $allSettings = $settingsModel->getAllSettings();

            // استخراج إعدادات الإشعارات
            $notificationSettings = [];
            foreach ($allSettings as $key => $value) {
                if (strpos($key, 'notification_') === 0) {
                    $shortKey = substr($key, strlen('notification_'));
                    $notificationSettings[$shortKey] = $value;
                }
            }

            if (!isset($notificationSettings['appointment_reminder']) || $notificationSettings['appointment_reminder'] != '1') {
                $response = [
                    'status' => 'error',
                    'message' => 'تذكير المواعيد غير مفعل في الإعدادات',
                    'code' => 400
                ];
                break;
            }

            // إنشاء تذكيرات للمواعيد القادمة
            $createStats = $notificationModel->createAppointmentReminders(7);

            // إرسال الإشعارات المجدولة
            $sendStats = $notificationModel->sendScheduledNotifications();

            $response = [
                'status' => 'success',
                'message' => 'تم معالجة التذكيرات بنجاح',
                'code' => 200,
                'data' => [
                    'created' => $createStats,
                    'sent' => $sendStats
                ]
            ];
            break;

        case 'get_unread_count':
            // الحصول على عدد الإشعارات غير المقروءة للمستخدم الحالي
            $userId = $_SESSION['user_id'];

            $db->prepare("SELECT COUNT(*) FROM notifications
                        WHERE recipient_id = :user_id
                        AND recipient_type = 'user'
                        AND is_read = 0");
            $db->bind(':user_id', $userId);
            $unreadCount = $db->fetchColumn();

            $response = [
                'status' => 'success',
                'message' => 'تم الحصول على عدد الإشعارات غير المقروءة بنجاح',
                'code' => 200,
                'data' => [
                    'unread_count' => (int)$unreadCount
                ]
            ];
            break;

        case 'mark_as_read':
            // التحقق من وجود معرف الإشعار
            if (!isset($_POST['notification_id'])) {
                $response = [
                    'status' => 'error',
                    'message' => 'معرف الإشعار مطلوب',
                    'code' => 400
                ];
                break;
            }

            $notificationId = (int)$_POST['notification_id'];

            // التحقق من ملكية الإشعار
            $userId = $_SESSION['user_id'];

            $db->prepare("SELECT COUNT(*) FROM notifications
                        WHERE id = :notification_id
                        AND recipient_id = :user_id
                        AND recipient_type = 'user'");
            $db->bind(':notification_id', $notificationId);
            $db->bind(':user_id', $userId);
            $isOwner = $db->fetchColumn() > 0;

            if (!$isOwner && !isAdmin()) {
                $response = [
                    'status' => 'error',
                    'message' => 'ليس لديك صلاحية للوصول إلى هذا الإشعار',
                    'code' => 403
                ];
                break;
            }

            // تحديث حالة الإشعار
            $success = $notificationModel->markAsRead($notificationId);

            if ($success) {
                $response = [
                    'status' => 'success',
                    'message' => 'تم تحديث حالة الإشعار بنجاح',
                    'code' => 200
                ];
            } else {
                $response = [
                    'status' => 'error',
                    'message' => 'فشل تحديث حالة الإشعار',
                    'code' => 500
                ];
            }
            break;

        case 'get_user_notifications':
            // الحصول على إشعارات المستخدم الحالي
            $userId = $_SESSION['user_id'];
            $limit = isset($_GET['limit']) ? (int)$_GET['limit'] : 10;

            // التحقق من صحة الحد
            if ($limit < 1 || $limit > 50) {
                $limit = 10;
            }

            // استعلام الإشعارات
            $db->prepare("SELECT * FROM notifications
                        WHERE recipient_id = :user_id
                        AND recipient_type = 'user'
                        ORDER BY created_at DESC
                        LIMIT :limit");
            $db->bind(':user_id', $userId);
            $db->bind(':limit', $limit, PDO::PARAM_INT);
            $notifications = $db->fetchAll();

            $response = [
                'status' => 'success',
                'message' => 'تم الحصول على الإشعارات بنجاح',
                'code' => 200,
                'data' => [
                    'notifications' => $notifications
                ]
            ];
            break;

        case 'mark_all_as_read':
            // تعيين جميع إشعارات المستخدم كمقروءة
            $userId = $_SESSION['user_id'];

            $db->prepare("UPDATE notifications
                        SET is_read = 1, updated_at = NOW()
                        WHERE recipient_id = :user_id
                        AND recipient_type = 'user'
                        AND is_read = 0");
            $db->bind(':user_id', $userId);
            $success = $db->execute();

            if ($success) {
                $response = [
                    'status' => 'success',
                    'message' => 'تم تعيين جميع الإشعارات كمقروءة بنجاح',
                    'code' => 200
                ];
            } else {
                $response = [
                    'status' => 'error',
                    'message' => 'فشل تعيين جميع الإشعارات كمقروءة',
                    'code' => 500
                ];
            }
            break;

        case 'create_notification':
            // إنشاء إشعار جديد (للمديرين فقط)
            if (!isAdmin() && !isManager()) {
                $response = [
                    'status' => 'error',
                    'message' => 'ليس لديك صلاحية لإنشاء إشعارات',
                    'code' => 403
                ];
                break;
            }

            // التحقق من البيانات المطلوبة
            if (!isset($_POST['title']) || !isset($_POST['message']) || !isset($_POST['recipient_id']) || !isset($_POST['recipient_type'])) {
                $response = [
                    'status' => 'error',
                    'message' => 'البيانات غير مكتملة',
                    'code' => 400
                ];
                break;
            }

            // إعداد بيانات الإشعار
            $notificationData = [
                'type' => $_POST['type'] ?? 'system',
                'recipient_id' => (int)$_POST['recipient_id'],
                'recipient_type' => $_POST['recipient_type'],
                'title' => $_POST['title'],
                'message' => $_POST['message'],
                'related_id' => isset($_POST['related_id']) ? (int)$_POST['related_id'] : null,
                'related_type' => $_POST['related_type'] ?? null,
                'branch_id' => isset($_POST['branch_id']) ? (int)$_POST['branch_id'] : null,
                'is_read' => 0,
                'is_sent' => 1,
                'send_email' => isset($_POST['send_email']) ? (int)$_POST['send_email'] : 0,
                'send_sms' => isset($_POST['send_sms']) ? (int)$_POST['send_sms'] : 0
            ];

            // إنشاء الإشعار
            $notificationId = $notificationModel->createNotification($notificationData);

            if ($notificationId) {
                $response = [
                    'status' => 'success',
                    'message' => 'تم إنشاء الإشعار بنجاح',
                    'code' => 200,
                    'data' => [
                        'notification_id' => $notificationId
                    ]
                ];
            } else {
                $response = [
                    'status' => 'error',
                    'message' => 'فشل إنشاء الإشعار',
                    'code' => 500
                ];
            }
            break;

        default:
            $response = [
                'status' => 'error',
                'message' => 'إجراء غير صالح',
                'code' => 400
            ];
            break;
    }
} catch (Exception $e) {
    // تسجيل الخطأ
    error_log('خطأ في API الإشعارات: ' . $e->getMessage());

    $response = [
        'status' => 'error',
        'message' => 'حدث خطأ أثناء معالجة الطلب: ' . $e->getMessage(),
        'code' => 500
    ];
}

// إرسال الاستجابة
header('Content-Type: application/json');
echo json_encode($response);
exit;
