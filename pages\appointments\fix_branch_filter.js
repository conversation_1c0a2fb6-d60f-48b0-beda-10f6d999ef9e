// Script para corregir el problema del filtro de sucursales
$(document).ready(function() {
    // Verificar si el usuario es administrador o gerente
    const userRole = '<?php echo $_SESSION["user_role"]; ?>';
    const hasAdminPermission = userRole === 'admin';
    const hasManagerPermission = userRole === 'manager';
    
    console.log('Script de corrección - Rol del usuario:', userRole);
    console.log('Script de corrección - ¿Es administrador?:', hasAdminPermission);
    
    // Si el usuario es administrador o gerente, habilitar el filtro de sucursales
    if (hasAdminPermission || hasManagerPermission) {
        // Asegurarse de que el filtro de sucursales esté habilitado
        $('#branch_filter').prop('disabled', false);
        console.log('Script de corrección - Filtro de sucursales habilitado');
        
        // Verificar el estado del filtro después de un breve retraso
        setTimeout(function() {
            const isDisabled = $('#branch_filter').prop('disabled');
            console.log('Script de corrección - Estado del filtro después del retraso:', isDisabled ? 'deshabilitado' : 'habilitado');
            
            if (isDisabled) {
                $('#branch_filter').prop('disabled', false);
                console.log('Script de corrección - Filtro de sucursales habilitado nuevamente');
            }
        }, 1000);
    }
});
