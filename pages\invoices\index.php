<?php
/**
 * صفحة عرض الفواتير
 * تعرض قائمة الفواتير مع إمكانية البحث والفلترة
 */

// تعريف ثابت لمنع الوصول المباشر للملفات المرتبطة
define('BASEPATH', true);

// استدعاء ملفات التهيئة
require_once '../../config/init.php';

// التحقق من تسجيل الدخول
redirectIfNotLoggedIn();

// التحقق من صلاحية عرض الفواتير
if (!hasPermission('invoices_view')) {
    $_SESSION['error_message'] = 'ليس لديك صلاحية لعرض الفواتير';
    header('Location: ' . BASE_URL . 'pages/dashboard.php');
    exit;
}

// عنوان الصفحة
$pageTitle = 'إدارة الفواتير';

// إنشاء كائنات النماذج
$invoiceModel = new Invoice($db);
$customerModel = new Customer($db);
$employeeModel = new Employee($db);
$branchModel = new Branch($db);
$settingsModel = new Settings($db);

// الحصول على رمز العملة من الإعدادات
$currencySymbol = $settingsModel->get('system_currency_symbol', 'ر.س');
$currencyName = $settingsModel->get('system_currency', 'ريال سعودي');

// استرجاع قائمة الفروع (للمدير فقط)
$branches = [];
if ($_SESSION['user_role'] === ROLE_ADMIN || $_SESSION['user_role'] === ROLE_MANAGER) {
    $branches = $branchModel->getBranches(['is_active' => 1]);
}

// الحصول على معلمات البحث
$search = isset($_GET['search']) ? trim($_GET['search']) : '';
$branchId = isset($_GET['branch_id']) ? intval($_GET['branch_id']) : $_SESSION['user_branch_id'];
$customerId = isset($_GET['customer_id']) ? intval($_GET['customer_id']) : 0;
$employeeId = isset($_GET['employee_id']) ? intval($_GET['employee_id']) : 0;
$paymentMethod = isset($_GET['payment_method']) ? $_GET['payment_method'] : '';
$paymentStatus = isset($_GET['payment_status']) ? $_GET['payment_status'] : '';
$startDate = isset($_GET['start_date']) ? $_GET['start_date'] : date('Y-m-d', strtotime('-30 days'));
$endDate = isset($_GET['end_date']) ? $_GET['end_date'] : date('Y-m-d');

// الصفحة الحالية وعدد العناصر في الصفحة
$page = isset($_GET['page']) ? max(1, intval($_GET['page'])) : 1;
$perPage = 20;
$offset = ($page - 1) * $perPage;

// فلاتر البحث
$filters = [
    'search' => $search,
    'branch_id' => $branchId,
    'customer_id' => $customerId,
    'employee_id' => $employeeId,
    'payment_method' => $paymentMethod,
    'payment_status' => $paymentStatus,
    'start_date' => $startDate,
    'end_date' => $endDate,
    'limit' => $perPage,
    'offset' => $offset
];

// التحقق من دور المستخدم - إذا كان كاشير، عرض فقط فواتير اليوم المفتوح
$isCashier = ($_SESSION['user_role'] === ROLE_CASHIER);

if ($isCashier) {
    // الحصول على اليوم المفتوح للفرع
    $db->prepare("SELECT id FROM end_days WHERE branch_id = :branch_id AND closed_at IS NULL");
    $db->bind(':branch_id', $branchId);
    $openDay = $db->fetch();

    if ($openDay) {
        // إضافة شرط لعرض فقط فواتير اليوم المفتوح
        $filters['end_day_id'] = $openDay['id'];
    } else {
        // إذا لم يكن هناك يوم مفتوح، لا تعرض أي فواتير
        $filters['end_day_id'] = -1; // قيمة غير موجودة لضمان عدم عرض أي فواتير
    }
}

// استرجاع الفواتير
$invoices = $invoiceModel->getInvoices($filters);
$totalCount = $invoiceModel->getInvoicesCount($filters);

// إنشاء نسخة من الفلاتر للحصول على إجمالي المبيعات
$salesFilters = $filters;

// نريد عرض إجمالي المبيعات للفواتير المدفوعة فقط
// لذلك نستخدم استعلام SQL مباشر للحصول على إجمالي المبيعات للفواتير المدفوعة فقط
$sql = "SELECT SUM(final_amount) FROM invoices i";
$whereConditions = ["i.payment_status = 'paid'"]; // فقط الفواتير المدفوعة
$bindings = [];

// تطبيق الفلاتر المختلفة باستثناء حالة الدفع
if (!empty($filters['search'])) {
    $whereConditions[] = "(i.invoice_number LIKE :search OR EXISTS (SELECT 1 FROM customers c WHERE c.id = i.customer_id AND (c.name LIKE :search OR c.phone LIKE :search)))";
    $bindings[':search'] = '%' . $filters['search'] . '%';
}

if (!empty($filters['branch_id'])) {
    $whereConditions[] = "i.branch_id = :branch_id";
    $bindings[':branch_id'] = $filters['branch_id'];
}

// إذا كان المستخدم كاشير، نضيف فلتر اليوم المفتوح لإجمالي المبيعات
if ($isCashier && isset($filters['end_day_id'])) {
    $whereConditions[] = "i.end_day_id = :end_day_id";
    $bindings[':end_day_id'] = $filters['end_day_id'];
}

if (!empty($filters['customer_id'])) {
    $whereConditions[] = "i.customer_id = :customer_id";
    $bindings[':customer_id'] = $filters['customer_id'];
}

if (!empty($filters['employee_id'])) {
    $whereConditions[] = "i.employee_id = :employee_id";
    $bindings[':employee_id'] = $filters['employee_id'];
}

if (!empty($filters['payment_method'])) {
    $whereConditions[] = "i.payment_method = :payment_method";
    $bindings[':payment_method'] = $filters['payment_method'];
}

// إذا تم تحديد حالة دفع مختلفة في الفلاتر، نستخدمها بدلاً من 'paid'
if (!empty($filters['payment_status'])) {
    // إزالة الشرط الافتراضي لحالة الدفع
    $whereConditions = array_filter($whereConditions, function($condition) {
        return $condition !== "i.payment_status = 'paid'";
    });

    $whereConditions[] = "i.payment_status = :payment_status";
    $bindings[':payment_status'] = $filters['payment_status'];
}

// إذا كان المستخدم ليس كاشير، نطبق فلتر التاريخ
if (!$isCashier && !empty($filters['start_date']) && !empty($filters['end_date'])) {
    $whereConditions[] = "i.created_at BETWEEN :start_date AND :end_date";
    $bindings[':start_date'] = $filters['start_date'] . ' 00:00:00';
    $bindings[':end_date'] = $filters['end_date'] . ' 23:59:59';
}

// إضافة شروط WHERE إذا وجدت
if (!empty($whereConditions)) {
    $sql .= " WHERE " . implode(' AND ', $whereConditions);
}

$db->prepare($sql);

// ربط القيم
foreach ($bindings as $param => $value) {
    $db->bind($param, $value);
}

$totalSales = $db->fetchColumn() ?: 0;

// حساب عدد الصفحات
$totalPages = ceil($totalCount / $perPage);

// استدعاء رأس الصفحة
include '../../includes/templates/header.php';
?>

<!-- المحتوى الرئيسي للصفحة -->
<div class="container-fluid">
    <!-- عنوان الصفحة والأزرار -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-body d-flex justify-content-between align-items-center">
                    <h1 class="h3 mb-0">إدارة الفواتير</h1>
                    <?php if (hasPermission('invoices_create')): ?>
                    <a href="<?php echo BASE_URL; ?>pages/pos/index.php" class="btn btn-primary">
                        <i class="fas fa-plus-circle"></i> إنشاء فاتورة جديدة
                    </a>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- رسائل النجاح والخطأ -->
    <?php include '../../includes/templates/alerts.php'; ?>

    <?php if (!$isCashier): ?>
    <!-- بطاقة البحث والفلترة -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white py-3">
                    <h5 class="mb-0">بحث وفلترة</h5>
                </div>
                <div class="card-body">
                    <form method="get" class="row g-3">
                        <!-- البحث -->
                        <div class="col-md-3">
                            <label for="search" class="form-label">بحث</label>
                            <input type="text" class="form-control" id="search" name="search" placeholder="رقم الفاتورة / العميل" value="<?php echo htmlspecialchars($search); ?>">
                        </div>

                        <!-- الفرع -->
                        <?php if ($_SESSION['user_role'] === ROLE_ADMIN || $_SESSION['user_role'] === ROLE_MANAGER): ?>
                        <div class="col-md-3">
                            <label for="branch_id" class="form-label">الفرع</label>
                            <select class="form-select" id="branch_id" name="branch_id">
                                <option value="">كل الفروع</option>
                                <?php foreach ($branches as $branch): ?>
                                <option value="<?php echo $branch['id']; ?>" <?php echo $branchId == $branch['id'] ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($branch['name']); ?>
                                </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <?php else: ?>
                        <input type="hidden" name="branch_id" value="<?php echo $_SESSION['user_branch_id']; ?>">
                        <?php endif; ?>

                        <!-- طريقة الدفع -->
                        <div class="col-md-3">
                            <label for="payment_method" class="form-label">طريقة الدفع</label>
                            <select class="form-select" id="payment_method" name="payment_method">
                                <option value="">الكل</option>
                                <option value="cash" <?php echo $paymentMethod === 'cash' ? 'selected' : ''; ?>>نقدي</option>
                                <option value="card" <?php echo $paymentMethod === 'card' ? 'selected' : ''; ?>>بطاقة</option>
                                <option value="other" <?php echo $paymentMethod === 'other' ? 'selected' : ''; ?>>أخرى</option>
                            </select>
                        </div>

                        <!-- تاريخ البداية -->
                        <div class="col-md-3">
                            <label for="start_date" class="form-label">من تاريخ</label>
                            <input type="date" class="form-control" id="start_date" name="start_date" value="<?php echo $startDate; ?>">
                        </div>

                        <!-- تاريخ النهاية -->
                        <div class="col-md-3">
                            <label for="end_date" class="form-label">إلى تاريخ</label>
                            <input type="date" class="form-control" id="end_date" name="end_date" value="<?php echo $endDate; ?>">
                        </div>

                        <!-- أزرار البحث وإعادة التعيين -->
                        <div class="col-md-6 d-flex align-items-end">
                            <button type="submit" class="btn btn-primary me-2">
                                <i class="fas fa-search"></i> بحث
                            </button>
                            <a href="<?php echo BASE_URL; ?>pages/invoices/index.php" class="btn btn-secondary">
                                <i class="fas fa-redo"></i> إعادة تعيين
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    <?php else: ?>
    <!-- رسالة للكاشير -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="alert alert-info">
                <i class="fas fa-info-circle"></i> أنت تشاهد فقط فواتير اليوم المفتوح الحالي.
            </div>
        </div>
    </div>
    <?php endif; ?>

    <!-- ملخص المبيعات -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="row g-4">
                        <div class="col-md-4">
                            <div class="d-flex align-items-center">
                                <div class="bg-primary bg-opacity-10 p-3 rounded me-3">
                                    <i class="fas fa-file-invoice-dollar text-primary fa-2x"></i>
                                </div>
                                <div>
                                    <h6 class="text-muted mb-1">إجمالي الفواتير</h6>
                                    <h3 class="mb-0"><?php echo number_format($totalCount); ?></h3>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="d-flex align-items-center">
                                <div class="bg-success bg-opacity-10 p-3 rounded me-3">
                                    <i class="fas fa-money-bill-wave text-success fa-2x"></i>
                                </div>
                                <div>
                                    <h6 class="text-muted mb-1">إجمالي المبيعات</h6>
                                    <h3 class="mb-0"><?php echo number_format($totalSales, 2) . ' ' . $currencySymbol; ?></h3>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="d-flex align-items-center">
                                <div class="bg-info bg-opacity-10 p-3 rounded me-3">
                                    <i class="fas fa-calendar-alt text-info fa-2x"></i>
                                </div>
                                <div>
                                    <h6 class="text-muted mb-1">الفترة</h6>
                                    <h5 class="mb-0">
                                        <?php if ($isCashier && isset($openDay)): ?>
                                            اليوم المفتوح الحالي
                                        <?php else: ?>
                                            <?php echo date('Y-m-d', strtotime($startDate)); ?>
                                            <i class="fas fa-arrow-left mx-2"></i>
                                            <?php echo date('Y-m-d', strtotime($endDate)); ?>
                                        <?php endif; ?>
                                    </h5>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- جدول الفواتير -->
    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table id="invoicesTable" class="table table-hover mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th>#</th>
                                    <th>رقم الفاتورة</th>
                                    <th>العميل</th>
                                    <th>الموظف</th>
                                    <th>التاريخ</th>
                                    <th>المبلغ الإجمالي</th>
                                    <th>الخصم</th>
                                    <th>المبلغ النهائي</th>
                                    <th>المبلغ المدفوع</th>
                                    <th>طريقة الدفع</th>
                                    <th>حالة الدفع</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- الترقيم (Pagination) -->
    <?php if ($totalPages > 1): ?>
    <div class="row mt-4">
        <div class="col-12">
            <nav aria-label="Page navigation">
                <ul class="pagination justify-content-center">
                    <!-- زر الصفحة السابقة -->
                    <li class="page-item <?php echo $page <= 1 ? 'disabled' : ''; ?>">
                        <a class="page-link" href="<?php echo BASE_URL; ?>pages/invoices/index.php?<?php echo http_build_query(array_merge($_GET, ['page' => $page - 1])); ?>" aria-label="Previous">
                            <span aria-hidden="true">&laquo;</span>
                        </a>
                    </li>

                    <!-- أرقام الصفحات -->
                    <?php for ($i = max(1, $page - 2); $i <= min($totalPages, $page + 2); $i++): ?>
                    <li class="page-item <?php echo $i == $page ? 'active' : ''; ?>">
                        <a class="page-link" href="<?php echo BASE_URL; ?>pages/invoices/index.php?<?php echo http_build_query(array_merge($_GET, ['page' => $i])); ?>">
                            <?php echo $i; ?>
                        </a>
                    </li>
                    <?php endfor; ?>

                    <!-- زر الصفحة التالية -->
                    <li class="page-item <?php echo $page >= $totalPages ? 'disabled' : ''; ?>">
                        <a class="page-link" href="<?php echo BASE_URL; ?>pages/invoices/index.php?<?php echo http_build_query(array_merge($_GET, ['page' => $page + 1])); ?>" aria-label="Next">
                            <span aria-hidden="true">&raquo;</span>
                        </a>
                    </li>
                </ul>
            </nav>
        </div>
    </div>
    <?php endif; ?>
</div>

<!-- نافذة تأكيد الحذف -->
<div class="modal fade" id="deleteInvoiceModal" tabindex="-1" aria-labelledby="deleteInvoiceModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteInvoiceModalLabel">تأكيد الحذف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                هل أنت متأكد من رغبتك في حذف هذه الفاتورة؟ لا يمكن التراجع عن هذا الإجراء.
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-danger" id="confirmDeleteInvoice">حذف</button>
            </div>
        </div>
    </div>
</div>

<!-- نافذة طباعة الفاتورة -->
<div class="modal fade" id="printInvoiceModal" tabindex="-1" aria-labelledby="printInvoiceModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="printInvoiceModalLabel">طباعة الفاتورة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" id="printInvoiceContent">
                <div class="text-center">
                    <div class="spinner-border" role="status">
                        <span class="visually-hidden">جاري التحميل...</span>
                    </div>
                    <p class="mt-2">جاري تحضير الفاتورة للطباعة...</p>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                <button type="button" class="btn btn-primary" id="printInvoiceBtn">
                    <i class="fas fa-print"></i> طباعة
                </button>
            </div>
        </div>
    </div>
</div>

<!-- استدعاء قالب الفوتر -->
<?php include '../../includes/templates/footer.php'; ?>

<script>
// Pass PHP session data to JavaScript
const userRole = '<?php echo $_SESSION['user_role']; ?>';
const isAdmin = userRole === '<?php echo ROLE_ADMIN; ?>';
const userId = <?php echo $_SESSION['user_id']; ?>;

$(document).ready(function() {
    // تهيئة جدول الفواتير باستخدام DataTables
    $('#invoicesTable').DataTable({
        processing: true,
        serverSide: false,
        data: <?php echo json_encode($invoices); ?>,
        columns: [
            {
                data: null,
                render: function(data, type, row, meta) {
                    return meta.row + <?php echo $offset; ?> + 1;
                }
            },
            { data: 'invoice_number' },
            {
                data: 'customer_name',
                render: function(data) {
                    return data || 'عميل غير مسجل';
                }
            },
            {
                data: 'employee_name',
                render: function(data) {
                    return data || '-';
                }
            },
            {
                data: 'created_at',
                render: function(data) {
                    return moment(data).format('YYYY-MM-DD HH:mm');
                }
            },
            {
                data: 'total_amount',
                render: function(data) {
                    return parseFloat(data).toFixed(2) + ' <?php echo $currencySymbol; ?>';
                }
            },
            {
                data: null,
                render: function(data, type, row) {
                    if (row.discount_type === 'percentage') {
                        // إذا كان الخصم بالنسبة المئوية، نعرض النسبة المئوية
                        return parseFloat(data.discount_amount).toFixed(2) + ' %';
                    } else {
                        // إذا كان الخصم بالمبلغ، نعرض المبلغ بالعملة
                        return parseFloat(data.discount_amount).toFixed(2) + ' <?php echo $currencySymbol; ?>';
                    }
                }
            },
            {
                data: 'final_amount',
                render: function(data) {
                    return parseFloat(data).toFixed(2) + ' <?php echo $currencySymbol; ?>';
                }
            },
            {
                data: null,
                render: function(data, type, row) {
                    // تخزين المبلغ المدفوع في الصف لاستخدامه لاحقًا
                    $(row.node).data('paid-amount', row.paid_amount || 0);

                    if (row.payment_status === 'partial') {
                        // إذا كانت مدفوعة جزئيًا، نعرض المبلغ المدفوع
                        const paidAmount = parseFloat(row.paid_amount || 0).toFixed(2);
                        return paidAmount + ' <?php echo $currencySymbol; ?>';
                    } else if (row.payment_status === 'paid') {
                        // إذا كانت مدفوعة بالكامل، نعرض المبلغ النهائي
                        return parseFloat(row.final_amount).toFixed(2) + ' <?php echo $currencySymbol; ?>';
                    } else {
                        // إذا كانت غير مدفوعة، نعرض 0
                        return '0.00 <?php echo $currencySymbol; ?>';
                    }
                }
            },
            {
                data: 'payment_method',
                render: function(data) {
                    switch (data) {
                        case 'cash':
                            return '<span class="badge bg-success">نقدي</span>';
                        case 'card':
                            return '<span class="badge bg-primary">بطاقة</span>';
                        case 'other':
                            return '<span class="badge bg-info">أخرى</span>';
                        default:
                            return '<span class="badge bg-secondary">غير محدد</span>';
                    }
                }
            },
            {
                data: 'payment_status',
                render: function(data) {
                    switch (data) {
                        case 'paid':
                            return '<span class="badge bg-success">مدفوع</span>';
                        case 'partial':
                            return '<span class="badge bg-warning">مدفوع جزئي</span>';
                        case 'unpaid':
                            return '<span class="badge bg-danger">غير مدفوع</span>';
                        default:
                            return '<span class="badge bg-secondary">غير محدد</span>';
                    }
                }
            },
            {
                data: null,
                orderable: false,
                render: function(data) {
                    let actions = '<div class="btn-group btn-group-sm">';

                    // عرض الفاتورة
                    actions += `<a href="<?php echo BASE_URL; ?>pages/invoices/view.php?id=${data.id}" class="btn btn-info" title="عرض الفاتورة">
                        <i class="fas fa-eye"></i>
                    </a>`;

                    <?php if (hasPermission('invoices_edit')): ?>
                    // تحرير الفاتورة (إذا لم تكن مرتبطة بنهاية اليوم مغلقة)
                    if (isAdmin || !data.end_day_id || !data.end_day_closed_at) {
                        actions += `<a href="<?php echo BASE_URL; ?>pages/pos/index.php?invoice_id=${data.id}" class="btn btn-primary" title="تعديل الفاتورة">
                            <i class="fas fa-edit"></i>
                        </a>`;
                    }
                    <?php endif; ?>

                    // تغيير حالة الدفع (إذا لم تكن مرتبطة بنهاية اليوم مغلقة أو كان المستخدم مدير)
                    if (isAdmin || !data.end_day_id || !data.end_day_closed_at) {
                        // إذا كان المستخدم لديه صلاحية تعديل الفواتير أو هو منشئ الفاتورة
                        const canChangeStatus = <?php echo hasPermission('invoices_edit') ? 'true' : 'false'; ?> || data.cashier_id == userId;

                        if (canChangeStatus) {
                            actions += `<button class="btn btn-warning change-status" data-id="${data.id}" data-status="${data.payment_status}" title="تغيير حالة الدفع">
                                <i class="fas fa-money-bill-wave"></i>
                            </button>`;
                        }
                    }

                    <?php if (hasPermission('invoices_delete') || true): // نسمح للجميع برؤية زر الحذف، وسيتم التحقق من الصلاحية في الخلفية ?>
                    // حذف الفاتورة (إذا لم تكن مرتبطة بنهاية اليوم مغلقة أو كان المستخدم مدير)
                    if (isAdmin || !data.end_day_id || !data.end_day_closed_at) {
                        // إذا كان المستخدم لديه صلاحية حذف الفواتير أو هو منشئ الفاتورة
                        const canDelete = <?php echo hasPermission('invoices_delete') ? 'true' : 'false'; ?> || data.cashier_id == userId;

                        if (canDelete) {
                            actions += `<a href="javascript:void(0);" class="btn btn-danger delete-invoice" data-id="${data.id}" title="حذف الفاتورة">
                                <i class="fas fa-trash"></i>
                            </a>`;
                        }
                    }
                    <?php endif; ?>

                    actions += '</div>';
                    return actions;
                }
            }
        ],
        language: {
            url: '<?php echo BASE_URL; ?>assets/plugins/datatables/ar.json'
        },
        dom: "<'row'<'col-sm-12 col-md-6'l><'col-sm-12 col-md-6'f>>" +
             "<'row'<'col-sm-12'tr>>" +
             "<'row'<'col-sm-12 col-md-5'i><'col-sm-12 col-md-7'p>>",
        lengthMenu: [[10, 25, 50, 100, -1], [10, 25, 50, 100, "الكل"]],
        order: [[4, 'desc']] // ترتيب افتراضي حسب التاريخ (تنازلي)
    });

    // حذف الفاتورة
    $(document).on('click', '.delete-invoice', function() {
        const invoiceId = $(this).data('id');
        if (confirm('هل أنت متأكد من حذف هذه الفاتورة؟')) {
            $.ajax({
                url: '<?php echo BASE_URL; ?>api/invoices.php?action=delete',
                type: 'POST',
                data: JSON.stringify({
                    id: invoiceId
                }),
                contentType: 'application/json',
                dataType: 'json',
                success: function(response) {
                    if (response.status === 'success') {
                        alert('تم حذف الفاتورة بنجاح');
                        location.reload();
                    } else {
                        alert('خطأ: ' + response.message);
                    }
                },
                error: function(xhr) {
                    try {
                        const response = JSON.parse(xhr.responseText);
                        alert('خطأ: ' + (response.message || 'حدث خطأ أثناء الاتصال بالخادم'));
                    } catch (e) {
                        alert('حدث خطأ أثناء الاتصال بالخادم');
                    }
                }
            });
        }
    });

    // تغيير حالة الدفع
    $(document).on('click', '.change-status', function() {
        // الحصول على معرف الفاتورة وحالتها الحالية
        const invoiceId = $(this).data('id');
        const currentStatus = $(this).data('status');
        const invoiceRow = $(this).closest('tr');
        const finalAmount = parseFloat(invoiceRow.find('td:eq(7)').text().replace(/[^\d.-]/g, ''));
        const paidAmount = parseFloat(invoiceRow.data('paid-amount') || 0);

        console.log('Opening modal for invoice:', invoiceId);
        console.log('Current status:', currentStatus);
        console.log('Final amount:', finalAmount);
        console.log('Paid amount:', paidAmount);

        // تعيين قيم المودال
        $('#invoice_id').val(invoiceId);
        $('#invoice_final_amount').val(finalAmount);
        $('#invoice_total_text').text(finalAmount.toFixed(2));
        $('#paid_amount').val(paidAmount > 0 ? paidAmount.toFixed(2) : '');

        // إعادة تعيين الخيارات وتحديد الخيار المناسب
        // تعيين قيمة القائمة مباشرة
        const selectElement = document.getElementById('payment_status');

        // إعادة تعيين جميع الخيارات إلى غير محددة
        for (let i = 0; i < selectElement.options.length; i++) {
            if (selectElement.options[i].value === currentStatus) {
                selectElement.options[i].selected = true;
            } else {
                selectElement.options[i].selected = false;
            }
        }

        // إظهار أو إخفاء حقل المبلغ المدفوع بناءً على حالة الدفع
        if (currentStatus === 'partial') {
            $('#paid_amount_container').show();
        } else {
            $('#paid_amount_container').hide();
        }

        console.log('Current status:', currentStatus);
        console.log('Selected value after setting:', selectElement.value);

        // فتح المودال
        const modal = new bootstrap.Modal(document.getElementById('changeStatusModal'));
        modal.show();
    });

    // إظهار أو إخفاء حقل المبلغ المدفوع عند تغيير حالة الدفع
    $('#payment_status').on('change', function() {
        const selectedStatus = $(this).val();
        if (selectedStatus === 'partial') {
            $('#paid_amount_container').show();
        } else {
            $('#paid_amount_container').hide();
            // إذا كانت الحالة مدفوعة بالكامل، نضع المبلغ المدفوع مساويًا للمبلغ الإجمالي
            if (selectedStatus === 'paid') {
                $('#paid_amount').val($('#invoice_final_amount').val());
            } else if (selectedStatus === 'unpaid') {
                $('#paid_amount').val('0');
            }
        }
    });

    // حفظ تغيير حالة الدفع
    $('#saveStatusChange').on('click', function() {
        // الحصول على القيم
        const invoiceId = $('#invoice_id').val();
        const selectElement = document.getElementById('payment_status');
        const paymentStatus = selectElement.options[selectElement.selectedIndex].value;
        const finalAmount = parseFloat($('#invoice_final_amount').val());
        let paidAmount = 0;

        // الحصول على المبلغ المدفوع بناءً على حالة الدفع
        if (paymentStatus === 'paid') {
            paidAmount = finalAmount; // إذا كانت مدفوعة بالكامل، المبلغ المدفوع = المبلغ الإجمالي
        } else if (paymentStatus === 'partial') {
            paidAmount = parseFloat($('#paid_amount').val()) || 0;

            // التحقق من صحة المبلغ المدفوع
            if (paidAmount <= 0) {
                alert('يجب إدخال مبلغ مدفوع أكبر من صفر');
                return;
            }

            if (paidAmount >= finalAmount) {
                alert('المبلغ المدفوع يجب أن يكون أقل من المبلغ الإجمالي. إذا تم دفع المبلغ بالكامل، اختر حالة "مدفوع"');
                return;
            }
        } else {
            paidAmount = 0; // إذا كانت غير مدفوعة، المبلغ المدفوع = 0
        }

        console.log('Saving payment status:', paymentStatus);
        console.log('For invoice ID:', invoiceId);
        console.log('Paid amount:', paidAmount);

        // التحقق من صحة البيانات
        if (!invoiceId) {
            alert('حدث خطأ: لم يتم تحديد رقم الفاتورة');
            return;
        }

        // إرسال الطلب لتحديث حالة الدفع
        $.ajax({
            url: '<?php echo BASE_URL; ?>api/invoices.php?action=update_status',
            type: 'POST',
            data: {
                invoice_id: invoiceId,
                payment_status: paymentStatus,
                paid_amount: paidAmount
            },
            dataType: 'json',
            success: function(response) {
                if (response.success) {
                    // إغلاق المودال
                    const modalElement = document.getElementById('changeStatusModal');
                    const modal = bootstrap.Modal.getInstance(modalElement);
                    modal.hide();

                    // إظهار رسالة نجاح
                    alert('تم تحديث حالة الدفع بنجاح');

                    // إعادة تحميل الصفحة
                    window.location.reload();
                } else {
                    alert('خطأ: ' + (response.message || 'حدث خطأ غير معروف'));
                }
            },
            error: function() {
                alert('حدث خطأ أثناء الاتصال بالخادم');
            }
        });
    });
});
</script>

<!-- مودال تغيير حالة الدفع -->
<div class="modal fade" id="changeStatusModal" tabindex="-1" aria-labelledby="changeStatusModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="changeStatusModalLabel">تغيير حالة الدفع</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="إغلاق"></button>
            </div>
            <div class="modal-body">
                <input type="hidden" id="invoice_id">
                <input type="hidden" id="invoice_final_amount">
                <div class="mb-3">
                    <label for="payment_status" class="form-label">حالة الدفع</label>
                    <select class="form-select" id="payment_status" name="payment_status">
                        <option value="paid">مدفوع</option>
                        <option value="partial">مدفوع جزئي</option>
                        <option value="unpaid">غير مدفوع</option>
                    </select>
                </div>
                <div class="mb-3" id="paid_amount_container" style="display: none;">
                    <label for="paid_amount" class="form-label">المبلغ المدفوع</label>
                    <div class="input-group">
                        <input type="number" step="0.01" min="0" class="form-control" id="paid_amount" name="paid_amount" placeholder="أدخل المبلغ المدفوع">
                        <span class="input-group-text"><?php echo $currencySymbol; ?></span>
                    </div>
                    <div class="form-text">المبلغ الإجمالي للفاتورة: <span id="invoice_total_text">0.00</span> <?php echo $currencySymbol; ?></div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" id="saveStatusChange">حفظ</button>
            </div>
        </div>
    </div>
</div>
</body>
</html>