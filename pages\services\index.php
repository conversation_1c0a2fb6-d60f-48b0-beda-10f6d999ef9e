<?php
/**
 * صفحة إدارة الخدمات
 * تعرض قائمة الخدمات المتاحة مع إمكانية البحث والفلترة
 */

// تعريف ثابت لمنع الوصول المباشر للملفات المرتبطة
define('BASEPATH', true);

// استدعاء ملف التهيئة
require_once '../../config/init.php';

// التحقق من تسجيل الدخول
redirectIfNotLoggedIn();

// التحقق من صلاحية الوصول
if (!hasPermission('services_view')) {
    $_SESSION['error_message'] = 'ليس لديك صلاحية لعرض الخدمات';
    header('Location: ' . BASE_URL . 'pages/dashboard.php');
    exit;
}

// تحديد الفلاتر الافتراضية
$currentBranchId = $_SESSION['user_branch_id'];
$isAdmin = ($_SESSION['user_role'] === ROLE_ADMIN);

// الحصول على فئات الخدمات
$serviceObj = new Service($db);
$categories = $serviceObj->getServiceCategories();

// الحصول على الفروع (للمدير فقط)
$branches = [];
if ($isAdmin) {
    $branchObj = new Branch($db);
    $branches = $branchObj->getBranches();
}

// الحصول على رمز العملة من الإعدادات
$settingsModel = new Settings($db);
$currencySymbol = $settingsModel->get('system_currency_symbol', 'ر.س');
$currencyName = $settingsModel->get('system_currency', 'ريال سعودي');

// عنوان الصفحة
$pageTitle = 'إدارة الخدمات';

// استدعاء قالب الرأس
include_once '../../includes/templates/header.php';
?>

<!-- تأكد من تحميل jQuery أولاً إذا لم يكن موجودًا بالفعل -->
<script>
    if (typeof jQuery === 'undefined') {
        document.write('<script src="https://code.jquery.com/jquery-3.6.0.min.js"><\/script>');
    }
</script>
<!-- ثم قم بتحميل toastr -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.css">
<script src="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.js"></script>

<!-- بداية محتوى الصفحة -->
<div class="page-content">
    <div class="container-fluid">

        <!-- عنوان الصفحة والأزرار -->
        <div class="row mb-4 align-items-center">
            <div class="col-md-6">
                <h4 class="fw-bold mb-2"><?php echo $pageTitle; ?></h4>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb mb-0">
                        <li class="breadcrumb-item">
                            <a href="<?php echo BASE_URL . 'pages/dashboard.php'; ?>" class="text-decoration-none">
                                <i class="fas fa-home me-1"></i>الرئيسية
                            </a>
                        </li>
                        <li class="breadcrumb-item active" aria-current="page">الخدمات</li>
                    </ol>
                </nav>
            </div>
            <div class="col-md-6 text-md-end">
                <div class="btn-group">
                    <?php if (hasPermission('services_add')): ?>
                        <a href="<?php echo BASE_URL . 'pages/services/add.php'; ?>" class="btn btn-primary">
                            <i class="fas fa-plus-circle me-1"></i> إضافة خدمة
                        </a>
                    <?php endif; ?>

                    <?php if (hasPermission('services_edit')): ?>
                        <a href="<?php echo BASE_URL . 'pages/services/categories.php'; ?>" class="btn btn-outline-secondary ms-2">
                            <i class="fas fa-tags me-1"></i> فئات الخدمات
                        </a>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- بطاقة البحث والفلترة -->
        <div class="card mb-4">
            <div class="card-header bg-light">
                <h5 class="mb-0">البحث والفلترة</h5>
            </div>
            <div class="card-body">
                <form id="service-search-form" method="get">
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <label for="search" class="form-label">البحث</label>
                            <input type="text" class="form-control" id="search" name="search" placeholder="اسم الخدمة...">
                        </div>
                        <div class="col-md-3 mb-3">
                            <label for="category_id" class="form-label">الفئة</label>
                            <select class="form-select" id="category_id" name="category_id">
                                <option value="">الكل</option>
                                <?php foreach ($categories as $category): ?>
                                    <option value="<?php echo $category['id']; ?>"><?php echo $category['name']; ?></option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <?php if ($isAdmin): ?>
                            <div class="col-md-3 mb-3">
                                <label for="branch_id" class="form-label">الفرع</label>
                                <select class="form-select" id="branch_id" name="branch_id">
                                    <option value="">الكل</option>
                                    <?php foreach ($branches as $branch): ?>
                                        <option value="<?php echo $branch['id']; ?>"><?php echo $branch['name']; ?></option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        <?php endif; ?>
                        <div class="col-md-3 mb-3">
                            <label for="is_active" class="form-label">الحالة</label>
                            <select class="form-select" id="is_active" name="is_active">
                                <option value="">الكل</option>
                                <option value="1">نشط</option>
                                <option value="0">غير نشط</option>
                            </select>
                        </div>
                        <div class="col-md-12 mt-2">
                            <div class="d-flex justify-content-end gap-3">
                                <button type="submit" class="btn btn-primary px-4">
                                    <i class="fas fa-search me-2"></i> بحث
                                </button>
                                <button type="reset" class="btn btn-outline-secondary px-4" id="reset-filters">
                                    <i class="fas fa-redo me-2"></i> إعادة تعيين
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- جدول الخدمات -->
        <div class="card">
            <div class="card-header bg-light d-flex justify-content-between align-items-center">
                <h5 class="mb-0">قائمة الخدمات</h5>
                <div>
                    <span class="badge bg-primary" id="total-services">0</span> خدمة
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-bordered table-hover" id="services-table">
                        <thead class="table-light">
                            <tr>
                                <th width="5%">#</th>
                                <th width="25%">اسم الخدمة</th>
                                <th width="15%">الفئة</th>
                                <th width="10%">السعر</th>
                                <th width="10%">المدة (دقيقة)</th>
                                <?php if ($isAdmin): ?>
                                    <th width="15%">الفرع</th>
                                <?php endif; ?>
                                <th width="10%">الحالة</th>
                                <th width="15%">الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="services-list">
                            <!-- سيتم تعبئتها عبر الجافاسكربت -->
                            <tr>
                                <td colspan="<?php echo $isAdmin ? '8' : '7'; ?>" class="text-center">
                                    <div class="d-flex justify-content-center py-3">
                                        <div class="spinner-border text-primary" role="status">
                                            <span class="visually-hidden">جاري التحميل...</span>
                                        </div>
                                    </div>
                                    <p class="text-muted">جاري تحميل البيانات...</p>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <!-- التصفح -->
                <nav aria-label="Page navigation" class="mt-4">
                    <ul class="pagination justify-content-center" id="pagination">
                        <!-- سيتم تعبئتها عبر الجافاسكربت -->
                    </ul>
                </nav>
            </div>
        </div>

    </div>
</div>

<!-- موديل تفاصيل الخدمة -->
<div class="modal fade" id="serviceDetailsModal" tabindex="-1" aria-labelledby="serviceDetailsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="serviceDetailsModalLabel">تفاصيل الخدمة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-6">
                        <p><strong>اسم الخدمة:</strong> <span id="modal-service-name"></span></p>
                        <p><strong>الفئة:</strong> <span id="modal-service-category"></span></p>
                        <p><strong>المدة:</strong> <span id="modal-service-duration"></span> دقيقة</p>
                    </div>
                    <div class="col-md-6">
                        <p><strong>السعر:</strong> <span id="modal-service-price"></span></p>
                        <p><strong>الحالة:</strong> <span id="modal-service-status"></span></p>
                        <p><strong>الفرع:</strong> <span id="modal-service-branch"></span></p>
                    </div>
                    <div class="col-md-12">
                        <hr>
                        <p><strong>الوصف:</strong></p>
                        <p id="modal-service-description" class="text-muted"></p>
                    </div>
                    <div class="col-md-12 mt-3">
                        <h6>الموظفون المتاحون للخدمة:</h6>
                        <ul id="modal-service-employees" class="list-group">
                            <!-- سيتم تعبئتها عبر الجافاسكربت -->
                        </ul>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                <?php if (hasPermission('services_edit')): ?>
                    <a href="#" id="btn-edit-service" class="btn btn-primary">تعديل</a>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- موديل تأكيد الحذف -->
<div class="modal fade" id="deleteServiceModal" tabindex="-1" aria-labelledby="deleteServiceModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title" id="deleteServiceModalLabel">تأكيد حذف الخدمة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من حذف الخدمة: <strong id="delete-service-name"></strong>؟</p>
                <p class="text-danger">هذا الإجراء لا يمكن التراجع عنه!</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-danger" id="confirm-delete">تأكيد الحذف</button>
            </div>
        </div>
    </div>
</div>

<!-- جافاسكربت خاص بالصفحة -->
<script>
    // التحقق من وجود jQuery وتحميلها إذا لم تكن موجودة
    if (typeof jQuery === 'undefined') {
        document.write('<script src="https://code.jquery.com/jquery-3.6.0.min.js"><\/script>');
    }

    // استخدام window.onload بدلاً من $(document).ready للتأكد من تحميل jQuery
    window.onload = function() {
        // التحقق مرة أخرى من وجود jQuery
        if (typeof jQuery === 'undefined') {
            console.error('فشل تحميل jQuery. بعض الوظائف قد لا تعمل بشكل صحيح.');
            document.querySelector('#services-table tbody').innerHTML = '<tr><td colspan="<?php echo $isAdmin ? "8" : "7"; ?>" class="text-center text-danger">حدث خطأ في تحميل المكتبات اللازمة</td></tr>';
            return;
        }

        // متغيرات عامة
        let currentPage = 1;
        let totalPages = 1;
        let limit = 10;

        // تحميل الخدمات عند تحميل الصفحة
        loadServices();

        // تحميل الخدمات عند إرسال نموذج البحث
        $('#service-search-form').on('submit', function(e) {
            e.preventDefault();
            currentPage = 1;
            loadServices();
        });

        // إعادة تعيين الفلاتر
        $('#reset-filters').on('click', function() {
            setTimeout(function() {
                currentPage = 1;
                loadServices();
            }, 100);
        });

        // وظيفة تحميل الخدمات
        function loadServices() {
            // إظهار مؤشر التحميل
            $('#services-list').html(`
                <tr>
                    <td colspan="<?php echo $isAdmin ? '8' : '7'; ?>" class="text-center">
                        <div class="d-flex justify-content-center py-3">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">جاري التحميل...</span>
                            </div>
                        </div>
                        <p class="text-muted">جاري تحميل البيانات...</p>
                    </td>
                </tr>
            `);

            // جمع بيانات الفلاتر
            const filters = {
                page: currentPage,
                limit: 100
            };

            // إضافة قيم البحث فقط إذا كانت غير فارغة
            const searchVal = $('#search').val();
            if (searchVal && searchVal.trim() !== '') {
                filters.search = searchVal;
            }

            const categoryId = $('#category_id').val();
            if (categoryId && categoryId !== '') {
                filters.category_id = categoryId;
            }

            const isActive = $('#is_active').val();
            if (isActive !== '') {
                filters.is_active = isActive;
            }

            <?php if ($isAdmin): ?>
            const branchId = $('#branch_id').val();
            if (branchId && branchId !== '') {
                filters.branch_id = branchId;
            }
            <?php endif; ?>

            // إرسال طلب AJAX
            $.ajax({
                url: '<?php echo BASE_URL; ?>api/services.php',
                type: 'GET',
                data: filters,
                dataType: 'json',
                success: function(response) {
                    if (response.status === 'success') {
                        // تحديث عدد الخدمات
                        $('#total-services').text(response.total);

                        // تحديث عدد الصفحات
                        totalPages = Math.ceil(response.total / limit);

                        // عرض الخدمات
                        displayServices(response.data);

                        // تحديث التصفح
                        updatePagination();
                    } else {
                        // عرض رسالة الخطأ
                        $('#services-list').html(`
                            <tr>
                                <td colspan="<?php echo $isAdmin ? '8' : '7'; ?>" class="text-center text-danger">
                                    <i class="fas fa-exclamation-triangle me-2"></i>
                                    ${response.message || 'حدث خطأ أثناء تحميل البيانات'}
                                </td>
                            </tr>
                        `);
                    }
                },
                error: function(xhr, status, error) {
                    // عرض رسالة الخطأ
                    $('#services-list').html(`
                        <tr>
                            <td colspan="<?php echo $isAdmin ? '8' : '7'; ?>" class="text-center text-danger">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                حدث خطأ أثناء الاتصال بالخادم
                            </td>
                        </tr>
                    `);
                    console.error('AJAX Error:', error);
                }
            });
        }

        // عرض الخدمات في الجدول
        function displayServices(services) {
            let html = '';

            if (services.length === 0) {
                html = '<tr><td colspan="<?php echo $isAdmin ? "8" : "7"; ?>" class="text-center">لا توجد خدمات للعرض</td></tr>';
            } else {
                $.each(services, function(index, service) {
                    html += `
                        <tr>
                            <td>${(currentPage - 1) * limit + index + 1}</td>
                            <td>${service.name}</td>
                            <td>${service.category_name || '-'}</td>
                            <td>${service.price} <?php echo $currencySymbol; ?></td>
                            <td>${service.duration} دقيقة</td>
                            <?php if ($isAdmin): ?>
                                <td>${service.branch_name || 'جميع الفروع'}</td>
                            <?php endif; ?>
                            <td>
                                ${service.is_active == 1
                                    ? '<span class="badge bg-success">نشط</span>'
                                    : '<span class="badge bg-danger">غير نشط</span>'}
                            </td>
                            <td>
                                <button type="button" class="btn btn-sm btn-info view-service" data-id="${service.id}">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <?php if (hasPermission('services_edit')): ?>
                                    <a href="<?php echo BASE_URL; ?>pages/services/edit.php?id=${service.id}" class="btn btn-sm btn-primary">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                <?php endif; ?>
                                <?php if (hasPermission('services_delete')): ?>
                                    <button type="button" class="btn btn-sm btn-danger delete-service" data-id="${service.id}" data-name="${service.name}">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                <?php endif; ?>
                            </td>
                        </tr>
                    `;
                });
            }

            $('#services-table tbody').html(html);
        }

        // تحديث التصفح
        function updatePagination() {
            let paginationHtml = '';

            if (totalPages > 1) {
                paginationHtml += `
                    <li class="page-item ${currentPage === 1 ? 'disabled' : ''}">
                        <a class="page-link" href="#" data-page="1" aria-label="First">
                            <span aria-hidden="true">&laquo;&laquo;</span>
                        </a>
                    </li>
                    <li class="page-item ${currentPage === 1 ? 'disabled' : ''}">
                        <a class="page-link" href="#" data-page="${currentPage - 1}" aria-label="Previous">
                            <span aria-hidden="true">&laquo;</span>
                        </a>
                    </li>
                `;

                let startPage = Math.max(1, currentPage - 2);
                let endPage = Math.min(totalPages, startPage + 4);

                if (endPage - startPage < 4) {
                    startPage = Math.max(1, endPage - 4);
                }

                for (let i = startPage; i <= endPage; i++) {
                    paginationHtml += `
                        <li class="page-item ${i === currentPage ? 'active' : ''}">
                            <a class="page-link" href="#" data-page="${i}">${i}</a>
                        </li>
                    `;
                }

                paginationHtml += `
                    <li class="page-item ${currentPage === totalPages ? 'disabled' : ''}">
                        <a class="page-link" href="#" data-page="${currentPage + 1}" aria-label="Next">
                            <span aria-hidden="true">&raquo;</span>
                        </a>
                    </li>
                    <li class="page-item ${currentPage === totalPages ? 'disabled' : ''}">
                        <a class="page-link" href="#" data-page="${totalPages}" aria-label="Last">
                            <span aria-hidden="true">&raquo;&raquo;</span>
                        </a>
                    </li>
                `;
            }

            $('#pagination').html(paginationHtml);
        }

        // معالجة نقر أزرار التصفح
        $(document).on('click', '#pagination .page-link', function(e) {
            e.preventDefault();

            const page = $(this).data('page');

            if (page !== currentPage && page >= 1 && page <= totalPages) {
                currentPage = page;
                loadServices();
            }
        });

        // عرض تفاصيل الخدمة
        $(document).on('click', '.view-service', function() {
            const serviceId = $(this).data('id');

            $.ajax({
                url: '<?php echo API_URL; ?>services.php?action=view&id=' + serviceId,
                type: 'GET',
                dataType: 'json',
                success: function(response) {
                    if (response.status === 'success') {
                        const service = response.data;

                        // تعبئة بيانات الموديل
                        $('#modal-service-name').text(service.name);
                        $('#modal-service-category').text(service.category_name || '-');
                        $('#modal-service-duration').text(service.duration);
                        $('#modal-service-price').text(service.price + ' <?php echo $currencySymbol; ?>');
                        $('#modal-service-status').html(service.is_active == 1
                            ? '<span class="badge bg-success">نشط</span>'
                            : '<span class="badge bg-danger">غير نشط</span>');
                        $('#modal-service-branch').text(service.branch_name || 'جميع الفروع');
                        $('#modal-service-description').text(service.description || 'لا يوجد وصف');

                        // تعبئة الموظفين
                        let employeesList = '';
                        if (service.employees && service.employees.length > 0) {
                            $.each(service.employees, function(index, employee) {
                                employeesList += `<li class="list-group-item">${employee.name}</li>`;
                            });
                        } else {
                            employeesList = '<li class="list-group-item">لا يوجد موظفون مرتبطين بالخدمة</li>';
                        }

                        $('#modal-service-employees').html(employeesList);

                        // تعيين رابط التعديل
                        $('#btn-edit-service').attr('href', '<?php echo BASE_URL; ?>pages/services/edit.php?id=' + serviceId);

                        // عرض الموديل
                        $('#serviceDetailsModal').modal('show');
                    } else {
                        showToast('خطأ', response.message, 'error');
                    }
                },
                error: function() {
                    showToast('خطأ', 'حدث خطأ أثناء تحميل بيانات الخدمة', 'error');
                }
            });
        });

        // تهيئة موديل حذف الخدمة
        $(document).on('click', '.delete-service', function() {
            const serviceId = $(this).data('id');
            const serviceName = $(this).data('name');

            $('#delete-service-name').text(serviceName);
            $('#confirm-delete').data('id', serviceId);
            $('#deleteServiceModal').modal('show');
        });

        // تأكيد حذف الخدمة
        $('#confirm-delete').on('click', function() {
            const serviceId = $(this).data('id');

            $.ajax({
                url: '<?php echo API_URL; ?>services.php?action=delete',
                type: 'POST',
                data: { id: serviceId },
                dataType: 'json',
                success: function(response) {
                    $('#deleteServiceModal').modal('hide');

                    if (response.status === 'success') {
                        showToast('نجاح', response.message, 'success');
                        // إعادة تحميل الخدمات
                        loadServices();
                    } else {
                        showToast('خطأ', response.message, 'error');
                    }
                },
                error: function() {
                    $('#deleteServiceModal').modal('hide');
                    showToast('خطأ', 'حدث خطأ أثناء حذف الخدمة', 'error');
                }
            });
        });

        // عرض خطأ في حالة فشل تحميل البيانات
        function displayError(message) {
            $('#services-table tbody').html(`
                <tr>
                    <td colspan="<?php echo $isAdmin ? "8" : "7"; ?>" class="text-center text-danger">
                        <i class="fas fa-exclamation-circle me-2"></i> ${message}
                    </td>
                </tr>
            `);
            $('#pagination').html('');
            $('#total-services').text('0');
        }

        // عرض رسالة توست
        function showToast(title, message, type) {
            // التحقق من وجود مكتبة toastr
            if (typeof toastr !== 'undefined') {
                toastr.options = {
                    closeButton: true,
                    progressBar: true,
                    positionClass: 'toast-top-left',
                    timeOut: 3000
                };

                if (type === 'success') {
                    toastr.success(message, title);
                } else if (type === 'error') {
                    toastr.error(message, title);
                } else if (type === 'warning') {
                    toastr.warning(message, title);
                } else {
                    toastr.info(message, title);
                }
            } else {
                // استخدام تنبيه بديل إذا كانت مكتبة toastr غير متوفرة
                let alertClass = 'alert-info';
                if (type === 'success') alertClass = 'alert-success';
                if (type === 'error') alertClass = 'alert-danger';
                if (type === 'warning') alertClass = 'alert-warning';

                // إنشاء حاوية للتنبيهات إذا لم تكن موجودة
                if (!$('#alertContainer').length) {
                    $('<div id="alertContainer" class="alert-container position-fixed top-0 end-0 p-3" style="z-index: 1050;"></div>').appendTo('body');
                }

                // إنشاء عنصر التنبيه
                const alertId = 'alert-' + new Date().getTime();
                const alertHtml = `
                    <div id="${alertId}" class="alert ${alertClass} alert-dismissible fade show" role="alert">
                        <strong>${title}</strong> ${message}
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                `;

                // إضافة التنبيه إلى الحاوية
                $('#alertContainer').append(alertHtml);

                // إخفاء التنبيه تلقائيًا بعد 3 ثوانٍ
                setTimeout(function() {
                    $('#' + alertId).alert('close');
                }, 3000);
            }
        }
    };
</script>

<?php
// استدعاء قالب الذيل
include_once '../../includes/templates/footer.php';
?>
