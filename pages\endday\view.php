<?php
/**
 * صفحة عرض تفاصيل نهاية اليوم
 */

// تعريف ثابت لمنع الوصول المباشر للملفات المرتبطة
define('BASEPATH', true);

// استدعاء ملفات التهيئة
require_once '../../config/init.php';

// التحقق من الصلاحيات
requirePermission('endday_manage');

// عنوان الصفحة
$pageTitle = 'تفاصيل نهاية اليوم';

// إضافة أنماط CSS مخصصة
$customStyles = <<<CSS
<style>
    .table-responsive {
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
    }

    .table-bordered {
        border-collapse: collapse;
        width: 100%;
    }

    .table-bordered th,
    .table-bordered td {
        border: 1px solid #dee2e6;
        padding: 8px;
        vertical-align: middle;
    }

    .table-bordered th {
        background-color: #f8f9fa;
        font-weight: bold;
        text-align: center;
    }

    .info-card {
        border-radius: 8px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        margin-bottom: 20px;
    }

    .info-card .card-header {
        background-color: #f8f9fa;
        font-weight: 600;
        padding: 15px 20px;
    }

    .info-card .card-body {
        padding: 20px;
    }

    .info-item {
        margin-bottom: 15px;
    }

    .info-item .label {
        color: #6c757d;
        font-size: 0.9rem;
        margin-bottom: 5px;
    }

    .info-item .value {
        font-weight: 500;
        font-size: 1.1rem;
    }

    .summary-box {
        background-color: #f8f9fa;
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 15px;
    }

    .summary-box .title {
        color: #6c757d;
        font-size: 0.9rem;
        margin-bottom: 5px;
    }

    .summary-box .amount {
        font-weight: 600;
        font-size: 1.2rem;
    }

    @media screen and (max-width: 768px) {
        .table-responsive {
            width: 100%;
            margin-bottom: 15px;
            overflow-y: hidden;
            border: 1px solid #dee2e6;
        }

        .table-bordered {
            margin-bottom: 0;
        }
    }
</style>
CSS;

// إنشاء كائنات النماذج
$db = new Database();
$invoiceModel = new Invoice($db);
$expenseModel = new Expense($db);
$endDayModel = new EndDay($db);
$branchModel = new Branch($db);
$settingsModel = new Settings($db);

// الحصول على رمز العملة
$currencySymbol = $settingsModel->get('system_currency_symbol', 'ر.س');

// التحقق من وجود معرف نهاية اليوم
if (!isset($_GET['id']) || empty($_GET['id'])) {
    setErrorMessage('معرف نهاية اليوم غير صحيح');
    redirect('pages/endday/index.php');
}

$endDayId = intval($_GET['id']);

// الحصول على بيانات نهاية اليوم
$db->prepare("SELECT ed.*,
                     b.name as branch_name,
                     b.address as branch_address,
                     b.phone as branch_phone,
                     u.name as closed_by_name
              FROM end_days ed
              LEFT JOIN branches b ON ed.branch_id = b.id
              LEFT JOIN users u ON ed.closed_by = u.id
              WHERE ed.id = :id");
$db->bind(':id', $endDayId);
$endDay = $db->fetch();

// التحقق من وجود البيانات
if (!$endDay) {
    setErrorMessage('لم يتم العثور على بيانات نهاية اليوم');
    redirect('pages/endday/index.php');
}

// الحصول على الفواتير المرتبطة بنهاية اليوم
$db->prepare("SELECT i.*,
                     c.name as customer_name,
                     e.name as employee_name
              FROM invoices i
              LEFT JOIN customers c ON i.customer_id = c.id
              LEFT JOIN employees e ON i.employee_id = e.id
              WHERE i.end_day_id = :end_day_id
              AND i.payment_status = 'paid'
              ORDER BY i.created_at DESC");
$db->bind(':end_day_id', $endDayId);
$invoices = $db->fetchAll();

// الحصول على المصروفات المرتبطة بنهاية اليوم
$db->prepare("SELECT e.*, ec.name as category_name
              FROM expenses e
              LEFT JOIN expense_categories ec ON e.category_id = ec.id
              WHERE e.end_day_id = :end_day_id
              ORDER BY e.date DESC");
$db->bind(':end_day_id', $endDayId);
$expenses = $db->fetchAll();

// حساب إجمالي المبيعات والمصروفات
$totalSales = 0;
$totalExpenses = 0;
$cashAmount = 0;
$cardAmount = 0;
$otherAmount = 0;

foreach ($invoices as $invoice) {
    $totalSales += $invoice['final_amount'];

    switch ($invoice['payment_method']) {
        case 'cash':
            $cashAmount += $invoice['final_amount'];
            break;
        case 'card':
            $cardAmount += $invoice['final_amount'];
            break;
        default:
            $otherAmount += $invoice['final_amount'];
    }
}

foreach ($expenses as $expense) {
    $totalExpenses += $expense['amount'];
}

// حساب إجمالي الخصومات
$totalDiscounts = 0;
foreach ($invoices as $invoice) {
    $totalDiscounts += $invoice['discount_amount'];
}

// تحديث بيانات نهاية اليوم إذا كانت القيم صفرية
if (empty($endDay['total_sales']) && empty($endDay['total_expenses']) || empty($endDay['total_discounts'])) {
    // التأكد من أن المبالغ تعكس فقط الفواتير المدفوعة لليوم المحدد
    $db->prepare("UPDATE end_days
                  SET total_sales = :total_sales,
                      total_expenses = :total_expenses,
                      total_discounts = :total_discounts,
                      cash_amount = :cash_amount,
                      card_amount = :card_amount,
                      other_amount = :other_amount
                  WHERE id = :id");
    $db->bind(':id', $endDayId);
    $db->bind(':total_sales', $totalSales);
    $db->bind(':total_expenses', $totalExpenses);
    $db->bind(':total_discounts', $totalDiscounts);
    $db->bind(':cash_amount', $cashAmount);
    $db->bind(':card_amount', $cardAmount);
    $db->bind(':other_amount', $otherAmount);
    $db->execute();

    // تحديث البيانات المحلية
    $endDay['total_sales'] = $totalSales;
    $endDay['total_expenses'] = $totalExpenses;
    $endDay['total_discounts'] = $totalDiscounts;
    $endDay['cash_amount'] = $cashAmount;
    $endDay['card_amount'] = $cardAmount;
    $endDay['other_amount'] = $otherAmount;
}

// استدعاء رأس الصفحة
include '../../includes/templates/header.php';

// إضافة الأنماط المخصصة
echo $customStyles;
?>

<!-- المحتوى الرئيسي للصفحة -->
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2 class="h3 mb-0 text-gray-800">تفاصيل نهاية اليوم</h2>

        <div>
            <a href="index.php" class="btn btn-secondary">
                <i class="fas fa-arrow-right ml-1"></i> العودة للقائمة
            </a>
            <button class="btn btn-primary" onclick="window.print()">
                <i class="fas fa-print ml-1"></i> طباعة
            </button>
        </div>
    </div>

    <!-- بطاقة المعلومات الأساسية -->
    <div class="row">
        <div class="col-md-6">
            <div class="card info-card">
                <div class="card-header">
                    <i class="fas fa-info-circle ml-1"></i> معلومات نهاية اليوم
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="info-item">
                                <div class="label">الفرع</div>
                                <div class="value"><?php echo $endDay['branch_name']; ?></div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="info-item">
                                <div class="label">التاريخ</div>
                                <div class="value"><?php echo date('Y-m-d', strtotime($endDay['date'])); ?></div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="info-item">
                                <div class="label">وقت البدء</div>
                                <div class="value"><?php echo date('h:i A', strtotime($endDay['created_at'])); ?></div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="info-item">
                                <div class="label">وقت الإغلاق</div>
                                <div class="value">
                                    <?php if (!empty($endDay['closed_at'])): ?>
                                        <?php echo date('h:i A', strtotime($endDay['closed_at'])); ?>
                                    <?php else: ?>
                                        <span class="badge bg-warning">مفتوح</span>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                        <?php if (!empty($endDay['closed_by_name'])): ?>
                        <div class="col-md-6">
                            <div class="info-item">
                                <div class="label">تم الانشاء من قبل</div>
                                <div class="value"><?php echo $endDay['closed_by_name']; ?></div>
                            </div>
                        </div>
                        <?php endif; ?>
                        <?php if (!empty($endDay['notes'])): ?>
                        <div class="col-12">
                            <div class="info-item">
                                <div class="label">ملاحظات</div>
                                <div class="value"><?php echo $endDay['notes']; ?></div>
                            </div>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-6">
            <div class="card info-card">
                <div class="card-header">
                    <i class="fas fa-chart-pie ml-1"></i> ملخص المبيعات والمصروفات
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="summary-box">
                                <div class="title">إجمالي المبيعات</div>
                                <div class="amount text-primary"><?php echo number_format($endDay['total_sales'], 2) . ' ' . $currencySymbol; ?></div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="summary-box">
                                <div class="title">إجمالي المصروفات</div>
                                <div class="amount text-danger"><?php echo number_format($endDay['total_expenses'], 2) . ' ' . $currencySymbol; ?></div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="summary-box">
                                <div class="title">إجمالي الخصومات</div>
                                <div class="amount text-warning"><?php echo number_format($endDay['total_discounts'] ?? 0, 2) . ' ' . $currencySymbol; ?></div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="summary-box">
                                <div class="title">مدفوعات نقدية</div>
                                <div class="amount"><?php echo number_format($endDay['cash_amount'], 2) . ' ' . $currencySymbol; ?></div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="summary-box">
                                <div class="title">مدفوعات بطاقة</div>
                                <div class="amount"><?php echo number_format($endDay['card_amount'], 2) . ' ' . $currencySymbol; ?></div>
                            </div>
                        </div>
                        <?php if (!empty($endDay['other_amount'])): ?>
                        <div class="col-md-6">
                            <div class="summary-box">
                                <div class="title">مدفوعات أخرى</div>
                                <div class="amount"><?php echo number_format($endDay['other_amount'], 2) . ' ' . $currencySymbol; ?></div>
                            </div>
                        </div>
                        <?php endif; ?>
                        <div class="col-md-6">
                            <div class="summary-box">
                                <div class="title">صافي الربح</div>
                                <div class="amount <?php echo ($endDay['total_sales'] - $endDay['total_expenses'] >= 0) ? 'text-success' : 'text-danger'; ?>">
                                    <?php echo number_format($endDay['total_sales'] - $endDay['total_expenses'], 2) . ' ' . $currencySymbol; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- قائمة الفواتير -->
    <div class="card mt-4">
        <div class="card-header">
            <i class="fas fa-file-invoice ml-1"></i> الفواتير (<?php echo count($invoices); ?>)
        </div>
        <div class="card-body">
            <?php if (empty($invoices)): ?>
                <div class="alert alert-info">
                    <i class="fas fa-info-circle ml-1"></i> لا توجد فواتير لهذا اليوم
                </div>
            <?php else: ?>
                <div class="table-responsive">
                    <table class="table table-bordered table-hover">
                        <thead class="table-light">
                            <tr>
                                <th style="width: 80px;">رقم الفاتورة</th>
                                <th>العميل</th>
                                <th>الموظف</th>
                                <th>طريقة الدفع</th>
                                <th>المبلغ</th>
                                <th>الخصم</th>
                                <th>المبلغ النهائي</th>
                                <th>التاريخ</th>
                                <th style="width: 100px;">الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($invoices as $invoice): ?>
                                <tr>
                                    <td style="text-align: center;"><?php echo $invoice['id']; ?></td>
                                    <td><?php echo $invoice['customer_name'] ?: 'عميل غير مسجل'; ?></td>
                                    <td><?php echo $invoice['employee_name'] ?: '-'; ?></td>
                                    <td>
                                        <?php
                                        switch ($invoice['payment_method']) {
                                            case 'cash':
                                                echo '<span class="badge bg-success">نقدي</span>';
                                                break;
                                            case 'card':
                                                echo '<span class="badge bg-info">بطاقة</span>';
                                                break;
                                            default:
                                                echo '<span class="badge bg-secondary">أخرى</span>';
                                        }
                                        ?>
                                    </td>
                                    <td style="text-align: center;"><?php echo number_format($invoice['total_amount'], 2) . ' ' . $currencySymbol; ?></td>
                                    <td style="text-align: center;"><?php echo number_format($invoice['discount_amount'], 2) . ' ' . $currencySymbol; ?></td>
                                    <td style="text-align: center; font-weight: bold;"><?php echo number_format($invoice['final_amount'], 2) . ' ' . $currencySymbol; ?></td>
                                    <td style="text-align: center;"><?php echo date('h:i A', strtotime($invoice['created_at'])); ?></td>
                                    <td style="text-align: center;">
                                        <a href="../invoices/view.php?id=<?php echo $invoice['id']; ?>" class="btn btn-sm btn-primary" title="عرض التفاصيل">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="../invoices/print.php?id=<?php echo $invoice['id']; ?>" class="btn btn-sm btn-secondary" title="طباعة" target="_blank">
                                            <i class="fas fa-print"></i>
                                        </a>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                        <tfoot>
                            <tr class="table-light">
                                <th colspan="4" style="text-align: left;">الإجمالي</th>
                                <th style="text-align: center;"><?php echo number_format(array_sum(array_column($invoices, 'total_amount')), 2) . ' ' . $currencySymbol; ?></th>
                                <th style="text-align: center;"><?php echo number_format(array_sum(array_column($invoices, 'discount_amount')), 2) . ' ' . $currencySymbol; ?></th>
                                <th style="text-align: center;"><?php echo number_format(array_sum(array_column($invoices, 'final_amount')), 2) . ' ' . $currencySymbol; ?></th>
                                <th colspan="2"></th>
                            </tr>
                        </tfoot>
                    </table>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- قائمة المصروفات -->
    <div class="card mt-4">
        <div class="card-header">
            <i class="fas fa-money-bill-wave ml-1"></i> المصروفات (<?php echo count($expenses); ?>)
        </div>
        <div class="card-body">
            <?php if (empty($expenses)): ?>
                <div class="alert alert-info">
                    <i class="fas fa-info-circle ml-1"></i> لا توجد مصروفات لهذا اليوم
                </div>
            <?php else: ?>
                <div class="table-responsive">
                    <table class="table table-bordered table-hover">
                        <thead class="table-light">
                            <tr>
                                <th style="width: 80px;">رقم المصروف</th>
                                <th>الفئة</th>
                                <th>الوصف</th>
                                <th>المبلغ</th>
                                <th>التاريخ</th>
                                <th style="width: 100px;">الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($expenses as $expense): ?>
                                <tr>
                                    <td style="text-align: center;"><?php echo $expense['id']; ?></td>
                                    <td><?php echo $expense['category_name']; ?></td>
                                    <td><?php echo $expense['description']; ?></td>
                                    <td style="text-align: center; font-weight: bold;"><?php echo number_format($expense['amount'], 2) . ' ' . $currencySymbol; ?></td>
                                    <td style="text-align: center;"><?php echo date('h:i A', strtotime($expense['date'])); ?></td>
                                    <td style="text-align: center;">
                                        <a href="../expenses/view.php?id=<?php echo $expense['id']; ?>" class="btn btn-sm btn-primary" title="عرض التفاصيل">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                        <tfoot>
                            <tr class="table-light">
                                <th colspan="3" style="text-align: left;">الإجمالي</th>
                                <th style="text-align: center;"><?php echo number_format(array_sum(array_column($expenses, 'amount')), 2) . ' ' . $currencySymbol; ?></th>
                                <th colspan="2"></th>
                            </tr>
                        </tfoot>
                    </table>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- نافذة تأكيد إغلاق نهاية اليوم -->
<?php if (empty($endDay['closed_at']) && hasPermission('endday_close')): ?>
<div class="modal fade" id="closeEndDayModal" tabindex="-1" aria-labelledby="closeEndDayModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="closeEndDayModalLabel">إغلاق نهاية اليوم</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="closeEndDayForm">
                <div class="modal-body">
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle ml-1"></i>
                        هل أنت متأكد من إغلاق نهاية اليوم؟ لن تتمكن من إضافة فواتير جديدة لهذا اليوم بعد الإغلاق.
                    </div>
                    <div class="mb-3">
                        <label for="notes" class="form-label">ملاحظات (اختياري)</label>
                        <textarea class="form-control" id="notes" name="notes" rows="3"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-danger">إغلاق نهاية اليوم</button>
                </div>
            </form>
        </div>
    </div>
</div>
<?php endif; ?>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        <?php if (empty($endDay['closed_at']) && hasPermission('endday_close')): ?>
        // إغلاق نهاية اليوم
        document.getElementById('closeEndDayForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const notes = document.getElementById('notes').value;

            $.ajax({
                url: '../../api/endday.php?action=close',
                type: 'POST',
                data: {
                    branch_id: <?php echo $endDay['branch_id']; ?>,
                    notes: notes
                },
                dataType: 'json',
                success: function(response) {
                    if (response.status === 'success') {
                        showAlert('success', response.message);
                        setTimeout(function() {
                            location.reload();
                        }, 1500);
                    } else {
                        showAlert('danger', response.message);
                    }
                },
                error: function(xhr) {
                    let errorMessage = 'حدث خطأ أثناء معالجة الطلب';
                    try {
                        const response = JSON.parse(xhr.responseText);
                        if (response.message) {
                            errorMessage = response.message;
                        }
                    } catch (e) {}
                    showAlert('danger', errorMessage);
                }
            });
        });
        <?php endif; ?>

        // دالة لعرض التنبيهات
        function showAlert(type, message) {
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
            alertDiv.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            `;

            document.querySelector('.container-fluid').prepend(alertDiv);

            // إخفاء التنبيه بعد 5 ثوان
            setTimeout(function() {
                alertDiv.classList.remove('show');
                setTimeout(function() {
                    alertDiv.remove();
                }, 150);
            }, 5000);
        }
    });
</script>

<?php
// استدعاء ذيل الصفحة
include '../../includes/templates/footer.php';
?>