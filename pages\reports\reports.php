<?php
/**
 * صفحة التقارير الشاملة
 * تعرض تقارير المبيعات والأرباح والخسائر والمصروفات وصافي الربح
 */

// تعريف ثابت لمنع الوصول المباشر للملفات المرتبطة
define('BASEPATH', true);

// استدعاء ملفات التهيئة
require_once '../../config/init.php';

// التحقق من تسجيل الدخول
redirectIfNotLoggedIn();

// التحقق من الصلاحيات - يجب أن يكون لديه صلاحية عرض التقارير
if (!hasPermission('reports_sales') && !hasPermission('reports_expenses')) {
    $_SESSION['error_message'] = 'ليس لديك صلاحية لعرض التقارير الشاملة';
    header('Location: ' . BASE_URL . 'pages/dashboard.php');
    exit;
}

// تعيين عنوان الصفحة
$pageTitle = 'التقارير الشاملة';

// استدعاء قالب الهيدر
include_once '../../includes/templates/header.php';
include_once '../../includes/templates/sidebar.php';

// الحصول على قائمة الفروع للمستخدم الحالي
$branchModel = new Branch($db);
$branches = [];

// إذا كان المستخدم مدير، يمكنه رؤية جميع الفروع
if (isAdmin()) {
    $branches = $branchModel->getBranches();
} else {
    // للمشرفين والموظفين، يمكنهم رؤية الفرع الخاص بهم فقط
    // الحصول على الفرع الخاص بالمستخدم الحالي
    $userBranchId = $_SESSION['user_branch_id'];
    if ($userBranchId) {
        $branch = $branchModel->getBranchById($userBranchId);
        if ($branch) {
            $branches = [$branch];
        }
    }
}
?>

<!-- المحتوى الرئيسي للصفحة -->
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- بطاقة فلاتر التقرير -->
            <div class="card shadow-sm mb-4 border-0">
                <div class="card-header bg-white py-3 border-0">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-filter me-2 text-primary"></i>
                            فلاتر التقرير
                        </h5>
                    </div>
                </div>
                <div class="card-body">
                    <form id="report-filters-form">
                        <div class="row g-3 align-items-end">
                            <!-- اختيار الفترة الزمنية -->
                            <div class="col-md-4 mb-3">
                                <label for="date-range" class="form-label fw-bold">
                                    <i class="fas fa-calendar-alt me-1 text-primary"></i>
                                    الفترة الزمنية
                                </label>
                                <select class="form-select form-select-lg shadow-sm" id="date-range">
                                    <option value="today">اليوم</option>
                                    <option value="yesterday">الأمس</option>
                                    <option value="this_week">هذا الأسبوع</option>
                                    <option value="last_week">الأسبوع الماضي</option>
                                    <option value="this_month" selected>هذا الشهر</option>
                                    <option value="last_month">الشهر الماضي</option>
                                    <option value="this_year">هذه السنة</option>
                                    <option value="last_year">السنة الماضية</option>
                                    <option value="custom">فترة مخصصة</option>
                                </select>
                            </div>

                            <!-- تاريخ البداية والنهاية (للفترة المخصصة) -->
                            <div class="col-md-4 mb-3 custom-date-range" style="display: none;">
                                <label for="start-date" class="form-label fw-bold">
                                    <i class="fas fa-calendar-day me-1 text-primary"></i>
                                    تاريخ البداية
                                </label>
                                <input type="date" class="form-control form-control-lg shadow-sm" id="start-date">
                            </div>
                            <div class="col-md-4 mb-3 custom-date-range" style="display: none;">
                                <label for="end-date" class="form-label fw-bold">
                                    <i class="fas fa-calendar-day me-1 text-primary"></i>
                                    تاريخ النهاية
                                </label>
                                <input type="date" class="form-control form-control-lg shadow-sm" id="end-date">
                            </div>

                            <!-- اختيار الفرع -->
                            <div class="col-md-4 mb-3">
                                <label for="branch-id" class="form-label fw-bold">
                                    <i class="fas fa-store me-1 text-primary"></i>
                                    الفرع
                                </label>
                                <select class="form-select form-select-lg shadow-sm" id="branch-id">
                                    <?php if (isAdmin()): ?>
                                        <option value="">جميع الفروع</option>
                                    <?php endif; ?>

                                    <?php foreach ($branches as $branch): ?>
                                        <option value="<?= $branch['id'] ?>" <?= ($_SESSION['user_branch_id'] == $branch['id']) ? 'selected' : '' ?>>
                                            <?= $branch['name'] ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>

                            <!-- زر عرض التقرير -->
                            <div class="col-md-12 text-end mt-4">
                                <button type="button" id="generate-report" class="btn btn-primary btn-lg px-4 shadow-sm">
                                    <i class="fas fa-chart-bar me-2"></i> عرض التقرير
                                </button>
                                <button type="button" id="export-excel" class="btn btn-success btn-lg px-4 shadow-sm ms-2" disabled>
                                    <i class="fas fa-file-excel me-2"></i> تصدير إلى Excel
                                </button>
                                <button type="button" id="print-report" class="btn btn-secondary btn-lg px-4 shadow-sm ms-2" disabled>
                                    <i class="fas fa-print me-2"></i> طباعة
                                </button>
                                <button type="button" id="send-report" class="btn btn-info btn-lg px-4 shadow-sm ms-2" disabled>
                                    <i class="fab fa-whatsapp me-2"></i> إرسال للمدراء
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- بطاقة نتائج التقرير -->
            <div class="card shadow-sm mb-4 border-0">
                <div class="card-header bg-white py-3 border-0">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-chart-pie me-2 text-success"></i>
                            نتائج التقرير
                        </h5>
                    </div>
                </div>
                <div class="card-body">
                    <!-- ملخص التقرير -->
                    <div id="report-summary" class="row mb-4" style="display: none;">
                        <!-- إجمالي المبيعات -->
                        <div class="col-md-3 col-sm-6 mb-3">
                            <div class="card bg-primary text-white shadow-sm border-0 h-100">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <h6 class="card-title mb-2">إجمالي المبيعات</h6>
                                            <h3 id="total-sales" class="mb-0 display-6 fw-bold">0</h3>
                                        </div>
                                        <div class="fs-1 opacity-75">
                                            <i class="fas fa-shopping-cart"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- إجمالي الفواتير المدفوعة جزئياً -->
                        <div class="col-md-3 col-sm-6 mb-3">
                            <div class="card bg-warning text-white shadow-sm border-0 h-100">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <h6 class="card-title mb-2">الفواتير المدفوعة جزئياً</h6>
                                            <h3 id="total-partial-paid" class="mb-0 display-6 fw-bold">0</h3>
                                        </div>
                                        <div class="fs-1 opacity-75">
                                            <i class="fas fa-file-invoice-dollar"></i>
                                        </div>
                                    </div>
                                </div>
                                <a href="javascript:void(0)" id="view-partial-paid-invoices" class="card-footer text-white text-center text-decoration-none bg-opacity-25 bg-dark">
                                    <i class="fas fa-eye me-1"></i> عرض التفاصيل
                                </a>
                            </div>
                        </div>

                        <!-- إجمالي المصروفات -->
                        <div class="col-md-3 col-sm-6 mb-3">
                            <div class="card bg-danger text-white shadow-sm border-0 h-100">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <h6 class="card-title mb-2">إجمالي المصروفات</h6>
                                            <h3 id="total-expenses" class="mb-0 display-6 fw-bold">0</h3>
                                        </div>
                                        <div class="fs-1 opacity-75">
                                            <i class="fas fa-money-bill-wave"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- إجمالي الخصومات -->
                        <div class="col-md-3 col-sm-6 mb-3">
                            <div class="card bg-purple text-white shadow-sm border-0 h-100">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <h6 class="card-title mb-2">إجمالي الخصومات</h6>
                                            <h3 id="total-discounts" class="mb-0 display-6 fw-bold">0</h3>
                                        </div>
                                        <div class="fs-1 opacity-75">
                                            <i class="fas fa-percent"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- إجمالي الرواتب -->
                        <div class="col-md-3 col-sm-6 mb-3">
                            <div class="card bg-info text-white shadow-sm border-0 h-100">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <h6 class="card-title mb-2">إجمالي الرواتب</h6>
                                            <h3 id="total-salaries" class="mb-0 display-6 fw-bold">0</h3>
                                        </div>
                                        <div class="fs-1 opacity-75">
                                            <i class="fas fa-user-tie"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- إجمالي الأرباح -->
                        <div class="col-md-6 col-sm-12 mb-3">
                            <div class="card bg-success text-white shadow-sm border-0 h-100">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <h6 class="card-title mb-2">إجمالي الأرباح</h6>
                                            <h3 id="total-profits" class="mb-0 display-6 fw-bold">0</h3>
                                        </div>
                                        <div class="fs-1 opacity-75">
                                            <i class="fas fa-chart-line"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- صافي الربح -->
                        <div class="col-md-6 col-sm-12 mb-3">
                            <div class="card bg-info text-white shadow-sm border-0 h-100">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <h6 class="card-title mb-2">صافي الربح (بعد خصم الرواتب)</h6>
                                            <h3 id="net-profit" class="mb-0 display-6 fw-bold">0</h3>
                                        </div>
                                        <div class="fs-1 opacity-75">
                                            <i class="fas fa-hand-holding-usd"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- مخططات التقرير -->
                    <div class="row mb-4">
                        <div class="col-md-6 mb-3">
                            <div class="card shadow-sm border-0 h-100">
                                <div class="card-header bg-white py-3 border-0">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <h5 class="mb-0 text-primary">
                                            <i class="fas fa-chart-bar me-2"></i>
                                            المبيعات والمصروفات والرواتب
                                        </h5>
                                    </div>
                                </div>
                                <div class="card-body pt-0">
                                    <div class="chart-container" style="height: 300px;">
                                        <canvas id="sales-expenses-chart"></canvas>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <div class="card shadow-sm border-0 h-100">
                                <div class="card-header bg-white py-3 border-0">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <h5 class="mb-0 text-success">
                                            <i class="fas fa-chart-line me-2"></i>
                                            الأرباح والخسائر
                                        </h5>
                                    </div>
                                </div>
                                <div class="card-body pt-0">
                                    <div class="chart-container" style="height: 300px;">
                                        <canvas id="profit-chart"></canvas>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- تفاصيل التقرير -->
                    <div class="row">
                        <div class="col-12">
                            <ul class="nav nav-tabs nav-tabs-custom" id="report-tabs" role="tablist">
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link active" id="daily-tab" data-bs-toggle="tab" data-bs-target="#daily" type="button" role="tab" aria-controls="daily" aria-selected="true">
                                        <i class="fas fa-calendar-day me-2"></i>
                                        التقرير اليومي
                                    </button>
                                </li>
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link" id="payment-methods-tab" data-bs-toggle="tab" data-bs-target="#payment-methods" type="button" role="tab" aria-controls="payment-methods" aria-selected="false">
                                        <i class="fas fa-credit-card me-2"></i>
                                        طرق الدفع
                                    </button>
                                </li>
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link" id="expenses-tab" data-bs-toggle="tab" data-bs-target="#expenses" type="button" role="tab" aria-controls="expenses" aria-selected="false">
                                        <i class="fas fa-money-bill-wave me-2"></i>
                                        المصروفات
                                    </button>
                                </li>
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link" id="salaries-tab" data-bs-toggle="tab" data-bs-target="#salaries" type="button" role="tab" aria-controls="salaries" aria-selected="false">
                                        <i class="fas fa-user-tie me-2"></i>
                                        الرواتب
                                    </button>
                                </li>
                            </ul>
                            <div class="tab-content p-3 border border-top-0 rounded-bottom" id="report-tabs-content">
                                <!-- التقرير اليومي -->
                                <div class="tab-pane fade show active" id="daily" role="tabpanel" aria-labelledby="daily-tab">
                                    <div class="table-responsive">
                                        <table id="daily-report-table" class="table table-striped table-hover">
                                            <thead>
                                                <tr>
                                                    <th>التاريخ</th>
                                                    <th>المبيعات</th>
                                                    <th>المصروفات</th>
                                                    <th>الرواتب</th>
                                                    <th>الربح قبل الرواتب</th>
                                                    <th>صافي الربح/الخسارة</th>
                                                    <th>الإجراءات</th>
                                                </tr>
                                            </thead>
                                            <tbody id="daily-report-body">
                                                <!-- سيتم ملء البيانات هنا عبر جافاسكريبت -->
                                            </tbody>
                                            <tfoot>
                                                <tr class="table-dark">
                                                    <th>الإجمالي</th>
                                                    <th id="total-sales-sum">0</th>
                                                    <th id="total-expenses-sum">0</th>
                                                    <th id="total-salaries-sum">0</th>
                                                    <th id="total-profit-sum">0</th>
                                                    <th id="total-net-profit-sum">0</th>
                                                    <th></th>
                                                </tr>
                                            </tfoot>
                                        </table>
                                    </div>
                                </div>

                                <!-- طرق الدفع -->
                                <div class="tab-pane fade" id="payment-methods" role="tabpanel" aria-labelledby="payment-methods-tab">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="chart-container">
                                                <canvas id="payment-methods-chart"></canvas>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="table-responsive">
                                                <table id="payment-methods-table" class="table table-striped table-hover">
                                                    <thead>
                                                        <tr>
                                                            <th>طريقة الدفع</th>
                                                            <th>المبلغ</th>
                                                            <th>النسبة</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody id="payment-methods-body">
                                                        <!-- سيتم ملء البيانات هنا عبر جافاسكريبت -->
                                                    </tbody>
                                                </table>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- المصروفات -->
                                <div class="tab-pane fade" id="expenses" role="tabpanel" aria-labelledby="expenses-tab">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="chart-container">
                                                <canvas id="expenses-categories-chart"></canvas>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="table-responsive">
                                                <table id="expenses-categories-table" class="table table-striped table-hover">
                                                    <thead>
                                                        <tr>
                                                            <th>الفئة</th>
                                                            <th>المبلغ</th>
                                                            <th>النسبة</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody id="expenses-categories-body">
                                                        <!-- سيتم ملء البيانات هنا عبر جافاسكريبت -->
                                                    </tbody>
                                                </table>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- الرواتب -->
                                <div class="tab-pane fade" id="salaries" role="tabpanel" aria-labelledby="salaries-tab">
                                    <div class="row mb-3">
                                        <div class="col-12">
                                            <div class="alert alert-info">
                                                <i class="fas fa-info-circle me-2"></i>
                                                يعرض هذا القسم الرواتب المدفوعة خلال الفترة المحددة. يتم خصم هذه الرواتب من إجمالي الأرباح للحصول على صافي الربح.
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-12">
                                            <div class="table-responsive">
                                                <table id="salaries-table" class="table table-striped table-hover">
                                                    <thead>
                                                        <tr>
                                                            <th>الموظف</th>
                                                            <th>التاريخ</th>
                                                            <th>المبلغ</th>
                                                            <th>الملاحظات</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody id="salaries-body">
                                                        <!-- سيتم ملء البيانات هنا عبر جافاسكريبت -->
                                                    </tbody>
                                                    <tfoot>
                                                        <tr class="table-dark">
                                                            <th colspan="2">الإجمالي</th>
                                                            <th id="salaries-total">0</th>
                                                            <th></th>
                                                        </tr>
                                                    </tfoot>
                                                </table>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
// استدعاء قالب الفوتر لتحميل jQuery والمكتبات الأساسية
include_once '../../includes/templates/footer.php';
?>

<!-- استدعاء مكتبة Chart.js للرسوم البيانية -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<!-- استدعاء مكتبة SheetJS للتصدير إلى Excel -->
<script src="https://cdn.jsdelivr.net/npm/xlsx@0.18.5/dist/xlsx.full.min.js"></script>
<!-- استدعاء ملف AjaxHandler للتعامل مع طلبات AJAX -->
<script src="<?= BASE_URL ?>assets/js/ajax-handler.js"></script>
<!-- تعريف متغيرات جافاسكريبت للمسارات -->
<script>
    // تعريف مسار API مباشرة
    var API_URL = '<?= BASE_URL ?>api/reports.php';
    console.log('API URL defined as:', API_URL);
</script>

<!-- استدعاء ملف جافاسكريبت خاص بالتقارير -->
<script src="<?= BASE_URL ?>assets/js/comprehensive-reports.js"></script>

<!-- استدعاء ملف جافاسكريبت خاص بإشعارات واتساب للتقارير -->
<script src="<?= BASE_URL ?>assets/js/whatsapp-report-notification.js"></script>

<!-- استدعاء ملف جافاسكريبت خاص بإرسال التقارير مباشرة عبر واتساب -->
<script src="<?= BASE_URL ?>assets/js/whatsapp-direct-report-notification.js"></script>

<!-- إضافة الأنماط المخصصة -->
<style>
    /* تحسين شكل التبويبات */
    .nav-tabs-custom {
        border-bottom: 1px solid #dee2e6;
    }

    .nav-tabs-custom .nav-link {
        border: none;
        color: #6c757d;
        padding: 1rem 1.5rem;
        font-weight: 600;
        border-bottom: 3px solid transparent;
        transition: all 0.3s;
    }

    .nav-tabs-custom .nav-link:hover {
        color: #007bff;
        border-bottom-color: rgba(0, 123, 255, 0.3);
    }

    .nav-tabs-custom .nav-link.active {
        color: #007bff;
        background-color: transparent;
        border-bottom-color: #007bff;
    }

    /* تحسين شكل الجداول */
    .table {
        border-collapse: separate;
        border-spacing: 0;
    }

    .table thead th {
        background-color: #f8f9fa;
        border-top: none;
        border-bottom: 2px solid #dee2e6;
        font-weight: 600;
        text-transform: uppercase;
        font-size: 0.8rem;
        padding: 1rem;
    }

    .table tbody td {
        padding: 1rem;
        vertical-align: middle;
    }

    .table-striped tbody tr:nth-of-type(odd) {
        background-color: rgba(0, 0, 0, 0.02);
    }

    .table-hover tbody tr:hover {
        background-color: rgba(0, 123, 255, 0.05);
    }

    /* تحسين شكل الأزرار */
    .btn {
        border-radius: 4px;
        font-weight: 600;
        transition: all 0.3s;
    }

    /* تحسين شكل البطاقات */
    .card {
        border-radius: 8px;
        overflow: hidden;
        transition: all 0.3s;
    }

    .card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1) !important;
    }

    .card-header {
        font-weight: 600;
    }

    /* تحسين شكل المحتوى */
    .tab-content {
        background-color: #fff;
        border-radius: 0 0 8px 8px;
    }

    /* تحسين شكل المخططات */
    .chart-container {
        position: relative;
        height: 300px;
        width: 100%;
    }

    /* لون بنفسجي لكارد الخصومات */
    .bg-purple {
        background-color: #8e44ad !important;
    }
</style>
