<?php
/**
 * نقاط النهاية الخاصة بالمصادقة والتوثيق
 */

// تأكيد تحميل الملفات الأساسية
require_once __DIR__ . '/../config/init.php';

// التحقق من نوع الطلب (POST أو GET)
$action = isset($_GET['action']) ? $_GET['action'] : '';

// معالجة الطلبات
try {
    // إعداد رأس الاستجابة
    header('Content-Type: application/json');
    
    // إنشاء كائن قاعدة البيانات
    $db = new Database();
    $userModel = new User($db);
    
    switch ($action) {
        // تسجيل الدخول
        case 'login':
            // التأكد من أن الطلب عبر POST
            if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
                throw new Exception('الطريقة غير مسموح بها', 405);
            }
            
            // استخراج البيانات
            $username = input('username');
            $password = input('password');
            
            // التحقق من صحة المدخلات
            if (empty($username) || empty($password)) {
                throw new Exception('اسم المستخدم وكلمة المرور مطلوبان', 400);
            }
            
            // محاولة تسجيل الدخول
            $user = $userModel->login($username, $password);
            
            if ($user) {
                // إنشاء جلسة للمستخدم
                $_SESSION['user_id'] = $user['id'];
                $_SESSION['user_name'] = $user['name'];
                $_SESSION['user_role'] = $user['role'];
                $_SESSION['user_branch_id'] = $user['branch_id'];
                $_SESSION['user_permissions'] = $user['permissions'];
                $_SESSION['last_activity'] = time();
                
                // إعداد الاستجابة
                $response = [
                    'status' => 'success',
                    'message' => 'تم تسجيل الدخول بنجاح',
                    'user' => [
                        'id' => $user['id'],
                        'name' => $user['name'],
                        'username' => $user['username'],
                        'role' => $user['role'],
                        'branch_id' => $user['branch_id']
                    ]
                ];
                
                echo json_encode($response);
            } else {
                throw new Exception('اسم المستخدم أو كلمة المرور غير صحيحة', 401);
            }
            break;
        
        // تسجيل الخروج
        case 'logout':
            // التأكد من تسجيل الدخول
            if (!isLoggedIn()) {
                throw new Exception('يجب تسجيل الدخول أولاً', 401);
            }
            
            // تدمير الجلسة
            session_unset();
            session_destroy();
            
            echo json_encode([
                'status' => 'success',
                'message' => 'تم تسجيل الخروج بنجاح'
            ]);
            break;
        
        // تغيير كلمة المرور
        case 'change-password':
            // التأكد من تسجيل الدخول
            if (!isLoggedIn()) {
                throw new Exception('يجب تسجيل الدخول أولاً', 401);
            }
            
            // التأكد من أن الطلب عبر POST
            if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
                throw new Exception('الطريقة غير مسموح بها', 405);
            }
            
            // استخراج البيانات
            $currentPassword = input('current_password');
            $newPassword = input('new_password');
            $confirmPassword = input('confirm_password');
            
            // التحقق من صحة المدخلات
            if (empty($currentPassword) || empty($newPassword) || empty($confirmPassword)) {
                throw new Exception('جميع الحقول مطلوبة', 400);
            }
            
            // التحقق من تطابق كلمة المرور الجديدة
            if ($newPassword !== $confirmPassword) {
                throw new Exception('كلمة المرور الجديدة غير متطابقة', 400);
            }
            
            // التحقق من كلمة المرور الحالية
            $currentUser = $userModel->getUserById($_SESSION['user_id']);
            
            if (!password_verify($currentPassword, $currentUser['password'])) {
                throw new Exception('كلمة المرور الحالية غير صحيحة', 401);
            }
            
            // تغيير كلمة المرور
            $userModel->changePassword($_SESSION['user_id'], $newPassword);
            
            echo json_encode([
                'status' => 'success',
                'message' => 'تم تغيير كلمة المرور بنجاح'
            ]);
            break;
        
        // استرجاع بيانات المستخدم الحالي
        case 'current-user':
            // التأكد من تسجيل الدخول
            if (!isLoggedIn()) {
                throw new Exception('يجب تسجيل الدخول أولاً', 401);
            }
            
            // استرجاع بيانات المستخدم
            $user = $userModel->getUserById($_SESSION['user_id']);
            
            if (!$user) {
                throw new Exception('لم يتم العثور على بيانات المستخدم', 404);
            }
            
            echo json_encode([
                'status' => 'success',
                'user' => [
                    'id' => $user['id'],
                    'name' => $user['name'],
                    'username' => $user['username'],
                    'email' => $user['email'],
                    'role' => $user['role'],
                    'branch_id' => $user['branch_id'],
                    'branch_name' => $user['branch_name'],
                    'permissions' => $user['permissions']
                ]
            ]);
            break;
        
        // إعادة تعيين كلمة المرور
        case 'reset-password':
            // التأكد من أن الطلب عبر POST
            if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
                throw new Exception('الطريقة غير مسموح بها', 405);
            }
            
            // استخراج البيانات
            $username = input('username');
            
            // التحقق من وجود اسم المستخدم
            if (empty($username)) {
                throw new Exception('اسم المستخدم مطلوب', 400);
            }
            
            // البحث عن المستخدم
            $this->db->prepare("SELECT id FROM users WHERE username = :username");
            $this->db->bind(':username', $username);
            $user = $this->db->fetch();
            
            if (!$user) {
                throw new Exception('لم يتم العثور على المستخدم', 404);
            }
            
            // توليد كلمة مرور مؤقتة
            $tempPassword = generateRandomPassword(10);
            
            // تحديث كلمة المرور
            $userModel->changePassword($user['id'], $tempPassword);
            
            // يمكن إضافة إرسال كلمة المرور المؤقتة عبر البريد الإلكتروني هنا
            
            echo json_encode([
                'status' => 'success',
                'message' => 'تم إعادة تعيين كلمة المرور. يرجى تغييرها عند تسجيل الدخول'
            ]);
            break;
        
        // الافتراضي: إجراء غير معروف
        default:
            throw new Exception('إجراء غير معروف', 404);
    }
} catch (Exception $e) {
    // معالجة الأخطاء
    http_response_code($e->getCode() ?: 500);
    
    echo json_encode([
        'status' => 'error',
        'message' => $e->getMessage(),
        'code' => $e->getCode() ?: 500
    ]);
}