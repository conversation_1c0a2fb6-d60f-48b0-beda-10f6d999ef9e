<?php
/**
 * صفحة طباعة الفاتورة في نظام نقطة البيع
 * تستخدم للطباعة المباشرة للفواتير من خلال نقطة البيع
 */

// تعريف ثابت لمنع الوصول المباشر للملفات المرتبطة
define('BASEPATH', true);

// استدعاء ملفات التهيئة
require_once '../../config/init.php';

// التحقق من تسجيل الدخول
redirectIfNotLoggedIn();

// التحقق من صلاحية الطباعة
if (!hasPermission('invoices_print')) {
    $_SESSION['error_message'] = 'ليس لديك صلاحية طباعة الفواتير';
    header('Location: ' . BASE_URL . 'pages/dashboard.php');
    exit;
}

// التحقق من وجود معرف الفاتورة
$invoiceId = isset($_GET['id']) ? intval($_GET['id']) : 0;

if (!$invoiceId) {
    $_SESSION['error_message'] = 'لم يتم تحديد الفاتورة المطلوبة';
    header('Location: ' . BASE_URL . 'pages/invoices/index.php');
    exit;
}

// إنشاء كائن نموذج الفاتورة
$invoiceModel = new Invoice($db);

// استرجاع بيانات الفاتورة
$invoice = $invoiceModel->getInvoiceById($invoiceId);

// التحقق من وجود الفاتورة
if (!$invoice) {
    $_SESSION['error_message'] = 'الفاتورة غير موجودة أو محذوفة';
    header('Location: ' . BASE_URL . 'pages/invoices/index.php');
    exit;
}

// استرجاع عناصر الفاتورة
$invoiceItems = $invoiceModel->getInvoiceItems($invoiceId);

// استرجاع بيانات الفرع
$branchModel = new Branch($db);
$branch = $branchModel->getBranchById($invoice['branch_id']);

// تحديد نوع الطباعة
$printType = isset($_GET['type']) ? $_GET['type'] : 'normal';
$printMode = isset($_GET['mode']) ? $_GET['mode'] : 'preview';

// استرجاع إعدادات الفاتورة
$settingsModel = new Settings($db);
$invoiceSettings = [];
$allSettings = $settingsModel->getAllSettings();

// فرز إعدادات الفاتورة
foreach ($allSettings as $key => $value) {
    if (strpos($key, 'invoice_') === 0) {
        $shortKey = str_replace('invoice_', '', $key);
        $invoiceSettings[$shortKey] = $value;
    }
}

// تحضير البيانات للطباعة
$companyName = isset($invoiceSettings['company_name']) && !empty($invoiceSettings['company_name']) ? $invoiceSettings['company_name'] : SYSTEM_NAME;
$branchName = isset($branch['name']) ? $branch['name'] : 'الفرع الرئيسي';
$branchAddress = isset($branch['address']) ? $branch['address'] : '';
$branchPhone = isset($branch['phone']) ? $branch['phone'] : '';
$companyLogo = isset($invoiceSettings['company_logo']) && !empty($invoiceSettings['company_logo']) ? BASE_URL . $invoiceSettings['company_logo'] : '';
$invoiceCopies = isset($invoiceSettings['copies']) && !empty($invoiceSettings['copies']) ? intval($invoiceSettings['copies']) : 1;
$logoWidth = isset($invoiceSettings['logo_width']) && !empty($invoiceSettings['logo_width']) ? intval($invoiceSettings['logo_width']) : 200;
$logoHeight = isset($invoiceSettings['logo_height']) && !empty($invoiceSettings['logo_height']) ? intval($invoiceSettings['logo_height']) : 100;
$logoPrintQuality = isset($invoiceSettings['logo_print_quality']) ? $invoiceSettings['logo_print_quality'] : 'normal';

// حساب إجماليات الخدمات والمنتجات
$servicesTotal = 0;
$productsTotal = 0;

foreach ($invoiceItems as $item) {
    if ($item['item_type'] === 'service') {
        $servicesTotal += $item['total'];
    } else {
        $productsTotal += $item['total'];
    }
}

// إذا كان نوع الطباعة هو API JSON
if ($printType === 'json') {
    header('Content-Type: application/json');

    // تجميع بيانات الفاتورة بتنسيق JSON
    $jsonData = [
        'invoice' => [
            'id' => $invoice['id'],
            'invoice_number' => $invoice['invoice_number'],
            'created_at' => $invoice['created_at'],
            'customer_name' => $invoice['customer_name'] ?? 'عميل غير مسجل',
            'customer_phone' => $invoice['customer_phone'] ?? '',
            'employee_name' => $invoice['employee_name'] ?? '',
            'cashier_name' => $invoice['cashier_name'] ?? '',
            'branch_name' => $branchName,
            'branch_address' => $branchAddress,
            'branch_phone' => $branchPhone,
            'system_name' => $companyName,
            'payment_method' => $invoice['payment_method'],
            'payment_status' => $invoice['payment_status'],
            'total_amount' => (float)$invoice['total_amount'],
            'discount_amount' => (float)$invoice['discount_amount'],
            'discount_type' => $invoice['discount_type'],
            'tax_amount' => (float)$invoice['tax_amount'],
            'final_amount' => (float)$invoice['final_amount'],
            'notes' => $invoice['notes'] ?? '',
            'items' => $invoiceItems
        ]
    ];

    // إرجاع البيانات بتنسيق JSON
    echo json_encode($jsonData);
    exit;
}

// إذا تم طلب الطباعة الحرارية مباشرة
if ($printType === 'thermal' && $printMode === 'direct') {
    header('Content-Type: application/json');

    // إرسال طلب الطباعة إلى الخادم الطرفي للطباعة
    $result = printThermalReceipt($invoice);

    echo json_encode([
        'success' => $result,
        'message' => $result ? 'تمت الطباعة بنجاح' : 'فشل في الطباعة'
    ]);
    exit;
}

// إعداد صفحة الطباعة
// حسب نوع الطباعة (عادية أو حرارية)
?>
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>طباعة الفاتورة #<?php echo htmlspecialchars($invoice['invoice_number']); ?></title>

    <?php if ($printType === 'thermal'): ?>
    <!-- نمط الطباعة الحرارية -->
    <style>
        @page {
            size: <?php echo (isset($_GET['size']) && $_GET['size'] === '58mm') ? '58mm' : '80mm'; ?> auto;
            margin: 0;
        }
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        html, body {
            width: 100%;
            font-family: Arial, sans-serif;
            font-weight: bold;
            font-size: 12px;
            line-height: 1.3;
            color: #000;
            background-color: white;
            direction: rtl;
        }
        .receipt-container {
            width: <?php echo (isset($_GET['size']) && $_GET['size'] === '58mm') ? '48mm' : '72mm'; ?>;
            margin: 0 auto;
            padding: 5px;
            overflow: hidden;
        }
        h2 {
            font-size: 16px;
            font-weight: bold;
            margin: 3px 0;
            text-align: center;
        }
        p {
            margin: 3px 0;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 5px 0;
            table-layout: fixed;
        }
        th, td {
            padding: 2px 1px;
            font-weight: bold;
        }
        th {
            text-align: right;
            font-size: 12px;
        }
        td {
            text-align: right;
            font-size: 12px;
        }
        .text-left {
            text-align: left;
        }
        .text-right {
            text-align: right;
        }
        .text-center {
            text-align: center;
        }
        .border-bottom {
            border-bottom: 1px solid #000;
        }
        .dashed-border {
            border-bottom: 1px dashed #000;
        }
        .summary {
            margin-top: 5px;
            border-top: 1px solid #000;
            padding-top: 3px;
        }
        .summary-row {
            display: flex;
            justify-content: space-between;
            margin: 2px 0;
        }
        .footer {
            margin-top: 10px;
            text-align: center;
            border-top: 1px solid #000;
            padding-top: 3px;
            font-size: 12px;
        }
        @media print {
            body {
                width: 100%;
                margin: 0 !important;
                padding: 0 !important;
            }
            .receipt-container {
                width: 100%;
                max-width: <?php echo (isset($_GET['size']) && $_GET['size'] === '58mm') ? '48mm' : '72mm'; ?>;
                margin: 0 !important;
                padding: 5px !important;
            }
            .no-print {
                display: none;
            }
            /* تحسينات للشعار في الطباعة الحرارية */
            img {
                -webkit-print-color-adjust: exact !important;
                color-adjust: exact !important;
                print-color-adjust: exact !important;
                image-rendering: -webkit-optimize-contrast;
                image-rendering: crisp-edges;
            }
        }
        .print-btn {
            display: block;
            width: 100%;
            padding: 10px;
            background: #007bff;
            color: white;
            text-align: center;
            margin: 10px 0;
            cursor: pointer;
            border: none;
            border-radius: 4px;
        }
    </style>
    <?php else: ?>
    <!-- نمط الطباعة العادية -->
    <style>
        @media print {
            body {
                font-family: Arial, sans-serif;
                font-size: 12pt;
                direction: rtl;
            }
            .invoice-print {
                width: 100%;
                max-width: 800px;
                margin: 0 auto;
                padding: 20px;
            }
            table {
                width: 100%;
                border-collapse: collapse;
                margin-bottom: 20px;
            }
            table, th, td {
                border: 1px solid #ddd;
            }
            th, td {
                padding: 8px;
                text-align: right;
            }
            th {
                background-color: #f2f2f2;
            }
            .text-center {
                text-align: center;
            }
            .text-end {
                text-align: left;
            }
            .row {
                display: flex;
                flex-wrap: wrap;
                margin-bottom: 15px;
            }
            .col-6 {
                width: 50%;
            }
            .col-4 {
                width: 33.333333%;
            }
            .col-8 {
                width: 66.666667%;
            }
            .col-12 {
                width: 100%;
            }
            .mb-1 {
                margin-bottom: 4px;
            }
            .mb-3 {
                margin-bottom: 12px;
            }
            .mb-4 {
                margin-bottom: 16px;
            }
            .mt-2 {
                margin-top: 8px;
            }
            .mt-4 {
                margin-top: 16px;
            }
            .my-4 {
                margin-top: 16px;
                margin-bottom: 16px;
            }
            hr {
                border: none;
                border-top: 1px solid #ddd;
                margin: 20px 0;
            }
            h3 {
                font-size: 18pt;
                margin-bottom: 10px;
            }
            h5 {
                font-size: 14pt;
                margin-bottom: 10px;
            }
        }
        .no-print {
            padding: 20px;
            background-color: #f8f9fa;
            text-align: center;
            margin-bottom: 20px;
        }

        /* تحسينات للشعار في الطباعة */
        @media print {
            img {
                -webkit-print-color-adjust: exact !important;
                color-adjust: exact !important;
                print-color-adjust: exact !important;
            }
        }
    </style>
    <?php endif; ?>
</head>
<body>
    <!-- زر الطباعة (يظهر فقط في وضع المعاينة) -->
    <?php if ($printMode === 'preview'): ?>
    <div class="no-print">
        <button class="print-btn" onclick="window.print();">طباعة</button>
        <button class="print-btn" style="background-color: #6c757d;" onclick="window.close();">إغلاق</button>
    </div>
    <?php endif; ?>

    <?php if ($printType === 'thermal'): ?>
    <!-- محتوى الطباعة الحرارية -->
    <div class="receipt-container">
        <div class="text-center">
            <?php if (!empty($companyLogo)): ?>
            <div style="margin-bottom: 5px;">
                <?php
                // تطبيق تحسينات جودة الشعار بناءً على الإعدادات
                $logoStyle = 'max-width: 100%; width: ' . min($logoWidth, 300) . 'px; height: ' . min($logoHeight, 100) . 'px; object-fit: contain;';

                // إضافة تحسينات الجودة بناءً على الإعدادات
                switch ($logoPrintQuality) {
                    case 'enhanced':
                        $logoStyle .= ' filter: contrast(1.2) brightness(1.1);';
                        break;
                    case 'high':
                        $logoStyle .= ' filter: contrast(1.4) brightness(1.2);';
                        break;
                }
                ?>
                <img src="<?php echo $companyLogo; ?>" alt="شعار الشركة" style="<?php echo $logoStyle; ?>">
            </div>
            <?php endif; ?>
            <h2><?php echo htmlspecialchars($companyName); ?></h2>
            <p><?php echo htmlspecialchars($branchName); ?></p>
        </div>

        <div class="border-bottom"></div>

        <div style="margin: 5px 0;">
            <div>رقم الفاتورة: <?php echo htmlspecialchars($invoice['invoice_number']); ?></div>
            <div>التاريخ: <?php echo date('Y-m-d h:i A', strtotime($invoice['created_at'])); ?></div>
            <div>العميل: <?php echo htmlspecialchars($invoice['customer_name'] ?? 'عميل غير مسجل'); ?></div>
            <div>الموظف: <?php echo htmlspecialchars($invoice['employee_name'] ?? '-'); ?></div>
            <div>الكاشير: <?php echo htmlspecialchars($invoice['cashier_name'] ?? '-'); ?></div>
        </div>

        <div class="dashed-border"></div>

        <table>
            <thead>
                <tr class="border-bottom">
                    <th>الصنف</th>
                    <th class="text-left">السعر</th>
                    <th class="text-left">العدد</th>
                    <th class="text-left">المبلغ</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($invoiceItems as $item): ?>
                <tr class="dashed-border">
                    <td><?php echo htmlspecialchars($item['service_name'] ?? $item['product_name'] ?? 'غير محدد'); ?></td>
                    <td class="text-left"><?php echo number_format($item['price'] ?? 0, 2); ?></td>
                    <td class="text-left"><?php echo $item['quantity'] ?? 0; ?></td>
                    <td class="text-left"><?php echo number_format($item['total'] ?? 0, 2); ?></td>
                </tr>
                <?php endforeach; ?>
            </tbody>
        </table>

        <div class="summary">
            <div class="summary-row">
                <span>الإجمالي:</span>
                <span><?php echo number_format($invoice['total_amount'], 2); ?></span>
            </div>

            <?php if ($invoice['discount_amount'] > 0): ?>
            <div class="summary-row">
                <span>الخصم:</span>
                <span><?php echo number_format($invoice['discount_amount'], 2); ?></span>
            </div>
            <?php endif; ?>

            <?php if ($invoice['tax_amount'] > 0): ?>
            <div class="summary-row">
                <span>الضريبة:</span>
                <span><?php echo number_format($invoice['tax_amount'], 2); ?></span>
            </div>
            <?php endif; ?>

            <div class="summary-row dashed-border" style="font-weight: bold; margin-top: 5px; padding-top: 5px;">
                <span>المبلغ النهائي:</span>
                <span><?php echo number_format($invoice['final_amount'], 2); ?></span>
            </div>
        </div>

        <div style="margin-top: 10px;">
            <div>طريقة الدفع:
                <?php
                switch ($invoice['payment_method']) {
                    case 'cash':
                        echo 'نقدي';
                        break;
                    case 'card':
                        echo 'بطاقة';
                        break;
                    default:
                        echo 'أخرى';
                }
                ?>
            </div>
            <div>حالة الدفع:
                <?php
                switch ($invoice['payment_status']) {
                    case 'paid':
                        echo 'مدفوع';
                        break;
                    case 'partial':
                        echo 'مدفوع جزئي';
                        break;
                    default:
                        echo 'غير مدفوع';
                }
                ?>
            </div>
        </div>

        <div class="footer">
            <?php if (!empty($branchPhone)): ?>
            <div><strong>هاتف:</strong> <?php echo htmlspecialchars($branchPhone); ?></div>
            <?php endif; ?>

            <?php if (!empty($branchAddress)): ?>
            <div><?php echo htmlspecialchars($branchAddress); ?></div>
            <?php endif; ?>

            <p>شكراً لزيارتكم - نتطلع لخدمتكم مرة أخرى</p>
        </div>
    </div>
    <?php else: ?>
    <!-- محتوى الطباعة العادية -->
    <div class="invoice-print">
        <div class="text-center mb-4">
            <?php if (!empty($companyLogo)): ?>
            <div style="margin-bottom: 15px;">
                <?php
                // تطبيق تحسينات جودة الشعار بناءً على الإعدادات
                $logoStyle = 'max-width: 100%; width: ' . $logoWidth . 'px; height: ' . $logoHeight . 'px; object-fit: contain;';

                // إضافة تحسينات الجودة بناءً على الإعدادات
                switch ($logoPrintQuality) {
                    case 'enhanced':
                        $logoStyle .= ' filter: contrast(1.2) brightness(1.1);';
                        break;
                    case 'high':
                        $logoStyle .= ' filter: contrast(1.4) brightness(1.2);';
                        break;
                }
                ?>
                <img src="<?php echo $companyLogo; ?>" alt="شعار الشركة" style="<?php echo $logoStyle; ?>">
            </div>
            <?php endif; ?>
            <h3><?php echo htmlspecialchars($companyName); ?></h3>
            <p><?php echo htmlspecialchars($branchName); ?></p>
        </div>

        <div class="row mb-3">
            <div class="col-6">
                <strong>رقم الفاتورة:</strong> <?php echo htmlspecialchars($invoice['invoice_number']); ?>
            </div>
            <div class="col-6 text-end">
                <strong>التاريخ:</strong> <?php echo date('Y-m-d h:i A', strtotime($invoice['created_at'])); ?>
            </div>
        </div>

        <div class="row mb-3">
            <div class="col-6">
                <strong>العميل:</strong> <?php echo htmlspecialchars($invoice['customer_name'] ?? 'عميل غير مسجل'); ?>
            </div>
            <div class="col-6 text-end">
                <strong>الموظف:</strong> <?php echo htmlspecialchars($invoice['employee_name'] ?? '-'); ?>
            </div>
        </div>

        <div class="row mb-3">
            <div class="col-6">
                <strong>الكاشير:</strong> <?php echo htmlspecialchars($invoice['cashier_name'] ?? '-'); ?>
            </div>
            <div class="col-6 text-end">
                <strong>الفرع:</strong> <?php echo htmlspecialchars($branchName); ?>
            </div>
        </div>

        <div class="row">
            <div class="col-12">
                <table>
                    <thead>
                        <tr>
                            <th>#</th>
                            <th>المنتج/الخدمة</th>
                            <th>النوع</th>
                            <th>الكمية</th>
                            <th>السعر</th>
                            <th>الإجمالي</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($invoiceItems as $index => $item): ?>
                        <tr>
                            <td><?php echo $index + 1; ?></td>
                            <td><?php echo htmlspecialchars($item['service_name'] ?? $item['product_name'] ?? 'غير محدد'); ?></td>
                            <td>
                                <?php echo ($item['item_type'] === 'service') ? 'خدمة' : 'منتج'; ?>
                            </td>
                            <td><?php echo $item['quantity'] ?? 0; ?></td>
                            <td><?php echo number_format($item['price'] ?? 0, 2); ?></td>
                            <td><?php echo number_format($item['total'] ?? 0, 2); ?></td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>

        <div class="row">
            <div class="col-8">
                <?php if (!empty($invoice['notes'])): ?>
                <div>
                    <strong>ملاحظات:</strong>
                    <p><?php echo nl2br(htmlspecialchars($invoice['notes'])); ?></p>
                </div>
                <?php endif; ?>
            </div>
            <div class="col-4">
                <table>
                    <tbody>
                        <tr>
                            <td><strong>إجمالي الخدمات:</strong></td>
                            <td><?php echo number_format($servicesTotal, 2); ?></td>
                        </tr>
                        <tr>
                            <td><strong>إجمالي المنتجات:</strong></td>
                            <td><?php echo number_format($productsTotal, 2); ?></td>
                        </tr>
                        <tr>
                            <td><strong>الإجمالي:</strong></td>
                            <td><?php echo number_format($invoice['total_amount'], 2); ?></td>
                        </tr>
                        <?php if ($invoice['discount_amount'] > 0): ?>
                        <tr>
                            <td><strong>الخصم:</strong></td>
                            <td>
                                <?php echo number_format($invoice['discount_amount'], 2); ?>
                                <?php if ($invoice['discount_type'] === 'percentage'): ?>%<?php endif; ?>
                            </td>
                        </tr>
                        <?php endif; ?>
                        <?php if ($invoice['tax_amount'] > 0): ?>
                        <tr>
                            <td><strong>الضريبة:</strong></td>
                            <td><?php echo number_format($invoice['tax_amount'], 2); ?></td>
                        </tr>
                        <?php endif; ?>
                        <tr>
                            <td><strong>المبلغ النهائي:</strong></td>
                            <td><?php echo number_format($invoice['final_amount'], 2); ?></td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <div class="row mt-4">
            <div class="col-12">
                <p>
                    <strong>طريقة الدفع:</strong>
                    <?php
                    switch ($invoice['payment_method']) {
                        case 'cash':
                            echo 'نقدي';
                            break;
                        case 'card':
                            echo 'بطاقة';
                            break;
                        default:
                            echo 'أخرى';
                    }
                    ?>
                </p>
                <p>
                    <strong>حالة الدفع:</strong>
                    <?php
                    switch ($invoice['payment_status']) {
                        case 'paid':
                            echo 'مدفوع';
                            break;
                        case 'partial':
                            echo 'مدفوع جزئي';
                            break;
                        default:
                            echo 'غير مدفوع';
                    }
                    ?>
                </p>
            </div>
        </div>

        <hr class="my-4">

        <div class="row text-center">
            <div class="col-12">
                <?php if (!empty($branchPhone)): ?>
                <p><strong>هاتف:</strong> <?php echo htmlspecialchars($branchPhone); ?></p>
                <?php endif; ?>

                <?php if (!empty($branchAddress)): ?>
                <p><?php echo htmlspecialchars($branchAddress); ?></p>
                <?php endif; ?>

                <p>شكراً لزيارتكم - نتطلع لخدمتكم مرة أخرى</p>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <script>
        window.onload = function() {
            <?php if ($printMode === 'auto'): ?>
            // طباعة تلقائية
            setTimeout(function() {
                // طباعة عدد النسخ المحددة
                printCopies(<?php echo $invoiceCopies; ?>);

                <?php if ($printType === 'thermal'): ?>
                // إغلاق النافذة بعد الطباعة الحرارية
                setTimeout(function() {
                    // إرسال رسالة للنافذة الأصلية لإعلامها باكتمال الطباعة
                    // لا نرسل رسالة للنافذة الأصلية لتجنب تكرار إرسال الإشعارات
                    // الإشعارات يتم إرسالها من خلال حدث invoiceCreated في صفحة نقطة البيع
                    // if (window.opener && !window.opener.closed) {
                    //     window.opener.postMessage({ type: 'print_completed', invoiceId: <?php echo $invoiceId; ?> }, '*');
                    // }
                    window.close();
                }, <?php echo $invoiceCopies * 1000 + 500; ?>); // انتظار أطول للسماح بطباعة جميع النسخ
                <?php endif; ?>
            }, 500);
            <?php endif; ?>
        };

        // دالة لطباعة عدد محدد من النسخ
        function printCopies(copies) {
            if (copies <= 0) return;

            // طباعة النسخة الحالية
            window.print();

            // إذا كان هناك نسخ أخرى
            if (copies > 1) {
                setTimeout(function() {
                    printCopies(copies - 1);
                }, 1000); // انتظار ثانية بين كل نسخة
            }
        }

        // تعديل زر الطباعة لاستخدام الدالة الجديدة
        var printBtn = document.querySelector('.print-btn');
        if (printBtn) {
            printBtn.onclick = function() {
                printCopies(<?php echo $invoiceCopies; ?>);
            };
        }
    </script>
</body>
</html>