/**
 * Salon Management System - Calendar Management
 * Handles appointment scheduling and calendar-related operations
 */
(function($) {
    'use strict';

    const AppointmentCalendar = {
        // Configuration settings
        config: {
            currentView: 'month',
            selectedDate: null,
            selectedService: null,
            selectedEmployee: null,
            selectedBranch: null
        },

        // Initialize calendar module
        init: function() {
            this.bindEvents();
            this.initFullCalendar();
            this.loadInitialData();
        },

        // Bind DOM events
        bindEvents: function() {
            // Date selection
            $(document).on('click', '.calendar-date', this.handleDateSelection.bind(this));

            // Time slot selection
            $(document).on('click', '.time-slot', this.handleTimeSelection.bind(this));

            // Service selection
            $(document).on('change', '#service-select', this.handleServiceSelection.bind(this));

            // Employee selection
            $(document).on('change', '#employee-select', this.handleEmployeeSelection.bind(this));

            // Branch selection
            $(document).on('change', '#branch-select', this.handleBranchSelection.bind(this));

            // Appointment form submission
            $(document).on('submit', '#appointment-form', this.handleAppointmentSubmission.bind(this));

            // Cancel appointment
            $(document).on('click', '.cancel-appointment', this.handleAppointmentCancellation.bind(this));
        },

        // Load initial data
        loadInitialData: function() {
            // Load branches
            this.loadBranches();

            // Load services
            this.loadServices();
        },

        // Initialize FullCalendar
        initFullCalendar: function() {
            $('#salon-calendar').fullCalendar({
                locale: 'ar',
                defaultView: 'month',
                header: {
                    left: 'prev,next today',
                    center: 'title',
                    right: 'month,agendaWeek,agendaDay'
                },
                events: this.fetchCalendarEvents.bind(this),
                eventClick: this.handleEventClick.bind(this),
                dayClick: this.handleDayClick.bind(this),
                editable: true,
                eventLimit: true,
                height: 'auto',
                timezone: 'local'
            });
        },

        // Fetch calendar events
        fetchCalendarEvents: function(start, end, timezone, callback) {
            AjaxHandler.get('appointments.php', {
                action: 'list',
                start_date: start.format('YYYY-MM-DD'),
                end_date: end.format('YYYY-MM-DD'),
                branch_id: this.config.selectedBranch
            }, {
                success: (response) => {
                    const events = response.data.map(appointment => ({
                        id: appointment.id,
                        title: `${appointment.customer_name} - ${appointment.service_name}`,
                        start: appointment.date + 'T' + appointment.start_time,
                        end: appointment.date + 'T' + appointment.end_time,
                        color: this.getAppointmentColor(appointment.status)
                    }));

                    callback(events);
                }
            });
        },

        // Get appointment color based on status
        getAppointmentColor: function(status) {
            switch (status) {
                case 'booked': return '#28a745';   // Green
                case 'waiting': return '#ffc107';  // Yellow
                case 'completed': return '#007bff'; // Blue
                case 'cancelled': return '#dc3545'; // Red
                default: return '#6c757d';         // Gray
            }
        },

        // Load branches
        loadBranches: function() {
            AjaxHandler.get('branches.php', {}, {
                success: (response) => {
                    const $branchSelect = $('#branch-select');
                    $branchSelect.empty().append('<option value="">اختر الفرع</option>');

                    response.data.forEach(branch => {
                        $branchSelect.append(
                            `<option value="${branch.id}">${branch.name}</option>`
                        );
                    });
                }
            });
        },

        // Load services
        loadServices: function() {
            AjaxHandler.get('services.php', {
                action: 'list',
                is_active: 1
            }, {
                success: (response) => {
                    const $serviceSelect = $('#service-select');
                    $serviceSelect.empty().append('<option value="">اختر الخدمة</option>');

                    response.data.forEach(service => {
                        $serviceSelect.append(
                            `<option value="${service.id}">${service.name} (${service.price} ${typeof currencySymbol !== 'undefined' ? currencySymbol : 'ر.س'})</option>`
                        );
                    });
                }
            });
        },

        // Handle service selection
        handleServiceSelection: function(event) {
            const serviceId = $(event.target).val();
            this.config.selectedService = serviceId;

            // Load employees for this service
            if (serviceId) {
                this.loadEmployeesForService(serviceId);
            }
        },

        // Load employees for a specific service
        loadEmployeesForService: function(serviceId) {
            const $employeeSelect = $('#employee-select');
            $employeeSelect.empty().append('<option value="">اختر الموظف</option>');

            AjaxHandler.get('employees', {
                action: 'get_employees_by_service',
                service_id: serviceId,
                is_active: 1
            }, {
                success: (response) => {
                    response.employees.forEach(employee => {
                        $employeeSelect.append(
                            `<option value="${employee.id}">${employee.name}</option>`
                        );
                    });
                }
            });
        },

        // Handle employee selection
        handleEmployeeSelection: function(event) {
            this.config.selectedEmployee = $(event.target).val();
        },

        // Handle branch selection
        handleBranchSelection: function(event) {
            this.config.selectedBranch = $(event.target).val();

            // Refresh calendar events for the selected branch
            $('#salon-calendar').fullCalendar('refetchEvents');
        },

        // Handle day click
        handleDayClick: function(date) {
            this.config.selectedDate = date.format('YYYY-MM-DD');

            // Check service and branch availability before opening modal
            if (!this.config.selectedService || !this.config.selectedBranch) {
                AjaxHandler.showAlert('يرجى اختيار الخدمة والفرع أولاً', 'warning');
                return;
            }

            // Load available time slots
            this.loadAvailableTimeSlots(this.config.selectedDate);
        },

        // Load available time slots
        loadAvailableTimeSlots: function(date) {
            const $timeSlotsContainer = $('#time-slots');
            $timeSlotsContainer.empty();

            AjaxHandler.get('services', {
                action: 'check_availability',
                service_id: this.config.selectedService,
                date: date,
                start_time: null,
                branch_id: this.config.selectedBranch
            }, {
                success: (response) => {
                    const availableSlots = response.data.available_slots || [];

                    if (availableSlots.length > 0) {
                        availableSlots.forEach(slot => {
                            $timeSlotsContainer.append(`
                                <div class="time-slot" data-time="${slot.time}">
                                    ${slot.time}
                                </div>
                            `);
                        });

                        $('#appointment-modal').modal('show');
                    } else {
                        AjaxHandler.showAlert('لا توجد مواعيد متاحة في هذا اليوم', 'warning');
                    }
                }
            });
        },

        // Handle time slot selection
        handleTimeSelection: function(event) {
            $('.time-slot').removeClass('selected');
            $(event.target).addClass('selected');
            this.config.selectedTime = $(event.target).data('time');
        },

        // Handle appointment submission
        handleAppointmentSubmission: function(event) {
            event.preventDefault();

            const appointmentData = {
                action: 'create',
                customer_id: $('#customer-id').val(),
                service_id: this.config.selectedService,
                employee_id: this.config.selectedEmployee,
                branch_id: this.config.selectedBranch,
                date: this.config.selectedDate,
                start_time: this.config.selectedTime,
                notes: $('#appointment-notes').val()
            };

            AjaxHandler.post('appointments', appointmentData, {
                success: (response) => {
                    AjaxHandler.showAlert('تم حجز الموعد بنجاح', 'success');
                    $('#salon-calendar').fullCalendar('refetchEvents');
                    $('#appointment-modal').modal('hide');
                }
            });
        },

        // Handle event click on calendar
        handleEventClick: function(calEvent) {
            // Fetch and show appointment details
            AjaxHandler.get('appointments', {
                action: 'view',
                id: calEvent.id
            }, {
                success: (response) => {
                    const appointment = response.data;

                    // Populate appointment details modal
                    $('#appointment-details-modal').html(`
                        <div class="modal-header">
                            <h5 class="modal-title">تفاصيل الموعد</h5>
                        </div>
                        <div class="modal-body">
                            <p><strong>العميل:</strong> ${appointment.customer_name}</p>
                            <p><strong>الخدمة:</strong> ${appointment.service_name}</p>
                            <p><strong>الموظف:</strong> ${appointment.employee_name}</p>
                            <p><strong>التاريخ:</strong> ${appointment.date}</p>
                            <p><strong>الوقت:</strong> ${appointment.start_time} - ${appointment.end_time}</p>
                            <p><strong>الحالة:</strong> ${appointment.status}</p>
                            <p><strong>الملاحظات:</strong> ${appointment.notes || 'لا توجد ملاحظات'}</p>
                        </div>
                        <div class="modal-footer">
                            ${appointment.status === 'booked' ? `
                                <button type="button" class="btn btn-danger cancel-appointment"
                                        data-id="${appointment.id}">
                                    إلغاء الموعد
                                </button>
                            ` : ''}
                            <button type="button" class="btn btn-secondary" data-dismiss="modal">إغلاق</button>
                        </div>
                    `);

                    $('#appointment-details-modal').modal('show');
                }
            });
        },

        // Handle appointment cancellation
        handleAppointmentCancellation: function(event) {
            const appointmentId = $(event.target).data('id');

            if (!confirm('هل أنت متأكد من إلغاء هذا الموعد؟')) {
                return;
            }

            AjaxHandler.post('appointments', {
                action: 'cancel',
                id: appointmentId
            }, {
                success: (response) => {
                    AjaxHandler.showAlert('تم إلغاء الموعد بنجاح', 'success');
                    $('#salon-calendar').fullCalendar('refetchEvents');
                    $('#appointment-details-modal').modal('hide');
                }
            });
        }
    };

    // Document ready initialization
    $(document).ready(function() {
        AppointmentCalendar.init();
    });

})(jQuery);