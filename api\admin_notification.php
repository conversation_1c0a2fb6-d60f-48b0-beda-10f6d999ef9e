<?php
/**
 * نقطة نهاية API لإشعارات المدير
 * تتعامل مع إرسال إشعارات للمدراء عبر الواتساب
 */

// تأكيد تحميل الملفات الأساسية
require_once __DIR__ . '/../config/init.php';

// التحقق من نوع الطلب (POST أو GET)
$action = $_REQUEST['action'] ?? '';

// معالجة الطلبات
try {
    // إعداد رأس الاستجابة
    header('Content-Type: application/json');

    // إنشاء كائنات النماذج
    $db = new Database();
    $adminNotification = new AdminNotification($db);

    // التحقق من صلاحيات المستخدم
    if (!isLoggedIn()) {
        throw new Exception('يجب تسجيل الدخول أولاً', 401);
    }

    switch ($action) {
        // إرسال تقرير مخصص
        case 'send_custom_report':
            // التأكد من أن الطلب عبر POST
            if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
                throw new Exception('الطريقة غير مسموح بها', 405);
            }

            // التحقق من وجود عنوان ومحتوى التقرير
            $reportTitle = $_POST['report_title'] ?? '';
            $reportContent = $_POST['report_content'] ?? '';

            if (empty($reportTitle) || empty($reportContent)) {
                throw new Exception('عنوان ومحتوى التقرير مطلوبان', 400);
            }

            // إرسال التقرير المخصص
            $result = $adminNotification->sendCustomReport($reportTitle, $reportContent);

            if ($result) {
                echo json_encode([
                    'status' => 'success',
                    'message' => 'تم إرسال التقرير بنجاح'
                ]);
            } else {
                throw new Exception('فشل في إرسال التقرير. تحقق من إعدادات الإشعارات وأرقام المدراء', 500);
            }
            break;

        // الافتراضي: إجراء غير معروف
        default:
            throw new Exception('إجراء غير معروف', 404);
    }
} catch (Exception $e) {
    // معالجة الأخطاء
    $errorCode = $e->getCode();

    // التحقق من أن رمز الخطأ رقم صحيح وضمن نطاق رموز HTTP
    if (!is_int($errorCode) || $errorCode < 100 || $errorCode > 599) {
        // إذا لم يكن رمز الخطأ صالحاً، استخدم 500 (خطأ داخلي في الخادم)
        $errorCode = 500;
    }

    http_response_code($errorCode);

    echo json_encode([
        'status' => 'error',
        'message' => $e->getMessage(),
        'code' => $errorCode
    ]);
} catch (PDOException $e) {
    // إرجاع رسالة الخطأ العامة
    http_response_code(500);
    echo json_encode([
        'status' => 'error',
        'message' => 'حدث خطأ في قاعدة البيانات: ' . $e->getMessage()
    ]);
}
