/**
 * Script para mostrar un aviso sobre la configuración del servidor local de WhatsApp
 */

document.addEventListener('DOMContentLoaded', function() {
    // Verificar si WhatsAppClient está definido
    if (typeof WhatsAppClient === 'undefined') {
        console.warn('WhatsAppClient no está definido. No se mostrará el aviso.');
        return;
    }

    // Registrar un manejador para el evento de modo de respaldo activado
    WhatsAppClient.on('fallbackModeActivated', function(data) {
        showWhatsAppServerNotice();
    });

    // Registrar un manejador para el evento de error
    WhatsAppClient.on('error', function(data) {
        // Si el error está relacionado con la conexión al servidor, mostrar el aviso
        if (data.error && data.error.message && 
            (data.error.message.includes('conectar') || 
             data.error.message.includes('conexión') ||
             data.error.message.includes('Error de red'))) {
            
            showWhatsAppServerNotice();
        }
    });

    /**
     * Mostrar un aviso sobre la configuración del servidor local de WhatsApp
     */
    function showWhatsAppServerNotice() {
        // Verificar si ya se ha mostrado el aviso
        if (localStorage.getItem('whatsappServerNoticeShown')) {
            return;
        }

        // Crear el elemento del aviso
        const notice = document.createElement('div');
        notice.className = 'whatsapp-server-notice';
        notice.innerHTML = `
            <div class="whatsapp-server-notice-content">
                <h3><i class="fab fa-whatsapp"></i> Servidor de WhatsApp no disponible</h3>
                <p>No se pudo conectar al servidor local de WhatsApp. Las notificaciones se enviarán desde el servidor principal.</p>
                <p>Para enviar mensajes directamente desde el navegador, configure el servidor local siguiendo las <a href="${BASE_URL}docs/whatsapp-server-setup.md" target="_blank">instrucciones</a>.</p>
                <div class="whatsapp-server-notice-actions">
                    <button class="btn btn-primary btn-sm dismiss-notice">Entendido</button>
                    <button class="btn btn-outline-secondary btn-sm dont-show-again">No mostrar de nuevo</button>
                </div>
            </div>
        `;

        // Estilos para el aviso
        const style = document.createElement('style');
        style.textContent = `
            .whatsapp-server-notice {
                position: fixed;
                bottom: 20px;
                right: 20px;
                z-index: 9999;
                max-width: 400px;
                background-color: white;
                border-radius: 8px;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
                border-left: 4px solid #25D366;
                animation: slideIn 0.3s ease-out;
            }
            
            .whatsapp-server-notice-content {
                padding: 15px;
            }
            
            .whatsapp-server-notice h3 {
                margin-top: 0;
                font-size: 18px;
                color: #075E54;
            }
            
            .whatsapp-server-notice p {
                margin-bottom: 10px;
                font-size: 14px;
                color: #4A4A4A;
            }
            
            .whatsapp-server-notice-actions {
                display: flex;
                justify-content: flex-end;
                gap: 10px;
                margin-top: 15px;
            }
            
            @keyframes slideIn {
                from {
                    transform: translateX(100%);
                    opacity: 0;
                }
                to {
                    transform: translateX(0);
                    opacity: 1;
                }
            }
        `;

        // Añadir el aviso y los estilos al documento
        document.head.appendChild(style);
        document.body.appendChild(notice);

        // Manejar el botón "Entendido"
        notice.querySelector('.dismiss-notice').addEventListener('click', function() {
            notice.remove();
        });

        // Manejar el botón "No mostrar de nuevo"
        notice.querySelector('.dont-show-again').addEventListener('click', function() {
            localStorage.setItem('whatsappServerNoticeShown', 'true');
            notice.remove();
        });

        // Ocultar el aviso después de 30 segundos
        setTimeout(function() {
            if (document.body.contains(notice)) {
                notice.remove();
            }
        }, 30000);
    }
});
