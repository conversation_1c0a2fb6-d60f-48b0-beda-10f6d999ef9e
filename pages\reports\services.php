<?php
/**
 * صفحة تقارير الخدمات
 */

// تعريف ثابت لمنع الوصول المباشر للملفات المرتبطة
define('BASEPATH', true);

// استدعاء ملفات التهيئة
require_once '../../config/init.php';

// التحقق من تسجيل الدخول
redirectIfNotLoggedIn();

// التحقق من الصلاحيات
if (!hasPermission('reports_services')) {
    $_SESSION['error_message'] = 'ليس لديك صلاحية لعرض تقارير الخدمات';
    header('Location: ' . BASE_URL . 'pages/dashboard.php');
    exit;
}

// عنوان الصفحة
$pageTitle = 'تقارير الخدمات';

// استدعاء رأس الصفحة
include '../../includes/templates/header.php';

// إنشاء كائنات النماذج
$serviceModel = new Service($db);
$branchModel = new Branch($db);

// الحصول على قائمة الفروع للفلتر
$branches = [];
if (hasPermission('admin')) {
    $branches = $branchModel->getBranches(['is_active' => 1]);
}

// الحصول على فئات الخدمات
$serviceCategories = $serviceModel->getServiceCategories();

// تحديد نطاق تاريخ افتراضي (الشهر الحالي)
$today = date('Y-m-d');
$firstDayOfMonth = date('Y-m-01');
$lastDayOfMonth = date('Y-m-t');

// الحصول على إعدادات العملة من قاعدة البيانات
$settingsModel = new Settings($db);
$currencySymbol = $settingsModel->get('system_currency_symbol', 'ر.س');
$currency = $settingsModel->get('system_currency', 'ريال سعودي');

// البيانات الافتراضية للفلتر
$filters = [
    'start_date' => $firstDayOfMonth,
    'end_date' => $lastDayOfMonth,
    'branch_id' => $_SESSION['user_branch_id'] ?? null,
    'category_id' => '',
    'sort_by' => 'count',
    'sort_dir' => 'DESC'
];

// تقرير افتراضي
$servicesReport = null;
?>
<!-- المحتوى الرئيسي للصفحة -->
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-white py-3">
                    <h5 class="mb-0">خيارات التقرير</h5>
                </div>
                <div class="card-body">
                    <form id="reportForm" method="post" class="row align-items-end">
                        <div class="col-md-3 mb-3">
                            <label for="dateRange" class="form-label">نطاق التاريخ</label>
                            <select class="form-select" id="dateRange" name="date_range">
                                <option value="custom">تخصيص</option>
                                <option value="today">اليوم</option>
                                <option value="yesterday">الأمس</option>
                                <option value="this_week">هذا الأسبوع</option>
                                <option value="last_week">الأسبوع الماضي</option>
                                <option value="this_month" selected>هذا الشهر</option>
                                <option value="last_month">الشهر الماضي</option>
                                <option value="this_year">هذه السنة</option>
                            </select>
                        </div>
                    <div class="col-md-3 mb-3">
                        <label for="startDate" class="form-label">من تاريخ</label>
                        <input type="date" class="form-control" id="startDate" name="start_date" value="<?php echo $firstDayOfMonth; ?>">
                    </div>

                    <div class="col-md-3 mb-3">
                        <label for="endDate" class="form-label">إلى تاريخ</label>
                        <input type="date" class="form-control" id="endDate" name="end_date" value="<?php echo $lastDayOfMonth; ?>">
                    </div>

                    <?php if (hasPermission('admin')): ?>
                    <div class="col-md-3 mb-3">
                        <label for="branchId" class="form-label">الفرع</label>
                        <select class="form-select" id="branchId" name="branch_id">
                            <option value="">جميع الفروع</option>
                            <?php foreach($branches as $branch): ?>
                            <option value="<?php echo $branch['id']; ?>" <?php echo ($branch['id'] == $_SESSION['user_branch_id']) ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($branch['name']); ?>
                            </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <?php else: ?>
                    <input type="hidden" name="branch_id" value="<?php echo $_SESSION['user_branch_id']; ?>">
                    <?php endif; ?>

                    <div class="col-md-3 mb-3">
                        <label for="categoryId" class="form-label">فئة الخدمة</label>
                        <select class="form-select" id="categoryId" name="category_id">
                            <option value="">جميع الفئات</option>
                            <?php foreach($serviceCategories as $category): ?>
                            <option value="<?php echo $category['id']; ?>">
                                <?php echo htmlspecialchars($category['name']); ?>
                            </option>
                            <?php endforeach; ?>
                        </select>
                    </div>

                    <div class="col-md-3 mb-3">
                        <label for="sortBy" class="form-label">ترتيب حسب</label>
                        <select class="form-select" id="sortBy" name="sort_by">
                            <option value="count">عدد مرات الاستخدام</option>
                            <option value="revenue">الإيرادات</option>
                            <option value="name">اسم الخدمة</option>
                        </select>
                    </div>

                    <div class="col-md-3 mb-3">
                        <label for="sortDir" class="form-label">اتجاه الترتيب</label>
                        <select class="form-select" id="sortDir" name="sort_dir">
                            <option value="DESC">تنازلي</option>
                            <option value="ASC">تصاعدي</option>
                        </select>
                    </div>

                    <div class="col-md-3 mb-3">
                        <button type="submit" class="btn btn-primary w-100">
                            <i class="fas fa-filter me-1"></i> عرض التقرير
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <div class="alert alert-info d-none" id="loadingAlert">
            <i class="fas fa-spinner fa-spin me-1"></i> جاري تحميل التقرير...
        </div>

        <div class="alert alert-danger d-none" id="errorAlert"></div>

        <!-- ملخص التقرير -->
        <div class="row" id="reportSummary">
            <div class="col-md-3 mb-4">
                <div class="card shadow-sm">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="text-muted mb-1">إجمالي استخدام الخدمات</h6>
                                <h3 class="display-6 fw-bold mb-0" id="totalServices">0</h3>
                            </div>
                            <div class="bg-primary bg-opacity-10 p-3 rounded">
                                <i class="fas fa-clipboard-list text-primary fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-3 mb-4">
                <div class="card shadow-sm">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="text-muted mb-1">إجمالي الإيرادات</h6>
                                <h3 class="display-6 fw-bold mb-0" id="totalRevenue">0.00</h3>
                            </div>
                            <div class="bg-success bg-opacity-10 p-3 rounded">
                                <i class="fas fa-money-bill-wave text-success fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-3 mb-4">
                <div class="card shadow-sm">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="text-muted mb-1">عدد الخدمات المختلفة</h6>
                                <h3 class="display-6 fw-bold mb-0" id="uniqueServices">0</h3>
                            </div>
                            <div class="bg-info bg-opacity-10 p-3 rounded">
                                <i class="fas fa-th-list text-info fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-3 mb-4">
                <div class="card shadow-sm">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="text-muted mb-1">متوسط سعر الخدمة</h6>
                                <h3 class="display-6 fw-bold mb-0" id="averageService">0.00</h3>
                            </div>
                            <div class="bg-warning bg-opacity-10 p-3 rounded">
                                <i class="fas fa-calculator text-warning fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- الرسوم البيانية للتقرير -->
        <div class="row">
            <div class="col-md-6 mb-4">
                <div class="card shadow-sm">
                    <div class="card-header bg-white py-3">
                        <h5 class="mb-0">أكثر الخدمات استخداماً</h5>
                    </div>
                    <div class="card-body">
                        <canvas id="topServicesChart" height="300"></canvas>
                    </div>
                </div>
            </div>

            <div class="col-md-6 mb-4">
                <div class="card shadow-sm">
                    <div class="card-header bg-white py-3">
                        <h5 class="mb-0">الخدمات حسب الفئة</h5>
                    </div>
                    <div class="card-body">
                        <canvas id="categoriesChart" height="300"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- جدول تفاصيل التقرير -->
        <div class="card shadow-sm mb-4">
            <div class="card-header bg-white py-3 d-flex justify-content-between align-items-center">
                <h5 class="mb-0">تفاصيل الخدمات</h5>
                <div>
                    <button type="button" class="btn btn-sm btn-outline-success" id="exportExcel">
                        <i class="fas fa-file-excel me-1"></i> تصدير Excel
                    </button>
                    <button type="button" class="btn btn-sm btn-outline-danger" id="exportPdf">
                        <i class="fas fa-file-pdf me-1"></i> تصدير PDF
                    </button>
                    <button type="button" class="btn btn-sm btn-outline-secondary" id="printReport">
                        <i class="fas fa-print me-1"></i> طباعة
                    </button>
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-bordered table-striped table-hover" id="reportTable">
                        <thead class="table-light">
                            <tr>
                                <th>#</th>
                                <th>اسم الخدمة</th>
                                <th>الفئة</th>
                                <th>السعر</th>
                                <th>عدد مرات الاستخدام</th>
                                <th>الإيرادات</th>
                                <th>النسبة من الإجمالي</th>
                            </tr>
                        </thead>
                        <tbody id="reportTableBody">
                            <!-- ستتم تعبئة البيانات هنا عن طريق JavaScript -->
                        </tbody>
                        <tfoot class="table-light">
                            <tr>
                                <th colspan="4">الإجمالي</th>
                                <th id="footerTotalCount">0</th>
                                <th id="footerTotalRevenue">0.00</th>
                                <th>100%</th>
                            </tr>
                        </tfoot>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
</div>
<!-- استدعاء قالب الفوتر -->
<?php include '../../includes/templates/footer.php'; ?>
<!-- مكتبة لتصدير الجداول -->
<script src="https://cdn.jsdelivr.net/npm/tableexport@5.2.0/dist/js/tableexport.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/xlsx@0.18.5/dist/xlsx.full.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/file-saver@2.0.5/dist/FileSaver.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf-autotable/3.5.28/jspdf.plugin.autotable.min.js"></script>
<script>
// تعريف المتغيرات العامة
const BASE_URL = '<?php echo BASE_URL; ?>';

$(document).ready(function() {
    // تهيئة المخططات البيانية
    let topServicesChart, categoriesChart;

    // تغيير نطاق التاريخ
    $('#dateRange').change(function() {
        const today = new Date();
        const value = $(this).val();

        switch(value) {
            case 'today':
                $('#startDate').val(formatDate(today));
                $('#endDate').val(formatDate(today));
                break;

            case 'yesterday':
                const yesterday = new Date(today);
                yesterday.setDate(yesterday.getDate() - 1);
                $('#startDate').val(formatDate(yesterday));
                $('#endDate').val(formatDate(yesterday));
                break;

            case 'this_week':
                const thisWeekStart = new Date(today);
                const day = thisWeekStart.getDay() || 7;
                if (day !== 1) {
                    thisWeekStart.setDate(thisWeekStart.getDate() - (day - 1));
                }
                $('#startDate').val(formatDate(thisWeekStart));
                $('#endDate').val(formatDate(today));
                break;

            case 'last_week':
                const lastWeekStart = new Date(today);
                lastWeekStart.setDate(lastWeekStart.getDate() - 7 - (lastWeekStart.getDay() - 1));
                const lastWeekEnd = new Date(lastWeekStart);
                lastWeekEnd.setDate(lastWeekEnd.getDate() + 6);
                $('#startDate').val(formatDate(lastWeekStart));
                $('#endDate').val(formatDate(lastWeekEnd));
                break;

            case 'this_month':
                const thisMonthStart = new Date(today.getFullYear(), today.getMonth(), 1);
                const thisMonthEnd = new Date(today.getFullYear(), today.getMonth() + 1, 0);
                $('#startDate').val(formatDate(thisMonthStart));
                $('#endDate').val(formatDate(thisMonthEnd));
                break;

            case 'last_month':
                const lastMonthStart = new Date(today.getFullYear(), today.getMonth() - 1, 1);
                const lastMonthEnd = new Date(today.getFullYear(), today.getMonth(), 0);
                $('#startDate').val(formatDate(lastMonthStart));
                $('#endDate').val(formatDate(lastMonthEnd));
                break;

            case 'this_year':
                const thisYearStart = new Date(today.getFullYear(), 0, 1);
                const thisYearEnd = new Date(today.getFullYear(), 11, 31);
                $('#startDate').val(formatDate(thisYearStart));
                $('#endDate').val(formatDate(thisYearEnd));
                break;

            case 'custom':
                // ابق على القيم الحالية
                break;
        }
    });

    // تنسيق التاريخ لـ YYYY-MM-DD
    function formatDate(date) {
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        return `${year}-${month}-${day}`;
    }

    // تقديم نموذج التقرير
    $('#reportForm').on('submit', function(e) {
        e.preventDefault();
        loadReport();
    });

    // تحميل التقرير
    function loadReport() {
        try {
            const reportForm = document.getElementById('reportForm');
            if (!reportForm) {
                console.error('لم يتم العثور على نموذج التقرير');
                $('#errorAlert').removeClass('d-none').text('لم يتم العثور على نموذج التقرير');
                return;
            }

            const formData = new FormData(reportForm);

            // إظهار تنبيه التحميل
            $('#loadingAlert').removeClass('d-none');
            $('#errorAlert').addClass('d-none');

            // طباعة بيانات النموذج للتحقق
            console.log('Form data:', {
                start_date: formData.get('start_date'),
                end_date: formData.get('end_date'),
                branch_id: formData.get('branch_id'),
                category_id: formData.get('category_id'),
                sort_by: formData.get('sort_by'),
                sort_dir: formData.get('sort_dir')
            });

            // جلب البيانات من API
            $.ajax({
                url: `${BASE_URL}api/reports.php?type=services`,
                type: 'POST',
                data: {
                    start_date: formData.get('start_date'),
                    end_date: formData.get('end_date'),
                    branch_id: formData.get('branch_id'),
                    category_id: formData.get('category_id'),
                    sort_by: formData.get('sort_by'),
                    sort_dir: formData.get('sort_dir')
                },
                dataType: 'json',
                success: function(response) {
                    // إخفاء تنبيه التحميل
                    $('#loadingAlert').addClass('d-none');

                    // طباعة الاستجابة للتحقق
                    console.log('API response:', response);

                    if (response && response.success) {
                        // إنشاء نسخة من البيانات للتعديل
                        const data = response.data || {};

                        // معالجة هيكل البيانات المختلف
                        // استخدام top_services أو services_performance إذا كانت موجودة
                        if ((!data.services || !Array.isArray(data.services)) &&
                            (data.top_services && Array.isArray(data.top_services))) {
                            console.log('استخدام top_services بدلاً من services');
                            data.services = data.top_services;
                        } else if ((!data.services || !Array.isArray(data.services)) &&
                                  (data.services_performance && Array.isArray(data.services_performance))) {
                            console.log('استخدام services_performance بدلاً من services');
                            data.services = data.services_performance;
                        } else if (!data.services || !Array.isArray(data.services)) {
                            console.log('إنشاء مصفوفة خدمات فارغة');
                            data.services = [];
                        }

                        // التأكد من وجود الفئات
                        if (!data.categories || !Array.isArray(data.categories)) {
                            console.log('إنشاء مصفوفة فئات فارغة');
                            data.categories = [];
                        }

                        // التأكد من وجود الملخص
                        if (!data.summary) {
                            console.log('إنشاء ملخص فارغ');
                            data.summary = {
                                total_count: 0,
                                total_revenue: 0,
                                average_price: 0
                            };
                        }

                        // إضافة معلومات الملخص المفقودة
                        if (!data.summary.total_count && data.summary.total_services) {
                            data.summary.total_count = data.summary.total_services;
                        }

                        // حساب إجمالي الإيرادات إذا لم يكن موجوداً
                        if (!data.summary.total_revenue) {
                            data.summary.total_revenue = data.services.reduce((sum, service) => {
                                return sum + parseFloat(service.total_sales || 0);
                            }, 0);
                            console.log('تم حساب إجمالي الإيرادات:', data.summary.total_revenue);
                        }

                        // حساب متوسط السعر إذا لم يكن موجوداً
                        if (!data.summary.average_price) {
                            const totalServices = data.services.length;
                            data.summary.average_price = totalServices > 0 ?
                                data.services.reduce((sum, service) => sum + parseFloat(service.price || 0), 0) / totalServices : 0;
                            console.log('تم حساب متوسط السعر:', data.summary.average_price);
                        }

                        // تحديث ملخص التقرير
                        updateReportSummary(data);

                        // تحديث الرسوم البيانية
                        updateCharts(data);

                        // تحديث جدول التقرير
                        updateReportTable(data);
                    } else {
                        const errorMessage = response && response.message ? response.message : 'حدث خطأ أثناء تحميل التقرير';
                        $('#errorAlert').removeClass('d-none').text(errorMessage);
                    }
                },
                error: function(xhr, status, error) {
                    // إخفاء تنبيه التحميل وإظهار الخطأ
                    $('#loadingAlert').addClass('d-none');
                    $('#errorAlert').removeClass('d-none').text('حدث خطأ أثناء الاتصال بالخادم');
                    console.error('AJAX error:', status, error);
                    console.error('Response text:', xhr.responseText);
                }
            });
        } catch (e) {
            console.error('Error in loadReport:', e);
            $('#loadingAlert').addClass('d-none');
            $('#errorAlert').removeClass('d-none').text('حدث خطأ غير متوقع');
        }
    }

    // تحديث ملخص التقرير
    function updateReportSummary(data) {
        // التحقق من وجود البيانات
        if (!data || !data.summary) {
            console.error('بيانات الملخص غير متوفرة');
            $('#totalServices').text('0');
            $('#totalRevenue').text(formatCurrency(0));
            $('#uniqueServices').text('0');
            $('#averageService').text(formatCurrency(0));
            return;
        }

        // تعيين قيم الملخص
        $('#totalServices').text(data.summary.total_count || 0);
        $('#totalRevenue').text(formatCurrency(data.summary.total_revenue || 0));

        // التحقق من وجود قائمة الخدمات
        if (data.services && Array.isArray(data.services)) {
            $('#uniqueServices').text(data.services.length);
        } else {
            console.error('قائمة الخدمات غير متوفرة أو ليست مصفوفة');
            $('#uniqueServices').text('0');
        }

        $('#averageService').text(formatCurrency(data.summary.average_price || 0));
    }

    // تحديث الرسوم البيانية
    function updateCharts(data) {
        // التحقق من وجود البيانات
        if (!data) {
            console.error('بيانات الرسوم البيانية غير متوفرة');
            updateTopServicesChart([]);
            updateCategoriesChart([]);
            return;
        }

        // تحديث مخطط أكثر الخدمات استخداماً
        if (data.services && Array.isArray(data.services)) {
            updateTopServicesChart(data.services);
        } else {
            console.error('قائمة الخدمات غير متوفرة أو ليست مصفوفة');
            updateTopServicesChart([]);
        }

        // تحديث مخطط الفئات
        if (data.categories && Array.isArray(data.categories)) {
            updateCategoriesChart(data.categories);
        } else {
            console.error('قائمة الفئات غير متوفرة أو ليست مصفوفة');
            updateCategoriesChart([]);
        }
    }

    // تحديث مخطط أكثر الخدمات استخداماً
    function updateTopServicesChart(services) {
        const ctx = document.getElementById('topServicesChart').getContext('2d');

        // تدمير المخطط السابق إذا كان موجودًا
        if (topServicesChart) {
            topServicesChart.destroy();
        }

        // التحقق من وجود البيانات
        if (!services || !Array.isArray(services) || services.length === 0) {
            console.log('لا توجد بيانات للخدمات لعرضها في المخطط');
            // إنشاء مخطط فارغ
            topServicesChart = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: ['لا توجد بيانات'],
                    datasets: [{
                        label: 'عدد مرات الاستخدام',
                        data: [0],
                        backgroundColor: 'rgba(200, 200, 200, 0.8)',
                        borderColor: 'rgba(200, 200, 200, 1)',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    indexAxis: 'y',
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        x: {
                            beginAtZero: true
                        }
                    }
                }
            });
            return;
        }

        // أخذ أعلى 10 خدمات فقط
        const topServices = services.slice(0, 10);

        // طباعة الخدمات للتحقق
        console.log('Top services for chart:', topServices);

        // التعامل مع هيكل البيانات المختلف
        const labels = topServices.map(service => service.name);
        const counts = topServices.map(service => parseInt(service.count || service.total_bookings || 0));

        topServicesChart = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: labels,
                datasets: [{
                    label: 'عدد مرات الاستخدام',
                    data: counts,
                    backgroundColor: 'rgba(52, 152, 219, 0.8)',
                    borderColor: 'rgba(52, 152, 219, 1)',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                indexAxis: 'y',
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    x: {
                        beginAtZero: true
                    }
                }
            }
        });
    }

    // تحديث مخطط الفئات
    function updateCategoriesChart(categories) {
        const ctx = document.getElementById('categoriesChart').getContext('2d');

        // تدمير المخطط السابق إذا كان موجودًا
        if (categoriesChart) {
            categoriesChart.destroy();
        }

        // التحقق من وجود البيانات
        if (!categories || !Array.isArray(categories) || categories.length === 0) {
            console.log('لا توجد بيانات للفئات لعرضها في المخطط');
            // إنشاء مخطط فارغ
            categoriesChart = new Chart(ctx, {
                type: 'pie',
                data: {
                    labels: ['لا توجد بيانات'],
                    datasets: [{
                        data: [1],
                        backgroundColor: ['rgba(200, 200, 200, 0.8)'],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });
            return;
        }

        // طباعة الفئات للتحقق
        console.log('Categories for chart:', categories);

        // التعامل مع هيكل البيانات المختلف
        const labels = categories.map(category => category.name);
        const counts = categories.map(category => parseInt(category.count || category.services_count || category.total_bookings || 0));
        const revenues = categories.map(category => parseFloat(category.revenue || category.total_sales || 0));

        categoriesChart = new Chart(ctx, {
            type: 'pie',
            data: {
                labels: labels,
                datasets: [{
                    data: revenues,
                    backgroundColor: [
                        'rgba(52, 152, 219, 0.8)',
                        'rgba(46, 204, 113, 0.8)',
                        'rgba(231, 76, 60, 0.8)',
                        'rgba(241, 196, 15, 0.8)',
                        'rgba(155, 89, 182, 0.8)',
                        'rgba(26, 188, 156, 0.8)',
                        'rgba(230, 126, 34, 0.8)'
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const label = context.label || '';
                                const value = formatCurrency(context.raw);
                                const category = categories[context.dataIndex];
                                return `${label}: ${value} (${category.count} خدمة)`;
                            }
                        }
                    }
                }
            }
        });
    }

    // تحديث جدول التقرير
    function updateReportTable(data) {
        const tableBody = $('#reportTableBody');
        tableBody.empty();

        // التحقق من وجود البيانات
        if (!data || !data.services || !Array.isArray(data.services)) {
            console.error('بيانات الخدمات غير متوفرة أو ليست مصفوفة');
            $('#footerTotalCount').text('0');
            $('#footerTotalRevenue').text(formatCurrency(0));
            return;
        }

        let totalCount = 0;
        let totalRevenue = 0;

        data.services.forEach((service, index) => {
            // التحقق من وجود البيانات المطلوبة
            // التعامل مع هيكل البيانات المختلف
            const count = parseInt(service.count || service.total_bookings || 0);
            const revenue = parseFloat(service.revenue || service.total_sales || 0);
            const price = parseFloat(service.price || 0);

            // الحصول على اسم الفئة
            let category = service.category || service.category_name || 'بدون فئة';

            totalCount += count;
            totalRevenue += revenue;

            // حساب النسبة المئوية
            const totalRevenueValue = data.summary && data.summary.total_revenue ? parseFloat(data.summary.total_revenue) : 0;
            const percentage = totalRevenueValue > 0
                ? ((revenue / totalRevenueValue) * 100).toFixed(2)
                : '0.00';

            // طباعة بيانات الخدمة للتحقق
            console.log(`Service ${index}:`, {
                name: service.name,
                category: category,
                price: price,
                count: count,
                revenue: revenue
            });

            const row = `
                <tr>
                    <td>${index + 1}</td>
                    <td>${service.name || ''}</td>
                    <td>${category}</td>
                    <td>${formatCurrency(price)}</td>
                    <td>${count}</td>
                    <td>${formatCurrency(revenue)}</td>
                    <td>${percentage}%</td>
                </tr>
            `;
            tableBody.append(row);
        });

        // تحديث الإجماليات في الجدول
        $('#footerTotalCount').text(totalCount);
        $('#footerTotalRevenue').text(formatCurrency(totalRevenue));
    }

    // تنسيق المبالغ المالية
    function formatCurrency(amount) {
        return parseFloat(amount).toFixed(2) + ' <?php echo $currencySymbol; ?>';
    }

    // تصدير إلى Excel
    $('#exportExcel').click(function() {
        $('#reportTable').tableExport({
            headers: true,
            footers: true,
            formats: ['xlsx'],
            filename: 'تقرير_الخدمات',
            bootstrap: true,
            exportButtons: false,
            position: 'bottom',
            ignoreRows: null,
            ignoreCols: null,
            trimWhitespace: true
        });
    });

    // تصدير إلى PDF
    $('#exportPdf').click(function() {
        $('#reportTable').tableExport({
            headers: true,
            footers: true,
            formats: ['pdf'],
            filename: 'تقرير_الخدمات',
            bootstrap: true,
            exportButtons: false,
            position: 'bottom',
            ignoreRows: null,
            ignoreCols: null,
            trimWhitespace: true,
            RTL: true,
            jspdf: {
                orientation: 'p',
                margins: { left: 20, top: 10 },
                autotable: {
                    styles: {
                        rtl: true,
                        overflow: 'linebreak',
                        fontSize: 10,
                        cellPadding: 2
                    },
                    headerStyles: { fillColor: [41, 128, 185], textColor: 255 }
                }
            }
        });
    });

    // طباعة التقرير
    $('#printReport').click(function() {
        window.print();
    });

    // تحميل التقرير الافتراضي عند تحميل الصفحة
    loadReport();
});
</script>