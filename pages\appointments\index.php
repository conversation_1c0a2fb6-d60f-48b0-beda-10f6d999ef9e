<?php
/**
 * صفحة إدارة المواعيد
 * تعرض قائمة المواعيد وتتيح البحث والتصفية وإدارة المواعيد
 */

// تعريف ثابت لمنع الوصول المباشر للملفات المرتبطة
define('BASEPATH', true);

// استدعاء ملفات التهيئة
require_once '../../config/init.php';

// التحقق من تسجيل الدخول
redirectIfNotLoggedIn();

// التحقق من وجود صلاحية عرض المواعيد
if (!hasPermission('appointments_view')) {
    setErrorMessage(ACCESS_DENIED_MSG);
    redirect(BASE_URL . 'pages/dashboard.php');
    exit;
}

// عنوان الصفحة
$pageTitle = 'إدارة المواعيد';

// إنشاء كائنات النماذج
$appointmentModel = new Appointment($db);
$customerModel = new Customer($db);
$serviceModel = new Service($db);
$employeeModel = new Employee($db);
$branchModel = new Branch($db);

// استرجاع قائمة الفروع للمدير
$branches = [];
if ($_SESSION['user_role'] == ROLE_ADMIN || $_SESSION['user_role'] == ROLE_MANAGER) {
    $branches = $branchModel->getBranches(['is_active' => 1]);
}

// استرجاع قائمة الموظفين
$employees = $employeeModel->getEmployees([
    'is_active' => 1,
    'branch_id' => ($_SESSION['user_role'] == ROLE_ADMIN) ? null : $_SESSION['user_branch_id']
]);

// استرجاع قائمة الخدمات
$services = $serviceModel->getServices([
    'is_active' => 1,
    'branch_id' => ($_SESSION['user_role'] == ROLE_ADMIN) ? null : $_SESSION['user_branch_id']
]);

// حالات المواعيد
$appointmentStatus = [
    'booked' => 'محجوز',
    'waiting' => 'في الانتظار',
    'completed' => 'مكتمل',
    'cancelled' => 'ملغي'
];

// تاريخ اليوم
$today = date('Y-m-d');

// استدعاء رأس الصفحة
include '../../includes/templates/header.php';
?>

<!-- استدعاء ملف جافاسكريبت خاص بإرسال تذكيرات المواعيد عبر واتساب -->
<script src="<?= BASE_URL ?>assets/js/whatsapp-appointment-reminder.js"></script>

<!-- تنسيقات خاصة بالصفحة -->
<style>
    .stats-box {
        background-color: #f8f9fa;
        border-radius: 10px;
        padding: 15px;
        margin-bottom: 20px;
        text-align: center;
        transition: all 0.3s ease;
    }

    .stats-box:hover {
        transform: translateY(-5px);
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    }

    .stats-box.today {
        background-color: rgba(52, 152, 219, 0.15);
    }

    .stats-box.tomorrow {
        background-color: rgba(46, 204, 113, 0.15);
    }

    .stats-box.week {
        background-color: rgba(241, 196, 15, 0.15);
    }

    .stats-box.month {
        background-color: rgba(155, 89, 182, 0.15);
    }

    .stats-box h3 {
        font-size: 24px;
        font-weight: bold;
        margin-bottom: 0;
    }

    .stats-box p {
        margin-bottom: 0;
        color: #6c757d;
    }

    .filter-section {
        background-color: #fff;
        border-radius: 10px;
        padding: 15px;
        margin-bottom: 20px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.05);
    }

    .btn-icon {
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .btn-icon i {
        margin-left: 5px;
    }

    .card-header {
        background-color: #fff;
        border-bottom: 1px solid rgba(0,0,0,.125);
    }

    .table-actions .btn {
        padding: 0.25rem 0.5rem;
        font-size: 0.875rem;
    }

    .appointment-status {
        font-weight: bold;
        padding: 5px 10px;
        border-radius: 20px;
    }

    .status-booked {
        background-color: #4caf50 !important;
        border-color: #4caf50 !important;
    }

    .status-waiting {
        background-color: #ff9800 !important;
        border-color: #ff9800 !important;
    }

    .status-completed {
        background-color: #2196f3 !important;
        border-color: #2196f3 !important;
    }

    .status-cancelled {
        background-color: #f44336 !important;
        border-color: #f44336 !important;
    }

    .fc-event {
        cursor: pointer;
        padding: 2px 4px;
        margin: 1px 0;
        border-radius: 3px;
    }

    /* تصحيح مشكلة عرض التقويم */
    .fc-view-harness {
        min-height: 500px;
    }

    /* تحسين مظهر الأحداث */
    .fc-event {
        cursor: pointer;
        padding: 2px 4px;
        margin: 1px 0;
        border-radius: 3px;
    }

    /* تصحيح مشكلة RTL في التقويم */
    .fc-direction-rtl .fc-toolbar-title {
        text-align: center;
    }

    /* تحسين مظهر الأزرار */
    .main-actions .btn {
        margin-left: 5px;
    }

    /* تنسيق الجدول */
    #appointmentsTable th,
    #appointmentsTable td {
        vertical-align: middle;
    }

    /* تحسين ظهور صف العنوان بالجدول */
    #appointmentsTable thead th {
        background-color: #f8f9fa;
        color: #495057;
        font-weight: 600;
    }

    /* أيقونات لحالات المواعيد في التقويم */
    .fc-event.status-booked {
        background-color: #3498db;
        border-color: #2980b9;
    }

    .fc-event.status-waiting {
        background-color: #f39c12;
        border-color: #d35400;
    }

    .fc-event.status-completed {
        background-color: #2ecc71;
        border-color: #27ae60;
    }

    .fc-event.status-cancelled {
        background-color: #e74c3c;
        border-color: #c0392b;
    }
</style>

<!-- المحتوى الرئيسي للصفحة -->
<div class="container-fluid">
    <!-- رأس الصفحة مع الأزرار -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h5 mb-0">
                <i class="fas fa-calendar-alt"></i> إدارة المواعيد
            </h1>
        </div>
        <div class="main-actions">
            <?php if (hasPermission('appointments_create')): ?>
            <a href="add.php" class="btn btn-primary btn-icon">
                <i class="fas fa-plus"></i> حجز موعد جديد
            </a>
            <?php endif; ?>

        </div>
    </div>

    <!-- صف الإحصائيات -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6">
            <div class="stats-box today">
                <h3 id="today-count">0</h3>
                <p>مواعيد اليوم</p>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="stats-box tomorrow">
                <h3 id="tomorrow-count">0</h3>
                <p>مواعيد الغد</p>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="stats-box week">
                <h3 id="week-count">0</h3>
                <p>مواعيد هذا الأسبوع</p>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="stats-box month">
                <h3 id="month-count">0</h3>
                <p>مواعيد هذا الشهر</p>
            </div>
        </div>
    </div>

    <!-- قسم الفلترة والبحث -->
    <div class="filter-section mb-4">
        <form id="filterForm" class="row align-items-end">
            <!-- فلتر التاريخ -->
            <div class="col-lg-2 col-md-3 mb-3">
                <label for="date_filter" class="form-label">التاريخ</label>
                <input type="text" class="form-control date-picker" id="date_filter" name="date" value="<?php echo date('Y/m/d'); ?>">
            </div>

            <!-- فلتر الفرع -->
            <?php if (!empty($branches) && ($_SESSION['user_role'] == ROLE_ADMIN || $_SESSION['user_role'] == ROLE_MANAGER)): ?>
            <div class="col-lg-2 col-md-3 mb-3">
                <label for="branch_filter" class="form-label">الفرع</label>
                <select class="form-select" id="branch_filter" name="branch_id">
                    <option value="" selected>الكل</option>
                    <?php foreach ($branches as $branch): ?>
                        <option value="<?php echo $branch['id']; ?>">
                            <?php echo htmlspecialchars($branch['name']); ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>
            <?php endif; ?>

            <!-- فلتر الموظف -->
            <div class="col-lg-2 col-md-3 mb-3">
                <label for="employee_filter" class="form-label">الموظف</label>
                <select class="form-select" id="employee_filter" name="employee_id">
                    <option value="">الكل</option>
                    <?php foreach ($employees as $employee): ?>
                        <option value="<?php echo $employee['id']; ?>"><?php echo htmlspecialchars($employee['name']); ?></option>
                    <?php endforeach; ?>
                </select>
            </div>

            <!-- فلتر الخدمة -->
            <div class="col-lg-2 col-md-3 mb-3">
                <label for="service_filter" class="form-label">الخدمة</label>
                <select class="form-select" id="service_filter" name="service_id">
                    <option value="">الكل</option>
                    <?php foreach ($services as $service): ?>
                        <option value="<?php echo $service['id']; ?>"><?php echo htmlspecialchars($service['name']); ?></option>
                    <?php endforeach; ?>
                </select>
            </div>

            <!-- فلتر الحالة -->
            <div class="col-lg-2 col-md-3 mb-3">
                <label for="status_filter" class="form-label">الحالة</label>
                <select class="form-select" id="status_filter" name="status">
                    <option value="">الكل</option>
                    <?php foreach ($appointmentStatus as $value => $label): ?>
                        <option value="<?php echo $value; ?>"><?php echo $label; ?></option>
                    <?php endforeach; ?>
                </select>
            </div>

            <!-- أزرار البحث وإعادة الضبط -->
            <div class="col-lg-2 col-md-3 mb-3">
                <div class="d-flex">
                    <button type="submit" class="btn btn-primary flex-grow-1 me-2">
                        <i class="fas fa-search"></i> بحث
                    </button>
                    <button type="button" id="resetFilters" class="btn btn-secondary">
                        <i class="fas fa-redo"></i>
                    </button>
                </div>
            </div>
        </form>
    </div>

    <!-- طرق العرض (جدول/تقويم) -->
    <div class="card mb-4">
        <div class="card-body">
            <ul class="nav nav-tabs" id="viewTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="table-tab" data-bs-toggle="tab" data-bs-target="#table-view" type="button" role="tab" aria-controls="table-view" aria-selected="true">
                        <i class="fas fa-list me-1"></i> عرض القائمة
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="calendar-tab" data-bs-toggle="tab" data-bs-target="#calendar-view" type="button" role="tab" aria-controls="calendar-view" aria-selected="false">
                        <i class="fas fa-calendar-alt me-1"></i> عرض التقويم
                    </button>
                </li>
            </ul>

            <div class="tab-content mt-3" id="viewTabsContent">
                <!-- عرض الجدول -->
                <div class="tab-pane fade show active" id="table-view" role="tabpanel" aria-labelledby="table-tab">
                    <!-- مؤشر التحميل -->
                    <div id="loadingIndicator" class="text-center py-5" style="display: none;">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">جاري التحميل...</span>
                        </div>
                        <p class="mt-2">جاري تحميل البيانات...</p>
                    </div>

                    <!-- رسالة الخطأ -->
                    <div id="errorMessage" class="alert alert-danger" style="display: none;">
                        <i class="fas fa-exclamation-circle me-2"></i>
                        <span id="errorText">حدث خطأ أثناء تحميل البيانات. يرجى المحاولة مرة أخرى.</span>
                    </div>

                    <!-- جدول المواعيد -->
                    <div class="table-responsive">
                        <table id="appointmentsTable" class="table table-bordered table-hover">
                            <thead>
                                <tr>
                                    <th>#</th>
                                    <th>العميل</th>
                                    <th>الخدمة</th>
                                    <th>الموظف</th>
                                    <th>التاريخ</th>
                                    <th>الوقت</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- سيتم تعبئة البيانات عبر AJAX -->
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- عرض التقويم -->
                <div class="tab-pane fade" id="calendar-view" role="tabpanel" aria-labelledby="calendar-tab">
                    <div id="calendar"></div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- مودال تفاصيل الموعد -->
<div class="modal fade" id="appointmentDetailsModal" tabindex="-1" aria-labelledby="appointmentDetailsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="appointmentDetailsModalLabel">تفاصيل الموعد</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="إغلاق"></button>
            </div>
            <div class="modal-body">
                <input type="hidden" id="appointment_id">
                <input type="hidden" id="customer_id">

                <div class="row mb-3">
                    <div class="col-md-6">
                        <h6 class="fw-bold">بيانات العميل</h6>
                        <div class="mb-2">
                            <label class="fw-bold">الاسم:</label>
                            <span id="customer_name"></span>
                        </div>
                        <div class="mb-2">
                            <label class="fw-bold">رقم الهاتف:</label>
                            <span id="customer_phone"></span>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <h6 class="fw-bold">بيانات الموعد</h6>
                        <div class="mb-2">
                            <label class="fw-bold">التاريخ:</label>
                            <span id="appointment_date"></span>
                        </div>
                        <div class="mb-2">
                            <label class="fw-bold">الوقت:</label>
                            <span id="appointment_time"></span>
                        </div>
                        <div class="mb-2">
                            <label class="fw-bold">الخدمة:</label>
                            <span id="appointment_service"></span>
                        </div>
                        <div class="mb-2">
                            <label class="fw-bold">الموظف:</label>
                            <span id="appointment_employee"></span>
                        </div>
                        <div class="mb-2">
                            <label class="fw-bold">الحالة:</label>
                            <span id="appointment_status"></span>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-12">
                        <h6 class="fw-bold">ملاحظات</h6>
                        <p id="appointment_notes" class="text-muted"></p>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <?php if (hasPermission('pos_view')): ?>
                <a id="createInvoiceBtn" href="#" class="btn btn-primary">
                    <i class="fas fa-cash-register me-1"></i> إنشاء فاتورة
                </a>
                <?php endif; ?>

                <button id="remindCustomerBtn" type="button" class="btn btn-success" data-id="" data-phone="" data-name="" data-date="" data-time="">
                    <i class="fab fa-whatsapp me-1"></i> تذكير العميل
                </button>

                <?php if (hasPermission('appointments_edit')): ?>
                <a id="editBtn" href="#" class="btn btn-secondary">
                    <i class="fas fa-edit me-1"></i> تعديل
                </a>

                <div class="dropdown d-inline-block">
                    <button class="btn btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                        تغيير الحالة
                    </button>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item change-status" href="javascript:void(0)" data-status="booked">محجوز</a></li>
                        <li><a class="dropdown-item change-status" href="javascript:void(0)" data-status="waiting">في الانتظار</a></li>
                        <li><a class="dropdown-item change-status" href="javascript:void(0)" data-status="completed">مكتمل</a></li>
                        <li><a class="dropdown-item change-status" href="javascript:void(0)" data-status="cancelled">ملغي</a></li>
                    </ul>
                </div>
                <?php endif; ?>

                <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">إغلاق</button>
            </div>
        </div>
    </div>
</div>

<!-- استدعاء قالب الفوتر -->
<?php include '../../includes/templates/footer.php'; ?>

<!-- استدعاء مكتبات إضافية -->
<link href="https://cdn.jsdelivr.net/npm/fullcalendar@5.10.1/main.min.css" rel="stylesheet">
<script src="https://cdn.jsdelivr.net/npm/fullcalendar@5.10.1/main.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/fullcalendar@5.10.1/locales/ar.js"></script>

<!-- سكربت تصحيح مشكلة فلتر الفروع -->
<script>
// سكربت لتصحيح مشكلة فلتر الفروع
$(document).ready(function() {
    // التحقق من دور المستخدم
    const userRole = '<?php echo $_SESSION["user_role"]; ?>';

    // إذا كان المستخدم مديراً
    if (userRole === 'admin' || userRole === 'manager') {
        // التأكد من أن فلتر الفروع غير معطل
        $('#branch_filter').prop('disabled', false);
        console.log('تم تفعيل فلتر الفروع للمدير');

        // التحقق من حالة الفلتر بعد فترة قصيرة
        setTimeout(function() {
            if ($('#branch_filter').prop('disabled')) {
                $('#branch_filter').prop('disabled', false);
                console.log('تم تفعيل فلتر الفروع مرة أخرى');
            }
        }, 1000);
    }
});
</script>

<script>
// وظيفة للحصول على لون حسب حالة الموعد
function getStatusColor(status) {
    switch(status) {
        case 'booked':
            return '#4caf50'; // أخضر
        case 'waiting':
            return '#ff9800'; // برتقالي
        case 'completed':
            return '#2196f3'; // أزرق
        case 'cancelled':
            return '#f44336'; // أحمر
        default:
            return '#9e9e9e'; // رمادي
    }
}

// تحويل حالة الموعد إلى نص عربي
function getStatusText(status) {
    switch(status) {
        case 'booked':
            return 'محجوز';
        case 'waiting':
            return 'في الانتظار';
        case 'completed':
            return 'مكتمل';
        case 'cancelled':
            return 'ملغي';
        default:
            return status;
    }
}

$(document).ready(function() {
    // ------ متغيرات عامة ------
    let appointmentsData = []; // تخزين بيانات المواعيد
    let appointmentsTable; // جدول المواعيد
    let calendarInstance = null; // متغير عام للتقويم

    // تعريف متغيرات الصلاحيات
    const hasEditPermission = typeof hasPermission === 'function' && hasPermission('appointments_edit');
    const hasPosPermission = typeof hasPermission === 'function' && hasPermission('pos_access');
    // التحقق من دور المستخدم بشكل مباشر
    const userRole = '<?php echo $_SESSION['user_role']; ?>';
    const hasAdminPermission = userRole === 'admin';
    const hasManagerPermission = userRole === 'manager';

    console.log('دور المستخدم:', userRole);
    console.log('هل المستخدم مدير:', hasAdminPermission);

    // الحصول على معرف فرع المستخدم الحالي من الجلسة
    const userBranchId = <?php echo $_SESSION['user_branch_id'] ?? 'null'; ?>;

    // تعطيل عناصر الفلترة إذا لم يكن المستخدم مدير أو مشرف
    if (!hasAdminPermission && !hasManagerPermission) {
        $('#branch_filter').prop('disabled', true).val(userBranchId);
    } else {
        // التأكد من أن عنصر الفلترة غير معطل للمدير
        $('#branch_filter').prop('disabled', false);
        console.log('تم تفعيل فلتر الفروع للمدير');
    }

    // ------ دوال مساعدة للواجهة ------

    // التحكم في مؤشرات التحميل
    function showLoading() {
        $('#loadingIndicator').show();
        $('#errorMessage').hide();
        $('#appointmentsTable_wrapper').hide();
    }

    function hideLoading() {
        $('#loadingIndicator').hide();
        $('#appointmentsTable_wrapper').show();
    }

    function showError(message) {
        $('#loadingIndicator').hide();
        $('#errorText').text(message || 'حدث خطأ أثناء تحميل البيانات');
        $('#errorMessage').show();
    }

    // إظهار رسالة تنبيه
    function showAlert(message, type = 'success') {
        // إزالة أي تنبيهات سابقة
        $('.alert-temporary').remove();

        const alertDiv = $(`<div class="alert alert-${type} alert-dismissible fade show alert-temporary" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="إغلاق"></button>
        </div>`);

        // إظهار التنبيه أعلى الصفحة
        $('.container-fluid').prepend(alertDiv);

        // إخفاء التنبيه تلقائيًا بعد 3 ثوان
        setTimeout(function() {
            alertDiv.alert('close');
        }, 3000);
    }

    // ------ تهيئة عناصر واجهة المستخدم ------

    // تهيئة اختيار التاريخ
    $('.date-picker').daterangepicker({
        singleDatePicker: true,
        showDropdowns: true,
        locale: {
            format: 'YYYY-MM-DD',
            applyLabel: 'تطبيق',
            cancelLabel: 'إلغاء',
            daysOfWeek: ['أح', 'إث', 'ثل', 'أر', 'خم', 'جم', 'سب'],
            monthNames: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو', 'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'],
            firstDay: 0
        }
    });

    // ------ تهيئة عناصر الفلترة ------
    function initializeFilters() {
        console.log('تهيئة عناصر الفلترة');
        console.log('صلاحيات المستخدم:', {
            hasAdminPermission: hasAdminPermission,
            hasManagerPermission: hasManagerPermission,
            userRole: userRole,
            userBranchId: userBranchId
        });

        // تهيئة فلتر الفروع
        if (hasAdminPermission || hasManagerPermission) {
            console.log('تهيئة فلتر الفروع للمدير أو المشرف');

            // التأكد من أن عنصر الفلترة غير معطل
            $('#branch_filter').prop('disabled', false);

            // تحميل قائمة الفروع للمدير أو المشرف
            $.ajax({
                url: '../../api/branches.php',
                type: 'GET',
                data: { action: 'list' },
                dataType: 'json',
                success: function(response) {
                    if (response.status === 'success') {
                        const branches = response.branches || [];
                        let options = '<option value="">كل الفروع</option>';

                        branches.forEach(function(branch) {
                            options += `<option value="${branch.id}">${branch.name}</option>`;
                        });

                        $('#branch_filter').html(options);
                        console.log('تم تحميل قائمة الفروع:', branches.length);
                    } else {
                        console.error('خطأ في تحميل قائمة الفروع:', response.message || 'خطأ غير معروف');
                    }
                },
                error: function(xhr, status, error) {
                    console.error('خطأ في طلب قائمة الفروع:', error);
                }
            });
        } else {
            console.log('تهيئة فلتر الفروع للمستخدم العادي');
            // للمستخدم العادي، تقييد الفلترة بفرع المستخدم فقط
            $('#branch_filter').prop('disabled', true).val(userBranchId);

            // تحميل اسم الفرع
            $.ajax({
                url: '../../api/branches.php',
                type: 'GET',
                data: { action: 'get', id: userBranchId },
                dataType: 'json',
                success: function(response) {
                    if (response.status === 'success' && response.branch) {
                        $('#branch_filter').html(`<option value="${response.branch.id}">${response.branch.name}</option>`);
                        console.log('تم تحميل اسم الفرع:', response.branch.name);
                    } else {
                        console.error('خطأ في تحميل اسم الفرع:', response.message || 'خطأ غير معروف');
                    }
                },
                error: function(xhr, status, error) {
                    console.error('خطأ في طلب اسم الفرع:', error);
                }
            });
        }

        // تهيئة فلتر الموظفين
        loadEmployeeFilter();

        // تهيئة فلتر الخدمات
        loadServiceFilter();

        // تهيئة فلتر الحالة
        initStatusFilter();
    }

    // تهيئة فلتر الحالة
    function initStatusFilter() {
        const statusOptions = [
            { value: '', label: 'كل الحالات' },
            { value: 'pending', label: 'قيد الانتظار' },
            { value: 'confirmed', label: 'مؤكد' },
            { value: 'completed', label: 'مكتمل' },
            { value: 'cancelled', label: 'ملغي' },
            { value: 'no_show', label: 'لم يحضر' }
        ];

        let options = '';
        statusOptions.forEach(function(status) {
            options += `<option value="${status.value}">${status.label}</option>`;
        });

        $('#status_filter').html(options);
    }

    // دالة تحميل فلتر الموظفين
    function loadEmployeeFilter() {
        // تحديد معرف الفرع المستخدم في الفلترة
        let branchId = '';

        if (hasAdminPermission || hasManagerPermission) {
            // المدير أو المشرف يمكنه اختيار أي فرع
            branchId = $('#branch_filter').val() || '';
            console.log('تحميل الموظفين للمدير/المشرف - الفرع المختار:', branchId);
        } else {
            // المستخدم العادي مقيد بفرعه فقط
            branchId = userBranchId;
            console.log('تحميل الموظفين للمستخدم العادي - فرع المستخدم:', branchId);
        }

        // طلب قائمة الموظفين حسب الفرع
        $.ajax({
            url: '../../api/employees.php',
            type: 'GET',
            data: {
                action: 'get_employees',
                branch_id: branchId || ''
            },
            dataType: 'json',
            success: function(response) {
                if (response.status === 'success') {
                    const employees = response.employees || [];
                    let options = '<option value="">كل الموظفين</option>';

                    employees.forEach(function(employee) {
                        options += `<option value="${employee.id}">${employee.name}</option>`;
                    });

                    $('#employee_filter').html(options);
                    console.log('تم تحميل قائمة الموظفين:', employees.length);
                } else {
                    console.error('خطأ في تحميل قائمة الموظفين:', response.message || 'خطأ غير معروف');
                }
            },
            error: function(xhr, status, error) {
                console.error('خطأ في طلب قائمة الموظفين:', error);
            }
        });
    }

    // دالة تحميل فلتر الخدمات
    function loadServiceFilter() {
        // تحديد معرف الفرع المستخدم في الفلترة
        let branchId = '';

        if (hasAdminPermission || hasManagerPermission) {
            // المدير أو المشرف يمكنه اختيار أي فرع
            branchId = $('#branch_filter').val() || '';
            console.log('تحميل الخدمات للمدير/المشرف - الفرع المختار:', branchId);
        } else {
            // المستخدم العادي مقيد بفرعه فقط
            branchId = userBranchId;
            console.log('تحميل الخدمات للمستخدم العادي - فرع المستخدم:', branchId);
        }

        // طلب قائمة الخدمات حسب الفرع
        $.ajax({
            url: '../../api/services.php',
            type: 'GET',
            data: {
                action: 'list',
                branch_id: branchId || ''
            },
            dataType: 'json',
            success: function(response) {
                if (response.status === 'success') {
                    const services = response.services || [];
                    let options = '<option value="">كل الخدمات</option>';

                    services.forEach(function(service) {
                        options += `<option value="${service.id}">${service.name}</option>`;
                    });

                    $('#service_filter').html(options);
                    console.log('تم تحميل قائمة الخدمات:', services.length);
                } else {
                    console.error('خطأ في استجابة الخادم:', response.message || 'خطأ غير معروف');
                    $('#service_filter').html('<option value="">كل الخدمات</option>');
                }
            },
            error: function(xhr, status, error) {
                console.error('خطأ في تحميل قائمة الخدمات:', error);
                $('#service_filter').html('<option value="">كل الخدمات</option>');
            }
        });
    }

    // إضافة مستمع حدث لتغيير الفرع لتحديث قائمة الموظفين والخدمات
    $('#branch_filter').on('change', function() {
        if (hasAdminPermission || hasManagerPermission) {
            loadEmployeeFilter();
            loadServiceFilter();
        }
    });

    // استدعاء دالة تهيئة الفلاتر
    initializeFilters();

    // ------ دوال أساسية لإدارة البيانات ------

    // الحصول على معلمات الفلترة الحالية
    function getFilterParams() {
        // التحقق من صلاحيات المستخدم للفلترة حسب الفرع
        let branchId = '';

        if (hasAdminPermission || hasManagerPermission) {
            // المدير أو المشرف يمكنه اختيار أي فرع
            branchId = $('#branch_filter').val() || '';
            console.log('المستخدم مدير أو مشرف - الفرع المختار:', branchId);
        } else {
            // المستخدم العادي مقيد بفرعه فقط
            branchId = userBranchId;
            console.log('المستخدم عادي - فرع المستخدم:', branchId);
        }

        const params = {
            branch_id: branchId,
            date: $('#date_filter').val() || '',
            employee_id: $('#employee_filter').val() || '',
            service_id: $('#service_filter').val() || '',
            status: $('#status_filter').val() || ''
        };

        console.log('معلمات الفلترة:', params);
        return params;
    }

    // تحديث الإحصائيات من البيانات المخزنة
    function updateStats(statsData) {
        if (!statsData) return;

        $('#today-count').text(statsData.today || 0);
        $('#tomorrow-count').text(statsData.tomorrow || 0);
        $('#week-count').text(statsData.week || 0);
        $('#month-count').text(statsData.month || 0);
    }

    // الوظيفة الرئيسية لتحديث جميع البيانات
    function refreshData() {
        showLoading();

        // جمع معلمات الفلترة مع مراعاة صلاحيات المستخدم
        const filterParams = getFilterParams();

        // إضافة معامل العمل للطلب
        filterParams.action = 'filter';

        // للتقويم، نضيف نطاق التاريخ إذا كان التقويم نشطًا
        if ($('#calendar-tab').hasClass('active') && calendarInstance) {
            const calendarApi = calendarInstance.getApi();
            const view = calendarApi.view;
            filterParams.start_date = moment(view.activeStart).format('YYYY-MM-DD');
        }

        // طلب واحد للحصول على جميع البيانات المطلوبة
        $.ajax({
            url: '../../api/appointments.php',
            type: 'GET',
            data: filterParams,
            dataType: 'json',
            success: function(response) {
                if (response.status === 'success') {
                    // حفظ البيانات للاستخدام في المكونات الأخرى
                    appointmentsData = response.appointments || [];

                    // تحديث الجدول
                    if (appointmentsTable) {
                        appointmentsTable.clear().rows.add(appointmentsData).draw();
                    }

                    // تحديث التقويم إذا كان مرئ<|im_start|>
                    if ($('#calendar-tab').hasClass('active') && calendarInstance) {
                        try {
                            calendarInstance.refetchEvents();
                        } catch (error) {
                            console.error('خطأ في تحديث أحداث التقويم:', error);
                        }
                    }

                    // تحديث الإحصائيات
                    if (response.stats) {
                        updateStats(response.stats);
                    } else {
                        // إذا لم يتم إرجاع إحصائيات، نحتاج إلى طلبها بشكل منفصل
                        loadAppointmentStats();
                    }

                    hideLoading();
                } else {
                    showError(response.message);
                }
            },
            error: function(xhr, status, error) {
                console.error('خطأ في طلب البيانات:', status, error);
                showError('فشل الاتصال بالخادم. يرجى المحاولة مرة أخرى لاحقًا.');
            }
        });
    }

    // تحميل إحصائيات المواعيد (في حالة الحاجة إلى تحديثها بشكل منفصل)
    function loadAppointmentStats() {
        // جمع معلمات الفلترة الحالية مع مراعاة صلاحيات المستخدم
        const filterParams = getFilterParams();

        $.ajax({
            url: '../../api/appointments.php',
            type: 'GET',
            data: {
                action: 'get_stats',
                branch_id: filterParams.branch_id || '',
                employee_id: filterParams.employee_id || '',
                date: filterParams.date || ''
            },
            dataType: 'json',
            success: function(response) {
                if (response && response.status === 'success' && response.stats) {
                    updateStats(response.stats);
                } else {
                    console.warn('لم يتم استلام إحصائيات صالحة من الخادم');
                }
            },
            error: function(xhr, status, error) {
                console.error('خطأ في طلب الإحصائيات:', status, error);
            }
        });
    }

    // ------ تهيئة جدول المواعيد ------
    appointmentsTable = $('#appointmentsTable').DataTable({
        processing: true,
        serverSide: false,
        data: appointmentsData,
        columns: [
            { data: 'id' },
            { data: 'customer_name' },
            { data: 'service_name' },
            { data: 'employee_name' },
            {
                data: 'date',
                render: function(data) {
                    return moment(data).format('YYYY/MM/DD');
                }
            },
            {
                data: 'start_time',
                render: function(data, type, row) {
                    return moment(data, 'HH:mm:ss').format('hh:mm A') +
                           ' - ' +
                           moment(row.end_time || data, 'HH:mm:ss').format('hh:mm A');
                }
            },
            {
                data: 'status',
                render: function(data) {
                    const statusText = getStatusText(data);
                    const statusClass = 'status-' + data;
                    return '<span class="appointment-status ' + statusClass + '">' + statusText + '</span>';
                }
            },
            {
                data: null,
                orderable: false,
                render: function(data, type, row) {
                    let actions = `
                        <div class="dropdown">
                            <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="fas fa-ellipsis-v"></i>
                            </button>
                            <ul class="dropdown-menu dropdown-menu-end">
                                <li><a class="dropdown-item view-appointment" href="javascript:void(0)" data-id="${row.id}">
                                    <i class="fas fa-eye me-2"></i> عرض التفاصيل
                                </a></li>`;

                    // إضافة خيار التعديل إذا كان المستخدم لديه صلاحية
                    if (hasEditPermission) {
                        actions += `
                            <li><a class="dropdown-item edit-appointment" href="javascript:void(0)" data-id="${row.id}">
                                <i class="fas fa-edit me-2"></i> تعديل
                            </a></li>`;

                        if (row.status !== 'cancelled') {
                            actions += `
                                <li><a class="dropdown-item cancel-appointment" href="javascript:void(0)" data-id="${row.id}">
                                    <i class="fas fa-ban me-2"></i> إلغاء الموعد
                                </a></li>`;
                        }

                        if (row.status === 'booked' || row.status === 'waiting') {
                            actions += `
                                <li><a class="dropdown-item complete-appointment" href="javascript:void(0)" data-id="${row.id}">
                                    <i class="fas fa-check me-2"></i> إنهاء الموعد
                                </a></li>
                                <li><a class="dropdown-item remind-customer" href="javascript:void(0)" data-id="${row.id}" data-phone="${row.customer_phone}" data-name="${row.customer_name}" data-date="${row.date}" data-time="${row.start_time}">
                                    <i class="fab fa-whatsapp me-2"></i> تذكير العميل
                                </a></li>`;
                        }
                    }

                    // إضافة خيار الفاتورة إذا كان المستخدم لديه صلاحية نقاط البيع
                    if (hasPosPermission && row.status !== 'cancelled') {
                        actions += `
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="../pos/index.php?customer_id=${row.customer_id}&appointment_id=${row.id}">
                                <i class="fas fa-cash-register me-2"></i> إنشاء فاتورة
                            </a></li>`;
                    }

                    actions += `
                            </ul>
                        </div>`;

                    return actions;
                }
            }
        ],
        language: {
            url: 'https://cdn.datatables.net/plug-ins/1.11.5/i18n/ar.json',
            loadingRecords: 'جارٍ التحميل...',
            zeroRecords: 'لم يتم العثور على نتائج',
            emptyTable: 'لا توجد بيانات متاحة في الجدول'
        },
        order: [[4, 'asc'], [5, 'asc']],
        drawCallback: function() {
            // إضافة معالجات الأحداث للأزرار بعد إعادة رسم الجدول
            attachTableEvents();
        }
    });

    // ------ تهيئة تقويم المواعيد ------
    function initializeCalendar() {
        // التأكد من وجود عنصر التقويم
        const calendarEl = document.getElementById('calendar');
        if (!calendarEl) {
            console.error('عنصر التقويم غير موجود في الصفحة');
            return false;
        }

        try {
            // تهيئة التقويم
            calendarInstance = new FullCalendar.Calendar(calendarEl, {
                initialView: 'dayGridMonth',
                headerToolbar: {
                    right: 'today prev,next',
                    center: 'title',
                    left: 'dayGridMonth,timeGridWeek,timeGridDay'
                },
                locale: 'ar',
                direction: 'rtl',
                height: 650,
                events: fetchCalendarEvents,
                eventClick: function(info) {
                    // عرض تفاصيل الموعد عند النقر
                    viewAppointmentDetails(info.event.id);
                },
                dateClick: function(info) {
                    // إمكانية إضافة موعد جديد في التاريخ المحدد
                    if (hasEditPermission) {
                        $('#appointment_date').val(info.dateStr);
                        $('#addAppointmentModal').modal('show');
                    }
                }
            });

            // عرض التقويم
            calendarInstance.render();
            console.log('تم تهيئة التقويم بنجاح');
            return true;
        } catch (error) {
            console.error('خطأ في تهيئة التقويم:', error);
            return false;
        }
    }

    // دالة لجلب أحداث التقويم
    function fetchCalendarEvents(info, successCallback, failureCallback) {
        // جمع معلمات الفلترة
        const filterParams = getFilterParams();

        // إضافة نطاق التاريخ
        filterParams.action = 'list';
        filterParams.start_date = moment(info.start).format('YYYY-MM-DD');
        filterParams.end_date = moment(info.end).format('YYYY-MM-DD');

        // طلب البيانات من الخادم
        $.ajax({
            url: '../../api/appointments.php',
            type: 'GET',
            data: filterParams,
            dataType: 'json',
            success: function(response) {
                if (response.status === 'success' && response.appointments) {
                    // تحويل البيانات إلى تنسيق التقويم
                    const events = response.appointments.map(function(appointment) {
                        // تحديد لون الحدث بناءً على حالة الموعد
                        const color = getStatusColor(appointment.status);

                        // إنشاء عنوان الحدث
                        const title = appointment.customer_name +
                                     (appointment.service_name ? ' - ' + appointment.service_name : '');

                        // تحويل التاريخ والوقت إلى تنسيق ISO
                        const startDateTime = appointment.date + 'T' + appointment.start_time;

                        // حساب وقت الانتهاء بناءً على مدة الخدمة
                        let endDateTime;
                        if (appointment.end_time) {
                            endDateTime = appointment.date + 'T' + appointment.end_time;
                        } else {
                            // إذا لم يكن هناك وقت انتهاء، نفترض أن المدة 30 دقيقة
                            const endTime = moment(startDateTime).add(30, 'minutes').format('YYYY-MM-DDTHH:mm:ss');
                            endDateTime = endTime;
                        }

                        return {
                            id: appointment.id,
                            title: title,
                            start: startDateTime,
                            end: endDateTime,
                            backgroundColor: color,
                            borderColor: color,
                            textColor: '#fff',
                            extendedProps: appointment
                        };
                    });

                    successCallback(events);
                } else {
                    console.warn('تنسيق استجابة API غير متوقع:', response);
                    successCallback([]); // إرجاع مصفوفة فارغة
                }
            },
            error: function(xhr, status, error) {
                console.error('خطأ في طلب مواعيد التقويم:', status, error);
                showAlert('خطأ في تحميل بيانات التقويم', 'danger');
                successCallback([]);
            }
        });
    }

    // ------ معالجات أحداث الفلترة ------

    // معالج حدث مشترك لجميع عناصر الفلترة
    function handleFilterChange() {
        refreshData();
    }

    // ربط معالج الحدث بنموذج الفلترة
    $('#filterForm').on('submit', function(e) {
        e.preventDefault();
        handleFilterChange();
    });

    // ربط معالج الحدث بعناصر الفلترة الفردية
    $('#branch_filter, #employee_filter, #service_filter, #status_filter').on('change', function() {
        // يمكن تأخير تحديث البيانات لتجنب الكثير من الطلبات المتكررة
        clearTimeout($(this).data('filterTimeout'));
        $(this).data('filterTimeout', setTimeout(handleFilterChange, 300));
    });

    // معالج حدث خاص لفلتر التاريخ (يحتاج إلى تطبيق فوري)
    $('#date_filter').on('change', function() {
        handleFilterChange();
    });

    // زر إعادة تعيين الفلاتر
    $('#resetFilters').on('click', function() {
        // إعادة ضبط حقول النموذج
        $('#filterForm')[0].reset();

        // إعادة تعيين التاريخ إلى اليوم الحالي
        $('#date_filter').val(moment().format('YYYY/MM/DD'));

        // تحديث البيانات
        handleFilterChange();
    });

    // ------ إضافة النافذة المنبثقة لتعديل الموعد إذا لم تكن موجودة ------
    function setupQuickEditModal() {
        if (!$('#quickEditModal').length) {
            const modalHtml = `
                <div class="modal fade" id="quickEditModal" tabindex="-1" aria-labelledby="quickEditModalLabel" aria-hidden="true">
                    <div class="modal-dialog modal-dialog-centered">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title" id="quickEditModalLabel">تعديل سريع للموعد</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="إغلاق"></button>
                            </div>
                            <div class="modal-body">
                                <form id="quickEditForm">
                                    <input type="hidden" id="quick_edit_id">
                                    <input type="hidden" id="quick_edit_customer_id">
                                    <input type="hidden" id="quick_edit_service_id">
                                    <div class="row mb-3">
                                        <div class="col-md-6">
                                            <label for="quick_edit_status" class="form-label">الحالة</label>
                                            <select class="form-select" id="quick_edit_status" name="status">
                                                <option value="booked">محجوز</option>
                                                <option value="waiting">في الانتظار</option>
                                                <option value="completed">مكتمل</option>
                                                <option value="cancelled">ملغي</option>
                                            </select>
                                        </div>
                                        <div class="col-md-6">
                                            <label for="quick_edit_date" class="form-label">التاريخ</label>
                                            <input type="date" class="form-control" id="quick_edit_date" name="date">
                                        </div>
                                    </div>
                                    <div class="row mb-3">
                                        <div class="col-md-6">
                                            <label for="quick_edit_start_time" class="form-label">وقت البدء</label>
                                            <input type="time" class="form-control" id="quick_edit_start_time" name="start_time">
                                        </div>
                                        <div class="col-md-6">
                                            <label for="quick_edit_employee_id" class="form-label">الموظف</label>
                                            <select class="form-select" id="quick_edit_employee_id" name="employee_id">
                                                <!-- سيتم ملؤها ديناميكيًا -->
                                            </select>
                                        </div>
                                    </div>
                                    <div class="mb-3">
                                        <label for="quick_edit_notes" class="form-label">ملاحظات</label>
                                        <textarea class="form-control" id="quick_edit_notes" name="notes" rows="2"></textarea>
                                    </div>
                                </form>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                                <button type="button" class="btn btn-primary" id="saveQuickEdit">حفظ التغييرات</button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            $('body').append(modalHtml);

            // تحميل قائمة الموظفين
            $.ajax({
                url: '../../api/employees.php',
                type: 'GET',
                data: {
                    action: 'get_employees',
                    is_active: 1
                },
                dataType: 'json',
                success: function(response) {
                    if (response.status === 'success' && response.employees) {
                        let options = '<option value="">اختر الموظف</option>';
                        response.employees.forEach(function(employee) {
                            options += `<option value="${employee.id}">${employee.name}</option>`;
                        });
                        $('#quick_edit_employee_id').html(options);
                    }
                }
            });

            // حدث حفظ التغييرات
            $('#saveQuickEdit').on('click', function() {
                saveQuickEdit();
            });
        }
    }

    // ------ وظائف معالجة المواعيد ------

    // عرض بيانات الموعد في المودال
    function viewAppointmentDetails(appointmentId) {
        $.ajax({
            url: '../../api/appointments.php',
            type: 'GET',
            data: {
                action: 'view',
                id: appointmentId
            },
            dataType: 'json',
            success: function(response) {
                if (response.status === 'success' && response.appointment) {
                    showAppointmentDetails(response.appointment);
                } else {
                    showAlert(response.message || 'حدث خطأ أثناء تحميل بيانات الموعد', 'danger');
                }
            },
            error: function(xhr, status, error) {
                showAlert('حدث خطأ أثناء تحميل بيانات الموعد: ' + error, 'danger');
            }
        });
    }

    // عرض بيانات الموعد في المودال
    function showAppointmentDetails(appointment) {
        // تخزين معرف الموعد والعميل
        $('#appointment_id').val(appointment.id);
        $('#customer_id').val(appointment.customer_id);

        // تخزين معرف الموعد في زر التعديل
        $('#editBtn').data('id', appointment.id);

        // عرض بيانات العميل
        $('#customer_name').text(appointment.customer_name || '-');
        $('#customer_phone').text(appointment.customer_phone || '-');

        // عرض بيانات الموعد
        $('#appointment_date').text(moment(appointment.date).format('YYYY-MM-DD'));
        $('#appointment_time').text(
            moment(appointment.start_time, 'HH:mm:ss').format('hh:mm A') +
            ' - ' +
            moment(appointment.end_time || appointment.start_time, 'HH:mm:ss').format('hh:mm A')
        );
        $('#appointment_service').text(appointment.service_name || '-');
        $('#appointment_employee').text(appointment.employee_name || '-');

        // تعيين حالة الموعد
        const statusText = getStatusText(appointment.status);
        const statusClass = 'status-' + appointment.status;
        $('#appointment_status').html(`<span class="appointment-status ${statusClass}">${statusText}</span>`);

        // عرض الملاحظات
        $('#appointment_notes').text(appointment.notes || 'لا توجد ملاحظات');

        // تعيين الرابط المناسب لزر إنشاء الفاتورة
        $('#createInvoiceBtn').attr('href', `../pos/index.php?customer_id=${appointment.customer_id}&appointment_id=${appointment.id}`);

        // تحديث بيانات زر التذكير
        $('#remindCustomerBtn')
            .data('id', appointment.id)
            .attr('data-id', appointment.id)
            .data('phone', appointment.customer_phone)
            .attr('data-phone', appointment.customer_phone)
            .data('name', appointment.customer_name)
            .attr('data-name', appointment.customer_name)
            .data('date', moment(appointment.date).format('YYYY-MM-DD'))
            .attr('data-date', moment(appointment.date).format('YYYY-MM-DD'))
            .data('time', moment(appointment.start_time, 'HH:mm:ss').format('hh:mm A'))
            .attr('data-time', moment(appointment.start_time, 'HH:mm:ss').format('hh:mm A'));

        // فتح المودال
        $('#appointmentDetailsModal').modal('show');
    }

    // فتح نافذة التعديل السريع
    function quickEditAppointment(appointmentId) {
        // التأكد من وجود نافذة التعديل السريع
        setupQuickEditModal();

        // تحميل بيانات الموعد
        $.ajax({
            url: '../../api/appointments.php',
            type: 'GET',
            data: {
                action: 'view',
                id: appointmentId
            },
            dataType: 'json',
            success: function(response) {
                if (response.status === 'success' && response.appointment) {
                    const appointment = response.appointment;

                    // ملء بيانات النموذج
                    $('#quick_edit_id').val(appointment.id);
                    $('#quick_edit_customer_id').val(appointment.customer_id);
                    $('#quick_edit_service_id').val(appointment.service_id);
                    $('#quick_edit_status').val(appointment.status);
                    $('#quick_edit_date').val(appointment.date);
                    $('#quick_edit_start_time').val(appointment.start_time);
                    $('#quick_edit_employee_id').val(appointment.employee_id);
                    $('#quick_edit_notes').val(appointment.notes);

                    // عرض النافذة المنبثقة
                    $('#quickEditModal').modal('show');
                } else {
                    showAlert(response.message || 'حدث خطأ أثناء تحميل بيانات الموعد', 'danger');
                }
            },
            error: function() {
                showAlert('حدث خطأ أثناء الاتصال بالخادم', 'danger');
            }
        });
    }

    // حفظ تغييرات التعديل السريع
    function saveQuickEdit() {
        const appointmentId = $('#quick_edit_id').val();
        const customerId = $('#quick_edit_customer_id').val();
        const serviceId = $('#quick_edit_service_id').val();
        const status = $('#quick_edit_status').val();
        const date = $('#quick_edit_date').val()
        const startTime = $('#quick_edit_start_time').val();
        const employeeId = $('#quick_edit_employee_id').val();
        const notes = $('#quick_edit_notes').val();

        // التحقق من صحة البيانات
        if (!date || !startTime) {
            showAlert('يرجى ملء جميع الحقول المطلوبة', 'warning');
            return;
        }

        // إرسال طلب التحديث
        $.ajax({
            url: '../../api/appointments.php',
            type: 'POST',
            data: {
                action: 'update',
                id: appointmentId,
                customer_id: customerId,
                service_id: serviceId,
                status: status,
                date: date,
                start_time: startTime,
                employee_id: employeeId || null,
                notes: notes
            },
            dataType: 'json',
            success: function(response) {
                if (response.status === 'success') {
                    $('#quickEditModal').modal('hide');
                    showAlert('تم تحديث الموعد بنجاح', 'success');

                    // إعادة تحميل البيانات
                    refreshData();
                } else {
                    showAlert(response.message || 'حدث خطأ أثناء تحديث الموعد', 'danger');
                }
            },
            error: function() {
                showAlert('حدث خطأ أثناء الاتصال بالخادم', 'danger');
            }
        });
    }

    // إلغاء موعد
    function cancelAppointment(appointmentId) {
        if (confirm('هل أنت متأكد من إلغاء هذا الموعد؟')) {
            $.ajax({
                url: '../../api/appointments.php',
                type: 'POST',
                data: {
                    action: 'update',
                    id: appointmentId,
                    status: 'cancelled'
                },
                dataType: 'json',
                success: function(response) {
                    if (response.status === 'success') {
                        showAlert('تم إلغاء الموعد بنجاح', 'success');
                        refreshData();
                    } else {
                        showAlert(response.message || 'حدث خطأ أثناء إلغاء الموعد', 'danger');
                    }
                },
                error: function() {
                    showAlert('حدث خطأ أثناء الاتصال بالخادم', 'danger');
                }
            });
        }
    }

    // إنهاء موعد
    function completeAppointment(appointmentId) {
        $.ajax({
            url: '../../api/appointments.php',
            type: 'POST',
            data: {
                action: 'update',
                id: appointmentId,
                status: 'completed'
            },
            dataType: 'json',
            success: function(response) {
                if (response.status === 'success') {
                    showAlert('تم إنهاء الموعد بنجاح', 'success');
                    refreshData();
                } else {
                    showAlert(response.message || 'حدث خطأ أثناء إنهاء الموعد', 'danger');
                }
            },
            error: function() {
                showAlert('حدث خطأ أثناء الاتصال بالخادم', 'danger');
            }
        });
    }

    // ------ ربط معالجات الأحداث ------

    // ربط الأحداث بعناصر الجدول (يتم استدعاؤها بعد كل تحديث للجدول)
    function attachTableEvents() {
        // معالج عرض تفاصيل الموعد
        $('.view-appointment').off('click').on('click', function() {
            const appointmentId = $(this).data('id');
            viewAppointmentDetails(appointmentId);
        });

        // معالج تعديل الموعد
        $('.edit-appointment').off('click').on('click', function() {
            const appointmentId = $(this).data('id');
            quickEditAppointment(appointmentId);
        });

        // معالج إلغاء الموعد
        $('.cancel-appointment').off('click').on('click', function() {
            const appointmentId = $(this).data('id');
            cancelAppointment(appointmentId);
        });

        // معالج إنهاء الموعد
        $('.complete-appointment').off('click').on('click', function() {
            const appointmentId = $(this).data('id');
            completeAppointment(appointmentId);
        });

        // معالج تذكير العميل عبر واتساب
        $('.remind-customer').off('click').on('click', function() {
            const phone = $(this).data('phone');
            const name = $(this).data('name');
            const date = $(this).data('date');
            const time = $(this).data('time');
            const appointmentId = $(this).data('id');

            // عرض مؤشر التحميل
            const $btn = $(this);
            const originalText = $btn.html();
            $btn.html('<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> جاري الإرسال...');
            $btn.prop('disabled', true);

            // التحقق من وجود موديول WhatsAppAppointmentReminder أولاً
            if (typeof WhatsAppAppointmentReminder !== 'undefined') {
                console.log('استخدام WhatsAppAppointmentReminder لإرسال تذكير مباشرة');

                // إرسال التذكير باستخدام الموديول المباشر
                WhatsAppAppointmentReminder.sendAppointmentReminder(appointmentId)
                    .then(function(result) {
                        console.log('تم إرسال التذكير بنجاح:', result);
                        // لا نحتاج لعرض رسالة نجاح هنا لأن الموديول يقوم بذلك بالفعل

                        // إعادة الزر إلى حالته الأصلية
                        $btn.html(originalText);
                        $btn.prop('disabled', false);
                    })
                    .catch(function(error) {
                        console.error('خطأ في إرسال التذكير:', error);
                        showAlert(error.message || 'حدث خطأ أثناء إرسال التذكير', 'danger');

                        // إعادة الزر إلى حالته الأصلية
                        $btn.html(originalText);
                        $btn.prop('disabled', false);
                    });
            } else {
                console.log('WhatsAppAppointmentReminder غير متوفر، استخدام الطريقة القديمة');

                // استخدام الواجهة البرمجية لإرسال التذكير
                $.ajax({
                    url: '../../api/whatsapp.php',
                    type: 'POST',
                    data: {
                        action: 'send_appointment_reminder',
                        appointment_id: appointmentId
                    },
                    dataType: 'json',
                    success: function(response) {
                        console.log('استجابة API:', response); // تسجيل الاستجابة للتصحيح

                        // إذا كانت الاستجابة تحتوي على URL، فتح نافذة WhatsApp
                        if (response.status === 'success' && response.data && response.data.url) {
                            window.open(response.data.url, '_blank');
                            showAlert('تم فتح نافذة WhatsApp', 'success');
                        } else if (response.status === 'success') {
                            // عرض رسالة نجاح مع التحذير إذا كان موجودًا
                            if (response.warning) {
                                showAlert(response.message + '<br><small class="text-warning"><i class="fas fa-exclamation-triangle"></i> ' + response.warning + '</small>', 'success');
                            } else {
                                showAlert('تم إرسال التذكير بنجاح', 'success');
                            }
                        } else {
                            // عرض رسالة خطأ فقط إذا كان هناك خطأ حقيقي
                            showAlert('فشل إرسال التذكير: ' + response.message, 'danger');
                        }

                        // إعادة الزر إلى حالته الأصلية
                        $btn.html(originalText);
                        $btn.prop('disabled', false);
                    },
                    error: function() {
                        // في حالة الخطأ، استخدم الطريقة القديمة لفتح نافذة WhatsApp مباشرة
                        // تنسيق التاريخ والوقت
                        const formattedDate = moment(date).format('DD/MM/YYYY');
                        const formattedTime = moment(time, 'HH:mm:ss').format('hh:mm A');

                        const message = `مرحبا ${name}\nنود تذكيركم بموعدكم القادم في صالون البدرواي\nالتاريخ: ${formattedDate}\nالوقت: ${formattedTime}\nنتطلع لرؤيتكم!`;

                        let formattedPhone = phone;
                        if (formattedPhone.startsWith('0')) {
                            formattedPhone = '20' + formattedPhone.substring(1);
                        }

                        const whatsappUrl = `https://web.whatsapp.com/send?phone=${formattedPhone}&text=${encodeURIComponent(message)}`;
                        window.open(whatsappUrl, '_blank');

                        // إعادة الزر إلى حالته الأصلية
                        $btn.html(originalText);
                        $btn.prop('disabled', false);

                        showAlert('تم فتح نافذة WhatsApp مباشرة', 'success');
                    }
                });
            }
        });
    }

    // ربط أحداث المودال
    function attachModalEvents() {
        // زر التعديل في مودال التفاصيل
        $('#editBtn').off('click').on('click', function() {
            const appointmentId = $(this).data('id');
            if (appointmentId) {
                // إغلاق مودال التفاصيل
                $('#appointmentDetailsModal').modal('hide');
                // فتح مودال التعديل
                setTimeout(function() {
                    quickEditAppointment(appointmentId);
                }, 500);
            } else {
                showAlert('لم يتم تحديد موعد للتعديل', 'warning');
            }
        });

        // معالج تغيير الحالة من المودال
        $('.change-status').off('click').on('click', function() {
            const status = $(this).data('status');
            const appointmentId = $('#appointment_id').val();

            $.ajax({
                url: '../../api/appointments.php',
                type: 'POST',
                data: {
                    action: 'update',
                    id: appointmentId,
                    status: status
                },
                dataType: 'json',
                success: function(response) {
                    if (response.status === 'success') {
                        $('#appointmentDetailsModal').modal('hide');
                        showAlert('تم تغيير حالة الموعد بنجاح', 'success');
                        refreshData();
                    } else {
                        showAlert(response.message || 'حدث خطأ أثناء تغيير حالة الموعد', 'danger');
                    }
                },
                error: function() {
                    showAlert('حدث خطأ أثناء الاتصال بالخادم', 'danger');
                }
            });
        });

        // معالج تذكير العميل عبر واتساب
        $('#remindCustomerBtn').off('click').on('click', function() {
            // الحصول على بيانات العميل والموعد
            const name = $('#customer_name').text();
            const phone = $('#customer_phone').text();
            const date = $('#appointment_date').text();
            const time = $('#appointment_time').text();
            const appointmentId = $('#appointment_id').val();
            const customerId = $('#customer_id').val(); // الحصول على معرف العميل إن وجد

            // التحقق من وجود رقم هاتف
            if (!phone || phone === '-') {
                showAlert('لا يوجد رقم هاتف للعميل', 'warning');
                return;
            }

            // تسجيل معلومات الموعد للتصحيح
            console.log('معلومات الموعد:', {
                appointmentId,
                customerId,
                name,
                phone,
                date,
                time
            });

            // عرض مؤشر التحميل
            const $btn = $(this);
            const originalText = $btn.html();
            $btn.html('<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> جاري الإرسال...');
            $btn.prop('disabled', true);

            // التحقق من وجود موديول WhatsAppAppointmentReminder أولاً
            if (typeof WhatsAppAppointmentReminder !== 'undefined') {
                console.log('استخدام WhatsAppAppointmentReminder لإرسال تذكير مباشرة');

                // تهيئة الموديول إذا لم يتم تهيئته مسبقًا
                if (typeof WhatsAppAppointmentReminder.init === 'function' && !WhatsAppAppointmentReminder.initialized) {
                    WhatsAppAppointmentReminder.init();
                    WhatsAppAppointmentReminder.initialized = true;
                }

                // إرسال التذكير باستخدام الموديول المباشر
                WhatsAppAppointmentReminder.sendAppointmentReminder(appointmentId)
                    .then(function(result) {
                        console.log('تم إرسال التذكير بنجاح:', result);
                        // لا نحتاج لعرض رسالة نجاح هنا لأن الموديول يقوم بذلك بالفعل

                        // إعادة الزر إلى حالته الأصلية
                        $btn.html(originalText);
                        $btn.prop('disabled', false);
                    })
                    .catch(function(error) {
                        console.error('خطأ في إرسال التذكير:', error);
                        showAlert(error.message || 'حدث خطأ أثناء إرسال التذكير', 'danger');

                        // إعادة الزر إلى حالته الأصلية
                        $btn.html(originalText);
                        $btn.prop('disabled', false);
                    });
            } else {
                console.log('WhatsAppAppointmentReminder غير متوفر، استخدام الطريقة القديمة');

                // إرسال الرسالة باستخدام WhatsAppAutomation
                $.ajax({
                    url: '../../api/whatsapp.php',
                    type: 'POST',
                    data: {
                        action: 'send_appointment_reminder',
                        appointment_id: appointmentId
                    },
                    dataType: 'json',
                    success: function(response) {
                        console.log('استجابة API:', response); // تسجيل الاستجابة للتصحيح

                        // إذا كانت الاستجابة تحتوي على URL، فتح نافذة WhatsApp
                        if (response.status === 'success' && response.data && response.data.url) {
                            window.open(response.data.url, '_blank');
                            showAlert('تم فتح نافذة WhatsApp', 'success');
                        } else if (response.status === 'success') {
                            // عرض رسالة نجاح مع التحذير إذا كان موجودًا
                            if (response.warning) {
                                showAlert(response.message + '<br><small class="text-warning"><i class="fas fa-exclamation-triangle"></i> ' + response.warning + '</small>', 'success');
                            } else {
                                showAlert('تم إرسال التذكير بنجاح', 'success');
                            }
                        } else {
                            // عرض رسالة خطأ فقط إذا كان هناك خطأ حقيقي
                            showAlert('فشل إرسال التذكير: ' + response.message, 'danger');
                        }

                        // إعادة الزر إلى حالته الأصلية
                        $btn.html(originalText);
                        $btn.prop('disabled', false);
                    },
                    error: function() {
                        // في حالة الخطأ، استخدم الطريقة القديمة لفتح نافذة WhatsApp مباشرة
                        const message = `مرحبا ${name}\nنود تذكيركم بموعدكم القادم في صالون البدرواي\nالتاريخ: ${date}\nالوقت: ${time}\nنتطلع لرؤيتكم!`;

                        let formattedPhone = phone;
                        if (formattedPhone.startsWith('0')) {
                            formattedPhone = '20' + formattedPhone.substring(1);
                        }

                        const whatsappUrl = `https://web.whatsapp.com/send?phone=${formattedPhone}&text=${encodeURIComponent(message)}`;
                        window.open(whatsappUrl, '_blank');

                        // إعادة الزر إلى حالته الأصلية
                        $btn.html(originalText);
                        $btn.prop('disabled', false);

                        showAlert('تم فتح نافذة WhatsApp مباشرة', 'success');
                    }
                });
            }
        });
    }

    // ربط أحداث التبويب
    function attachTabEvents() {
        // معالج تبديل التبويب إلى التقويم
        $('#calendar-tab').on('shown.bs.tab', function(e) {
            // تأخير قليل للتأكد من إعادة تشكيل المساحة قبل تقديم التقويم
            setTimeout(function() {
                if ($('#calendar').length > 0) {
                    // إذا لم يتم تهيئة التقويم بعد، قم بتهيئته
                    if (!calendarInstance) {
                        initializeCalendar();
                    } else {
                        // إذا تم تهيئة التقويم بالفعل، قم بإعادة رسمه
                        try {
                            calendarInstance.render();
                        } catch (error) {
                            console.error('خطأ في إعادة رسم التقويم:', error);
                            // إعادة تهيئة التقويم في حالة حدوث خطأ
                            initializeCalendar();
                        }
                    }
                }
            }, 50);
        });
    }

    // ربط جميع الأحداث
    function attachAllEvents() {
        attachTableEvents();
        attachModalEvents();
        attachTabEvents();
    }

    // ------ بدء تنفيذ التطبيق ------

    // تهيئة موديول WhatsAppAppointmentReminder إذا كان متوفرًا
    if (typeof WhatsAppAppointmentReminder !== 'undefined') {
        console.log('تهيئة موديول WhatsAppAppointmentReminder');
        WhatsAppAppointmentReminder.init({
            debug: true,
            showSuccessMessage: true
        });
        WhatsAppAppointmentReminder.initialized = true;
        console.log('WhatsAppAppointmentReminder تم تهيئته بنجاح');
    } else {
        console.warn('WhatsAppAppointmentReminder غير متوفر. سيتم استخدام الطريقة القديمة لإرسال التذكيرات.');
    }

    // تهيئة وربط جميع الأحداث
    attachAllEvents();

    // تحميل البيانات الأولية
    refreshData();

    // تحديث البيانات دوريًا (كل 60 ثانية)
    setInterval(function() {
        // تحديث البيانات فقط إذا لم يكن المستخدم يتفاعل مع الصفحة
        if (!$('input:focus, select:focus').length && !$('.modal.show').length) {
            refreshData();
        }
    }, 60000);

    // ------ تحسينات أداء إضافية ------

    // استخدام مخزن مؤقت للبيانات المتكررة
    const dataCache = {
        // تخزين بيانات التقويم لكل شهر
        calendarData: {},

        // احفظ بيانات التقويم
        saveCalendarData: function(startDate, endDate, data) {
            const cacheKey = `${startDate}_${endDate}`;
            this.calendarData[cacheKey] = {
                data: data,
                timestamp: Date.now()
            };
        },

        // احصل على بيانات التقويم المخزنة مؤقتًا
        getCalendarData: function(startDate, endDate) {
            const cacheKey = `${startDate}_${endDate}`;
            const cachedItem = this.calendarData[cacheKey];

            // تحقق من وجود بيانات مخزنة مؤقتًا وأنها حديثة (أقل من 5 دقائق)
            if (cachedItem && (Date.now() - cachedItem.timestamp < 5 * 60 * 1000)) {
                return cachedItem.data;
            }

            return null;
        },

        // إزالة كل البيانات المخزنة مؤقتًا
        clearCache: function() {
            this.calendarData = {};
        }
    };
    function refreshData() {
    showLoading();

    // جمع معلمات الفلترة
    const filterParams = getFilterParams();

    // إضافة معامل العمل للطلب
    filterParams.action = 'filter';

    // طلب البيانات
    $.ajax({
        url: '../../api/appointments.php',
        type: 'GET',
        data: filterParams,
        dataType: 'json',
        success: function(response) {
            if (response.status === 'success') {
                // تحديث مباشر للجدول باستخدام الـ API الخاص به
                appointmentsTable.clear().rows.add(response.appointments || []).draw();

                // تحديث التقويم إذا كان مرئ<|im_start|>
                if ($('#calendar-tab').hasClass('active') && calendarInstance) {
                    try {
                        calendarInstance.refetchEvents();
                    } catch (error) {
                        console.error('خطأ في تحديث أحداث التقويم:', error);
                    }
                }

                // تحديث الإحصائيات
                if (response.stats) {
                    updateStats(response.stats);
                } else {
                    loadAppointmentStats();
                }

                hideLoading();
            } else {
                showError(response.message);
            }
        },
        error: function(xhr, status, error) {
            console.error('خطأ في طلب البيانات:', status, error);
            showError('فشل الاتصال بالخادم. يرجى المحاولة مرة أخرى لاحقًا.');
        }
    });
}
    // توقف مؤقت للمعالجات المتكررة
    const debounce = (func, delay) => {
        let timeout;
        return function() {
            const context = this;
            const args = arguments;
            clearTimeout(timeout);
            timeout = setTimeout(() => func.apply(context, args), delay);
        };
    };

    // تطبيق توقف مؤقت على تحديث البيانات
    const debouncedRefresh = debounce(refreshData, 300);

    // استخدام التحديث المؤجل لبعض الأحداث
    $('.filter-control-delayed').on('change', function() {
        debouncedRefresh();
    });
});
</script>
