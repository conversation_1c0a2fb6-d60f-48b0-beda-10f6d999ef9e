/**
 * إنشاء فاتورة مع إدارة المعاملة خارجيًا
 *
 * @param array $invoiceData بيانات الفاتورة
 * @param array $items عناصر الفاتورة
 * @return int معرف الفاتورة الجديدة
 */
public function createInvoiceWithTransaction($invoiceData, $items) {
    // إدراج بيانات الفاتورة الرئيسية
    $currentDateTime = date('Y-m-d H:i:s'); // الحصول على التاريخ والوقت الحالي من PHP

    $sql = "INSERT INTO invoices (
        invoice_number, customer_id, cashier_id, employee_id,
        total_amount, discount_amount, discount_type, tax_amount,
        final_amount, payment_method, payment_status, notes, branch_id, created_at
    ) VALUES (
        :invoice_number, :customer_id, :cashier_id, :employee_id,
        :total_amount, :discount_amount, :discount_type, :tax_amount,
        :final_amount, :payment_method, :payment_status, :notes, :branch_id, :created_at
    )";

    $stmt = $this->db->prepare($sql);
    $stmt->bindParam(':invoice_number', $invoiceData['invoice_number']);
    $stmt->bindParam(':customer_id', $invoiceData['customer_id']);
    $stmt->bindParam(':cashier_id', $invoiceData['cashier_id']);
    $stmt->bindParam(':employee_id', $invoiceData['employee_id']);
    $stmt->bindParam(':total_amount', $invoiceData['total_amount']);
    $stmt->bindParam(':discount_amount', $invoiceData['discount_amount']);
    $stmt->bindParam(':discount_type', $invoiceData['discount_type']);
    $stmt->bindParam(':tax_amount', $invoiceData['tax_amount']);
    $stmt->bindParam(':final_amount', $invoiceData['final_amount']);
    $stmt->bindParam(':payment_method', $invoiceData['payment_method']);
    $stmt->bindParam(':payment_status', $invoiceData['payment_status']);
    $stmt->bindParam(':notes', $invoiceData['notes']);
    $stmt->bindParam(':branch_id', $invoiceData['branch_id']);
    $stmt->bindParam(':created_at', $currentDateTime);
    $stmt->execute();

    // الحصول على معرف الفاتورة المدرجة
    $invoiceId = $this->db->lastInsertId();

    // إدراج عناصر الفاتورة
    foreach ($items as $item) {
        $sql = "INSERT INTO invoice_items (
            invoice_id, item_id, item_type, quantity, price, total, employee_id
        ) VALUES (
            :invoice_id, :item_id, :item_type, :quantity, :price, :total, :employee_id
        )";

        $stmt = $this->db->prepare($sql);
        $stmt->bindParam(':invoice_id', $invoiceId);
        $stmt->bindParam(':item_id', $item['item_id']);
        $stmt->bindParam(':item_type', $item['item_type']);
        $stmt->bindParam(':quantity', $item['quantity']);
        $stmt->bindParam(':price', $item['price']);
        $total = $item['price'] * $item['quantity'];
        $stmt->bindParam(':total', $total);
        $employeeId = $item['employee_id'] ?? null;
        $stmt->bindParam(':employee_id', $employeeId);
        $stmt->execute();

        // تحديث المخزون إذا كان العنصر منتجًا
        if ($item['item_type'] === 'product') {
            $inventoryModel = new Inventory($this->db);
            $inventoryModel->decreaseStock(
                $item['item_id'],
                $item['quantity'],
                $invoiceData['branch_id']
            );
        }
    }

    return $invoiceId;
}