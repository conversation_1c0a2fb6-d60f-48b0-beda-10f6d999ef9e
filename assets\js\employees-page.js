// دالة تنسيق العملة
function formatCurrency(amount, symbol) {
    return parseFloat(amount).toFixed(2) + ' ' + symbol;
}

// عند تحميل الصفحة
$(document).ready(function() {
    // تهيئة جدول البيانات
    const employeesTable = $('#employeesTable').DataTable({
        processing: true,
        serverSide: false,
        ajax: {
            url: '../../api/employees.php',
            type: 'POST',
            data: function(d) {
                d.action = 'get_employees';
                d.search = $('#search_term').val();
                d.branch_id = $('#branch_filter').val();
                d.position = $('#position_filter').val();
                d.is_active = $('#status_filter').val();
            }
        },
        columns: [
            { data: 'id' },
            { data: 'name' },
            { data: 'position' },
            { data: 'phone' },
            { 
                data: 'salary_type',
                render: function(data) {
                    switch(data) {
                        case 'fixed': return 'ثابت';
                        case 'percentage': return 'نسبة';
                        case 'both': return 'ثابت ونسبة';
                        default: return data;
                    }
                }
            },
            { 
                data: 'fixed_salary',
                render: function(data) {
                    return formatCurrency(data, currencySymbol);
                }
            },
            { 
                data: 'commission_percentage',
                render: function(data) {
                    return parseFloat(data) + '%';
                }
            },
            { data: 'branch_name' },
            { 
                data: 'is_active',
                render: function(data) {
                    if (data == 1) {
                        return '<span class="badge bg-success">نشط</span>';
                    } else {
                        return '<span class="badge bg-danger">غير نشط</span>';
                    }
                }
            },
            {
                data: null,
                orderable: false,
                render: function(data) {
                    let actions = `
                        <div class="dropdown">
                            <button class="btn btn-sm btn-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                                الإجراءات
                            </button>
                            <ul class="dropdown-menu dropdown-menu-end">
                                <li><a class="dropdown-item view-employee" href="javascript:void(0)" data-id="${data.id}">
                                    <i class="fas fa-eye me-2"></i> عرض التفاصيل
                                </a></li>`;
                    
                    if (hasPermission('employees_edit')) {
                        actions += `
                            <li><a class="dropdown-item edit-employee" href="javascript:void(0)" data-id="${data.id}">
                                <i class="fas fa-edit me-2"></i> تعديل
                            </a></li>
                            <li><a class="dropdown-item record-attendance" href="javascript:void(0)" data-id="${data.id}">
                                <i class="fas fa-user-clock me-2"></i> تسجيل حضور
                            </a></li>`;
                    }
                    
                    actions += `
                        <li><a class="dropdown-item pay-salary" href="javascript:void(0)" data-id="${data.id}" data-name="${data.name}">
                            <i class="fas fa-money-bill-alt me-2"></i> دفع راتب
                        </a></li>`;

                    actions += `
                        <li><a class="dropdown-item add-bonus" href="javascript:void(0)" data-id="${data.id}" data-name="${data.name}">
                            <i class="fas fa-gift me-2"></i> إضافة مكافأة
                        </a></li>
                        <li><a class="dropdown-item add-deduction" href="javascript:void(0)" data-id="${data.id}" data-name="${data.name}">
                            <i class="fas fa-minus-circle me-2"></i> إضافة خصم
                        </a></li>`;

                    if (hasPermission('employees_delete')) {
                        actions += `
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item text-danger delete-employee" href="javascript:void(0)" data-id="${data.id}">
                                <i class="fas fa-trash-alt me-2"></i> حذف
                            </a></li>`;
                    }
                    
                    actions += `
                            </ul>
                        </div>`;
                    
                    return actions;
                }
            }
        ],
        language: {
            url: 'https://cdn.datatables.net/plug-ins/1.11.5/i18n/ar.json'
        }
    });
    
    // تبديل قسم حساب المستخدم عند إضافة موظف
    $('#create_user').on('change', function() {
        if ($(this).is(':checked')) {
            $('#userAccountSection').show();
        } else {
            $('#userAccountSection').hide();
        }
    });
    
    // تبديل قسم حساب المستخدم عند تعديل موظف
    $('#edit_create_user').on('change', function() {
        if ($(this).is(':checked')) {
            $('#edit_userAccountSection').show();
        } else {
            $('#edit_userAccountSection').hide();
        }
    });
    
    // البحث
    $('#searchForm').on('submit', function(e) {
        e.preventDefault();
        employeesTable.ajax.reload();
    });
    
    // إعادة ضبط البحث
    $('#searchForm button[type="reset"]').on('click', function() {
        $('#search_term').val('');
        $('#branch_filter').val('');
        $('#position_filter').val('');
        $('#status_filter').val('1');
        employeesTable.ajax.reload();
    });
    
    // إضافة موظف جديد
    $('#submitAddEmployee').on('click', function() {
        const form = $('#addEmployeeForm')[0];
        if (!form.checkValidity()) {
            form.reportValidity();
            return;
        }
        
        const formData = new FormData(form);
        formData.append('action', 'add_employee');
        
        // جمع الخدمات المختارة
        const services = $('#services').val();
        if (services && services.length > 0) {
            formData.delete('services[]');
            formData.append('services', services);
        }
        
        $.ajax({
            url: '../../api/employees.php',
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            dataType: 'json',
            success: function(response) {
                if (response.status === 'success') {
                    // إغلاق المودال وإعادة تحميل الجدول
                    $('#addEmployeeModal').modal('hide');
                    
                    // إظهار كلمة المرور المولدة إذا كانت موجودة
                    if (response.generated_password) {
                        let message = response.message + `<br><br>تم إنشاء حساب مستخدم بكلمة المرور: <strong>${response.generated_password}</strong>`;
                        showAlert(message);
                    } else {
                        showAlert(response.message);
                    }
                    
                    // إعادة تعيين النموذج
                    form.reset();
                    $('#services').val(null).trigger('change');
                    $('#userAccountSection').hide();
                    
                    employeesTable.ajax.reload();
                } else {
                    showAlert(response.message, 'danger');
                }
            },
            error: function() {
                showAlert('حدث خطأ أثناء إضافة الموظف', 'danger');
            }
        });
    });
    
    // عرض تفاصيل الموظف
    $(document).on('click', '.view-employee', function() {
        const employeeId = $(this).data('id');
        
        $.ajax({
            url: '../../api/employees.php',
            type: 'POST',
            data: {
                action: 'get_employee',
                employee_id: employeeId,
                with_services: true
            },
            dataType: 'json',
            success: function(response) {
                if (response.status === 'success') {
                    const employee = response.employee;
                    
                    // عرض بيانات الموظف
                    $('#view_name').text(employee.name);
                    $('#view_position').text(employee.position);
                    $('#view_phone').text(employee.phone || '-');
                    $('#view_email').text(employee.email || '-');
                    
                    // عرض بيانات الراتب
                    let salaryType = '';
                    switch(employee.salary_type) {
                        case 'fixed': salaryType = 'ثابت'; break;
                        case 'percentage': salaryType = 'نسبة'; break;
                        case 'both': salaryType = 'ثابت ونسبة'; break;
                        default: salaryType = employee.salary_type;
                    }
                    
                    $('#view_salary_type').text(salaryType);
                    $('#view_fixed_salary').text(formatCurrency(employee.fixed_salary, currencySymbol));
                    $('#view_commission_percentage').text(parseFloat(employee.commission_percentage).toFixed(2) + '%');
                    $('#view_branch_name').text(employee.branch_name || '-');
                    
                    // عرض الخدمات
                    const servicesDiv = $('#view_services');
                    servicesDiv.empty();
                    
                    if (employee.services && employee.services.length > 0) {
                        const servicesList = $('<div class="d-flex flex-wrap gap-1"></div>');
                        employee.services.forEach(function(service) {
                            servicesList.append(`<span class="badge bg-info">${service.name}</span>`);
                        });
                        servicesDiv.append(servicesList);
                    } else {
                        servicesDiv.text('لا توجد خدمات مسجلة لهذا الموظف');
                    }
                    
                    // تحديث روابط الإجراءات
                    $('#editEmployeeBtn').data('id', employee.id);
                    $('#viewSalaryBtn').attr('href', `salaries.php?employee_id=${employee.id}`);
                    
                    // تحميل الإحصائيات
                    loadEmployeeStats(employee.id);
                    
                    // تحميل سجل الحضور للشهر الحالي
                    loadEmployeeAttendance(employee.id);
                    
                    // عرض المودال
                    $('#viewEmployeeModal').modal('show');
                } else {
                    showAlert(response.message, 'danger');
                }
            },
            error: function() {
                showAlert('حدث خطأ أثناء تحميل بيانات الموظف', 'danger');
            }
        });
    });
    
    // تحميل إحصائيات الموظف
    function loadEmployeeStats(employeeId) {
        $.ajax({
            url: '../../api/employees.php',
            type: 'POST',
            data: {
                action: 'get_employee_stats',
                employee_id: employeeId
            },
            dataType: 'json',
            success: function(response) {
                if (response.status === 'success') {
                    const stats = response.stats;
                    $('#view_services_count').text(stats.services_count || 0);
                    $('#view_sales_total').text(formatCurrency(stats.sales_total || 0, currencySymbol));
                } else {
                    $('#view_services_count').text('0');
                    $('#view_sales_total').text(formatCurrency(0, currencySymbol));
                }
            },
            error: function() {
                $('#view_services_count').text('0');
                $('#view_sales_total').text(formatCurrency(0, currencySymbol));
            }
        });
    }
    
    // تحميل سجل حضور الموظف
    function loadEmployeeAttendance(employeeId, month, year) {
        month = month || $('#attendance_month').val();
        year = year || $('#attendance_year').val();
        
        $.ajax({
            url: '../../api/employees.php',
            type: 'POST',
            data: {
                action: 'get_employee_attendance',
                employee_id: employeeId,
                month: month,
                year: year
            },
            dataType: 'json',
            success: function(response) {
                const tbody = $('#attendance_table_body');
                tbody.empty();
                
                if (response.status === 'success' && response.attendance && response.attendance.length > 0) {
                    response.attendance.forEach(function(record) {
                        const row = $('<tr></tr>');
                        row.append(`<td>${record.date}</td>`);
                        row.append(`<td>${record.check_in || '-'}</td>`);
                        row.append(`<td>${record.check_out || '-'}</td>`);
                        row.append(`<td>${record.notes || '-'}</td>`);
                        tbody.append(row);
                    });
                    
                    $('#no_attendance_message').hide();
                } else {
                    $('#no_attendance_message').show();
                }
            },
            error: function() {
                $('#attendance_table_body').empty();
                $('#no_attendance_message').show();
            }
        });
    }
    
    // زر تحميل سجل الحضور
    $('#load_attendance').on('click', function() {
        const employeeId = $('#editEmployeeBtn').data('id');
        const month = $('#attendance_month').val();
        const year = $('#attendance_year').val();
        
        loadEmployeeAttendance(employeeId, month, year);
    });
    
    // فتح مودال التعديل من مودال التفاصيل
    $('#editEmployeeBtn').on('click', function() {
        const employeeId = $(this).data('id');
        $('#viewEmployeeModal').modal('hide');
        
        setTimeout(function() {
            $('.edit-employee[data-id="' + employeeId + '"]').click();
        }, 500);
    });
    
    // تحميل بيانات الموظف للتعديل
    $(document).on('click', '.edit-employee', function() {
        const employeeId = $(this).data('id');
        
        $.ajax({
            url: '../../api/employees.php',
            type: 'POST',
            data: {
                action: 'get_employee',
                employee_id: employeeId
            },
            dataType: 'json',
            success: function(response) {
                if (response.status === 'success') {
                    const employee = response.employee;
                    
                    $('#edit_employee_id').val(employee.id);
                    $('#edit_name').val(employee.name);
                    $('#edit_position').val(employee.position);
                    $('#edit_phone').val(employee.phone);
                    $('#edit_email').val(employee.email);
                    $('#edit_salary_type').val(employee.salary_type).trigger('change');
                    $('#edit_fixed_salary').val(employee.fixed_salary);
                    $('#edit_commission_percentage').val(employee.commission_percentage);
                    
                    if ($('#edit_branch_id').length) {
                        $('#edit_branch_id').val(employee.branch_id);
                    }
                    
                    $('#edit_is_active').val(employee.is_active);
                    
                    // تحميل الخدمات
                    if (employee.services && employee.services.length > 0) {
                        $('#edit_services').val(employee.services.map(s => s.id)).trigger('change');
                    } else {
                        $('#edit_services').val([]).trigger('change');
                    }
                    
                    // إذا كان لديه حساب مستخدم
                    if (employee.user_id) {
                        $('#edit_create_user').prop('checked', true).trigger('change');
                        $('#edit_username').val(employee.username);
                    } else {
                        $('#edit_create_user').prop('checked', false).trigger('change');
                        $('#edit_username').val('');
                    }
                    
                    $('#editEmployeeModal').modal('show');
                } else {
                    showAlert(response.message, 'danger');
                }
            },
            error: function() {
                showAlert('حدث خطأ أثناء تحميل بيانات الموظف', 'danger');
            }
        });
    });
    
    // تعديل بيانات الموظف
    $('#submitEditEmployee').on('click', function() {
        const form = $('#editEmployeeForm')[0];
        if (!form.checkValidity()) {
            form.reportValidity();
            return;
        }
        
        const formData = new FormData(form);
        formData.append('action', 'update_employee');
        
        // جمع الخدمات المختارة
        const services = $('#edit_services').val();
        if (services && services.length > 0) {
            formData.delete('services[]');
            formData.append('services', services);
        }
        
        $.ajax({
            url: '../../api/employees.php',
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            dataType: 'json',
            success: function(response) {
                if (response.status === 'success') {
                    // إغلاق المودال وإعادة تحميل الجدول
                    $('#editEmployeeModal').modal('hide');
                    
                    // إظهار كلمة المرور المولدة إذا كانت موجودة
                    if (response.generated_password) {
                        let message = response.message + `<br><br>تم إنشاء حساب مستخدم بكلمة المرور: <strong>${response.generated_password}</strong>`;
                        showAlert(message);
                    } else {
                        showAlert(response.message);
                    }
                    
                    employeesTable.ajax.reload();
                } else {
                    showAlert(response.message, 'danger');
                }
            },
            error: function() {
                showAlert('حدث خطأ أثناء تعديل بيانات الموظف', 'danger');
            }
        });
    });
    
    // حذف موظف
    $(document).on('click', '.delete-employee', function() {
        if (!confirm('هل أنت متأكد من حذف هذا الموظف؟')) {
            return;
        }
        
        const employeeId = $(this).data('id');
        
        $.ajax({
            url: '../../api/employees.php',
            type: 'POST',
            data: {
                action: 'delete_employee',
                employee_id: employeeId
            },
            dataType: 'json',
            success: function(response) {
                if (response.status === 'success') {
                    showAlert(response.message);
                    employeesTable.ajax.reload();
                } else {
                    showAlert(response.message, 'danger');
                }
            },
            error: function() {
                showAlert('حدث خطأ أثناء حذف الموظف', 'danger');
            }
        });
    });
    
    // فتح مودال تسجيل الحضور
    $(document).on('click', '.record-attendance', function() {
        const employeeId = $(this).data('id');
        $('#attendance_employee_id').val(employeeId);
        $('#attendance_date').val(getCurrentDate());
        $('#check_in').val('');
        $('#check_out').val('');
        $('#attendance_notes').val('');
        $('#recordAttendanceModal').modal('show');
    });
    
    // تسجيل الحضور
    $('#submitAttendance').on('click', function() {
        const form = $('#attendanceForm')[0];
        if (!form.checkValidity()) {
            form.reportValidity();
            return;
        }
        
        const formData = new FormData(form);
        formData.append('action', 'record_attendance');
        
        $.ajax({
            url: '../../api/employees.php',
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            dataType: 'json',
            success: function(response) {
                if (response.status === 'success') {
                    $('#recordAttendanceModal').modal('hide');
                    showAlert(response.message);
                } else {
                    showAlert(response.message, 'danger');
                }
            },
            error: function() {
                showAlert('حدث خطأ أثناء تسجيل الحضور', 'danger');
            }
        });
    });
    
    // الحصول على التاريخ الحالي بتنسيق YYYY/MM/DD
    function getCurrentDate() {
        const now = new Date();
        const year = now.getFullYear();
        const month = String(now.getMonth() + 1).padStart(2, '0');
        const day = String(now.getDate()).padStart(2, '0');
        return `${year}/${month}/${day}`;
    }
    
    // عرض رسالة تنبيه
    function showAlert(message, type = 'success') {
        if (typeof toastr !== 'undefined') {
            toastr.options = {
                closeButton: true,
                progressBar: true,
                positionClass: 'toast-top-left',
                timeOut: 5000
            };
            
            if (type === 'success') {
                toastr.success(message);
            } else if (type === 'danger' || type === 'error') {
                toastr.error(message);
            } else if (type === 'warning') {
                toastr.warning(message);
            } else {
                toastr.info(message);
            }
        } else {
            alert(message);
        }
    }
    
    // تهيئة Select2 للخدمات
    $('.select2').select2({
        dir: "rtl",
        language: "ar",
        placeholder: "اختر الخدمات",
        width: '100%'
    });
    
    // تهيئة تقويم التاريخ
    $('.date-picker').daterangepicker({
        singleDatePicker: true,
        showDropdowns: true,
        locale: {
            format: 'YYYY/MM/DD',
            applyLabel: 'تطبيق',
            cancelLabel: 'إلغاء',
            fromLabel: 'من',
            toLabel: 'إلى',
            customRangeLabel: 'مخصص',
            daysOfWeek: ['أح', 'إث', 'ثل', 'أر', 'خم', 'جم', 'سب'],
            monthNames: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو', 'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'],
            firstDay: 0
        }
    });
});
