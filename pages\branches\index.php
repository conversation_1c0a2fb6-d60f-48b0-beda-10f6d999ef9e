<?php
/**
 * صفحة إدارة الفروع
 */

// تعريف ثابت لمنع الوصول المباشر للملفات المرتبطة
define('BASEPATH', true);

// استدعاء ملفات التهيئة
require_once '../../config/init.php';

// التحقق من تسجيل الدخول
requireLogin();

// التحقق من وجود صلاحية عرض الفروع
requirePermission('branches_view');

// عنوان الصفحة
$pageTitle = 'إدارة الفروع';

// استدعاء رأس الصفحة
include '../../includes/templates/header.php';

// إنشاء كائن قاعدة البيانات
$branchModel = new Branch($db);
?>

<!-- المحتوى الرئيسي للصفحة -->
<div class="container-fluid">
    <!-- رأس الصفحة -->
    <div class="card border-0 shadow-sm mb-4">
        <div class="card-body d-flex justify-content-between align-items-center">
            <h5 class="mb-0">
                <i class="fas fa-store me-2"></i> إدارة الفروع
            </h5>
            <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addBranchModal">
                <i class="fas fa-plus me-1"></i> إضافة فرع جديد
            </button>
        </div>
    </div>

    <!-- جدول الفروع -->
    <div class="card border-0 shadow-sm">
        <div class="card-body">
            <div class="table-responsive">
                <table id="branchesTable" class="table table-hover">
                    <thead class="table-light">
                        <tr>
                            <th>#</th>
                            <th>اسم الفرع</th>
                            <th>العنوان</th>
                            <th>رقم الهاتف</th>
                            <th>المدير</th>
                            <th>الحالة</th>
                            <th>تاريخ الإنشاء</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <!-- سيتم تعبئة البيانات عبر AJAX -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- مودال إضافة فرع جديد -->
<div class="modal fade" id="addBranchModal" tabindex="-1" aria-labelledby="addBranchModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addBranchModalLabel">إضافة فرع جديد</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="إغلاق"></button>
            </div>
            <div class="modal-body">
                <form id="addBranchForm">
                    <div class="mb-3">
                        <label for="name" class="form-label">اسم الفرع <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="name" name="name" required>
                    </div>
                    <div class="mb-3">
                        <label for="address" class="form-label">العنوان</label>
                        <input type="text" class="form-control" id="address" name="address">
                    </div>
                    <div class="mb-3">
                        <label for="phone" class="form-label">رقم الهاتف</label>
                        <input type="text" class="form-control" id="phone" name="phone" dir="ltr">
                    </div>
                    <div class="mb-3">
                        <label for="manager_id" class="form-label">المدير</label>
                        <select class="form-select" id="manager_id" name="manager_id">
                            <option value="">-- اختر المدير --</option>
                            <?php
                            // استرجاع قائمة الموظفين
                            $employeeModel = new Employee($db);
                            $employees = $employeeModel->getEmployees(['is_active' => 1]);
                            foreach ($employees as $employee) {
                                echo '<option value="' . $employee['id'] . '">' . htmlspecialchars($employee['name']) . '</option>';
                            }
                            ?>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="is_active" class="form-label">الحالة</label>
                        <select class="form-select" id="is_active" name="is_active">
                            <option value="1" selected>نشط</option>
                            <option value="0">غير نشط</option>
                        </select>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" id="submitAddBranch">حفظ</button>
            </div>
        </div>
    </div>
</div>

<!-- مودال تعديل فرع -->
<div class="modal fade" id="editBranchModal" tabindex="-1" aria-labelledby="editBranchModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editBranchModalLabel">تعديل بيانات الفرع</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="إغلاق"></button>
            </div>
            <div class="modal-body">
                <form id="editBranchForm">
                    <input type="hidden" id="edit_branch_id" name="branch_id">
                    <div class="mb-3">
                        <label for="edit_name" class="form-label">اسم الفرع <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="edit_name" name="name" required>
                    </div>
                    <div class="mb-3">
                        <label for="edit_address" class="form-label">العنوان</label>
                        <input type="text" class="form-control" id="edit_address" name="address">
                    </div>
                    <div class="mb-3">
                        <label for="edit_phone" class="form-label">رقم الهاتف</label>
                        <input type="text" class="form-control" id="edit_phone" name="phone" dir="ltr">
                    </div>
                    <div class="mb-3">
                        <label for="edit_manager_id" class="form-label">المدير</label>
                        <select class="form-select" id="edit_manager_id" name="manager_id">
                            <option value="">-- اختر المدير --</option>
                            <?php
                            foreach ($employees as $employee) {
                                echo '<option value="' . $employee['id'] . '">' . htmlspecialchars($employee['name']) . '</option>';
                            }
                            ?>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="edit_is_active" class="form-label">الحالة</label>
                        <select class="form-select" id="edit_is_active" name="is_active">
                            <option value="1">نشط</option>
                            <option value="0">غير نشط</option>
                        </select>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" id="submitEditBranch">حفظ التعديلات</button>
            </div>
        </div>
    </div>
</div>

<!-- مودال عرض تفاصيل الفرع -->
<div class="modal fade" id="viewBranchModal" tabindex="-1" aria-labelledby="viewBranchModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="viewBranchModalLabel">تفاصيل الفرع</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="إغلاق"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label fw-bold">اسم الفرع:</label>
                        <p id="view_name" class="mb-0"></p>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label fw-bold">الحالة:</label>
                        <p id="view_status" class="mb-0"></p>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label fw-bold">العنوان:</label>
                        <p id="view_address" class="mb-0"></p>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label fw-bold">رقم الهاتف:</label>
                        <p id="view_phone" dir="ltr" class="mb-0"></p>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label fw-bold">المدير:</label>
                        <p id="view_manager" class="mb-0"></p>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label fw-bold">تاريخ الإنشاء:</label>
                        <p id="view_created_at" class="mb-0"></p>
                    </div>
                </div>

                <hr>

                <!-- علامات التبويب للموظفين والعملاء -->
                <ul class="nav nav-tabs" id="branchTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="employees-tab" data-bs-toggle="tab" data-bs-target="#employees" type="button" role="tab" aria-controls="employees" aria-selected="true">
                            الموظفين
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="customers-tab" data-bs-toggle="tab" data-bs-target="#customers" type="button" role="tab" aria-controls="customers" aria-selected="false">
                            العملاء
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="inventory-tab" data-bs-toggle="tab" data-bs-target="#inventory" type="button" role="tab" aria-controls="inventory" aria-selected="false">
                            المخزون
                        </button>
                    </li>
                </ul>

                <div class="tab-content p-3 border border-top-0 rounded-bottom">
                    <!-- قسم الموظفين -->
                    <div class="tab-pane fade show active" id="employees" role="tabpanel" aria-labelledby="employees-tab">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>الاسم</th>
                                    <th>الوظيفة</th>
                                    <th>رقم الهاتف</th>
                                    <th>الحالة</th>
                                </tr>
                            </thead>
                            <tbody id="branch_employees_body">
                                <!-- سيتم تعبئة البيانات عبر AJAX -->
                            </tbody>
                        </table>
                        <div id="no_employees_message" class="text-center text-muted py-3" style="display: none;">
                            لا يوجد موظفين في هذا الفرع
                        </div>
                    </div>

                    <!-- قسم العملاء -->
                    <div class="tab-pane fade" id="customers" role="tabpanel" aria-labelledby="customers-tab">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>الاسم</th>
                                    <th>رقم الهاتف</th>
                                    <th>البريد الإلكتروني</th>
                                    <th>نقاط الولاء</th>
                                </tr>
                            </thead>
                            <tbody id="branch_customers_body">
                                <!-- سيتم تعبئة البيانات عبر AJAX -->
                            </tbody>
                        </table>
                        <div id="no_customers_message" class="text-center text-muted py-3" style="display: none;">
                            لا يوجد عملاء مسجلين في هذا الفرع
                        </div>
                    </div>

                    <!-- قسم المخزون -->
                    <div class="tab-pane fade" id="inventory" role="tabpanel" aria-labelledby="inventory-tab">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>المنتج</th>
                                    <th>الكمية المتوفرة</th>
                                    <th>سعر البيع</th>
                                </tr>
                            </thead>
                            <tbody id="branch_inventory_body">
                                <!-- سيتم تعبئة البيانات عبر AJAX -->
                            </tbody>
                        </table>
                        <div id="no_inventory_message" class="text-center text-muted py-3" style="display: none;">
                            لا يوجد مخزون مسجل في هذا الفرع
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-warning me-auto" id="editBranchBtn">
                    <i class="fas fa-edit me-1"></i> تعديل البيانات
                </button>
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
            </div>
        </div>
    </div>
</div>

<!-- استدعاء قالب الفوتر -->
<?php include '../../includes/templates/footer.php'; ?>

<script>
    $(document).ready(function() {
        // تهيئة جدول الفروع
        const branchesTable = $('#branchesTable').DataTable({
            processing: true,
            serverSide: false,
            ajax: {
                url: '../../api/branches.php',
                type: 'POST',
                data: function(d) {
                    d.action = 'list';
                },
                dataSrc: function(response) {
                    return response.data || [];
                }
            },
            columns: [
                { data: 'id' },
                { data: 'name' },
                {
                    data: 'address',
                    render: function(data) {
                        return data || '<span class="text-muted">-</span>';
                    }
                },
                {
                    data: 'phone',
                    render: function(data) {
                        return data || '<span class="text-muted">-</span>';
                    }
                },
                {
                    data: 'manager_name',
                    render: function(data) {
                        return data || '<span class="text-muted">-</span>';
                    }
                },
                {
                    data: 'is_active',
                    render: function(data) {
                        if (data == 1) {
                            return '<span class="badge bg-success">نشط</span>';
                        } else {
                            return '<span class="badge bg-danger">غير نشط</span>';
                        }
                    }
                },
                {
                    data: 'created_at',
                    render: function(data) {
                        return moment(data).format('YYYY/MM/DD');
                    }
                },
                {
                    data: null,
                    orderable: false,
                    render: function(data) {
                        let actions = `
                            <div class="dropdown">
                                <button class="btn btn-sm btn-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                                    الإجراءات
                                </button>
                                <ul class="dropdown-menu dropdown-menu-end">
                                    <li><a class="dropdown-item view-branch" href="javascript:void(0)" data-id="${data.id}">
                                        <i class="fas fa-eye me-2"></i> عرض التفاصيل
                                    </a></li>
                                    <li><a class="dropdown-item edit-branch" href="javascript:void(0)" data-id="${data.id}">
                                        <i class="fas fa-edit me-2"></i> تعديل
                                    </a></li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item text-danger delete-branch" href="javascript:void(0)" data-id="${data.id}">
                                        <i class="fas fa-trash-alt me-2"></i> حذف
                                    </a></li>
                                </ul>
                            </div>`;

                        return actions;
                    }
                }
            ],
            language: {
                url: 'https://cdn.datatables.net/plug-ins/1.11.5/i18n/ar.json'
            }
        });

        // إضافة فرع جديد
        $('#submitAddBranch').on('click', function() {
            const form = $('#addBranchForm')[0];
            if (!form.checkValidity()) {
                form.reportValidity();
                return;
            }

            const formData = new FormData(form);
            formData.append('action', 'add');

            $.ajax({
                url: '../../api/branches.php',
                type: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                dataType: 'json',
                success: function(response) {
                    if (response.success) {
                        // إغلاق المودال وإعادة تحميل الجدول
                        $('#addBranchModal').modal('hide');
                        form.reset();

                        showAlert(response.message);
                        branchesTable.ajax.reload();
                    } else {
                        showAlert(response.message, 'danger');
                    }
                },
                error: function() {
                    showAlert('حدث خطأ أثناء إضافة الفرع', 'danger');
                }
            });
        });

        // تحميل بيانات الفرع للتعديل
        $(document).on('click', '.edit-branch', function() {
            const branchId = $(this).data('id');

            $.ajax({
                url: '../../api/branches.php',
                type: 'POST',
                data: {
                    action: 'get',
                    id: branchId
                },
                dataType: 'json',
                success: function(response) {
                    if (response.success) {
                        const branch = response.data;

                        $('#edit_branch_id').val(branch.id);
                        $('#edit_name').val(branch.name);
                        $('#edit_address').val(branch.address);
                        $('#edit_phone').val(branch.phone);
                        $('#edit_manager_id').val(branch.manager_id);
                        $('#edit_is_active').val(branch.is_active);

                        $('#editBranchModal').modal('show');
                    } else {
                        showAlert(response.message, 'danger');
                    }
                },
                error: function() {
                    showAlert('حدث خطأ أثناء تحميل بيانات الفرع', 'danger');
                }
            });
        });

        // تعديل بيانات الفرع
        $('#submitEditBranch').on('click', function() {
            const form = $('#editBranchForm')[0];
            if (!form.checkValidity()) {
                form.reportValidity();
                return;
            }

            const formData = new FormData(form);
            formData.append('action', 'update');

            $.ajax({
                url: '../../api/branches.php',
                type: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                dataType: 'json',
                success: function(response) {
                    if (response.success) {
                        // إغلاق المودال وإعادة تحميل الجدول
                        $('#editBranchModal').modal('hide');

                        showAlert(response.message);
                        branchesTable.ajax.reload();
                    } else {
                        showAlert(response.message, 'danger');
                    }
                },
                error: function() {
                    showAlert('حدث خطأ أثناء تعديل بيانات الفرع', 'danger');
                }
            });
        });

        // حذف فرع
        $(document).on('click', '.delete-branch', function() {
            const branchId = $(this).data('id');

            confirmAction('هل أنت متأكد من حذف هذا الفرع؟', function() {
                $.ajax({
                    url: '../../api/branches.php',
                    type: 'POST',
                    data: {
                        action: 'delete',
                        id: branchId
                    },
                    dataType: 'json',
                    success: function(response) {
                        if (response.success) {
                            showAlert(response.message);
                            branchesTable.ajax.reload();
                        } else {
                            showAlert(response.message, 'danger');
                        }
                    },
                    error: function() {
                        showAlert('حدث خطأ أثناء حذف الفرع', 'danger');
                    }
                });
            });
        });

        // عرض تفاصيل الفرع
        $(document).on('click', '.view-branch', function() {
            const branchId = $(this).data('id');

            $.ajax({
                url: '../../api/branches.php',
                type: 'POST',
                data: {
                    action: 'get',
                    id: branchId,
                    with_details: true
                },
                dataType: 'json',
                success: function(response) {
                    if (response.success) {
                        const branch = response.data;

                        // عرض بيانات الفرع
                        $('#view_name').text(branch.name);
                        $('#view_address').text(branch.address || '-');
                        $('#view_phone').text(branch.phone || '-');
                        $('#view_manager').text(branch.manager_name || '-');
                        $('#view_created_at').text(moment(branch.created_at).format('YYYY/MM/DD'));

                        // عرض حالة الفرع
                        if (branch.is_active == 1) {
                            $('#view_status').html('<span class="badge bg-success">نشط</span>');
                        } else {
                            $('#view_status').html('<span class="badge bg-danger">غير نشط</span>');
                        }

                        // تحديث روابط الإجراءات
                        $('#editBranchBtn').data('id', branch.id);

                        // عرض الموظفين
                        const employeesBody = $('#branch_employees_body');
                        employeesBody.empty();

                        if (branch.employees && branch.employees.length > 0) {
                            $('#no_employees_message').hide();
                            branch.employees.forEach(function(employee) {
                                let status = employee.is_active == 1 ?
                                    '<span class="badge bg-success">نشط</span>' :
                                    '<span class="badge bg-danger">غير نشط</span>';

                                employeesBody.append(`
                                    <tr>
                                        <td>${employee.name}</td>
                                        <td>${employee.position || '-'}</td>
                                        <td>${employee.phone || '-'}</td>
                                        <td>${status}</td>
                                    </tr>
                                `);
                            });
                        } else {
                            $('#no_employees_message').show();
                        }

                        // عرض العملاء
                        const customersBody = $('#branch_customers_body');
                        customersBody.empty();

                        if (branch.customers && branch.customers.length > 0) {
                            $('#no_customers_message').hide();
                            branch.customers.forEach(function(customer) {
                                customersBody.append(`
                                    <tr>
                                        <td>${customer.name}</td>
                                        <td>${customer.phone || '-'}</td>
                                        <td>${customer.email || '-'}</td>
                                        <td>${customer.loyalty_points || 0}</td>
                                    </tr>
                                `);
                            });
                        } else {
                            $('#no_customers_message').show();
                        }

                        // عرض المخزون
                        const inventoryBody = $('#branch_inventory_body');
                        inventoryBody.empty();

                        if (branch.inventory && branch.inventory.length > 0) {
                            $('#no_inventory_message').hide();
                            branch.inventory.forEach(function(item) {
                                inventoryBody.append(`
                                    <tr>
                                        <td>${item.name}</td>
                                        <td>${item.quantity}</td>
                                        <td>${parseFloat(item.price).toFixed(2)}</td>
                                    </tr>
                                `);
                            });
                        } else {
                            $('#no_inventory_message').show();
                        }

                        // عرض المودال
                        $('#viewBranchModal').modal('show');
                    } else {
                        showAlert(response.message, 'danger');
                    }
                },
                error: function() {
                    showAlert('حدث خطأ أثناء تحميل بيانات الفرع', 'danger');
                }
            });
        });

        // فتح مودال التعديل من مودال التفاصيل
        $('#editBranchBtn').on('click', function() {
            const branchId = $(this).data('id');
            $('#viewBranchModal').modal('hide');

            setTimeout(function() {
                $('.edit-branch[data-id="' + branchId + '"]').click();
            }, 500);
        });
    });
</script>