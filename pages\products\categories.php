<?php
/**
 * صفحة إدارة فئات المنتجات
 * تعرض قائمة الفئات مع إمكانية الإضافة والتعديل والحذف
 */

// تعريف ثابت لمنع الوصول المباشر للملفات المرتبطة
define('BASEPATH', true);

// استدعاء ملف التهيئة
require_once '../../config/init.php';

// التحقق من تسجيل الدخول
redirectIfNotLoggedIn();

// التحقق من صلاحية الوصول
if (!hasPermission('products_view')) {
    $_SESSION['error_message'] = 'ليس لديك صلاحية لعرض فئات المنتجات';
    header('Location: ' . BASE_URL . 'pages/dashboard.php');
    exit;
}

// إنشاء كائن المنتجات
$productObj = new Product($db);

// الحصول على بيانات الفئات
$categories = $productObj->getProductCategories();

// عنوان الصفحة
$pageTitle = 'إدارة فئات المنتجات';

// استدعاء قالب الرأس
include_once '../../includes/templates/header.php';
?>

<!-- تأكد من تحميل jQuery قبل toastr -->
<script>
    // التحقق من وجود jQuery
    if (typeof jQuery === 'undefined') {
        document.write('<script src="https://code.jquery.com/jquery-3.6.0.min.js"><\/script>');
    }
</script>

<!-- إضافة مكتبة toastr بعد التأكد من وجود jQuery -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.css">
<script src="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.js"></script>

<!-- تهيئة toastr -->
<script>
    $(document).ready(function() {
        // تهيئة إعدادات toastr
        toastr.options = {
            closeButton: true,
            progressBar: true,
            positionClass: 'toast-top-left',
            timeOut: 3000
        };
    });
</script>

<!-- بداية محتوى الصفحة -->
<div class="page-content">
    <div class="container-fluid">
        
        <!-- عنوان الصفحة والأزرار -->
        <div class="row mb-4 align-items-center">
            <div class="col-md-6">
                <h4 class="fw-bold mb-2"><?php echo $pageTitle; ?></h4>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb mb-0">
                        <li class="breadcrumb-item">
                            <a href="<?php echo BASE_URL . 'pages/dashboard.php'; ?>" class="text-decoration-none">
                                <i class="fas fa-home me-1"></i>الرئيسية
                            </a>
                        </li>
                        <li class="breadcrumb-item">
                            <a href="<?php echo BASE_URL . 'pages/products/index.php'; ?>" class="text-decoration-none">
                                <i class="fas fa-box me-1"></i>المنتجات
                            </a>
                        </li>
                        <li class="breadcrumb-item active" aria-current="page">فئات المنتجات</li>
                    </ol>
                </nav>
            </div>
            <div class="col-md-6 text-md-end">
                <a href="<?php echo BASE_URL . 'pages/products/index.php'; ?>" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-right me-1"></i> العودة للمنتجات
                </a>
            </div>
        </div>
        
        <!-- محتوى الصفحة -->
        <div class="row">
            <div class="col-lg-4 mb-4">
                <!-- بطاقة إضافة/تعديل فئة -->
                <div class="card h-100">
                    <div class="card-header bg-light">
                        <h5 class="mb-0" id="categoryFormTitle">إضافة فئة جديدة</h5>
                    </div>
                    <div class="card-body">
                        <form id="categoryForm">
                            <input type="hidden" id="categoryId" name="categoryId" value="">
                            <div class="mb-3">
                                <label for="categoryName" class="form-label">اسم الفئة <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="categoryName" name="name" required>
                            </div>
                            <div class="mb-3">
                                <label for="categoryDescription" class="form-label">الوصف</label>
                                <textarea class="form-control" id="categoryDescription" name="description" rows="3"></textarea>
                            </div>
                            <div class="d-flex gap-2">
                                <button type="submit" class="btn btn-primary px-4" id="saveCategory">
                                    <i class="fas fa-save me-1"></i> حفظ
                                </button>
                                <button type="button" class="btn btn-outline-secondary px-4" id="cancelEdit" style="display: none;">
                                    <i class="fas fa-times me-1"></i> إلغاء
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <div class="col-lg-8">
                <!-- بطاقة قائمة الفئات -->
                <div class="card">
                    <div class="card-header bg-light d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">قائمة فئات المنتجات</h5>
                        <div>
                            <span class="badge bg-primary" id="total-categories"><?php echo count($categories); ?></span> فئة
                        </div>
                    </div>
                    <div class="card-body">
                        <?php if (count($categories) > 0): ?>
                            <div class="table-responsive">
                                <table class="table table-bordered table-hover" id="categories-table">
                                    <thead class="table-light">
                                        <tr>
                                            <th width="5%">#</th>
                                            <th width="30%">اسم الفئة</th>
                                            <th>الوصف</th>
                                            <th width="15%">عدد المنتجات</th>
                                            <th width="15%">الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody id="categories-list">
                                        <?php foreach ($categories as $index => $category): ?>
                                            <tr id="category-row-<?php echo $category['id']; ?>">
                                                <td><?php echo $index + 1; ?></td>
                                                <td><?php echo htmlspecialchars($category['name']); ?></td>
                                                <td><?php echo htmlspecialchars($category['description'] ?? ''); ?></td>
                                                <td>
                                                    <span class="badge bg-info"><?php echo isset($category['product_count']) ? $category['product_count'] : '0'; ?></span>
                                                </td>
                                                <td>
                                                    <?php if (hasPermission('products_edit')): ?>
                                                        <button type="button" class="btn btn-sm btn-primary edit-category" 
                                                            data-id="<?php echo $category['id']; ?>" 
                                                            data-name="<?php echo htmlspecialchars($category['name']); ?>" 
                                                            data-description="<?php echo htmlspecialchars($category['description'] ?? ''); ?>">
                                                            <i class="fas fa-edit"></i>
                                                        </button>
                                                    <?php endif; ?>
                                                    
                                                    <?php if (hasPermission('products_delete')): ?>
                                                        <button type="button" class="btn btn-sm btn-danger delete-category" 
                                                            data-id="<?php echo $category['id']; ?>" 
                                                            data-name="<?php echo htmlspecialchars($category['name']); ?>">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    <?php endif; ?>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php else: ?>
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i> لا توجد فئات منتجات مضافة بعد.
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- موديل تأكيد الحذف -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title" id="deleteModalLabel">تأكيد حذف الفئة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من حذف الفئة: <strong id="deleteCategoryName"></strong>؟</p>
                <p class="text-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    سيؤدي حذف الفئة إلى إزالة ارتباطها بالمنتجات. سيتم نقل المنتجات المرتبطة بهذه الفئة إلى "بدون فئة".
                </p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-danger" id="confirmDelete">
                    <i class="fas fa-trash me-1"></i> تأكيد الحذف
                </button>
            </div>
        </div>
    </div>
</div>

<?php
// استدعاء قالب الذيل
include_once '../../includes/templates/footer.php';
?>

<script>
$(document).ready(function() {
    // تهيئة جدول البيانات
    $('#categories-table').DataTable({
        "paging": true,
        "lengthChange": false,
        "searching": true,
        "ordering": true,
        "info": true,
        "autoWidth": false,
        "responsive": true,
        "language": {
            // استخدام ملف اللغة العربية من CDN بدلاً من الملف المحلي
            "url": "https://cdn.datatables.net/plug-ins/1.11.5/i18n/ar.json"
        }
    });

    // حدث إضافة/تعديل فئة
    $('#categoryForm').on('submit', function(e) {
        e.preventDefault();
        
        const categoryId = $('#categoryId').val();
        const name = $('#categoryName').val().trim();
        const description = $('#categoryDescription').val().trim();
        
        if (!name) {
            showToast('تنبيه', 'يرجى إدخال اسم الفئة', 'warning');
            return;
        }
        
        // تحديد نوع العملية (إضافة أو تحديث)
        const isUpdate = categoryId !== '';
        const action = isUpdate ? 'update_category' : 'add_category';
        const url = `<?php echo API_URL; ?>products.php?action=${action}`;
        
        // إعداد البيانات
        const formData = new FormData();
        formData.append('action', action);
        formData.append('name', name);
        formData.append('description', description);
        
        if (isUpdate) {
            formData.append('id', categoryId);
        }
        
        // إرسال الطلب
        $.ajax({
            url: url,
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            beforeSend: function() {
                $('#saveCategory').prop('disabled', true).html('<span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span> جاري الحفظ...');
            },
            success: function(response) {
                $('#saveCategory').prop('disabled', false).html('<i class="fas fa-save me-1"></i> حفظ');
                
                if (response.success) {
                    // عرض رسالة نجاح
                    showToast('نجاح', response.message || (isUpdate ? 'تم تحديث الفئة بنجاح' : 'تمت إضافة الفئة بنجاح'), 'success');
                    
                    // تحديث الصفحة بعد نجاح العملية
                    setTimeout(function() {
                        location.reload();
                    }, 1000);
                } else {
                    showToast('خطأ', response.message || 'حدث خطأ أثناء معالجة الطلب', 'error');
                }
            },
            error: function(xhr, status, error) {
                $('#saveCategory').prop('disabled', false).html('<i class="fas fa-save me-1"></i> حفظ');
                
                console.error(xhr.responseText);
                let errorMessage = 'حدث خطأ في الاتصال بالخادم';
                
                try {
                    const response = JSON.parse(xhr.responseText);
                    if (response.message) {
                        errorMessage = response.message;
                    }
                } catch (e) {}
                
                showToast('خطأ', errorMessage, 'error');
            }
        });
    });

    // حدث النقر على زر التعديل
    $(document).on('click', '.edit-category', function() {
        const categoryId = $(this).data('id');
        const name = $(this).data('name');
        const description = $(this).data('description');
        
        // تعبئة النموذج ببيانات الفئة
        $('#categoryId').val(categoryId);
        $('#categoryName').val(name);
        $('#categoryDescription').val(description);
        
        // تغيير عنوان النموذج وإظهار زر الإلغاء
        $('#categoryFormTitle').text('تعديل الفئة');
        $('#saveCategory').html('<i class="fas fa-save me-1"></i> حفظ التعديلات');
        $('#cancelEdit').show();
        
        // التمرير إلى النموذج
        $('html, body').animate({
            scrollTop: $("#categoryForm").offset().top - 100
        }, 500);
    });

    // حدث النقر على زر إلغاء التعديل
    $('#cancelEdit').on('click', function() {
        resetForm();
    });

    // حدث النقر على زر الحذف
    $(document).on('click', '.delete-category', function() {
        const categoryId = $(this).data('id');
        const categoryName = $(this).data('name');
        
        // تعيين بيانات الفئة في النافذة
        $('#deleteCategoryName').text(categoryName);
        
        // تخزين معرف الفئة في زر التأكيد
        $('#confirmDelete').data('id', categoryId);
        
        // عرض نافذة التأكيد
        $('#deleteModal').modal('show');
    });

    // حدث النقر على زر تأكيد الحذف
    $('#confirmDelete').on('click', function() {
        const categoryId = $(this).data('id');
        const url = `<?php echo API_URL; ?>products.php?action=delete_category&id=${categoryId}`;
        
        // إرسال طلب الحذف
        $.ajax({
            url: url,
            type: 'GET',
            beforeSend: function() {
                $('#confirmDelete').prop('disabled', true).html('<span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span> جاري الحذف...');
            },
            success: function(response) {
                $('#confirmDelete').prop('disabled', false).html('<i class="fas fa-trash me-1"></i> تأكيد الحذف');
                
                // إخفاء النافذة
                $('#deleteModal').modal('hide');
                
                if (response.success) {
                    // عرض رسالة نجاح
                    showToast('نجاح', response.message || 'تم حذف الفئة بنجاح', 'success');
                    
                    // حذف صف الفئة من الجدول
                    $(`#category-row-${categoryId}`).fadeOut(500, function() {
                        $(this).remove();
                        
                        // تحديث عداد الفئات
                        $('#total-categories').text($('#categories-list tr').length);
                        
                        // إذا لم تعد هناك فئات، عرض رسالة
                        if ($('#categories-list tr').length === 0) {
                            $('#categories-table').parent().html(`
                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle me-2"></i> لا توجد فئات منتجات مضافة بعد.
                                </div>
                            `);
                        }
                    });
                } else {
                    showToast('خطأ', response.message || 'حدث خطأ أثناء حذف الفئة', 'error');
                }
            },
            error: function(xhr, status, error) {
                $('#confirmDelete').prop('disabled', false).html('<i class="fas fa-trash me-1"></i> تأكيد الحذف');
                $('#deleteModal').modal('hide');
                
                let errorMessage = 'حدث خطأ في الاتصال بالخادم';
                try {
                    const response = JSON.parse(xhr.responseText);
                    if (response.message) {
                        errorMessage = response.message;
                    }
                } catch (e) {}
                
                showToast('خطأ', errorMessage, 'error');
            }
        });
    });

    // دالة إعادة تعيين النموذج
    function resetForm() {
        $('#categoryId').val('');
        $('#categoryName').val('');
        $('#categoryDescription').val('');
        $('#categoryFormTitle').text('إضافة فئة جديدة');
        $('#saveCategory').html('<i class="fas fa-save me-1"></i> حفظ');
        $('#cancelEdit').hide();
    }

    // دالة عرض التنبيهات
    function showToast(title, message, type) {
        // التحقق من وجود مكتبة toastr
        if (typeof toastr !== 'undefined') {
            toastr.options = {
                closeButton: true,
                progressBar: true,
                positionClass: 'toast-top-left',
                timeOut: 3000
            };
            
            if (type === 'success') {
                toastr.success(message, title);
            } else if (type === 'error') {
                toastr.error(message, title);
            } else if (type === 'warning') {
                toastr.warning(message, title);
            } else {
                toastr.info(message, title);
            }
        } else {
            // استخدام تنبيهات Bootstrap إذا كانت toastr غير متوفرة
            let alertClass = 'alert-info';
            if (type === 'success') alertClass = 'alert-success';
            if (type === 'error') alertClass = 'alert-danger';
            if (type === 'warning') alertClass = 'alert-warning';
            
            // إنشاء عنصر التنبيه
            const alertHtml = `
                <div class="alert ${alertClass} alert-dismissible fade show">
                    <strong>${title}:</strong> ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            `;
            
            // إنشاء حاوية للتنبيهات إذا لم تكن موجودة
            if (!document.getElementById('alertsContainer')) {
                const alertsContainer = document.createElement('div');
                alertsContainer.id = 'alertsContainer';
                alertsContainer.className = 'position-fixed top-0 start-0 p-3';
                alertsContainer.style.zIndex = '1050';
                document.body.appendChild(alertsContainer);
            }
            
            // إضافة التنبيه للحاوية
            const container = document.getElementById('alertsContainer');
            container.innerHTML += alertHtml;
            
            // إزالة التنبيه بعد 3 ثوانٍ
            setTimeout(() => {
                const alerts = container.getElementsByClassName('alert');
                if (alerts.length > 0) {
                    alerts[0].remove();
                }
            }, 3000);
        }
    }
});
</script>
