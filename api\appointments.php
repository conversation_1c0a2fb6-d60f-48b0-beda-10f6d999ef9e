<?php
/**
 * نقطة النهاية الخاصة بالمواعيد
 * تتعامل مع العمليات المتعلقة بإدارة المواعيد في النظام
 */

// تعريف ثابت لمنع الوصول المباشر للملف
define('BASEPATH', true);

// استدعاء ملفات التهيئة
require_once __DIR__ . '/../config/init.php';
header('Content-Type: application/json');


// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    echo json_encode([
        'status' => 'error',
        'message' => 'يجب تسجيل الدخول أولاً'
    ]);
    exit;
}

// الحصول على الإجراء المطلوب
$action = isset($_POST['action']) ? $_POST['action'] : (isset($_GET['action']) ? $_GET['action'] : '');

// التحقق من وجود إجراء
if (empty($action)) {
    echo json_encode([
        'status' => 'error',
        'message' => 'لم يتم تحديد الإجراء المطلوب'
    ]);
    exit;
}

// إنشاء كائنات النماذج
$appointmentModel = new Appointment($db);
$serviceModel = new Service($db);
$customerModel = new Customer($db);
$employeeModel = new Employee($db);
function convertArabicToEnglishNumbers($str) {
    if (!$str) return $str;

    $arabicNumbers = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
    $englishNumbers = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'];

    return str_replace($arabicNumbers, $englishNumbers, $str);
}
// معالجة الطلب بناءً على الإجراء
try {
    switch ($action) {
        // إجراء تصفية المواعيد
        case 'filter':
        case 'list':
            // التحقق من صلاحية عرض المواعيد
            if (!hasPermission('appointments_view')) {
                throw new Exception('ليس لديك صلاحية لعرض المواعيد');
            }

            // تسجيل بيانات الطلب للتصحيح
            file_put_contents(__DIR__ . '/../logs/filter_debug.log',
                date('Y-m-d H:i:s') . " - بداية طلب filter\n" .
                "GET: " . print_r($_GET, true) . "\n" .
                "POST: " . print_r($_POST, true) . "\n" .
                "SESSION branch_id: " . $_SESSION['user_branch_id'] . "\n",
                FILE_APPEND
            );

            // معلمات الفلترة
            $filters = [];

            // فلتر الفرع
            $raw_branch_id = isset($_GET['branch_id']) ? $_GET['branch_id'] : '';
            file_put_contents(__DIR__ . '/../logs/filter_debug.log',
                date('Y-m-d H:i:s') . " - قيمة branch_id الأصلية من GET: '" . $raw_branch_id . "'\n",
                FILE_APPEND
            );

            // تحويل معرف الفرع إلى عدد صحيح إذا كان موجودًا وليس فارغًا
            if ($raw_branch_id !== '') {
                $filters['branch_id'] = intval($raw_branch_id);
            } else {
                file_put_contents(__DIR__ . '/../logs/filter_debug.log',
                    date('Y-m-d H:i:s') . " - تم اختيار عرض كل الفروع\n",
                    FILE_APPEND
                );
            }

            // فلتر الموظف
            if (isset($_GET['employee_id']) && $_GET['employee_id'] !== '') {
                $filters['employee_id'] = intval($_GET['employee_id']);
                file_put_contents(__DIR__ . '/../logs/filter_debug.log',
                    date('Y-m-d H:i:s') . " - تم تعيين فلتر الموظف: " . $filters['employee_id'] . "\n",
                    FILE_APPEND
                );
            }

            // فلتر الخدمة
            if (isset($_GET['service_id']) && $_GET['service_id'] !== '') {
                $filters['service_id'] = intval($_GET['service_id']);
                file_put_contents(__DIR__ . '/../logs/filter_debug.log',
                    date('Y-m-d H:i:s') . " - تم تعيين فلتر الخدمة: " . $filters['service_id'] . "\n",
                    FILE_APPEND
                );
            }

            // فلتر الحالة
            if (isset($_GET['status']) && $_GET['status'] !== '') {
                $filters['status'] = $_GET['status'];
                file_put_contents(__DIR__ . '/../logs/filter_debug.log',
                    date('Y-m-d H:i:s') . " - تم تعيين فلتر الحالة: " . $filters['status'] . "\n",
                    FILE_APPEND
                );
            }

            // معالجة فلتر التاريخ
            if (isset($_GET['date']) && $_GET['date'] !== '') {
                // تحويل الأرقام العربية إلى أرقام إنجليزية
                $arabic_date = $_GET['date'];
                $english_date = convertArabicToEnglishNumbers($arabic_date);

                file_put_contents(__DIR__ . '/../logs/filter_debug.log',
                    date('Y-m-d H:i:s') . " - تاريخ أصلي: " . $arabic_date . "\n" .
                    date('Y-m-d H:i:s') . " - تاريخ بعد التحويل: " . $english_date . "\n",
                    FILE_APPEND
                );

                // إذا تم تحديد تاريخ واحد، استخدمه لكل من تاريخ البداية والنهاية
                $filters['date'] = $english_date;
                $filters['start_date'] = $english_date;
                $filters['end_date'] = $english_date;

                // تطبيق فلتر التاريخ بشكل صريح
                $filters['exact_date'] = true;

                file_put_contents(__DIR__ . '/../logs/filter_debug.log',
                    date('Y-m-d H:i:s') . " - تم تعيين فلتر التاريخ: " . $english_date . "\n",
                    FILE_APPEND
                );
            }

            // تسجيل معلمات الفلترة النهائية للتصحيح
            file_put_contents(__DIR__ . '/../logs/filter_debug.log',
                date('Y-m-d H:i:s') . " - الفلاتر النهائية قبل استدعاء getAppointments:\n" .
                print_r($filters, true) . "\n\n",
                FILE_APPEND
            );

            // استرجاع المواعيد باستخدام الفلاتر
            $appointments = $appointmentModel->getAppointments($filters);

            // تسجيل عدد المواعيد المسترجعة
            file_put_contents(__DIR__ . '/../logs/filter_debug.log',
                date('Y-m-d H:i:s') . " - تم استرجاع " . count($appointments) . " موعد\n\n",
                FILE_APPEND
            );

            // تنسيق البيانات للعرض
            $formattedAppointments = [];
            foreach ($appointments as $appointment) {
                // استخدام الطريقة الموجودة في كائن Appointment لتنسيق البيانات
                $formattedAppointments[] = $appointmentModel->formatAppointmentData($appointment);
            }

            // إرجاع النتائج
            echo json_encode([
                'status' => 'success',
                'appointments' => $formattedAppointments
            ]);
            break;


        // إنشاء موعد جديد
        case 'create':
            // التحقق من صلاحية إنشاء المواعيد
            if (!hasPermission('appointments_create')) {
                throw new Exception('ليس لديك صلاحية لحجز المواعيد');
            }

            // التحقق من أن الطلب عبر POST
            if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
                throw new Exception('الطريقة غير مسموح بها', 405);
            }

            // التحقق من البيانات المطلوبة
            $requiredFields = ['customer_id', 'service_id', 'employee_id', 'date', 'start_time', 'branch_id'];
            foreach ($requiredFields as $field) {
                if (!isset($_POST[$field]) || empty($_POST[$field])) {
                    throw new Exception("الحقل $field مطلوب");
                }
            }

            // جمع بيانات الموعد
            $appointmentData = [
                'customer_id' => intval($_POST['customer_id']),
                'service_id' => intval($_POST['service_id']),
                'employee_id' => intval($_POST['employee_id']),
                'date' => date('Y-m-d', strtotime(sanitizeInput(convertArabicToEnglishNumbers($_POST['date'])))),
                'start_time' => sanitizeInput($_POST['start_time']),
                'status' => 'booked',
                'notes' => isset($_POST['notes']) ? sanitizeInput($_POST['notes']) : null,
                'branch_id' => intval($_POST['branch_id'])
            ];

            // إنشاء الموعد
            $appointmentId = $appointmentModel->createAppointment($appointmentData);

            echo json_encode([
                'status' => 'success',
                'message' => 'تم حجز الموعد بنجاح',
                'appointment_id' => $appointmentId
            ]);
            break;

        // تحديث موعد
        case 'update':
            // التحقق من صلاحية تعديل المواعيد
            if (!hasPermission('appointments_edit')) {
                throw new Exception('ليس لديك صلاحية لتعديل المواعيد');
            }

            // التحقق من وجود معرف الموعد
            if (!isset($_POST['id']) || empty($_POST['id'])) {
                throw new Exception('معرف الموعد مطلوب');
            }

            $appointmentId = intval($_POST['id']);

            // الحصول على بيانات الموعد الحالية
            $existingAppointment = $appointmentModel->getAppointmentById($appointmentId);
            if (!$existingAppointment) {
                throw new Exception('الموعد غير موجود');
            }

            // إذا كان التحديث فقط لحالة الموعد
            if (isset($_POST['status']) && count($_POST) == 3) { // id, action, status
                $result = $appointmentModel->updateAppointmentStatus($appointmentId, $_POST['status']);

                echo json_encode([
                    'status' => 'success',
                    'message' => 'تم تحديث حالة الموعد بنجاح'
                ]);
                break;
            }

            // جمع بيانات الموعد
            $appointmentData = [
                'customer_id' => isset($_POST['customer_id']) ? intval($_POST['customer_id']) : $existingAppointment['customer_id'],
                'service_id' => isset($_POST['service_id']) ? intval($_POST['service_id']) : $existingAppointment['service_id'],
                'employee_id' => isset($_POST['employee_id']) ? intval($_POST['employee_id']) : $existingAppointment['employee_id'],
                'date' => isset($_POST['date']) ? sanitizeInput($_POST['date']) : $existingAppointment['date'],
                'start_time' => isset($_POST['start_time']) ? sanitizeInput($_POST['start_time']) : $existingAppointment['start_time'],
                'status' => isset($_POST['status']) ? sanitizeInput($_POST['status']) : $existingAppointment['status'],
                'notes' => isset($_POST['notes']) ? sanitizeInput($_POST['notes']) : ($existingAppointment['notes'] ?? ''),
                'branch_id' => isset($_POST['branch_id']) ? intval($_POST['branch_id']) : $existingAppointment['branch_id']
            ];

            // تحديث الموعد
            $result = $appointmentModel->updateAppointment($appointmentId, $appointmentData);

            echo json_encode([
                'status' => 'success',
                'message' => 'تم تحديث الموعد بنجاح'
            ]);
            break;

        // إلغاء موعد
        case 'cancel':
            // التحقق من صلاحية إلغاء المواعيد
            if (!hasPermission('appointments_edit')) {
                throw new Exception('ليس لديك صلاحية لإلغاء المواعيد');
            }

            // التحقق من وجود معرف الموعد
            if (!isset($_POST['id']) || empty($_POST['id'])) {
                throw new Exception('معرف الموعد مطلوب');
            }

            // إلغاء الموعد
            $appointmentModel->cancelAppointment(intval($_POST['id']));

            echo json_encode([
                'status' => 'success',
                'message' => 'تم إلغاء الموعد بنجاح'
            ]);
            break;

        // استرجاع قائمة المواعيد
        case 'list':
            // التحقق من صلاحية عرض المواعيد
            if (!hasPermission('appointments_view')) {
                throw new Exception('ليس لديك صلاحية لعرض المواعيد');
            }

            // معلمات الفلترة
            $filters = [];

            // فلتر الفرع
            $raw_branch_id = input('branch_id');

            // تحويل معرف الفرع إلى عدد صحيح إذا كان موجودًا وليس فارغًا
            if (isset($raw_branch_id) && $raw_branch_id !== '') {
                $filters['branch_id'] = intval($raw_branch_id);
            } else if ($_SESSION['user_role'] != ROLE_ADMIN && $_SESSION['user_role'] != ROLE_MANAGER) {
                // إذا لم يكن المستخدم مدير أو مشرف، استخدم فرع المستخدم
                $filters['branch_id'] = intval($_SESSION['user_branch_id']);
            }

            // فلتر الموظف
            if (input('employee_id') && input('employee_id') !== '') {
                $filters['employee_id'] = intval(input('employee_id'));
            }

            // فلتر الخدمة
            if (input('service_id') && input('service_id') !== '') {
                $filters['service_id'] = intval(input('service_id'));
            }

            // فلتر الحالة
            if (input('status') && input('status') !== '') {
                $filters['status'] = input('status');
            }

            // معالجة فلتر التاريخ
            if (!empty($_GET['date'])) {
                // تحويل الأرقام العربية إلى أرقام إنجليزية
                $date = convertArabicToEnglishNumbers($_GET['date']);

                // إذا تم تحديد تاريخ واحد، استخدمه لكل من تاريخ البداية والنهاية
                $filters['date'] = $date;
                $filters['start_date'] = $date;
                $filters['end_date'] = $date;

                // تطبيق فلتر التاريخ بشكل صريح
                $filters['exact_date'] = true;
            }

            // تسجيل معلمات الفلترة للتصحيح
            file_put_contents(__DIR__ . '/debug_filter.log',
                date('Y-m-d H:i:s') . " - معلمات الفلترة: " .
                print_r($filters, true) . "\n",
                FILE_APPEND
            );

            // استرجاع المواعيد
            $appointments = $appointmentModel->getAppointments($filters);
            $totalAppointments = $appointmentModel->getAppointmentsCount($filters);

            echo json_encode([
                'status' => 'success',
                'appointments' => $appointments,
                'total_count' => $totalAppointments
            ]);
            break;

        // عرض تفاصيل موعد محدد
        case 'view':
            // التحقق من صلاحية عرض المواعيد
            if (!hasPermission('appointments_view')) {
                throw new Exception('ليس لديك صلاحية لعرض المواعيد');
            }

            // التحقق من وجود معرف الموعد
            $appointmentId = isset($_GET['id']) ? intval($_GET['id']) : (isset($_POST['id']) ? intval($_POST['id']) : null);
            if (!$appointmentId) {
                throw new Exception('معرف الموعد مطلوب', 400);
            }

            // استرجاع بيانات الموعد
            $appointment = $appointmentModel->getAppointmentById($appointmentId);

            if (!$appointment) {
                throw new Exception('لم يتم العثور على الموعد', 404);
            }

            echo json_encode([
                'status' => 'success',
                'appointment' => $appointment
            ]);
            break;



        // توليد تقرير المواعيد
        case 'report':
            // التحقق من صلاحية عرض التقارير
            if (!hasPermission('reports_appointments')) {
                throw new Exception('ليس لديك صلاحية لعرض التقارير');
            }

            // معلمات التقرير
            $filters = [
                'branch_id' => input('branch_id') ?? $_SESSION['user_branch_id'],
                'start_date' => input('start_date'),
                'end_date' => input('end_date'),
                'employee_id' => input('employee_id'),
                'service_id' => input('service_id')
            ];

            // توليد التقرير
            $report = $appointmentModel->generateAppointmentsReport($filters);

            echo json_encode([
                'status' => 'success',
                'report' => $report
            ]);
            break;

        // مقارنة المواعيد بين فترتين
        case 'compare_periods':
            // التحقق من صلاحية عرض التقارير
            if (!hasPermission('reports_appointments')) {
                throw new Exception('ليس لديك صلاحية لعرض التقارير');
            }

            // التحقق من وجود البيانات المطلوبة
            if (!isset($_GET['period1_start']) || !isset($_GET['period1_end']) ||
                !isset($_GET['period2_start']) || !isset($_GET['period2_end'])) {
                throw new Exception('يجب تحديد الفترات للمقارنة');
            }

            $period1 = [
                'start_date' => trim($_GET['period1_start']),
                'end_date' => trim($_GET['period1_end'])
            ];

            $period2 = [
                'start_date' => trim($_GET['period2_start']),
                'end_date' => trim($_GET['period2_end'])
            ];

            $branchId = isset($_GET['branch_id']) ? intval($_GET['branch_id']) : null;

            // مقارنة الفترات
            $comparisonReport = $appointmentModel->compareAppointmentsPeriods($period1, $period2, $branchId);

            echo json_encode([
                'status' => 'success',
                'report' => $comparisonReport
            ]);
            break;

        // الحصول على رمز البلد من الفرع للموعد
        case 'get_branch_country_code':
            // التحقق من وجود معرف الموعد
            if (!isset($_GET['id']) || empty($_GET['id'])) {
                throw new Exception('معرف الموعد مطلوب');
            }

            $appointmentId = intval($_GET['id']);

            // الحصول على بيانات الموعد
            $appointment = $appointmentModel->getAppointmentById($appointmentId);

            if (!$appointment) {
                throw new Exception('لم يتم العثور على الموعد');
            }

            // الحصول على معرف الفرع
            $branchId = $appointment['branch_id'] ?? $_SESSION['user_branch_id'];

            // إنشاء كائن الفرع
            $branchModel = new Branch($db);

            // الحصول على بيانات الفرع
            $branch = $branchModel->getBranchById($branchId);

            if (!$branch) {
                // إذا لم يتم العثور على الفرع، استخدم القيمة الافتراضية
                error_log('لم يتم العثور على الفرع للموعد رقم ' . $appointmentId . '. استخدام القيمة الافتراضية +20');
                $branch = ['country_code' => '+20'];
            }

            // إرجاع رمز البلد
            echo json_encode([
                'status' => 'success',
                'country_code' => $branch['country_code'] ?? '+20'
            ]);
            break;

        // استرجاع إحصائيات المواعيد
        case 'get_stats':
            // التحقق من صلاحية عرض المواعيد
            if (!hasPermission('appointments_view')) {
                throw new Exception('ليس لديك صلاحية لعرض إحصائيات المواعيد');
            }

            // تحديد معلمات الفلترة
            $filters = [];

            // فلتر الفرع
            $raw_branch_id = isset($_GET['branch_id']) ? $_GET['branch_id'] : '';

            // تحويل معرف الفرع إلى عدد صحيح إذا كان موجودًا وليس فارغًا
            if ($raw_branch_id !== '') {
                $filters['branch_id'] = intval($raw_branch_id);
            } else if ($_SESSION['user_role'] != ROLE_ADMIN && $_SESSION['user_role'] != ROLE_MANAGER) {
                // إذا لم يكن المستخدم مدير أو مشرف، استخدم فرع المستخدم
                $filters['branch_id'] = intval($_SESSION['user_branch_id']);
            }

            // فلتر الموظف
            if (isset($_GET['employee_id']) && $_GET['employee_id'] !== '') {
                $filters['employee_id'] = intval($_GET['employee_id']);
                file_put_contents(__DIR__ . '/../logs/filter_debug.log',
                    date('Y-m-d H:i:s') . " - تم تعيين فلتر الموظف: " . $filters['employee_id'] . "\n",
                    FILE_APPEND
                );
            }

            // تحديد التواريخ
            $userDate = isset($_GET['date']) && !empty($_GET['date']) ? convertArabicToEnglishNumbers($_GET['date']) : null;

            // استخدام التاريخ المحدد من قبل المستخدم إذا كان موجودًا، وإلا استخدام التاريخ الحالي
            $today = $userDate ?: date('Y-m-d');

            // حساب باقي التواريخ بناءً على التاريخ المحدد
            $tomorrow = date('Y-m-d', strtotime($today . ' +1 day'));
            $weekStart = $today;
            $weekEnd = date('Y-m-d', strtotime($today . ' +7 days'));
            $monthStart = date('Y-m-01', strtotime($today));
            $monthEnd = date('Y-m-t', strtotime($today));

            // استرجاع إحصائيات المواعيد
            $todayFilters = $filters;
            $todayFilters['date'] = convertArabicToEnglishNumbers($today);

            $tomorrowFilters = $filters;
            $tomorrowFilters['date'] = $tomorrow;

            $weekFilters = $filters;
            $weekFilters['start_date'] = $weekStart;
            $weekFilters['end_date'] = $weekEnd;

            $monthFilters = $filters;
            $monthFilters['start_date'] = $monthStart;
            $monthFilters['end_date'] = $monthEnd;

            $stats = [
                'today' => $appointmentModel->getAppointmentsCount($todayFilters),
                'tomorrow' => $appointmentModel->getAppointmentsCount($tomorrowFilters),
                'week' => $appointmentModel->getAppointmentsCount($weekFilters),
                'month' => $appointmentModel->getAppointmentsCount($monthFilters)
            ];

            echo json_encode([
                'status' => 'success',
                'message' => 'تم استرجاع الإحصائيات بنجاح',
                'stats' => $stats
            ]);
            break;
            case 'check_availability':
                $appointmentModel = Appointment::find($appointmentId);
                if (empty($appointmentModel)) {
                    throw new Exception('الموعد غير موجود');
                }
                $appointment = $appointmentModel->appointment;
                $available = $appointmentModel->checkAvailability($appointment);
                echo json_encode([
                    'status' => 'success',
                    'available' => $available
                ]);
                break;

        // استرجاع مواعيد اليوم
        case 'get_today_appointments':
            // التحقق من صلاحية عرض المواعيد
            if (!hasPermission('appointments_view')) {
                throw new Exception('ليس لديك صلاحية لعرض المواعيد');
            }

            // الحصول على معرف الفرع إذا تم تحديده
            $branchId = isset($_GET['branch_id']) && !empty($_GET['branch_id'])
                ? intval($_GET['branch_id'])
                : $_SESSION['user_branch_id'];

            // تحديد تاريخ اليوم
            $today = date('Y-m-d');

            // استرجاع مواعيد اليوم
            $filters = [
                'branch_id' => $branchId,
                'date' => $today,
                'exact_date' => true
            ];

            // تسجيل للتصحيح
            error_log('استرجاع مواعيد اليوم: ' . json_encode($filters));

            // استرجاع المواعيد
            $appointments = $appointmentModel->getAppointmentsByDate($today, [
                'branch_id' => $branchId
            ]);

            // تنسيق البيانات للعرض
            $formattedAppointments = [];
            foreach ($appointments as $appointment) {
                // استخدام الطريقة الموجودة في كائن Appointment لتنسيق البيانات
                $formattedAppointments[] = $appointmentModel->formatAppointmentData($appointment);
            }

            echo json_encode([
                'status' => 'success',
                'appointments' => $formattedAppointments,
                'count' => count($formattedAppointments),
                'date' => $today
            ]);
            break;

        // استرجاع الأوقات المتاحة
        case 'get_available_times':
            // التحقق من صلاحية عرض المواعيد
            if (!hasPermission('appointments_view')) {
                throw new Exception('ليس لديك صلاحية لعرض المواعيد');
            }

            // التحقق من البيانات المطلوبة
            if (empty($_GET['service_id']) || empty($_GET['date']) || empty($_GET['branch_id'])) {
                throw new Exception('البيانات المطلوبة غير مكتملة');
            }

            // الحصول على البيانات
            $serviceId = intval($_GET['service_id']);
            $employeeId = !empty($_GET['employee_id']) ? intval($_GET['employee_id']) : 0;
            $date = convertArabicToEnglishNumbers($_GET['date']); // تحويل التاريخ إلى تنسيق يوم-شهر-سنة إنجليز
            $branchId = intval($_GET['branch_id']);

            // التحقق من صحة التاريخ
            if (!validateDate($date)) {
                throw new Exception('التاريخ غير صالح');
            }

            // استرجاع الأوقات المتاحة
            $availableTimes = $appointmentModel->getAvailableTimes($serviceId, $date, $branchId, $employeeId);

            echo json_encode([
                'status' => 'success',
                'available_times' => $availableTimes,
                'count' => count($availableTimes),
                'date' => $date
            ]);
            break;

        // الافتراضي: إجراء غير معروف
        default:
            throw new Exception('إجراء غير معروف');
    }
} catch (Exception $e) {
    // معالجة الأخطاء
    http_response_code(400);

    // تسجيل الخطأ
    error_log('خطأ في نقطة النهاية الخاصة بالمواعيد: ' . $e->getMessage());

    // إرجاع رسالة الخطأ للعميل
    echo json_encode([
        'status' => 'error',
        'message' => $e->getMessage()
    ]);
}
