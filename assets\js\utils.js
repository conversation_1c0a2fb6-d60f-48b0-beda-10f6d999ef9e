/**
 * وظائف مساعدة مشتركة للتطبيق
 */

/**
 * عرض رسالة تنبيه موحدة في جميع أنحاء التطبيق
 * @param {string} message نص الرسالة
 * @param {string} type نوع الرسالة (success, error, warning, info)
 * @param {string} title عنوان الرسالة (اختياري)
 * @param {number} duration مدة ظهور التنبيه بالمللي ثانية (اختياري، الافتراضي 5000)
 */
function showAlert(message, type = 'info', title = '', duration = 5000) {
    // تحويل نوع الخطأ إلى نوع التنبيه المناسب
    let alertType = type;
    if (type === 'error') alertType = 'danger';

    // التحقق من وجود مكتبة toastr
    if (typeof toastr !== 'undefined') {
        // تهيئة خيارات toastr
        toastr.options = {
            closeButton: true,
            progressBar: true,
            positionClass: 'toast-top-left',
            timeOut: duration,
            extendedTimeOut: 1000,
            showEasing: 'swing',
            hideEasing: 'linear',
            showMethod: 'fadeIn',
            hideMethod: 'fadeOut',
            rtl: true
        };

        // عرض التنبيه المناسب حسب النوع
        switch (type) {
            case 'success':
                toastr.success(message, title);
                break;
            case 'error':
            case 'danger':
                toastr.error(message, title);
                break;
            case 'warning':
                toastr.warning(message, title);
                break;
            default:
                toastr.info(message, title);
        }
    } else {
        // استخدام تنبيه بديل إذا كانت مكتبة toastr غير متوفرة
        // إنشاء حاوية للتنبيهات إذا لم تكن موجودة
        if (!$('#alertContainer').length) {
            $('<div id="alertContainer" class="alert-container position-fixed top-0 end-0 p-3" style="z-index: 1050;"></div>').appendTo('body');
        }

        // إنشاء عنصر التنبيه
        const alertId = 'alert-' + new Date().getTime();
        let alertHtml = `
            <div id="${alertId}" class="alert alert-${alertType} alert-dismissible fade show" role="alert">
                ${title ? '<strong>' + title + '</strong> ' : ''}${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        `;

        // إضافة التنبيه إلى الحاوية
        $('#alertContainer').append(alertHtml);

        // إخفاء التنبيه تلقائيًا بعد المدة المحددة
        setTimeout(function() {
            $('#' + alertId).alert('close');
        }, duration);
    }
}

/**
 * عرض رسالة توست (للتوافق مع الكود القديم)
 * @param {string} title عنوان الرسالة
 * @param {string} message نص الرسالة
 * @param {string} type نوع الرسالة (success, error, warning, info)
 */
function showToast(title, message, type) {
    // استدعاء الدالة الجديدة للتوافق مع الكود القديم
    showAlert(message, type, title);
}

/**
 * إظهار مؤشر التحميل
 * @param {string} selector محدد زر الإرسال
 * @param {string} loadingText نص التحميل
 */
function showLoading(selector = 'button[type="submit"]', loadingText = 'جاري الحفظ...') {
    $(selector)
        .prop('disabled', true)
        .html('<span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span> ' + loadingText);
}

/**
 * إخفاء مؤشر التحميل
 * @param {string} selector محدد زر الإرسال
 * @param {string} originalText النص الأصلي
 */
function hideLoading(selector = 'button[type="submit"]', originalText = '<i class="fas fa-save me-1"></i> حفظ') {
    $(selector)
        .prop('disabled', false)
        .html(originalText);
}