/**
 * سكريبت تنفيذ المهام في الخلفية
 * يقوم بتشغيل مهام محددة في الخلفية أثناء استخدام النظام
 */

// استخدام متغير BASE_URL المعرف مسبقًا في ملف header.php

// تكوين المهام
const backgroundTasks = {
    // مهمة إرسال تذكيرات المواعيد
    appointmentReminders: {
        enabled: true,
        interval: 15 * 60 * 1000, // 15 دقيقة
        url: BASE_URL + 'api/whatsapp.php',
        data: { action: 'send_scheduled_reminders' },
        lastRun: null
    }
};

// دالة تنفيذ المهمة
function runTask(taskName) {
    const task = backgroundTasks[taskName];

    if (!task || !task.enabled) {
        return;
    }

    // تحديث وقت آخر تنفيذ
    task.lastRun = new Date();

    // تنفيذ المهمة
    $.ajax({
        url: task.url,
        type: 'POST',
        data: task.data,
        dataType: 'text', // استخدام نص بدلاً من JSON لتجنب أخطاء التحليل
        success: function(responseText) {
            try {
                // محاولة تحليل النص كـ JSON
                const response = JSON.parse(responseText);
                console.log(`تم تنفيذ المهمة ${taskName} بنجاح:`, response);
            } catch (jsonError) {
                // إذا فشل التحليل، سجل الخطأ والنص المستلم
                console.error(`فشل تحليل استجابة المهمة ${taskName} كـ JSON:`, jsonError);
                console.log('النص المستلم:', responseText);
            }
        },
        error: function(xhr, status, error) {
            console.error(`فشل تنفيذ المهمة ${taskName}:`, error);
            if (xhr.responseText) {
                console.log('النص المستلم:', xhr.responseText);
            }
        }
    });
}

// دالة بدء تشغيل المهام
function startBackgroundTasks() {
    // تنفيذ كل مهمة وفقًا للفاصل الزمني المحدد
    Object.keys(backgroundTasks).forEach(taskName => {
        const task = backgroundTasks[taskName];

        if (task.enabled) {
            // تنفيذ المهمة مرة واحدة عند بدء التشغيل
            setTimeout(() => {
                runTask(taskName);
            }, 5000); // تأخير 5 ثوانٍ قبل التنفيذ الأول

            // إعداد تنفيذ المهمة بشكل دوري
            setInterval(() => {
                runTask(taskName);
            }, task.interval);
        }
    });
}

// بدء تشغيل المهام عند تحميل الصفحة
$(document).ready(function() {
    // التحقق من تفعيل المهام في الخلفية
    $.ajax({
        url: BASE_URL + 'api/settings.php',
        type: 'GET',
        data: { action: 'get_background_tasks_settings' },
        dataType: 'text', // استخدام نص بدلاً من JSON لتجنب أخطاء التحليل
        timeout: 10000, // زيادة المهلة الزمنية إلى 10 ثواني
        // تجاهل أخطاء SSL
        xhrFields: {
            withCredentials: true
        },
        beforeSend: function(xhr) {
            // لا يمكن تعطيل التحقق من SSL مباشرة في jQuery
            // لكن يمكننا إضافة رأس للتعامل مع المشكلة
            xhr.setRequestHeader('X-Ignore-SSL', 'true');
        },
        success: function(response) {
            console.log('تم استلام استجابة من الخادم:', response);

            if (response.status === 'success' && response.settings) {
                // تحديث إعدادات المهام
                if (response.settings.enable_background_tasks === '1') {
                    // تحديث إعدادات مهمة تذكيرات المواعيد
                    if (backgroundTasks.appointmentReminders) {
                        var isWhatsAppEnabled = response.settings.whatsapp_enabled === '1';
                        var isNotificationEnabled = response.settings.notification_enable_whatsapp === '1';
                        var isReminderEnabled = response.settings.notification_appointment_reminder === '1';

                        console.log('حالة WhatsApp:', isWhatsAppEnabled);
                        console.log('حالة الإشعارات:', isNotificationEnabled);
                        console.log('حالة التذكيرات:', isReminderEnabled);

                        backgroundTasks.appointmentReminders.enabled = isReminderEnabled && isNotificationEnabled && isWhatsAppEnabled;

                        console.log('حالة مهمة تذكيرات المواعيد:', backgroundTasks.appointmentReminders.enabled);
                    }

                    // بدء تشغيل المهام
                    startBackgroundTasks();
                    console.log('تم بدء تشغيل المهام في الخلفية');
                } else {
                    console.log('المهام في الخلفية غير مفعلة في إعدادات النظام');
                }
            } else if (response.status === 'error') {
                console.error('خطأ في الحصول على إعدادات المهام:', response.message);
            } else {
                console.error('استجابة غير متوقعة من الخادم:', response);
            }
        },
        error: function(xhr, status, error) {
            console.error('فشل الحصول على إعدادات المهام في الخلفية');
            console.error('الحالة:', status);
            console.error('الخطأ:', error);
            console.error('الاستجابة:', xhr.responseText);

            // محاولة تشغيل المهام بالإعدادات الافتراضية
            console.log('محاولة تشغيل المهام بالإعدادات الافتراضية');

            // تعطيل مهمة تذكيرات المواعيد في حالة الخطأ
            if (backgroundTasks.appointmentReminders) {
                backgroundTasks.appointmentReminders.enabled = false;
            }
        }
    });
});
