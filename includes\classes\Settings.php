<?php
/**
 * فئة إدارة إعدادات النظام
 * تتيح إضافة وتعديل وعرض إعدادات النظام المخزنة في قاعدة البيانات
 */

// منع الوصول المباشر للملف
if (!defined('BASEPATH')) {
    exit('لا يمكن الوصول المباشر لهذا الملف');
}

class Settings {
    /**
     * كائن الاتصال بقاعدة البيانات
     * @var Database
     */
    private $db;

    /**
     * مخزن مؤقت للإعدادات
     * @var array
     */
    private $cache = [];

    /**
     * مسار ملف السجل
     * @var string
     */
    private $logFile;

    /**
     * نشئ: يأخذ كائن الاتصال بقاعدة البيانات
     *
     * @param Database $db كائن الاتصال بقاعدة البيانات
     */
    public function __construct($db) {
        $this->db = $db;
        // تحديد مسار ملف السجل
        $this->logFile = dirname(dirname(__DIR__)) . '/logs/settings.log';

        // التأكد من وجود مجلد السجلات
        $logDir = dirname($this->logFile);
        if (!is_dir($logDir)) {
            mkdir($logDir, 0777, true);
        }
    }

    /**
     * تسجيل رسالة في ملف السجل
     *
     * @param string $message الرسالة المراد تسجيلها
     * @return void
     */
    private function log($message) {
        $timestamp = date('Y-m-d H:i:s');
        $logMessage = "[{$timestamp}] {$message}" . PHP_EOL;
        file_put_contents($this->logFile, $logMessage, FILE_APPEND);
    }

    /**
     * الحصول على قيمة إعداد معين
     *
     * @param string $key مفتاح الإعداد
     * @param mixed $default القيمة الافتراضية في حالة عدم وجود الإعداد
     * @return mixed قيمة الإعداد أو القيمة الافتراضية
     */
    public function get($key, $default = null) {
        // التحقق من وجود القيمة في المخزن المؤقت أولاً
        if (isset($this->cache[$key])) {
            $this->log("استرجاع الإعداد من المخزن المؤقت: {$key}");
            return $this->cache[$key];
        }

        try {
            // Escapar el valor de la clave para evitar inyección SQL
            $escapedKey = str_replace("'", "\'", $key);

            // Usar consulta directa en lugar de consulta preparada
            $query = "SELECT setting_value FROM settings WHERE setting_key = '{$escapedKey}'";
            $result = $this->db->query($query);

            if (!$result) {
                throw new Exception("Failed to execute query: {$query}");
            }

            $row = $result->fetch(PDO::FETCH_ASSOC);

            if ($row) {
                // تخزين القيمة في المخزن المؤقت
                $this->cache[$key] = $row['setting_value'];
                $this->log("استرجاع الإعداد من قاعدة البيانات: {$key} = {$row['setting_value']}");
                return $row['setting_value'];
            }
        } catch (Exception $e) {
            $this->log("خطأ في استرجاع الإعداد {$key}: " . $e->getMessage());
        }

        $this->log("الإعداد {$key} غير موجود، استخدام القيمة الافتراضية: " . var_export($default, true));
        return $default;
    }

    /**
     * حفظ أو تحديث قيمة إعداد
     *
     * @param string $key مفتاح الإعداد
     * @param mixed $value قيمة الإعداد
     * @param string $description وصف الإعداد (اختياري)
     * @return bool نجاح أو فشل العملية
     */
    public function set($key, $value, $description = null) {
        try {
            // التحقق من وجود الإعداد مسبق
            $exists = $this->get($key, null) !== null;

            // Escapar valores para evitar inyección SQL
            $escapedKey = str_replace("'", "\'", $key);
            $escapedValue = str_replace("'", "\'", $value);
            $escapedDescription = $description !== null ? str_replace("'", "\'", $description) : '';

            if ($exists) {
                // تحديث الإعداد الموجود
                if ($description !== null) {
                    $query = "UPDATE settings SET setting_value = '{$escapedValue}', description = '{$escapedDescription}' WHERE setting_key = '{$escapedKey}'";
                } else {
                    $query = "UPDATE settings SET setting_value = '{$escapedValue}' WHERE setting_key = '{$escapedKey}'";
                }

                $this->log("تحديث الإعداد: {$key} = {$value}");
            } else {
                // إضافة إعداد جديد
                $query = "INSERT INTO settings (setting_key, setting_value, description) VALUES ('{$escapedKey}', '{$escapedValue}', '{$escapedDescription}')";

                $this->log("إضافة إعداد جديد: {$key} = {$value}, الوصف: {$description}");
            }

            $result = $this->db->query($query);

            if (!$result) {
                throw new Exception("Failed to execute query: {$query}");
            }

            // تحديث المخزن المؤقت
            $this->cache[$key] = $value;

            return true;
        } catch (Exception $e) {
            $this->log("خطأ في حفظ الإعداد {$key}: " . $e->getMessage());
            return false;
        }
    }

    /**
     * حذف إعداد معين
     *
     * @param string $key مفتاح الإعداد
     * @return bool نجاح أو فشل العملية
     */
    public function delete($key) {
        try {
            // Escapar el valor de la clave para evitar inyección SQL
            $escapedKey = str_replace("'", "\'", $key);

            $query = "DELETE FROM settings WHERE setting_key = '{$escapedKey}'";
            $result = $this->db->query($query);

            if (!$result) {
                throw new Exception("Failed to execute query: {$query}");
            }

            // حذف القيمة من المخزن المؤقت إذا كانت موجودة
            if (isset($this->cache[$key])) {
                unset($this->cache[$key]);
            }

            $this->log("تم حذف الإعداد: {$key}");
            return true;
        } catch (Exception $e) {
            $this->log("خطأ في حذف الإعداد {$key}: " . $e->getMessage());
            return false;
        }
    }

    /**
     * الحصول على جميع الإعدادات
     *
     * @return array مصفوفة من الإعدادات بتنسيق ['key' => 'value']
     */
    public function getAllSettings() {
        try {
            // إذا كانت المخزن المؤقت فارغًا، قم بتحميل جميع الإعدادات
            if (empty($this->cache)) {
                $query = "SELECT setting_key, setting_value FROM settings";
                $result = $this->db->query($query)->fetchAll(PDO::FETCH_KEY_PAIR);

                if ($result) {
                    $this->cache = $result;
                    error_log("Loaded " . count($result) . " settings into cache");
                } else {
                    error_log("No settings found in database");
                    return [];
                }
            }

            return $this->cache;
        } catch (Exception $e) {
            error_log('خطأ في استرجاع جميع الإعدادات: ' . $e->getMessage());
            return [];
        }
    }



    /**
     * تنظيف المخزن المؤقت للإعدادات
     *
     * @return void
     */
    public function clearCache() {
        $this->cache = [];
    }

    /**
     * تخزين مجموعة من الإعدادات دفعة واحدة
     *
     * @param array $settings مصفوفة من الإعدادات بصيغة ['key' => 'value']
     * @return bool نجاح أو فشل العملية
     */
    public function bulkSet($settings) {
        if (!is_array($settings) || empty($settings)) {
            return false;
        }

        try {
            $this->db->beginTransaction();

            foreach ($settings as $key => $value) {
                if (is_array($value) && isset($value['value'])) {
                    $description = isset($value['description']) ? $value['description'] : null;
                    $this->set($key, $value['value'], $description);
                } else {
                    $this->set($key, $value);
                }
            }

            $this->db->commit();
            return true;
        } catch (Exception $e) {
            $this->db->rollBack();
            error_log('خطأ في حفظ مجموعة الإعدادات: ' . getErrorMessage($e));
            return false;
        }
    }

    /**
     * استيراد إعدادات افتراضية من ملف
     *
     * @param string $filePath مسار ملف الإعدادات (JSON)
     * @return bool نجاح أو فشل العملية
     */
    public function importFromFile($filePath) {
        if (!file_exists($filePath)) {
            return false;
        }

        try {
            $jsonData = file_get_contents($filePath);
            $settings = json_decode($jsonData, true);

            if (json_last_error() !== JSON_ERROR_NONE) {
                throw new Exception('تنسيق ملف JSON غير صالح: ' . json_last_error_msg());
            }

            return $this->bulkSet($settings);
        } catch (Exception $e) {
            error_log('خطأ في استيراد الإعدادات: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * الحصول على جميع الإعدادات
     *
     * @return array مصفوفة من الإعدادات
     */
    public function getAll() {
        try {
            $query = "SELECT * FROM settings ORDER BY setting_key ASC";
            $result = $this->db->query($query);

            if (!$result) {
                throw new Exception("Failed to execute query: {$query}");
            }

            $settings = [];
            $rows = $result->fetchAll(PDO::FETCH_ASSOC);

            foreach ($rows as $row) {
                $key = $row['setting_key'];
                $settings[$key] = [
                    'value' => $row['setting_value'],
                    'description' => $row['description']
                ];

                // تحديث المخزن المؤقت
                $this->cache[$key] = $row['setting_value'];
            }

            return $settings;
        } catch (Exception $e) {
            $this->log('خطأ في استرجاع جميع الإعدادات: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * تصدير الإعدادات الحالية إلى ملف
     *
     * @param string $filePath مسار ملف الإعدادات (JSON)
     * @return bool نجاح أو فشل العملية
     */
    public function exportToFile($filePath) {
        try {
            $settings = $this->getAll();
            $jsonData = json_encode($settings, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);

            if (file_put_contents($filePath, $jsonData) === false) {
                throw new Exception("Failed to write settings to file: {$filePath}");
            }

            $this->log("Settings exported successfully to: {$filePath}");
            return true;
        } catch (Exception $e) {
            $this->log('خطأ في تصدير الإعدادات: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * التحقق من وجود إعداد معين
     *
     * @param string $key مفتاح الإعداد
     * @return bool هل الإعداد موجود
     */
    public function exists($key) {
        return $this->get($key, null) !== null;
    }

    /**
     * تهيئة الإعدادات الافتراضية للنظام
     * تستخدم عند تثبيت النظام لأول مرة
     *
     * @return bool نجاح أو فشل العملية
     */
    public function initDefaultSettings() {
        $this->log("بدء تهيئة الإعدادات الافتراضية");

        // الإعدادات الافتراضية للنظام
        $defaultSettings = [
            'system_default_language' => [
                'value' => 'ar',
                'description' => 'اللغة الافتراضية للنظام'
            ],
            'system_timezone' => [
                'value' => 'Asia/Riyadh',
                'description' => 'المنطقة الزمنية'
            ],
            'system_currency' => [
                'value' => 'ريال سعودي',
                'description' => 'العملة الافتراضية'
            ],
            'system_currency_symbol' => [
                'value' => 'ر.س',
                'description' => 'رمز العملة'
            ],
            'system_backup_interval' => [
                'value' => '7',
                'description' => 'الفترة بين النسخ الاحتياطية (بالأيام)'
            ],
            'invoice_company_name' => [
                'value' => '',
                'description' => 'اسم الشركة في الفاتورة'
            ],
            'invoice_header_text' => [
                'value' => '',
                'description' => 'نص رأس الفاتورة'
            ],
            'invoice_footer_text' => [
                'value' => '',
                'description' => 'نص تذييل الفاتورة'
            ]
        ];

        try {
            $this->db->beginTransaction();

            // تحقق من هيكل جدول الإعدادات
            $tableInfo = $this->db->query("DESCRIBE settings")->fetchAll(PDO::FETCH_COLUMN);
            $this->log("Table structure: " . implode(", ", $tableInfo));

            $insertedCount = 0;

            foreach ($defaultSettings as $key => $setting) {
                try {
                    $value = $setting['value'];
                    $description = $setting['description'];

                    // Usar consulta directa con valores escapados manualmente
                    $escapedKey = str_replace("'", "\'", $key);
                    $escapedValue = str_replace("'", "\'", $value);
                    $escapedDescription = str_replace("'", "\'", $description);

                    $insertQuery = "INSERT INTO settings (setting_key, setting_value, description)
                                   VALUES ('{$escapedKey}', '{$escapedValue}', '{$escapedDescription}')";

                    $result = $this->db->query($insertQuery);

                    if ($result) {
                        $this->cache[$key] = $value;
                        $insertedCount++;
                        $this->log("Inserted default setting: {$key} with value: {$value}");
                    } else {
                        $this->log("Failed to insert setting {$key}");
                    }
                } catch (Exception $e) {
                    $this->log("Error inserting setting {$key}: " . $e->getMessage());
                }
            }

            $this->db->commit();
            $this->log("تم تهيئة {$insertedCount} إعداد افتراضي بنجاح");
            return true;
        } catch (Exception $e) {
            $this->db->rollBack();
            $this->log("خطأ في تهيئة الإعدادات الافتراضية: " . $e->getMessage());
            return false;
        }
    }

    /**
     * التحقق من وجود الإعدادات وتهيئتها إذا كانت غير موجودة
     *
     * @return bool نجاح أو فشل العملية
     */
    public function ensureSettingsExist() {
        try {
            // التحقق من وجود أي إعدادات
            $query = "SELECT COUNT(*) FROM settings";
            $count = (int) $this->db->query($query)->fetchColumn();

            // إذا كان الجدول فارغًا، قم بتهيئة الإعدادات الافتراضية
            if ($count === 0) {
                $this->log("Settings table is empty. Initializing default settings.");
                return $this->initDefaultSettings();
            }

            return true;
        } catch (Exception $e) {
            $this->log('خطأ في التحقق من وجود الإعدادات: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * التحقق من وجود إعداد في قاعدة البيانات
     *
     * @param string $key مفتاح الإعداد
     * @return bool هل الإعداد موجود
     */
    public function settingExists($key) {
        try {
            // Escapar el valor de la clave para evitar inyección SQL
            $escapedKey = str_replace("'", "\'", $key);

            $checkQuery = "SELECT COUNT(*) FROM settings WHERE setting_key = '{$escapedKey}'";
            $result = $this->db->query($checkQuery);

            if (!$result) {
                throw new Exception("Failed to execute query: {$checkQuery}");
            }

            $count = (int) $result->fetchColumn();
            return $count > 0;
        } catch (Exception $e) {
            $this->log('خطأ في التحقق من وجود الإعداد: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * إضافة إعداد جديد إلى قاعدة البيانات
     *
     * @param string $key مفتاح الإعداد
     * @param mixed $value قيمة الإعداد
     * @param string $description وصف الإعداد
     * @param bool $isAdmin هل المستخدم مدير
     * @return bool نتيجة العملية
     */
    public function addSetting($key, $value, $description = '', $isAdmin = false) {
        // التحقق من صلاحيات المستخدم
        if (!$isAdmin) {
            $this->log("Non-admin user attempted to add new setting: {$key} - Operation denied");
            return false;
        }

        try {
            // Usar consulta directa con valores escapados manualmente
            $escapedKey = str_replace("'", "\'", $key);
            $escapedValue = str_replace("'", "\'", $value);
            $escapedDescription = str_replace("'", "\'", $description);

            $insertQuery = "INSERT INTO settings (setting_key, setting_value, description)
                           VALUES ('{$escapedKey}', '{$escapedValue}', '{$escapedDescription}')";

            $result = $this->db->query($insertQuery);

            if (!$result) {
                throw new Exception("Failed to insert setting: {$key}");
            }

            // تحديث المخزن المؤقت
            $this->cache[$key] = $value;

            // تسجيل إضافة إعداد جديد
            $this->log("Admin user added new setting: {$key} with value: {$value}");
            return true;
        } catch (Exception $e) {
            $this->log('خطأ في إضافة إعداد جديد: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * تحديث مجموعة من الإعدادات بطريقة أكثر كفاءة
     *
     * @param string $prefix بادئة مفتاح الإعدادات
     * @param array $settings مصفوفة من الإعدادات بتنسيق ['key' => 'value']
     * @param bool $isAdmin هل المستخدم مدير
     * @return bool نتيجة العملية
     */
    public function updateMultiple($prefix, $settings, $isAdmin = false) {
        if (empty($settings) || !is_array($settings)) {
            error_log("updateMultiple: Empty or invalid settings array for prefix: {$prefix}");
            return false;
        }

        // التأكد من وجود الإعدادات الأساسية
        $this->ensureSettingsExist();

        try {
            // بدء المعاملة
            $this->db->beginTransaction();

            $updatedCount = 0;
            $insertedCount = 0;
            $skippedCount = 0;

            foreach ($settings as $key => $value) {
                // تكوين مفتاح الإعداد باستخدام البادئة
                $fullKey = "{$prefix}_{$key}";

                // التحقق من وجود المفتاح
                $exists = $this->settingExists($fullKey);

                if ($exists) {
                    // تحديث الإعداد الموجود
                    $escapedValue = str_replace("'", "\'", $value);
                    $escapedKey = str_replace("'", "\'", $fullKey);

                    $updateQuery = "UPDATE settings SET setting_value = '{$escapedValue}' WHERE setting_key = '{$escapedKey}'";
                    $result = $this->db->query($updateQuery);

                    if ($result) {
                        // تحديث المخزن المؤقت
                        $this->cache[$fullKey] = $value;
                        $updatedCount++;
                        $this->log("Updated setting: {$fullKey} with value: {$value}");
                    } else {
                        $this->log("Failed to update setting: {$fullKey}");
                        $skippedCount++;
                    }
                } else {
                    // إضافة إعداد جديد فقط إذا كان المستخدم مديراً
                    $description = "{$prefix} {$key}"; // وصف بسيط للإعداد

                    if ($this->addSetting($fullKey, $value, $description, $isAdmin)) {
                        $insertedCount++;
                    } else {
                        $skippedCount++;
                    }
                }
            }

            // تأكيد المعاملة
            $this->db->commit();

            // تسجيل ملخص العملية
            error_log("Settings update summary for prefix '{$prefix}': Updated: {$updatedCount}, Inserted: {$insertedCount}, Skipped: {$skippedCount}");

            return true;
        } catch (Exception $e) {
            // التراجع عن المعاملة في حالة الخطأ
            $this->db->rollBack();
            error_log('خطأ في تحديث الإعدادات المتعددة: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * تحديث مجموعة من الإعدادات بطريقة أكثر كفاءة (بدون بادئة)
     *
     * @param array $settings مصفوفة من الإعدادات بتنسيق ['key' => 'value']
     * @param bool $isAdmin هل المستخدم مدير
     * @param string $description وصف افتراضي للإعدادات الجديدة
     * @return bool نتيجة العملية
     */
    public function updateSettings($settings, $isAdmin = false, $description = '') {
        if (empty($settings) || !is_array($settings)) {
            $this->log("updateSettings: Empty or invalid settings array");
            return false;
        }

        // التأكد من وجود الإعدادات الأساسية
        $this->ensureSettingsExist();

        try {
            // بدء المعاملة
            $this->db->beginTransaction();

            $updatedCount = 0;
            $insertedCount = 0;
            $skippedCount = 0;

            foreach ($settings as $key => $value) {
                // التحقق من وجود المفتاح
                $exists = $this->settingExists($key);

                if ($exists) {
                    // تحديث الإعداد الموجود
                    $escapedValue = str_replace("'", "\'", $value);
                    $escapedKey = str_replace("'", "\'", $key);

                    $updateQuery = "UPDATE settings SET setting_value = '{$escapedValue}' WHERE setting_key = '{$escapedKey}'";
                    $result = $this->db->query($updateQuery);

                    if ($result) {
                        // تحديث المخزن المؤقت
                        $this->cache[$key] = $value;
                        $updatedCount++;
                        $this->log("Updated setting: {$key} with value: {$value}");
                    } else {
                        $this->log("Failed to update setting: {$key}");
                        $skippedCount++;
                    }
                } else {
                    // إضافة إعداد جديد فقط إذا كان المستخدم مديراً
                    if ($this->addSetting($key, $value, $description, $isAdmin)) {
                        $insertedCount++;
                    } else {
                        $skippedCount++;
                    }
                }
            }

            // تأكيد المعاملة
            $this->db->commit();

            // تسجيل ملخص العملية
            $this->log("Settings update summary: Updated: {$updatedCount}, Inserted: {$insertedCount}, Skipped: {$skippedCount}");

            return true;
        } catch (Exception $e) {
            // التراجع عن المعاملة في حالة الخطأ
            $this->db->rollBack();
            $this->log('خطأ في تحديث الإعدادات: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * الحصول على إعداد كامل بواسطة المفتاح
     *
     * @param string $key مفتاح الإعداد
     * @return array|null بيانات الإعداد كاملة أو null إذا لم يتم العثور عليه
     */
    public function getByKey($key) {
        try {
            // Escapar el valor de la clave para evitar inyección SQL
            $escapedKey = str_replace("'", "\'", $key);

            $query = "SELECT * FROM settings WHERE setting_key = '{$escapedKey}'";
            $result = $this->db->query($query);

            if (!$result) {
                throw new Exception("Failed to execute query: {$query}");
            }

            $row = $result->fetch(PDO::FETCH_ASSOC);

            if ($row) {
                // تخزين القيمة في المخزن المؤقت
                $this->cache[$key] = $row['setting_value'];
                return $row;
            }
        } catch (Exception $e) {
            $this->log('خطأ في استرجاع الإعداد: ' . $e->getMessage());
        }

        return null;
    }

    /**
     * تحديث إعداد واحد
     *
     * @param string $key مفتاح الإعداد
     * @param mixed $value قيمة الإعداد
     * @return bool نجاح أو فشل العملية
     */
    public function updateSetting($key, $value) {
        try {
            // استخدام دالة set الموجودة لتحديث الإعداد
            $result = $this->set($key, $value);

            if ($result) {
                $this->log("تم تحديث الإعداد: {$key} = {$value}");
            } else {
                $this->log("فشل تحديث الإعداد: {$key}");
            }

            return $result;
        } catch (Exception $e) {
            $this->log('خطأ في تحديث الإعداد: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * الحصول على الإعدادات بواسطة بادئة
     *
     * @param string $prefix بادئة مفتاح الإعدادات
     * @return array مصفوفة من الإعدادات بتنسيق ['key' => 'value']
     */
    public function getSettingsByPrefix($prefix) {
        try {
            // التأكد من وجود الإعدادات الأساسية
            $this->ensureSettingsExist();

            // الحصول على جميع الإعدادات إذا لم تكن مخزنة مسبقًا
            if (empty($this->cache)) {
                $this->getAllSettings();
            }

            // البحث عن الإعدادات التي تبدأ بالبادئة
            $result = [];
            $prefixLength = strlen($prefix);

            foreach ($this->cache as $key => $value) {
                if (strpos($key, $prefix) === 0) {
                    // إزالة البادئة من المفتاح
                    $shortKey = substr($key, $prefixLength + 1); // +1 لإزالة الشرطة السفلية
                    $result[$shortKey] = $value;
                }
            }

            // إذا لم يتم العثور على أي إعدادات في المخزن المؤقت، ابحث في قاعدة البيانات
            if (empty($result)) {
                // Escapar el valor del prefijo para evitar inyección SQL
                $escapedPrefix = str_replace("'", "\'", $prefix);

                $query = "SELECT setting_key, setting_value FROM settings WHERE setting_key LIKE '{$escapedPrefix}_%'";
                $dbResult = $this->db->query($query);

                if (!$dbResult) {
                    throw new Exception("Failed to execute query: {$query}");
                }

                $rows = $dbResult->fetchAll(PDO::FETCH_ASSOC);

                foreach ($rows as $row) {
                    $key = $row['setting_key'];
                    $value = $row['setting_value'];

                    // تخزين القيمة في المخزن المؤقت
                    $this->cache[$key] = $value;

                    // إزالة البادئة من المفتاح
                    $shortKey = substr($key, $prefixLength + 1);
                    $result[$shortKey] = $value;
                }
            }

            return $result;
        } catch (Exception $e) {
            $this->log('خطأ في استرجاع الإعدادات بواسطة البادئة: ' . $e->getMessage());
            return [];
        }
    }
}
