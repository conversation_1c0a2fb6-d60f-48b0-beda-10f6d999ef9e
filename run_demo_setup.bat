@echo off
echo ========================================
echo    نظام إدارة الصالونات - النسخة التجريبية
echo    سكريبت الإعداد التلقائي
echo ========================================
echo.

echo [1/5] التحقق من متطلبات النظام...
php --version >nul 2>&1
if errorlevel 1 (
    echo خطأ: PHP غير مثبت أو غير متوفر في PATH
    echo يرجى تثبيت PHP أولاً
    pause
    exit /b 1
)
echo ✓ PHP متوفر

echo.
echo [2/5] التحقق من اتصال قاعدة البيانات...
mysql --version >nul 2>&1
if errorlevel 1 (
    echo تحذير: MySQL غير متوفر في PATH
    echo تأكد من تشغيل خادم MySQL
) else (
    echo ✓ MySQL متوفر
)

echo.
echo [3/5] إنشاء المجلدات المطلوبة...
if not exist "logs" mkdir logs
if not exist "backups" mkdir backups
if not exist "scripts" mkdir scripts
if not exist "includes" mkdir includes
echo ✓ تم إنشاء المجلدات

echo.
echo [4/5] تشغيل سكريبت الإعداد...
php setup_demo.php
if errorlevel 1 (
    echo خطأ: فشل في تشغيل سكريبت الإعداد
    echo تحقق من إعدادات قاعدة البيانات
    pause
    exit /b 1
)

echo.
echo [5/5] التحقق من الإعداد...
if exist "includes\demo_check.php" (
    echo ✓ تم إنشاء ملفات النسخة التجريبية
) else (
    echo تحذير: لم يتم إنشاء بعض الملفات
)

echo.
echo ========================================
echo تم إعداد النسخة التجريبية بنجاح!
echo ========================================
echo.
echo بيانات الدخول:
echo - المدير: admin / password
echo - مدير الفرع: manager / password  
echo - أمين الصندوق: cashier / password
echo - مستخدم تجريبي: demo / password
echo.
echo للوصول للنظام:
echo http://localhost/demo/
echo.
echo للمساعدة، راجع ملف README_DEMO.md
echo.
pause
