<?php
/**
 * صفحة إدارة المنتجات
 * تعرض قائمة المنتجات المتاحة مع إمكانية البحث والفلترة والإضافة والتعديل والحذف
 */

// تعريف ثابت لمنع الوصول المباشر للملفات المرتبطة
define('BASEPATH', true);

// استدعاء ملف التهيئة
require_once '../../config/init.php';

// التحقق من تسجيل الدخول
redirectIfNotLoggedIn();

// التحقق من صلاحية الوصول
if (!hasPermission('products_view')) {
    $_SESSION['error_message'] = 'ليس لديك صلاحية لعرض المنتجات';
    header('Location: ' . BASE_URL . 'pages/dashboard.php');
    exit;
}

// تحديد الفلاتر الافتراضية
$currentBranchId = $_SESSION['user_branch_id'];
$userBranchId = $_SESSION['user_branch_id']; // تعريف متغير فرع المستخدم
$isAdmin = ($_SESSION['user_role'] === ROLE_ADMIN);

// الحصول على فئات المنتجات
$productObj = new Product($db);
$categories = $productObj->getProductCategories();

// الحصول على رمز العملة من الإعدادات
$settingsModel = new Settings($db);
$currencySymbol = $settingsModel->get('system_currency_symbol', 'ر.س');

// الحصول على الفروع (للمدير فقط)
$branches = [];
if ($isAdmin) {
    $branchObj = new Branch($db);
    $branches = $branchObj->getBranches();
}

// عنوان الصفحة
$pageTitle = 'إدارة المنتجات';

// استدعاء قالب الرأس
include_once '../../includes/templates/header.php';
?>

<!-- بداية محتوى الصفحة -->
<div class="page-content">
    <div class="container-fluid">

        <!-- عنوان الصفحة والأزرار -->
        <div class="row mb-4 align-items-center">
            <div class="col-md-6">
                <h4 class="fw-bold mb-2"><?php echo $pageTitle; ?></h4>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb mb-0">
                        <li class="breadcrumb-item">
                            <a href="<?php echo BASE_URL . 'pages/dashboard.php'; ?>" class="text-decoration-none">
                                <i class="fas fa-home me-1"></i>الرئيسية
                            </a>
                        </li>
                        <li class="breadcrumb-item active" aria-current="page">المنتجات</li>
                    </ol>
                </nav>
            </div>
            <div class="col-md-6 text-md-end">
                <div class="btn-group">
                    <?php if (hasPermission('products_add')): ?>
                        <a href="<?php echo BASE_URL . 'pages/products/add.php'; ?>" class="btn btn-primary">
                            <i class="fas fa-plus-circle me-1"></i> إضافة منتج
                        </a>
                    <?php endif; ?>

                    <?php if (hasPermission('products_edit')): ?>
                        <a href="<?php echo BASE_URL . 'pages/products/categories.php'; ?>" class="btn btn-outline-secondary ms-2">
                            <i class="fas fa-tags me-1"></i> فئات المنتجات
                        </a>
                    <?php endif; ?>

                    <?php if (hasPermission('inventory_adjust')): ?>
                        <a href="<?php echo BASE_URL . 'pages/inventory/index.php'; ?>" class="btn btn-outline-info ms-2">
                            <i class="fas fa-boxes me-1"></i> المخزون
                        </a>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- بطاقة البحث والفلترة -->
        <?php
        // تحديد حقول البحث
        $searchFields = [
            [
                'name' => 'search',
                'label' => 'البحث',
                'type' => 'text'
            ],
            [
                'name' => 'category_id',
                'label' => 'الفئة',
                'type' => 'select',
                'options' => array_column($categories, 'name', 'id')
            ]
        ];

        // إضافة حقل الفرع للمدير فقط
        if ($isAdmin) {
            $searchFields[] = [
                'name' => 'branch_id',
                'label' => 'الفرع',
                'type' => 'select',
                'options' => array_column($branches, 'name', 'id')
            ];
        }

        // إضافة حقول أخرى
        $searchFields[] = [
            'name' => 'is_for_sale',
            'label' => 'نوع المنتج',
            'type' => 'select',
            'options' => [
                '1' => 'للبيع',
                '0' => 'للاستخدام الداخلي'
            ]
        ];

        $searchFields[] = [
            'name' => 'is_active',
            'label' => 'الحالة',
            'type' => 'select',
            'options' => [
                '1' => 'نشط',
                '0' => 'غير نشط'
            ]
        ];

        $searchFields[] = [
            'name' => 'stock_status',
            'label' => 'حالة المخزون',
            'type' => 'select',
            'options' => [
                'low' => 'منخفض',
                'out' => 'نفذ',
                'available' => 'متوفر'
            ]
        ];

        // القيم الحالية للبحث
        $searchValues = [
            'search' => isset($_GET['search']) ? $_GET['search'] : '',
            'category_id' => isset($_GET['category_id']) ? $_GET['category_id'] : '',
            'is_for_sale' => isset($_GET['is_for_sale']) ? $_GET['is_for_sale'] : '',
            'is_active' => isset($_GET['is_active']) ? $_GET['is_active'] : '',
            'stock_status' => isset($_GET['stock_status']) ? $_GET['stock_status'] : ''
        ];

        if ($isAdmin) {
            $searchValues['branch_id'] = isset($_GET['branch_id']) ? $_GET['branch_id'] : '';
        }

        // عرض نموذج البحث باستخدام الدالة الجديدة
        echo displayCard('البحث والفلترة',
            displaySearchForm($searchFields, $searchValues, ''),
            ['id' => 'search-card', 'class' => 'mb-4']
        );
        ?>

        <!-- جدول المنتجات -->
        <?php
        // عناوين الجدول
        $tableHeaders = [
            'id' => '#',
            'name' => 'اسم المنتج',
            'category' => 'الفئة',
            'price' => 'السعر',
            'cost' => 'التكلفة',
            'profit' => 'الربح'
        ];

        // إضافة عمود الفرع للمدير فقط
        if ($isAdmin) {
            $tableHeaders['branch'] = 'الفرع';
        }

        // إضافة الأعمدة المتبقية
        $tableHeaders['stock'] = 'المخزون';
        $tableHeaders['status'] = 'الحالة';

        // خيارات الجدول
        $tableOptions = [
            'id' => 'products-table',
            'class' => 'table table-hover align-middle data-table',
            'actionUrls' => [
                'view' => 'javascript:viewProduct("{id}")',
                'edit' => BASE_URL . 'pages/products/edit.php?id={id}',
                'delete' => 'javascript:deleteProduct("{id}")'
            ],
            'emptyMessage' => '<div class="d-flex flex-column align-items-center py-4">
                <div class="spinner-border text-primary mb-3" role="status" style="width: 3rem; height: 3rem;">
                    <span class="visually-hidden">جاري التحميل...</span>
                </div>
                <p class="text-muted fs-5 mb-0">جاري تحميل البيانات...</p>
            </div>'
        ];

        // عرض الجدول باستخدام الدالة الجديدة
        echo displayCard(
            '<div class="d-flex justify-content-between align-items-center">
                <h5 class="mb-0 fw-bold text-primary">
                    <i class="fas fa-box-open me-2"></i> قائمة المنتجات
                </h5>
                <div class="badge bg-primary bg-gradient rounded-pill px-3 py-2 fs-6">
                    <i class="fas fa-cubes me-1"></i> <span id="total-products">0</span> منتج
                </div>
            </div>',
            '<div id="products-container">
                ' . displayTable($tableHeaders, [], $tableOptions) . '
                <div class="d-flex justify-content-center p-3 border-top">
                    <nav aria-label="Page navigation">
                        <ul class="pagination pagination-sm mb-0" id="pagination"></ul>
                    </nav>
                </div>
            </div>',
            ['id' => 'products-card', 'class' => 'shadow-sm border-0 mb-4', 'headerClass' => 'bg-white py-3']
        );
        ?>

    </div>
</div>

<!-- موديل تفاصيل المنتج -->
<div class="modal fade" id="productDetailsModal" tabindex="-1" aria-labelledby="productDetailsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="productDetailsModalLabel">تفاصيل المنتج</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-6">
                        <p><strong>اسم المنتج:</strong> <span id="modal-product-name"></span></p>
                        <p><strong>الفئة:</strong> <span id="modal-product-category"></span></p>
                        <p><strong>النوع:</strong> <span id="modal-product-type"></span></p>
                    </div>
                    <div class="col-md-6">
                        <p><strong>السعر:</strong> <span id="modal-product-price"></span></p>
                        <p><strong>التكلفة:</strong> <span id="modal-product-cost"></span></p>
                        <p><strong>الحالة:</strong> <span id="modal-product-status"></span></p>
                    </div>
                    <div class="col-md-12">
                        <hr>
                        <p><strong>الوصف:</strong></p>
                        <p id="modal-product-description" class="text-muted"></p>
                    </div>
                    <div class="col-md-12 mt-3">
                        <h6>معلومات المخزون:</h6>
                        <div class="row">
                            <div class="col-md-4">
                                <p><strong>الكمية الحالية:</strong> <span id="modal-product-stock"></span></p>
                            </div>
                            <div class="col-md-4">
                                <p><strong>الحد الأدنى:</strong> <span id="modal-product-min-quantity"></span></p>
                            </div>
                            <div class="col-md-4">
                                <p><strong>الفرع:</strong> <span id="modal-product-branch"></span></p>
                            </div>
                        </div>
                    </div>
                    <?php if (hasPermission('inventory_view')): ?>
                    <div class="col-md-12 mt-3">
                        <h6>آخر حركات المخزون:</h6>
                        <div class="table-responsive">
                            <table class="table table-sm table-striped">
                                <thead>
                                    <tr>
                                        <th>التاريخ</th>
                                        <th>النوع</th>
                                        <th>الكمية</th>
                                        <th>الرصيد</th>
                                        <th>الملاحظات</th>
                                    </tr>
                                </thead>
                                <tbody id="modal-product-transactions">
                                    <!-- سيتم تعبئتها عبر الجافاسكربت -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                <?php if (hasPermission('products_edit')): ?>
                    <a href="#" id="btn-edit-product" class="btn btn-primary">تعديل</a>
                <?php endif; ?>
                <?php if (hasPermission('inventory_adjust')): ?>
                    <button type="button" class="btn btn-success" id="btn-adjust-stock">تعديل المخزون</button>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- موديل تعديل المخزون -->
<div class="modal fade" id="adjustStockModal" tabindex="-1" aria-labelledby="adjustStockModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="adjustStockModalLabel">تعديل مخزون المنتج</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <input type="hidden" id="adjust-product-id">
                <div class="mb-3">
                    <p><strong>المنتج:</strong> <span id="adjust-product-name"></span></p>
                    <p><strong>الكمية الحالية:</strong> <span id="adjust-current-stock"></span></p>
                </div>
                <div class="mb-3">
                    <label class="form-label">نوع العملية</label>
                    <div class="btn-group w-100" role="group">
                        <input type="radio" class="btn-check" name="operation-type" id="operation-add" value="add" autocomplete="off" checked>
                        <label class="btn btn-outline-success" for="operation-add">إضافة</label>

                        <input type="radio" class="btn-check" name="operation-type" id="operation-remove" value="remove" autocomplete="off">
                        <label class="btn btn-outline-danger" for="operation-remove">سحب</label>

                        <input type="radio" class="btn-check" name="operation-type" id="operation-adjust" value="adjust" autocomplete="off">
                        <label class="btn btn-outline-primary" for="operation-adjust">تسوية</label>
                    </div>
                </div>
                <div class="mb-3">
                    <label for="adjust-quantity" class="form-label">الكمية</label>
                    <input type="number" class="form-control" id="adjust-quantity" min="1" value="1">
                    <div class="form-text" id="quantity-help-text">أدخل الكمية المراد إضافتها للمخزون</div>
                </div>
                <div class="mb-3">
                    <label for="adjust-notes" class="form-label">ملاحظات</label>
                    <textarea class="form-control" id="adjust-notes" rows="2"></textarea>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" id="btn-save-stock-adjustment">حفظ</button>
            </div>
        </div>
    </div>
</div>

<!-- موديل استهلاك المنتج -->
<div class="modal fade" id="consumeProductModal" tabindex="-1" aria-labelledby="consumeProductModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="consumeProductModalLabel">استهلاك منتج</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <input type="hidden" id="consume-product-id">
                <div class="mb-3">
                    <p><strong>المنتج:</strong> <span id="consume-product-name"></span></p>
                    <p><strong>الكمية الحالية:</strong> <span id="consume-current-stock"></span></p>
                </div>
                <div class="mb-3">
                    <label for="consume-quantity" class="form-label">الكمية المستهلكة</label>
                    <input type="number" class="form-control" id="consume-quantity" min="1" value="1">
                    <div class="form-text">أدخل الكمية المراد استهلاكها من المنتج</div>
                </div>
                <div class="mb-3">
                    <label for="consume-notes" class="form-label">ملاحظات</label>
                    <textarea class="form-control" id="consume-notes" rows="2" placeholder="سبب الاستهلاك"></textarea>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" id="btn-save-consumption">تأكيد الاستهلاك</button>
            </div>
        </div>
    </div>
</div>

<!-- موديل تأكيد الحذف -->
<div class="modal fade" id="deleteProductModal" tabindex="-1" aria-labelledby="deleteProductModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title" id="deleteProductModalLabel">تأكيد حذف المنتج</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من حذف المنتج: <strong id="delete-product-name"></strong>؟</p>
                <p class="text-danger">هذا الإجراء لا يمكن التراجع عنه!</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-danger" id="confirm-delete">تأكيد الحذف</button>
            </div>
        </div>
    </div>
</div>

<!-- جافاسكربت خاص بالصفحة -->
<script>
    // استخدام نظام الأنماط الموحد
    document.addEventListener('DOMContentLoaded', function() {
        // تهيئة الجدول
        TableSystem.initTable('products-table');
    });

    // التحقق من وجود jQuery وتحميلها إذا لم تكن موجودة
    if (typeof jQuery === 'undefined') {
        document.write('<script src="https://code.jquery.com/jquery-3.6.0.min.js"><\/script>');
    }

    // استخدام window.onload بدلاً من $(document).ready للتأكد من تحميل jQuery
    window.onload = function() {
        // التحقق مرة أخرى من وجود jQuery
        if (typeof jQuery === 'undefined') {
            console.error('فشل تحميل jQuery. بعض الوظائف قد لا تعمل بشكل صحيح.');
            document.querySelector('#products-table tbody').innerHTML = '<tr><td colspan="<?php echo $isAdmin ? "10" : "9"; ?>" class="text-center text-danger">حدث خطأ في تحميل المكتبات اللازمة</td></tr>';
            return;
        }

        // متغيرات عامة
        let currentPage = 1;
        let totalPages = 1;
        let limit = 10;

        // تحميل المنتجات عند تحميل الصفحة
        loadProducts();

        // تحميل المنتجات عند إرسال نموذج البحث
        $('#product-search-form').on('submit', function(e) {
            e.preventDefault();
            currentPage = 1;
            loadProducts();
        });

        // إعادة تعيين الفلاتر
        $('#reset-filters').on('click', function() {
            setTimeout(function() {
                currentPage = 1;
                loadProducts();
            }, 100);
        });

        // وظيفة تحميل المنتجات
        function loadProducts() {
            // إظهار مؤشر التحميل
            $('#products-list').html(`
                <tr>
                    <td colspan="<?php echo $isAdmin ? '10' : '9'; ?>" class="text-center">
                        <div class="d-flex justify-content-center py-3">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">جاري التحميل...</span>
                            </div>
                        </div>
                        <p class="text-muted">جاري تحميل البيانات...</p>
                    </td>
                </tr>
            `);

            // جمع بيانات الفلاتر
            const filters = {
                page: currentPage,
                limit: limit
            };

            // إضافة قيم البحث فقط إذا كانت غير فارغة
            const searchVal = $('#search').val();
            if (searchVal && searchVal.trim() !== '') {
                filters.search = searchVal;
            }

            const categoryId = $('#category_id').val();
            if (categoryId && categoryId !== '') {
                filters.category_id = categoryId;
            }

            const isForSale = $('#is_for_sale').val();
            if (isForSale !== '') {
                filters.is_for_sale = isForSale;
            }

            const isActive = $('#is_active').val();
            if (isActive !== '') {
                filters.is_active = isActive;
            }

            <?php if ($isAdmin): ?>
            const branchId = $('#branch_id').val();
            if (branchId && branchId !== '') {
                filters.branch_id = branchId;
            }
            <?php endif; ?>

            const stockStatus = $('#stock_status').val();
            if (stockStatus === 'low') {
                filters.low_stock = 1;
            } else if (stockStatus === 'out') {
                filters.out_of_stock = 1;
            }

            // إرسال طلب AJAX
            $.ajax({
                url: '<?php echo API_URL; ?>products.php?action=list',
                type: 'GET',
                data: filters,
                dataType: 'json',
                success: function(response) {
                    if (response.success) {
                        // تحديث عدد المنتجات
                        $('#total-products').text(response.data.pagination.total);

                        // تحديث عدد الصفحات
                        totalPages = response.data.pagination.total_pages;

                        // عرض المنتجات
                        displayProducts(response.data.products);

                        // تحديث التصفح
                        updatePagination();
                    } else {
                        // عرض رسالة الخطأ
                        $('#products-list').html(`
                            <tr>
                                <td colspan="<?php echo $isAdmin ? '10' : '9'; ?>" class="text-center text-danger">
                                    <i class="fas fa-exclamation-triangle me-2"></i>
                                    ${response.message || 'حدث خطأ أثناء تحميل البيانات'}
                                </td>
                            </tr>
                        `);
                    }
                },
                error: function(xhr, status, error) {
                    // عرض رسالة الخطأ
                    $('#products-list').html(`
                        <tr>
                            <td colspan="<?php echo $isAdmin ? '10' : '9'; ?>" class="text-center text-danger">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                حدث خطأ أثناء الاتصال بالخادم
                            </td>
                        </tr>
                    `);
                    console.error('AJAX Error:', error);
                }
            });
        }

        // عرض المنتجات في الجدول
        function displayProducts(products) {
            let html = '';

            if (products.length === 0) {
                html = `
                    <tr>
                        <td colspan="<?php echo $isAdmin ? "10" : "9"; ?>" class="text-center py-5">
                            <div class="py-4">
                                <i class="fas fa-box-open text-muted mb-3" style="font-size: 3rem;"></i>
                                <p class="text-muted fs-5 mb-0">لا توجد منتجات للعرض</p>
                            </div>
                        </td>
                    </tr>
                `;
            } else {
                $.each(products, function(index, product) {
                    const profit = (product.price - (product.cost || 0)).toFixed(2);
                    const profitClass = profit > 0 ? 'text-success' : (profit < 0 ? 'text-danger' : '');

                    let stockStatusHtml = '';
                    if (product.current_stock !== undefined) {
                        if (product.current_stock <= 0) {
                            stockStatusHtml = '<span class="badge bg-danger rounded-pill px-2">نفذ</span>';
                        } else if (product.current_stock < product.min_quantity) {
                            stockStatusHtml = '<span class="badge bg-warning text-dark rounded-pill px-2">منخفض</span>';
                        } else {
                            stockStatusHtml = '<span class="badge bg-success rounded-pill px-2">متوفر</span>';
                        }
                        stockStatusHtml += ' <span class="ms-1 fw-bold">' + product.current_stock + '</span>';
                    } else {
                        stockStatusHtml = '<span class="text-muted">غير متوفر</span>';
                    }

                    // إنشاء أزرار الإجراءات بشكل أكثر تنظيمًا
                    let actionsButtons = `
                        <div class="d-flex justify-content-center gap-1">
                            <button type="button" class="btn btn-sm btn-outline-info view-product" data-id="${product.id}" title="عرض التفاصيل">
                                <i class="fas fa-eye"></i>
                            </button>`;

                    <?php if (hasPermission('products_edit')): ?>
                        actionsButtons += `
                            <a href="<?php echo BASE_URL; ?>pages/products/edit.php?id=${product.id}" class="btn btn-sm btn-outline-primary" title="تعديل">
                                <i class="fas fa-edit"></i>
                            </a>`;
                    <?php endif; ?>

                    <?php if (hasPermission('inventory_adjust')): ?>
                        actionsButtons += `
                            <button type="button" class="btn btn-sm btn-outline-success adjust-stock" data-id="${product.id}" data-name="${product.name}" data-stock="${product.stock || 0}" title="تعديل المخزون">
                                <i class="fas fa-cubes"></i>
                            </button>`;

                        // إضافة زر استهلاك للمنتجات التي ليست للبيع
                        actionsButtons += `
                            <button type="button" class="btn btn-sm btn-outline-warning consume-product" data-id="${product.id}" data-name="${product.name}" data-stock="${product.stock || 0}" data-for-sale="${product.is_for_sale}" title="استهلاك منتج">
                                <i class="fas fa-box-open"></i>
                            </button>`;
                    <?php endif; ?>

                    <?php if (hasPermission('products_delete')): ?>
                        actionsButtons += `
                            <button type="button" class="btn btn-sm btn-outline-danger delete-product" data-id="${product.id}" data-name="${product.name}" title="حذف">
                                <i class="fas fa-trash"></i>
                            </button>`;
                    <?php endif; ?>

                    actionsButtons += `
                        </div>
                    `;

                    // إنشاء عرض الحالة بشكل أكثر تنظيمًا
                    const statusHtml = `
                        <div>
                            ${product.is_active == 1
                                ? '<span class="badge bg-success rounded-pill px-2">نشط</span>'
                                : '<span class="badge bg-danger rounded-pill px-2">غير نشط</span>'}
                            <div class="small mt-1">
                                ${product.is_for_sale == 1
                                    ? '<span class="text-success"><i class="fas fa-tag me-1"></i> للبيع</span>'
                                    : '<span class="text-primary"><i class="fas fa-cog me-1"></i> للاستخدام الداخلي</span>'}
                            </div>
                        </div>
                    `;

                    html += `
                        <tr>
                            <td class="text-center">${(currentPage - 1) * limit + index + 1}</td>
                            <td>
                                <div class="fw-bold">${product.name}</div>
                                <div class="small text-muted d-md-none">
                                    ${product.category_name || '-'} | ${product.price} <?php echo $currencySymbol; ?>
                                </div>
                            </td>
                            <td class="d-none d-md-table-cell">${product.category_name || '-'}</td>
                            <td>${product.price} <?php echo $currencySymbol; ?></td>
                            <td class="d-none d-md-table-cell">${product.cost ? product.cost + ' <?php echo $currencySymbol; ?>' : '-'}</td>
                            <td class="d-none d-md-table-cell ${profitClass}">${profit} <?php echo $currencySymbol; ?></td>
                            <?php if ($isAdmin): ?>
                                <td class="d-none d-lg-table-cell">${product.branch_name || 'جميع الفروع'}</td>
                            <?php endif; ?>
                            <td>${stockStatusHtml}</td>
                            <td class="d-none d-md-table-cell">${statusHtml}</td>
                            <td class="text-center">${actionsButtons}</td>
                        </tr>
                    `;
                });
            }

            $('#products-table tbody').html(html);
        }

        // تحديث التصفح
        function updatePagination() {
            let paginationHtml = '';

            if (totalPages > 1) {
                // زر الصفحة الأولى والسابقة
                paginationHtml += `
                    <li class="page-item ${currentPage === 1 ? 'disabled' : ''}">
                        <a class="page-link" href="#" data-page="1" aria-label="First" title="الصفحة الأولى">
                            <span aria-hidden="true">&laquo;&laquo;</span>
                        </a>
                    </li>
                    <li class="page-item ${currentPage === 1 ? 'disabled' : ''}">
                        <a class="page-link" href="#" data-page="${currentPage - 1}" aria-label="Previous" title="الصفحة السابقة">
                            <span aria-hidden="true">&laquo;</span>
                        </a>
                    </li>
                `;

                // تحديد نطاق الصفحات المعروضة
                let startPage = Math.max(1, currentPage - 2);
                let endPage = Math.min(totalPages, startPage + 4);

                if (endPage - startPage < 4) {
                    startPage = Math.max(1, endPage - 4);
                }

                // إضافة أرقام الصفحات
                for (let i = startPage; i <= endPage; i++) {
                    paginationHtml += `
                        <li class="page-item ${i === currentPage ? 'active' : ''}">
                            <a class="page-link" href="#" data-page="${i}">${i}</a>
                        </li>
                    `;
                }

                // زر الصفحة التالية والأخيرة
                paginationHtml += `
                    <li class="page-item ${currentPage === totalPages ? 'disabled' : ''}">
                        <a class="page-link" href="#" data-page="${currentPage + 1}" aria-label="Next" title="الصفحة التالية">
                            <span aria-hidden="true">&raquo;</span>
                        </a>
                    </li>
                    <li class="page-item ${currentPage === totalPages ? 'disabled' : ''}">
                        <a class="page-link" href="#" data-page="${totalPages}" aria-label="Last" title="الصفحة الأخيرة">
                            <span aria-hidden="true">&raquo;&raquo;</span>
                        </a>
                    </li>
                `;
            }

            // إضافة معلومات الصفحة الحالية
            if (totalPages > 0) {
                const startItem = (currentPage - 1) * limit + 1;
                const endItem = Math.min(currentPage * limit, $('#total-products').text());
                const totalItems = $('#total-products').text();

                // إضافة معلومات الصفحة بعد أزرار التنقل
                $('#pagination-info').html(`
                    <div class="text-muted small mt-2">
                        عرض ${startItem} - ${endItem} من ${totalItems} منتج
                    </div>
                `);
            } else {
                $('#pagination-info').html('');
            }

            $('#pagination').html(paginationHtml);
        }

        // معالجة نقر أزرار التصفح
        $(document).on('click', '#pagination .page-link', function(e) {
            e.preventDefault();

            const page = $(this).data('page');

            if (page !== currentPage && page >= 1 && page <= totalPages) {
                currentPage = page;
                loadProducts();
            }
        });

        // عرض تفاصيل المنتج
        $(document).on('click', '.view-product', function() {
            const productId = $(this).data('id');
            const branch_id_data = $(this).data('branch_id');

            $.ajax({
                url: '<?php echo API_URL; ?>products.php?action=get&id=' + productId,
                data: { branch_id: branch_id_data || $('#branch_id').val() || <?php echo $userBranchId ?: 'null'; ?> },
                type: 'GET',
                dataType: 'json',
                success: function(response) {
                    if (response.success) {
                        const product = response.data;

                        // تعبئة بيانات الموديل
                        $('#modal-product-name').text(product.name);
                        $('#modal-product-category').text(product.category_name || '-');
                        $('#modal-product-type').html(product.is_for_sale == 1
                            ? '<span class="badge bg-success">للبيع</span>'
                            : '<span class="badge bg-primary">للاستخدام الداخلي</span>');
                        $('#modal-product-price').text(product.price);
                        $('#modal-product-cost').text(product.cost || '-');
                        $('#modal-product-status').html(product.is_active == 1
                            ? '<span class="badge bg-success">نشط</span>'
                            : '<span class="badge bg-danger">غير نشط</span>');
                        $('#modal-product-description').text(product.description || 'لا يوجد وصف');
                        $('#modal-product-stock').text(product.current_stock !== undefined ? product.current_stock : 'غير متوفر');
                        $('#modal-product-min-quantity').text(product.min_quantity);
                        $('#modal-product-branch').text(product.branch_name || 'جميع الفروع');

                        // تعبئة حركات المخزون
                        let transactionsHtml = '';
                        if (product.transactions && product.transactions.length > 0) {
                            $.each(product.transactions, function(index, transaction) {
                                const typeClass = transaction.transaction_type === 'in' ? 'text-success' : 'text-danger';
                                const typeIcon = transaction.transaction_type === 'in' ?
                                    '<i class="fas fa-arrow-up text-success"></i>' :
                                    '<i class="fas fa-arrow-down text-danger"></i>';
                                const typeText = transaction.transaction_type === 'in' ? 'إضافة' : 'سحب';

                                transactionsHtml += `
                                    <tr>
                                        <td>${transaction.created_at}</td>
                                        <td class="${typeClass}">${typeIcon} ${typeText}</td>
                                        <td>${transaction.quantity}</td>
                                        <td>${transaction.current_quantity}</td>
                                        <td>${transaction.notes || '-'}</td>
                                    </tr>
                                `;
                            });
                        } else {
                            transactionsHtml = '<tr><td colspan="5" class="text-center">لا توجد حركات مخزون لهذا المنتج</td></tr>';
                        }

                        $('#modal-product-transactions').html(transactionsHtml);

                        // تعيين رابط التعديل
                        $('#btn-edit-product').attr('href', '<?php echo BASE_URL; ?>pages/products/edit.php?id=' + productId);

                        // تعيين معرف المنتج لزر تعديل المخزون
                        $('#btn-adjust-stock').data('id', productId);
                        $('#btn-adjust-stock').data('name', product.name);
                        $('#btn-adjust-stock').data('stock', product.stock || 0);

                        // عرض الموديل
                        $('#productDetailsModal').modal('show');
                    } else {
                        showToast('خطأ', response.message, 'error');
                    }
                },
                error: function() {
                    showToast('خطأ', 'حدث خطأ أثناء تحميل بيانات المنتج', 'error');
                }
            });
        });

        // فتح موديل تعديل المخزون من زر في موديل التفاصيل
        $('#btn-adjust-stock').on('click', function() {
            const productId = $(this).data('id');
            const productName = $(this).data('name');
            const currentStock = $(this).data('stock');

            // إغلاق موديل التفاصيل
            $('#productDetailsModal').modal('hide');

            // تعبئة بيانات موديل تعديل المخزون
            $('#adjust-product-id').val(productId);
            $('#adjust-product-name').text(productName);
            $('#adjust-current-stock').text(currentStock);
            $('#adjust-quantity').val(1);
            $('#adjust-notes').val('');

            // فتح موديل تعديل المخزون
            setTimeout(function() {
                $('#adjustStockModal').modal('show');
            }, 500);
        });

        // فتح موديل تعديل المخزون مباشرة من القائمة
        $(document).on('click', '.adjust-stock', function() {
            const productId = $(this).data('id');
            const productName = $(this).data('name');
            const currentStock = $(this).data('stock');

            // تعبئة بيانات موديل تعديل المخزون
            $('#adjust-product-id').val(productId);
            $('#adjust-product-name').text(productName);
            $('#adjust-current-stock').text(currentStock);
            $('#adjust-quantity').val(1);
            $('#adjust-notes').val('');

            // فتح موديل تعديل المخزون
            $('#adjustStockModal').modal('show');
        });

        // تغيير نص المساعدة حسب نوع العملية المختارة
        $('input[name="operation-type"]').on('change', function() {
            const operationType = $('input[name="operation-type"]:checked').val();

            if (operationType === 'add') {
                $('#quantity-help-text').text('أدخل الكمية المراد إضافتها للمخزون');
            } else if (operationType === 'remove') {
                $('#quantity-help-text').text('أدخل الكمية المراد سحبها من المخزون');
            } else if (operationType === 'adjust') {
                $('#quantity-help-text').text('أدخل الكمية الفعلية الجديدة للمخزون');
            }
        });

        // حفظ تعديل المخزون
        $('#btn-save-stock-adjustment').on('click', function() {
            const productId = $('#adjust-product-id').val();
            const operationType = $('input[name="operation-type"]:checked').val();
            const quantity = parseInt($('#adjust-quantity').val());
            const notes = $('#adjust-notes').val();

            if (!quantity || quantity < 0) {
                showToast('خطأ', 'الرجاء إدخال كمية صحيحة', 'error');
                return;
            }

            // تحديد نقطة النهاية والبيانات حسب نوع العملية
            let endpoint = '';
            let data = {
                product_id: productId,
                notes: notes || null
            };

            if (operationType === 'add') {
                endpoint = '<?php echo API_URL; ?>inventory.php?action=add-stock';
                data.quantity = quantity;
            } else if (operationType === 'remove') {
                endpoint = '<?php echo API_URL; ?>inventory.php?action=remove-stock';
                data.quantity = quantity;
            } else if (operationType === 'adjust') {
                endpoint = '<?php echo API_URL; ?>inventory.php?action=adjust';
                data.actual_quantity = quantity;
            }

            // تنفيذ عملية تعديل المخزون
            $.ajax({
                url: endpoint,
                type: 'POST',
                data: data,
                dataType: 'json',
                beforeSend: function() {
                    // تعطيل زر الحفظ وإظهار مؤشر التحميل
                    $('#btn-save-stock-adjustment').prop('disabled', true).html('<span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span> جاري الحفظ...');
                },
                success: function(response) {
                    // إعادة تفعيل زر الحفظ
                    $('#btn-save-stock-adjustment').prop('disabled', false).html('حفظ');

                    if (response.status === 'success') {
                        // إغلاق الموديل
                        $('#adjustStockModal').modal('hide');

                        // عرض رسالة النجاح
                        showToast('نجاح', response.message, 'success');

                        // إعادة تحميل المنتجات
                        loadProducts();
                    } else {
                        // عرض رسالة الخطأ
                        showToast('خطأ', response.message, 'error');
                    }
                },
                error: function(xhr, status, error) {
                    // إعادة تفعيل زر الحفظ
                    $('#btn-save-stock-adjustment').prop('disabled', false).html('حفظ');

                    let errorMessage = 'حدث خطأ أثناء تعديل المخزون';
                    try {
                        const response = JSON.parse(xhr.responseText);
                        if (response.message) {
                            errorMessage = response.message;
                        }
                    } catch (e) {
                        console.error(e);
                    }

                    showToast('خطأ', errorMessage, 'error');
                }
            });
        });

        // تهيئة موديل حذف المنتج
        $(document).on('click', '.delete-product', function() {
            const productId = $(this).data('id');
            const productName = $(this).data('name');

            $('#delete-product-name').text(productName);
            $('#confirm-delete').data('id', productId);
            $('#deleteProductModal').modal('show');
        });

        // فتح موديل استهلاك المنتج
        $(document).on('click', '.consume-product', function() {
            const productId = $(this).data('id');
            const productName = $(this).data('name');
            const currentStock = $(this).data('stock');
            const isForSale = $(this).data('for-sale');

            // تعبئة بيانات موديل استهلاك المنتج
            $('#consume-product-id').val(productId);
            $('#consume-product-name').text(productName);
            $('#consume-current-stock').text(currentStock);
            $('#consume-quantity').val(1);
            $('#consume-notes').val('');

            // فتح موديل استهلاك المنتج
            $('#consumeProductModal').modal('show');
        });

        // حفظ استهلاك المنتج
        $('#btn-save-consumption').on('click', function() {
            const productId = $('#consume-product-id').val();
            const quantity = parseInt($('#consume-quantity').val());
            const notes = $('#consume-notes').val();

            if (!quantity || quantity < 1) {
                showToast('خطأ', 'الرجاء إدخال كمية صحيحة', 'error');
                return;
            }

            // تنفيذ عملية استهلاك المنتج
            $.ajax({
                url: '<?php echo API_URL; ?>inventory.php?action=remove-stock',
                type: 'POST',
                data: {
                    product_id: productId,
                    quantity: quantity,
                    notes: notes || 'استهلاك داخلي'
                },
                dataType: 'json',
                beforeSend: function() {
                    // تعطيل زر الحفظ وإظهار مؤشر التحميل
                    $('#btn-save-consumption').prop('disabled', true).html('<span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span> جاري الحفظ...');
                },
                success: function(response) {
                    // إعادة تفعيل زر الحفظ
                    $('#btn-save-consumption').prop('disabled', false).html('تأكيد الاستهلاك');

                    if (response.status === 'success') {
                        // إغلاق الموديل
                        $('#consumeProductModal').modal('hide');

                        // عرض رسالة النجاح
                        showToast('نجاح', 'تم استهلاك المنتج بنجاح', 'success');

                        // إعادة تحميل المنتجات
                        loadProducts();
                    } else {
                        // عرض رسالة الخطأ
                        showToast('خطأ', response.message, 'error');
                    }
                },
                error: function(xhr, status, error) {
                    // إعادة تفعيل زر الحفظ
                    $('#btn-save-consumption').prop('disabled', false).html('تأكيد الاستهلاك');

                    let errorMessage = 'حدث خطأ أثناء استهلاك المنتج';
                    try {
                        const response = JSON.parse(xhr.responseText);
                        if (response.message) {
                            errorMessage = response.message;
                        }
                    } catch (e) {
                        console.error(e);
                    }

                    showToast('خطأ', errorMessage, 'error');
                }
            });
        });

        // تأكيد حذف المنتج
        $('#confirm-delete').on('click', function() {
            const productId = $(this).data('id');

            $.ajax({
                url: '<?php echo API_URL; ?>products.php?action=delete&id=' + productId,
                type: 'POST',
                dataType: 'json',
                beforeSend: function() {
                    // تعطيل زر الحذف وإظهار مؤشر التحميل
                    $('#confirm-delete').prop('disabled', true).html('<span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span> جاري الحذف...');
                },
                success: function(response) {
                    // إعادة تفعيل زر الحذف
                    $('#confirm-delete').prop('disabled', false).html('تأكيد الحذف');

                    // إغلاق الموديل
                    $('#deleteProductModal').modal('hide');

                    if (response.success) {
                        // عرض رسالة النجاح
                        showToast('نجاح', response.message, 'success');

                        // إعادة تحميل المنتجات
                        loadProducts();
                    } else {
                        // عرض رسالة الخطأ
                        showToast('خطأ', response.message, 'error');
                    }
                },
                error: function(xhr, status, error) {
                    // إعادة تفعيل زر الحذف
                    $('#confirm-delete').prop('disabled', false).html('تأكيد الحذف');

                    // إغلاق الموديل
                    $('#deleteProductModal').modal('hide');

                    let errorMessage = 'حدث خطأ أثناء حذف المنتج';
                    try {
                        const response = JSON.parse(xhr.responseText);
                        if (response.message) {
                            errorMessage = response.message;
                        }
                    } catch (e) {
                        console.error(e);
                    }

                    showToast('خطأ', errorMessage, 'error');
                }
            });
        });

        // عرض خطأ في حالة فشل تحميل البيانات
        function displayError(message) {
            $('#products-table tbody').html(`
                <tr>
                    <td colspan="<?php echo $isAdmin ? "10" : "9"; ?>" class="text-center text-danger">
                        <i class="fas fa-exclamation-circle me-2"></i> ${message}
                    </td>
                </tr>
            `);
            $('#pagination').html('');
            $('#total-products').text('0');
        }

        // عرض رسالة تنبيه باستخدام نظام الأنماط الموحد
        function showToast(title, message, type) {
            if (type === 'success') {
                AlertSystem.success(message);
            } else if (type === 'error') {
                AlertSystem.error(message);
            } else if (type === 'warning') {
                AlertSystem.warning(message);
            } else {
                AlertSystem.info(message);
            }
        }
    };
</script>

<?php
// استدعاء قالب الذيل
include_once '../../includes/templates/footer.php';
?>
