<?php
/**
 * كلاس إشعارات المدير
 * يتعامل مع إرسال إشعارات الواتساب للمدراء
 */

class AdminNotification {
    private $db;
    private $settings;
    private $whatsappService;
    private $logFile;

    /**
     * إنشاء كائن جديد من إشعارات المدير
     *
     * @param Database $db كائن قاعدة البيانات
     */
    public function __construct($db) {
        $this->db = $db;

        // إنشاء كائن الإعدادات
        $this->settings = new Settings($db);

        // إنشاء كائن خدمة WhatsApp
        $this->whatsappService = new WhatsAppAutomation($db);

        // تحديد ملف السجل
        $this->logFile = __DIR__ . '/../../logs/admin_notifications.log';

        // التأكد من وجود مجلد السجلات
        if (!is_dir(dirname($this->logFile))) {
            mkdir(dirname($this->logFile), 0755, true);
        }
    }

    /**
     * إرسال إشعار فتح يوم عمل جديد للمدراء
     *
     * @param int $dayId معرف يوم العمل
     * @param array $dayData بيانات يوم العمل
     * @return bool نجاح أو فشل الإرسال
     */
    public function sendWorkdayOpenNotification($dayId, $dayData) {
        try {
            // التحقق من تفعيل إشعارات المدير وإشعار فتح يوم العمل
            $notificationSettings = $this->getNotificationSettings();

            if (!$this->isAdminNotificationsEnabled($notificationSettings) ||
                !$this->isWorkdayOpenNotificationsEnabled($notificationSettings)) {
                $this->log("إشعارات فتح يوم العمل للمدراء غير مفعلة. لم يتم إرسال إشعار ليوم العمل رقم $dayId");
                return false;
            }

            // الحصول على أرقام هواتف المدراء
            $adminPhoneNumbers = $this->getAdminPhoneNumbers($notificationSettings);
            if (empty($adminPhoneNumbers)) {
                $this->log("لا توجد أرقام هواتف للمدراء. لم يتم إرسال إشعار ليوم العمل رقم $dayId");
                return false;
            }

            // التأكد من وجود بيانات يوم العمل
            if (!$dayData || empty($dayData)) {
                $this->log("بيانات يوم العمل غير موجودة. محاولة الحصول عليها من قاعدة البيانات.");
                $dayData = $this->getWorkdayData($dayId);
                if (!$dayData) {
                    $this->log("لم يتم العثور على بيانات يوم العمل رقم $dayId");
                    return false;
                }
            }

            // إعداد رسالة الإشعار
            $message = $this->prepareWorkdayOpenMessage($dayData);

            // إرسال الرسالة لكل مدير
            $success = true;
            foreach ($adminPhoneNumbers as $phoneNumber) {
                $result = $this->whatsappService->sendMessage($phoneNumber, $message);
                if (!$result) {
                    $success = false;
                    $this->log("فشل إرسال إشعار فتح يوم العمل رقم $dayId إلى المدير برقم $phoneNumber");
                } else {
                    $this->log("تم إرسال إشعار فتح يوم العمل رقم $dayId بنجاح إلى المدير برقم $phoneNumber");
                }
            }

            return $success;
        } catch (Exception $e) {
            $this->log("خطأ في إرسال إشعار فتح يوم العمل رقم $dayId: " . $e->getMessage());
            return false;
        }
    }

    /**
     * إرسال إشعار إغلاق يوم عمل للمدراء
     *
     * @param int $dayId معرف يوم العمل
     * @param array $dayData بيانات يوم العمل
     * @return bool نجاح أو فشل الإرسال
     */
    public function sendWorkdayCloseNotification($dayId, $dayData) {
        try {
            // التحقق من تفعيل إشعارات المدير وإشعار إغلاق يوم العمل
            $notificationSettings = $this->getNotificationSettings();

            if (!$this->isAdminNotificationsEnabled($notificationSettings) ||
                !$this->isWorkdayCloseNotificationsEnabled($notificationSettings)) {
                $this->log("إشعارات إغلاق يوم العمل للمدراء غير مفعلة. لم يتم إرسال إشعار ليوم العمل رقم $dayId");
                return false;
            }

            // الحصول على أرقام هواتف المدراء
            $adminPhoneNumbers = $this->getAdminPhoneNumbers($notificationSettings);
            if (empty($adminPhoneNumbers)) {
                $this->log("لا توجد أرقام هواتف للمدراء. لم يتم إرسال إشعار ليوم العمل رقم $dayId");
                return false;
            }

            // التأكد من وجود بيانات يوم العمل
            if (!$dayData || empty($dayData)) {
                $this->log("بيانات يوم العمل غير موجودة. محاولة الحصول عليها من قاعدة البيانات.");
                $dayData = $this->getWorkdayData($dayId);
                if (!$dayData) {
                    $this->log("لم يتم العثور على بيانات يوم العمل رقم $dayId");
                    return false;
                }
            }

            // التحقق من أن اليوم مغلق بالفعل
            if (empty($dayData['closed_at'])) {
                $this->log("يوم العمل رقم $dayId لم يتم إغلاقه بعد. لا يمكن إرسال إشعار الإغلاق.");
                return false;
            }

            // إعداد رسالة الإشعار
            $message = $this->prepareWorkdayCloseMessage($dayData);

            // إرسال الرسالة لكل مدير
            $success = true;
            foreach ($adminPhoneNumbers as $phoneNumber) {
                $result = $this->whatsappService->sendMessage($phoneNumber, $message);
                if (!$result) {
                    $success = false;
                    $this->log("فشل إرسال إشعار إغلاق يوم العمل رقم $dayId إلى المدير برقم $phoneNumber");
                } else {
                    $this->log("تم إرسال إشعار إغلاق يوم العمل رقم $dayId بنجاح إلى المدير برقم $phoneNumber");
                }
            }

            // إرسال التقرير اليومي إذا كان مفعلاً
            if ($this->isDailyReportEnabled($notificationSettings)) {
                // انتظر 30 ثانية قبل إرسال التقرير اليومي لتجنب الإرسال المتكرر
                $this->log("سيتم إرسال التقرير اليومي ليوم العمل رقم $dayId بعد 30 ثانية");
                // لا يمكن استخدام sleep() هنا لأنه سيؤدي إلى تأخير الاستجابة للمستخدم
                // بدلاً من ذلك، نقوم بتسجيل الوقت الحالي في قاعدة البيانات
                // وسيتم التحقق من هذا الوقت في المرة القادمة التي يتم فيها تشغيل المهمة المجدولة
                $this->sendDailyReport($dayId, $dayData);
            }

            return $success;
        } catch (Exception $e) {
            $this->log("خطأ في إرسال إشعار إغلاق يوم العمل رقم $dayId: " . $e->getMessage());
            return false;
        }
    }

    /**
     * إرسال تقرير يومي للمدراء
     *
     * @param int $dayId معرف يوم العمل
     * @param array $dayData بيانات يوم العمل (اختياري)
     * @return bool نجاح أو فشل الإرسال
     */
    public function sendDailyReport($dayId, $dayData = null) {
        try {
            // التحقق من تفعيل إشعارات المدير والتقرير اليومي
            $notificationSettings = $this->getNotificationSettings();

            if (!$this->isAdminNotificationsEnabled($notificationSettings) ||
                !$this->isDailyReportEnabled($notificationSettings)) {
                $this->log("إشعارات التقرير اليومي للمدراء غير مفعلة. لم يتم إرسال تقرير ليوم العمل رقم $dayId");
                return false;
            }

            // الحصول على أرقام هواتف المدراء
            $adminPhoneNumbers = $this->getAdminPhoneNumbers($notificationSettings);
            if (empty($adminPhoneNumbers)) {
                $this->log("لا توجد أرقام هواتف للمدراء. لم يتم إرسال تقرير ليوم العمل رقم $dayId");
                return false;
            }

            // التأكد من وجود بيانات يوم العمل
            if (!$dayData || empty($dayData)) {
                $this->log("بيانات يوم العمل غير موجودة أو فارغة. محاولة الحصول عليها من قاعدة البيانات.");
                $dayData = $this->getWorkdayData($dayId);
                if (!$dayData) {
                    $this->log("لم يتم العثور على بيانات يوم العمل رقم $dayId");
                    return false;
                }
            }

            // التحقق من أن اليوم مغلق بالفعل
            if (empty($dayData['closed_at'])) {
                $this->log("يوم العمل رقم $dayId لم يتم إغلاقه بعد. لا يمكن إرسال التقرير اليومي.");
                return false;
            }

            // سجل بيانات يوم العمل للتشخيص
            $this->log("بيانات يوم العمل: " . json_encode($dayData));

            // إعداد رسالة التقرير
            $message = $this->prepareDailyReportMessage($dayId, $dayData);

            // إرسال الرسالة لكل مدير
            $success = true;
            foreach ($adminPhoneNumbers as $phoneNumber) {
                $result = $this->whatsappService->sendMessage($phoneNumber, $message);
                if (!$result) {
                    $success = false;
                    $this->log("فشل إرسال التقرير اليومي ليوم العمل رقم $dayId إلى المدير برقم $phoneNumber");
                } else {
                    $this->log("تم إرسال التقرير اليومي ليوم العمل رقم $dayId بنجاح إلى المدير برقم $phoneNumber");
                }
            }

            return $success;
        } catch (Exception $e) {
            $this->log("خطأ في إرسال التقرير اليومي ليوم العمل رقم $dayId: " . $e->getMessage());
            return false;
        }
    }

    /**
     * إرسال تقرير مخصص للمدراء
     *
     * @param string $reportTitle عنوان التقرير
     * @param string $reportContent محتوى التقرير
     * @return bool نجاح أو فشل الإرسال
     */
    public function sendCustomReport($reportTitle, $reportContent) {
        try {
            // التحقق من تفعيل إشعارات المدير
            $notificationSettings = $this->getNotificationSettings();

            if (!$this->isAdminNotificationsEnabled($notificationSettings)) {
                $this->log("إشعارات المدراء غير مفعلة. لم يتم إرسال التقرير المخصص: $reportTitle");
                return false;
            }

            // الحصول على أرقام هواتف المدراء
            $adminPhoneNumbers = $this->getAdminPhoneNumbers($notificationSettings);
            if (empty($adminPhoneNumbers)) {
                $this->log("لا توجد أرقام هواتف للمدراء. لم يتم إرسال التقرير المخصص: $reportTitle");
                return false;
            }

            // إعداد رسالة التقرير
            $message = "$reportTitle\n\n$reportContent";

            // إرسال الرسالة لكل مدير
            $success = true;
            foreach ($adminPhoneNumbers as $phoneNumber) {
                $result = $this->whatsappService->sendMessage($phoneNumber, $message);
                if (!$result) {
                    $success = false;
                    $this->log("فشل إرسال التقرير المخصص: $reportTitle إلى المدير برقم $phoneNumber");
                } else {
                    $this->log("تم إرسال التقرير المخصص: $reportTitle بنجاح إلى المدير برقم $phoneNumber");
                }
            }

            return $success;
        } catch (Exception $e) {
            $this->log("خطأ في إرسال التقرير المخصص: $reportTitle: " . $e->getMessage());
            return false;
        }
    }

    /**
     * إعداد رسالة فتح يوم عمل جديد
     *
     * @param array $dayData بيانات يوم العمل
     * @return string نص الرسالة
     */
    private function prepareWorkdayOpenMessage($dayData) {
        // تسجيل البيانات المستلمة للتشخيص
        $this->log("\n\nبيانات يوم العمل المستلمة لفتح يوم عمل: " . json_encode($dayData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));

        // التأكد من وجود معرف يوم العمل
        if (empty($dayData['id'])) {
            $this->log("خطأ: معرف يوم العمل غير موجود في البيانات لفتح يوم عمل");
            return "خطأ: بيانات يوم العمل غير مكتملة";
        }

        // الحصول على بيانات يوم العمل من قاعدة البيانات مباشرة
        try {
            $this->db->prepare("SELECT * FROM end_days WHERE id = :id");
            $this->db->bind(':id', $dayData['id']);
            $this->db->execute();
            $freshDayData = $this->db->fetch();

            if ($freshDayData) {
                // تسجيل البيانات الجديدة للتشخيص
                $this->log("بيانات يوم العمل من قاعدة البيانات لفتح يوم عمل: " . json_encode($freshDayData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
                // استخدام البيانات الجديدة
                $dayData = $freshDayData;
            } else {
                $this->log("لم يتم العثور على بيانات يوم العمل بالمعرف: " . $dayData['id'] . " لفتح يوم عمل");
            }
        } catch (Exception $e) {
            $this->log("خطأ في الحصول على بيانات يوم العمل لفتح يوم عمل: " . $e->getMessage());
        }

        // الحصول على اسم الفرع
        $branchName = $this->getBranchName($dayData['branch_id']);

        // تنسيق التاريخ
        $date = date('Y-m-d', strtotime($dayData['date']));

        // التحقق من وجود معرف المستخدم
        // في نموذج EndDay، يتم تخزين معرف المستخدم الذي فتح اليوم في حقل closed_by
        if (empty($dayData['closed_by'])) {
            $this->log("معرف المستخدم غير موجود في بيانات يوم العمل");
        } else {
            $this->log("معرف المستخدم في بيانات يوم العمل: " . $dayData['closed_by']);
        }

        // الحصول على اسم المستخدم
        $userName = $this->getUserName($dayData['closed_by']);

        // إعداد الرسالة
        $message = "تم فتح يوم عمل جديد\n\n";
        $message .= "التاريخ: $date\n";
        $message .= "الفرع: $branchName\n";
        $message .= "الكاشير: " . ($userName ?: 'غير معروف') . "\n";
        $message .= "الرصيد الافتتاحي: " . number_format($dayData['opening_amount'], 2) . " ج";
        $message .= "\n\nنظام easySalon";

        return $message;
    }

    /**
     * إعداد رسالة إغلاق يوم عمل
     *
     * @param array $dayData بيانات يوم العمل
     * @return string نص الرسالة
     */
    private function prepareWorkdayCloseMessage($dayData) {
        // تسجيل البيانات المستلمة للتشخيص
        $this->log("\n\nبيانات يوم العمل المستلمة: " . json_encode($dayData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));

        // التأكد من وجود معرف يوم العمل
        if (empty($dayData['id'])) {
            $this->log("خطأ: معرف يوم العمل غير موجود في البيانات");
            return "خطأ: بيانات يوم العمل غير مكتملة";
        }

        // الحصول على بيانات يوم العمل من قاعدة البيانات مباشرة
        try {
            $this->db->prepare("SELECT * FROM end_days WHERE id = :id");
            $this->db->bind(':id', $dayData['id']);
            $this->db->execute();
            $freshDayData = $this->db->fetch();

            if ($freshDayData) {
                // تسجيل البيانات الجديدة للتشخيص
                $this->log("بيانات يوم العمل من قاعدة البيانات: " . json_encode($freshDayData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
                // استخدام البيانات الجديدة
                $dayData = $freshDayData;
            } else {
                $this->log("لم يتم العثور على بيانات يوم العمل بالمعرف: " . $dayData['id']);
            }
        } catch (Exception $e) {
            $this->log("خطأ في الحصول على بيانات يوم العمل: " . $e->getMessage());
        }

        // الحصول على إجمالي المبيعات من جدول الفواتير مباشرة
        $totalSales = 0;
        try {
            $this->db->prepare("SELECT SUM(final_amount) as total_sales FROM invoices WHERE end_day_id = :day_id AND payment_status = 'paid'");
            $this->db->bind(':day_id', $dayData['id']);
            $this->db->execute();
            $salesResult = $this->db->fetch();

            if ($salesResult && isset($salesResult['total_sales'])) {
                $totalSales = $salesResult['total_sales'] ?: 0;
                $this->log("إجمالي المبيعات من جدول الفواتير: " . $totalSales);

                // مقارنة مع القيمة المخزنة في جدول end_days
                if ($totalSales != $dayData['total_sales']) {
                    $this->log("تحذير: هناك تضارب بين إجمالي المبيعات في جدول end_days (" . $dayData['total_sales'] . ") وإجمالي المبيعات من جدول الفواتير (" . $totalSales . ")");
                }
            }
        } catch (Exception $e) {
            $this->log("خطأ في الحصول على إجمالي المبيعات: " . $e->getMessage());
        }

        // الحصول على اسم الفرع
        $branchName = $this->getBranchName($dayData['branch_id']);

        // تنسيق التاريخ
        $date = date('Y-m-d', strtotime($dayData['date']));

        // إعداد الرسالة
        $message = "تم إغلاق يوم العمل\n\n";
        $message .= "الرقم: #" . $dayData['id'] . "\n";
        $message .= "التاريخ: $date\n";
        $message .= "الفرع: $branchName\n";
        $message .= "الكاشير: " . $this->getUserName($dayData['closed_by']) . "\n";
        $message .= "الرصيد الافتتاحي: " . number_format($dayData['opening_amount'], 2) . " ج\n";
        $message .= "إجمالي المبيعات: " . number_format($totalSales, 2) . " ج\n";
        $message .= "إجمالي المصروفات: " . number_format($dayData['total_expenses'], 2) . " ج\n";
        $message .= "الرصيد الختامي: " . number_format($totalSales - $dayData['total_expenses'], 2) . " ج";
        $message .= "\n\nنظام easySalon";

        return $message;
    }

    /**
     * إعداد رسالة التقرير اليومي
     *
     * @param int $dayId معرف يوم العمل
     * @param array $dayData بيانات يوم العمل
     * @return string نص الرسالة
     */
    private function prepareDailyReportMessage($dayId, $dayData) {
        // تسجيل البيانات المستلمة للتشخيص
        $this->log("\n\nبيانات يوم العمل للتقرير اليومي: " . json_encode($dayData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));

        // التأكد من وجود معرف يوم العمل
        if (empty($dayId)) {
            $this->log("خطأ: معرف يوم العمل غير موجود");
            return "خطأ: بيانات يوم العمل غير مكتملة";
        }

        // الحصول على بيانات يوم العمل من قاعدة البيانات مباشرة
        try {
            $this->db->prepare("SELECT * FROM end_days WHERE id = :id");
            $this->db->bind(':id', $dayId);
            $this->db->execute();
            $freshDayData = $this->db->fetch();

            if ($freshDayData) {
                // تسجيل البيانات الجديدة للتشخيص
                $this->log("بيانات يوم العمل من قاعدة البيانات للتقرير: " . json_encode($freshDayData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
                // استخدام البيانات الجديدة
                $dayData = $freshDayData;
            } else {
                $this->log("لم يتم العثور على بيانات يوم العمل بالمعرف: " . $dayId);
            }
        } catch (Exception $e) {
            $this->log("خطأ في الحصول على بيانات يوم العمل للتقرير: " . $e->getMessage());
        }

        // الحصول على قالب الرسالة
        $notificationSettings = $this->getNotificationSettings();
        $template = $notificationSettings['admin_message_template'] ?? "تقرير يومي {date}\n\nإجمالي المبيعات: {total_sales}\nعدد الفواتير: {invoices_count}\nإجمالي المصروفات: {total_expenses}\nصافي الربح: {net_profit}\n\nأفضل المنتجات مبيعاً:\n{top_products}\n\nأفضل الخدمات مبيعاً:\n{top_services}";

        // الحصول على اسم الفرع
        $branchName = $this->getBranchName($dayData['branch_id']);

        // تنسيق التاريخ
        $date = date('Y-m-d', strtotime($dayData['date']));

        // الحصول على إجمالي المبيعات من جدول الفواتير مباشرة
        $totalSales = 0;
        try {
            $this->db->prepare("SELECT SUM(final_amount) as total_sales FROM invoices WHERE end_day_id = :day_id AND payment_status = 'paid'");
            $this->db->bind(':day_id', $dayId);
            $this->db->execute();
            $salesResult = $this->db->fetch();

            if ($salesResult && isset($salesResult['total_sales'])) {
                $totalSales = $salesResult['total_sales'] ?: 0;
                $this->log("إجمالي المبيعات من جدول الفواتير للتقرير: " . $totalSales);
            }
        } catch (Exception $e) {
            $this->log("خطأ في الحصول على إجمالي المبيعات للتقرير: " . $e->getMessage());
        }

        // حساب صافي الربح
        $netProfit = $totalSales - $dayData['total_expenses'];

        // الحصول على عدد الفواتير
        $invoicesCount = $this->getInvoicesCount($dayId);

        // الحصول على أفضل المنتجات مبيعاً
        $topProducts = $this->getTopProducts($dayId);

        // الحصول على أفضل الخدمات مبيعاً
        $topServices = $this->getTopServices($dayId);

        // استبدال المتغيرات في القالب
        $message = str_replace(
            [
                '{date}',
                '{total_sales}',
                '{invoices_count}',
                '{total_expenses}',
                '{net_profit}',
                '{top_products}',
                '{top_services}',
                '{branch_name}'
            ],
            [
                $date,
                number_format($totalSales, 2) . ' ج',
                $invoicesCount,
                number_format($dayData['total_expenses'], 2) . ' ج',
                number_format($netProfit, 2) . ' ج',
                $topProducts,
                $topServices,
                $branchName
            ],

            $template
        );
        return $message;
    }

    /**
     * الحصول على عدد الفواتير ليوم عمل معين
     *
     * @param int $dayId معرف يوم العمل
     * @return int عدد الفواتير
     */
    private function getInvoicesCount($dayId) {
        try {
            $this->db->prepare("SELECT COUNT(*) as count FROM invoices WHERE end_day_id = :day_id AND payment_status = 'paid'");
            $this->db->bind(':day_id', $dayId);
            $this->db->execute();
            $result = $this->db->fetch();
            return $result['count'] ?? 0;
        } catch (Exception $e) {
            $this->log("خطأ في الحصول على عدد الفواتير ليوم العمل رقم $dayId: " . $e->getMessage());
            return 0;
        }
    }

    /**
     * الحصول على أفضل المنتجات مبيعاً ليوم عمل معين
     *
     * @param int $dayId معرف يوم العمل
     * @param int $limit عدد المنتجات المراد عرضها
     * @return string نص يحتوي على أفضل المنتجات
     */
    private function getTopProducts($dayId, $limit = 5) {
        try {
            $this->db->prepare("
                SELECT p.name, SUM(ii.quantity) as total_quantity, SUM(ii.total) as total_amount
                FROM invoice_items ii
                JOIN invoices i ON ii.invoice_id = i.id
                JOIN products p ON ii.item_id = p.id
                WHERE i.end_day_id = :day_id AND ii.item_type = 'product' AND i.payment_status = 'paid'
                GROUP BY p.id
                ORDER BY total_amount DESC
                LIMIT :limit
            ");
            $this->db->bind(':day_id', $dayId);
            $this->db->bind(':limit', $limit);
            $this->db->execute();
            $results = $this->db->fetchAll();

            if (empty($results)) {
                return "لا توجد مبيعات منتجات لهذا اليوم";
            }

            $text = "";
            foreach ($results as $index => $product) {
                $text .= ($index + 1) . ". " . $product['name'] . " - " . $product['total_quantity'] . " قطعة - " . number_format($product['total_amount'], 2) . " ج\n";
            }

            return $text;
        } catch (Exception $e) {
            $this->log("خطأ في الحصول على أفضل المنتجات مبيعاً ليوم العمل رقم $dayId: " . $e->getMessage());
            return "خطأ في الحصول على البيانات";
        }
    }

    /**
     * الحصول على أفضل الخدمات مبيعاً ليوم عمل معين
     *
     * @param int $dayId معرف يوم العمل
     * @param int $limit عدد الخدمات المراد عرضها
     * @return string نص يحتوي على أفضل الخدمات
     */
    private function getTopServices($dayId, $limit = 5) {
        try {
            $this->db->prepare("
                SELECT s.name, SUM(ii.quantity) as total_quantity, SUM(ii.total) as total_amount
                FROM invoice_items ii
                JOIN invoices i ON ii.invoice_id = i.id
                JOIN services s ON ii.item_id = s.id
                WHERE i.end_day_id = :day_id AND ii.item_type = 'service' AND i.payment_status = 'paid'
                GROUP BY s.id
                ORDER BY total_amount DESC
                LIMIT :limit
            ");
            $this->db->bind(':day_id', $dayId);
            $this->db->bind(':limit', $limit);
            $this->db->execute();
            $results = $this->db->fetchAll();

            if (empty($results)) {
                return "لا توجد مبيعات خدمات لهذا اليوم";
            }

            $text = "";
            foreach ($results as $index => $service) {
                $text .= ($index + 1) . ". " . $service['name'] . " - " . $service['total_quantity'] . " مرة - " . number_format($service['total_amount'], 2) . " ج\n";
            }

            return $text;
        } catch (Exception $e) {
            $this->log("خطأ في الحصول على أفضل الخدمات مبيعاً ليوم العمل رقم $dayId: " . $e->getMessage());
            return "خطأ في الحصول على البيانات";
        }
    }

    /**
     * الحصول على بيانات يوم العمل
     *
     * @param int $dayId معرف يوم العمل
     * @return array|false بيانات يوم العمل أو false في حالة الفشل
     */
    private function getWorkdayData($dayId) {
        try {
            $this->db->prepare("SELECT * FROM end_days WHERE id = :id");
            $this->db->bind(':id', $dayId);
            $this->db->execute();
            return $this->db->fetch();
        } catch (Exception $e) {
            $this->log("خطأ في الحصول على بيانات يوم العمل رقم $dayId: " . $e->getMessage());
            return false;
        }
    }

    /**
     * الحصول على اسم الفرع
     *
     * @param int $branchId معرف الفرع
     * @return string اسم الفرع
     */
    private function getBranchName($branchId) {
        try {
            $this->db->prepare("SELECT name FROM branches WHERE id = :id");
            $this->db->bind(':id', $branchId);
            $this->db->execute();
            $result = $this->db->fetch();
            return $result['name'] ?? 'غير معروف';
        } catch (Exception $e) {
            $this->log("خطأ في الحصول على اسم الفرع رقم $branchId: " . $e->getMessage());
            return 'غير معروف';
        }
    }

    /**
     * الحصول على اسم المستخدم
     *
     * @param int $userId معرف المستخدم
     * @return string اسم المستخدم
     */
    private function getUserName($userId) {
        if (empty($userId)) {
            $this->log("معرف المستخدم فارغ");
            return 'غير معروف';
        }

        try {
            $this->log("محاولة الحصول على اسم المستخدم بالمعرف: $userId");
            $this->db->prepare("SELECT name FROM users WHERE id = :id");
            $this->db->bind(':id', $userId);
            $this->db->execute();
            $result = $this->db->fetch();

            if ($result && isset($result['name']) && !empty($result['name'])) {
                $this->log("تم العثور على اسم المستخدم: " . $result['name']);
                return $result['name'];
            } else {
                $this->log("لم يتم العثور على اسم المستخدم بالمعرف: $userId");
                return 'غير معروف';
            }
        } catch (Exception $e) {
            $this->log("خطأ في الحصول على اسم المستخدم رقم $userId: " . $e->getMessage());
            return 'غير معروف';
        }
    }

    /**
     * التحقق من تفعيل إشعارات المدير
     *
     * @param array $settings إعدادات الإشعارات
     * @return bool هل إشعارات المدير مفعلة
     */
    private function isAdminNotificationsEnabled($settings) {
        return isset($settings['admin_notifications']) && $settings['admin_notifications'] ||
               isset($settings['dmin_notifications']) && $settings['dmin_notifications'];
    }

    /**
     * التحقق من تفعيل إشعارات فتح يوم العمل
     *
     * @param array $settings إعدادات الإشعارات
     * @return bool هل إشعارات فتح يوم العمل مفعلة
     */
    private function isWorkdayOpenNotificationsEnabled($settings) {
        return isset($settings['notify_workday_open']) && $settings['notify_workday_open'] ||
               isset($settings['otify_workday_open']) && $settings['otify_workday_open'];
    }

    /**
     * التحقق من تفعيل إشعارات إغلاق يوم العمل
     *
     * @param array $settings إعدادات الإشعارات
     * @return bool هل إشعارات إغلاق يوم العمل مفعلة
     */
    private function isWorkdayCloseNotificationsEnabled($settings) {
        return isset($settings['notify_workday_close']) && $settings['notify_workday_close'] ||
               isset($settings['otify_workday_close']) && $settings['otify_workday_close'];
    }

    /**
     * التحقق من تفعيل التقرير اليومي
     *
     * @param array $settings إعدادات الإشعارات
     * @return bool هل التقرير اليومي مفعل
     */
    private function isDailyReportEnabled($settings) {
        return isset($settings['notify_daily_report']) && $settings['notify_daily_report'] ||
               isset($settings['otify_daily_report']) && $settings['otify_daily_report'];
    }

    /**
     * الحصول على أرقام هواتف المدراء
     *
     * @param array $settings إعدادات الإشعارات
     * @return array مصفوفة تحتوي على أرقام هواتف المدراء
     */
    private function getAdminPhoneNumbers($settings) {
        // Buscar en todas las posibles claves para los números de teléfono
        $phoneNumbersStr = '';
        $possibleKeys = ['admin_phone_numbers', 'dmin_phone_numbers', 'notification_admin_phone_numbers'];

        foreach ($possibleKeys as $key) {
            if (!empty($settings[$key])) {
                $phoneNumbersStr = $settings[$key];
                $this->log("Números de teléfono encontrados en la clave '$key': $phoneNumbersStr");
                break;
            }
        }

        if (empty($phoneNumbersStr)) {
            $this->log("No se encontraron números de teléfono en la configuración. Configuración: " . json_encode($settings));
            return [];
        }

        // تقسيم النص إلى أسطر
        $phoneNumbers = explode("\n", $phoneNumbersStr);

        // تنظيف الأرقام
        $cleanedNumbers = [];
        foreach ($phoneNumbers as $number) {
            $number = trim($number);
            if (!empty($number)) {
                $cleanedNumbers[] = $number;
            }
        }

        return $cleanedNumbers;
    }

    /**
     * الحصول على إعدادات الإشعارات
     *
     * @return array إعدادات الإشعارات
     */
    private function getNotificationSettings() {
        return $this->settings->getSettingsByPrefix('notification_');
    }

    /**
     * تسجيل رسالة في ملف السجل
     *
     * @param string $message الرسالة المراد تسجيلها
     * @return void
     */
    private function log($message) {
        $timestamp = date('Y-m-d H:i:s');
        $logMessage = "[$timestamp] $message" . PHP_EOL;
        file_put_contents($this->logFile, $logMessage, FILE_APPEND);
    }
}
