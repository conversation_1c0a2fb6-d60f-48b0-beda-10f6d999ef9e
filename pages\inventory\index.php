<?php
/**
 * صفحة إدارة المخزون
 * تعرض حالة المخزون الحالية مع إمكانية البحث والفلترة وتعديل الكميات
 */

// تعريف ثابت لمنع الوصول المباشر للملفات المرتبطة
define('BASEPATH', true);

// استدعاء ملف التهيئة
require_once '../../config/init.php';

// التحقق من تسجيل الدخول
redirectIfNotLoggedIn();

// التحقق من صلاحية الوصول
if (!hasPermission('inventory_view')) {
    $_SESSION['error_message'] = 'ليس لديك صلاحية لعرض المخزون';
    header('Location: ' . BASE_URL . 'pages/dashboard.php');
    exit;
}

// تحديد الفلاتر الافتراضية
$currentBranchId = $_SESSION['user_branch_id'];
$isAdmin = $_SESSION['user_role'] === ROLE_ADMIN;

// الحصول على فئات المنتجات
$productObj = new Product($db);
$categories = $productObj->getProductCategories();

// الحصول على رمز العملة واسمها من الإعدادات
$settingsModel = new Settings($db);
$currencySymbol = $settingsModel->get('system_currency_symbol', 'ر.س');
$currencyName = $settingsModel->get('system_currency', 'ريال سعودي');

// الحصول على الفروع (للمدير فقط)
$branches = [];
if ($isAdmin) {
    $branchObj = new Branch($db);
    $branches = $branchObj->getBranches();
}

// عنوان الصفحة
$pageTitle = 'إدارة المخزون';

// استدعاء قالب الرأس
include_once '../../includes/templates/header.php';
?>

<!-- بداية محتوى الصفحة -->
<div class="page-content">
    <div class="container-fluid">

        <!-- عنوان الصفحة والأزرار -->
        <div class="row mb-4 align-items-center">
            <div class="col-md-6">
                <h4 class="fw-bold mb-2"><?php echo $pageTitle; ?></h4>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb mb-0">
                        <li class="breadcrumb-item">
                            <a href="<?php echo BASE_URL . 'pages/dashboard.php'; ?>" class="text-decoration-none">
                                <i class="fas fa-home me-1"></i>الرئيسية
                            </a>
                        </li>
                        <li class="breadcrumb-item active" aria-current="page">المخزون</li>
                    </ol>
                </nav>
            </div>
            <div class="col-md-6 text-md-end">
                <div class="btn-group">
                    <?php if (hasPermission('inventory_adjust')): ?>
                        <a href="<?php echo BASE_URL . 'pages/inventory/adjust.php'; ?>" class="btn btn-primary">
                            <i class="fas fa-edit me-1"></i> تسوية المخزون
                        </a>
                    <?php endif; ?>

                    <?php if (hasPermission('products_view')): ?>
                        <a href="<?php echo BASE_URL . 'pages/products/index.php'; ?>" class="btn btn-outline-secondary ms-2">
                            <i class="fas fa-box me-1"></i> المنتجات
                        </a>
                    <?php endif; ?>

                    <?php if (hasPermission('reports_view')): ?>
                        <a href="<?php echo BASE_URL . 'pages/reports/inventory.php'; ?>" class="btn btn-outline-info ms-2">
                            <i class="fas fa-chart-pie me-1"></i> تقارير المخزون
                        </a>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- لوحة الإحصائيات السريعة -->
        <div class="row mb-4">
            <div class="col-md-3 mb-3">
                <div class="card bg-primary text-white h-100">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="card-title mb-1">إجمالي المنتجات</h6>
                                <h4 class="mb-0" id="total-products">-</h4>
                            </div>
                            <div>
                                <i class="fas fa-boxes fa-3x opacity-50"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card bg-success text-white h-100">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="card-title mb-1">المنتجات المتوفرة</h6>
                                <h4 class="mb-0" id="available-products">-</h4>
                            </div>
                            <div>
                                <i class="fas fa-check-circle fa-3x opacity-50"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card bg-warning text-dark h-100">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="card-title mb-1">المنتجات منخفضة المخزون</h6>
                                <h4 class="mb-0" id="low-stock-products">-</h4>
                            </div>
                            <div>
                                <i class="fas fa-exclamation-triangle fa-3x opacity-50"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card bg-danger text-white h-100">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="card-title mb-1">المنتجات النافدة</h6>
                                <h4 class="mb-0" id="out-of-stock-products">-</h4>
                            </div>
                            <div>
                                <i class="fas fa-times-circle fa-3x opacity-50"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- بطاقة البحث والفلترة -->
        <div class="card mb-4">
            <div class="card-header bg-light">
                <h5 class="mb-0">البحث والفلترة</h5>
            </div>
            <div class="card-body">
                <form id="inventory-search-form" method="get">
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <label for="search" class="form-label">البحث</label>
                            <input type="text" class="form-control" id="search" name="search" placeholder="اسم المنتج...">
                        </div>
                        <div class="col-md-3 mb-3">
                            <label for="category_id" class="form-label">الفئة</label>
                            <select class="form-select" id="category_id" name="category_id">
                                <option value="">الكل</option>
                                <?php foreach ($categories as $category): ?>
                                    <option value="<?php echo $category['id']; ?>"><?php echo $category['name']; ?></option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <?php if ($isAdmin): ?>
                            <div class="col-md-3 mb-3">
                                <label for="branch_id" class="form-label">الفرع</label>
                                <select class="form-select" id="branch_id" name="branch_id">
                                    <option value="0" selected>كل الفروع</option>
                                    <?php foreach ($branches as $branch): ?>
                                        <option value="<?php echo $branch['id']; ?>">
                                            <?php echo $branch['name']; ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        <?php else: ?>
                            <input type="hidden" name="branch_id" value="<?php echo $currentBranchId; ?>">
                        <?php endif; ?>
                        <div class="col-md-3 mb-3">
                            <label for="stock_status" class="form-label">حالة المخزون</label>
                            <select class="form-select" id="stock_status" name="stock_status">
                                <option value="">الكل</option>
                                <option value="out_of_stock">نافد</option>
                                <option value="low_stock">منخفض</option>
                                <option value="available">متوفر</option>
                            </select>
                        </div>
                        <div class="col-md-3 mb-3">
                            <label for="is_for_sale" class="form-label">نوع المنتج</label>
                            <select class="form-select" id="is_for_sale" name="is_for_sale">
                                <option value="">الكل</option>
                                <option value="1">للبيع</option>
                                <option value="0">للاستخدام الداخلي</option>
                            </select>
                        </div>
                        <div class="col-md-12 mt-2">
                            <div class="d-flex justify-content-end gap-3">
                                <button type="submit" class="btn btn-primary px-4">
                                    <i class="fas fa-search me-2"></i> بحث
                                </button>
                                <button type="reset" class="btn btn-outline-secondary px-4" id="reset-filters">
                                    <i class="fas fa-redo me-2"></i> إعادة تعيين
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- جدول المخزون -->
        <div class="card">
            <div class="card-header bg-light d-flex justify-content-between align-items-center">
                <h5 class="mb-0">حالة المخزون</h5>
                <div>
                    <span class="badge bg-primary" id="total-items">0</span> منتج
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-bordered table-hover" id="inventory-table">
                        <thead class="table-light">
                            <tr>
                                <th width="5%">#</th>
                                <th width="20%">اسم المنتج</th>
                                <th width="10%">الفئة</th>
                                <th width="8%">السعر</th>
                                <th width="8%">الكمية</th>
                                <th width="10%">القيمة الإجمالية</th>
                                <th width="8%">الحد الأدنى</th>
                                <th width="8%">الحالة</th>
                                <?php if ($isAdmin): ?>
                                    <th width="8%">الفرع</th>
                                <?php endif; ?>
                                <th width="15%">الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="inventory-list">
                            <!-- سيتم تعبئتها عبر الجافاسكربت -->
                            <tr>
                                <td colspan="<?php echo $isAdmin ? '9' : '8'; ?>" class="text-center">
                                    <div class="d-flex justify-content-center py-3">
                                        <div class="spinner-border text-primary" role="status">
                                            <span class="visually-hidden">جاري التحميل...</span>
                                        </div>
                                    </div>
                                    <p class="text-muted">جاري تحميل البيانات...</p>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <!-- التصفح -->
                <nav aria-label="Page navigation" class="mt-4">
                    <ul class="pagination justify-content-center" id="pagination">
                        <!-- سيتم تعبئتها عبر الجافاسكربت -->
                    </ul>
                </nav>
            </div>
        </div>

    </div>
</div>

<!-- موديل تفاصيل حركات المخزون -->
<div class="modal fade" id="inventoryTransactionsModal" tabindex="-1" aria-labelledby="inventoryTransactionsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="inventoryTransactionsModalLabel">حركات المخزون</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <h6>المنتج: <span id="transaction-product-name"></span></h6>
                    <p>الكمية الحالية: <span id="transaction-current-quantity"></span></p>
                </div>
                <div class="table-responsive">
                    <table class="table table-bordered table-striped">
                        <thead class="table-light">
                            <tr>
                                <th>التاريخ</th>
                                <th>نوع الحركة</th>
                                <th>الكمية</th>
                                <th>الكمية السابقة</th>
                                <th>الكمية الحالية</th>
                                <th>ملاحظات</th>
                                <th>المستخدم</th>
                            </tr>
                        </thead>
                        <tbody id="transactions-list">
                            <!-- سيتم تعبئتها عبر الجافاسكربت -->
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
            </div>
        </div>
    </div>
</div>

<!-- موديل تعديل المخزون -->
<div class="modal fade" id="addTransactionModal" tabindex="-1" aria-labelledby="addTransactionModal" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="adjustStockModalLabel">تعديل مخزون المنتج</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <input type="hidden" id="adjust-product-id">
                <div class="mb-3">
                    <p><strong>المنتج:</strong> <span id="adjust-product-name"></span></p>
                    <p><strong>الكمية الحالية:</strong> <span id="adjust-current-stock"></span></p>
                </div>
                <div class="mb-3">
                    <label class="form-label">نوع العملية</label>
                    <div class="btn-group w-100" role="group">
                        <input type="radio" class="btn-check" name="operation-type" id="operation-add" value="add" autocomplete="off" checked>
                        <label class="btn btn-outline-success" for="operation-add">إضافة</label>

                        <input type="radio" class="btn-check" name="operation-type" id="operation-remove" value="remove" autocomplete="off">
                        <label class="btn btn-outline-danger" for="operation-remove">سحب</label>

                        <input type="radio" class="btn-check" name="operation-type" id="operation-adjust" value="adjust" autocomplete="off">
                        <label class="btn btn-outline-primary" for="operation-adjust">تسوية</label>
                    </div>
                </div>
                <div class="mb-3">
                    <label for="adjust-quantity" class="form-label">الكمية</label>
                    <input type="number" class="form-control" id="adjust-quantity" min="1" value="1">
                    <div class="form-text" id="quantity-help-text">أدخل الكمية المراد إضافتها للمخزون</div>
                </div>
                <div class="mb-3">
                    <label for="adjust-notes" class="form-label">ملاحظات</label>
                    <textarea class="form-control" id="adjust-notes" rows="2"></textarea>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" id="btn-save-stock-adjustment">حفظ</button>
            </div>
        </div>
    </div>
</div>

<?php
// استدعاء قالب الذيل
include_once '../../includes/templates/footer.php';
?>
<!-- جافاسكربت خاص بالصفحة -->
<script>
    // تعريف متغيرات عالمية للعملة
    var currencySymbol = '<?php echo $currencySymbol; ?>';
    var currencyName = '<?php echo $currencyName; ?>';
    // دالة عرض رسالة توست
    function showToast(type, title, message) {
        // Use simple alert as fallback
        try {
            // Check if toastr is properly loaded
            if (typeof toastr === 'object' && toastr !== null) {
                // Set toastr options
                toastr.options = {
                    closeButton: true,
                    progressBar: true,
                    positionClass: 'toast-top-left',
                    timeOut: 3000
                };

                // Show appropriate toast type
                if (type === 'success') {
                    toastr.success(message, title);
                } else if (type === 'error') {
                    toastr.error(message, title);
                } else if (type === 'warning') {
                    toastr.warning(message, title);
                } else {
                    toastr.info(message, title);
                }
                return;
            }
        } catch (e) {
            console.error('Error using toastr:', e);
        }

        // Fallback to Bootstrap alerts if toastr fails
        try {
            let alertClass = 'alert-info';
            if (type === 'success') alertClass = 'alert-success';
            if (type === 'error') alertClass = 'alert-danger';
            if (type === 'warning') alertClass = 'alert-warning';

            // Create alert container if it doesn't exist
            if (!$('#alertContainer').length) {
                $('<div id="alertContainer" class="alert-container position-fixed top-0 end-0 p-3" style="z-index: 1050;"></div>').appendTo('body');
            }

            const alertHtml = `
                <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
                    <strong>${title}</strong> ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            `;

            // Add alert to container
            $('#alertContainer').append(alertHtml);

            // Remove alert after 3 seconds
            setTimeout(function() {
                $('#alertContainer .alert').first().alert('close');
            }, 3000);
        } catch (e) {
            // Last resort: use native alert
            console.error('Error showing alert:', e);
            alert(title + ': ' + message);
        }
    }

    $(document).ready(function() {
        // المتغيرات العامة
        let currentPage = 1;
        let totalPages = 1;
        const itemsPerPage = 10;

        // تحميل إحصائيات المخزون
        loadInventoryStats();

        // تحميل بيانات المخزون عند تحميل الصفحة
        loadInventoryData();

        // معالجة نموذج البحث
        $('#inventory-search-form').on('submit', function(e) {
            e.preventDefault();
            currentPage = 1;
            loadInventoryData();
        });

        // إعادة تعيين الفلاتر
        $('#reset-filters').on('click', function() {
            $('#inventory-search-form')[0].reset();

            // إذا كان المستخدم مديرًا، قم بتعيين الفرع الافتراضي
            <?php if (!$isAdmin): ?>
            $('#branch_id').val('<?php echo $currentBranchId; ?>');
            <?php endif; ?>

            currentPage = 1;
            loadInventoryData();
        });

        // دالة تحميل إحصائيات المخزون
        function loadInventoryStats() {
            <?php if (!$isAdmin): ?>
              var  branchId  = <?php echo $currentBranchId?>;
            <?php else: ?>
              var branchId =  0;
            <?php endif; ?>


            $.ajax({
                url: '<?php echo BASE_URL; ?>api/inventory.php',
                type: 'GET',
                data: {
                    action: 'stats',
                    branch_id: branchId
                },
                dataType: 'json',
                success: function(response) {
                    if (response.status === 'success' && response.stats) {
                        const stats = response.stats;
                        $('#total-products').text(stats.total_products || 0);
                        $('#low-stock-products').text(stats.low_stock || 0);
                        $('#out-of-stock-products').text(stats.out_of_stock || 0);
                        $('#available-products').text((stats.total_products - stats.low_stock - stats.out_of_stock) || 0);

                        // إذا كان هناك قيمة إجمالية، عرضها
                        if (stats.total_value !== undefined) {
                            $('#total-inventory-value').text(formatCurrency(stats.total_value));
                        }
                    } else {
                        showToast('error', 'خطأ', response.message || 'حدث خطأ أثناء تحميل الإحصائيات');
                    }
                },
                error: function() {
                    showToast('error', 'خطأ', 'حدث خطأ في الاتصال بالخادم');
                }
            });
        }

        // دالة تحميل بيانات المخزون
        function loadInventoryData() {
            // عرض حالة التحميل
            $('#inventory-list').html(`
                <tr>
                    <td colspan="<?php echo $isAdmin ? '9' : '8'; ?>" class="text-center">
                        <div class="d-flex justify-content-center py-3">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">جاري التحميل...</span>
                            </div>
                        </div>
                        <p class="text-muted">جاري تحميل البيانات...</p>
                    </td>
                </tr>
            `);

            // إعداد بيانات البحث
            const searchData = {
                action: 'list',
                page: currentPage,
                limit: itemsPerPage,
                search: $('#search').val(),
                category_id: $('#category_id').val(),
                branch_id: $('#branch_id').val() || 0, // تعديل هنا: استخدام 0 كقيمة افتراضية للفروع (الكل)
                stock_status: $('#stock_status').val(),
                is_for_sale: $('#is_for_sale').val()
            };

            // طلب البيانات من الخادم
            $.ajax({
                url: '<?php echo BASE_URL; ?>api/inventory.php',
                type: 'GET',
                data: searchData,
                dataType: 'json',
                success: function(response) {
                    if (response.status === 'success') {
                        // تعديل هنا: استخدام response.inventory بدلاً من response.data
                        // معالجة البيانات وإزالة التكرارات قبل عرضها
                        const processedData = processInventoryData(response.inventory);

                        // عرض البيانات المعالجة
                        renderInventoryTable(processedData);

                        // إنشاء كائن pagination من البيانات المستلمة
                        // استخدام عدد العناصر الفعلية بعد معالجة التكرارات
                        const actualTotalCount = $('#branch_id').val() == 0 ? processedData.length : response.total_count;
                        const pagination = {
                            total_items: actualTotalCount,
                            total_pages: Math.ceil(actualTotalCount / itemsPerPage),
                            current_page: currentPage
                        };

                        renderPagination(pagination);
                        $('#total-items').text(actualTotalCount);

                        // تحديث الإحصائيات بعد تغيير الفلاتر
                        loadInventoryStats();
                    } else {
                        $('#inventory-list').html(`
                            <tr>
                                <td colspan="<?php echo $isAdmin ? '9' : '8'; ?>" class="text-center">
                                    <p class="text-danger">${response.message || 'حدث خطأ أثناء تحميل البيانات'}</p>
                                </td>
                            </tr>
                        `);
                    }
                },
                error: function() {
                    $('#inventory-list').html(`
                        <tr>
                            <td colspan="<?php echo $isAdmin ? '9' : '8'; ?>" class="text-center">
                                <p class="text-danger">حدث خطأ في الاتصال بالخادم</p>
                            </td>
                        </tr>
                    `);
                }
            });
        }

        /**
         * معالجة بيانات المخزون لإزالة التكرارات
         * @param {Array} data بيانات المخزون الأصلية
         * @returns {Array} بيانات المخزون بعد معالجة التكرارات
         */
        function processInventoryData(data) {
            if (!data || !Array.isArray(data) || data.length === 0) {
                return [];
            }

            // معالجة المنتجات المكررة عند اختيار "جميع الفروع"
            const branchId = $('#branch_id').val() || 0;
            if (branchId == 0) {
                // إنشاء قائمة منتجات فريدة بناءً على معرف المنتج
                const uniqueProducts = {};

                // تجميع المنتجات المتكررة
                data.forEach(item => {
                    const productId = item.product_id;

                    // إذا كان المنتج موجودًا بالفعل، قم بتحديث الكمية والفرع
                    if (uniqueProducts[productId]) {
                        // إذا كان المنتج متاحًا لجميع الفروع (branch_id = null)، استخدمه
                        if (item.branch_id === null) {
                            uniqueProducts[productId] = item;
                        }
                        // إذا كان المنتج الحالي مخصصًا لفرع محدد، أضف كميته إلى المجموع
                        else {
                            uniqueProducts[productId].quantity += parseInt(item.quantity || 0);
                            uniqueProducts[productId].branch_name = 'جميع الفروع';
                        }
                    } else {
                        // إذا لم يكن المنتج موجودًا بعد، أضفه إلى القائمة
                        uniqueProducts[productId] = { ...item };

                        // إذا كان المنتج متاحًا لجميع الفروع، قم بتحديث اسم الفرع
                        if (item.branch_id === null) {
                            uniqueProducts[productId].branch_name = 'جميع الفروع';
                        }
                    }
                });

                // تحويل المنتجات الفريدة إلى مصفوفة
                return Object.values(uniqueProducts);
            }

            // إذا كان الفرع محددًا، أرجع البيانات كما هي
            return data;
        }

        // دالة عرض جدول المخزون
        function renderInventoryTable(data) {
            // التحقق من وجود البيانات
            if (!data || !Array.isArray(data) || data.length === 0) {
                $('#inventory-list').html(`
                    <tr>
                        <td colspan="<?php echo $isAdmin ? '10' : '9'; ?>" class="text-center">
                            <p class="text-muted">لا توجد بيانات للعرض</p>
                        </td>
                    </tr>
                `);
                return;
            }

            let html = '';
            data.forEach((item, index) => {
                // تحديد لون حالة المخزون
                let stockStatusClass = 'text-success';
                let stockStatusText = 'طبيعي';

                if (item.stock_status === 'low_stock') {
                    stockStatusClass = 'text-warning';
                    stockStatusText = 'منخفض';
                } else if (item.stock_status === 'out_of_stock') {
                    stockStatusClass = 'text-danger';
                    stockStatusText = 'نفذ';
                }

                // حساب القيمة الإجمالية
                const totalValue = item.quantity * item.price;

                html += `
                    <tr>
                        <td>${(currentPage - 1) * itemsPerPage + index + 1}</td>
                        <td>${item.product_name}</td>
                        <td>${item.category_name || '-'}</td>
                        <td>${formatCurrency(item.price)}</td>
                        <td><span class="${stockStatusClass}">${item.quantity}</span></td>
                        <td>${formatCurrency(totalValue)}</td>
                        <td>${item.min_quantity}</td>
                        <td><span class="${stockStatusClass}">${stockStatusText}</span></td>
                        <?php if ($isAdmin): ?>
                        <td>${item.branch_name || '-'}</td>
                        <?php endif; ?>
                        <td>
                            <div class="btn-group btn-group-sm">
                                <button type="button" class="btn btn-outline-primary add-transaction" data-id="${item.product_id}" data-name="${item.product_name}" data-quantity="${item.quantity}">
                                    <i class="fas fa-plus-minus"></i>
                                </button>
                                <?php if ($isAdmin || hasPermission('inventory_edit')): ?>
                                <a href="<?php echo BASE_URL; ?>pages/inventory/edit.php?id=${item.product_id}" class="btn btn-outline-secondary">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <?php endif; ?>
                                <button type="button" class="btn btn-outline-info view-transactions" data-id="${item.product_id}" data-name="${item.product_name}" data-quantity="${item.quantity}">
                                    <i class="fas fa-history"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                `;
            });

            $('#inventory-list').html(html);

            // تفعيل زر عرض حركات المخزون
            $('.view-transactions').on('click', function() {
                const productId = $(this).data('id');
                const productName = $(this).data('name');
                const currentQuantity = $(this).data('quantity');

                $('#transaction-product-name').text(productName);
                $('#transaction-current-quantity').text(currentQuantity);

                // تحميل حركات المخزون للمنتج
                loadProductTransactions(productId);

                $('#inventoryTransactionsModal').modal('show');
            });

            // تفعيل زر إضافة حركة مخزون
            $('.add-transaction').on('click', function() {
                const productId = $(this).data('id');
                const productName = $(this).data('name');
                const currentQuantity = $(this).data('quantity');

                // تعيين بيانات المنتج في النموذج
                $('#adjust-product-id').val(productId);
                $('#adjust-product-name').text(productName);
                $('#adjust-current-stock').text(currentQuantity);

                // إعادة تعيين النموذج
                $('#operation-add').prop('checked', true);
                $('#adjust-quantity').val(1);
                $('#adjust-notes').val('');
                $('#quantity-help-text').text('أدخل الكمية المراد إضافتها للمخزون');

                // عرض الموديل
                $('#addTransactionModal').modal('show');
            });
        }

        // دالة عرض أزرار التصفح
        function renderPagination(pagination) {
            totalPages = pagination.total_pages;

            if (totalPages <= 1) {
                $('#pagination').html('');
                return;
            }

            let html = '';

            // زر الصفحة السابقة
            html += `
                <li class="page-item ${currentPage === 1 ? 'disabled' : ''}">
                    <a class="page-link" href="#" data-page="${currentPage - 1}" aria-label="Previous">
                        <span aria-hidden="true">&laquo;</span>
                    </a>
                </li>
            `;

            // أزرار الصفحات
            const startPage = Math.max(1, currentPage - 2);
            const endPage = Math.min(totalPages, startPage + 4);

            for (let i = startPage; i <= endPage; i++) {
                html += `
                    <li class="page-item ${i === currentPage ? 'active' : ''}">
                        <a class="page-link" href="#" data-page="${i}">${i}</a>
                    </li>
                `;
            }

            // زر الصفحة التالية
            html += `
                <li class="page-item ${currentPage === totalPages ? 'disabled' : ''}">
                    <a class="page-link" href="#" data-page="${currentPage + 1}" aria-label="Next">
                        <span aria-hidden="true">&raquo;</span>
                    </a>
                </li>
            `;

            $('#pagination').html(html);

            // تفعيل أزرار التصفح
            $('.page-link').on('click', function(e) {
                e.preventDefault();

                if ($(this).parent().hasClass('disabled')) {
                    return;
                }

                currentPage = parseInt($(this).data('page'));
                loadInventoryData();

                // التمرير إلى أعلى الجدول
                $('html, body').animate({
                    scrollTop: $('#inventory-table').offset().top - 100
                }, 200);
            });
        }

        // دالة تنسيق العملة
        function formatCurrency(amount) {
            return parseFloat(amount).toFixed(2) + ' ' + (typeof currencySymbol !== 'undefined' ? currencySymbol : '<?php echo $currencySymbol; ?>');
        }

        // تحديث البيانات عند تغيير الفرع
        $('#branch_id').on('change', function() {
            currentPage = 1;
            loadInventoryData();
        });

        // دالة تحميل حركات المخزون للمنتج
        function loadProductTransactions(productId) {
            const branchId = $('#branch_id').val() || <?php if ($isAdmin) echo '0';?>

            // عرض حالة التحميل
            $('#transactions-list').html(`
                <tr>
                    <td colspan="7" class="text-center">
                        <div class="d-flex justify-content-center py-3">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">جاري التحميل...</span>
                            </div>
                        </div>
                        <p class="text-muted">جاري تحميل البيانات...</p>
                    </td>
                </tr>
            `);

            // طلب البيانات من الخادم
            $.ajax({
                url: '<?php echo BASE_URL; ?>api/inventory.php',
                type: 'GET',
                data: {
                    action: 'product-transactions',
                    product_id: productId,
                    branch_id: branchId
                },
                dataType: 'json',
                success: function(response) {
                    if (response.status === 'success') {
                        // تعديل هنا: استخدام response.transactions بدلاً من response.data
                        renderTransactionsTable(response.transactions);
                    } else {
                        $('#transactions-list').html(`
                            <tr>
                                <td colspan="7" class="text-center">
                                    <p class="text-danger">${response.message || 'حدث خطأ أثناء تحميل البيانات'}</p>
                                </td>
                            </tr>
                        `);
                    }
                },
                error: function() {
                    $('#transactions-list').html(`
                        <tr>
                            <td colspan="7" class="text-center">
                                <p class="text-danger">حدث خطأ في الاتصال بالخادم</p>
                            </td>
                        </tr>
                    `);
                }
            });
        }

        // دالة عرض جدول حركات المخزون
        function renderTransactionsTable(data) {
            // التحقق من وجود البيانات
            if (!data || !Array.isArray(data) || data.length === 0) {
                $('#transactions-list').html(`
                    <tr>
                        <td colspan="7" class="text-center">
                            <p class="text-muted">لا توجد حركات مخزون لهذا المنتج</p>
                        </td>
                    </tr>
                `);
                return;
            }

            let html = '';
            data.forEach((item) => {
                // تحديد لون نوع الحركة
                let typeClass = item.transaction_type === 'in' ? 'text-success' : 'text-danger';
                let typeText = item.transaction_type === 'in' ? 'إضافة' : 'سحب';

                html += `
                    <tr>
                        <td>${formatDateTime(item.created_at)}</td>
                        <td><span class="${typeClass}">${typeText}</span></td>
                        <td>${item.quantity}</td>
                        <td>${item.previous_quantity}</td>
                        <td>${item.current_quantity}</td>
                        <td>${item.notes || '-'}</td>
                        <td>${item.user_name || '-'}</td>
                    </tr>
                `;
            });

            $('#transactions-list').html(html);
        }

        // دالة تنسيق التاريخ والوقت
        function formatDateTime(dateTimeStr) {
            const date = new Date(dateTimeStr);
            return date.toLocaleDateString('ar-SA') + ' ' + date.toLocaleTimeString('ar-SA');
        }

        // حفظ تعديل المخزون
        $('#btn-save-stock-adjustment').on('click', function() {
            const productId = $('#adjust-product-id').val();
            const operationType = $('input[name="operation-type"]:checked').val();
            const quantity = parseInt($('#adjust-quantity').val());
            const notes = $('#adjust-notes').val();

            if (!quantity || quantity <= 0) {
                showToast('خطأ', 'الرجاء إدخال كمية صحيحة', 'error');
                return;
            }

            // تحديد نقطة النهاية والبيانات حسب نوع العملية
            let endpoint = '';
            let data = {
                product_id: productId,
                quantity: quantity,
                notes: notes || null
            };

            if (operationType === 'add') {
                endpoint = '<?php echo BASE_URL; ?>api/inventory.php?action=add-stock';
            } else if (operationType === 'remove') {
                endpoint = '<?php echo BASE_URL; ?>api/inventory.php?action=remove-stock';
            } else if (operationType === 'adjust') {
                endpoint = '<?php echo BASE_URL; ?>api/inventory.php?action=set-stock';
            }

            // تنفيذ عملية تعديل المخزون
            $.ajax({
                url: endpoint,
                type: 'POST',
                data: data,
                dataType: 'json',
                beforeSend: function() {
                    // Disable save button and show loading indicator
                    $('#btn-save-stock-adjustment').prop('disabled', true).html('<span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span> جاري الحفظ...');
                },
                success: function(response) {
                    // Reactivate save button
                    $('#btn-save-stock-adjustment').prop('disabled', false).html('حفظ');

                    if (response.status === 'success') {
                        // Close modal
                        $('#addTransactionModal').modal('hide');

                        // Show success message
                        showToast('نجاح', response.message, 'success');

                        // Reload inventory data
                        loadInventoryData();
                    } else {
                        // Show error message
                        showToast('خطأ', response.message, 'error');
                    }
                },
                error: function(xhr, status, error) {
                    // Reactivate save button
                    $('#btn-save-stock-adjustment').prop('disabled', false).html('حفظ');

                    let errorMessage = 'حدث خطأ أثناء تعديل المخزون';

                    // Check if the response is JSON
                    const contentType = xhr.getResponseHeader('content-type');
                    if (contentType && contentType.indexOf('application/json') !== -1) {
                        try {
                            const response = JSON.parse(xhr.responseText);
                            if (response.message) {
                                errorMessage = response.message;
                            }
                        } catch (e) {
                            console.error('Error parsing JSON:', e);
                        }
                    } else {
                        console.error('Server returned non-JSON response');
                        if (xhr.status === 404) {
                            errorMessage = 'الصفحة غير موجودة (404)';
                        } else if (xhr.status === 500) {
                            errorMessage = 'خطأ في الخادم (500)';
                        }
                    }

                    // Use alternative alert if toastr is not available
                    if (typeof toastr === 'undefined') {
                        alert('خطأ: ' + errorMessage);
                    } else {
                        showToast('خطأ', errorMessage, 'error');
                    }
                }
            });
        });
    });

</script>
