<?php

/**

 * صفحة عرض تفاصيل الفاتورة

 */



// تعريف ثابت لمنع الوصول المباشر للملفات المرتبطة

define('BASEPATH', true);



// استدعاء ملفات التهيئة

require_once '../../config/init.php';



// التحقق من تسجيل الدخول

redirectIfNotLoggedIn();



// التحقق من صلاحية عرض الفواتير

if (!hasPermission('invoices_view')) {

    $_SESSION['error_message'] = 'ليس لديك صلاحية لعرض الفواتير';

    header('Location: ' . BASE_URL . 'pages/dashboard.php');

    exit;

}



// التحقق من وجود معرف الفاتورة

$invoiceId = isset($_GET['id']) ? intval($_GET['id']) : 0;



if (!$invoiceId) {

    $_SESSION['error_message'] = 'لم يتم تحديد الفاتورة المطلوبة';

    header('Location: ' . BASE_URL . 'pages/invoices/index.php');

    exit;

}



// إنشاء كائنات النماذج

$invoiceModel = new Invoice($db);



// استرجاع بيانات الفاتورة

$invoice = $invoiceModel->getInvoiceById($invoiceId);



// التحقق من وجود الفاتورة

if (!$invoice) {

    $_SESSION['error_message'] = 'الفاتورة غير موجودة أو محذوفة';

    header('Location: ' . BASE_URL . 'pages/invoices/index.php');

    exit;

}



// التحقق من صلاحية الوصول للفاتورة (للفروع)

if ($_SESSION['user_role'] !== ROLE_ADMIN && $invoice['branch_id'] != $_SESSION['user_branch_id']) {

    $_SESSION['error_message'] = 'ليس لديك صلاحية للوصول لهذه الفاتورة';

    header('Location: ' . BASE_URL . 'pages/invoices/index.php');

    exit;

}



// استرجاع عناصر الفاتورة

$invoiceItems = $invoiceModel->getInvoiceItems($invoiceId);



// حساب إجماليات الخدمات والمنتجات

$servicesTotal = 0;

$productsTotal = 0;



foreach ($invoiceItems as $item) {

    if ($item['item_type'] === 'service') {

        $servicesTotal += $item['total'];

    } else {

        $productsTotal += $item['total'];

    }

}



// عنوان الصفحة

$pageTitle = 'تفاصيل الفاتورة #' . $invoice['invoice_number'];



// استدعاء رأس الصفحة

include '../../includes/templates/header.php';

?>



<!-- بداية المحتوى الرئيسي -->

<div class="container-fluid py-4">

    <!-- حاوية التنبيهات -->

    <div id="alerts-container"></div>



    <div class="row">

        <div class="col-12">

            <div class="card mb-4">

                <div class="card-body">

                    <div class="row">

                        <div class="col-12">

                            <div class="card border-0 shadow-sm">

                                <div class="card-body d-flex justify-content-between align-items-center">

                                    <h1 class="h3 mb-0">تفاصيل الفاتورة #<?php echo htmlspecialchars($invoice['invoice_number']); ?></h1>

                                    <div class="btn-group">

                                        <a href="<?php echo BASE_URL; ?>pages/invoices/index.php" class="btn btn-secondary">

                                            <i class="fas fa-arrow-right"></i> العودة للقائمة

                                        </a>



                                        <?php if (hasPermission('invoices_print')): ?>

                                        <a href="javascript:void(0);" class="btn btn-info print-invoice">

                                            <i class="fas fa-print"></i> طباعة الفاتورة

                                        </a>

                                        <?php endif; ?>



                                        <?php if (!empty($invoice['customer_phone'])): ?>

                                        <a href="javascript:void(0);" class="btn btn-success send-invoice-notification" data-id="<?php echo $invoice['id']; ?>">

                                            <i class="fab fa-whatsapp"></i> إرسال إشعار للعميل

                                        </a>

                                        <?php endif; ?>



                                        <?php if (hasPermission('invoices_edit') && ($_SESSION['user_role'] === ROLE_ADMIN || empty($invoice['end_day_id']) || !$invoice['is_end_day_closed'])): ?>

                                        <a href="<?php echo BASE_URL; ?>pages/pos/index.php?invoice_id=<?php echo $invoice['id']; ?>" class="btn btn-primary">

                                            <i class="fas fa-edit"></i> تعديل الفاتورة

                                        </a>

                                        <?php endif; ?>

                                    </div>

                                </div>

                            </div>

                        </div>

                    </div>

                </div>

            </div>

        </div>

    </div>



    <!-- رسائل النجاح والخطأ -->

    <?php include '../../includes/templates/alerts.php'; ?>



    <!-- معلومات الفاتورة الأساسية -->

    <div class="row mb-4">

        <div class="col-lg-8">

            <div class="card border-0 shadow-sm h-100">

                <div class="card-header bg-white py-3">

                    <h5 class="mb-0">معلومات الفاتورة</h5>

                </div>

                <div class="card-body">

                    <div class="row">

                        <div class="col-md-6 mb-3">

                            <p class="text-muted mb-1">رقم الفاتورة</p>

                            <h5><?php echo htmlspecialchars($invoice['invoice_number']); ?></h5>

                        </div>

                        <div class="col-md-6 mb-3">

                            <p class="text-muted mb-1">تاريخ الإنشاء</p>

                            <h5><?php echo date('Y-m-d h:i A', strtotime($invoice['created_at'])); ?></h5>

                        </div>

                        <div class="col-md-6 mb-3">

                            <p class="text-muted mb-1">تاريخ آخر تعديل</p>

                            <h5>

                                <?php if (!empty($invoice['updated_at']) && $invoice['updated_at'] != $invoice['created_at']): ?>

                                    <?php echo date('Y-m-d h:i A', strtotime($invoice['updated_at'])); ?>

                                <?php else: ?>

                                    <span class="text-muted">لم يتم التعديل</span>

                                <?php endif; ?>

                            </h5>

                        </div>

                        <div class="col-md-6 mb-3">

                            <p class="text-muted mb-1">العميل</p>

                            <h5>

                                <?php if ($invoice['customer_id']): ?>

                                    <?php echo htmlspecialchars($invoice['customer_name']); ?>

                                <?php else: ?>

                                    <span class="text-muted">عميل غير مسجل</span>

                                <?php endif; ?>

                            </h5>

                        </div>

                        <div class="col-md-6 mb-3">

                            <p class="text-muted mb-1">الحلاق/المختص</p>

                            <h5>

                                <?php if ($invoice['employee_id']): ?>

                                    <?php echo htmlspecialchars($invoice['employee_name']); ?>

                                <?php else: ?>

                                    <span class="text-muted">-</span>

                                <?php endif; ?>

                            </h5>

                        </div>

                        <div class="col-md-6 mb-3">

                            <p class="text-muted mb-1">الكاشير</p>

                            <h5>

                                <?php if ($invoice['cashier_id']): ?>

                                    <?php echo htmlspecialchars($invoice['cashier_name']); ?>

                                <?php else: ?>

                                    <span class="text-muted">-</span>

                                <?php endif; ?>

                            </h5>

                        </div>

                        <div class="col-md-6 mb-3">

                            <p class="text-muted mb-1">الفرع</p>

                            <h5>

                                <?php if ($invoice['branch_id']): ?>

                                    <?php echo htmlspecialchars($invoice['branch_name']); ?>

                                <?php else: ?>

                                    <span class="text-muted">-</span>

                                <?php endif; ?>

                            </h5>

                        </div>

                    </div>

                </div>

            </div>

        </div>

        <div class="col-lg-4">

            <div class="card border-0 shadow-sm h-100">

                <div class="card-header bg-white py-3">

                    <h5 class="mb-0">معلومات الدفع</h5>

                </div>

                <div class="card-body">

                    <div class="row">

                        <div class="col-12 mb-3">

                            <p class="text-muted mb-1">طريقة الدفع</p>

                            <h5>

                                <?php

                                switch ($invoice['payment_method']) {

                                    case 'cash':

                                        echo '<span class="badge bg-success">نقدي</span>';

                                        break;

                                    case 'card':

                                        echo '<span class="badge bg-primary">بطاقة</span>';

                                        break;

                                    case 'other':

                                        echo '<span class="badge bg-info">أخرى</span>';

                                        break;

                                    default:

                                        echo '<span class="badge bg-secondary">غير محدد</span>';

                                }

                                ?>

                            </h5>

                        </div>

                        <div class="col-12 mb-3">

                            <p class="text-muted mb-1">حالة الدفع</p>

                            <h5>

                                <?php

                                switch ($invoice['payment_status']) {

                                    case 'paid':

                                        echo '<span class="badge bg-success">مدفوع</span>';

                                        break;

                                    case 'partial':

                                        echo '<span class="badge bg-warning">مدفوع جزئي</span>';

                                        break;

                                    case 'unpaid':

                                        echo '<span class="badge bg-danger">غير مدفوع</span>';

                                        break;

                                    default:

                                        echo '<span class="badge bg-secondary">غير محدد</span>';

                                }

                                ?>

                            </h5>

                        </div>

                        <?php if ($invoice['payment_status'] === 'partial'): ?>

                        <div class="col-12 mb-3">

                            <p class="text-muted mb-1">المبلغ المدفوع</p>

                            <h5>

                                <?php echo number_format($invoice['paid_amount'] ?? 0, 2); ?> <?php echo $currencySymbol; ?>

                            </h5>

                        </div>

                        <div class="col-12 mb-3">

                            <p class="text-muted mb-1">المبلغ المتبقي</p>

                            <h5>

                                <?php echo number_format(($invoice['final_amount'] ?? 0) - ($invoice['paid_amount'] ?? 0), 2); ?> <?php echo $currencySymbol; ?>

                            </h5>

                        </div>

                        <?php endif; ?>

                        <?php if (!empty($invoice['end_day_id'])): ?>

                        <div class="col-12 mb-3">

                            <p class="text-muted mb-1">حالة الإغلاق</p>

                            <h5>

                                <?php if ($invoice['is_end_day_closed']): ?>

                                <span class="badge bg-info">تم إغلاقها في نهاية اليوم <?php echo date('Y-m-d', strtotime($invoice['end_day_date'])); ?></span>

                                <?php else: ?>

                                <span class="badge bg-success">مرتبطة بيوم عمل مفتوح</span>

                                <?php endif; ?>

                            </h5>

                        </div>

                        <?php endif; ?>

                    </div>

                </div>

            </div>

        </div>

    </div>



    <!-- تفاصيل عناصر الفاتورة -->

    <div class="row mb-4">

        <div class="col-12">

            <div class="card border-0 shadow-sm">

                <div class="card-header bg-white py-3">

                    <h5 class="mb-0">عناصر الفاتورة</h5>

                </div>

                <div class="card-body p-0">

                    <div class="table-responsive">

                        <table class="table table-hover mb-0">

                            <thead class="table-light">

                                <tr>

                                    <th>#</th>

                                    <th>العنصر</th>

                                    <th>النوع</th>

                                    <th>الكمية</th>

                                    <th>السعر</th>

                                    <th>الخصم</th>

                                    <th>الإجمالي</th>

                                    <th>الموظف</th>

                                </tr>

                            </thead>

                            <tbody>

                                <?php if (empty($invoiceItems)): ?>

                                <tr>

                                    <td colspan="8" class="text-center py-4 text-muted">لا توجد عناصر متاحة</td>

                                </tr>

                                <?php else: ?>

                                <?php foreach ($invoiceItems as $index => $item): ?>

                                <tr>

                                    <td><?php echo $index + 1; ?></td>

                                    <td>

                                        <?php

                                        if (isset($item['item_type']) && $item['item_type'] === 'service') {

                                            echo isset($item['service_name']) ? htmlspecialchars($item['service_name']) : 'غير محدد';

                                        } else {

                                            echo isset($item['product_name']) ? htmlspecialchars($item['product_name']) : 'غير محدد';

                                        }

                                        ?>

                                    </td>

                                    <td>

                                        <?php if (isset($item['item_type']) && $item['item_type'] === 'service'): ?>

                                            <span class="badge bg-primary">خدمة</span>

                                        <?php else: ?>

                                            <span class="badge bg-info">منتج</span>

                                        <?php endif; ?>

                                    </td>

                                    <td><?php echo isset($item['quantity']) ? $item['quantity'] : 0; ?></td>

                                    <td><?php echo isset($item['price']) ? number_format($item['price'], 2) : '0.00'; ?></td>

                                    <td><?php echo isset($item['discount']) ? number_format($item['discount'], 2) : '0.00'; ?></td>

                                    <td><?php echo isset($item['total']) ? number_format($item['total'], 2) : '0.00'; ?></td>

                                    <td>

                                        <?php if (isset($item['employee_id']) && $item['employee_id']): ?>

                                            <?php echo isset($item['employee_name']) ? htmlspecialchars($item['employee_name']) : 'غير محدد'; ?>

                                        <?php else: ?>

                                            <span class="text-muted">-</span>

                                        <?php endif; ?>

                                    </td>

                                </tr>

                                <?php endforeach; ?>

                                <?php endif; ?>

                            </tbody>

                        </table>

                    </div>

                </div>

            </div>

        </div>

    </div>



    <!-- ملخص الفاتورة -->

    <div class="row">

        <div class="col-lg-6">

            <div class="card border-0 shadow-sm">

                <div class="card-header bg-white py-3">

                    <h5 class="mb-0">ملاحظات</h5>

                </div>

                <div class="card-body">

                    <?php if (!empty($invoice['notes'])): ?>

                        <p><?php echo nl2br(htmlspecialchars($invoice['notes'])); ?></p>

                    <?php else: ?>

                        <p class="text-muted">لا توجد ملاحظات</p>

                    <?php endif; ?>

                </div>

            </div>

        </div>

        <div class="col-lg-6">

            <div class="card border-0 shadow-sm">

                <div class="card-header bg-white py-3">

                    <h5 class="mb-0">ملخص الفاتورة</h5>

                </div>

                <div class="card-body">

                    <div class="row">

                        <div class="col-md-6 mb-3">

                            <p class="text-muted mb-1">إجمالي الخدمات</p>

                            <h5><?php echo number_format($servicesTotal, 2); ?></h5>

                        </div>

                        <div class="col-md-6 mb-3">

                            <p class="text-muted mb-1">إجمالي المنتجات</p>

                            <h5><?php echo number_format($productsTotal, 2); ?></h5>

                        </div>

                    </div>

                    <hr>

                    <div class="row">

                        <div class="col-md-6 mb-3">

                            <p class="text-muted mb-1">المجموع قبل الخصم</p>

                            <h5><?php echo number_format($invoice['total_amount'], 2); ?></h5>

                        </div>

                        <div class="col-md-6 mb-3">

                            <p class="text-muted mb-1">الخصم</p>

                            <h5>

                                <?php if ($invoice['discount_amount'] > 0): ?>

                                    <?php echo number_format($invoice['discount_amount'], 2); ?>

                                    <?php if ($invoice['discount_type'] === 'percentage'): ?>

                                        %

                                    <?php endif; ?>



                                    <?php

                                    // التحقق من مصدر الخصم

                                    $db->prepare("SELECT pcu.*, pc.code, pc.name

                                                 FROM promo_code_usage pcu

                                                 JOIN promo_codes pc ON pcu.promo_code_id = pc.id

                                                 WHERE pcu.invoice_id = :invoice_id LIMIT 1");

                                    $db->bind(':invoice_id', $invoice['id']);

                                    $promoCodeUsage = $db->fetch();



                                    // التحقق من وجود ملاحظات تشير إلى استخدام عرض ترويجي

                                    $isPromotionDiscount = false;

                                    $promotionInfo = null;



                                    if (!empty($invoice['notes'])) {

                                        // البحث عن نمط مثل "[PROMOTION_ID:123]"

                                        if (preg_match('/\[PROMOTION_ID:(\d+)\]/', $invoice['notes'], $matches)) {

                                            $promotionId = $matches[1];



                                            // استرجاع معلومات العرض

                                            $db->prepare("SELECT * FROM promotions WHERE id = :id LIMIT 1");

                                            $db->bind(':id', $promotionId);

                                            $promotionInfo = $db->fetch();



                                            if ($promotionInfo) {

                                                $isPromotionDiscount = true;

                                            }

                                        }

                                    }



                                    if ($promoCodeUsage): ?>

                                        <span class="badge bg-info">كود ترويج</span>

                                        <div class="small text-muted mt-1">

                                            <?php echo htmlspecialchars($promoCodeUsage['name']); ?> (<?php echo htmlspecialchars($promoCodeUsage['code']); ?>)

                                        </div>

                                    <?php elseif ($isPromotionDiscount): ?>

                                        <span class="badge bg-success">عرض ترويجي</span>

                                        <div class="small text-muted mt-1">

                                            <?php echo htmlspecialchars($promotionInfo['name']); ?>

                                        </div>

                                    <?php else: ?>

                                        <span class="badge bg-secondary">خصم يدوي</span>

                                    <?php endif; ?>

                                <?php else: ?>

                                    <span class="text-muted">0</span>

                                <?php endif; ?>

                            </h5>

                        </div>

                        <div class="col-md-6 mb-3">

                            <p class="text-muted mb-1">الضريبة</p>

                            <h5>

                                <?php if ($invoice['tax_amount'] > 0): ?>

                                    <?php echo number_format($invoice['tax_amount'], 2); ?>

                                <?php else: ?>

                                    <span class="text-muted">0</span>

                                <?php endif; ?>

                            </h5>

                        </div>

                        <div class="col-md-6 mb-3">

                            <p class="text-muted mb-1">المبلغ النهائي</p>

                            <h5 class="text-primary fw-bold"><?php echo number_format($invoice['final_amount'], 2); ?></h5>

                        </div>



                        <?php if (isset($promoCodeUsage) && $promoCodeUsage): ?>

                        <div class="col-12">

                            <div class="alert alert-info p-2 mb-0">

                                <p class="mb-0"><strong>تفاصيل كود الترويج:</strong>

                                <?php echo htmlspecialchars($promoCodeUsage['name']); ?> (<?php echo htmlspecialchars($promoCodeUsage['code']); ?>)</p>

                                <p class="mb-0 small">تم تطبيق خصم بقيمة <?php echo number_format($promoCodeUsage['discount_amount'], 2); ?> <?php echo $currencySymbol; ?></p>

                            </div>

                        </div>

                        <?php elseif (isset($isPromotionDiscount) && $isPromotionDiscount && isset($promotionInfo)): ?>

                        <div class="col-12">

                            <div class="alert alert-success p-2 mb-0">

                                <p class="mb-0"><strong>تفاصيل العرض الترويجي:</strong>

                                <?php echo htmlspecialchars($promotionInfo['name']); ?></p>

                                <p class="mb-0 small">

                                    <?php if ($promotionInfo['discount_type'] === 'percentage'): ?>

                                        خصم <?php echo htmlspecialchars($promotionInfo['discount_value']); ?>% على الفاتورة

                                    <?php else: ?>

                                        خصم ثابت بقيمة <?php echo number_format($promotionInfo['discount_value'], 2); ?> <?php echo $currencySymbol; ?>

                                    <?php endif; ?>

                                </p>

                            </div>

                        </div>

                        <?php endif; ?>

                    </div>

                </div>

            </div>

        </div>

    </div>

</div>



<!-- نافذة طباعة الفاتورة -->

<div class="modal fade" id="printInvoiceModal" tabindex="-1" aria-labelledby="printInvoiceModalLabel" aria-hidden="true">

    <div class="modal-dialog modal-lg">

        <div class="modal-content">

            <div class="modal-header">

                <h5 class="modal-title" id="printInvoiceModalLabel">طباعة الفاتورة</h5>

                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>

            </div>

            <div class="modal-body" id="printInvoiceContent">

                <div class="text-center">

                    <div class="spinner-border" role="status">

                        <span class="visually-hidden">جاري التحميل...</span>

                    </div>

                    <p class="mt-2">جاري تحضير الفاتورة للطباعة...</p>

                </div>

            </div>

            <div class="modal-footer">

                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>

                <button type="button" class="btn btn-primary" id="printInvoiceBtn">

                    <i class="fas fa-print"></i> طباعة عادية

                </button>

                <button type="button" class="btn btn-success" id="printThermalBtn">

                    <i class="fas fa-receipt"></i> طباعة حرارية

                </button>

            </div>

        </div>

    </div>

</div>



<!-- استدعاء قالب الفوتر -->

<?php include '../../includes/templates/footer.php'; ?>



<script>

$(document).ready(function() {

    // معالجة طباعة الفاتورة

    $('.print-invoice').on('click', function() {

        // عرض نافذة الطباعة

        $('#printInvoiceModal').modal('show');



        // استخدام البيانات المتاحة مباشرة من PHP

        const invoiceId = <?php echo $invoiceId; ?>;

        const invoiceData = {

            invoice_number: '<?php echo htmlspecialchars($invoice['invoice_number']); ?>',

            created_at: '<?php echo date('Y-m-d h:i A', strtotime($invoice['created_at'])); ?>',

            customer_name: '<?php echo htmlspecialchars($invoice['customer_name'] ?? "عميل غير مسجل"); ?>',

            employee_name: '<?php echo htmlspecialchars($invoice['employee_name'] ?? "-"); ?>',

            branch_name: '<?php echo htmlspecialchars($invoice['branch_name'] ?? "الفرع الرئيسي"); ?>',

            payment_method: '<?php echo $invoice['payment_method']; ?>',

            payment_status: '<?php echo $invoice['payment_status']; ?>',

            total_amount: <?php echo (float)$invoice['total_amount']; ?>,

            discount_amount: <?php echo (float)$invoice['discount_amount']; ?>,

            tax_amount: <?php echo (float)$invoice['tax_amount']; ?>,

            final_amount: <?php echo (float)$invoice['final_amount']; ?>,

            paid_amount: <?php echo (float)($invoice['paid_amount'] ?? 0); ?>,

            items: <?php echo json_encode($invoiceItems); ?>

        };



        // عرض الفاتورة للطباعة

        displayInvoiceForPrinting(invoiceData);

    });



    // تهيئة زر الطباعة الحرارية

    $('#printThermalBtn').on('click', function() {

        $(this).prop('disabled', true);

        $(this).html('<i class="fas fa-spinner fa-spin"></i> جاري الطباعة...');



        // استخدام البيانات المتاحة مباشرة من PHP

        const invoiceId = <?php echo $invoiceId; ?>;



        // استخدام مكتبة الطباعة الحرارية إذا كانت متوفرة

        if (typeof ThermalPrinter !== 'undefined') {

            // جمع بيانات الفاتورة من PHP

            const invoiceData = {

                invoice_number: '<?php echo htmlspecialchars($invoice['invoice_number']); ?>',

                created_at: '<?php echo date('Y-m-d h:i A', strtotime($invoice['created_at'])); ?>',

                customer_name: '<?php echo htmlspecialchars($invoice['customer_name'] ?? "عميل غير مسجل"); ?>',

                cashier_name: '<?php echo htmlspecialchars($invoice['cashier_name']); ?>',

                employee_name: '<?php echo htmlspecialchars($invoice['employee_name'] ?? "-"); ?>',

                branch_name: '<?php echo htmlspecialchars($invoice['branch_name'] ?? "الفرع الرئيسي"); ?>',

                branch_address: '<?php echo htmlspecialchars($invoice['branch_address'] ?? ""); ?>',

                branch_phone: '<?php echo htmlspecialchars($invoice['branch_phone'] ?? ""); ?>',

                system_name: '<?php echo htmlspecialchars(SYSTEM_NAME); ?>',

                payment_method: '<?php echo $invoice['payment_method']; ?>',

                payment_status: '<?php echo $invoice['payment_status']; ?>',

                items: <?php echo json_encode($invoiceItems); ?>,

                total_amount: <?php echo (float)$invoice['total_amount']; ?>,

                discount_amount: <?php echo (float)$invoice['discount_amount']; ?>,

                tax_amount: <?php echo (float)$invoice['tax_amount']; ?>,

                final_amount: <?php echo (float)$invoice['final_amount']; ?>,

                paid_amount: <?php echo (float)($invoice['paid_amount'] ?? 0); ?>

            };



            // طباعة الفاتورة

            ThermalPrinter.printInvoice(invoiceData)

                .then(() => {

                    showAlert('تمت الطباعة بنجاح', 'success');

                })

                .catch((error) => {

                    showAlert('حدث خطأ أثناء الطباعة: ' + error, 'danger');

                })

                .finally(() => {

                    // إعادة تعيين زر الطباعة

                    $('#printThermalBtn').html('<i class="fas fa-receipt"></i> طباعة حرارية');

                    $('#printThermalBtn').prop('disabled', false);

                });

        } else {

            showAlert('مكتبة الطباعة الحرارية غير متوفرة', 'warning');

            // إعادة تعيين زر الطباعة

            $('#printThermalBtn').html('<i class="fas fa-receipt"></i> طباعة حرارية');

            $('#printThermalBtn').prop('disabled', false);

        }

    });

});



// عرض الفاتورة للطباعة

function displayInvoiceForPrinting(invoice) {

    // تنسيق محتوى الفاتورة باستخدام السلسلة العادية بدلاً من القوالب

    let content = '<div class="invoice-print">' +

        '<div class="text-center mb-4">' +

        '<h3>' + '<?php echo SYSTEM_NAME; ?>' + '</h3>' +

        '<p>' + (invoice.branch_name || 'الفرع الرئيسي') + '</p>' +

        '</div>' +

        '<div class="row mb-3">' +

        '<div class="col-6">' +

        '<strong>رقم الفاتورة:</strong> ' + invoice.invoice_number +

        '</div>' +

        '<div class="col-6 text-end">' +

        '<strong>التاريخ:</strong> ' + invoice.created_at +

        '</div>' +

        '</div>' +

        '<div class="row mb-3">' +

        '<div class="col-6">' +

        '<strong>العميل:</strong> ' + (invoice.customer_name || 'عميل غير مسجل') +

        '</div>' +

        '<div class="col-6 text-end">' +

        '<strong>الموظف:</strong> ' + (invoice.employee_name || '-') +

        '</div>' +

        '</div>' +

        '<div class="row">' +

        '<div class="col-12">' +

        '<table class="table table-bordered">' +

        '<thead>' +

        '<tr>' +

        '<th>#</th>' +

        '<th>المنتج/الخدمة</th>' +

        '<th>الكمية</th>' +

        '<th>السعر</th>' +

        '<th>الإجمالي</th>' +

        '</tr>' +

        '</thead>' +

        '<tbody>';



    // إضافة عناصر الفاتورة باستخدام السلسلة العادية بدلاً من القوالب

    if (invoice.items && Array.isArray(invoice.items)) {

        invoice.items.forEach(function(item, index) {

            const itemName = item.service_name || item.product_name || 'غير محدد';

            const price = parseFloat(item.price || 0);

            const total = parseFloat(item.total || 0);

            content += '<tr>' +

                '<td>' + (index + 1) + '</td>' +

                '<td>' + itemName + '</td>' +

                '<td>' + (item.quantity || 0) + '</td>' +

                '<td>' + price.toFixed(2) + '</td>' +

                '<td>' + total.toFixed(2) + '</td>' +

                '</tr>';

        });

    }



    // إضافة ملخص الفاتورة باستخدام السلسلة العادية بدلاً من القوالب

    content += '</tbody>' +

            '</table>' +

            '<div class="row mt-4">' +

            '<div class="col-8"></div>' +

            '<div class="col-4">' +

            '<table class="table table-bordered">' +

            '<tbody>' +

            '<tr>' +

            '<td><strong>الإجمالي:</strong></td>' +

            '<td>' + (invoice.total_amount || 0).toFixed(2) + '</td>' +

            '</tr>';



    // إضافة الخصم إذا وجد باستخدام السلسلة العادية بدلاً من القوالب

    if (invoice.discount_amount && invoice.discount_amount > 0) {

        content += '<tr>' +

                '<td><strong>الخصم:</strong></td>' +

                '<td>' + invoice.discount_amount.toFixed(2) + '</td>' +

                '</tr>';

    }



    // إضافة الضريبة إذا وجدت باستخدام السلسلة العادية بدلاً من القوالب

    if (invoice.tax_amount && invoice.tax_amount > 0) {

        content += '<tr>' +

                '<td><strong>الضريبة:</strong></td>' +

                '<td>' + invoice.tax_amount.toFixed(2) + '</td>' +

                '</tr>';

    }



    // إضافة المبلغ النهائي باستخدام السلسلة العادية بدلاً من القوالب

    content += '<tr>' +

            '<td><strong>المبلغ النهائي:</strong></td>' +

            '<td>' + invoice.final_amount.toFixed(2) + '</td>' +

            '</tr>';



    // إضافة معلومات الدفع الجزئي إذا كان موجوداً

    if (invoice.payment_status === 'partial') {

        content += '<tr>' +

                '<td><strong>المبلغ المدفوع:</strong></td>' +

                '<td>' + (invoice.paid_amount || 0).toFixed(2) + '</td>' +

                '</tr>' +

                '<tr>' +

                '<td><strong>المبلغ المتبقي:</strong></td>' +

                '<td>' + (invoice.final_amount - (invoice.paid_amount || 0)).toFixed(2) + '</td>' +

                '</tr>';

    }



    content += '</tbody>' +

            '</table>' +

            '</div>' +

            '</div>';



    // إضافة معلومات الدفع باستخدام السلسلة العادية بدلاً من القوالب

    content += '<div class="row mt-4">' +

        '<div class="col-12">' +

        '<p>' +

        '<strong>طريقة الدفع:</strong> ' +

        (invoice.payment_method === 'cash' ? 'نقدي' :

          invoice.payment_method === 'card' ? 'بطاقة' : 'أخرى') +

        '</p>' +

        '<p>' +

        '<strong>حالة الدفع:</strong> ' +

        (invoice.payment_status === 'paid' ? 'مدفوع' :

          invoice.payment_status === 'partial' ? 'مدفوع جزئي' : 'غير مدفوع') +

        '</p>' +

        '</div>' +

        '</div>' +

        '<div class="row mt-4">' +

        '<div class="col-12">' +

        '<p class="mb-1"><strong>ملاحظات:</strong></p>' +

        '<p>' + (invoice.notes || 'لا توجد ملاحظات') + '</p>' +

        '</div>' +

        '</div>' +

        '<hr class="my-4">' +

        '<div class="row text-center">' +

        '<div class="col-12">' +

        '<p>شكراً لزيارتكم - نتطلع لخدمتكم مرة أخرى</p>' +

        '</div>' +

        '</div>' +

        '</div>';



    // عرض المحتوى في النافذة

    $('#printInvoiceContent').html(content);



    // تهيئة زر الطباعة

    $('#printInvoiceBtn').off('click').on('click', function() {

        // إنشاء نافذة طباعة للمحتوى

        const printWindow = window.open('', '_blank');



        if (!printWindow) {

            alert('يرجى السماح بالنوافذ المنبثقة لطباعة الفاتورة');

            return;

        }



        // تحضير CSS للطباعة

        const printStyles = `

            <style>

                @media print {

                    body {

                        font-family: Arial, sans-serif;

                        font-size: 12pt;

                        direction: rtl;

                    }

                    .invoice-print {

                        width: 100%;

                        max-width: 800px;

                        margin: 0 auto;

                        padding: 20px;

                    }

                    table {

                        width: 100%;

                        border-collapse: collapse;

                        margin-bottom: 20px;

                    }

                    table, th, td {

                        border: 1px solid #ddd;

                    }

                    th, td {

                        padding: 8px;

                        text-align: right;

                    }

                    th {

                        background-color: #f2f2f2;

                    }

                    .text-center {

                        text-align: center;

                    }

                    .text-end {

                        text-align: left;

                    }

                    .row {

                        display: flex;

                        flex-wrap: wrap;

                        margin-bottom: 15px;

                    }

                    .col-6 {

                        width: 50%;

                    }

                    .col-4 {

                        width: 33.333333%;

                    }

                    .col-8 {

                        width: 66.666667%;

                    }

                    .col-12 {

                        width: 100%;

                    }

                    .mb-1 {

                        margin-bottom: 4px;

                    }

                    .mb-3 {

                        margin-bottom: 12px;

                    }

                    .mb-4 {

                        margin-bottom: 16px;

                    }

                    .mt-2 {

                        margin-top: 8px;

                    }

                    .mt-4 {

                        margin-top: 16px;

                    }

                    .my-4 {

                        margin-top: 16px;

                        margin-bottom: 16px;

                    }

                    hr {

                        border: none;

                        border-top: 1px solid #ddd;

                        margin: 20px 0;

                    }

                    h3 {

                        font-size: 18pt;

                        margin-bottom: 10px;

                    }

                    h5 {

                        font-size: 14pt;

                        margin-bottom: 10px;

                    }

                }

            </style>`;



        // تحضير محتوى نافذة الطباعة باستخدام السلسلة العادية بدلاً من القوالب

        const printContent = '<!DOCTYPE html>' +

            '<html dir="rtl">' +

            '<head>' +

            '<meta charset="UTF-8">' +

            '<title>فاتورة رقم ' + invoice.invoice_number + '</title>' +

            printStyles +

            '</head>' +

            '<body>' +

            content +

            '<script>' +

            'window.onload = function() {' +

            'window.print();' +

            'setTimeout(function() { window.close(); }, 500);' +

            '};' +

            '</body>' +

            '</html>';



        // كتابة المحتوى إلى نافذة الطباعة

        printWindow.document.open();

        printWindow.document.write(printContent);

        printWindow.document.close();

    });

}



// تضمين ملف دالة التنبيهات

$.getScript('<?php echo BASE_URL; ?>pages/invoices/alert-function.js');



// معالجة إرسال إشعار الفاتورة

$(document).ready(function() {

    $('.send-invoice-notification').on('click', function() {

        var invoiceId = $(this).data('id');

        var $btn = $(this);



        // تغيير حالة الزر

        $btn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> جاري الإرسال...');



        // التأكد من تهيئة مكتبة إشعارات الفواتير

        if (typeof WhatsAppInvoiceNotification === 'undefined') {

            // إذا لم تكن مكتبة إشعارات الفواتير متوفرة

            $btn.prop('disabled', false).html('<i class="fab fa-whatsapp"></i> إرسال إشعار للعميل');

            showAlert('لم يتم تحميل مكتبة WhatsApp بشكل صحيح. يرجى تحديث الصفحة والمحاولة مرة أخرى.', 'danger');

            return;

        }



        // إرسال إشعار الفاتورة باستخدام مكتبة إشعارات الفواتير

        WhatsAppInvoiceNotification.sendInvoiceNotification(invoiceId)

            .then(function(result) {

                // إعادة الزر إلى حالته الأصلية

                $btn.prop('disabled', false).html('<i class="fab fa-whatsapp"></i> إرسال إشعار للعميل');



                // التحقق من حالة النتيجة

                if (result.status === 'warning') {

                    // عرض رسالة تحذير

                    showAlert('تم إرسال إشعار الفاتورة مع بعض التحذيرات: ' + result.message, 'warning');

                } else {

                    // عرض رسالة نجاح

                    showAlert('تم إرسال إشعار الفاتورة بنجاح', 'success');

                }

            })

            .catch(function(error) {

                console.error('خطأ في إرسال إشعار الفاتورة:', error);



                // إعادة الزر إلى حالته الأصلية

                $btn.prop('disabled', false).html('<i class="fab fa-whatsapp"></i> إرسال إشعار للعميل');



                // عرض رسالة خطأ

                showAlert('فشل إرسال إشعار الفاتورة: ' + (error.message || 'خطأ غير معروف'), 'danger');

            });

    });

});

</script>



<!-- إضافة مكتبة الطباعة الحرارية من CDN -->

<script src="https://cdn.jsdelivr.net/npm/escpos-printer-toolkit@1.0.8/dist/escpos-printer.min.js"></script>

<script src="<?php echo BASE_URL; ?>assets/js/thermal-print.js"></script>



<!-- سكريبت رسائل واتساب مخصصة -->

<script src="<?php echo BASE_URL; ?>assets/js/whatsapp-custom-message.js"></script>



<!-- مكتبة إشعارات الفواتير للواتساب -->

<script src="<?php echo BASE_URL; ?>assets/js/whatsapp-invoice-notification.js"></script>



<!-- تأكد من تهيئة مكتبة إشعارات الفواتير -->

<script>

$(document).ready(function() {

    // التأكد من وجود مكتبة إشعارات الفواتير

    if (typeof WhatsAppInvoiceNotification !== 'undefined') {

        console.log('تم تهيئة مكتبة إشعارات الفواتير');

        // التأكد من تهيئة مكتبة إشعارات الفواتير

        WhatsAppInvoiceNotification.init({

            debug: true,

            showSuccessMessage: true,

            timeout: 60000 // زيادة مهلة الانتظار إلى 60 ثانية

        });



        console.log('تم تهيئة مكتبة إشعارات الفواتير بنجاح');

    } else {

        console.error('لم يتم تحميل مكتبة إشعارات الفواتير بشكل صحيح');

    }



    // التأكد من وجود عميل الواتساب

    if (typeof WhatsAppClient !== 'undefined') {

        console.log('تم تهيئة عميل الواتساب');

        // التأكد من تهيئة عميل الواتساب

        WhatsAppClient.init({

            debug: true,

            autoReconnect: true

        });

    }

});

</script>

