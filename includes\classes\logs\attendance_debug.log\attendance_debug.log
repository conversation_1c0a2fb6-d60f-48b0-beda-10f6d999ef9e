=== بدء تنفيذ getEmployeeAttendance ===
معرف الموظف: 1
الفلاتر: {"month":2,"year":2025}
الاستعلام النهائي: SELECT a.*, e.name as employee_name 
                FROM employee_attendance a 
                JOIN employees e ON a.employee_id = e.id 
                WHERE a.employee_id = :employee_id ORDER BY a.date DESC
قيم الربط: {":employee_id":1}
عدد النتائج: 2
=== انتهاء تنفيذ getEmployeeAttendance ===

=== بدء تنفيذ getEmployeeAttendance ===
معرف الموظف: 1
الفلاتر: {"month":2,"year":2025}
تمت إضافة فلتر الشهر: 2 والسنة: 2025
الاستعلام النهائي: SELECT a.*, e.name as employee_name 
                FROM employee_attendance a 
                JOIN employees e ON a.employee_id = e.id 
                WHERE a.employee_id = :employee_id AND MONTH(a.date) = :month AND YEAR(a.date) = :year ORDER BY a.date DESC
قيم الربط: {":employee_id":1,":month":2,":year":2025}
عدد النتائج: 0
=== انتهاء تنفيذ getEmployeeAttendance ===

=== بدء تنفيذ getEmployeeAttendance ===
معرف الموظف: 1
الفلاتر: {"month":3,"year":2025}
تمت إضافة فلتر الشهر: 3 والسنة: 2025
الاستعلام النهائي: SELECT a.*, e.name as employee_name 
                FROM employee_attendance a 
                JOIN employees e ON a.employee_id = e.id 
                WHERE a.employee_id = :employee_id AND MONTH(a.date) = :month AND YEAR(a.date) = :year ORDER BY a.date DESC
قيم الربط: {":employee_id":1,":month":3,":year":2025}
عدد النتائج: 2
=== انتهاء تنفيذ getEmployeeAttendance ===

=== بدء تنفيذ getEmployeeAttendance ===
معرف الموظف: 1
الفلاتر: {"month":1,"year":2025}
تمت إضافة فلتر الشهر: 1 والسنة: 2025
الاستعلام النهائي: SELECT a.*, e.name as employee_name 
                FROM employee_attendance a 
                JOIN employees e ON a.employee_id = e.id 
                WHERE a.employee_id = :employee_id AND MONTH(a.date) = :month AND YEAR(a.date) = :year ORDER BY a.date DESC
قيم الربط: {":employee_id":1,":month":1,":year":2025}
عدد النتائج: 0
=== انتهاء تنفيذ getEmployeeAttendance ===

=== بدء تنفيذ getEmployeeAttendance ===
معرف الموظف: 1
الفلاتر: {"month":3,"year":2025}
تمت إضافة فلتر الشهر: 3 والسنة: 2025
الاستعلام النهائي: SELECT a.*, e.name as employee_name 
                FROM employee_attendance a 
                JOIN employees e ON a.employee_id = e.id 
                WHERE a.employee_id = :employee_id AND MONTH(a.date) = :month AND YEAR(a.date) = :year ORDER BY a.date DESC
قيم الربط: {":employee_id":1,":month":3,":year":2025}
عدد النتائج: 2
=== انتهاء تنفيذ getEmployeeAttendance ===

=== بدء تنفيذ getEmployeeAttendance ===
معرف الموظف: 1
الفلاتر: {"month":4,"year":2025}
تمت إضافة فلتر الشهر: 4 والسنة: 2025
الاستعلام النهائي: SELECT a.*, e.name as employee_name 
                FROM employee_attendance a 
                JOIN employees e ON a.employee_id = e.id 
                WHERE a.employee_id = :employee_id AND MONTH(a.date) = :month AND YEAR(a.date) = :year ORDER BY a.date DESC
قيم الربط: {":employee_id":1,":month":4,":year":2025}
عدد النتائج: 0
=== انتهاء تنفيذ getEmployeeAttendance ===

=== بدء تنفيذ getEmployeeAttendance ===
معرف الموظف: 1
الفلاتر: {"month":3,"year":2025}
تمت إضافة فلتر الشهر: 3 والسنة: 2025
الاستعلام النهائي: SELECT a.*, e.name as employee_name 
                FROM employee_attendance a 
                JOIN employees e ON a.employee_id = e.id 
                WHERE a.employee_id = :employee_id AND MONTH(a.date) = :month AND YEAR(a.date) = :year ORDER BY a.date DESC
قيم الربط: {":employee_id":1,":month":3,":year":2025}
عدد النتائج: 2
=== انتهاء تنفيذ getEmployeeAttendance ===

=== بدء تنفيذ getEmployeeAttendance ===
معرف الموظف: 1
الفلاتر: {"month":4,"year":2025}
تمت إضافة فلتر الشهر: 4 والسنة: 2025
الاستعلام النهائي: SELECT a.*, e.name as employee_name 
                FROM employee_attendance a 
                JOIN employees e ON a.employee_id = e.id 
                WHERE a.employee_id = :employee_id AND MONTH(a.date) = :month AND YEAR(a.date) = :year ORDER BY a.date DESC
قيم الربط: {":employee_id":1,":month":4,":year":2025}
عدد النتائج: 0
=== انتهاء تنفيذ getEmployeeAttendance ===

=== بدء تنفيذ getEmployeeAttendance ===
معرف الموظف: 1
الفلاتر: {"month":4,"year":2025}
تمت إضافة فلتر الشهر: 4 والسنة: 2025
الاستعلام النهائي: SELECT a.*, e.name as employee_name 
                FROM employee_attendance a 
                JOIN employees e ON a.employee_id = e.id 
                WHERE a.employee_id = :employee_id AND MONTH(a.date) = :month AND YEAR(a.date) = :year ORDER BY a.date DESC
قيم الربط: {":employee_id":1,":month":4,":year":2025}
عدد النتائج: 0
=== انتهاء تنفيذ getEmployeeAttendance ===

=== بدء تنفيذ getEmployeeAttendance ===
معرف الموظف: 1
الفلاتر: {"month":4,"year":2025}
تمت إضافة فلتر الشهر: 4 والسنة: 2025
الاستعلام النهائي: SELECT a.*, e.name as employee_name 
                FROM employee_attendance a 
                JOIN employees e ON a.employee_id = e.id 
                WHERE a.employee_id = :employee_id AND MONTH(a.date) = :month AND YEAR(a.date) = :year ORDER BY a.date DESC
قيم الربط: {":employee_id":1,":month":4,":year":2025}
عدد النتائج: 0
=== انتهاء تنفيذ getEmployeeAttendance ===

=== بدء تنفيذ getEmployeeAttendance ===
معرف الموظف: 1
الفلاتر: {"month":4,"year":2025}
تمت إضافة فلتر الشهر: 4 والسنة: 2025
الاستعلام النهائي: SELECT a.*, e.name as employee_name 
                FROM employee_attendance a 
                JOIN employees e ON a.employee_id = e.id 
                WHERE a.employee_id = :employee_id AND MONTH(a.date) = :month AND YEAR(a.date) = :year ORDER BY a.date DESC
قيم الربط: {":employee_id":1,":month":4,":year":2025}
عدد النتائج: 0
=== انتهاء تنفيذ getEmployeeAttendance ===

=== بدء تنفيذ getEmployeeAttendance ===
معرف الموظف: 1
الفلاتر: {"month":4,"year":2025}
تمت إضافة فلتر الشهر: 4 والسنة: 2025
الاستعلام النهائي: SELECT a.*, e.name as employee_name 
                FROM employee_attendance a 
                JOIN employees e ON a.employee_id = e.id 
                WHERE a.employee_id = :employee_id AND MONTH(a.date) = :month AND YEAR(a.date) = :year ORDER BY a.date DESC
قيم الربط: {":employee_id":1,":month":4,":year":2025}
عدد النتائج: 0
=== انتهاء تنفيذ getEmployeeAttendance ===

=== بدء تنفيذ getEmployeeAttendance ===
معرف الموظف: 1
الفلاتر: {"month":4,"year":2025}
تمت إضافة فلتر الشهر: 4 والسنة: 2025
الاستعلام النهائي: SELECT a.*, e.name as employee_name 
                FROM employee_attendance a 
                JOIN employees e ON a.employee_id = e.id 
                WHERE a.employee_id = :employee_id AND MONTH(a.date) = :month AND YEAR(a.date) = :year ORDER BY a.date DESC
قيم الربط: {":employee_id":1,":month":4,":year":2025}
عدد النتائج: 0
=== انتهاء تنفيذ getEmployeeAttendance ===

=== بدء تنفيذ getEmployeeAttendance ===
معرف الموظف: 5
الفلاتر: {"month":4,"year":2025}
تمت إضافة فلتر الشهر: 4 والسنة: 2025
الاستعلام النهائي: SELECT a.*, e.name as employee_name 
                FROM employee_attendance a 
                JOIN employees e ON a.employee_id = e.id 
                WHERE a.employee_id = :employee_id AND MONTH(a.date) = :month AND YEAR(a.date) = :year ORDER BY a.date DESC
قيم الربط: {":employee_id":5,":month":4,":year":2025}
عدد النتائج: 0
=== انتهاء تنفيذ getEmployeeAttendance ===

=== بدء تنفيذ getEmployeeAttendance ===
معرف الموظف: 1
الفلاتر: {"month":4,"year":2025}
تمت إضافة فلتر الشهر: 4 والسنة: 2025
الاستعلام النهائي: SELECT a.*, e.name as employee_name 
                FROM employee_attendance a 
                JOIN employees e ON a.employee_id = e.id 
                WHERE a.employee_id = :employee_id AND MONTH(a.date) = :month AND YEAR(a.date) = :year ORDER BY a.date DESC
قيم الربط: {":employee_id":1,":month":4,":year":2025}
عدد النتائج: 0
=== انتهاء تنفيذ getEmployeeAttendance ===

=== بدء تنفيذ getEmployeeAttendance ===
معرف الموظف: 2
الفلاتر: {"month":4,"year":2025}
تمت إضافة فلتر الشهر: 4 والسنة: 2025
الاستعلام النهائي: SELECT a.*, e.name as employee_name 
                FROM employee_attendance a 
                JOIN employees e ON a.employee_id = e.id 
                WHERE a.employee_id = :employee_id AND MONTH(a.date) = :month AND YEAR(a.date) = :year ORDER BY a.date DESC
قيم الربط: {":employee_id":2,":month":4,":year":2025}
عدد النتائج: 0
=== انتهاء تنفيذ getEmployeeAttendance ===

=== بدء تنفيذ getEmployeeAttendance ===
معرف الموظف: 2
الفلاتر: {"month":4,"year":2025}
تمت إضافة فلتر الشهر: 4 والسنة: 2025
الاستعلام النهائي: SELECT a.*, e.name as employee_name 
                FROM employee_attendance a 
                JOIN employees e ON a.employee_id = e.id 
                WHERE a.employee_id = :employee_id AND MONTH(a.date) = :month AND YEAR(a.date) = :year ORDER BY a.date DESC
قيم الربط: {":employee_id":2,":month":4,":year":2025}
عدد النتائج: 0
=== انتهاء تنفيذ getEmployeeAttendance ===

=== بدء تنفيذ getEmployeeAttendance ===
معرف الموظف: 1
الفلاتر: {"month":4,"year":2025}
تمت إضافة فلتر الشهر: 4 والسنة: 2025
الاستعلام النهائي: SELECT a.*, e.name as employee_name 
                FROM employee_attendance a 
                JOIN employees e ON a.employee_id = e.id 
                WHERE a.employee_id = :employee_id AND MONTH(a.date) = :month AND YEAR(a.date) = :year ORDER BY a.date DESC
قيم الربط: {":employee_id":1,":month":4,":year":2025}
عدد النتائج: 0
=== انتهاء تنفيذ getEmployeeAttendance ===

=== بدء تنفيذ getEmployeeAttendance ===
معرف الموظف: 5
الفلاتر: {"month":4,"year":2025}
تمت إضافة فلتر الشهر: 4 والسنة: 2025
الاستعلام النهائي: SELECT a.*, e.name as employee_name 
                FROM employee_attendance a 
                JOIN employees e ON a.employee_id = e.id 
                WHERE a.employee_id = :employee_id AND MONTH(a.date) = :month AND YEAR(a.date) = :year ORDER BY a.date DESC
قيم الربط: {":employee_id":5,":month":4,":year":2025}
عدد النتائج: 0
=== انتهاء تنفيذ getEmployeeAttendance ===

=== بدء تنفيذ getEmployeeAttendance ===
معرف الموظف: 5
الفلاتر: {"month":1,"year":2025}
تمت إضافة فلتر الشهر: 1 والسنة: 2025
الاستعلام النهائي: SELECT a.*, e.name as employee_name 
                FROM employee_attendance a 
                JOIN employees e ON a.employee_id = e.id 
                WHERE a.employee_id = :employee_id AND MONTH(a.date) = :month AND YEAR(a.date) = :year ORDER BY a.date DESC
قيم الربط: {":employee_id":5,":month":1,":year":2025}
عدد النتائج: 0
=== انتهاء تنفيذ getEmployeeAttendance ===

=== بدء تنفيذ getEmployeeAttendance ===
معرف الموظف: 5
الفلاتر: {"month":2,"year":2025}
تمت إضافة فلتر الشهر: 2 والسنة: 2025
الاستعلام النهائي: SELECT a.*, e.name as employee_name 
                FROM employee_attendance a 
                JOIN employees e ON a.employee_id = e.id 
                WHERE a.employee_id = :employee_id AND MONTH(a.date) = :month AND YEAR(a.date) = :year ORDER BY a.date DESC
قيم الربط: {":employee_id":5,":month":2,":year":2025}
عدد النتائج: 0
=== انتهاء تنفيذ getEmployeeAttendance ===

=== بدء تنفيذ getEmployeeAttendance ===
معرف الموظف: 5
الفلاتر: {"month":3,"year":2025}
تمت إضافة فلتر الشهر: 3 والسنة: 2025
الاستعلام النهائي: SELECT a.*, e.name as employee_name 
                FROM employee_attendance a 
                JOIN employees e ON a.employee_id = e.id 
                WHERE a.employee_id = :employee_id AND MONTH(a.date) = :month AND YEAR(a.date) = :year ORDER BY a.date DESC
قيم الربط: {":employee_id":5,":month":3,":year":2025}
عدد النتائج: 0
=== انتهاء تنفيذ getEmployeeAttendance ===

=== بدء تنفيذ getEmployeeAttendance ===
معرف الموظف: 5
الفلاتر: {"month":4,"year":2025}
تمت إضافة فلتر الشهر: 4 والسنة: 2025
الاستعلام النهائي: SELECT a.*, e.name as employee_name 
                FROM employee_attendance a 
                JOIN employees e ON a.employee_id = e.id 
                WHERE a.employee_id = :employee_id AND MONTH(a.date) = :month AND YEAR(a.date) = :year ORDER BY a.date DESC
قيم الربط: {":employee_id":5,":month":4,":year":2025}
عدد النتائج: 0
=== انتهاء تنفيذ getEmployeeAttendance ===

=== بدء تنفيذ getEmployeeAttendance ===
معرف الموظف: 5
الفلاتر: {"month":1,"year":2025}
تمت إضافة فلتر الشهر: 1 والسنة: 2025
الاستعلام النهائي: SELECT a.*, e.name as employee_name 
                FROM employee_attendance a 
                JOIN employees e ON a.employee_id = e.id 
                WHERE a.employee_id = :employee_id AND MONTH(a.date) = :month AND YEAR(a.date) = :year ORDER BY a.date DESC
قيم الربط: {":employee_id":5,":month":1,":year":2025}
عدد النتائج: 0
=== انتهاء تنفيذ getEmployeeAttendance ===

=== بدء تنفيذ getEmployeeAttendance ===
معرف الموظف: 1
الفلاتر: {"month":1,"year":2025}
تمت إضافة فلتر الشهر: 1 والسنة: 2025
الاستعلام النهائي: SELECT a.*, e.name as employee_name 
                FROM employee_attendance a 
                JOIN employees e ON a.employee_id = e.id 
                WHERE a.employee_id = :employee_id AND MONTH(a.date) = :month AND YEAR(a.date) = :year ORDER BY a.date DESC
قيم الربط: {":employee_id":1,":month":1,":year":2025}
عدد النتائج: 0
=== انتهاء تنفيذ getEmployeeAttendance ===

=== بدء تنفيذ getEmployeeAttendance ===
معرف الموظف: 1
الفلاتر: {"month":2,"year":2025}
تمت إضافة فلتر الشهر: 2 والسنة: 2025
الاستعلام النهائي: SELECT a.*, e.name as employee_name 
                FROM employee_attendance a 
                JOIN employees e ON a.employee_id = e.id 
                WHERE a.employee_id = :employee_id AND MONTH(a.date) = :month AND YEAR(a.date) = :year ORDER BY a.date DESC
قيم الربط: {":employee_id":1,":month":2,":year":2025}
عدد النتائج: 0
=== انتهاء تنفيذ getEmployeeAttendance ===

=== بدء تنفيذ getEmployeeAttendance ===
معرف الموظف: 1
الفلاتر: {"month":3,"year":2025}
تمت إضافة فلتر الشهر: 3 والسنة: 2025
الاستعلام النهائي: SELECT a.*, e.name as employee_name 
                FROM employee_attendance a 
                JOIN employees e ON a.employee_id = e.id 
                WHERE a.employee_id = :employee_id AND MONTH(a.date) = :month AND YEAR(a.date) = :year ORDER BY a.date DESC
قيم الربط: {":employee_id":1,":month":3,":year":2025}
عدد النتائج: 2
=== انتهاء تنفيذ getEmployeeAttendance ===

=== بدء تنفيذ getEmployeeAttendance ===
معرف الموظف: 4
الفلاتر: {"month":3,"year":2025}
تمت إضافة فلتر الشهر: 3 والسنة: 2025
الاستعلام النهائي: SELECT a.*, e.name as employee_name 
                FROM employee_attendance a 
                JOIN employees e ON a.employee_id = e.id 
                WHERE a.employee_id = :employee_id AND MONTH(a.date) = :month AND YEAR(a.date) = :year ORDER BY a.date DESC
قيم الربط: {":employee_id":4,":month":3,":year":2025}
عدد النتائج: 0
=== انتهاء تنفيذ getEmployeeAttendance ===

=== بدء تنفيذ getEmployeeAttendance ===
معرف الموظف: 2
الفلاتر: {"month":4,"year":2025}
تمت إضافة فلتر الشهر: 4 والسنة: 2025
الاستعلام النهائي: SELECT a.*, e.name as employee_name 
                FROM employee_attendance a 
                JOIN employees e ON a.employee_id = e.id 
                WHERE a.employee_id = :employee_id AND MONTH(a.date) = :month AND YEAR(a.date) = :year ORDER BY a.date DESC
قيم الربط: {":employee_id":2,":month":4,":year":2025}
عدد النتائج: 0
=== انتهاء تنفيذ getEmployeeAttendance ===

=== بدء تنفيذ getEmployeeAttendance ===
معرف الموظف: 1
الفلاتر: {"month":4,"year":2025}
تمت إضافة فلتر الشهر: 4 والسنة: 2025
الاستعلام النهائي: SELECT a.*, e.name as employee_name 
                FROM employee_attendance a 
                JOIN employees e ON a.employee_id = e.id 
                WHERE a.employee_id = :employee_id AND MONTH(a.date) = :month AND YEAR(a.date) = :year ORDER BY a.date DESC
قيم الربط: {":employee_id":1,":month":4,":year":2025}
عدد النتائج: 0
=== انتهاء تنفيذ getEmployeeAttendance ===

=== بدء تنفيذ getEmployeeAttendance ===
معرف الموظف: 3
الفلاتر: {"month":4,"year":2025}
تمت إضافة فلتر الشهر: 4 والسنة: 2025
الاستعلام النهائي: SELECT a.*, e.name as employee_name 
                FROM employee_attendance a 
                JOIN employees e ON a.employee_id = e.id 
                WHERE a.employee_id = :employee_id AND MONTH(a.date) = :month AND YEAR(a.date) = :year ORDER BY a.date DESC
قيم الربط: {":employee_id":3,":month":4,":year":2025}
عدد النتائج: 0
=== انتهاء تنفيذ getEmployeeAttendance ===

=== بدء تنفيذ getEmployeeAttendance ===
معرف الموظف: 2
الفلاتر: {"month":4,"year":2025}
تمت إضافة فلتر الشهر: 4 والسنة: 2025
الاستعلام النهائي: SELECT a.*, e.name as employee_name 
                FROM employee_attendance a 
                JOIN employees e ON a.employee_id = e.id 
                WHERE a.employee_id = :employee_id AND MONTH(a.date) = :month AND YEAR(a.date) = :year ORDER BY a.date DESC
قيم الربط: {":employee_id":2,":month":4,":year":2025}
عدد النتائج: 0
=== انتهاء تنفيذ getEmployeeAttendance ===

=== بدء تنفيذ getEmployeeAttendance ===
معرف الموظف: 1
الفلاتر: {"month":4,"year":2025}
تمت إضافة فلتر الشهر: 4 والسنة: 2025
الاستعلام النهائي: SELECT a.*, e.name as employee_name 
                FROM employee_attendance a 
                JOIN employees e ON a.employee_id = e.id 
                WHERE a.employee_id = :employee_id AND MONTH(a.date) = :month AND YEAR(a.date) = :year ORDER BY a.date DESC
قيم الربط: {":employee_id":1,":month":4,":year":2025}
عدد النتائج: 0
=== انتهاء تنفيذ getEmployeeAttendance ===

=== بدء تنفيذ getEmployeeAttendance ===
معرف الموظف: 2
الفلاتر: {"month":4,"year":2025}
تمت إضافة فلتر الشهر: 4 والسنة: 2025
الاستعلام النهائي: SELECT a.*, e.name as employee_name 
                FROM employee_attendance a 
                JOIN employees e ON a.employee_id = e.id 
                WHERE a.employee_id = :employee_id AND MONTH(a.date) = :month AND YEAR(a.date) = :year ORDER BY a.date DESC
قيم الربط: {":employee_id":2,":month":4,":year":2025}
عدد النتائج: 0
=== انتهاء تنفيذ getEmployeeAttendance ===

=== بدء تنفيذ getEmployeeAttendance ===
معرف الموظف: 3
الفلاتر: {"month":4,"year":2025}
تمت إضافة فلتر الشهر: 4 والسنة: 2025
الاستعلام النهائي: SELECT a.*, e.name as employee_name 
                FROM employee_attendance a 
                JOIN employees e ON a.employee_id = e.id 
                WHERE a.employee_id = :employee_id AND MONTH(a.date) = :month AND YEAR(a.date) = :year ORDER BY a.date DESC
قيم الربط: {":employee_id":3,":month":4,":year":2025}
عدد النتائج: 0
=== انتهاء تنفيذ getEmployeeAttendance ===

=== بدء تنفيذ getEmployeeAttendance ===
معرف الموظف: 4
الفلاتر: {"month":4,"year":2025}
تمت إضافة فلتر الشهر: 4 والسنة: 2025
الاستعلام النهائي: SELECT a.*, e.name as employee_name 
                FROM employee_attendance a 
                JOIN employees e ON a.employee_id = e.id 
                WHERE a.employee_id = :employee_id AND MONTH(a.date) = :month AND YEAR(a.date) = :year ORDER BY a.date DESC
قيم الربط: {":employee_id":4,":month":4,":year":2025}
عدد النتائج: 0
=== انتهاء تنفيذ getEmployeeAttendance ===

=== بدء تنفيذ getEmployeeAttendance ===
معرف الموظف: 1
الفلاتر: {"month":4,"year":2025}
تمت إضافة فلتر الشهر: 4 والسنة: 2025
الاستعلام النهائي: SELECT a.*, e.name as employee_name 
                FROM employee_attendance a 
                JOIN employees e ON a.employee_id = e.id 
                WHERE a.employee_id = :employee_id AND MONTH(a.date) = :month AND YEAR(a.date) = :year ORDER BY a.date DESC
قيم الربط: {":employee_id":1,":month":4,":year":2025}
عدد النتائج: 0
=== انتهاء تنفيذ getEmployeeAttendance ===

=== بدء تنفيذ getEmployeeAttendance ===
معرف الموظف: 2
الفلاتر: {"month":4,"year":2025}
تمت إضافة فلتر الشهر: 4 والسنة: 2025
الاستعلام النهائي: SELECT a.*, e.name as employee_name 
                FROM employee_attendance a 
                JOIN employees e ON a.employee_id = e.id 
                WHERE a.employee_id = :employee_id AND MONTH(a.date) = :month AND YEAR(a.date) = :year ORDER BY a.date DESC
قيم الربط: {":employee_id":2,":month":4,":year":2025}
عدد النتائج: 0
=== انتهاء تنفيذ getEmployeeAttendance ===

=== بدء تنفيذ getEmployeeAttendance ===
معرف الموظف: 2
الفلاتر: {"month":4,"year":2025}
تمت إضافة فلتر الشهر: 4 والسنة: 2025
الاستعلام النهائي: SELECT a.*, e.name as employee_name 
                FROM employee_attendance a 
                JOIN employees e ON a.employee_id = e.id 
                WHERE a.employee_id = :employee_id AND MONTH(a.date) = :month AND YEAR(a.date) = :year ORDER BY a.date DESC
قيم الربط: {":employee_id":2,":month":4,":year":2025}
عدد النتائج: 0
=== انتهاء تنفيذ getEmployeeAttendance ===

=== بدء تنفيذ getEmployeeAttendance ===
معرف الموظف: 3
الفلاتر: {"month":4,"year":2025}
تمت إضافة فلتر الشهر: 4 والسنة: 2025
الاستعلام النهائي: SELECT a.*, e.name as employee_name 
                FROM employee_attendance a 
                JOIN employees e ON a.employee_id = e.id 
                WHERE a.employee_id = :employee_id AND MONTH(a.date) = :month AND YEAR(a.date) = :year ORDER BY a.date DESC
قيم الربط: {":employee_id":3,":month":4,":year":2025}
عدد النتائج: 0
=== انتهاء تنفيذ getEmployeeAttendance ===

=== بدء تنفيذ getEmployeeAttendance ===
معرف الموظف: 6
الفلاتر: {"month":4,"year":2025}
تمت إضافة فلتر الشهر: 4 والسنة: 2025
الاستعلام النهائي: SELECT a.*, e.name as employee_name 
                FROM employee_attendance a 
                JOIN employees e ON a.employee_id = e.id 
                WHERE a.employee_id = :employee_id AND MONTH(a.date) = :month AND YEAR(a.date) = :year ORDER BY a.date DESC
قيم الربط: {":employee_id":6,":month":4,":year":2025}
عدد النتائج: 0
=== انتهاء تنفيذ getEmployeeAttendance ===

=== بدء تنفيذ getEmployeeAttendance ===
معرف الموظف: 2
الفلاتر: {"month":4,"year":2025}
تمت إضافة فلتر الشهر: 4 والسنة: 2025
الاستعلام النهائي: SELECT a.*, e.name as employee_name 
                FROM employee_attendance a 
                JOIN employees e ON a.employee_id = e.id 
                WHERE a.employee_id = :employee_id AND MONTH(a.date) = :month AND YEAR(a.date) = :year ORDER BY a.date DESC
قيم الربط: {":employee_id":2,":month":4,":year":2025}
عدد النتائج: 0
=== انتهاء تنفيذ getEmployeeAttendance ===

=== بدء تنفيذ getEmployeeAttendance ===
معرف الموظف: 1
الفلاتر: {"month":4,"year":2025}
تمت إضافة فلتر الشهر: 4 والسنة: 2025
الاستعلام النهائي: SELECT a.*, e.name as employee_name 
                FROM employee_attendance a 
                JOIN employees e ON a.employee_id = e.id 
                WHERE a.employee_id = :employee_id AND MONTH(a.date) = :month AND YEAR(a.date) = :year ORDER BY a.date DESC
قيم الربط: {":employee_id":1,":month":4,":year":2025}
عدد النتائج: 0
=== انتهاء تنفيذ getEmployeeAttendance ===

=== بدء تنفيذ getEmployeeAttendance ===
معرف الموظف: 1
الفلاتر: {"month":4,"year":2025}
تمت إضافة فلتر الشهر: 4 والسنة: 2025
الاستعلام النهائي: SELECT a.*, e.name as employee_name 
                FROM employee_attendance a 
                JOIN employees e ON a.employee_id = e.id 
                WHERE a.employee_id = :employee_id AND MONTH(a.date) = :month AND YEAR(a.date) = :year ORDER BY a.date DESC
قيم الربط: {":employee_id":1,":month":4,":year":2025}
عدد النتائج: 0
=== انتهاء تنفيذ getEmployeeAttendance ===

=== بدء تنفيذ getEmployeeAttendance ===
معرف الموظف: 1
الفلاتر: {"month":4,"year":2025}
تمت إضافة فلتر الشهر: 4 والسنة: 2025
الاستعلام النهائي: SELECT a.*, e.name as employee_name 
                FROM employee_attendance a 
                JOIN employees e ON a.employee_id = e.id 
                WHERE a.employee_id = :employee_id AND MONTH(a.date) = :month AND YEAR(a.date) = :year ORDER BY a.date DESC
قيم الربط: {":employee_id":1,":month":4,":year":2025}
عدد النتائج: 0
=== انتهاء تنفيذ getEmployeeAttendance ===

=== بدء تنفيذ getEmployeeAttendance ===
معرف الموظف: 1
الفلاتر: {"month":4,"year":2025}
تمت إضافة فلتر الشهر: 4 والسنة: 2025
الاستعلام النهائي: SELECT a.*, e.name as employee_name 
                FROM employee_attendance a 
                JOIN employees e ON a.employee_id = e.id 
                WHERE a.employee_id = :employee_id AND MONTH(a.date) = :month AND YEAR(a.date) = :year ORDER BY a.date DESC
قيم الربط: {":employee_id":1,":month":4,":year":2025}
عدد النتائج: 0
=== انتهاء تنفيذ getEmployeeAttendance ===

=== بدء تنفيذ getEmployeeAttendance ===
معرف الموظف: 1
الفلاتر: {"month":3,"year":2025}
تمت إضافة فلتر الشهر: 3 والسنة: 2025
الاستعلام النهائي: SELECT a.*, e.name as employee_name 
                FROM employee_attendance a 
                JOIN employees e ON a.employee_id = e.id 
                WHERE a.employee_id = :employee_id AND MONTH(a.date) = :month AND YEAR(a.date) = :year ORDER BY a.date DESC
قيم الربط: {":employee_id":1,":month":3,":year":2025}
عدد النتائج: 2
=== انتهاء تنفيذ getEmployeeAttendance ===

=== بدء تنفيذ getEmployeeAttendance ===
معرف الموظف: 1
الفلاتر: {"month":2,"year":2025}
تمت إضافة فلتر الشهر: 2 والسنة: 2025
الاستعلام النهائي: SELECT a.*, e.name as employee_name 
                FROM employee_attendance a 
                JOIN employees e ON a.employee_id = e.id 
                WHERE a.employee_id = :employee_id AND MONTH(a.date) = :month AND YEAR(a.date) = :year ORDER BY a.date DESC
قيم الربط: {":employee_id":1,":month":2,":year":2025}
عدد النتائج: 0
=== انتهاء تنفيذ getEmployeeAttendance ===

=== بدء تنفيذ getEmployeeAttendance ===
معرف الموظف: 1
الفلاتر: {"month":1,"year":2025}
تمت إضافة فلتر الشهر: 1 والسنة: 2025
الاستعلام النهائي: SELECT a.*, e.name as employee_name 
                FROM employee_attendance a 
                JOIN employees e ON a.employee_id = e.id 
                WHERE a.employee_id = :employee_id AND MONTH(a.date) = :month AND YEAR(a.date) = :year ORDER BY a.date DESC
قيم الربط: {":employee_id":1,":month":1,":year":2025}
عدد النتائج: 0
=== انتهاء تنفيذ getEmployeeAttendance ===

=== بدء تنفيذ getEmployeeAttendance ===
معرف الموظف: 2
الفلاتر: {"month":4,"year":2025}
تمت إضافة فلتر الشهر: 4 والسنة: 2025
الاستعلام النهائي: SELECT a.*, e.name as employee_name
                FROM employee_attendance a
                JOIN employees e ON a.employee_id = e.id
                WHERE a.employee_id = :employee_id AND MONTH(a.date) = :month AND YEAR(a.date) = :year ORDER BY a.date DESC
قيم الربط: {":employee_id":2,":month":4,":year":2025}
عدد النتائج: 0
=== انتهاء تنفيذ getEmployeeAttendance ===

=== بدء تنفيذ getEmployeeAttendance ===
معرف الموظف: 2
الفلاتر: {"month":3,"year":2025}
تمت إضافة فلتر الشهر: 3 والسنة: 2025
الاستعلام النهائي: SELECT a.*, e.name as employee_name
                FROM employee_attendance a
                JOIN employees e ON a.employee_id = e.id
                WHERE a.employee_id = :employee_id AND MONTH(a.date) = :month AND YEAR(a.date) = :year ORDER BY a.date DESC
قيم الربط: {":employee_id":2,":month":3,":year":2025}
عدد النتائج: 1
=== انتهاء تنفيذ getEmployeeAttendance ===

=== بدء تنفيذ getEmployeeAttendance ===
معرف الموظف: 2
الفلاتر: {"month":2,"year":2025}
تمت إضافة فلتر الشهر: 2 والسنة: 2025
الاستعلام النهائي: SELECT a.*, e.name as employee_name
                FROM employee_attendance a
                JOIN employees e ON a.employee_id = e.id
                WHERE a.employee_id = :employee_id AND MONTH(a.date) = :month AND YEAR(a.date) = :year ORDER BY a.date DESC
قيم الربط: {":employee_id":2,":month":2,":year":2025}
عدد النتائج: 0
=== انتهاء تنفيذ getEmployeeAttendance ===

=== بدء تنفيذ getEmployeeAttendance ===
معرف الموظف: 2
الفلاتر: {"month":3,"year":2025}
تمت إضافة فلتر الشهر: 3 والسنة: 2025
الاستعلام النهائي: SELECT a.*, e.name as employee_name
                FROM employee_attendance a
                JOIN employees e ON a.employee_id = e.id
                WHERE a.employee_id = :employee_id AND MONTH(a.date) = :month AND YEAR(a.date) = :year ORDER BY a.date DESC
قيم الربط: {":employee_id":2,":month":3,":year":2025}
عدد النتائج: 1
=== انتهاء تنفيذ getEmployeeAttendance ===

=== بدء تنفيذ getEmployeeAttendance ===
معرف الموظف: 1
الفلاتر: {"month":4,"year":2025}
تمت إضافة فلتر الشهر: 4 والسنة: 2025
الاستعلام النهائي: SELECT a.*, e.name as employee_name
                FROM employee_attendance a
                JOIN employees e ON a.employee_id = e.id
                WHERE a.employee_id = :employee_id AND MONTH(a.date) = :month AND YEAR(a.date) = :year ORDER BY a.date DESC
قيم الربط: {":employee_id":1,":month":4,":year":2025}
عدد النتائج: 0
=== انتهاء تنفيذ getEmployeeAttendance ===

=== بدء تنفيذ getEmployeeAttendance ===
معرف الموظف: 1
الفلاتر: {"month":4,"year":2025}
تمت إضافة فلتر الشهر: 4 والسنة: 2025
الاستعلام النهائي: SELECT a.*, e.name as employee_name
                FROM employee_attendance a
                JOIN employees e ON a.employee_id = e.id
                WHERE a.employee_id = :employee_id AND MONTH(a.date) = :month AND YEAR(a.date) = :year ORDER BY a.date DESC
قيم الربط: {":employee_id":1,":month":4,":year":2025}
عدد النتائج: 0
=== انتهاء تنفيذ getEmployeeAttendance ===

=== بدء تنفيذ getEmployeeAttendance ===
معرف الموظف: 11
الفلاتر: {"month":4,"year":2025}
تمت إضافة فلتر الشهر: 4 والسنة: 2025
الاستعلام النهائي: SELECT a.*, e.name as employee_name
                FROM employee_attendance a
                JOIN employees e ON a.employee_id = e.id
                WHERE a.employee_id = :employee_id AND MONTH(a.date) = :month AND YEAR(a.date) = :year ORDER BY a.date DESC
قيم الربط: {":employee_id":11,":month":4,":year":2025}
عدد النتائج: 0
=== انتهاء تنفيذ getEmployeeAttendance ===

=== بدء تنفيذ getEmployeeAttendance ===
معرف الموظف: 11
الفلاتر: {"month":4,"year":2025}
تمت إضافة فلتر الشهر: 4 والسنة: 2025
الاستعلام النهائي: SELECT a.*, e.name as employee_name
                FROM employee_attendance a
                JOIN employees e ON a.employee_id = e.id
                WHERE a.employee_id = :employee_id AND MONTH(a.date) = :month AND YEAR(a.date) = :year ORDER BY a.date DESC
قيم الربط: {":employee_id":11,":month":4,":year":2025}
عدد النتائج: 0
=== انتهاء تنفيذ getEmployeeAttendance ===

=== بدء تنفيذ getEmployeeAttendance ===
معرف الموظف: 1
الفلاتر: {"month":4,"year":2025}
تمت إضافة فلتر الشهر: 4 والسنة: 2025
الاستعلام النهائي: SELECT a.*, e.name as employee_name
                FROM employee_attendance a
                JOIN employees e ON a.employee_id = e.id
                WHERE a.employee_id = :employee_id AND MONTH(a.date) = :month AND YEAR(a.date) = :year ORDER BY a.date DESC
قيم الربط: {":employee_id":1,":month":4,":year":2025}
عدد النتائج: 0
=== انتهاء تنفيذ getEmployeeAttendance ===

=== بدء تنفيذ getEmployeeAttendance ===
معرف الموظف: 1
الفلاتر: {"month":4,"year":2025}
تمت إضافة فلتر الشهر: 4 والسنة: 2025
الاستعلام النهائي: SELECT a.*, e.name as employee_name
                FROM employee_attendance a
                JOIN employees e ON a.employee_id = e.id
                WHERE a.employee_id = :employee_id AND MONTH(a.date) = :month AND YEAR(a.date) = :year ORDER BY a.date DESC
قيم الربط: {":employee_id":1,":month":4,":year":2025}
عدد النتائج: 0
=== انتهاء تنفيذ getEmployeeAttendance ===

=== بدء تنفيذ getEmployeeAttendance ===
معرف الموظف: 1
الفلاتر: {"month":4,"year":2025}
تمت إضافة فلتر الشهر: 4 والسنة: 2025
الاستعلام النهائي: SELECT a.*, e.name as employee_name
                FROM employee_attendance a
                JOIN employees e ON a.employee_id = e.id
                WHERE a.employee_id = :employee_id AND MONTH(a.date) = :month AND YEAR(a.date) = :year ORDER BY a.date DESC
قيم الربط: {":employee_id":1,":month":4,":year":2025}
عدد النتائج: 0
=== انتهاء تنفيذ getEmployeeAttendance ===

=== بدء تنفيذ getEmployeeAttendance ===
معرف الموظف: 1
الفلاتر: {"month":4,"year":2025}
تمت إضافة فلتر الشهر: 4 والسنة: 2025
الاستعلام النهائي: SELECT a.*, e.name as employee_name
                FROM employee_attendance a
                JOIN employees e ON a.employee_id = e.id
                WHERE a.employee_id = :employee_id AND MONTH(a.date) = :month AND YEAR(a.date) = :year ORDER BY a.date DESC
قيم الربط: {":employee_id":1,":month":4,":year":2025}
عدد النتائج: 0
=== انتهاء تنفيذ getEmployeeAttendance ===

=== بدء تنفيذ getEmployeeAttendance ===
معرف الموظف: 1
الفلاتر: {"month":4,"year":2025}
تمت إضافة فلتر الشهر: 4 والسنة: 2025
الاستعلام النهائي: SELECT a.*, e.name as employee_name
                FROM employee_attendance a
                JOIN employees e ON a.employee_id = e.id
                WHERE a.employee_id = :employee_id AND MONTH(a.date) = :month AND YEAR(a.date) = :year ORDER BY a.date DESC
قيم الربط: {":employee_id":1,":month":4,":year":2025}
عدد النتائج: 0
=== انتهاء تنفيذ getEmployeeAttendance ===

=== بدء تنفيذ getEmployeeAttendance ===
معرف الموظف: 1
الفلاتر: {"month":4,"year":2025}
تمت إضافة فلتر الشهر: 4 والسنة: 2025
الاستعلام النهائي: SELECT a.*, e.name as employee_name
                FROM employee_attendance a
                JOIN employees e ON a.employee_id = e.id
                WHERE a.employee_id = :employee_id AND MONTH(a.date) = :month AND YEAR(a.date) = :year ORDER BY a.date DESC
قيم الربط: {":employee_id":1,":month":4,":year":2025}
عدد النتائج: 0
=== انتهاء تنفيذ getEmployeeAttendance ===

=== بدء تنفيذ getEmployeeAttendance ===
معرف الموظف: 1
الفلاتر: {"month":4,"year":2025}
تمت إضافة فلتر الشهر: 4 والسنة: 2025
الاستعلام النهائي: SELECT a.*, e.name as employee_name
                FROM employee_attendance a
                JOIN employees e ON a.employee_id = e.id
                WHERE a.employee_id = :employee_id AND MONTH(a.date) = :month AND YEAR(a.date) = :year ORDER BY a.date DESC
قيم الربط: {":employee_id":1,":month":4,":year":2025}
عدد النتائج: 0
=== انتهاء تنفيذ getEmployeeAttendance ===

=== بدء تنفيذ getEmployeeAttendance ===
معرف الموظف: 1
الفلاتر: {"month":4,"year":2025}
تمت إضافة فلتر الشهر: 4 والسنة: 2025
الاستعلام النهائي: SELECT a.*, e.name as employee_name
                FROM employee_attendance a
                JOIN employees e ON a.employee_id = e.id
                WHERE a.employee_id = :employee_id AND MONTH(a.date) = :month AND YEAR(a.date) = :year ORDER BY a.date DESC
قيم الربط: {":employee_id":1,":month":4,":year":2025}
عدد النتائج: 0
=== انتهاء تنفيذ getEmployeeAttendance ===

=== بدء تنفيذ getEmployeeAttendance ===
معرف الموظف: 1
الفلاتر: {"month":4,"year":2025}
تمت إضافة فلتر الشهر: 4 والسنة: 2025
الاستعلام النهائي: SELECT a.*, e.name as employee_name
                FROM employee_attendance a
                JOIN employees e ON a.employee_id = e.id
                WHERE a.employee_id = :employee_id AND MONTH(a.date) = :month AND YEAR(a.date) = :year ORDER BY a.date DESC
قيم الربط: {":employee_id":1,":month":4,":year":2025}
عدد النتائج: 0
=== انتهاء تنفيذ getEmployeeAttendance ===

=== بدء تنفيذ getEmployeeAttendance ===
معرف الموظف: 1
الفلاتر: {"month":4,"year":2025}
تمت إضافة فلتر الشهر: 4 والسنة: 2025
الاستعلام النهائي: SELECT a.*, e.name as employee_name
                FROM employee_attendance a
                JOIN employees e ON a.employee_id = e.id
                WHERE a.employee_id = :employee_id AND MONTH(a.date) = :month AND YEAR(a.date) = :year ORDER BY a.date DESC
قيم الربط: {":employee_id":1,":month":4,":year":2025}
عدد النتائج: 0
=== انتهاء تنفيذ getEmployeeAttendance ===

=== بدء تنفيذ getEmployeeAttendance ===
معرف الموظف: 1
الفلاتر: {"month":4,"year":2025}
تمت إضافة فلتر الشهر: 4 والسنة: 2025
الاستعلام النهائي: SELECT a.*, e.name as employee_name
                FROM employee_attendance a
                JOIN employees e ON a.employee_id = e.id
                WHERE a.employee_id = :employee_id AND MONTH(a.date) = :month AND YEAR(a.date) = :year ORDER BY a.date DESC
قيم الربط: {":employee_id":1,":month":4,":year":2025}
عدد النتائج: 0
=== انتهاء تنفيذ getEmployeeAttendance ===

=== بدء تنفيذ getEmployeeAttendance ===
معرف الموظف: 1
الفلاتر: {"month":4,"year":2025}
تمت إضافة فلتر الشهر: 4 والسنة: 2025
الاستعلام النهائي: SELECT a.*, e.name as employee_name
                FROM employee_attendance a
                JOIN employees e ON a.employee_id = e.id
                WHERE a.employee_id = :employee_id AND MONTH(a.date) = :month AND YEAR(a.date) = :year ORDER BY a.date DESC
قيم الربط: {":employee_id":1,":month":4,":year":2025}
عدد النتائج: 0
=== انتهاء تنفيذ getEmployeeAttendance ===

=== بدء تنفيذ getEmployeeAttendance ===
معرف الموظف: 1
الفلاتر: {"month":4,"year":2025}
تمت إضافة فلتر الشهر: 4 والسنة: 2025
الاستعلام النهائي: SELECT a.*, e.name as employee_name
                FROM employee_attendance a
                JOIN employees e ON a.employee_id = e.id
                WHERE a.employee_id = :employee_id AND MONTH(a.date) = :month AND YEAR(a.date) = :year ORDER BY a.date DESC
قيم الربط: {":employee_id":1,":month":4,":year":2025}
عدد النتائج: 0
=== انتهاء تنفيذ getEmployeeAttendance ===

=== بدء تنفيذ getEmployeeAttendance ===
معرف الموظف: 1
الفلاتر: {"month":4,"year":2025}
تمت إضافة فلتر الشهر: 4 والسنة: 2025
الاستعلام النهائي: SELECT a.*, e.name as employee_name
                FROM employee_attendance a
                JOIN employees e ON a.employee_id = e.id
                WHERE a.employee_id = :employee_id AND MONTH(a.date) = :month AND YEAR(a.date) = :year ORDER BY a.date DESC
قيم الربط: {":employee_id":1,":month":4,":year":2025}
عدد النتائج: 0
=== انتهاء تنفيذ getEmployeeAttendance ===

=== بدء تنفيذ getEmployeeAttendance ===
معرف الموظف: 1
الفلاتر: {"month":4,"year":2025}
تمت إضافة فلتر الشهر: 4 والسنة: 2025
الاستعلام النهائي: SELECT a.*, e.name as employee_name
                FROM employee_attendance a
                JOIN employees e ON a.employee_id = e.id
                WHERE a.employee_id = :employee_id AND MONTH(a.date) = :month AND YEAR(a.date) = :year ORDER BY a.date DESC
قيم الربط: {":employee_id":1,":month":4,":year":2025}
عدد النتائج: 0
=== انتهاء تنفيذ getEmployeeAttendance ===

=== بدء تنفيذ getEmployeeAttendance ===
معرف الموظف: 1
الفلاتر: {"month":4,"year":2025}
تمت إضافة فلتر الشهر: 4 والسنة: 2025
الاستعلام النهائي: SELECT a.*, e.name as employee_name
                FROM employee_attendance a
                JOIN employees e ON a.employee_id = e.id
                WHERE a.employee_id = :employee_id AND MONTH(a.date) = :month AND YEAR(a.date) = :year ORDER BY a.date DESC
قيم الربط: {":employee_id":1,":month":4,":year":2025}
عدد النتائج: 0
=== انتهاء تنفيذ getEmployeeAttendance ===

=== بدء تنفيذ getEmployeeAttendance ===
معرف الموظف: 1
الفلاتر: {"month":2,"year":2025}
تمت إضافة فلتر الشهر: 2 والسنة: 2025
الاستعلام النهائي: SELECT a.*, e.name as employee_name
                FROM employee_attendance a
                JOIN employees e ON a.employee_id = e.id
                WHERE a.employee_id = :employee_id AND MONTH(a.date) = :month AND YEAR(a.date) = :year ORDER BY a.date DESC
قيم الربط: {":employee_id":1,":month":2,":year":2025}
عدد النتائج: 0
=== انتهاء تنفيذ getEmployeeAttendance ===

=== بدء تنفيذ getEmployeeAttendance ===
معرف الموظف: 1
الفلاتر: {"month":3,"year":2025}
تمت إضافة فلتر الشهر: 3 والسنة: 2025
الاستعلام النهائي: SELECT a.*, e.name as employee_name
                FROM employee_attendance a
                JOIN employees e ON a.employee_id = e.id
                WHERE a.employee_id = :employee_id AND MONTH(a.date) = :month AND YEAR(a.date) = :year ORDER BY a.date DESC
قيم الربط: {":employee_id":1,":month":3,":year":2025}
عدد النتائج: 1
=== انتهاء تنفيذ getEmployeeAttendance ===

=== بدء تنفيذ getEmployeeAttendance ===
معرف الموظف: 7
الفلاتر: {"month":4,"year":2025}
تمت إضافة فلتر الشهر: 4 والسنة: 2025
الاستعلام النهائي: SELECT a.*, e.name as employee_name
                FROM employee_attendance a
                JOIN employees e ON a.employee_id = e.id
                WHERE a.employee_id = :employee_id AND MONTH(a.date) = :month AND YEAR(a.date) = :year ORDER BY a.date DESC
قيم الربط: {":employee_id":7,":month":4,":year":2025}
عدد النتائج: 0
=== انتهاء تنفيذ getEmployeeAttendance ===

=== بدء تنفيذ getEmployeeAttendance ===
معرف الموظف: 7
الفلاتر: {"month":4,"year":2025}
تمت إضافة فلتر الشهر: 4 والسنة: 2025
الاستعلام النهائي: SELECT a.*, e.name as employee_name
                FROM employee_attendance a
                JOIN employees e ON a.employee_id = e.id
                WHERE a.employee_id = :employee_id AND MONTH(a.date) = :month AND YEAR(a.date) = :year ORDER BY a.date DESC
قيم الربط: {":employee_id":7,":month":4,":year":2025}
عدد النتائج: 0
=== انتهاء تنفيذ getEmployeeAttendance ===

=== بدء تنفيذ getEmployeeAttendance ===
معرف الموظف: 1
الفلاتر: {"month":4,"year":2025}
تمت إضافة فلتر الشهر: 4 والسنة: 2025
الاستعلام النهائي: SELECT a.*, e.name as employee_name
                FROM employee_attendance a
                JOIN employees e ON a.employee_id = e.id
                WHERE a.employee_id = :employee_id AND MONTH(a.date) = :month AND YEAR(a.date) = :year ORDER BY a.date DESC
قيم الربط: {":employee_id":1,":month":4,":year":2025}
عدد النتائج: 0
=== انتهاء تنفيذ getEmployeeAttendance ===

=== بدء تنفيذ getEmployeeAttendance ===
معرف الموظف: 7
الفلاتر: {"month":4,"year":2025}
تمت إضافة فلتر الشهر: 4 والسنة: 2025
الاستعلام النهائي: SELECT a.*, e.name as employee_name
                FROM employee_attendance a
                JOIN employees e ON a.employee_id = e.id
                WHERE a.employee_id = :employee_id AND MONTH(a.date) = :month AND YEAR(a.date) = :year ORDER BY a.date DESC
قيم الربط: {":employee_id":7,":month":4,":year":2025}
عدد النتائج: 0
=== انتهاء تنفيذ getEmployeeAttendance ===

=== بدء تنفيذ getEmployeeAttendance ===
معرف الموظف: 10
الفلاتر: {"month":4,"year":2025}
تمت إضافة فلتر الشهر: 4 والسنة: 2025
الاستعلام النهائي: SELECT a.*, e.name as employee_name
                FROM employee_attendance a
                JOIN employees e ON a.employee_id = e.id
                WHERE a.employee_id = :employee_id AND MONTH(a.date) = :month AND YEAR(a.date) = :year ORDER BY a.date DESC
قيم الربط: {":employee_id":10,":month":4,":year":2025}
عدد النتائج: 0
=== انتهاء تنفيذ getEmployeeAttendance ===

=== بدء تنفيذ getEmployeeAttendance ===
معرف الموظف: 1
الفلاتر: {"month":4,"year":2025}
تمت إضافة فلتر الشهر: 4 والسنة: 2025
الاستعلام النهائي: SELECT a.*, e.name as employee_name
                FROM employee_attendance a
                JOIN employees e ON a.employee_id = e.id
                WHERE a.employee_id = :employee_id AND MONTH(a.date) = :month AND YEAR(a.date) = :year ORDER BY a.date DESC
قيم الربط: {":employee_id":1,":month":4,":year":2025}
عدد النتائج: 0
=== انتهاء تنفيذ getEmployeeAttendance ===

=== بدء تنفيذ getEmployeeAttendance ===
معرف الموظف: 1
الفلاتر: {"month":4,"year":2025}
تمت إضافة فلتر الشهر: 4 والسنة: 2025
الاستعلام النهائي: SELECT a.*, e.name as employee_name
                FROM employee_attendance a
                JOIN employees e ON a.employee_id = e.id
                WHERE a.employee_id = :employee_id AND MONTH(a.date) = :month AND YEAR(a.date) = :year ORDER BY a.date DESC
قيم الربط: {":employee_id":1,":month":4,":year":2025}
عدد النتائج: 0
=== انتهاء تنفيذ getEmployeeAttendance ===

=== بدء تنفيذ getEmployeeAttendance ===
معرف الموظف: 1
الفلاتر: {"month":4,"year":2025}
تمت إضافة فلتر الشهر: 4 والسنة: 2025
الاستعلام النهائي: SELECT a.*, e.name as employee_name
                FROM employee_attendance a
                JOIN employees e ON a.employee_id = e.id
                WHERE a.employee_id = :employee_id AND MONTH(a.date) = :month AND YEAR(a.date) = :year ORDER BY a.date DESC
قيم الربط: {":employee_id":1,":month":4,":year":2025}
عدد النتائج: 0
=== انتهاء تنفيذ getEmployeeAttendance ===

=== بدء تنفيذ getEmployeeAttendance ===
معرف الموظف: 1
الفلاتر: {"month":4,"year":2025}
تمت إضافة فلتر الشهر: 4 والسنة: 2025
الاستعلام النهائي: SELECT a.*, e.name as employee_name
                FROM employee_attendance a
                JOIN employees e ON a.employee_id = e.id
                WHERE a.employee_id = :employee_id AND MONTH(a.date) = :month AND YEAR(a.date) = :year ORDER BY a.date DESC
قيم الربط: {":employee_id":1,":month":4,":year":2025}
عدد النتائج: 0
=== انتهاء تنفيذ getEmployeeAttendance ===

=== بدء تنفيذ getEmployeeAttendance ===
معرف الموظف: 1
الفلاتر: {"month":4,"year":2025}
تمت إضافة فلتر الشهر: 4 والسنة: 2025
الاستعلام النهائي: SELECT a.*, e.name as employee_name
                FROM employee_attendance a
                JOIN employees e ON a.employee_id = e.id
                WHERE a.employee_id = :employee_id AND MONTH(a.date) = :month AND YEAR(a.date) = :year ORDER BY a.date DESC
قيم الربط: {":employee_id":1,":month":4,":year":2025}
عدد النتائج: 0
=== انتهاء تنفيذ getEmployeeAttendance ===

=== بدء تنفيذ getEmployeeAttendance ===
معرف الموظف: 1
الفلاتر: {"month":4,"year":2025}
تمت إضافة فلتر الشهر: 4 والسنة: 2025
الاستعلام النهائي: SELECT a.*, e.name as employee_name
                FROM employee_attendance a
                JOIN employees e ON a.employee_id = e.id
                WHERE a.employee_id = :employee_id AND MONTH(a.date) = :month AND YEAR(a.date) = :year ORDER BY a.date DESC
قيم الربط: {":employee_id":1,":month":4,":year":2025}
عدد النتائج: 0
=== انتهاء تنفيذ getEmployeeAttendance ===

=== بدء تنفيذ getEmployeeAttendance ===
معرف الموظف: 2
الفلاتر: {"month":4,"year":2025}
تمت إضافة فلتر الشهر: 4 والسنة: 2025
الاستعلام النهائي: SELECT a.*, e.name as employee_name
                FROM employee_attendance a
                JOIN employees e ON a.employee_id = e.id
                WHERE a.employee_id = :employee_id AND MONTH(a.date) = :month AND YEAR(a.date) = :year ORDER BY a.date DESC
قيم الربط: {":employee_id":2,":month":4,":year":2025}
عدد النتائج: 0
=== انتهاء تنفيذ getEmployeeAttendance ===

=== بدء تنفيذ getEmployeeAttendance ===
معرف الموظف: 1
الفلاتر: {"month":4,"year":2025}
تمت إضافة فلتر الشهر: 4 والسنة: 2025
الاستعلام النهائي: SELECT a.*, e.name as employee_name
                FROM employee_attendance a
                JOIN employees e ON a.employee_id = e.id
                WHERE a.employee_id = :employee_id AND MONTH(a.date) = :month AND YEAR(a.date) = :year ORDER BY a.date DESC
قيم الربط: {":employee_id":1,":month":4,":year":2025}
عدد النتائج: 0
=== انتهاء تنفيذ getEmployeeAttendance ===

=== بدء تنفيذ getEmployeeAttendance ===
معرف الموظف: 1
الفلاتر: {"month":4,"year":2025}
تمت إضافة فلتر الشهر: 4 والسنة: 2025
الاستعلام النهائي: SELECT a.*, e.name as employee_name
                FROM employee_attendance a
                JOIN employees e ON a.employee_id = e.id
                WHERE a.employee_id = :employee_id AND MONTH(a.date) = :month AND YEAR(a.date) = :year ORDER BY a.date DESC
قيم الربط: {":employee_id":1,":month":4,":year":2025}
عدد النتائج: 0
=== انتهاء تنفيذ getEmployeeAttendance ===

=== بدء تنفيذ getEmployeeAttendance ===
معرف الموظف: 1
الفلاتر: {"month":4,"year":2025}
تمت إضافة فلتر الشهر: 4 والسنة: 2025
الاستعلام النهائي: SELECT a.*, e.name as employee_name
                FROM employee_attendance a
                JOIN employees e ON a.employee_id = e.id
                WHERE a.employee_id = :employee_id AND MONTH(a.date) = :month AND YEAR(a.date) = :year ORDER BY a.date DESC
قيم الربط: {":employee_id":1,":month":4,":year":2025}
عدد النتائج: 0
=== انتهاء تنفيذ getEmployeeAttendance ===

=== بدء تنفيذ getEmployeeAttendance ===
معرف الموظف: 1
الفلاتر: {"month":4,"year":2025}
تمت إضافة فلتر الشهر: 4 والسنة: 2025
الاستعلام النهائي: SELECT a.*, e.name as employee_name
                FROM employee_attendance a
                JOIN employees e ON a.employee_id = e.id
                WHERE a.employee_id = :employee_id AND MONTH(a.date) = :month AND YEAR(a.date) = :year ORDER BY a.date DESC
قيم الربط: {":employee_id":1,":month":4,":year":2025}
عدد النتائج: 0
=== انتهاء تنفيذ getEmployeeAttendance ===

=== بدء تنفيذ getEmployeeAttendance ===
معرف الموظف: 13
الفلاتر: {"month":4,"year":2025}
تمت إضافة فلتر الشهر: 4 والسنة: 2025
الاستعلام النهائي: SELECT a.*, e.name as employee_name
                FROM employee_attendance a
                JOIN employees e ON a.employee_id = e.id
                WHERE a.employee_id = :employee_id AND MONTH(a.date) = :month AND YEAR(a.date) = :year ORDER BY a.date DESC
قيم الربط: {":employee_id":13,":month":4,":year":2025}
عدد النتائج: 0
=== انتهاء تنفيذ getEmployeeAttendance ===

=== بدء تنفيذ getEmployeeAttendance ===
معرف الموظف: 1
الفلاتر: {"month":4,"year":2025}
تمت إضافة فلتر الشهر: 4 والسنة: 2025
الاستعلام النهائي: SELECT a.*, e.name as employee_name
                FROM employee_attendance a
                JOIN employees e ON a.employee_id = e.id
                WHERE a.employee_id = :employee_id AND MONTH(a.date) = :month AND YEAR(a.date) = :year ORDER BY a.date DESC
قيم الربط: {":employee_id":1,":month":4,":year":2025}
عدد النتائج: 1
=== انتهاء تنفيذ getEmployeeAttendance ===

=== بدء تنفيذ getEmployeeAttendance ===
معرف الموظف: 1
الفلاتر: {"month":4,"year":2025}
تمت إضافة فلتر الشهر: 4 والسنة: 2025
الاستعلام النهائي: SELECT a.*, e.name as employee_name
                FROM employee_attendance a
                JOIN employees e ON a.employee_id = e.id
                WHERE a.employee_id = :employee_id AND MONTH(a.date) = :month AND YEAR(a.date) = :year ORDER BY a.date DESC
قيم الربط: {":employee_id":1,":month":4,":year":2025}
عدد النتائج: 1
=== انتهاء تنفيذ getEmployeeAttendance ===

=== بدء تنفيذ getEmployeeAttendance ===
معرف الموظف: 1
الفلاتر: {"month":4,"year":2025}
تمت إضافة فلتر الشهر: 4 والسنة: 2025
الاستعلام النهائي: SELECT a.*, e.name as employee_name
                FROM employee_attendance a
                JOIN employees e ON a.employee_id = e.id
                WHERE a.employee_id = :employee_id AND MONTH(a.date) = :month AND YEAR(a.date) = :year ORDER BY a.date DESC
قيم الربط: {":employee_id":1,":month":4,":year":2025}
عدد النتائج: 1
=== انتهاء تنفيذ getEmployeeAttendance ===

=== بدء تنفيذ getEmployeeAttendance ===
معرف الموظف: 5
الفلاتر: {"month":4,"year":2025}
تمت إضافة فلتر الشهر: 4 والسنة: 2025
الاستعلام النهائي: SELECT a.*, e.name as employee_name
                FROM employee_attendance a
                JOIN employees e ON a.employee_id = e.id
                WHERE a.employee_id = :employee_id AND MONTH(a.date) = :month AND YEAR(a.date) = :year ORDER BY a.date DESC
قيم الربط: {":employee_id":5,":month":4,":year":2025}
عدد النتائج: 0
=== انتهاء تنفيذ getEmployeeAttendance ===

=== بدء تنفيذ getEmployeeAttendance ===
معرف الموظف: 1
الفلاتر: {"month":4,"year":2025}
تمت إضافة فلتر الشهر: 4 والسنة: 2025
الاستعلام النهائي: SELECT a.*, e.name as employee_name
                FROM employee_attendance a
                JOIN employees e ON a.employee_id = e.id
                WHERE a.employee_id = :employee_id AND MONTH(a.date) = :month AND YEAR(a.date) = :year ORDER BY a.date DESC
قيم الربط: {":employee_id":1,":month":4,":year":2025}
عدد النتائج: 1
=== انتهاء تنفيذ getEmployeeAttendance ===

=== بدء تنفيذ getEmployeeAttendance ===
معرف الموظف: 1
الفلاتر: {"month":4,"year":2025}
تمت إضافة فلتر الشهر: 4 والسنة: 2025
الاستعلام النهائي: SELECT a.*, e.name as employee_name
                FROM employee_attendance a
                JOIN employees e ON a.employee_id = e.id
                WHERE a.employee_id = :employee_id AND MONTH(a.date) = :month AND YEAR(a.date) = :year ORDER BY a.date DESC
قيم الربط: {":employee_id":1,":month":4,":year":2025}
عدد النتائج: 1
=== انتهاء تنفيذ getEmployeeAttendance ===

=== بدء تنفيذ getEmployeeAttendance ===
معرف الموظف: 6
الفلاتر: {"month":4,"year":2025}
تمت إضافة فلتر الشهر: 4 والسنة: 2025
الاستعلام النهائي: SELECT a.*, e.name as employee_name
                FROM employee_attendance a
                JOIN employees e ON a.employee_id = e.id
                WHERE a.employee_id = :employee_id AND MONTH(a.date) = :month AND YEAR(a.date) = :year ORDER BY a.date DESC
قيم الربط: {":employee_id":6,":month":4,":year":2025}
عدد النتائج: 0
=== انتهاء تنفيذ getEmployeeAttendance ===

=== بدء تنفيذ getEmployeeAttendance ===
معرف الموظف: 2
الفلاتر: {"month":4,"year":2025}
تمت إضافة فلتر الشهر: 4 والسنة: 2025
الاستعلام النهائي: SELECT a.*, e.name as employee_name
                FROM employee_attendance a
                JOIN employees e ON a.employee_id = e.id
                WHERE a.employee_id = :employee_id AND MONTH(a.date) = :month AND YEAR(a.date) = :year ORDER BY a.date DESC
قيم الربط: {":employee_id":2,":month":4,":year":2025}
عدد النتائج: 1
=== انتهاء تنفيذ getEmployeeAttendance ===

=== بدء تنفيذ getEmployeeAttendance ===
معرف الموظف: 1
الفلاتر: {"month":4,"year":2025}
تمت إضافة فلتر الشهر: 4 والسنة: 2025
الاستعلام النهائي: SELECT a.*, e.name as employee_name
                FROM employee_attendance a
                JOIN employees e ON a.employee_id = e.id
                WHERE a.employee_id = :employee_id AND MONTH(a.date) = :month AND YEAR(a.date) = :year ORDER BY a.date DESC
قيم الربط: {":employee_id":1,":month":4,":year":2025}
عدد النتائج: 1
=== انتهاء تنفيذ getEmployeeAttendance ===

=== بدء تنفيذ getEmployeeAttendance ===
معرف الموظف: 2
الفلاتر: {"month":4,"year":2025}
تمت إضافة فلتر الشهر: 4 والسنة: 2025
الاستعلام النهائي: SELECT a.*, e.name as employee_name
                FROM employee_attendance a
                JOIN employees e ON a.employee_id = e.id
                WHERE a.employee_id = :employee_id AND MONTH(a.date) = :month AND YEAR(a.date) = :year ORDER BY a.date DESC
قيم الربط: {":employee_id":2,":month":4,":year":2025}
عدد النتائج: 1
=== انتهاء تنفيذ getEmployeeAttendance ===

=== بدء تنفيذ getEmployeeAttendance ===
معرف الموظف: 3
الفلاتر: {"month":4,"year":2025}
تمت إضافة فلتر الشهر: 4 والسنة: 2025
الاستعلام النهائي: SELECT a.*, e.name as employee_name
                FROM employee_attendance a
                JOIN employees e ON a.employee_id = e.id
                WHERE a.employee_id = :employee_id AND MONTH(a.date) = :month AND YEAR(a.date) = :year ORDER BY a.date DESC
قيم الربط: {":employee_id":3,":month":4,":year":2025}
عدد النتائج: 0
=== انتهاء تنفيذ getEmployeeAttendance ===

=== بدء تنفيذ getEmployeeAttendance ===
معرف الموظف: 4
الفلاتر: {"month":4,"year":2025}
تمت إضافة فلتر الشهر: 4 والسنة: 2025
الاستعلام النهائي: SELECT a.*, e.name as employee_name
                FROM employee_attendance a
                JOIN employees e ON a.employee_id = e.id
                WHERE a.employee_id = :employee_id AND MONTH(a.date) = :month AND YEAR(a.date) = :year ORDER BY a.date DESC
قيم الربط: {":employee_id":4,":month":4,":year":2025}
عدد النتائج: 0
=== انتهاء تنفيذ getEmployeeAttendance ===

=== بدء تنفيذ getEmployeeAttendance ===
معرف الموظف: 2
الفلاتر: {"month":4,"year":2025}
تمت إضافة فلتر الشهر: 4 والسنة: 2025
الاستعلام النهائي: SELECT a.*, e.name as employee_name
                FROM employee_attendance a
                JOIN employees e ON a.employee_id = e.id
                WHERE a.employee_id = :employee_id AND MONTH(a.date) = :month AND YEAR(a.date) = :year ORDER BY a.date DESC
قيم الربط: {":employee_id":2,":month":4,":year":2025}
عدد النتائج: 1
=== انتهاء تنفيذ getEmployeeAttendance ===

=== بدء تنفيذ getEmployeeAttendance ===
معرف الموظف: 2
الفلاتر: {"month":4,"year":2025}
تمت إضافة فلتر الشهر: 4 والسنة: 2025
الاستعلام النهائي: SELECT a.*, e.name as employee_name
                FROM employee_attendance a
                JOIN employees e ON a.employee_id = e.id
                WHERE a.employee_id = :employee_id AND MONTH(a.date) = :month AND YEAR(a.date) = :year ORDER BY a.date DESC
قيم الربط: {":employee_id":2,":month":4,":year":2025}
عدد النتائج: 1
=== انتهاء تنفيذ getEmployeeAttendance ===

=== بدء تنفيذ getEmployeeAttendance ===
معرف الموظف: 2
الفلاتر: {"month":4,"year":2025}
تمت إضافة فلتر الشهر: 4 والسنة: 2025
الاستعلام النهائي: SELECT a.*, e.name as employee_name
                FROM employee_attendance a
                JOIN employees e ON a.employee_id = e.id
                WHERE a.employee_id = :employee_id AND MONTH(a.date) = :month AND YEAR(a.date) = :year ORDER BY a.date DESC
قيم الربط: {":employee_id":2,":month":4,":year":2025}
عدد النتائج: 1
=== انتهاء تنفيذ getEmployeeAttendance ===

=== بدء تنفيذ getEmployeeAttendance ===
معرف الموظف: 1
الفلاتر: {"month":5,"year":2025}
تمت إضافة فلتر الشهر: 5 والسنة: 2025
الاستعلام النهائي: SELECT a.*, e.name as employee_name
                FROM employee_attendance a
                JOIN employees e ON a.employee_id = e.id
                WHERE a.employee_id = :employee_id AND MONTH(a.date) = :month AND YEAR(a.date) = :year ORDER BY a.date DESC
قيم الربط: {":employee_id":1,":month":5,":year":2025}
عدد النتائج: 0
=== انتهاء تنفيذ getEmployeeAttendance ===

=== بدء تنفيذ getEmployeeAttendance ===
معرف الموظف: 2
الفلاتر: {"month":5,"year":2025}
تمت إضافة فلتر الشهر: 5 والسنة: 2025
الاستعلام النهائي: SELECT a.*, e.name as employee_name
                FROM employee_attendance a
                JOIN employees e ON a.employee_id = e.id
                WHERE a.employee_id = :employee_id AND MONTH(a.date) = :month AND YEAR(a.date) = :year ORDER BY a.date DESC
قيم الربط: {":employee_id":2,":month":5,":year":2025}
عدد النتائج: 0
=== انتهاء تنفيذ getEmployeeAttendance ===

=== بدء تنفيذ getEmployeeAttendance ===
معرف الموظف: 1
الفلاتر: {"month":5,"year":2025}
تمت إضافة فلتر الشهر: 5 والسنة: 2025
الاستعلام النهائي: SELECT a.*, e.name as employee_name
                FROM employee_attendance a
                JOIN employees e ON a.employee_id = e.id
                WHERE a.employee_id = :employee_id AND MONTH(a.date) = :month AND YEAR(a.date) = :year ORDER BY a.date DESC
قيم الربط: {":employee_id":1,":month":5,":year":2025}
عدد النتائج: 0
=== انتهاء تنفيذ getEmployeeAttendance ===

=== بدء تنفيذ getEmployeeAttendance ===
معرف الموظف: 1
الفلاتر: {"month":5,"year":2025}
تمت إضافة فلتر الشهر: 5 والسنة: 2025
الاستعلام النهائي: SELECT a.*, e.name as employee_name
                FROM employee_attendance a
                JOIN employees e ON a.employee_id = e.id
                WHERE a.employee_id = :employee_id AND MONTH(a.date) = :month AND YEAR(a.date) = :year ORDER BY a.date DESC
قيم الربط: {":employee_id":1,":month":5,":year":2025}
عدد النتائج: 0
=== انتهاء تنفيذ getEmployeeAttendance ===

=== بدء تنفيذ getEmployeeAttendance ===
معرف الموظف: 1
الفلاتر: {"month":5,"year":2025}
تمت إضافة فلتر الشهر: 5 والسنة: 2025
الاستعلام النهائي: SELECT a.*, e.name as employee_name
                FROM employee_attendance a
                JOIN employees e ON a.employee_id = e.id
                WHERE a.employee_id = :employee_id AND MONTH(a.date) = :month AND YEAR(a.date) = :year ORDER BY a.date DESC
قيم الربط: {":employee_id":1,":month":5,":year":2025}
عدد النتائج: 0
=== انتهاء تنفيذ getEmployeeAttendance ===

=== بدء تنفيذ getEmployeeAttendance ===
معرف الموظف: 2
الفلاتر: {"month":5,"year":2025}
تمت إضافة فلتر الشهر: 5 والسنة: 2025
الاستعلام النهائي: SELECT a.*, e.name as employee_name
                FROM employee_attendance a
                JOIN employees e ON a.employee_id = e.id
                WHERE a.employee_id = :employee_id AND MONTH(a.date) = :month AND YEAR(a.date) = :year ORDER BY a.date DESC
قيم الربط: {":employee_id":2,":month":5,":year":2025}
عدد النتائج: 0
=== انتهاء تنفيذ getEmployeeAttendance ===

=== بدء تنفيذ getEmployeeAttendance ===
معرف الموظف: 2
الفلاتر: {"month":6,"year":2025}
تمت إضافة فلتر الشهر: 6 والسنة: 2025
الاستعلام النهائي: SELECT a.*, e.name as employee_name
                FROM employee_attendance a
                JOIN employees e ON a.employee_id = e.id
                WHERE a.employee_id = :employee_id AND MONTH(a.date) = :month AND YEAR(a.date) = :year ORDER BY a.date DESC
قيم الربط: {":employee_id":2,":month":6,":year":2025}
عدد النتائج: 0
=== انتهاء تنفيذ getEmployeeAttendance ===

=== بدء تنفيذ getEmployeeAttendance ===
معرف الموظف: 1
الفلاتر: {"month":6,"year":2025}
تمت إضافة فلتر الشهر: 6 والسنة: 2025
الاستعلام النهائي: SELECT a.*, e.name as employee_name
                FROM employee_attendance a
                JOIN employees e ON a.employee_id = e.id
                WHERE a.employee_id = :employee_id AND MONTH(a.date) = :month AND YEAR(a.date) = :year ORDER BY a.date DESC
قيم الربط: {":employee_id":1,":month":6,":year":2025}
عدد النتائج: 0
=== انتهاء تنفيذ getEmployeeAttendance ===

=== بدء تنفيذ getEmployeeAttendance ===
معرف الموظف: 2
الفلاتر: {"month":6,"year":2025}
تمت إضافة فلتر الشهر: 6 والسنة: 2025
الاستعلام النهائي: SELECT a.*, e.name as employee_name
                FROM employee_attendance a
                JOIN employees e ON a.employee_id = e.id
                WHERE a.employee_id = :employee_id AND MONTH(a.date) = :month AND YEAR(a.date) = :year ORDER BY a.date DESC
قيم الربط: {":employee_id":2,":month":6,":year":2025}
عدد النتائج: 0
=== انتهاء تنفيذ getEmployeeAttendance ===

=== بدء تنفيذ getEmployeeAttendance ===
معرف الموظف: 1
الفلاتر: {"month":6,"year":2025}
تمت إضافة فلتر الشهر: 6 والسنة: 2025
الاستعلام النهائي: SELECT a.*, e.name as employee_name
                FROM employee_attendance a
                JOIN employees e ON a.employee_id = e.id
                WHERE a.employee_id = :employee_id AND MONTH(a.date) = :month AND YEAR(a.date) = :year ORDER BY a.date DESC
قيم الربط: {":employee_id":1,":month":6,":year":2025}
عدد النتائج: 0
=== انتهاء تنفيذ getEmployeeAttendance ===

=== بدء تنفيذ getEmployeeAttendance ===
معرف الموظف: 1
الفلاتر: {"month":6,"year":2025}
تمت إضافة فلتر الشهر: 6 والسنة: 2025
الاستعلام النهائي: SELECT a.*, e.name as employee_name
                FROM employee_attendance a
                JOIN employees e ON a.employee_id = e.id
                WHERE a.employee_id = :employee_id AND MONTH(a.date) = :month AND YEAR(a.date) = :year ORDER BY a.date DESC
قيم الربط: {":employee_id":1,":month":6,":year":2025}
عدد النتائج: 0
=== انتهاء تنفيذ getEmployeeAttendance ===

