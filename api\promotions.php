<?php
/**
 * واجهة برمجة التطبيقات للعروض والخصومات
 */

// تعريف ثابت لمنع الوصول المباشر للملفات المرتبطة
define('BASEPATH', true);

// استدعاء ملفات التهيئة
require_once '../config/init.php';

// التحقق من تسجيل الدخول
requireLogin();

// إنشاء كائن نموذج العروض والخصومات
$promotionModel = new Promotion($db);

// التحقق من وجود إجراء
if (!isset($_GET['action']) && !isset($_POST['action'])) {
    log_debug('No action parameter found in request', [
        'GET' => $_GET,
        'POST' => $_POST
    ]);
    echo json_encode([
        'status' => 'error',
        'message' => 'الإجراء غير محدد'
    ]);
    exit;
}

// تنفيذ الإجراء المطلوب
$action = isset($_GET['action']) ? $_GET['action'] : $_POST['action'];

// تسجيل الإجراء المطلوب
log_debug('Requested action: ' . $action);

switch ($action) {
    case 'add':
        // التحقق من الصلاحيات
        if (!hasPermission('promotions_create')) {
            echo json_encode([
                'status' => 'error',
                'message' => 'ليس لديك صلاحية لإضافة عروض جديدة'
            ]);
            exit;
        }

        // التحقق من البيانات المطلوبة
        if (empty($_POST['name']) || empty($_POST['condition_type']) || !isset($_POST['condition_value']) ||
            empty($_POST['discount_type']) || !isset($_POST['discount_value'])) {
            echo json_encode([
                'status' => 'error',
                'message' => 'يرجى ملء جميع الحقول المطلوبة'
            ]);
            exit;
        }

        // التحقق من صحة البيانات
        if ($_POST['condition_value'] < 0 || $_POST['discount_value'] < 0) {
            echo json_encode([
                'status' => 'error',
                'message' => 'يجب أن تكون قيم الشرط والخصم أكبر من أو تساوي صفر'
            ]);
            exit;
        }

        // التحقق من العنصر المحدد إذا كان نوع الشرط يتطلب ذلك
        if (in_array($_POST['condition_type'], ['specific_product', 'specific_service']) && empty($_POST['specific_item_id'])) {
            echo json_encode([
                'status' => 'error',
                'message' => 'يرجى اختيار العنصر المحدد'
            ]);
            exit;
        }

        // إعداد البيانات
        $promotionData = [
            'name' => $_POST['name'],
            'description' => $_POST['description'] ?? '',
            'condition_type' => $_POST['condition_type'],
            'condition_value' => $_POST['condition_value'],
            'condition_max_value' => !empty($_POST['condition_max_value']) ? $_POST['condition_max_value'] : null,
            'discount_type' => $_POST['discount_type'],
            'discount_value' => $_POST['discount_value'],
            'start_date' => $_POST['start_date'] ?? null,
            'end_date' => $_POST['end_date'] ?? null,
            'is_active' => isset($_POST['is_active']) ? 1 : 0,
            'branch_id' => !empty($_POST['branch_id']) ? $_POST['branch_id'] : null,
            'specific_item_id' => !empty($_POST['specific_item_id']) ? $_POST['specific_item_id'] : null
        ];

        try {
            // إضافة العرض
            $promotionId = $promotionModel->addPromotion($promotionData);

            if ($promotionId) {
                echo json_encode([
                    'status' => 'success',
                    'message' => 'تم إضافة العرض بنجاح',
                    'promotion_id' => $promotionId
                ]);
            } else {
                echo json_encode([
                    'status' => 'error',
                    'message' => 'فشل في إضافة العرض. يرجى المحاولة مرة أخرى.'
                ]);
            }
        } catch (Exception $e) {
            echo json_encode([
                'status' => 'error',
                'message' => 'حدث خطأ أثناء إضافة العرض: ' . $e->getMessage()
            ]);
        }
        break;

    case 'update':
        // التحقق من الصلاحيات
        if (!hasPermission('promotions_edit')) {
            echo json_encode([
                'status' => 'error',
                'message' => 'ليس لديك صلاحية لتعديل العروض'
            ]);
            exit;
        }

        // التحقق من وجود معرف العرض
        if (empty($_POST['id']) || !is_numeric($_POST['id'])) {
            echo json_encode([
                'status' => 'error',
                'message' => 'معرف العرض غير صحيح'
            ]);
            exit;
        }

        $promotionId = intval($_POST['id']);

        // التحقق من وجود العرض
        $promotion = $promotionModel->getPromotionById($promotionId);
        if (!$promotion) {
            echo json_encode([
                'status' => 'error',
                'message' => 'العرض غير موجود'
            ]);
            exit;
        }

        // التحقق من البيانات المطلوبة
        if (empty($_POST['name']) || empty($_POST['condition_type']) || !isset($_POST['condition_value']) ||
            empty($_POST['discount_type']) || !isset($_POST['discount_value'])) {
            echo json_encode([
                'status' => 'error',
                'message' => 'يرجى ملء جميع الحقول المطلوبة'
            ]);
            exit;
        }

        // التحقق من صحة البيانات
        if ($_POST['condition_value'] < 0 || $_POST['discount_value'] < 0) {
            echo json_encode([
                'status' => 'error',
                'message' => 'يجب أن تكون قيم الشرط والخصم أكبر من أو تساوي صفر'
            ]);
            exit;
        }

        // التحقق من العنصر المحدد إذا كان نوع الشرط يتطلب ذلك
        if (in_array($_POST['condition_type'], ['specific_product', 'specific_service']) && empty($_POST['specific_item_id'])) {
            echo json_encode([
                'status' => 'error',
                'message' => 'يرجى اختيار العنصر المحدد'
            ]);
            exit;
        }

        // إعداد البيانات
        $promotionData = [
            'name' => $_POST['name'],
            'description' => $_POST['description'] ?? '',
            'condition_type' => $_POST['condition_type'],
            'condition_value' => $_POST['condition_value'],
            'condition_max_value' => !empty($_POST['condition_max_value']) ? $_POST['condition_max_value'] : null,
            'discount_type' => $_POST['discount_type'],
            'discount_value' => $_POST['discount_value'],
            'start_date' => $_POST['start_date'] ?? null,
            'end_date' => $_POST['end_date'] ?? null,
            'is_active' => isset($_POST['is_active']) ? 1 : 0,
            'branch_id' => !empty($_POST['branch_id']) ? $_POST['branch_id'] : null,
            'specific_item_id' => !empty($_POST['specific_item_id']) ? $_POST['specific_item_id'] : null
        ];

        try {
            // تحديث العرض
            $result = $promotionModel->updatePromotion($promotionId, $promotionData);

            if ($result) {
                echo json_encode([
                    'status' => 'success',
                    'message' => 'تم تحديث العرض بنجاح'
                ]);
            } else {
                echo json_encode([
                    'status' => 'error',
                    'message' => 'فشل في تحديث العرض. يرجى المحاولة مرة أخرى.'
                ]);
            }
        } catch (Exception $e) {
            echo json_encode([
                'status' => 'error',
                'message' => 'حدث خطأ أثناء تحديث العرض: ' . $e->getMessage()
            ]);
        }
        break;

    case 'delete':
        // التحقق من الصلاحيات
        if (!hasPermission('promotions_delete')) {
            echo json_encode([
                'status' => 'error',
                'message' => 'ليس لديك صلاحية لحذف العروض'
            ]);
            exit;
        }

        // التحقق من وجود معرف العرض
        if (empty($_POST['id']) || !is_numeric($_POST['id'])) {
            echo json_encode([
                'status' => 'error',
                'message' => 'معرف العرض غير صحيح'
            ]);
            exit;
        }

        $promotionId = intval($_POST['id']);

        // التحقق من وجود العرض
        $promotion = $promotionModel->getPromotionById($promotionId);
        if (!$promotion) {
            echo json_encode([
                'status' => 'error',
                'message' => 'العرض غير موجود'
            ]);
            exit;
        }

        try {
            // حذف العرض
            $result = $promotionModel->deletePromotion($promotionId);

            if ($result) {
                echo json_encode([
                    'status' => 'success',
                    'message' => 'تم حذف العرض بنجاح'
                ]);
            } else {
                echo json_encode([
                    'status' => 'error',
                    'message' => 'فشل في حذف العرض. يرجى المحاولة مرة أخرى.'
                ]);
            }
        } catch (Exception $e) {
            echo json_encode([
                'status' => 'error',
                'message' => 'حدث خطأ أثناء حذف العرض: ' . $e->getMessage()
            ]);
        }
        break;

    case 'toggle_status':
        // التحقق من الصلاحيات
        if (!hasPermission('promotions_edit')) {
            echo json_encode([
                'status' => 'error',
                'message' => 'ليس لديك صلاحية لتعديل حالة العروض'
            ]);
            exit;
        }

        // التحقق من وجود معرف العرض
        if (empty($_POST['id']) || !is_numeric($_POST['id'])) {
            echo json_encode([
                'status' => 'error',
                'message' => 'معرف العرض غير صحيح'
            ]);
            exit;
        }

        $promotionId = intval($_POST['id']);
        $isActive = isset($_POST['is_active']) ? (bool)$_POST['is_active'] : false;

        // التحقق من وجود العرض
        $promotion = $promotionModel->getPromotionById($promotionId);
        if (!$promotion) {
            echo json_encode([
                'status' => 'error',
                'message' => 'العرض غير موجود'
            ]);
            exit;
        }

        try {
            // تغيير حالة العرض
            $result = $promotionModel->togglePromotionStatus($promotionId, $isActive);

            if ($result) {
                echo json_encode([
                    'status' => 'success',
                    'message' => 'تم تغيير حالة العرض بنجاح'
                ]);
            } else {
                echo json_encode([
                    'status' => 'error',
                    'message' => 'فشل في تغيير حالة العرض. يرجى المحاولة مرة أخرى.'
                ]);
            }
        } catch (Exception $e) {
            echo json_encode([
                'status' => 'error',
                'message' => 'حدث خطأ أثناء تغيير حالة العرض: ' . $e->getMessage()
            ]);
        }
        break;

    case 'get_available_promotions':
        // تسجيل معلومات للتصحيح
        log_debug('get_available_promotions API called with POST data', $_POST);

        // التحقق من البيانات المطلوبة
        if (!isset($_POST['total_amount']) || !isset($_POST['items_count'])) {
            log_debug('Missing required data: total_amount or items_count');
            echo json_encode([
                'status' => 'error',
                'message' => 'البيانات المطلوبة غير مكتملة'
            ]);
            exit;
        }

        $totalAmount = floatval($_POST['total_amount']);
        $itemsCount = intval($_POST['items_count']);
        $items = isset($_POST['items']) ? $_POST['items'] : '[]'; // لا نقوم بفك الترميز هنا
        // تحديد معرف الفرع من الطلب أو من الجلسة
        $userRole = isset($_SESSION['user_role']) ? $_SESSION['user_role'] : '';

        // استخدام معرف الفرع المرسل من صفحة POS إذا كان موجودًا
        if (isset($_POST['branch_id']) && $_POST['branch_id'] !== 'null' && $_POST['branch_id'] !== '') {
            $branchId = intval($_POST['branch_id']);
            log_debug('Using branch_id from request: ' . $branchId);
        } else {
            // استخدام معرف الفرع من الجلسة إذا لم يتم تمريره من الطلب
            $branchId = isset($_SESSION['user_branch_id']) ? $_SESSION['user_branch_id'] : null;
            log_debug('Using branch_id from session: ' . ($branchId ?? 'null (all branches)'));
        }

        // إذا كان المستخدم مسؤول (ادمن) ولم يتم تحديد فرع محدد
        // نترك branchId كما هو (null) للبحث في جميع الفروع
        log_debug('User role: ' . $userRole . ', Final Branch ID: ' . ($branchId ?? 'null (all branches)'));

        // التحقق من صحة البيانات
        if ($totalAmount <= 0) {
            log_debug('Invalid total_amount: ' . $totalAmount);
            echo json_encode([
                'status' => 'error',
                'message' => 'قيمة الفاتورة غير صحيحة'
            ]);
            exit;
        }

        log_debug('Processing promotion request', [
            'totalAmount' => $totalAmount,
            'itemsCount' => $itemsCount,
            'branchId' => $branchId,
            'items' => $items
        ]);

        try {
            // التحقق من وجود عروض نشطة في النظام
            $activePromotions = $promotionModel->getPromotions([
                'is_active' => 1,
                'check_date' => true // التحقق من التاريخ الحالي
            ]);
            log_debug('Active promotions in system', $activePromotions);

            // الحصول على العروض المتاحة للفاتورة
            $availablePromotions = $promotionModel->getAvailablePromotionsForInvoice($totalAmount, $itemsCount, $items, $branchId);

            log_debug('Found ' . count($availablePromotions) . ' available promotions for this invoice');

            echo json_encode([
                'status' => 'success',
                'promotions' => $availablePromotions
            ]);
        } catch (Exception $e) {
            log_error('Error in get_available_promotions: ' . $e->getMessage(), $e);

            echo json_encode([
                'status' => 'error',
                'message' => 'حدث خطأ أثناء استرجاع العروض المتاحة: ' . $e->getMessage()
            ]);
        }
        break;

    case 'record_usage':
        // التحقق من الصلاحيات
        if (!hasPermission('invoices_edit')) {
            echo json_encode([
                'status' => 'error',
                'message' => 'ليس لديك صلاحية لتسجيل استخدام العروض'
            ]);
            exit;
        }

        // التحقق من البيانات المطلوبة
        if (empty($_POST['promotion_id']) || !is_numeric($_POST['promotion_id']) ||
            empty($_POST['invoice_id']) || !is_numeric($_POST['invoice_id']) ||
            !isset($_POST['discount_amount']) || !is_numeric($_POST['discount_amount'])) {
            echo json_encode([
                'status' => 'error',
                'message' => 'البيانات المطلوبة غير مكتملة أو غير صحيحة'
            ]);
            exit;
        }

        $promotionId = intval($_POST['promotion_id']);
        $invoiceId = intval($_POST['invoice_id']);
        $discountAmount = floatval($_POST['discount_amount']);
        $customerId = isset($_POST['customer_id']) && is_numeric($_POST['customer_id']) ? intval($_POST['customer_id']) : null;

        // التحقق من وجود العرض
        $promotion = $promotionModel->getPromotionById($promotionId);
        if (!$promotion) {
            echo json_encode([
                'status' => 'error',
                'message' => 'العرض غير موجود'
            ]);
            exit;
        }

        try {
            // تسجيل استخدام العرض
            $result = $promotionModel->recordPromotionUsage($promotionId, $invoiceId, $customerId, $discountAmount);

            if ($result) {
                echo json_encode([
                    'status' => 'success',
                    'message' => 'تم تسجيل استخدام العرض بنجاح'
                ]);
            } else {
                echo json_encode([
                    'status' => 'error',
                    'message' => 'فشل في تسجيل استخدام العرض. يرجى المحاولة مرة أخرى.'
                ]);
            }
        } catch (Exception $e) {
            echo json_encode([
                'status' => 'error',
                'message' => 'حدث خطأ أثناء تسجيل استخدام العرض: ' . $e->getMessage()
            ]);
        }
        break;

    default:
        echo json_encode([
            'status' => 'error',
            'message' => 'الإجراء غير معروف'
        ]);
        break;
}
