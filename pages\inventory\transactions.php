<?php
/**
 * صفحة حركات المخزون
 * تعرض جميع حركات المخزون مع إمكانية التصفية حسب المنتج والفرع والتاريخ
 */

// تعريف ثابت لمنع الوصول المباشر للملفات المرتبطة
define('BASEPATH', true);

// استدعاء ملف التهيئة
require_once '../../config/init.php';

// التحقق من تسجيل الدخول
redirectIfNotLoggedIn();

// التحقق من صلاحية الوصول
if (!hasPermission('inventory_view')) {
    $_SESSION['error_message'] = 'ليس لديك صلاحية لعرض حركات المخزون';
    header('Location: ' . BASE_URL . 'pages/dashboard.php');
    exit;
}

// عنوان الصفحة
$pageTitle = 'حركات المخزون';

// استدعاء رأس الصفحة
include '../../includes/templates/header.php';

// الحصول على معرف الفرع الحالي
$currentBranchId = $_SESSION['user_branch_id'] ?? null;

// التحقق مما إذا كان المستخدم مديرًا (يمكنه رؤية جميع الفروع)
$isAdmin = hasPermission('admin_access');

// الحصول على قائمة الفروع
$branchModel = new Branch($db);
$branches = $branchModel->getBranches();

// الحصول على قائمة المنتجات
$productModel = new Product($db);
$products = $productModel->getProducts();

// الحصول على فئات المنتجات
$categories = $productModel->getProductCategories();

// تهيئة نموذج المخزون
$inventoryModel = new Inventory($db);

// الحصول على حركات المخزون
$filters = [];
if (!$isAdmin && $currentBranchId) {
    $filters['branch_id'] = $currentBranchId;
}

// تطبيق الفلاتر من الطلب
if (!empty($_GET['product_id'])) {
    $filters['product_id'] = $_GET['product_id'];
}

if (!empty($_GET['branch_id']) && $isAdmin) {
    $filters['branch_id'] = $_GET['branch_id'];
}

if (!empty($_GET['transaction_type'])) {
    $filters['transaction_type'] = $_GET['transaction_type'];
}

if (!empty($_GET['start_date']) && !empty($_GET['end_date'])) {
    $filters['start_date'] = $_GET['start_date'];
    $filters['end_date'] = $_GET['end_date'];
}

if (!empty($_GET['category_id'])) {
    $filters['category_id'] = $_GET['category_id'];
}

// الحصول على حركات المخزون
$transactions = $inventoryModel->getInventoryTransactions($filters);
?>

<!-- المحتوى الرئيسي للصفحة -->
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- عنوان الصفحة والأزرار -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h4 class="mb-0"><?php echo $pageTitle; ?></h4>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="<?php echo BASE_URL; ?>pages/dashboard.php">الرئيسية</a></li>
                            <li class="breadcrumb-item"><a href="<?php echo BASE_URL; ?>pages/inventory/index.php">المخزون</a></li>
                            <li class="breadcrumb-item active" aria-current="page">حركات المخزون</li>
                        </ol>
                    </nav>
                </div>
                <div class="btn-group">
                    <a href="<?php echo BASE_URL; ?>pages/inventory/index.php" class="btn btn-outline-primary">
                        <i class="fas fa-boxes me-1"></i> المخزون
                    </a>
                    <a href="<?php echo BASE_URL; ?>pages/inventory/adjust.php" class="btn btn-outline-success">
                        <i class="fas fa-edit me-1"></i> تعديل المخزون
                    </a>
                    <button type="button" class="btn btn-outline-secondary" id="print-transactions">
                        <i class="fas fa-print me-1"></i> طباعة
                    </button>
                </div>
            </div>

            <!-- بطاقة فلاتر البحث -->
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-white py-3">
                    <h5 class="mb-0">فلاتر البحث</h5>
                </div>
                <div class="card-body">
                    <form id="transactions-filter-form" method="get">
                        <div class="row">
                            <div class="col-md-3 mb-3">
                                <label for="product-select" class="form-label">المنتج</label>
                                <select class="form-select" id="product-select" name="product_id">
                                    <option value="">جميع المنتجات</option>
                                    <?php foreach ($products as $product): ?>
                                    <option value="<?php echo $product['id']; ?>" <?php echo (isset($_GET['product_id']) && $_GET['product_id'] == $product['id']) ? 'selected' : ''; ?>>
                                        <?php echo $product['name']; ?>
                                    </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>

                            <div class="col-md-3 mb-3">
                                <label for="category-select" class="form-label">فئة المنتج</label>
                                <select class="form-select" id="category-select" name="category_id">
                                    <option value="">جميع الفئات</option>
                                    <?php foreach ($categories as $category): ?>
                                    <option value="<?php echo $category['id']; ?>" <?php echo (isset($_GET['category_id']) && $_GET['category_id'] == $category['id']) ? 'selected' : ''; ?>>
                                        <?php echo $category['name']; ?>
                                    </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>

                            <?php if ($isAdmin): ?>
                            <div class="col-md-3 mb-3">
                                <label for="branch-select" class="form-label">الفرع</label>
                                <select class="form-select" id="branch-select" name="branch_id">
                                    <option value="">جميع الفروع</option>
                                    <?php foreach ($branches as $branch): ?>
                                    <option value="<?php echo $branch['id']; ?>" <?php echo (isset($_GET['branch_id']) && $_GET['branch_id'] == $branch['id']) ? 'selected' : ''; ?>>
                                        <?php echo $branch['name']; ?>
                                    </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <?php endif; ?>

                            <div class="col-md-3 mb-3">
                                <label for="transaction-type-select" class="form-label">نوع الحركة</label>
                                <select class="form-select" id="transaction-type-select" name="transaction_type">
                                    <option value="">جميع الحركات</option>
                                    <option value="in" <?php echo (isset($_GET['transaction_type']) && $_GET['transaction_type'] == 'in') ? 'selected' : ''; ?>>إضافة</option>
                                    <option value="out" <?php echo (isset($_GET['transaction_type']) && $_GET['transaction_type'] == 'out') ? 'selected' : ''; ?>>سحب</option>
                                </select>
                            </div>

                            <div class="col-md-3 mb-3">
                                <label for="start-date" class="form-label">من تاريخ</label>
                                <input type="date" class="form-control" id="start-date" name="start_date" value="<?php echo $_GET['start_date'] ?? ''; ?>">
                            </div>

                            <div class="col-md-3 mb-3">
                                <label for="end-date" class="form-label">إلى تاريخ</label>
                                <input type="date" class="form-control" id="end-date" name="end_date" value="<?php echo $_GET['end_date'] ?? ''; ?>">
                            </div>

                            <div class="col-md-3 mb-3 d-flex align-items-end">
                                <button type="submit" class="btn btn-primary w-100">
                                    <i class="fas fa-search me-1"></i> بحث
                                </button>
                            </div>

                            <div class="col-md-3 mb-3 d-flex align-items-end">
                                <a href="<?php echo BASE_URL; ?>pages/inventory/transactions.php" class="btn btn-secondary w-100">
                                    <i class="fas fa-redo me-1"></i> إعادة تعيين
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- جدول حركات المخزون -->
            <div class="card shadow-sm">
                <div class="card-header bg-white py-3">
                    <h5 class="mb-0">حركات المخزون</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered table-hover" id="transactions-table">
                            <thead class="table-light">
                                <tr>
                                    <th>#</th>
                                    <th>المنتج</th>
                                    <th>نوع الحركة</th>
                                    <th>الغرض</th>
                                    <th>الكمية</th>
                                    <th>الكمية السابقة</th>
                                    <th>الكمية الحالية</th>
                                    <th>الملاحظات</th>
                                    <th>المستخدم</th>
                                    <th>الفرع</th>
                                    <th>التاريخ</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (empty($transactions)): ?>
                                <tr>
                                    <td colspan="10" class="text-center">لا توجد حركات مخزون</td>
                                </tr>
                                <?php else: ?>
                                <?php foreach ($transactions as $index => $transaction): ?>
                                <tr>
                                    <td><?php echo $index + 1; ?></td>
                                    <td><?php echo $transaction['product_name']; ?></td>
                                    <td>
                                        <?php if ($transaction['transaction_type'] == 'in'): ?>
                                        <span class="badge bg-success">إضافة</span>
                                        <?php else: ?>
                                        <span class="badge bg-danger">سحب</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php
                                        // تحديد نوع الحركة بناءً على الملاحظات
                                        $purpose = '-';
                                        $badgeClass = 'bg-secondary';

                                        if (strpos($transaction['notes'], 'استهلاك داخلي') !== false) {
                                            $purpose = 'استهلاك داخلي';
                                            $badgeClass = 'bg-danger';
                                        } elseif (strpos($transaction['notes'], 'بيع منتج') !== false) {
                                            $purpose = 'مبيعات';
                                            $badgeClass = 'bg-primary';
                                        } elseif (strpos($transaction['notes'], 'إلغاء بيع منتج') !== false) {
                                            $purpose = 'إلغاء بيع';
                                            $badgeClass = 'bg-warning text-dark';
                                        } elseif (strpos($transaction['notes'], 'تعديل مخزون') !== false) {
                                            $purpose = 'تعديل مخزون';
                                            $badgeClass = 'bg-info text-dark';
                                        } elseif (strpos($transaction['notes'], 'نقل مخزون') !== false) {
                                            $purpose = 'نقل مخزون';
                                            $badgeClass = 'bg-dark';
                                        } elseif (strpos($transaction['notes'], 'استلام مخزون') !== false) {
                                            $purpose = 'استلام مخزون';
                                            $badgeClass = 'bg-dark';
                                        } elseif ($transaction['transaction_type'] == 'out' && isset($transaction['is_for_sale']) && $transaction['is_for_sale'] == 0) {
                                            $purpose = 'استهلاك داخلي';
                                            $badgeClass = 'bg-danger';
                                        }
                                        ?>
                                        <span class="badge <?php echo $badgeClass; ?>"><?php echo $purpose; ?></span>
                                    </td>
                                    <td><?php echo $transaction['quantity']; ?></td>
                                    <td><?php echo $transaction['previous_quantity']; ?></td>
                                    <td><?php echo $transaction['current_quantity']; ?></td>
                                    <td><?php echo $transaction['notes'] ?: '-'; ?></td>
                                    <td><?php echo $transaction['user_name'] ?: '-'; ?></td>
                                    <td><?php echo $transaction['branch_name'] ?: '-'; ?></td>
                                    <td><?php echo date('Y-m-d H:i', strtotime($transaction['created_at'])); ?></td>
                                </tr>
                                <?php endforeach; ?>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- استدعاء قالب الفوتر -->
<?php include '../../includes/templates/footer.php'; ?>

<script>
$(document).ready(function() {
    // تهيئة جدول حركات المخزون
    $('#transactions-table').DataTable({
        "language": {
            "url": "<?php echo BASE_URL; ?>assets/js/arabic-datatables.json"
        },
        "order": [[9, "desc"]], // ترتيب حسب التاريخ (تنازلي)
        "pageLength": 25
    });

    // طباعة حركات المخزون
    $('#print-transactions').on('click', function() {
        window.print();
    });

    // تحديث قائمة المنتجات عند تغيير الفئة
    $('#category-select').on('change', function() {
        const categoryId = $(this).val();

        if (categoryId) {
            // إرسال طلب AJAX لجلب المنتجات حسب الفئة
            $.ajax({
                url: '<?php echo BASE_URL; ?>api/products.php',
                type: 'GET',
                data: {
                    action: 'get-by-category',
                    category_id: categoryId
                },
                dataType: 'json',
                success: function(response) {
                    if (response.success) {
                        // تحديث قائمة المنتجات
                        const productSelect = $('#product-select');
                        productSelect.empty();
                        productSelect.append('<option value="">جميع المنتجات</option>');

                        response.products.forEach(function(product) {
                            productSelect.append(`<option value="${product.id}">${product.name}</option>`);
                        });
                    }
                }
            });
        }
    });
});
</script>
