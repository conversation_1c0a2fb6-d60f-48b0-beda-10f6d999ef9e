=== 2025-04-14 22:16:01 ===
Branch ID: 0
Total Products Query (All Branches): SELECT COUNT(*) as total_products
                              FROM products
                              WHERE is_active = 1
Total Products Result: 78
Low Stock Query (All Branches): SELECT COUNT(DISTINCT p.id) as low_stock
                                 FROM products p
                                 JOIN inventory i ON p.id = i.product_id
                                 WHERE p.is_active = 1
                                 AND i.quantity > 0 AND i.quantity <= p.min_quantity
Low Stock Result: 45
Out of Stock Query (All Branches): SELECT COUNT(DISTINCT p.id) as out_of_stock
                                   FROM products p
                                   LEFT JOIN inventory i ON p.id = i.product_id
                                   WHERE p.is_active = 1
                                   AND (i.quantity = 0 OR i.quantity IS NULL)
Out of Stock Result: 30
Total Value Query (All Branches): SELECT SUM(i.quantity * p.cost) as total_value
                                       FROM products p
                                       JOIN inventory i ON p.id = i.product_id
                                       WHERE p.is_active = 1
Total Value Result: 8075.00
Final Stats: {
    "total_products": 78,
    "low_stock": 45,
    "out_of_stock": 30,
    "total_value": 8075,
    "branch_id": 0
}

=== 2025-04-14 22:16:02 ===
Branch ID: 0
Total Products Query (All Branches): SELECT COUNT(*) as total_products
                              FROM products
                              WHERE is_active = 1
Total Products Result: 78
Low Stock Query (All Branches): SELECT COUNT(DISTINCT p.id) as low_stock
                                 FROM products p
                                 JOIN inventory i ON p.id = i.product_id
                                 WHERE p.is_active = 1
                                 AND i.quantity > 0 AND i.quantity <= p.min_quantity
Low Stock Result: 45
Out of Stock Query (All Branches): SELECT COUNT(DISTINCT p.id) as out_of_stock
                                   FROM products p
                                   LEFT JOIN inventory i ON p.id = i.product_id
                                   WHERE p.is_active = 1
                                   AND (i.quantity = 0 OR i.quantity IS NULL)
Out of Stock Result: 30
Total Value Query (All Branches): SELECT SUM(i.quantity * p.cost) as total_value
                                       FROM products p
                                       JOIN inventory i ON p.id = i.product_id
                                       WHERE p.is_active = 1
Total Value Result: 8075.00
Final Stats: {
    "total_products": 78,
    "low_stock": 45,
    "out_of_stock": 30,
    "total_value": 8075,
    "branch_id": 0
}

=== 2025-04-14 22:24:10 ===
Branch ID: 0
Total Products Query (All Branches): SELECT COUNT(*) as total_products
                              FROM products
                              WHERE is_active = 1
Total Products Result: 78
Low Stock Query (All Branches): SELECT COUNT(DISTINCT p.id) as low_stock
                                 FROM products p
                                 JOIN inventory i ON p.id = i.product_id
                                 WHERE p.is_active = 1
                                 AND i.quantity > 0 AND i.quantity <= p.min_quantity
Low Stock Result: 45
Out of Stock Query (All Branches): SELECT COUNT(DISTINCT p.id) as out_of_stock
                                   FROM products p
                                   LEFT JOIN inventory i ON p.id = i.product_id
                                   WHERE p.is_active = 1
                                   AND (i.quantity = 0 OR i.quantity IS NULL)
Out of Stock Result: 30
Total Value Query (All Branches): SELECT SUM(i.quantity * p.cost) as total_value
                                       FROM products p
                                       JOIN inventory i ON p.id = i.product_id
                                       WHERE p.is_active = 1
Total Value Result: 8075.00
Final Stats: {
    "total_products": 78,
    "low_stock": 45,
    "out_of_stock": 30,
    "total_value": 8075,
    "branch_id": 0
}

=== 2025-04-14 22:24:10 ===
Branch ID: 0
Total Products Query (All Branches): SELECT COUNT(*) as total_products
                              FROM products
                              WHERE is_active = 1
Total Products Result: 78
Low Stock Query (All Branches): SELECT COUNT(DISTINCT p.id) as low_stock
                                 FROM products p
                                 JOIN inventory i ON p.id = i.product_id
                                 WHERE p.is_active = 1
                                 AND i.quantity > 0 AND i.quantity <= p.min_quantity
Low Stock Result: 45
Out of Stock Query (All Branches): SELECT COUNT(DISTINCT p.id) as out_of_stock
                                   FROM products p
                                   LEFT JOIN inventory i ON p.id = i.product_id
                                   WHERE p.is_active = 1
                                   AND (i.quantity = 0 OR i.quantity IS NULL)
Out of Stock Result: 30
Total Value Query (All Branches): SELECT SUM(i.quantity * p.cost) as total_value
                                       FROM products p
                                       JOIN inventory i ON p.id = i.product_id
                                       WHERE p.is_active = 1
Total Value Result: 8075.00
Final Stats: {
    "total_products": 78,
    "low_stock": 45,
    "out_of_stock": 30,
    "total_value": 8075,
    "branch_id": 0
}

=== 2025-04-14 22:24:23 ===
Branch ID: 0
Total Products Query (All Branches): SELECT COUNT(*) as total_products
                              FROM products
                              WHERE is_active = 1
Total Products Result: 78
Low Stock Query (All Branches): SELECT COUNT(DISTINCT p.id) as low_stock
                                 FROM products p
                                 JOIN inventory i ON p.id = i.product_id
                                 WHERE p.is_active = 1
                                 AND i.quantity > 0 AND i.quantity <= p.min_quantity
Low Stock Result: 45
Out of Stock Query (All Branches): SELECT COUNT(DISTINCT p.id) as out_of_stock
                                   FROM products p
                                   LEFT JOIN inventory i ON p.id = i.product_id
                                   WHERE p.is_active = 1
                                   AND (i.quantity = 0 OR i.quantity IS NULL)
Out of Stock Result: 30
Total Value Query (All Branches): SELECT SUM(i.quantity * p.cost) as total_value
                                       FROM products p
                                       JOIN inventory i ON p.id = i.product_id
                                       WHERE p.is_active = 1
Total Value Result: 8075.00
Final Stats: {
    "total_products": 78,
    "low_stock": 45,
    "out_of_stock": 30,
    "total_value": 8075,
    "branch_id": 0
}

=== 2025-04-14 22:24:27 ===
Branch ID: 0
Total Products Query (All Branches): SELECT COUNT(*) as total_products
                              FROM products
                              WHERE is_active = 1
Total Products Result: 78
Low Stock Query (All Branches): SELECT COUNT(DISTINCT p.id) as low_stock
                                 FROM products p
                                 JOIN inventory i ON p.id = i.product_id
                                 WHERE p.is_active = 1
                                 AND i.quantity > 0 AND i.quantity <= p.min_quantity
Low Stock Result: 45
Out of Stock Query (All Branches): SELECT COUNT(DISTINCT p.id) as out_of_stock
                                   FROM products p
                                   LEFT JOIN inventory i ON p.id = i.product_id
                                   WHERE p.is_active = 1
                                   AND (i.quantity = 0 OR i.quantity IS NULL)
Out of Stock Result: 30
Total Value Query (All Branches): SELECT SUM(i.quantity * p.cost) as total_value
                                       FROM products p
                                       JOIN inventory i ON p.id = i.product_id
                                       WHERE p.is_active = 1
Total Value Result: 8075.00
Final Stats: {
    "total_products": 78,
    "low_stock": 45,
    "out_of_stock": 30,
    "total_value": 8075,
    "branch_id": 0
}

=== 2025-04-14 22:24:34 ===
Branch ID: 0
Total Products Query (All Branches): SELECT COUNT(*) as total_products
                              FROM products
                              WHERE is_active = 1
Total Products Result: 78
Low Stock Query (All Branches): SELECT COUNT(DISTINCT p.id) as low_stock
                                 FROM products p
                                 JOIN inventory i ON p.id = i.product_id
                                 WHERE p.is_active = 1
                                 AND i.quantity > 0 AND i.quantity <= p.min_quantity
Low Stock Result: 45
Out of Stock Query (All Branches): SELECT COUNT(DISTINCT p.id) as out_of_stock
                                   FROM products p
                                   LEFT JOIN inventory i ON p.id = i.product_id
                                   WHERE p.is_active = 1
                                   AND (i.quantity = 0 OR i.quantity IS NULL)
Out of Stock Result: 30
Total Value Query (All Branches): SELECT SUM(i.quantity * p.cost) as total_value
                                       FROM products p
                                       JOIN inventory i ON p.id = i.product_id
                                       WHERE p.is_active = 1
Total Value Result: 8075.00
Final Stats: {
    "total_products": 78,
    "low_stock": 45,
    "out_of_stock": 30,
    "total_value": 8075,
    "branch_id": 0
}

=== 2025-04-14 22:24:35 ===
Branch ID: 0
Total Products Query (All Branches): SELECT COUNT(*) as total_products
                              FROM products
                              WHERE is_active = 1
Total Products Result: 78
Low Stock Query (All Branches): SELECT COUNT(DISTINCT p.id) as low_stock
                                 FROM products p
                                 JOIN inventory i ON p.id = i.product_id
                                 WHERE p.is_active = 1
                                 AND i.quantity > 0 AND i.quantity <= p.min_quantity
Low Stock Result: 45
Out of Stock Query (All Branches): SELECT COUNT(DISTINCT p.id) as out_of_stock
                                   FROM products p
                                   LEFT JOIN inventory i ON p.id = i.product_id
                                   WHERE p.is_active = 1
                                   AND (i.quantity = 0 OR i.quantity IS NULL)
Out of Stock Result: 30
Total Value Query (All Branches): SELECT SUM(i.quantity * p.cost) as total_value
                                       FROM products p
                                       JOIN inventory i ON p.id = i.product_id
                                       WHERE p.is_active = 1
Total Value Result: 8075.00
Final Stats: {
    "total_products": 78,
    "low_stock": 45,
    "out_of_stock": 30,
    "total_value": 8075,
    "branch_id": 0
}

=== 2025-04-14 22:24:35 ===
Branch ID: 0
Total Products Query (All Branches): SELECT COUNT(*) as total_products
                              FROM products
                              WHERE is_active = 1
Total Products Result: 78
Low Stock Query (All Branches): SELECT COUNT(DISTINCT p.id) as low_stock
                                 FROM products p
                                 JOIN inventory i ON p.id = i.product_id
                                 WHERE p.is_active = 1
                                 AND i.quantity > 0 AND i.quantity <= p.min_quantity
Low Stock Result: 45
Out of Stock Query (All Branches): SELECT COUNT(DISTINCT p.id) as out_of_stock
                                   FROM products p
                                   LEFT JOIN inventory i ON p.id = i.product_id
                                   WHERE p.is_active = 1
                                   AND (i.quantity = 0 OR i.quantity IS NULL)
Out of Stock Result: 30
Total Value Query (All Branches): SELECT SUM(i.quantity * p.cost) as total_value
                                       FROM products p
                                       JOIN inventory i ON p.id = i.product_id
                                       WHERE p.is_active = 1
Total Value Result: 8075.00
Final Stats: {
    "total_products": 78,
    "low_stock": 45,
    "out_of_stock": 30,
    "total_value": 8075,
    "branch_id": 0
}

=== 2025-04-14 22:24:38 ===
Branch ID: 0
Total Products Query (All Branches): SELECT COUNT(*) as total_products
                              FROM products
                              WHERE is_active = 1
Total Products Result: 78
Low Stock Query (All Branches): SELECT COUNT(DISTINCT p.id) as low_stock
                                 FROM products p
                                 JOIN inventory i ON p.id = i.product_id
                                 WHERE p.is_active = 1
                                 AND i.quantity > 0 AND i.quantity <= p.min_quantity
Low Stock Result: 45
Out of Stock Query (All Branches): SELECT COUNT(DISTINCT p.id) as out_of_stock
                                   FROM products p
                                   LEFT JOIN inventory i ON p.id = i.product_id
                                   WHERE p.is_active = 1
                                   AND (i.quantity = 0 OR i.quantity IS NULL)
Out of Stock Result: 30
Total Value Query (All Branches): SELECT SUM(i.quantity * p.cost) as total_value
                                       FROM products p
                                       JOIN inventory i ON p.id = i.product_id
                                       WHERE p.is_active = 1
Total Value Result: 8075.00
Final Stats: {
    "total_products": 78,
    "low_stock": 45,
    "out_of_stock": 30,
    "total_value": 8075,
    "branch_id": 0
}

=== 2025-04-14 22:24:38 ===
Branch ID: 0
Total Products Query (All Branches): SELECT COUNT(*) as total_products
                              FROM products
                              WHERE is_active = 1
Total Products Result: 78
Low Stock Query (All Branches): SELECT COUNT(DISTINCT p.id) as low_stock
                                 FROM products p
                                 JOIN inventory i ON p.id = i.product_id
                                 WHERE p.is_active = 1
                                 AND i.quantity > 0 AND i.quantity <= p.min_quantity
Low Stock Result: 45
Out of Stock Query (All Branches): SELECT COUNT(DISTINCT p.id) as out_of_stock
                                   FROM products p
                                   LEFT JOIN inventory i ON p.id = i.product_id
                                   WHERE p.is_active = 1
                                   AND (i.quantity = 0 OR i.quantity IS NULL)
Out of Stock Result: 30
Total Value Query (All Branches): SELECT SUM(i.quantity * p.cost) as total_value
                                       FROM products p
                                       JOIN inventory i ON p.id = i.product_id
                                       WHERE p.is_active = 1
Total Value Result: 8075.00
Final Stats: {
    "total_products": 78,
    "low_stock": 45,
    "out_of_stock": 30,
    "total_value": 8075,
    "branch_id": 0
}

=== 2025-04-14 22:43:27 ===
Branch ID: 0
Total Products Query (All Branches): SELECT COUNT(*) as total_products
                              FROM products
                              WHERE is_active = 1
Total Products Result: 78
Low Stock Query (All Branches): SELECT COUNT(DISTINCT p.id) as low_stock
                                 FROM products p
                                 JOIN inventory i ON p.id = i.product_id
                                 WHERE p.is_active = 1
                                 AND i.quantity > 0 AND i.quantity <= p.min_quantity
Low Stock Result: 47
Out of Stock Query (All Branches): SELECT COUNT(DISTINCT p.id) as out_of_stock
                                   FROM products p
                                   LEFT JOIN inventory i ON p.id = i.product_id
                                   WHERE p.is_active = 1
                                   AND (i.quantity = 0 OR i.quantity IS NULL)
Out of Stock Result: 31
Total Value Query (All Branches): SELECT SUM(i.quantity * p.cost) as total_value
                                       FROM products p
                                       JOIN inventory i ON p.id = i.product_id
                                       WHERE p.is_active = 1
Total Value Result: 7975.00
Final Stats: {
    "total_products": 78,
    "low_stock": 47,
    "out_of_stock": 31,
    "total_value": 7975,
    "branch_id": 0
}

=== 2025-04-14 22:43:27 ===
Branch ID: 0
Total Products Query (All Branches): SELECT COUNT(*) as total_products
                              FROM products
                              WHERE is_active = 1
Total Products Result: 78
Low Stock Query (All Branches): SELECT COUNT(DISTINCT p.id) as low_stock
                                 FROM products p
                                 JOIN inventory i ON p.id = i.product_id
                                 WHERE p.is_active = 1
                                 AND i.quantity > 0 AND i.quantity <= p.min_quantity
Low Stock Result: 47
Out of Stock Query (All Branches): SELECT COUNT(DISTINCT p.id) as out_of_stock
                                   FROM products p
                                   LEFT JOIN inventory i ON p.id = i.product_id
                                   WHERE p.is_active = 1
                                   AND (i.quantity = 0 OR i.quantity IS NULL)
Out of Stock Result: 31
Total Value Query (All Branches): SELECT SUM(i.quantity * p.cost) as total_value
                                       FROM products p
                                       JOIN inventory i ON p.id = i.product_id
                                       WHERE p.is_active = 1
Total Value Result: 7975.00
Final Stats: {
    "total_products": 78,
    "low_stock": 47,
    "out_of_stock": 31,
    "total_value": 7975,
    "branch_id": 0
}

=== 2025-04-14 22:43:33 ===
Branch ID: 0
Total Products Query (All Branches): SELECT COUNT(*) as total_products
                              FROM products
                              WHERE is_active = 1
Total Products Result: 78
Low Stock Query (All Branches): SELECT COUNT(DISTINCT p.id) as low_stock
                                 FROM products p
                                 JOIN inventory i ON p.id = i.product_id
                                 WHERE p.is_active = 1
                                 AND i.quantity > 0 AND i.quantity <= p.min_quantity
Low Stock Result: 47
Out of Stock Query (All Branches): SELECT COUNT(DISTINCT p.id) as out_of_stock
                                   FROM products p
                                   LEFT JOIN inventory i ON p.id = i.product_id
                                   WHERE p.is_active = 1
                                   AND (i.quantity = 0 OR i.quantity IS NULL)
Out of Stock Result: 31
Total Value Query (All Branches): SELECT SUM(i.quantity * p.cost) as total_value
                                       FROM products p
                                       JOIN inventory i ON p.id = i.product_id
                                       WHERE p.is_active = 1
Total Value Result: 7975.00
Final Stats: {
    "total_products": 78,
    "low_stock": 47,
    "out_of_stock": 31,
    "total_value": 7975,
    "branch_id": 0
}

=== 2025-04-14 22:43:33 ===
Branch ID: 0
Total Products Query (All Branches): SELECT COUNT(*) as total_products
                              FROM products
                              WHERE is_active = 1
Total Products Result: 78
Low Stock Query (All Branches): SELECT COUNT(DISTINCT p.id) as low_stock
                                 FROM products p
                                 JOIN inventory i ON p.id = i.product_id
                                 WHERE p.is_active = 1
                                 AND i.quantity > 0 AND i.quantity <= p.min_quantity
Low Stock Result: 47
Out of Stock Query (All Branches): SELECT COUNT(DISTINCT p.id) as out_of_stock
                                   FROM products p
                                   LEFT JOIN inventory i ON p.id = i.product_id
                                   WHERE p.is_active = 1
                                   AND (i.quantity = 0 OR i.quantity IS NULL)
Out of Stock Result: 31
Total Value Query (All Branches): SELECT SUM(i.quantity * p.cost) as total_value
                                       FROM products p
                                       JOIN inventory i ON p.id = i.product_id
                                       WHERE p.is_active = 1
Total Value Result: 7975.00
Final Stats: {
    "total_products": 78,
    "low_stock": 47,
    "out_of_stock": 31,
    "total_value": 7975,
    "branch_id": 0
}

=== 2025-04-16 04:05:29 ===
Branch ID: 1
Total Products Query (Branch 1): SELECT COUNT(DISTINCT p.id) as total_products
                              FROM products p
                              JOIN inventory i ON p.id = i.product_id
                              WHERE i.branch_id = :branch_id AND p.is_active = 1
Total Products Result: 87
Low Stock Query (Branch 1): SELECT COUNT(DISTINCT p.id) as low_stock
                                 FROM products p
                                 JOIN inventory i ON p.id = i.product_id
                                 WHERE i.branch_id = :branch_id AND p.is_active = 1
                                 AND i.quantity > 0 AND i.quantity <= p.min_quantity
Low Stock Result: 42
Out of Stock Query (Branch 1): SELECT COUNT(DISTINCT p.id) as out_of_stock
                                   FROM products p
                                   LEFT JOIN inventory i ON p.id = i.product_id
                                   WHERE i.branch_id = :branch_id AND p.is_active = 1
                                   AND (i.quantity = 0 OR i.quantity IS NULL)
Out of Stock Result: 41
Total Value Query (Branch 1): SELECT SUM(i.quantity * p.cost) as total_value
                                       FROM products p
                                       JOIN inventory i ON p.id = i.product_id
                                       WHERE i.branch_id = :branch_id AND p.is_active = 1
Total Value Result: 9220.00
Final Stats: {
    "total_products": 87,
    "low_stock": 42,
    "out_of_stock": 41,
    "total_value": 9220,
    "branch_id": "1"
}

=== 2025-04-16 04:05:29 ===
Branch ID: 1
Total Products Query (Branch 1): SELECT COUNT(DISTINCT p.id) as total_products
                              FROM products p
                              JOIN inventory i ON p.id = i.product_id
                              WHERE i.branch_id = :branch_id AND p.is_active = 1
Total Products Result: 87
Low Stock Query (Branch 1): SELECT COUNT(DISTINCT p.id) as low_stock
                                 FROM products p
                                 JOIN inventory i ON p.id = i.product_id
                                 WHERE i.branch_id = :branch_id AND p.is_active = 1
                                 AND i.quantity > 0 AND i.quantity <= p.min_quantity
Low Stock Result: 42
Out of Stock Query (Branch 1): SELECT COUNT(DISTINCT p.id) as out_of_stock
                                   FROM products p
                                   LEFT JOIN inventory i ON p.id = i.product_id
                                   WHERE i.branch_id = :branch_id AND p.is_active = 1
                                   AND (i.quantity = 0 OR i.quantity IS NULL)
Out of Stock Result: 41
Total Value Query (Branch 1): SELECT SUM(i.quantity * p.cost) as total_value
                                       FROM products p
                                       JOIN inventory i ON p.id = i.product_id
                                       WHERE i.branch_id = :branch_id AND p.is_active = 1
Total Value Result: 9220.00
Final Stats: {
    "total_products": 87,
    "low_stock": 42,
    "out_of_stock": 41,
    "total_value": 9220,
    "branch_id": "1"
}

=== 2025-04-16 04:07:01 ===
Branch ID: 1
Total Products Query (Branch 1): SELECT COUNT(DISTINCT p.id) as total_products
                              FROM products p
                              JOIN inventory i ON p.id = i.product_id
                              WHERE i.branch_id = :branch_id AND p.is_active = 1
Total Products Result: 87
Low Stock Query (Branch 1): SELECT COUNT(DISTINCT p.id) as low_stock
                                 FROM products p
                                 JOIN inventory i ON p.id = i.product_id
                                 WHERE i.branch_id = :branch_id AND p.is_active = 1
                                 AND i.quantity > 0 AND i.quantity <= p.min_quantity
Low Stock Result: 42
Out of Stock Query (Branch 1): SELECT COUNT(DISTINCT p.id) as out_of_stock
                                   FROM products p
                                   LEFT JOIN inventory i ON p.id = i.product_id
                                   WHERE i.branch_id = :branch_id AND p.is_active = 1
                                   AND (i.quantity = 0 OR i.quantity IS NULL)
Out of Stock Result: 41
Total Value Query (Branch 1): SELECT SUM(i.quantity * p.cost) as total_value
                                       FROM products p
                                       JOIN inventory i ON p.id = i.product_id
                                       WHERE i.branch_id = :branch_id AND p.is_active = 1
Total Value Result: 9220.00
Final Stats: {
    "total_products": 87,
    "low_stock": 42,
    "out_of_stock": 41,
    "total_value": 9220,
    "branch_id": "1"
}

=== 2025-04-16 04:07:01 ===
Branch ID: 1
Total Products Query (Branch 1): SELECT COUNT(DISTINCT p.id) as total_products
                              FROM products p
                              JOIN inventory i ON p.id = i.product_id
                              WHERE i.branch_id = :branch_id AND p.is_active = 1
Total Products Result: 87
Low Stock Query (Branch 1): SELECT COUNT(DISTINCT p.id) as low_stock
                                 FROM products p
                                 JOIN inventory i ON p.id = i.product_id
                                 WHERE i.branch_id = :branch_id AND p.is_active = 1
                                 AND i.quantity > 0 AND i.quantity <= p.min_quantity
Low Stock Result: 42
Out of Stock Query (Branch 1): SELECT COUNT(DISTINCT p.id) as out_of_stock
                                   FROM products p
                                   LEFT JOIN inventory i ON p.id = i.product_id
                                   WHERE i.branch_id = :branch_id AND p.is_active = 1
                                   AND (i.quantity = 0 OR i.quantity IS NULL)
Out of Stock Result: 41
Total Value Query (Branch 1): SELECT SUM(i.quantity * p.cost) as total_value
                                       FROM products p
                                       JOIN inventory i ON p.id = i.product_id
                                       WHERE i.branch_id = :branch_id AND p.is_active = 1
Total Value Result: 9220.00
Final Stats: {
    "total_products": 87,
    "low_stock": 42,
    "out_of_stock": 41,
    "total_value": 9220,
    "branch_id": "1"
}

=== 2025-04-16 07:25:38 ===
Branch ID: 0
Total Products Query (All Branches): SELECT COUNT(*) as total_products
                              FROM products
                              WHERE is_active = 1
Total Products Result: 92
Low Stock Query (All Branches): SELECT COUNT(DISTINCT p.id) as low_stock
                                 FROM products p
                                 JOIN inventory i ON p.id = i.product_id
                                 WHERE p.is_active = 1
                                 AND i.quantity > 0 AND i.quantity <= p.min_quantity
Low Stock Result: 43
Out of Stock Query (All Branches): SELECT COUNT(DISTINCT p.id) as out_of_stock
                                   FROM products p
                                   LEFT JOIN inventory i ON p.id = i.product_id
                                   WHERE p.is_active = 1
                                   AND (i.quantity = 0 OR i.quantity IS NULL)
Out of Stock Result: 45
Total Value Query (All Branches): SELECT SUM(i.quantity * p.cost) as total_value
                                       FROM products p
                                       JOIN inventory i ON p.id = i.product_id
                                       WHERE p.is_active = 1
Total Value Result: 9390.00
Final Stats: {
    "total_products": 92,
    "low_stock": 43,
    "out_of_stock": 45,
    "total_value": 9390,
    "branch_id": 0
}

=== 2025-04-16 07:25:38 ===
Branch ID: 0
Total Products Query (All Branches): SELECT COUNT(*) as total_products
                              FROM products
                              WHERE is_active = 1
Total Products Result: 92
Low Stock Query (All Branches): SELECT COUNT(DISTINCT p.id) as low_stock
                                 FROM products p
                                 JOIN inventory i ON p.id = i.product_id
                                 WHERE p.is_active = 1
                                 AND i.quantity > 0 AND i.quantity <= p.min_quantity
Low Stock Result: 43
Out of Stock Query (All Branches): SELECT COUNT(DISTINCT p.id) as out_of_stock
                                   FROM products p
                                   LEFT JOIN inventory i ON p.id = i.product_id
                                   WHERE p.is_active = 1
                                   AND (i.quantity = 0 OR i.quantity IS NULL)
Out of Stock Result: 45
Total Value Query (All Branches): SELECT SUM(i.quantity * p.cost) as total_value
                                       FROM products p
                                       JOIN inventory i ON p.id = i.product_id
                                       WHERE p.is_active = 1
Total Value Result: 9390.00
Final Stats: {
    "total_products": 92,
    "low_stock": 43,
    "out_of_stock": 45,
    "total_value": 9390,
    "branch_id": 0
}

=== 2025-04-16 07:41:32 ===
Branch ID: 0
Total Products Query (All Branches): SELECT COUNT(*) as total_products
                              FROM products
                              WHERE is_active = 1
Total Products Result: 91
Low Stock Query (All Branches): SELECT COUNT(DISTINCT p.id) as low_stock
                                 FROM products p
                                 JOIN inventory i ON p.id = i.product_id
                                 WHERE p.is_active = 1
                                 AND i.quantity > 0 AND i.quantity <= p.min_quantity
Low Stock Result: 42
Out of Stock Query (All Branches): SELECT COUNT(DISTINCT p.id) as out_of_stock
                                   FROM products p
                                   LEFT JOIN inventory i ON p.id = i.product_id
                                   WHERE p.is_active = 1
                                   AND (i.quantity = 0 OR i.quantity IS NULL)
Out of Stock Result: 45
Total Value Query (All Branches): SELECT SUM(i.quantity * p.cost) as total_value
                                       FROM products p
                                       JOIN inventory i ON p.id = i.product_id
                                       WHERE p.is_active = 1
Total Value Result: 9220.00
Final Stats: {
    "total_products": 91,
    "low_stock": 42,
    "out_of_stock": 45,
    "total_value": 9220,
    "branch_id": 0
}

=== 2025-04-16 07:41:32 ===
Branch ID: 0
Total Products Query (All Branches): SELECT COUNT(*) as total_products
                              FROM products
                              WHERE is_active = 1
Total Products Result: 91
Low Stock Query (All Branches): SELECT COUNT(DISTINCT p.id) as low_stock
                                 FROM products p
                                 JOIN inventory i ON p.id = i.product_id
                                 WHERE p.is_active = 1
                                 AND i.quantity > 0 AND i.quantity <= p.min_quantity
Low Stock Result: 42
Out of Stock Query (All Branches): SELECT COUNT(DISTINCT p.id) as out_of_stock
                                   FROM products p
                                   LEFT JOIN inventory i ON p.id = i.product_id
                                   WHERE p.is_active = 1
                                   AND (i.quantity = 0 OR i.quantity IS NULL)
Out of Stock Result: 45
Total Value Query (All Branches): SELECT SUM(i.quantity * p.cost) as total_value
                                       FROM products p
                                       JOIN inventory i ON p.id = i.product_id
                                       WHERE p.is_active = 1
Total Value Result: 9220.00
Final Stats: {
    "total_products": 91,
    "low_stock": 42,
    "out_of_stock": 45,
    "total_value": 9220,
    "branch_id": 0
}

=== 2025-04-16 09:15:25 ===
Branch ID: 1
Total Products Query (Branch 1): SELECT COUNT(DISTINCT p.id) as total_products
                              FROM products p
                              JOIN inventory i ON p.id = i.product_id
                              WHERE i.branch_id = :branch_id AND p.is_active = 1
Total Products Result: 87
Low Stock Query (Branch 1): SELECT COUNT(DISTINCT p.id) as low_stock
                                 FROM products p
                                 JOIN inventory i ON p.id = i.product_id
                                 WHERE i.branch_id = :branch_id AND p.is_active = 1
                                 AND i.quantity > 0 AND i.quantity <= p.min_quantity
Low Stock Result: 42
Out of Stock Query (Branch 1): SELECT COUNT(DISTINCT p.id) as out_of_stock
                                   FROM products p
                                   LEFT JOIN inventory i ON p.id = i.product_id
                                   WHERE i.branch_id = :branch_id AND p.is_active = 1
                                   AND (i.quantity = 0 OR i.quantity IS NULL)
Out of Stock Result: 41
Total Value Query (Branch 1): SELECT SUM(i.quantity * p.cost) as total_value
                                       FROM products p
                                       JOIN inventory i ON p.id = i.product_id
                                       WHERE i.branch_id = :branch_id AND p.is_active = 1
Total Value Result: 9220.00
Final Stats: {
    "total_products": 87,
    "low_stock": 42,
    "out_of_stock": 41,
    "total_value": 9220,
    "branch_id": "1"
}

=== 2025-04-16 09:15:25 ===
Branch ID: 1
Total Products Query (Branch 1): SELECT COUNT(DISTINCT p.id) as total_products
                              FROM products p
                              JOIN inventory i ON p.id = i.product_id
                              WHERE i.branch_id = :branch_id AND p.is_active = 1
Total Products Result: 87
Low Stock Query (Branch 1): SELECT COUNT(DISTINCT p.id) as low_stock
                                 FROM products p
                                 JOIN inventory i ON p.id = i.product_id
                                 WHERE i.branch_id = :branch_id AND p.is_active = 1
                                 AND i.quantity > 0 AND i.quantity <= p.min_quantity
Low Stock Result: 42
Out of Stock Query (Branch 1): SELECT COUNT(DISTINCT p.id) as out_of_stock
                                   FROM products p
                                   LEFT JOIN inventory i ON p.id = i.product_id
                                   WHERE i.branch_id = :branch_id AND p.is_active = 1
                                   AND (i.quantity = 0 OR i.quantity IS NULL)
Out of Stock Result: 41
Total Value Query (Branch 1): SELECT SUM(i.quantity * p.cost) as total_value
                                       FROM products p
                                       JOIN inventory i ON p.id = i.product_id
                                       WHERE i.branch_id = :branch_id AND p.is_active = 1
Total Value Result: 9220.00
Final Stats: {
    "total_products": 87,
    "low_stock": 42,
    "out_of_stock": 41,
    "total_value": 9220,
    "branch_id": "1"
}

=== 2025-04-17 13:03:46 ===
Branch ID: 0
Total Products Query (All Branches): SELECT COUNT(*) as total_products
                              FROM products
                              WHERE is_active = 1
Total Products Result: 91
Low Stock Query (All Branches): SELECT COUNT(DISTINCT p.id) as low_stock
                                 FROM products p
                                 JOIN inventory i ON p.id = i.product_id
                                 WHERE p.is_active = 1
                                 AND i.quantity > 0 AND i.quantity <= p.min_quantity
Low Stock Result: 42
Out of Stock Query (All Branches): SELECT COUNT(DISTINCT p.id) as out_of_stock
                                   FROM products p
                                   LEFT JOIN inventory i ON p.id = i.product_id
                                   WHERE p.is_active = 1
                                   AND (i.quantity = 0 OR i.quantity IS NULL)
Out of Stock Result: 45
Total Value Query (All Branches): SELECT SUM(i.quantity * p.cost) as total_value
                                       FROM products p
                                       JOIN inventory i ON p.id = i.product_id
                                       WHERE p.is_active = 1
Total Value Result: 9220.00
Final Stats: {
    "total_products": 91,
    "low_stock": 42,
    "out_of_stock": 45,
    "total_value": 9220,
    "branch_id": 0
}

=== 2025-04-17 13:03:46 ===
Branch ID: 0
Total Products Query (All Branches): SELECT COUNT(*) as total_products
                              FROM products
                              WHERE is_active = 1
Total Products Result: 91
Low Stock Query (All Branches): SELECT COUNT(DISTINCT p.id) as low_stock
                                 FROM products p
                                 JOIN inventory i ON p.id = i.product_id
                                 WHERE p.is_active = 1
                                 AND i.quantity > 0 AND i.quantity <= p.min_quantity
Low Stock Result: 42
Out of Stock Query (All Branches): SELECT COUNT(DISTINCT p.id) as out_of_stock
                                   FROM products p
                                   LEFT JOIN inventory i ON p.id = i.product_id
                                   WHERE p.is_active = 1
                                   AND (i.quantity = 0 OR i.quantity IS NULL)
Out of Stock Result: 45
Total Value Query (All Branches): SELECT SUM(i.quantity * p.cost) as total_value
                                       FROM products p
                                       JOIN inventory i ON p.id = i.product_id
                                       WHERE p.is_active = 1
Total Value Result: 9220.00
Final Stats: {
    "total_products": 91,
    "low_stock": 42,
    "out_of_stock": 45,
    "total_value": 9220,
    "branch_id": 0
}

=== 2025-05-05 01:46:56 ===
Branch ID: 1
Total Products Query (Branch 1): SELECT COUNT(DISTINCT p.id) as total_products
                              FROM products p
                              JOIN inventory i ON p.id = i.product_id
                              WHERE i.branch_id = :branch_id AND p.is_active = 1
Total Products Result: 87
Low Stock Query (Branch 1): SELECT COUNT(DISTINCT p.id) as low_stock
                                 FROM products p
                                 JOIN inventory i ON p.id = i.product_id
                                 WHERE i.branch_id = :branch_id AND p.is_active = 1
                                 AND i.quantity > 0 AND i.quantity <= p.min_quantity
Low Stock Result: 37
Out of Stock Query (Branch 1): SELECT COUNT(DISTINCT p.id) as out_of_stock
                                   FROM products p
                                   LEFT JOIN inventory i ON p.id = i.product_id
                                   WHERE i.branch_id = :branch_id AND p.is_active = 1
                                   AND (i.quantity = 0 OR i.quantity IS NULL)
Out of Stock Result: 46
Total Value Query (Branch 1): SELECT SUM(i.quantity * p.cost) as total_value
                                       FROM products p
                                       JOIN inventory i ON p.id = i.product_id
                                       WHERE i.branch_id = :branch_id AND p.is_active = 1
Total Value Result: 8680.00
Final Stats: {
    "total_products": 87,
    "low_stock": 37,
    "out_of_stock": 46,
    "total_value": 8680,
    "branch_id": "1"
}

=== 2025-05-05 01:46:56 ===
Branch ID: 1
Total Products Query (Branch 1): SELECT COUNT(DISTINCT p.id) as total_products
                              FROM products p
                              JOIN inventory i ON p.id = i.product_id
                              WHERE i.branch_id = :branch_id AND p.is_active = 1
Total Products Result: 87
Low Stock Query (Branch 1): SELECT COUNT(DISTINCT p.id) as low_stock
                                 FROM products p
                                 JOIN inventory i ON p.id = i.product_id
                                 WHERE i.branch_id = :branch_id AND p.is_active = 1
                                 AND i.quantity > 0 AND i.quantity <= p.min_quantity
Low Stock Result: 37
Out of Stock Query (Branch 1): SELECT COUNT(DISTINCT p.id) as out_of_stock
                                   FROM products p
                                   LEFT JOIN inventory i ON p.id = i.product_id
                                   WHERE i.branch_id = :branch_id AND p.is_active = 1
                                   AND (i.quantity = 0 OR i.quantity IS NULL)
Out of Stock Result: 46
Total Value Query (Branch 1): SELECT SUM(i.quantity * p.cost) as total_value
                                       FROM products p
                                       JOIN inventory i ON p.id = i.product_id
                                       WHERE i.branch_id = :branch_id AND p.is_active = 1
Total Value Result: 8680.00
Final Stats: {
    "total_products": 87,
    "low_stock": 37,
    "out_of_stock": 46,
    "total_value": 8680,
    "branch_id": "1"
}

=== 2025-05-08 17:05:06 ===
Branch ID: 0
Total Products Query (All Branches): SELECT COUNT(*) as total_products
                              FROM products
                              WHERE is_active = 1
Total Products Result: 91
Low Stock Query (All Branches): SELECT COUNT(DISTINCT p.id) as low_stock
                                 FROM products p
                                 JOIN inventory i ON p.id = i.product_id
                                 WHERE p.is_active = 1
                                 AND i.quantity > 0 AND i.quantity <= p.min_quantity
Low Stock Result: 37
Out of Stock Query (All Branches): SELECT COUNT(DISTINCT p.id) as out_of_stock
                                   FROM products p
                                   LEFT JOIN inventory i ON p.id = i.product_id
                                   WHERE p.is_active = 1
                                   AND (i.quantity = 0 OR i.quantity IS NULL)
Out of Stock Result: 50
Total Value Query (All Branches): SELECT SUM(i.quantity * p.cost) as total_value
                                       FROM products p
                                       JOIN inventory i ON p.id = i.product_id
                                       WHERE p.is_active = 1
Total Value Result: 8680.00
Final Stats: {
    "total_products": 91,
    "low_stock": 37,
    "out_of_stock": 50,
    "total_value": 8680,
    "branch_id": 0
}

=== 2025-05-08 17:05:06 ===
Branch ID: 0
Total Products Query (All Branches): SELECT COUNT(*) as total_products
                              FROM products
                              WHERE is_active = 1
Total Products Result: 91
Low Stock Query (All Branches): SELECT COUNT(DISTINCT p.id) as low_stock
                                 FROM products p
                                 JOIN inventory i ON p.id = i.product_id
                                 WHERE p.is_active = 1
                                 AND i.quantity > 0 AND i.quantity <= p.min_quantity
Low Stock Result: 37
Out of Stock Query (All Branches): SELECT COUNT(DISTINCT p.id) as out_of_stock
                                   FROM products p
                                   LEFT JOIN inventory i ON p.id = i.product_id
                                   WHERE p.is_active = 1
                                   AND (i.quantity = 0 OR i.quantity IS NULL)
Out of Stock Result: 50
Total Value Query (All Branches): SELECT SUM(i.quantity * p.cost) as total_value
                                       FROM products p
                                       JOIN inventory i ON p.id = i.product_id
                                       WHERE p.is_active = 1
Total Value Result: 8680.00
Final Stats: {
    "total_products": 91,
    "low_stock": 37,
    "out_of_stock": 50,
    "total_value": 8680,
    "branch_id": 0
}

=== 2025-05-11 13:36:57 ===
Branch ID: 1
Total Products Query (Branch 1): SELECT COUNT(DISTINCT p.id) as total_products
                              FROM products p
                              JOIN inventory i ON p.id = i.product_id
                              WHERE i.branch_id = :branch_id AND p.is_active = 1
Total Products Result: 88
Low Stock Query (Branch 1): SELECT COUNT(DISTINCT p.id) as low_stock
                                 FROM products p
                                 JOIN inventory i ON p.id = i.product_id
                                 WHERE i.branch_id = :branch_id AND p.is_active = 1
                                 AND i.quantity > 0 AND i.quantity <= p.min_quantity
Low Stock Result: 37
Out of Stock Query (Branch 1): SELECT COUNT(DISTINCT p.id) as out_of_stock
                                   FROM products p
                                   LEFT JOIN inventory i ON p.id = i.product_id
                                   WHERE i.branch_id = :branch_id AND p.is_active = 1
                                   AND (i.quantity = 0 OR i.quantity IS NULL)
Out of Stock Result: 48
Total Value Query (Branch 1): SELECT SUM(i.quantity * p.cost) as total_value
                                       FROM products p
                                       JOIN inventory i ON p.id = i.product_id
                                       WHERE i.branch_id = :branch_id AND p.is_active = 1
Total Value Result: 7985.00
Final Stats: {
    "total_products": 88,
    "low_stock": 37,
    "out_of_stock": 48,
    "total_value": 7985,
    "branch_id": "1"
}

=== 2025-05-11 13:36:57 ===
Branch ID: 1
Total Products Query (Branch 1): SELECT COUNT(DISTINCT p.id) as total_products
                              FROM products p
                              JOIN inventory i ON p.id = i.product_id
                              WHERE i.branch_id = :branch_id AND p.is_active = 1
Total Products Result: 88
Low Stock Query (Branch 1): SELECT COUNT(DISTINCT p.id) as low_stock
                                 FROM products p
                                 JOIN inventory i ON p.id = i.product_id
                                 WHERE i.branch_id = :branch_id AND p.is_active = 1
                                 AND i.quantity > 0 AND i.quantity <= p.min_quantity
Low Stock Result: 37
Out of Stock Query (Branch 1): SELECT COUNT(DISTINCT p.id) as out_of_stock
                                   FROM products p
                                   LEFT JOIN inventory i ON p.id = i.product_id
                                   WHERE i.branch_id = :branch_id AND p.is_active = 1
                                   AND (i.quantity = 0 OR i.quantity IS NULL)
Out of Stock Result: 48
Total Value Query (Branch 1): SELECT SUM(i.quantity * p.cost) as total_value
                                       FROM products p
                                       JOIN inventory i ON p.id = i.product_id
                                       WHERE i.branch_id = :branch_id AND p.is_active = 1
Total Value Result: 7985.00
Final Stats: {
    "total_products": 88,
    "low_stock": 37,
    "out_of_stock": 48,
    "total_value": 7985,
    "branch_id": "1"
}

=== 2025-05-17 01:06:08 ===
Branch ID: 0
Total Products Query (All Branches): SELECT COUNT(*) as total_products
                              FROM products
                              WHERE is_active = 1
Total Products Result: 92
Low Stock Query (All Branches): SELECT COUNT(DISTINCT p.id) as low_stock
                                 FROM products p
                                 JOIN inventory i ON p.id = i.product_id
                                 WHERE p.is_active = 1
                                 AND i.quantity > 0 AND i.quantity <= p.min_quantity
Low Stock Result: 32
Out of Stock Query (All Branches): SELECT COUNT(DISTINCT p.id) as out_of_stock
                                   FROM products p
                                   LEFT JOIN inventory i ON p.id = i.product_id
                                   WHERE p.is_active = 1
                                   AND (i.quantity = 0 OR i.quantity IS NULL)
Out of Stock Result: 57
Total Value Query (All Branches): SELECT SUM(i.quantity * p.cost) as total_value
                                       FROM products p
                                       JOIN inventory i ON p.id = i.product_id
                                       WHERE p.is_active = 1
Total Value Result: 7297.00
Final Stats: {
    "total_products": 92,
    "low_stock": 32,
    "out_of_stock": 57,
    "total_value": 7297,
    "branch_id": 0
}

=== 2025-05-17 01:06:08 ===
Branch ID: 0
Total Products Query (All Branches): SELECT COUNT(*) as total_products
                              FROM products
                              WHERE is_active = 1
Total Products Result: 92
Low Stock Query (All Branches): SELECT COUNT(DISTINCT p.id) as low_stock
                                 FROM products p
                                 JOIN inventory i ON p.id = i.product_id
                                 WHERE p.is_active = 1
                                 AND i.quantity > 0 AND i.quantity <= p.min_quantity
Low Stock Result: 32
Out of Stock Query (All Branches): SELECT COUNT(DISTINCT p.id) as out_of_stock
                                   FROM products p
                                   LEFT JOIN inventory i ON p.id = i.product_id
                                   WHERE p.is_active = 1
                                   AND (i.quantity = 0 OR i.quantity IS NULL)
Out of Stock Result: 57
Total Value Query (All Branches): SELECT SUM(i.quantity * p.cost) as total_value
                                       FROM products p
                                       JOIN inventory i ON p.id = i.product_id
                                       WHERE p.is_active = 1
Total Value Result: 7297.00
Final Stats: {
    "total_products": 92,
    "low_stock": 32,
    "out_of_stock": 57,
    "total_value": 7297,
    "branch_id": 0
}

=== 2025-06-04 18:40:20 ===
Branch ID: 0
Total Products Query (All Branches): SELECT COUNT(*) as total_products
                              FROM products
                              WHERE is_active = 1
Total Products Result: 92
Low Stock Query (All Branches): SELECT COUNT(DISTINCT p.id) as low_stock
                                 FROM products p
                                 JOIN inventory i ON p.id = i.product_id
                                 WHERE p.is_active = 1
                                 AND i.quantity > 0 AND i.quantity <= p.min_quantity
Low Stock Result: 31
Out of Stock Query (All Branches): SELECT COUNT(DISTINCT p.id) as out_of_stock
                                   FROM products p
                                   LEFT JOIN inventory i ON p.id = i.product_id
                                   WHERE p.is_active = 1
                                   AND (i.quantity = 0 OR i.quantity IS NULL)
Out of Stock Result: 58
Total Value Query (All Branches): SELECT SUM(i.quantity * p.cost) as total_value
                                       FROM products p
                                       JOIN inventory i ON p.id = i.product_id
                                       WHERE p.is_active = 1
Total Value Result: 7087.00
Final Stats: {
    "total_products": 92,
    "low_stock": 31,
    "out_of_stock": 58,
    "total_value": 7087,
    "branch_id": 0
}

=== 2025-06-04 18:40:20 ===
Branch ID: 0
Total Products Query (All Branches): SELECT COUNT(*) as total_products
                              FROM products
                              WHERE is_active = 1
Total Products Result: 92
Low Stock Query (All Branches): SELECT COUNT(DISTINCT p.id) as low_stock
                                 FROM products p
                                 JOIN inventory i ON p.id = i.product_id
                                 WHERE p.is_active = 1
                                 AND i.quantity > 0 AND i.quantity <= p.min_quantity
Low Stock Result: 31
Out of Stock Query (All Branches): SELECT COUNT(DISTINCT p.id) as out_of_stock
                                   FROM products p
                                   LEFT JOIN inventory i ON p.id = i.product_id
                                   WHERE p.is_active = 1
                                   AND (i.quantity = 0 OR i.quantity IS NULL)
Out of Stock Result: 58
Total Value Query (All Branches): SELECT SUM(i.quantity * p.cost) as total_value
                                       FROM products p
                                       JOIN inventory i ON p.id = i.product_id
                                       WHERE p.is_active = 1
Total Value Result: 7087.00
Final Stats: {
    "total_products": 92,
    "low_stock": 31,
    "out_of_stock": 58,
    "total_value": 7087,
    "branch_id": 0
}

