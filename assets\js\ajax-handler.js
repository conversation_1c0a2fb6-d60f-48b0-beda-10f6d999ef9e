/**
 * Salon Management System - AJAX Handler
 * Centralized AJAX request management utility
 * 
 * @version 1.0.0
 * <AUTHOR> Management System Team
 */
(function(window, $) {
    'use strict';

    // AJAX Handler Singleton
    const AjaxHandler = {
        /**
         * Default AJAX configuration
         * @type {Object}
         */
        defaultConfig: {
            baseUrl: '/api/',
            timeout: 30000,
            contentType: 'application/json',
            dataType: 'json',
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        },

        /**
         * Perform a GET request
         * @param {string} endpoint - API endpoint
         * @param {Object} [params] - Query parameters
         * @param {Object} [config] - Additional configuration
         * @returns {Promise} - Promise resolving with response data
         */
        get: function(endpoint, params = {}, config = {}) {
            return this.request('GET', endpoint, params, config);
        },

        /**
         * Perform a POST request
         * @param {string} endpoint - API endpoint
         * @param {Object} data - Request payload
         * @param {Object} [config] - Additional configuration
         * @returns {Promise} - Promise resolving with response data
         */
        post: function(endpoint, data, config = {}) {
            return this.request('POST', endpoint, data, config);
        },

        /**
         * Perform a PUT request
         * @param {string} endpoint - API endpoint
         * @param {Object} data - Request payload
         * @param {Object} [config] - Additional configuration
         * @returns {Promise} - Promise resolving with response data
         */
        put: function(endpoint, data, config = {}) {
            return this.request('PUT', endpoint, data, config);
        },

        /**
         * Perform a DELETE request
         * @param {string} endpoint - API endpoint
         * @param {Object} [params] - Query parameters
         * @param {Object} [config] - Additional configuration
         * @returns {Promise} - Promise resolving with response data
         */
        delete: function(endpoint, params = {}, config = {}) {
            return this.request('DELETE', endpoint, params, config);
        },

        /**
         * Core AJAX request method
         * @param {string} method - HTTP method
         * @param {string} endpoint - API endpoint
         * @param {Object} data - Request data
         * @param {Object} [customConfig] - Custom configuration
         * @returns {Promise} - Promise resolving with response data
         */
        request: function(method, endpoint, data, customConfig = {}) {
            // Merge default and custom configurations
            const config = $.extend(true, {}, this.defaultConfig, customConfig, {
                method: method,
                url: this.defaultConfig.baseUrl + endpoint,
                data: method === 'GET' ? data : JSON.stringify(data)
            });

            // Show loading indicator
            this.showLoading();

            // Return a promise
            return new Promise((resolve, reject) => {
                $.ajax(config)
                    .done((response) => {
                        this.hideLoading();
                        
                        // Check for API-level errors
                        if (response.status === 'error') {
                            this.handleError(response.message);
                            reject(response);
                            return;
                        }

                        resolve(response);
                    })
                    .fail((jqXHR, textStatus, errorThrown) => {
                        this.hideLoading();
                        this.handleError(this.getErrorMessage(jqXHR, textStatus, errorThrown));
                        reject(jqXHR);
                    });
            });
        },

        /**
         * Generate user-friendly error message
         * @param {Object} jqXHR - jQuery XHR object
         * @param {string} textStatus - Error status
         * @param {string} errorThrown - Error description
         * @returns {string} - Formatted error message
         */
        getErrorMessage: function(jqXHR, textStatus, errorThrown) {
            if (jqXHR.responseJSON && jqXHR.responseJSON.message) {
                return jqXHR.responseJSON.message;
            }

            switch (jqXHR.status) {
                case 400:
                    return 'Bad Request: The server cannot process the request.';
                case 401:
                    return 'Unauthorized: Please log in again.';
                case 403:
                    return 'Forbidden: You do not have permission to access this resource.';
                case 404:
                    return 'Not Found: The requested resource could not be found.';
                case 500:
                    return 'Internal Server Error: Something went wrong on the server.';
                case 503:
                    return 'Service Unavailable: The server is temporarily unable to handle the request.';
                default:
                    return `Request failed: ${textStatus} - ${errorThrown}`;
            }
        },

        /**
         * Handle and display error messages
         * @param {string} message - Error message to display
         */
        handleError: function(message) {
            // Optionally integrate with a toast or alert system
            console.error(message);
            
            // Example of showing an error alert
            this.showAlert(message, 'danger');
        },

        /**
         * Show a loading indicator
         */
        showLoading: function() {
            // Create or show a loading spinner
            if (!this.loadingIndicator) {
                this.loadingIndicator = $(`
                    <div class="ajax-loading">
                        <div class="spinner">
                            <div class="bounce1"></div>
                            <div class="bounce2"></div>
                            <div class="bounce3"></div>
                        </div>
                    </div>
                `).appendTo('body');
            }
            this.loadingIndicator.show();
        },

        /**
         * Hide the loading indicator
         */
        hideLoading: function() {
            if (this.loadingIndicator) {
                this.loadingIndicator.hide();
            }
        },

        /**
         * Display an alert message
         * @param {string} message - Message to display
         * @param {string} [type='info'] - Alert type (success, danger, warning, info)
         * @param {number} [duration=5000] - Duration to show the alert
         */
        showAlert: function(message, type = 'info', duration = 5000) {
            const alertHtml = `
                <div class="alert alert-${type} alert-dismissible fade show" role="alert">
                    ${message}
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
            `;

            const $alert = $(alertHtml).appendTo('body');

            // Automatically remove alert after specified duration
            setTimeout(() => {
                $alert.alert('close');
            }, duration);
        },

        /**
         * Upload files via AJAX
         * @param {string} endpoint - Upload endpoint
         * @param {FormData} formData - Form data with files
         * @param {Object} [config] - Additional configuration
         * @returns {Promise} - Promise resolving with upload response
         */
        uploadFiles: function(endpoint, formData, config = {}) {
            const uploadConfig = $.extend({}, this.defaultConfig, config, {
                url: this.defaultConfig.baseUrl + endpoint,
                processData: false,
                contentType: false
            });

            return this.request('POST', endpoint, formData, uploadConfig);
        },

        /**
         * Initialize global AJAX event listeners
         */
        init: function() {
            // Global AJAX error handler
            $(document).ajaxError((event, jqXHR) => {
                if (jqXHR.status === 401) {
                    // Redirect to login on unauthorized access
                    window.location.href = '/login';
                }
            });

            // Add CSS for loading spinner
            $('head').append(`
                <style>
                    .ajax-loading {
                        position: fixed;
                        top: 0;
                        left: 0;
                        width: 100%;
                        height: 100%;
                        background: rgba(255,255,255,0.7);
                        z-index: 9999;
                        display: none;
                        justify-content: center;
                        align-items: center;
                    }
                    .ajax-loading .spinner {
                        width: 70px;
                        text-align: center;
                    }
                    .ajax-loading .spinner > div {
                        width: 18px;
                        height: 18px;
                        background-color: #007bff;
                        border-radius: 100%;
                        display: inline-block;
                        animation: sk-bouncedelay 1.4s infinite ease-in-out both;
                    }
                    .ajax-loading .spinner .bounce1 {
                        animation-delay: -0.32s;
                    }
                    .ajax-loading .spinner .bounce2 {
                        animation-delay: -0.16s;
                    }
                    @keyframes sk-bouncedelay {
                        0%, 80%, 100% { transform: scale(0); }
                        40% { transform: scale(1.0); }
                    }
                </style>
            `);

            return this;
        }
    };

    // Expose AjaxHandler to global scope
    window.AjaxHandler = AjaxHandler;

    // Initialize on document ready
    $(document).ready(() => {
        AjaxHandler.init();
    });

})(window, jQuery);

// Convenience methods for quick access
function ajaxGet(endpoint, params, config) {
    return window.AjaxHandler.get(endpoint, params, config);
}

function ajaxPost(endpoint, data, config) {
    return window.AjaxHandler.post(endpoint, data, config);
}

function ajaxPut(endpoint, data, config) {
    return window.AjaxHandler.put(endpoint, data, config);
}

function ajaxDelete(endpoint, params, config) {
    return window.AjaxHandler.delete(endpoint, params, config);
}