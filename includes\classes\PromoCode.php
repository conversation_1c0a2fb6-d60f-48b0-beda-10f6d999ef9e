<?php
/**
 * فئة أكواد الترويج (Promo Codes)
 * تتعامل مع إدارة أكواد الترويج والخصومات
 */

// منع الوصول المباشر للملف
if (!defined('BASEPATH')) {
    exit('لا يمكن الوصول المباشر لهذا الملف');
}

class PromoCode {
    private $db;

    /**
     * إنشاء كائن من فئة أكواد الترويج
     * @param Database $db كائن قاعدة البيانات
     */
    public function __construct($db) {
        $this->db = $db;
    }

    /**
     * إضافة كود ترويج جديد
     * @param array $data بيانات كود الترويج
     * @return int|false معرف كود الترويج الجديد أو false إذا فشلت العملية
     */
    public function addPromoCode($data) {
        try {
            // التحقق من وجود كود بنفس الاسم
            $this->db->prepare("SELECT id FROM promo_codes WHERE code = :code");
            $this->db->bind(':code', $data['code']);
            if ($this->db->fetch()) {
                throw new Exception('يوجد كود ترويج مسجل بنفس الرمز');
            }

            // إدراج كود الترويج الجديد
            $this->db->prepare("INSERT INTO promo_codes (
                code, name, description, discount_type, discount_value,
                min_invoice_amount, max_invoice_amount, max_uses, one_use_per_customer,
                is_for_specific_customers, required_loyalty_points,
                start_date, end_date, is_active, branch_id
            ) VALUES (
                :code, :name, :description, :discount_type, :discount_value,
                :min_invoice_amount, :max_invoice_amount, :max_uses, :one_use_per_customer,
                :is_for_specific_customers, :required_loyalty_points,
                :start_date, :end_date, :is_active, :branch_id
            )");

            // ربط البيانات
            $this->db->bind(':code', $data['code']);
            $this->db->bind(':name', $data['name']);
            $this->db->bind(':description', $data['description'] ?? null);
            $this->db->bind(':discount_type', $data['discount_type']);
            $this->db->bind(':discount_value', $data['discount_value']);
            $this->db->bind(':min_invoice_amount', $data['min_invoice_amount'] ?? null);
            $this->db->bind(':max_invoice_amount', $data['max_invoice_amount'] ?? null);
            $this->db->bind(':max_uses', $data['max_uses'] ?? null);
            $this->db->bind(':one_use_per_customer', $data['one_use_per_customer'] ?? 0);
            $this->db->bind(':is_for_specific_customers', $data['is_for_specific_customers'] ?? 0);
            $this->db->bind(':required_loyalty_points', $data['required_loyalty_points'] ?? null);
            $this->db->bind(':start_date', $data['start_date'] ?? null);
            $this->db->bind(':end_date', $data['end_date'] ?? null);
            $this->db->bind(':is_active', $data['is_active'] ?? 1);
            $this->db->bind(':branch_id', $data['branch_id'] ?? null);

            // تنفيذ الاستعلام
            $this->db->execute();
            $promoCodeId = $this->db->lastInsertId();

            // إذا كان الكود مخصصًا لعملاء محددين، قم بإضافة العلاقات
            if (isset($data['customer_ids']) && is_array($data['customer_ids']) && !empty($data['customer_ids'])) {
                foreach ($data['customer_ids'] as $customerId) {
                    $this->addPromoCodeCustomer($promoCodeId, $customerId);
                }
            }

            return $promoCodeId;
        } catch (Exception $e) {
            error_log('خطأ أثناء إضافة كود ترويج: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * تحديث كود ترويج موجود
     * @param int $id معرف كود الترويج
     * @param array $data بيانات كود الترويج المحدثة
     * @return bool نجاح أو فشل العملية
     */
    public function updatePromoCode($id, $data) {
        try {
            // التحقق من وجود كود بنفس الاسم (غير الكود الحالي)
            $this->db->prepare("SELECT id FROM promo_codes WHERE code = :code AND id != :id");
            $this->db->bind(':code', $data['code']);
            $this->db->bind(':id', $id);
            if ($this->db->fetch()) {
                throw new Exception('يوجد كود ترويج آخر مسجل بنفس الرمز');
            }

            // تحديث كود الترويج
            $this->db->prepare("UPDATE promo_codes SET
                code = :code,
                name = :name,
                description = :description,
                discount_type = :discount_type,
                discount_value = :discount_value,
                min_invoice_amount = :min_invoice_amount,
                max_invoice_amount = :max_invoice_amount,
                max_uses = :max_uses,
                one_use_per_customer = :one_use_per_customer,
                is_for_specific_customers = :is_for_specific_customers,
                required_loyalty_points = :required_loyalty_points,
                start_date = :start_date,
                end_date = :end_date,
                is_active = :is_active,
                branch_id = :branch_id
            WHERE id = :id");

            // ربط البيانات
            $this->db->bind(':id', $id);
            $this->db->bind(':code', $data['code']);
            $this->db->bind(':name', $data['name']);
            $this->db->bind(':description', $data['description'] ?? null);
            $this->db->bind(':discount_type', $data['discount_type']);
            $this->db->bind(':discount_value', $data['discount_value']);
            $this->db->bind(':min_invoice_amount', $data['min_invoice_amount'] ?? null);
            $this->db->bind(':max_invoice_amount', $data['max_invoice_amount'] ?? null);
            $this->db->bind(':max_uses', $data['max_uses'] ?? null);
            $this->db->bind(':one_use_per_customer', isset($data['one_use_per_customer']) && $data['one_use_per_customer'] == 1 ? 1 : 0);
            $this->db->bind(':is_for_specific_customers', isset($data['is_for_specific_customers']) && $data['is_for_specific_customers'] == 1 ? 1 : 0);
            $this->db->bind(':required_loyalty_points', $data['required_loyalty_points'] ?? null);
            $this->db->bind(':start_date', $data['start_date'] ?? null);
            $this->db->bind(':end_date', $data['end_date'] ?? null);
            $this->db->bind(':is_active', isset($data['is_active']) && $data['is_active'] == 1 ? 1 : 0);
            $this->db->bind(':branch_id', $data['branch_id'] ?? null);

            // للتأكد من أن قيمة is_for_specific_customers يتم تعيينها بشكل صحيح
            error_log('is_for_specific_customers value in updatePromoCode: ' . (isset($data['is_for_specific_customers']) ? $data['is_for_specific_customers'] : 'not set'));

            // تنفيذ الاستعلام
            $this->db->execute();

            // حذف العلاقات الحالية بغض النظر عن الخيار
            $this->deletePromoCodeCustomers($id);

            // إذا كان الكود مخصصًا لعملاء محددين، قم بإضافة العلاقات الجديدة
            if (isset($data['is_for_specific_customers']) && $data['is_for_specific_customers'] == 1) {
                // إضافة العلاقات الجديدة
                if (isset($data['customer_ids']) && is_array($data['customer_ids']) && !empty($data['customer_ids'])) {
                    foreach ($data['customer_ids'] as $customerId) {
                        $this->addPromoCodeCustomer($id, $customerId);
                    }
                }
            } else {
                // للتأكد من أن العلاقات مع العملاء تم حذفها بالفعل
                error_log('is_for_specific_customers is not set or 0, no customer relationships will be added');
            }

            return true;
        } catch (Exception $e) {
            error_log('خطأ أثناء تحديث كود ترويج: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * حذف كود ترويج
     * @param int $id معرف كود الترويج
     * @return bool نجاح أو فشل العملية
     */
    public function deletePromoCode($id) {
        try {
            // حذف كود الترويج
            $this->db->prepare("DELETE FROM promo_codes WHERE id = :id");
            $this->db->bind(':id', $id);
            return $this->db->execute();
        } catch (Exception $e) {
            error_log('خطأ أثناء حذف كود ترويج: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * تغيير حالة كود ترويج (نشط/غير نشط)
     * @param int $id معرف كود الترويج
     * @param int $isActive الحالة الجديدة (1 = نشط، 0 = غير نشط)
     * @return bool نجاح أو فشل العملية
     */
    public function togglePromoCodeStatus($id, $isActive) {
        try {
            $this->db->prepare("UPDATE promo_codes SET is_active = :is_active WHERE id = :id");
            $this->db->bind(':is_active', $isActive);
            $this->db->bind(':id', $id);
            return $this->db->execute();
        } catch (Exception $e) {
            error_log('خطأ أثناء تغيير حالة كود ترويج: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * الحصول على كود ترويج بواسطة المعرف
     * @param int $id معرف كود الترويج
     * @return array|false بيانات كود الترويج أو false إذا لم يتم العثور عليه
     */
    public function getPromoCodeById($id) {
        try {
            $this->db->prepare("SELECT * FROM promo_codes WHERE id = :id");
            $this->db->bind(':id', $id);
            $promoCode = $this->db->fetch();

            if ($promoCode) {
                // إذا كان الكود مخصصًا لعملاء محددين، قم بجلب قائمة العملاء
                if ($promoCode['is_for_specific_customers'] == 1) {
                    $promoCode['customers'] = $this->getPromoCodeCustomers($id);
                }

                // التحقق من وجود علاقات مع العملاء في قاعدة البيانات
                $this->db->prepare("SELECT COUNT(*) as count FROM promo_code_customers WHERE promo_code_id = :id");
                $this->db->bind(':id', $id);
                $result = $this->db->fetch();

                // إذا كان الخيار غير مفعل ولكن توجد علاقات، قم بحذفها
                if ($promoCode['is_for_specific_customers'] == 0 && $result['count'] > 0) {
                    $this->deletePromoCodeCustomers($id);
                }
            }

            return $promoCode;
        } catch (Exception $e) {
            error_log('خطأ أثناء استرجاع كود ترويج: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * الحصول على كود ترويج بواسطة الرمز
     * @param string $code رمز كود الترويج
     * @return array|false بيانات كود الترويج أو false إذا لم يتم العثور عليه
     */
    public function getPromoCodeByCode($code) {
        try {
            $this->db->prepare("SELECT * FROM promo_codes WHERE code = :code");
            $this->db->bind(':code', $code);
            $promoCode = $this->db->fetch();

            if ($promoCode) {
                // إذا كان الكود مخصصًا لعملاء محددين، قم بجلب قائمة العملاء
                if ($promoCode['is_for_specific_customers'] == 1) {
                    $promoCode['customers'] = $this->getPromoCodeCustomers($promoCode['id']);
                }

                // التحقق من وجود علاقات مع العملاء في قاعدة البيانات
                $this->db->prepare("SELECT COUNT(*) as count FROM promo_code_customers WHERE promo_code_id = :id");
                $this->db->bind(':id', $promoCode['id']);
                $result = $this->db->fetch();

                // إذا كان الخيار غير مفعل ولكن توجد علاقات، قم بحذفها
                if ($promoCode['is_for_specific_customers'] == 0 && $result['count'] > 0) {
                    $this->deletePromoCodeCustomers($promoCode['id']);
                }
            }

            return $promoCode;
        } catch (Exception $e) {
            error_log('خطأ أثناء استرجاع كود ترويج: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * الحصول على قائمة أكواد الترويج
     * @param array $params معلمات البحث والفلترة
     * @return array قائمة أكواد الترويج
     */
    public function getPromoCodes($params = []) {
        try {
            $sql = "SELECT * FROM promo_codes WHERE 1=1";
            $bindings = [];

            // إضافة فلتر البحث
            if (isset($params['search']) && !empty($params['search'])) {
                $sql .= " AND (code LIKE :search OR name LIKE :search OR description LIKE :search)";
                $bindings[':search'] = '%' . $params['search'] . '%';
            }

            // فلتر الفرع
            if (isset($params['branch_id']) && !empty($params['branch_id'])) {
                $sql .= " AND (branch_id = :branch_id OR branch_id IS NULL)";
                $bindings[':branch_id'] = $params['branch_id'];
            }

            // فلتر الحالة
            if (isset($params['is_active'])) {
                $sql .= " AND is_active = :is_active";
                $bindings[':is_active'] = $params['is_active'];
            }

            // فلتر التاريخ
            if (isset($params['date'])) {
                $sql .= " AND (start_date IS NULL OR start_date <= :date) AND (end_date IS NULL OR end_date >= :date)";
                $bindings[':date'] = $params['date'];
            }

            // ترتيب النتائج
            $sql .= " ORDER BY created_at DESC";

            // إضافة حدود الصفحات
            if (isset($params['limit'])) {
                $sql .= " LIMIT :offset, :limit";
                $bindings[':limit'] = $params['limit'];
                $bindings[':offset'] = $params['offset'] ?? 0;
            }

            $this->db->prepare($sql);

            // ربط المعلمات
            foreach ($bindings as $param => $value) {
                $this->db->bind($param, $value);
            }

            return $this->db->fetchAll();
        } catch (Exception $e) {
            error_log('خطأ أثناء استرجاع أكواد الترويج: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * حساب عدد أكواد الترويج
     * @param array $params معلمات البحث والفلترة
     * @return int عدد أكواد الترويج
     */
    public function countPromoCodes($params = []) {
        try {
            $sql = "SELECT COUNT(*) as count FROM promo_codes WHERE 1=1";
            $bindings = [];

            // إضافة فلتر البحث
            if (isset($params['search']) && !empty($params['search'])) {
                $sql .= " AND (code LIKE :search OR name LIKE :search OR description LIKE :search)";
                $bindings[':search'] = '%' . $params['search'] . '%';
            }

            // فلتر الفرع
            if (isset($params['branch_id']) && !empty($params['branch_id'])) {
                $sql .= " AND (branch_id = :branch_id OR branch_id IS NULL)";
                $bindings[':branch_id'] = $params['branch_id'];
            }

            // فلتر الحالة
            if (isset($params['is_active'])) {
                $sql .= " AND is_active = :is_active";
                $bindings[':is_active'] = $params['is_active'];
            }

            // فلتر التاريخ
            if (isset($params['date'])) {
                $sql .= " AND (start_date IS NULL OR start_date <= :date) AND (end_date IS NULL OR end_date >= :date)";
                $bindings[':date'] = $params['date'];
            }

            $this->db->prepare($sql);

            // ربط المعلمات
            foreach ($bindings as $param => $value) {
                $this->db->bind($param, $value);
            }

            $result = $this->db->fetch();
            return $result['count'];
        } catch (Exception $e) {
            error_log('خطأ أثناء حساب عدد أكواد الترويج: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * إضافة علاقة بين كود ترويج وعميل
     * @param int $promoCodeId معرف كود الترويج
     * @param int $customerId معرف العميل
     * @return bool نجاح أو فشل العملية
     */
    public function addPromoCodeCustomer($promoCodeId, $customerId) {
        try {
            $this->db->prepare("INSERT IGNORE INTO promo_code_customers (promo_code_id, customer_id) VALUES (:promo_code_id, :customer_id)");
            $this->db->bind(':promo_code_id', $promoCodeId);
            $this->db->bind(':customer_id', $customerId);
            return $this->db->execute();
        } catch (Exception $e) {
            error_log('خطأ أثناء إضافة علاقة بين كود ترويج وعميل: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * حذف علاقات كود ترويج بالعملاء
     * @param int $promoCodeId معرف كود الترويج
     * @return bool نجاح أو فشل العملية
     */
    public function deletePromoCodeCustomers($promoCodeId) {
        try {
            $this->db->prepare("DELETE FROM promo_code_customers WHERE promo_code_id = :promo_code_id");
            $this->db->bind(':promo_code_id', $promoCodeId);
            return $this->db->execute();
        } catch (Exception $e) {
            error_log('خطأ أثناء حذف علاقات كود ترويج بالعملاء: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * الحصول على قائمة العملاء المرتبطين بكود ترويج
     * @param int $promoCodeId معرف كود الترويج
     * @return array قائمة العملاء
     */
    public function getPromoCodeCustomers($promoCodeId) {
        try {
            $this->db->prepare("
                SELECT c.*
                FROM customers c
                JOIN promo_code_customers pcc ON c.id = pcc.customer_id
                WHERE pcc.promo_code_id = :promo_code_id
            ");
            $this->db->bind(':promo_code_id', $promoCodeId);
            return $this->db->fetchAll();
        } catch (Exception $e) {
            error_log('خطأ أثناء استرجاع العملاء المرتبطين بكود ترويج: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * التحقق من صلاحية كود ترويج للاستخدام
     * @param string $code رمز كود الترويج
     * @param float $invoiceAmount قيمة الفاتورة
     * @param int $customerId معرف العميل (اختياري)
     * @return array|false بيانات كود الترويج إذا كان صالحًا، أو false إذا كان غير صالح
     */
    public function validatePromoCode($code, $invoiceAmount, $customerId = null) {
        try {
            // الحصول على بيانات كود الترويج
            $promoCode = $this->getPromoCodeByCode($code);

            // التحقق من وجود الكود
            if (!$promoCode) {
                throw new Exception('كود الترويج غير موجود');
            }

            // التحقق من حالة الكود
            if (!$promoCode['is_active']) {
                throw new Exception('كود الترويج غير نشط');
            }

            // التحقق من تاريخ الصلاحية
            $today = date('Y-m-d');
            if ($promoCode['start_date'] && $promoCode['start_date'] > $today) {
                throw new Exception('كود الترويج غير صالح بعد');
            }
            if ($promoCode['end_date'] && $promoCode['end_date'] < $today) {
                throw new Exception('كود الترويج منتهي الصلاحية');
            }

            // التحقق من عدد مرات الاستخدام
            if ($promoCode['max_uses'] && $promoCode['current_uses'] >= $promoCode['max_uses']) {
                throw new Exception('تم استخدام كود الترويج الحد الأقصى من المرات');
            }

            // التحقق من استخدام العميل للكود من قبل إذا كان مسموحاً باستخدامه مرة واحدة لكل عميل
            if ($promoCode['one_use_per_customer'] && $customerId) {
                $this->db->prepare("
                    SELECT 1 FROM promo_code_usage
                    WHERE promo_code_id = :promo_code_id AND customer_id = :customer_id
                    LIMIT 1
                ");
                $this->db->bind(':promo_code_id', $promoCode['id']);
                $this->db->bind(':customer_id', $customerId);
                if ($this->db->fetch()) {
                    throw new Exception('لقد قمت باستخدام هذا الكود من قبل');
                }
            }

            // التحقق من قيمة الفاتورة
            if ($promoCode['min_invoice_amount'] && $invoiceAmount < $promoCode['min_invoice_amount']) {
                throw new Exception('قيمة الفاتورة أقل من الحد الأدنى المطلوب لاستخدام كود الترويج');
            }
            if ($promoCode['max_invoice_amount'] && $invoiceAmount > $promoCode['max_invoice_amount']) {
                throw new Exception('قيمة الفاتورة أكبر من الحد الأقصى المسموح لاستخدام كود الترويج');
            }

            // التحقق من العميل إذا كان الكود مخصصًا لعملاء محددين
            if ($promoCode['is_for_specific_customers'] == 1) {
                if (!$customerId) {
                    throw new Exception('يجب تحديد العميل لاستخدام هذا الكود');
                }

                // التحقق من وجود العميل في قائمة العملاء المسموح لهم باستخدام الكود
                $this->db->prepare("
                    SELECT 1 FROM promo_code_customers
                    WHERE promo_code_id = :promo_code_id AND customer_id = :customer_id
                ");
                $this->db->bind(':promo_code_id', $promoCode['id']);
                $this->db->bind(':customer_id', $customerId);
                if (!$this->db->fetch()) {
                    throw new Exception('هذا الكود غير مخصص لهذا العميل');
                }
            } else if ($promoCode['one_use_per_customer'] == 1 && !$customerId) {
                // إذا كان الكود يستخدم مرة واحدة لكل عميل، فيجب تحديد العميل
                throw new Exception('يجب تحديد العميل لاستخدام هذا الكود');
            }

            // التحقق من نقاط الولاء إذا كان الكود يتطلب نقاطًا
            if ($promoCode['required_loyalty_points'] && $customerId) {
                $this->db->prepare("SELECT loyalty_points FROM customers WHERE id = :id");
                $this->db->bind(':id', $customerId);
                $customer = $this->db->fetch();

                if (!$customer || $customer['loyalty_points'] < $promoCode['required_loyalty_points']) {
                    throw new Exception('لا توجد نقاط ولاء كافية لاستخدام هذا الكود');
                }
            }

            // حساب قيمة الخصم
            $discountAmount = 0;
            if ($promoCode['discount_type'] === 'percentage') {
                $discountAmount = $invoiceAmount * ($promoCode['discount_value'] / 100);
            } else {
                $discountAmount = $promoCode['discount_value'];
                // التأكد من أن قيمة الخصم لا تتجاوز قيمة الفاتورة
                if ($discountAmount > $invoiceAmount) {
                    $discountAmount = $invoiceAmount;
                }
            }

            // إضافة قيمة الخصم إلى بيانات كود الترويج
            $promoCode['calculated_discount'] = $discountAmount;

            return $promoCode;
        } catch (Exception $e) {
            error_log('خطأ أثناء التحقق من صلاحية كود ترويج: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * تسجيل استخدام كود ترويج
     * @param int $promoCodeId معرف كود الترويج
     * @param int $invoiceId معرف الفاتورة
     * @param int $customerId معرف العميل (اختياري)
     * @param float $discountAmount قيمة الخصم المطبق
     * @return bool نجاح أو فشل العملية
     */
    public function recordPromoCodeUsage($promoCodeId, $invoiceId, $customerId, $discountAmount) {
        try {
            // التحقق مما إذا كانت هناك معاملة نشطة بالفعل
            $transactionStartedHere = false;
            if (!$this->db->inTransaction()) {
                $this->db->beginTransaction();
                $transactionStartedHere = true;
                error_log('Started new transaction in recordPromoCodeUsage');
            } else {
                error_log('Using existing transaction in recordPromoCodeUsage');
            }

            // تسجيل استخدام الكود
            $this->db->prepare("
                INSERT INTO promo_code_usage (
                    promo_code_id, customer_id, invoice_id, discount_amount
                ) VALUES (
                    :promo_code_id, :customer_id, :invoice_id, :discount_amount
                )
            ");
            $this->db->bind(':promo_code_id', $promoCodeId);
            $this->db->bind(':customer_id', $customerId);
            $this->db->bind(':invoice_id', $invoiceId);
            $this->db->bind(':discount_amount', $discountAmount);
            $this->db->execute();

            // زيادة عداد الاستخدام
            $this->db->prepare("
                UPDATE promo_codes
                SET current_uses = current_uses + 1
                WHERE id = :id
            ");
            $this->db->bind(':id', $promoCodeId);
            $this->db->execute();

            // خصم نقاط الولاء إذا كان الكود يتطلب نقاطًا
            $promoCode = $this->getPromoCodeById($promoCodeId);
            if ($promoCode && $promoCode['required_loyalty_points'] && $customerId) {
                $this->db->prepare("
                    UPDATE customers
                    SET loyalty_points = loyalty_points - :points
                    WHERE id = :id
                ");
                $this->db->bind(':points', $promoCode['required_loyalty_points']);
                $this->db->bind(':id', $customerId);
                $this->db->execute();
            }

            // إنهاء المعاملة فقط إذا بدأناها هنا
            if ($transactionStartedHere) {
                $this->db->commit();
                error_log('Committed transaction in recordPromoCodeUsage');
            }

            return true;
        } catch (Exception $e) {
            // التراجع عن المعاملة فقط إذا بدأناها هنا
            if ($transactionStartedHere && $this->db->inTransaction()) {
                $this->db->rollBack();
                error_log('Rolled back transaction in recordPromoCodeUsage');
            }
            error_log('خطأ أثناء تسجيل استخدام كود ترويج: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * الحصول على سجل استخدام كود ترويج
     * @param int $promoCodeId معرف كود الترويج
     * @return array سجل الاستخدام
     */
    public function getPromoCodeUsageHistory($promoCodeId) {
        try {
            $this->db->prepare("
                SELECT pcu.*, c.name as customer_name, i.invoice_number
                FROM promo_code_usage pcu
                LEFT JOIN customers c ON pcu.customer_id = c.id
                JOIN invoices i ON pcu.invoice_id = i.id
                WHERE pcu.promo_code_id = :promo_code_id
                ORDER BY pcu.created_at DESC
            ");
            $this->db->bind(':promo_code_id', $promoCodeId);
            return $this->db->fetchAll();
        } catch (Exception $e) {
            error_log('خطأ أثناء استرجاع سجل استخدام كود ترويج: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * إنشاء كود ترويج عشوائي
     * @param int $length طول الكود (افتراضيًا 8 أحرف)
     * @return string كود ترويج عشوائي
     */
    public function generateRandomCode($length = 8) {
        $characters = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ';
        $code = '';
        $max = strlen($characters) - 1;

        for ($i = 0; $i < $length; $i++) {
            $code .= $characters[random_int(0, $max)];
        }

        // التحقق من عدم وجود كود مماثل
        $this->db->prepare("SELECT id FROM promo_codes WHERE code = :code");
        $this->db->bind(':code', $code);
        if ($this->db->fetch()) {
            // إذا كان الكود موجودًا بالفعل، قم بإنشاء كود جديد
            return $this->generateRandomCode($length);
        }

        return $code;
    }
}
