<?php
/**
 * صفحة تسجيل الخروج
 * تقوم بإنهاء جلسة المستخدم وإعادة توجيهه إلى صفحة تسجيل الدخول
 */

// تعريف ثابت لمنع الوصول المباشر للملفات المرتبطة
define('BASEPATH', true);

// استدعاء ملفات التهيئة
require_once '../../config/init.php';

// التحقق من وجود جلسة نشطة
if (isLoggedIn()) {
    // تسجيل وقت تسجيل الخروج إذا كان هناك معرف مستخدم في الجلسة
    if (isset($_SESSION['user_id'])) {
        $userId = $_SESSION['user_id'];
        
        // تحديث وقت آخر تسجيل خروج في قاعدة البيانات (اختياري)
        try {
            $userModel = new User($db);
            $userModel->updateLogoutTime($userId);
        } catch (Exception $e) {
            // تجاهل الأخطاء هنا، فقط مواصلة تسجيل الخروج
            error_log('خطأ في تحديث وقت تسجيل الخروج: ' . $e->getMessage());
        }
    }
    
    // إنهاء الجلسة
    session_unset();     // إزالة كل متغيرات الجلسة
    session_destroy();   // تدمير الجلسة
    
    // إنشاء جلسة جديدة لرسالة النجاح
    session_start();
    $_SESSION['success_message'] = 'تم تسجيل الخروج بنجاح';
}

// إعادة التوجيه إلى صفحة تسجيل الدخول
header('Location: ../../index.php');
exit;