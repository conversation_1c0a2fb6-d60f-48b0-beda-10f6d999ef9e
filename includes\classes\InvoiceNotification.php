<?php
/**
 * كلاس إشعارات الفواتير
 * يتعامل مع إرسال إشعارات الفواتير للعملاء
 */

class InvoiceNotification {
    private $db;
    private $settings;
    private $whatsappService;
    private $logFile;

    /**
     * إنشاء كائن جديد من إشعارات الفواتير
     *
     * @param Database $db كائن قاعدة البيانات
     */
    public function __construct($db) {
        $this->db = $db;

        // إنشاء كائن الإعدادات
        $this->settings = new Settings($db);

        // إنشاء كائن خدمة WhatsApp
        $this->whatsappService = new WhatsAppAutomation($db);

        // تحديد ملف السجل
        $this->logFile = __DIR__ . '/../../logs/invoice_notifications.log';

        // التأكد من وجود مجلد السجلات
        if (!is_dir(dirname($this->logFile))) {
            mkdir(dirname($this->logFile), 0755, true);
        }
    }

    /**
     * إرسال إشعار فاتورة للعميل
     *
     * @param int $invoiceId معرف الفاتورة
     * @return bool نجاح أو فشل الإرسال
     */
    public function sendInvoiceNotification($invoiceId) {
        try {
            // التحقق من تفعيل إشعارات الفواتير
            $notificationSettings = $this->getNotificationSettings();

            // تسجيل الإعدادات للتصحيح
            $this->log("إعدادات الإشعارات: " . print_r($notificationSettings, true));

            // التحقق من تفعيل إشعارات الفواتير (مع مراعاة مشكلة الأحرف الأولى المفقودة)
            if ((!isset($notificationSettings['invoice_notification']) || !$notificationSettings['invoice_notification']) &&
                (!isset($notificationSettings['nvoice_notification']) || !$notificationSettings['nvoice_notification'])) {
                $this->log("إشعارات الفواتير غير مفعلة. لم يتم إرسال إشعار للفاتورة رقم $invoiceId");
                return false;
            }

            // الحصول على بيانات الفاتورة
            $invoice = $this->getInvoiceData($invoiceId);
            if (!$invoice) {
                $this->log("لم يتم العثور على الفاتورة رقم $invoiceId");
                return false;
            }

            // التحقق من وجود رقم هاتف للعميل
            if (empty($invoice['customer_phone'])) {
                $this->log("لا يوجد رقم هاتف للعميل في الفاتورة رقم $invoiceId");
                return false; // لا يمكن إرسال إشعار بدون رقم هاتف للعميل
            }

            // إعداد رسالة الإشعار
            $message = $this->prepareNotificationMessage($invoice, $notificationSettings);

            // تسجيل محاولة الإرسال
            $this->log("جاري محاولة إرسال إشعار الفاتورة رقم $invoiceId إلى العميل " . $invoice['customer_name'] . " على الرقم " . $invoice['customer_phone']);

            // التحقق من تفعيل خدمة WhatsApp
            $allSettings = $this->settings->getAllSettings();
            $whatsappEnabled = isset($allSettings['whatsapp_enabled']) ? (bool)$allSettings['whatsapp_enabled'] : false;

            if (!$whatsappEnabled) {
                // تفعيل خدمة WhatsApp مؤقتًا لإرسال الإشعار
                $this->log("تفعيل خدمة WhatsApp مؤقتًا لإرسال إشعار الفاتورة");
                $this->settings->updateSetting('whatsapp_enabled', 1);
            }

            // التحقق من تشغيل الخادم المحلي في حالة وضع جانب العميل
            $whatsappClientSide = isset($allSettings['whatsapp_client_side']) ? (bool)$allSettings['whatsapp_client_side'] : false;

            if ($whatsappClientSide) {
                // التحقق من الاتصال بالخادم المحلي
                $localServerUrl = 'http://localhost:3000';
                $ch = curl_init($localServerUrl);
                curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 2); // مهلة الاتصال ثانيتين فقط
                curl_setopt($ch, CURLOPT_TIMEOUT, 2); // مهلة الطلب ثانيتين فقط
                $response = curl_exec($ch);
                $error = curl_error($ch);
                $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
                curl_close($ch);

                if ($httpCode !== 200) {
                    $this->log("الخادم المحلي غير متاح. يرجى التأكد من تشغيل الخادم المحلي على جهازك. خطأ: $error");

                    // تسجيل الإشعار في قاعدة البيانات كفاشل
                    $this->saveNotificationRecord($invoice, false);

                    // إذا كانت خدمة WhatsApp غير مفعلة مسبقًا، نعيدها إلى حالتها السابقة
                    if (!$whatsappEnabled) {
                        $this->settings->updateSetting('whatsapp_enabled', 0);
                    }

                    return false;
                }
            }

            // إرسال الرسالة عبر WhatsApp
            $result = $this->whatsappService->sendMessage($invoice['customer_phone'], $message);

            // تسجيل الإشعار في قاعدة البيانات
            $this->saveNotificationRecord($invoice, $result);

            // تسجيل نتيجة الإرسال
            if ($result) {
                $this->log("تم إرسال إشعار الفاتورة رقم $invoiceId بنجاح إلى العميل " . $invoice['customer_name']);
            } else {
                $this->log("فشل إرسال إشعار الفاتورة رقم $invoiceId إلى العميل " . $invoice['customer_name']);

                // محاولة أخرى بعد تأخير قصير
                $this->log("جاري محاولة إرسال مرة أخرى بعد تأخير قصير");
                sleep(1); // تأخير لمدة ثانية واحدة
                $result = $this->whatsappService->sendMessage($invoice['customer_phone'], $message);

                if ($result) {
                    $this->log("تم إرسال إشعار الفاتورة رقم $invoiceId بنجاح في المحاولة الثانية إلى العميل " . $invoice['customer_name']);
                } else {
                    $this->log("فشل إرسال إشعار الفاتورة رقم $invoiceId في المحاولة الثانية إلى العميل " . $invoice['customer_name']);
                }
            }

            // إذا كانت خدمة WhatsApp غير مفعلة مسبقًا، نعيدها إلى حالتها السابقة
            if (!$whatsappEnabled) {
                $this->settings->updateSetting('whatsapp_enabled', 0);
            }

            return $result;
        } catch (Exception $e) {
            $this->log("خطأ في إرسال إشعار الفاتورة رقم $invoiceId: " . $e->getMessage());
            return false;
        }
    }

    /**
     * الحصول على بيانات الفاتورة
     *
     * @param int $invoiceId معرف الفاتورة
     * @return array|false بيانات الفاتورة أو false في حالة الفشل
     */
    private function getInvoiceData($invoiceId) {
        try {
            // استعلام للحصول على بيانات الفاتورة والعميل
            $this->db->prepare("
                SELECT i.*, c.name as customer_name, c.phone as customer_phone, b.name as branch_name
                FROM invoices i
                LEFT JOIN customers c ON i.customer_id = c.id
                LEFT JOIN branches b ON i.branch_id = b.id
                WHERE i.id = :invoice_id
            ");
            $this->db->bind(':invoice_id', $invoiceId);
            $this->db->execute();
            $invoice = $this->db->fetch();

            if (!$invoice) {
                return false;
            }

            // الحصول على عناصر الفاتورة
            $this->db->prepare("
                SELECT ii.*,
                    CASE
                        WHEN ii.item_type = 'service' THEN s.name
                        WHEN ii.item_type = 'product' THEN p.name
                        ELSE 'غير معروف'
                    END as item_name
                FROM invoice_items ii
                LEFT JOIN services s ON ii.item_type = 'service' AND ii.item_id = s.id
                LEFT JOIN products p ON ii.item_type = 'product' AND ii.item_id = p.id
                WHERE ii.invoice_id = :invoice_id
            ");
            $this->db->bind(':invoice_id', $invoiceId);
            $this->db->execute();
            $invoice['items'] = $this->db->fetchAll();

            return $invoice;
        } catch (Exception $e) {
            $this->log("خطأ في الحصول على بيانات الفاتورة: " . $e->getMessage());
            return false;
        }
    }

    /**
     * إعداد رسالة الإشعار
     *
     * @param array $invoice بيانات الفاتورة
     * @param array $settings إعدادات الإشعارات
     * @return string نص الرسالة
     */
    private function prepareNotificationMessage($invoice, $settings) {
        // الحصول على قالب الرسالة (مع مراعاة مشكلة الأحرف الأولى المفقودة)
        $template = $settings['invoice_message_template'] ?? $settings['nvoice_message_template'] ?? "شكراً لزيارتكم {company_name}\n\nتفاصيل الفاتورة:\nرقم الفاتورة: {invoice_number}\nالتاريخ: {invoice_date}\nالمبلغ الإجمالي: {invoice_total}\n\nنتطلع لرؤيتكم مرة أخرى!";

        // الحصول على إعدادات الفاتورة (مع مراعاة مشكلة الأحرف الأولى المفقودة)
        $invoiceSettings = $this->settings->getSettingsByPrefix('invoice_');
        $companyNameSetting = $invoiceSettings['company_name'] ?? $invoiceSettings['ompany_name'] ?? null;
        $companyName = $companyNameSetting ?? $invoice['branch_name'] ?? 'صالون البدرواي';

        // تنسيق التاريخ
        $invoiceDate = date('Y-m-d', strtotime($invoice['created_at']));

        // الحصول على رمز العملة (مع مراعاة مشكلة الأحرف الأولى المفقودة)
        $currencySymbol = $invoiceSettings['currency_symbol'] ?? $invoiceSettings['urrency_symbol'] ?? 'ج';

        // استبدال المتغيرات في القالب
        $message = str_replace(
            [
                '{customer_name}',
                '{company_name}',
                '{invoice_number}',
                '{invoice_date}',
                '{invoice_total}'
            ],
            [
                $invoice['customer_name'] ?? 'العميل',
                $companyName,
                $invoice['invoice_number'],
                $invoiceDate,
                number_format($invoice['final_amount'], 2) . ' ' . $currencySymbol
            ],
            $template
        );

        // إضافة تفاصيل الفاتورة إذا كان الخيار مفعل (مع مراعاة مشكلة الأحرف الأولى المفقودة)
        $includeDetails = (isset($settings['include_invoice_details']) && $settings['include_invoice_details']) ||
                         (isset($settings['nclude_invoice_details']) && $settings['nclude_invoice_details']);

        if ($includeDetails && !empty($invoice['items'])) {
            $itemsText = "\n\nتفاصيل المنتجات والخدمات:\n";
            foreach ($invoice['items'] as $item) {
                $itemsText .= "- " . $item['item_name'] . " × " . $item['quantity'] . ": " . number_format($item['total'], 2) . " " . $currencySymbol . "\n";
            }
            $message = str_replace('{invoice_items}', $itemsText, $message);
        } else {
            $message = str_replace('{invoice_items}', '', $message);
        }

        return $message;
    }

    /**
     * تسجيل الإشعار في قاعدة البيانات
     *
     * @param array $invoice بيانات الفاتورة
     * @param bool $success نجاح أو فشل الإرسال
     * @return void
     */
    private function saveNotificationRecord($invoice, $success) {
        try {
            // التحقق من وجود جدول الإشعارات
            $this->db->prepare("SHOW TABLES LIKE 'notifications'");
            $this->db->execute();
            if (!$this->db->rowCount()) {
                $this->log("جدول الإشعارات غير موجود. لم يتم تسجيل الإشعار.");
                return;
            }

            // إنشاء سجل إشعار جديد
            $this->db->prepare("
                INSERT INTO notifications (
                    type, recipient_id, recipient_type, title, message,
                    related_id, related_type, branch_id, is_read, is_sent,
                    send_email, send_sms, sent_at, created_at
                ) VALUES (
                    'invoice_notification', :recipient_id, 'customer', :title, :message,
                    :related_id, 'invoice', :branch_id, 0, :is_sent,
                    0, 0, :sent_at, :created_at
                )
            ");

            $this->db->bind(':recipient_id', $invoice['customer_id'] ?? null);
            $this->db->bind(':title', 'إشعار فاتورة جديدة');
            $this->db->bind(':message', "تم إرسال إشعار للعميل " . ($invoice['customer_name'] ?? 'غير معروف') . " بخصوص الفاتورة رقم " . $invoice['invoice_number']);
            $this->db->bind(':related_id', $invoice['id']);
            $this->db->bind(':branch_id', $invoice['branch_id'] ?? null);
            $this->db->bind(':is_sent', $success ? 1 : 0);
            $this->db->bind(':sent_at', $success ? date('Y-m-d H:i:s') : null);
            $this->db->bind(':created_at', date('Y-m-d H:i:s'));

            $this->db->execute();
        } catch (Exception $e) {
            $this->log("خطأ في تسجيل الإشعار: " . $e->getMessage());
        }
    }

    /**
     * الحصول على إعدادات الإشعارات
     *
     * @return array إعدادات الإشعارات
     */
    private function getNotificationSettings() {
        return $this->settings->getSettingsByPrefix('notification_');
    }

    /**
     * تسجيل رسالة في ملف السجل
     *
     * @param string $message الرسالة المراد تسجيلها
     * @return void
     */
    private function log($message) {
        $timestamp = date('Y-m-d H:i:s');
        $logMessage = "[$timestamp] $message" . PHP_EOL;
        file_put_contents($this->logFile, $logMessage, FILE_APPEND);
    }
}
