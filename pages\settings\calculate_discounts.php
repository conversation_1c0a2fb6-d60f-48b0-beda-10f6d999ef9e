<?php
// تعريف ثابت لمنع الوصول المباشر للملفات المرتبطة
define('BASEPATH', true);

// استدعاء ملفات التهيئة
require_once '../../config/init.php';

// التحقق من تسجيل الدخول
requireLogin();

// التحقق من وجود صلاحية إدارة الإعدادات
requirePermission('settings_manage');

// عنوان الصفحة
$pageTitle = "حساب الخصومات للأيام السابقة";

// تضمين ملف الهيدر
include_once __DIR__ . '/../../includes/templates/header.php';

// استدعاء كلاس قاعدة البيانات
$db = new Database();

// معالجة النموذج عند الإرسال
$message = '';
$messageType = '';
$processedDays = 0;
$totalDiscounts = 0;

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['calculate_discounts'])) {
    try {
        // الحصول على جميع أيام العمل المغلقة
        $db->prepare("SELECT id, date FROM end_days WHERE closed_at IS NOT NULL ORDER BY date ASC");
        $endDays = $db->fetchAll();

        foreach ($endDays as $endDay) {
            $endDayId = $endDay['id'];

            // حساب إجمالي الخصومات للفواتير المرتبطة بهذا اليوم
            $db->prepare("SELECT SUM(discount_amount) as total_discounts
                          FROM invoices
                          WHERE end_day_id = :end_day_id
                          AND payment_status = 'paid'");
            $db->bind(':end_day_id', $endDayId);
            $discountResult = $db->fetch();

            $dayDiscounts = $discountResult && $discountResult['total_discounts'] ? $discountResult['total_discounts'] : 0;
            $totalDiscounts += $dayDiscounts;

            // تحديث قيمة الخصومات في جدول end_days
            $db->prepare("UPDATE end_days SET total_discounts = :total_discounts WHERE id = :id");
            $db->bind(':total_discounts', $dayDiscounts);
            $db->bind(':id', $endDayId);
            $db->execute();

            $processedDays++;
        }

        $message = "تم حساب الخصومات بنجاح لعدد $processedDays يوم عمل بإجمالي خصومات: $totalDiscounts " . $currencySymbol;
        $messageType = 'success';
    } catch (Exception $e) {
        $message = "حدث خطأ أثناء حساب الخصومات: " . $e->getMessage();
        $messageType = 'danger';
    }
}
?>

<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-calculator me-2"></i>
                        <?php echo $pageTitle; ?>
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (!empty($message)): ?>
                        <div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show" role="alert">
                            <?php echo $message; ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    <?php endif; ?>

                    <div class="row">
                        <div class="col-md-8 offset-md-2">
                            <div class="card shadow-sm">
                                <div class="card-body">
                                    <h5 class="card-title mb-4">حساب الخصومات للأيام السابقة</h5>

                                    <div class="alert alert-info">
                                        <i class="fas fa-info-circle me-2"></i>
                                        ستقوم هذه العملية بحساب إجمالي قيمة الخصومات لجميع أيام العمل السابقة وتحديث قيمة الخصومات في جدول نهاية اليوم.
                                    </div>

                                    <form method="post" action="">
                                        <div class="d-grid gap-2 mt-4">
                                            <button type="submit" name="calculate_discounts" class="btn btn-primary">
                                                <i class="fas fa-calculator me-2"></i>
                                                حساب الخصومات
                                            </button>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include_once __DIR__ . '/../../includes/templates/footer.php'; ?>
