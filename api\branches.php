<?php
/**
 * API للتعامل مع الفروع
 */

// تعريف ثابت لمنع الوصول المباشر للملفات المرتبطة
define('BASEPATH', true);

// استدعاء ملفات التهيئة
require_once '../config/init.php';

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    outputResponse([
        'success' => false,
        'message' => 'يجب تسجيل الدخول أولاً'
    ], 401);
    exit;
}

// التحقق من الصلاحيات
if (!hasPermission('admin')) {
    outputResponse([
        'success' => false,
        'message' => 'ليس لديك صلاحية الوصول لهذه الواجهة'
    ], 403);
    exit;
}

// تحديد الإجراء المطلوب
$action = isset($_POST['action']) ? $_POST['action'] : (isset($_GET['action']) ? $_GET['action'] : '');

// إنشاء كائن الفروع
$branchModel = new Branch($db);

// معالجة الطلبات حسب الإجراء
try {
    switch ($action) {
        // استرجاع قائمة الفروع
        case 'list':
            // معلمات الفلترة والترتيب
            $filters = [
                'search' => isset($_POST['search']) ? $_POST['search'] : null,
                'is_active' => isset($_POST['is_active']) ? $_POST['is_active'] : null,
                'sort_by' => isset($_POST['sort_by']) ? $_POST['sort_by'] : 'id',
                'sort_dir' => isset($_POST['sort_dir']) ? $_POST['sort_dir'] : 'DESC',
                'limit' => isset($_POST['limit']) ? intval($_POST['limit']) : 100,
                'offset' => isset($_POST['offset']) ? intval($_POST['offset']) : 0
            ];
            
            // استرجاع الفروع
            $branches = $branchModel->getBranches($filters);
            $totalCount = $branchModel->getBranchesCount($filters);
            
            outputResponse([
                'success' => true,
                'data' => $branches,
                'total_count' => $totalCount
            ]);
            break;
        
        // استرجاع تفاصيل فرع محدد
        case 'get':
            // التحقق من وجود معرف الفرع
            if (!isset($_POST['id']) && !isset($_GET['id'])) {
                throw new Exception('معرف الفرع مطلوب');
            }
            
            $branchId = isset($_POST['id']) ? intval($_POST['id']) : intval($_GET['id']);
            $withDetails = isset($_POST['with_details']) ? (bool)$_POST['with_details'] : (isset($_GET['with_details']) ? (bool)$_GET['with_details'] : false);
            
            // استرجاع بيانات الفرع
            $branch = $branchModel->getBranchById($branchId);
            
            if (!$branch) {
                throw new Exception('الفرع غير موجود');
            }
            
            // استرجاع التفاصيل الإضافية إذا طُلبت
            if ($withDetails) {
                // استرجاع الموظفين
                $employeeModel = new Employee($db);
                $branch['employees'] = $employeeModel->getEmployees(['branch_id' => $branchId, 'limit' => 10]);
                
                // استرجاع العملاء
                $customerModel = new Customer($db);
                $branch['customers'] = $customerModel->getCustomers(['branch_id' => $branchId, 'limit' => 10]);
                
                // استرجاع المخزون
                $inventoryModel = new Inventory($db);
                $branch['inventory'] = $inventoryModel->getInventory(['branch_id' => $branchId, 'limit' => 10]);
            }
            
            outputResponse([
                'success' => true,
                'data' => $branch
            ]);
            break;
        
        // إضافة فرع جديد
        case 'add':
            // التحقق من وجود اسم الفرع
            if (empty($_POST['name'])) {
                throw new Exception('اسم الفرع مطلوب');
            }
            
            // تحضير بيانات الفرع
            $branchData = [
                'name' => sanitizeInput($_POST['name']),
                'address' => isset($_POST['address']) ? sanitizeInput($_POST['address']) : null,
                'phone' => isset($_POST['phone']) ? sanitizeInput($_POST['phone']) : null,
                'manager_id' => !empty($_POST['manager_id']) ? intval($_POST['manager_id']) : null,
                'is_active' => isset($_POST['is_active']) ? intval($_POST['is_active']) : 1
            ];
            
            // إضافة الفرع
            $branchId = $branchModel->addBranch($branchData);
            
            outputResponse([
                'success' => true,
                'message' => 'تم إضافة الفرع بنجاح',
                'branch_id' => $branchId
            ]);
            break;
        
        // تحديث بيانات فرع
        case 'update':
            // التحقق من وجود معرف الفرع واسمه
            if (empty($_POST['branch_id']) || empty($_POST['name'])) {
                throw new Exception('معرف الفرع واسمه مطلوبان');
            }
            
            $branchId = intval($_POST['branch_id']);
            
            // تحضير بيانات الفرع
            $branchData = [
                'name' => sanitizeInput($_POST['name']),
                'address' => isset($_POST['address']) ? sanitizeInput($_POST['address']) : null,
                'phone' => isset($_POST['phone']) ? sanitizeInput($_POST['phone']) : null,
                'manager_id' => !empty($_POST['manager_id']) ? intval($_POST['manager_id']) : null,
                'is_active' => isset($_POST['is_active']) ? intval($_POST['is_active']) : 1
            ];
            
            // تحديث الفرع
            $branchModel->updateBranch($branchId, $branchData);
            
            outputResponse([
                'success' => true,
                'message' => 'تم تحديث بيانات الفرع بنجاح'
            ]);
            break;
        
        // حذف فرع
        case 'delete':
            // التحقق من وجود معرف الفرع
            if (empty($_POST['id'])) {
                throw new Exception('معرف الفرع مطلوب');
            }
            
            $branchId = intval($_POST['id']);
            
            // التحقق من عدم وجود موظفين أو عملاء مرتبطين بالفرع
            $employeeModel = new Employee($db);
            $employeesCount = $employeeModel->getEmployeesCount(['branch_id' => $branchId]);
            
            if ($employeesCount > 0) {
                throw new Exception('لا يمكن حذف الفرع لأنه يحتوي على موظفين');
            }
            
            $customerModel = new Customer($db);
            $customersCount = $customerModel->getCustomersCount(['branch_id' => $branchId]);
            
            if ($customersCount > 0) {
                throw new Exception('لا يمكن حذف الفرع لأنه يحتوي على عملاء');
            }
            
            // حذف الفرع
            $branchModel->deleteBranch($branchId);
            
            outputResponse([
                'success' => true,
                'message' => 'تم حذف الفرع بنجاح'
            ]);
            break;
        
        // استرجاع تقرير أداء الفرع
        case 'report':
            // التحقق من وجود معرف الفرع
            if (empty($_POST['id']) && empty($_GET['id'])) {
                throw new Exception('معرف الفرع مطلوب');
            }
            
            $branchId = isset($_POST['id']) ? intval($_POST['id']) : intval($_GET['id']);
            $period = isset($_POST['period']) ? $_POST['period'] : (isset($_GET['period']) ? $_GET['period'] : 'month');
            
            // تجهيز التواريخ
            $startDate = null;
            $endDate = date('Y-m-d');
            
            switch ($period) {
                case 'week':
                    $startDate = date('Y-m-d', strtotime('-1 week'));
                    break;
                case 'month':
                    $startDate = date('Y-m-d', strtotime('-1 month'));
                    break;
                case 'quarter':
                    $startDate = date('Y-m-d', strtotime('-3 months'));
                    break;
                case 'year':
                    $startDate = date('Y-m-d', strtotime('-1 year'));
                    break;
                default:
                    $startDate = date('Y-m-d', strtotime('-1 month'));
            }
            
            // إنشاء تقرير الفرع
            $report = $branchModel->generateBranchReport($branchId, $startDate, $endDate);
            
            outputResponse([
                'success' => true,
                'data' => $report
            ]);
            break;
        
        // الإجراء الافتراضي إذا لم يتم تحديد إجراء صحيح
        default:
            throw new Exception('الإجراء غير معروف');
    }
} catch (Exception $e) {
    // معالجة الأخطاء
    outputResponse([
        'success' => false,
        'message' => $e->getMessage()
    ], 400);
}

/**
 * إرسال استجابة JSON
 * 
 * @param array $data البيانات المراد إرسالها
 * @param int $statusCode كود الحالة HTTP
 */
function outputResponse($data, $statusCode = 200) {
    http_response_code($statusCode);
    header('Content-Type: application/json; charset=UTF-8');
    echo json_encode($data, JSON_UNESCAPED_UNICODE);
    exit;
}