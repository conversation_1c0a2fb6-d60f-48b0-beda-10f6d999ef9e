<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار النسخة التجريبية</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.0.2/dist/css/bootstrap.rtl.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap">
    
    <style>
        body {
            font-family: 'Tajawal', sans-serif;
            background-color: #f8f9fa;
            padding: 20px;
        }
        
        /* أنماط النسخة التجريبية */
        .demo-warning-bar {
            background: linear-gradient(45deg, #ff6b6b, #feca57);
            color: white;
            text-align: center;
            padding: 10px;
            font-weight: bold;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 10000;
            box-shadow: 0 2px 10px rgba(0,0,0,0.2);
            animation: slideDown 0.5s ease-out;
        }

        .demo-watermark {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%) rotate(-45deg);
            font-size: 120px;
            color: rgba(0,0,0,0.05);
            font-weight: bold;
            z-index: -1;
            pointer-events: none;
            user-select: none;
        }

        .demo-badge {
            position: fixed;
            top: 20px;
            right: 20px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            padding: 8px 15px;
            border-radius: 20px;
            font-weight: bold;
            font-size: 12px;
            z-index: 9999;
            box-shadow: 0 2px 10px rgba(0,0,0,0.2);
            animation: pulse 2s infinite;
        }

        @keyframes slideDown {
            from { transform: translateY(-100%); }
            to { transform: translateY(0); }
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        body.demo-mode {
            padding-top: 70px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">اختبار النسخة التجريبية</h5>
                    </div>
                    <div class="card-body">
                        <p>هذه صفحة اختبار للتأكد من عمل نظام النسخة التجريبية.</p>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <h6>أزرار للاختبار:</h6>
                                <button type="button" class="btn btn-primary me-2 mb-2">حفظ</button>
                                <button type="button" class="btn btn-success me-2 mb-2">إضافة</button>
                                <button type="button" class="btn btn-warning me-2 mb-2">تعديل</button>
                                <button type="button" class="btn btn-danger me-2 mb-2">حذف</button>
                                <button type="submit" class="btn btn-info me-2 mb-2">إرسال</button>
                            </div>
                            <div class="col-md-6">
                                <h6>نموذج للاختبار:</h6>
                                <form>
                                    <div class="mb-3">
                                        <label for="testInput" class="form-label">حقل اختبار</label>
                                        <input type="text" class="form-control" id="testInput" placeholder="أدخل نص">
                                    </div>
                                    <button type="submit" class="btn btn-primary">حفظ البيانات</button>
                                </form>
                            </div>
                        </div>
                        
                        <hr>
                        
                        <div class="alert alert-info">
                            <h6><i class="fas fa-info-circle me-2"></i>تعليمات الاختبار:</h6>
                            <ol>
                                <li>انقر على أي زر أعلاه</li>
                                <li>أو حاول إرسال النموذج</li>
                                <li>يجب أن تظهر نافذة تحذير النسخة التجريبية</li>
                                <li>تحقق من وجود شريط التحذير في الأعلى</li>
                                <li>تحقق من وجود شارة النسخة التجريبية في الزاوية</li>
                            </ol>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.2/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- نسخة مبسطة من demo-mode.js للاختبار -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // إضافة شريط التحذير
            const warningBar = document.createElement('div');
            warningBar.className = 'demo-warning-bar';
            warningBar.innerHTML = `
                <i class="fas fa-exclamation-triangle"></i>
                تحذير: هذه نسخة تجريبية - جميع البيانات وهمية لأغراض العرض فقط
                <button onclick="this.parentElement.remove()" style="float: left; background: none; border: none; color: white; font-size: 16px;">
                    <i class="fas fa-times"></i>
                </button>
            `;
            document.body.insertBefore(warningBar, document.body.firstChild);
            
            // إضافة العلامة المائية
            const watermark = document.createElement('div');
            watermark.className = 'demo-watermark';
            watermark.textContent = 'نسخة تجريبية';
            document.body.appendChild(watermark);
            
            // إضافة شارة النسخة التجريبية
            const badge = document.createElement('div');
            badge.className = 'demo-badge';
            badge.innerHTML = '<i class="fas fa-flask"></i> DEMO';
            document.body.appendChild(badge);
            
            // إضافة كلاس النسخة التجريبية
            document.body.classList.add('demo-mode');
            
            // دالة إظهار النافذة المنبثقة
            function showDemoRestrictionModal() {
                const modal = document.createElement('div');
                modal.className = 'modal fade';
                modal.innerHTML = `
                    <div class="modal-dialog modal-lg">
                        <div class="modal-content">
                            <div class="modal-header bg-warning text-dark">
                                <h5 class="modal-title">
                                    <i class="fas fa-exclamation-triangle me-2"></i>نسخة تجريبية - عرض المميزات فقط
                                </h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                            </div>
                            <div class="modal-body">
                                <div class="alert alert-info">
                                    <h6><i class="fas fa-info-circle me-2"></i>هذه نسخة تجريبية للعرض فقط</h6>
                                    <p class="mb-0">لا يمكن إتمام عمليات الحفظ أو التعديل أو الحذف في النسخة التجريبية. هذه النسخة مخصصة لعرض مميزات النظام فقط.</p>
                                </div>
                                
                                <div class="alert alert-success mt-3">
                                    <h6><i class="fas fa-crown me-2"></i>للحصول على النسخة الكاملة</h6>
                                    <p class="mb-3"><strong>تواصل مع المطور:</strong> بشمهندس محمود صلاح</p>
                                    <div class="row">
                                        <div class="col-md-4">
                                            <a href="https://wa.me/201556262660" target="_blank" class="btn btn-success w-100 mb-2">
                                                <i class="fab fa-whatsapp me-2"></i>واتساب
                                            </a>
                                        </div>
                                        <div class="col-md-4">
                                            <a href="tel:+201556262660" class="btn btn-primary w-100 mb-2">
                                                <i class="fas fa-phone me-2"></i>اتصال
                                            </a>
                                        </div>
                                        <div class="col-md-4">
                                            <a href="mailto:<EMAIL>" class="btn btn-secondary w-100 mb-2">
                                                <i class="fas fa-envelope me-2"></i>بريد
                                            </a>
                                        </div>
                                    </div>
                                    <div class="text-center mt-2">
                                        <small class="text-muted">
                                            <i class="fas fa-globe me-1"></i>hoktech.site | 
                                            <i class="fas fa-phone me-1"></i>+201556262660
                                        </small>
                                    </div>
                                </div>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                                    <i class="fas fa-times me-1"></i>إغلاق
                                </button>
                                <a href="https://wa.me/201556262660" target="_blank" class="btn btn-success">
                                    <i class="fab fa-whatsapp me-1"></i>تواصل للحصول على النسخة الكاملة
                                </a>
                            </div>
                        </div>
                    </div>
                `;
                
                document.body.appendChild(modal);
                const bsModal = new bootstrap.Modal(modal);
                bsModal.show();
                
                modal.addEventListener('hidden.bs.modal', () => {
                    modal.remove();
                });
            }
            
            // اعتراض جميع الأزرار والنماذج
            document.addEventListener('click', function(e) {
                if (e.target.matches('button, input[type="submit"], .btn')) {
                    e.preventDefault();
                    e.stopPropagation();
                    showDemoRestrictionModal();
                    return false;
                }
            });
            
            document.addEventListener('submit', function(e) {
                e.preventDefault();
                e.stopPropagation();
                showDemoRestrictionModal();
                return false;
            });
        });
    </script>
</body>
</html>
