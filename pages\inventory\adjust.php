<?php
/**
 * صفحة تسوية المخزون
 * تسمح للمستخدمين بإجراء جرد للمخزون وتسوية الفروقات
 */

// تعريف ثابت لمنع الوصول المباشر للملفات المرتبطة
define('BASEPATH', true);

// استدعاء ملف التهيئة
require_once '../../config/init.php';

// التحقق من تسجيل الدخول
redirectIfNotLoggedIn();

// التحقق من صلاحية الوصول
if (!hasPermission('inventory_adjust')) {
    $_SESSION['error_message'] = 'ليس لديك صلاحية لتسوية المخزون';
    header('Location: ' . BASE_URL . 'pages/inventory/index.php');
    exit;
}

// تحديد الفلاتر الافتراضية
$currentBranchId = $_SESSION['user_branch_id'];
$isAdmin = ($_SESSION['user_role'] === ROLE_ADMIN);

// الحصول على فئات المنتجات
$productObj = new Product($db);
$categories = $productObj->getProductCategories();

// الحصول على الفروع (للمدير فقط)
$branches = [];
if ($isAdmin) {
    $branchObj = new Branch($db);
    $branches = $branchObj->getBranches();
}

// عنوان الصفحة
$pageTitle = 'تسوية المخزون';

// استدعاء قالب الرأس
include_once '../../includes/templates/header.php';
?>

<!-- تأكد من تحميل jQuery أولاً -->
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

<!-- ثم إضافة مكتبات toastr -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.css">
<script src="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.js"></script>

<!-- تهيئة toastr -->
<script>
    $(document).ready(function() {
        // تهيئة إعدادات toastr
        if (typeof toastr !== 'undefined') {
            toastr.options = {
                closeButton: true,
                progressBar: true,
                positionClass: 'toast-top-left',
                timeOut: 3000
            };
        }
    });
</script>

<!-- بداية محتوى الصفحة -->
<div class="page-content">
    <div class="container-fluid">
        
        <!-- عنوان الصفحة والأزرار -->
        <div class="row mb-4 align-items-center">
            <div class="col-md-6">
                <h4 class="fw-bold mb-2"><?php echo $pageTitle; ?></h4>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb mb-0">
                        <li class="breadcrumb-item">
                            <a href="<?php echo BASE_URL . 'pages/dashboard.php'; ?>" class="text-decoration-none">
                                <i class="fas fa-home me-1"></i>الرئيسية
                            </a>
                        </li>
                        <li class="breadcrumb-item">
                            <a href="<?php echo BASE_URL . 'pages/inventory/index.php'; ?>" class="text-decoration-none">
                                المخزون
                            </a>
                        </li>
                        <li class="breadcrumb-item active" aria-current="page">تسوية المخزون</li>
                    </ol>
                </nav>
            </div>
            <div class="col-md-6 text-md-end">
                <a href="<?php echo BASE_URL . 'pages/inventory/index.php'; ?>" class="btn btn-secondary">
                    <i class="fas fa-arrow-right me-1"></i> العودة للمخزون
                </a>
            </div>
        </div>
        
        <!-- بطاقة اختيار المنتجات للتسوية -->
        <div class="card mb-4">
            <div class="card-header bg-light">
                <h5 class="mb-0">اختيار المنتجات للتسوية</h5>
            </div>
            <div class="card-body">
                <form id="inventory-filter-form" method="get">
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label for="category_id" class="form-label">الفئة</label>
                            <select class="form-select" id="category_id" name="category_id">
                                <option value="">كل الفئات</option>
                                <?php foreach ($categories as $category): ?>
                                    <option value="<?php echo $category['id']; ?>"><?php echo $category['name']; ?></option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <?php if ($isAdmin): ?>
                            <div class="col-md-4 mb-3">
                                <label for="branch_id" class="form-label">الفرع</label>
                                <select class="form-select" id="branch_id" name="branch_id">
                                    <?php foreach ($branches as $branch): ?>
                                        <option value="<?php echo $branch['id']; ?>" <?php echo ($branch['id'] == $currentBranchId) ? 'selected' : ''; ?>>
                                            <?php echo $branch['name']; ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        <?php else: ?>
                            <input type="hidden" name="branch_id" id="branch_id" value="<?php echo $currentBranchId; ?>">
                        <?php endif; ?>
                        <div class="col-md-4 mb-3">
                            <label for="search" class="form-label">البحث</label>
                            <input type="text" class="form-control" id="search" name="search" placeholder="اسم المنتج...">
                        </div>
                        <div class="col-12 mt-2">
                            <div class="d-flex justify-content-end">
                                <button type="submit" class="btn btn-primary px-4">
                                    <i class="fas fa-search me-2"></i> عرض المنتجات
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
        
        <!-- بطاقة نموذج تسوية المخزون -->
        <div class="card mb-4" id="adjust-stock-form-card" style="display: none;">
            <div class="card-header bg-light d-flex justify-content-between align-items-center">
                <h5 class="mb-0">تسوية المخزون</h5>
                <div class="btn-group btn-group-sm">
                    <button type="button" class="btn btn-outline-secondary" id="toggle-all-products">
                        <i class="fas fa-eye me-1"></i> إظهار/إخفاء الكل
                    </button>
                    <button type="button" class="btn btn-outline-secondary" id="toggle-only-differences">
                        <i class="fas fa-filter me-1"></i> إظهار الاختلافات فقط
                    </button>
                </div>
            </div>
            <div class="card-body">
                <form id="adjust-stock-form">
                    <div class="mb-3">
                        <label for="adjustment_note" class="form-label">ملاحظات التسوية</label>
                        <textarea class="form-control" id="adjustment_note" name="adjustment_note" rows="2" placeholder="سبب التسوية أو أي ملاحظات مهمة..."></textarea>
                    </div>
                    
                    <div class="table-responsive mt-4">
                        <table class="table table-bordered table-striped" id="products-table">
                            <thead class="table-light">
                                <tr>
                                    <th style="width: 5%">#</th>
                                    <th style="width: 30%">اسم المنتج</th>
                                    <th style="width: 15%">الفئة</th>
                                    <th style="width: 15%">الكمية الحالية</th>
                                    <th style="width: 15%">الكمية الفعلية</th>
                                    <th style="width: 10%">الفرق</th>
                                    <th style="width: 10%">ملاحظات</th>
                                </tr>
                            </thead>
                            <tbody id="products-list">
                                <!-- سيتم تعبئتها عبر الجافاسكربت -->
                            </tbody>
                        </table>
                    </div>
                    
                    <div class="alert alert-info mt-4" id="adjustment-summary">
                        <h6>ملخص التسوية:</h6>
                        <p class="mb-1">إجمالي المنتجات: <span id="total-products">0</span></p>
                        <p class="mb-1">المنتجات التي تحتاج تسوية: <span id="products-to-adjust">0</span></p>
                        <p class="mb-0">إجمالي الفروقات: <span id="total-differences">0</span> وحدة</p>
                    </div>
                    
                    <div class="mt-4 text-end">
                        <button type="button" class="btn btn-outline-secondary me-2" id="cancel-adjustment">
                            <i class="fas fa-times me-1"></i> إلغاء
                        </button>
                        <button type="submit" class="btn btn-primary" id="save-adjustment">
                            <i class="fas fa-save me-1"></i> حفظ التسوية
                        </button>
                    </div>
                </form>
            </div>
        </div>
        
        <!-- جدول المنتجات للاختيار -->
        <div class="card" id="products-selection-card">
            <div class="card-header bg-light d-flex justify-content-between align-items-center">
                <h5 class="mb-0">المنتجات المتاحة</h5>
                <div>
                    <span class="badge bg-primary" id="products-count">0</span> منتج
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-bordered table-hover" id="products-selection-table">
                        <thead class="table-light">
                            <tr>
                                <th width="5%">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="select-all-products">
                                    </div>
                                </th>
                                <th width="35%">اسم المنتج</th>
                                <th width="15%">الفئة</th>
                                <th width="15%">المخزون الحالي</th>
                                <th width="15%">آخر تسوية</th>
                                <th width="15%">حالة المخزون</th>
                            </tr>
                        </thead>
                        <tbody id="products-selection-list">
                            <!-- سيتم تعبئتها عبر الجافاسكربت -->
                            <tr>
                                <td colspan="6" class="text-center">
                                    <div class="py-5">
                                        <i class="fas fa-search fa-3x text-muted mb-3"></i>
                                        <p class="text-muted">قم باختيار الفئة أو ابحث عن المنتجات للبدء</p>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                
                <div class="mt-4 text-end" id="select-products-btn" style="display: none;">
                    <button type="button" class="btn btn-primary" id="start-adjustment">
                        <i class="fas fa-check me-1"></i> البدء بتسوية المنتجات المحددة
                    </button>
                </div>
            </div>
        </div>
        
    </div>
</div>

<!-- موديل تأكيد التسوية -->
<div class="modal fade" id="confirmAdjustmentModal" tabindex="-1" aria-labelledby="confirmAdjustmentModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="confirmAdjustmentModalLabel">تأكيد تسوية المخزون</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من تسوية المخزون للمنتجات المحددة؟</p>
                <div class="alert alert-warning mb-0">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <span>سيتم تعديل المخزون وفق للكميات الفعلية المدخلة. هذا الإجراء لا يمكن التراجع عنه.</span>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" id="confirm-adjustment">تأكيد التسوية</button>
            </div>
        </div>
    </div>
</div>

<!-- جافاسكربت خاص بالصفحة -->
<script>
    // التحقق من وجود jQuery وتحميلها إذا لم تكن موجودة
    if (typeof jQuery === 'undefined') {
        document.write('<script src="https://code.jquery.com/jquery-3.6.0.min.js"><\/script>');
    }
    
    // استخدام window.onload بدلاً من $(document).ready للتأكد من تحميل jQuery
    window.onload = function() {
        // التحقق مرة أخرى من وجود jQuery
        if (typeof jQuery === 'undefined') {
            console.error('فشل تحميل jQuery. بعض الوظائف قد لا تعمل بشكل صحيح.');
            return;
        }
        
        // متغيرات عامة
        let selectedProducts = [];
        let productsData = [];
        
        // البحث عن المنتجات للتسوية
        $('#inventory-filter-form').on('submit', function(e) {
            e.preventDefault();
            
            // جمع البيانات من النموذج
            const categoryId = $('#category_id').val();
            const branchId = $('#branch_id').val();
            const search = $('#search').val();
            
            // التحقق من وجود معرف الفرع
            if (!branchId) {
                showToast('خطأ', 'يجب اختيار الفرع', 'error');
                return;
            }
            
            // إظهار مؤشر التحميل
            $('#products-selection-list').html(`
                <tr>
                    <td colspan="6" class="text-center">
                        <div class="d-flex justify-content-center py-5">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">جاري التحميل...</span>
                            </div>
                        </div>
                        <p class="text-muted">جاري تحميل المنتجات...</p>
                    </div>
                </tr>
            `);
            
            // إرسال طلب AJAX
            $.ajax({
                url: '<?php echo API_URL; ?>inventory.php?action=list',
                type: 'GET',
                data: {
                    branch_id: branchId,
                    category_id: categoryId,
                    search: search,
                    limit: 100  // الحد الأقصى للمنتجات
                },
                dataType: 'json',
                success: function(response) {
                    if (response.status === 'success') {
                        productsData = response.inventory;
                        
                        // تحديث عداد المنتجات
                        $('#products-count').text(productsData.length);
                        
                        // عرض المنتجات
                        displayProductsForSelection(productsData);
                        
                        // إظهار زر البدء بالتسوية
                        $('#select-products-btn').show();
                    } else {
                        // عرض رسالة الخطأ
                        $('#products-selection-list').html(`
                            <tr>
                                <td colspan="6" class="text-center text-danger">
                                    <i class="fas fa-exclamation-triangle me-2"></i>
                                    ${response.message || 'حدث خطأ أثناء تحميل البيانات'}
                                </td>
                            </tr>
                        `);
                    }
                },
                error: function(xhr, status, error) {
                    // عرض رسالة الخطأ
                    $('#products-selection-list').html(`
                        <tr>
                            <td colspan="6" class="text-center text-danger">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                حدث خطأ أثناء الاتصال بالخادم
                            </td>
                        </tr>
                    `);
                    console.error('AJAX Error:', error);
                }
            });
        });
        
        // عرض المنتجات للاختيار
        function displayProductsForSelection(products) {
            let html = '';
            
            if (products.length === 0) {
                html = `
                    <tr>
                        <td colspan="6" class="text-center">
                            <div class="py-4">
                                <i class="fas fa-box-open fa-3x text-muted mb-3"></i>
                                <p class="text-muted">لا توجد منتجات متاحة وفق للفلترة المحددة</p>
                            </div>
                        </td>
                    </tr>
                `;
            } else {
                $.each(products, function(index, product) {
                    let stockStatusHtml = '';
                    
                    if (product.quantity <= 0) {
                        stockStatusHtml = '<span class="badge bg-danger">نفذ</span>';
                    } else if (product.quantity < product.min_quantity) {
                        stockStatusHtml = '<span class="badge bg-warning text-dark">منخفض</span>';
                    } else {
                        stockStatusHtml = '<span class="badge bg-success">متوفر</span>';
                    }
                    
                    html += `
                        <tr>
                            <td>
                                <div class="form-check">
                                    <input class="form-check-input product-checkbox" type="checkbox" value="${product.product_id}" data-name="${product.name}" data-quantity="${product.quantity}">
                                </div>
                            </td>
                            <td>${product.product_name}</td>
                            <td>${product.category_name || '-'}</td>
                            <td>${product.quantity}</td>
                            <td>${product.last_adjustment || '-'}</td>
                            <td>${stockStatusHtml}</td>
                        </tr>
                    `;
                });
            }
            
            $('#products-selection-list').html(html);
        }
        
        // تحديد/إلغاء تحديد جميع المنتجات
        $('#select-all-products').on('change', function() {
            $('.product-checkbox').prop('checked', $(this).is(':checked'));
        });
        
        // تحديث حالة زر "تحديد الكل" عند تغيير أي صندوق اختيار
        $(document).on('change', '.product-checkbox', function() {
            const allChecked = $('.product-checkbox:checked').length === $('.product-checkbox').length;
            $('#select-all-products').prop('checked', allChecked);
        });
        
        // البدء بتسوية المنتجات المحددة
        $('#start-adjustment').on('click', function() {
            // جمع المنتجات المحددة
            selectedProducts = [];
            $('.product-checkbox:checked').each(function() {
                const productId = $(this).val();
                const product = productsData.find(p => p.product_id == productId);
                
                if (product) {
                    selectedProducts.push({
                        id: product.product_id,
                        name: product.product_name,
                        category: product.category_name || '-',
                        current_quantity: product.quantity,
                        actual_quantity: product.quantity, // نفس الكمية الحالية افتراض
                        difference: 0,
                        notes: ''
                    });
                }
            });
            
            if (selectedProducts.length === 0) {
                showToast('تنبيه', 'الرجاء اختيار منتج واحد على الأقل', 'warning');
                return;
            }
            
            // عرض نموذج التسوية وإخفاء قائمة الاختيار
            $('#products-selection-card').hide();
            $('#adjust-stock-form-card').show();
            
            // عرض المنتجات في نموذج التسوية
            displayProductsForAdjustment();
            
            // تحديث ملخص التسوية
            updateAdjustmentSummary();
        });
        
        // عرض المنتجات في نموذج التسوية
        function displayProductsForAdjustment() {
            let html = '';
            
            $.each(selectedProducts, function(index, product) {
                html += `
                    <tr class="product-row" data-id="${product.id}" data-difference="0">
                        <td>${index + 1}</td>
                        <td>${product.name}</td>
                        <td>${product.category}</td>
                        <td>${product.current_quantity}</td>
                        <td>
                            <input type="number" class="form-control actual-quantity" 
                                   data-id="${product.id}" 
                                   value="${product.actual_quantity}" 
                                   min="0">
                        </td>
                        <td class="difference-cell">0</td>
                        <td>
                            <input type="text" class="form-control product-note" 
                                   data-id="${product.id}" 
                                   placeholder="ملاحظات...">
                        </td>
                    </tr>
                `;
            });
            
            $('#products-list').html(html);
        }
        
        // تحديث الفرق عند تغيير الكمية الفعلية
        $(document).on('input', '.actual-quantity', function() {
            const productId = $(this).data('id');
            const currentQuantity = selectedProducts.find(p => p.id == productId).current_quantity;
            const actualQuantity = parseInt($(this).val()) || 0;
            const difference = actualQuantity - currentQuantity;
            
            // تحديث الفرق في الجدول
            $(this).closest('tr').find('.difference-cell').text(difference);
            $(this).closest('tr').attr('data-difference', difference);
            
            // تحديث الفرق في مصفوفة المنتجات
            const productIndex = selectedProducts.findIndex(p => p.id == productId);
            if (productIndex !== -1) {
                selectedProducts[productIndex].actual_quantity = actualQuantity;
                selectedProducts[productIndex].difference = difference;
            }
            
            // تحديث ملخص التسوية
            updateAdjustmentSummary();
            
            // تمييز الصف بلون مختلف إذا كان هناك فرق
            if (difference !== 0) {
                $(this).closest('tr').addClass('table-warning');
            } else {
                $(this).closest('tr').removeClass('table-warning');
            }
        });
        
        // تحديث الملاحظات عند تغييرها
        $(document).on('input', '.product-note', function() {
            const productId = $(this).data('id');
            const note = $(this).val();
            
            // تحديث الملاحظات في مصفوفة المنتجات
            const productIndex = selectedProducts.findIndex(p => p.id == productId);
            if (productIndex !== -1) {
                selectedProducts[productIndex].notes = note;
            }
        });
        
        // تحديث ملخص التسوية
        function updateAdjustmentSummary() {
            const totalProducts = selectedProducts.length;
            const productsToAdjust = selectedProducts.filter(p => p.difference !== 0).length;
            const totalDifferences = selectedProducts.reduce((sum, p) => sum + p.difference, 0);
            
            $('#total-products').text(totalProducts);
            $('#products-to-adjust').text(productsToAdjust);
            $('#total-differences').text(totalDifferences);
            
            // تغيير لون إجمالي الفروقات حسب القيمة
            if (totalDifferences > 0) {
                $('#total-differences').removeClass('text-danger').addClass('text-success');
            } else if (totalDifferences < 0) {
                $('#total-differences').removeClass('text-success').addClass('text-danger');
            } else {
                $('#total-differences').removeClass('text-success text-danger');
            }
        }
        
        // إظهار/إخفاء كل المنتجات
        $('#toggle-all-products').on('click', function() {
            const allVisible = $('.product-row:visible').length === $('.product-row').length;
            
            if (allVisible) {
                // إخفاء كل المنتجات التي ليس لها فرق
                $('.product-row[data-difference="0"]').hide();
            } else {
                // إظهار كل المنتجات
                $('.product-row').show();
            }
        });
        
        // إظهار الاختلافات فقط
        $('#toggle-only-differences').on('click', function() {
            // إخفاء كل المنتجات
            $('.product-row').hide();
            
            // إظهار المنتجات التي لها فرق فقط
            $('.product-row[data-difference!="0"]').show();
            
            // إذا لم يتم إظهار أي منتج، عرض رسالة
            if ($('.product-row:visible').length === 0) {
                showToast('تنبيه', 'لا توجد منتجات بها اختلافات', 'info');
            }
        });
        
        // إلغاء التسوية والعودة لقائمة الاختيار
        $('#cancel-adjustment').on('click', function() {
            // إعادة تعيين المنتجات المحددة
            selectedProducts = [];
            
            // إخفاء نموذج التسوية وإظهار قائمة الاختيار
            $('#adjust-stock-form-card').hide();
            $('#products-selection-card').show();
        });
        
        // تقديم نموذج التسوية
        $('#adjust-stock-form').on('submit', function(e) {
            e.preventDefault();
            
            // التحقق من وجود تغييرات
            const hasChanges = selectedProducts.some(p => p.difference !== 0);
            if (!hasChanges) {
                showToast('تنبيه', 'لم يتم إجراء أي تغييرات على المخزون', 'warning');
                return;
            }
            
            // عرض موديل التأكيد
            $('#confirmAdjustmentModal').modal('show');
        });
        
        // تأكيد التسوية
        $('#confirm-adjustment').on('click', function() {
            // جمع البيانات للإرسال
            const adjustmentData = {
                branch_id: $('#branch_id').val(),
                notes: $('#adjustment_note').val() || 'تسوية المخزون',
                products: []
            };
            
            // إضافة المنتجات التي تحتاج للتسوية فقط
            $.each(selectedProducts, function(index, product) {
                if (product.difference !== 0) {
                    adjustmentData.products.push({
                        product_id: product.id,
                        actual_quantity: product.actual_quantity,
                        notes: product.notes
                    });
                }
            });
            
            // تعطيل زر التأكيد وإظهار مؤشر التحميل
            $('#confirm-adjustment').prop('disabled', true).html('<span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span> جاري التسوية...');
            
            // إرسال البيانات إلى الخادم
            $.ajax({
                url: '<?php echo API_URL; ?>inventory.php?action=process-stock-take',
                type: 'POST',
                data: {
                    branch_id: adjustmentData.branch_id,
                    notes: adjustmentData.notes,
                    stock_take_results: JSON.stringify(adjustmentData.products)
                },
                dataType: 'json',
                success: function(response) {
                    // إعادة تفعيل زر التأكيد
                    $('#confirm-adjustment').prop('disabled', false).html('تأكيد التسوية');
                    
                    // إغلاق موديل التأكيد
                    $('#confirmAdjustmentModal').modal('hide');
                    
                    if (response.status === 'success') {
                        // عرض رسالة النجاح
                        showToast('نجاح', 'تمت تسوية المخزون بنجاح', 'success');
                        
                        // إعادة توجيه المستخدم إلى صفحة المخزون بعد ثانيتين
                        setTimeout(function() {
                            window.location.href = '<?php echo BASE_URL; ?>pages/inventory/index.php';
                        }, 2000);
                    } else {
                        // عرض رسالة الخطأ
                        showToast('خطأ', response.message, 'error');
                    }
                },
                error: function(xhr, status, error) {
                    // إعادة تفعيل زر التأكيد
                    $('#confirm-adjustment').prop('disabled', false).html('تأكيد التسوية');
                    
                    // إغلاق موديل التأكيد
                    $('#confirmAdjustmentModal').modal('hide');
                    
                    let errorMessage = 'حدث خطأ أثناء تسوية المخزون';
                    try {
                        const response = JSON.parse(xhr.responseText);
                        if (response.message) {
                            errorMessage = response.message;
                        }
                    } catch (e) {
                        console.error(e);
                    }
                    
                    showToast('خطأ', errorMessage, 'error');
                }
            });
        });
        
        // عرض رسالة توست
        function showToast(title, message, type) {
            if (typeof toastr !== 'undefined') {
                toastr.options = {
                    closeButton: true,
                    progressBar: true,
                    positionClass: 'toast-top-left',
                    timeOut: 3000
                };
                
                if (type === 'success') {
                    toastr.success(message, title);
                } else if (type === 'error') {
                    toastr.error(message, title);
                } else if (type === 'warning') {
                    toastr.warning(message, title);
                } else {
                    toastr.info(message, title);
                }
            } else {
                // بديل في حالة عدم توفر toastr
                let alertClass = 'alert-info';
                if (type === 'success') alertClass = 'alert-success';
                if (type === 'error') alertClass = 'alert-danger';
                if (type === 'warning') alertClass = 'alert-warning';
                
                // إنشاء حاوية للتنبيهات إذا لم تكن موجودة
                if (!$('#alertContainer').length) {
                    $('<div id="alertContainer" class="alert-container position-fixed top-0 end-0 p-3" style="z-index: 1050;"></div>').appendTo('body');
                }
                
                const alertHtml = `
                    <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
                        <strong>${title}</strong> ${message}
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                `;
                
                // إضافة التنبيه للحاوية
                $('#alertContainer').append(alertHtml);
                
                // إزالة التنبيه بعد 3 ثوانٍ
                setTimeout(function() {
                    $('#alertContainer .alert').first().alert('close');
                }, 3000);
            }
        }
    };
</script>

<?php
// استدعاء قالب الذيل
include_once '../../includes/templates/footer.php';
?>
