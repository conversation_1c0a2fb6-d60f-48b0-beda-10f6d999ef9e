<?php
/**
 * API لإشعارات نهاية اليوم
 * يتعامل مع إرسال إشعارات WhatsApp للمدراء عند فتح وإغلاق يوم العمل
 */

// تعريف ثابت لمنع الوصول المباشر للملفات المرتبطة
define('BASEPATH', true);

// استدعاء ملفات التهيئة
require_once '../config/init.php';

// التحقق من تسجيل الدخول
requireLogin();

// استجابة JSON
header('Content-Type: application/json');

try {
    // التحقق من وجود الإجراء المطلوب
    if (!isset($_GET['action'])) {
        throw new Exception('الإجراء غير محدد');
    }

    $action = $_GET['action'];

    // إنشاء كائنات النماذج
    $db = new Database();
    $endDayModel = new EndDay($db);
    $settingsModel = new Settings($db);
    $userModel = new User($db);
    $branchModel = new Branch($db);

    // الحصول على إعدادات النظام
    $settings = $settingsModel->getAllSettings();

    // التحقق من تفعيل إشعارات WhatsApp
    $whatsappEnabled = isset($settings['whatsapp_enabled']) && $settings['whatsapp_enabled'] == '1';
    $adminNotificationsEnabled = isset($settings['notification_enable_admin']) && $settings['notification_enable_admin'] == '1';

    switch ($action) {
        // التحقق من وجود أرقام هواتف للمدراء
        case 'check_admin_phones':
            // تسجيل الطلب للتشخيص
            error_log("API: طلب التحقق من أرقام هواتف المدراء");

            // الحصول على أرقام هواتف المدراء
            $adminPhones = [];

            // التحقق من وجود أرقام هواتف في الإعدادات - البحث في جميع المفاتيح المحتملة
            $possibleKeys = ['notification_admin_phone_numbers', 'admin_phone_numbers', 'dmin_phone_numbers'];
            $foundPhoneNumbers = false;

            foreach ($possibleKeys as $key) {
                error_log("API: التحقق من إعداد {$key}: " . ($settings[$key] ?? 'NULL'));

                if (!empty($settings[$key])) {
                    $phoneNumbersStr = $settings[$key];
                    $phoneNumbers = explode("\n", $phoneNumbersStr);

                    foreach ($phoneNumbers as $phoneNumber) {
                        $phoneNumber = trim($phoneNumber);
                        if (!empty($phoneNumber)) {
                            $adminPhones[] = [
                                'phone' => $phoneNumber,
                                'name' => 'المدير'
                            ];
                        }
                    }

                    $foundPhoneNumbers = true;
                    error_log("API: تم العثور على " . count($adminPhones) . " رقم هاتف من الإعدادات باستخدام المفتاح {$key}");
                }
            }

            if ($foundPhoneNumbers) {
                error_log("API: تم العثور على إجمالي " . count($adminPhones) . " رقم هاتف من الإعدادات");
            }

            // إذا لم يتم تحديد أرقام هواتف المدراء، نستخدم أرقام المستخدمين ذوي دور المدير
            if (empty($adminPhones)) {
                error_log("API: البحث عن أرقام هواتف المستخدمين ذوي دور المدير");
                $admins = $userModel->getUsersByRole(ROLE_ADMIN);
                error_log("API: تم العثور على " . count($admins) . " مستخدم بدور مدير");

                foreach ($admins as $admin) {
                    if (!empty($admin['phone'])) {
                        $adminPhones[] = [
                            'phone' => $admin['phone'],
                            'name' => $admin['name']
                        ];
                    }
                }
                error_log("API: تم العثور على " . count($adminPhones) . " رقم هاتف من المستخدمين");
            }

            // إضافة رقم افتراضي للتجربة إذا لم يتم العثور على أرقام
            if (empty($adminPhones)) {
                error_log("API: لم يتم العثور على أي أرقام هواتف، إضافة رقم افتراضي");

                // استخدام الرقم الموجود في الصورة
                $adminPhones[] = [
                    'phone' => '201555262660',
                    'name' => 'المدير (افتراضي)'
                ];

                // محاولة حفظ هذا الرقم في الإعدادات للاستخدام المستقبلي
                try {
                    $settingsModel->set('notification_admin_phone_numbers', '201555262660', 'أرقام هواتف المدراء للإشعارات');
                    error_log("API: تم حفظ رقم الهاتف الافتراضي في الإعدادات");
                } catch (Exception $e) {
                    error_log("API: خطأ في حفظ رقم الهاتف الافتراضي: " . $e->getMessage());
                }
            }

            // إرجاع البيانات
            $response = [
                'status' => 'success',
                'data' => [
                    'admin_phones' => $adminPhones
                ]
            ];

            error_log("API: إرجاع " . count($adminPhones) . " رقم هاتف للمدراء");
            echo json_encode($response);
            break;

        // الحصول على بيانات يوم العمل لإرسال الإشعارات
        case 'get_endday_data':
            // التحقق من وجود معرف يوم العمل
            if (!isset($_POST['end_day_id']) || empty($_POST['end_day_id'])) {
                throw new Exception('معرف يوم العمل غير محدد');
            }

            // التحقق من نوع الإشعار
            if (!isset($_POST['notification_type']) || empty($_POST['notification_type'])) {
                throw new Exception('نوع الإشعار غير محدد');
            }

            $endDayId = intval($_POST['end_day_id']);
            $notificationType = $_POST['notification_type'];

            // التحقق من صحة نوع الإشعار
            if (!in_array($notificationType, ['open', 'close'])) {
                throw new Exception('نوع الإشعار غير صالح');
            }

            // الحصول على بيانات يوم العمل مباشرة من قاعدة البيانات
            // تحقق من هيكل جدول end_days - قد لا يحتوي على عمود created_by
            $db->prepare("SELECT
                            ed.*,
                            b.name as branch_name,
                            closer.name as closed_by_name
                        FROM end_days ed
                        LEFT JOIN branches b ON ed.branch_id = b.id
                        LEFT JOIN users closer ON ed.closed_by = closer.id
                        WHERE ed.id = :id");
            $db->bind(':id', $endDayId);
            $endDayDetails = $db->fetch();

            // استخدام البيانات المسترجعة مباشرة إذا كانت متوفرة
            if ($endDayDetails) {
                error_log("API get_endday_data: تم استرجاع بيانات يوم العمل مباشرة: " . json_encode($endDayDetails, JSON_UNESCAPED_UNICODE));
                $endDay = $endDayDetails;
            } else {
                // استخدام النموذج كخطة بديلة
                $endDay = $endDayModel->getEndDayById($endDayId);
                if (!$endDay) {
                    throw new Exception('يوم العمل غير موجود');
                }
            }

            // الحصول على بيانات الفرع
            $branch = $branchModel->getBranchById($endDay['branch_id']);
            if (!$branch) {
                throw new Exception('الفرع غير موجود');
            }

            // الحصول على أرقام هواتف المدراء - البحث في جميع المفاتيح المحتملة
            $adminPhones = [];
            $possibleKeys = ['notification_admin_phone_numbers', 'admin_phone_numbers', 'dmin_phone_numbers'];
            $foundPhoneNumbers = false;

            foreach ($possibleKeys as $key) {
                error_log("API get_endday_data: التحقق من إعداد {$key}: " . ($settings[$key] ?? 'NULL'));

                if (!empty($settings[$key])) {
                    $phoneNumbersStr = $settings[$key];
                    $phoneNumbers = explode("\n", $phoneNumbersStr);

                    foreach ($phoneNumbers as $phoneNumber) {
                        $phoneNumber = trim($phoneNumber);
                        if (!empty($phoneNumber)) {
                            $adminPhones[] = [
                                'phone' => $phoneNumber,
                                'name' => 'المدير'
                            ];
                        }
                    }

                    $foundPhoneNumbers = true;
                    error_log("API get_endday_data: تم العثور على " . count($adminPhones) . " رقم هاتف من الإعدادات باستخدام المفتاح {$key}");
                }
            }

            if ($foundPhoneNumbers) {
                error_log("API get_endday_data: تم العثور على إجمالي " . count($adminPhones) . " رقم هاتف من الإعدادات");
            }

            // إذا لم يتم تحديد أرقام هواتف المدراء، نستخدم أرقام المستخدمين ذوي دور المدير
            if (empty($adminPhones)) {
                $admins = $userModel->getUsersByRole(ROLE_ADMIN);
                foreach ($admins as $admin) {
                    if (!empty($admin['phone'])) {
                        $adminPhones[] = [
                            'phone' => $admin['phone'],
                            'name' => $admin['name']
                        ];
                    }
                }
            }

            // التحقق من وجود أرقام هواتف للمدراء
            if (empty($adminPhones)) {
                error_log("API get_endday_data: لم يتم العثور على أي أرقام هواتف، إضافة رقم افتراضي");

                // استخدام الرقم الموجود في الصورة
                $adminPhones[] = [
                    'phone' => '201555262660',
                    'name' => 'المدير (افتراضي)'
                ];

                // محاولة حفظ هذا الرقم في الإعدادات للاستخدام المستقبلي
                try {
                    $settingsModel->set('notification_admin_phone_numbers', '201555262660', 'أرقام هواتف المدراء للإشعارات');
                    error_log("API get_endday_data: تم حفظ رقم الهاتف الافتراضي في الإعدادات");
                } catch (Exception $e) {
                    error_log("API get_endday_data: خطأ في حفظ رقم الهاتف الافتراضي: " . $e->getMessage());
                }
            }

            // إعداد رسالة الإشعار حسب نوع الإشعار
            $message = '';
            if ($notificationType === 'open') {
                // رسالة فتح يوم العمل
                $message = "تنبيه: تم فتح يوم عمل جديد\n\n";
                $message .= "الفرع: {$branch['name']}\n";
                $message .= "التاريخ: " . date('Y-m-d', strtotime($endDay['date'])) . "\n";
                $message .= "وقت الفتح: " . date('h:i A', strtotime($endDay['created_at'])) . "\n";

                // إضافة اسم الفرع الذي تم فتح اليوم فيه
                // يبدو أن جدول end_days لا يحتوي على معلومات عن المستخدم الذي فتح اليوم
                error_log("API get_endday_data: استخدام اسم الفرع لعرض معلومات فتح اليوم: " . $branch['name']);
                $message .= "تم الفتح في فرع: {$branch['name']}\n";

                // إضافة اسم المستخدم الحالي كمعلومات إضافية
                $currentUser = $userModel->getUserById($_SESSION['user_id']);
                if ($currentUser && isset($currentUser['name'])) {
                    error_log("API get_endday_data: إضافة اسم المستخدم الحالي كمعلومات إضافية: " . $currentUser['name']);
                    $message .= "تم إرسال الإشعار بواسطة: {$currentUser['name']}\n";
                }
            } else {
                // رسالة إغلاق يوم العمل
                $message = "تنبيه: تم إغلاق يوم العمل\n\n";
                // تنسيق رقم اليوم بشكل صحيح
                $dayId = intval($endDay['id']);
                error_log("API get_endday_data: رقم اليوم قبل التنسيق: " . $endDay['id'] . ", بعد التنسيق: " . $dayId);
                $message .= "رقم اليوم: #" . $dayId . "\n";
                $message .= "الفرع: {$branch['name']}\n";
                $message .= "التاريخ: " . date('Y-m-d', strtotime($endDay['date'])) . "\n";
                $message .= "وقت الإغلاق: " . date('h:i A', strtotime($endDay['closed_at'])) . "\n";
                // تسجيل البيانات المالية للتشخيص
                error_log("API get_endday_data: البيانات المالية قبل التنسيق: " .
                          "المبيعات: {$endDay['total_sales']}, " .
                          "المصروفات: {$endDay['total_expenses']}, " .
                          "الخصومات: " . ($endDay['total_discounts'] ?? 0) . ", " .
                          "النقدية: {$endDay['cash_amount']}, " .
                          "البطاقة: {$endDay['card_amount']}");

                // التحقق من وجود رمز العملة في الإعدادات
                $currencySymbol = "ج.م";
                if (isset($settings['system_currency_symbol']) && !empty($settings['system_currency_symbol'])) {
                    $currencySymbol = $settings['system_currency_symbol'];
                } elseif (isset($settings['currency_symbol']) && !empty($settings['currency_symbol'])) {
                    $currencySymbol = $settings['currency_symbol'];
                }

                // تحويل البيانات المالية إلى أرقام صحيحة
                $totalSales = floatval($endDay['total_sales'] ?? 0);
                $totalExpenses = floatval($endDay['total_expenses'] ?? 0);
                $totalDiscounts = floatval($endDay['total_discounts'] ?? 0);
                $cashAmount = floatval($endDay['cash_amount'] ?? 0);
                $cardAmount = floatval($endDay['card_amount'] ?? 0);

                error_log("API get_endday_data: البيانات المالية بعد التحويل: " .
                          "المبيعات: {$totalSales}, " .
                          "المصروفات: {$totalExpenses}, " .
                          "الخصومات: {$totalDiscounts}, " .
                          "النقدية: {$cashAmount}, " .
                          "البطاقة: {$cardAmount}");

                // تنسيق البيانات المالية
                $message .= "إجمالي المبيعات: " . number_format($totalSales, 2) . " {$currencySymbol}\n";
                $message .= "إجمالي المصروفات: " . number_format($totalExpenses, 2) . " {$currencySymbol}\n";
                $message .= "إجمالي الخصومات: " . number_format($totalDiscounts, 2) . " {$currencySymbol}\n";
                $message .= "المدفوعات النقدية: " . number_format($cashAmount, 2) . " {$currencySymbol}\n";
                $message .= "مدفوعات البطاقة: " . number_format($cardAmount, 2) . " {$currencySymbol}\n";

                // إضافة اسم المستخدم الذي أغلق اليوم
                // استعلام مباشر للحصول على اسم المستخدم الذي أغلق اليوم
                $db->prepare("SELECT u.name
                              FROM users u
                              JOIN end_days ed ON u.id = ed.closed_by
                              WHERE ed.id = :id");
                $db->bind(':id', $endDay['id']);
                $closedByUser = $db->fetch();

                if ($closedByUser && isset($closedByUser['name']) && !empty($closedByUser['name'])) {
                    // استخدام الاسم من الاستعلام المباشر
                    error_log("API get_endday_data: استخدام اسم المستخدم من الاستعلام المباشر الجديد: " . $closedByUser['name']);
                    $message .= "تم الإغلاق بواسطة: {$closedByUser['name']}\n";
                } else if (isset($endDay['closed_by_name']) && !empty($endDay['closed_by_name'])) {
                    // استخدام الاسم من الاستعلام المباشر السابق
                    error_log("API get_endday_data: استخدام اسم المستخدم من الاستعلام المباشر السابق: " . $endDay['closed_by_name']);
                    $message .= "تم الإغلاق بواسطة: {$endDay['closed_by_name']}\n";
                } else if (isset($endDay['closed_by']) && !empty($endDay['closed_by'])) {
                    // محاولة الحصول على المستخدم من النموذج
                    $closedBy = $userModel->getUserById($endDay['closed_by']);
                    if ($closedBy && isset($closedBy['name'])) {
                        error_log("API get_endday_data: استخدام اسم المستخدم من النموذج: " . $closedBy['name']);
                        $message .= "تم الإغلاق بواسطة: {$closedBy['name']}\n";
                    } else {
                        // استخدام اسم المستخدم الحالي كملاذ أخير
                        $currentUser = $userModel->getUserById($_SESSION['user_id']);
                        if ($currentUser && isset($currentUser['name'])) {
                            error_log("API get_endday_data: استخدام اسم المستخدم الحالي: " . $currentUser['name']);
                            $message .= "تم الإغلاق بواسطة: {$currentUser['name']}\n";
                        } else {
                            // استخدام اسم الفرع كملاذ أخير جدًا
                            error_log("API get_endday_data: استخدام اسم الفرع كملاذ أخير: " . $branch['name']);
                            $message .= "تم الإغلاق من فرع: {$branch['name']}\n";
                        }
                    }
                } else {
                    // استخدام اسم الفرع كملاذ أخير
                    error_log("API get_endday_data: لا يوجد معرف للمستخدم الذي أغلق اليوم، استخدام اسم الفرع: " . $branch['name']);
                    $message .= "تم الإغلاق من فرع: {$branch['name']}\n";
                }

                // إضافة الملاحظات إذا وجدت
                if (!empty($endDay['notes'])) {
                    $message .= "\nملاحظات: {$endDay['notes']}\n";
                }
            }

            // إرجاع البيانات
            echo json_encode([
                'status' => 'success',
                'data' => [
                    'end_day_id' => $endDayId,
                    'notification_type' => $notificationType,
                    'admins' => $adminPhones,
                    'message' => $message,
                    'branch' => $branch
                ]
            ]);
            break;

        // تسجيل نتيجة إرسال الإشعار
        case 'log_notification':
            // التحقق من وجود معرف يوم العمل
            if (!isset($_POST['end_day_id']) || empty($_POST['end_day_id'])) {
                throw new Exception('معرف يوم العمل غير محدد');
            }

            $endDayId = intval($_POST['end_day_id']);
            $success = isset($_POST['success']) && $_POST['success'] == '1';
            $details = isset($_POST['details']) ? $_POST['details'] : '';

            // تسجيل النتيجة في ملف السجل
            $logMessage = date('Y-m-d H:i:s') . " - إشعار يوم العمل #{$endDayId}: " .
                          ($success ? 'نجاح' : 'فشل') . " - التفاصيل: {$details}";

            // التأكد من وجود مجلد السجلات
            $logDir = __DIR__ . '/../logs';
            if (!is_dir($logDir)) {
                mkdir($logDir, 0755, true);
            }

            error_log($logMessage . "\n", 3, $logDir . '/whatsapp_notifications.log');

            echo json_encode([
                'status' => 'success',
                'message' => 'تم تسجيل نتيجة الإشعار بنجاح'
            ]);
            break;

        default:
            throw new Exception('الإجراء غير صالح');
    }
} catch (Exception $e) {
    echo json_encode([
        'status' => 'error',
        'message' => $e->getMessage()
    ]);
}
