<?php
/**
 * صفحة إدارة فئات الخدمات
 * تسمح للمستخدمين بإضافة وتعديل وحذف فئات الخدمات في النظام
 */

// تعريف ثابت لمنع الوصول المباشر للملفات المرتبطة
define('BASEPATH', true);

// استدعاء ملف التهيئة
require_once '../../config/init.php';

// التحقق من تسجيل الدخول
redirectIfNotLoggedIn();

// التحقق من صلاحية الوصول
if (!hasPermission('services_edit')) {
    $_SESSION['error_message'] = 'ليس لديك صلاحية لإدارة فئات الخدمات';
    header('Location: ' . BASE_URL . 'pages/services/index.php');
    exit;
}

// إنشاء كائن الخدمة
$serviceObj = new Service($db);

// عنوان الصفحة
$pageTitle = 'إدارة فئات الخدمات';

// استدعاء قالب الرأس
include_once '../../includes/templates/header.php';
?>
<!-- إضافة مكتبة toastr -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.css">
<script src="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.js"></script>

<!-- تضمين ملف الوظائف المساعدة -->
<script src="<?php echo BASE_URL; ?>assets/js/utils.js"></script>
<!-- بداية محتوى الصفحة -->
<div class="page-content">
    <div class="container-fluid">
        
        <!-- عنوان الصفحة والأزرار -->
        <div class="row mb-4 align-items-center">
            <div class="col-md-6">
                <h4 class="fw-bold mb-2"><?php echo $pageTitle; ?></h4>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb mb-0">
                        <li class="breadcrumb-item">
                            <a href="<?php echo BASE_URL . 'pages/dashboard.php'; ?>" class="text-decoration-none">
                                <i class="fas fa-home me-1"></i>الرئيسية
                            </a>
                        </li>
                        <li class="breadcrumb-item">
                            <a href="<?php echo BASE_URL . 'pages/services/index.php'; ?>" class="text-decoration-none">
                                الخدمات
                            </a>
                        </li>
                        <li class="breadcrumb-item active" aria-current="page">فئات الخدمات</li>
                    </ol>
                </nav>
            </div>
            <div class="col-md-6 text-md-end">
                <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addCategoryModal">
                    <i class="fas fa-plus-circle me-1"></i> إضافة فئة جديدة
                </button>
                <a href="<?php echo BASE_URL . 'pages/services/index.php'; ?>" class="btn btn-secondary ms-2">
                    <i class="fas fa-arrow-right me-1"></i> العودة للخدمات
                </a>
            </div>
        </div>
        
        <!-- قائمة الفئات -->
        <div class="card">
            <div class="card-header bg-light d-flex justify-content-between align-items-center">
                <h5 class="mb-0">قائمة فئات الخدمات</h5>
                <div>
                    <span class="badge bg-primary" id="total-categories">0</span> فئة
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-bordered table-hover" id="categories-table">
                        <thead class="table-light">
                            <tr>
                                <th width="5%">#</th>
                                <th width="30%">اسم الفئة</th>
                                <th width="45%">الوصف</th>
                                <th width="20%">الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="categories-list">
                            <!-- سيتم تعبئتها عبر الجافاسكربت -->
                            <tr>
                                <td colspan="4" class="text-center">
                                    <div class="d-flex justify-content-center py-3">
                                        <div class="spinner-border text-primary" role="status">
                                            <span class="visually-hidden">جاري التحميل...</span>
                                        </div>
                                    </div>
                                    <p class="text-muted">جاري تحميل البيانات...</p>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        
    </div>
</div>

<!-- موديل إضافة فئة جديدة -->
<div class="modal fade" id="addCategoryModal" tabindex="-1" aria-labelledby="addCategoryModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addCategoryModalLabel">إضافة فئة جديدة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="add-category-form">
                    <div class="mb-3">
                        <label for="category_name" class="form-label">اسم الفئة <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="category_name" name="name" required>
                    </div>
                    <div class="mb-3">
                        <label for="category_description" class="form-label">وصف الفئة</label>
                        <textarea class="form-control" id="category_description" name="description" rows="3"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" id="btn-add-category">إضافة</button>
            </div>
        </div>
    </div>
</div>

<!-- موديل تعديل فئة -->
<div class="modal fade" id="editCategoryModal" tabindex="-1" aria-labelledby="editCategoryModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editCategoryModalLabel">تعديل فئة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="edit-category-form">
                    <input type="hidden" id="edit_category_id" name="id">
                    <div class="mb-3">
                        <label for="edit_category_name" class="form-label">اسم الفئة <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="edit_category_name" name="name" required>
                    </div>
                    <div class="mb-3">
                        <label for="edit_category_description" class="form-label">وصف الفئة</label>
                        <textarea class="form-control" id="edit_category_description" name="description" rows="3"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" id="btn-update-category">حفظ التغييرات</button>
            </div>
        </div>
    </div>
</div>

<!-- موديل تأكيد حذف فئة -->
<div class="modal fade" id="deleteCategoryModal" tabindex="-1" aria-labelledby="deleteCategoryModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title" id="deleteCategoryModalLabel">تأكيد حذف الفئة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من حذف الفئة: <strong id="delete-category-name"></strong>؟</p>
                <p class="text-danger">سيؤدي حذف الفئة إلى إزالة ارتباطها بجميع الخدمات، ولكن لن يتم حذف الخدمات المرتبطة بها.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-danger" id="confirm-delete">تأكيد الحذف</button>
            </div>
        </div>
    </div>
</div>

<!-- جافاسكربت خاص بالصفحة -->
<script>
    // التحقق من وجود jQuery وتحميلها إذا لم تكن موجودة
    if (typeof jQuery === 'undefined') {
        document.write('<script src="https://code.jquery.com/jquery-3.6.0.min.js"><\/script>');
    }
    
    // استخدام window.onload بدلاً من $(document).ready للتأكد من تحميل jQuery
    window.onload = function() {
        // التحقق مرة أخرى من وجود jQuery
        if (typeof jQuery === 'undefined') {
            console.error('فشل تحميل jQuery. بعض الوظائف قد لا تعمل بشكل صحيح.');
            return;
        }
        
        // تحميل فئات الخدمات عند تحميل الصفحة
        loadCategories();
        
        // وظيفة تحميل فئات الخدمات
        function loadCategories() {
            // إظهار مؤشر التحميل
            $('#categories-list').html(`
                <tr>
                    <td colspan="4" class="text-center">
                        <div class="d-flex justify-content-center py-3">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">جاري التحميل...</span>
                            </div>
                        </div>
                        <p class="text-muted">جاري تحميل البيانات...</p>
                    </td>
                </tr>
            `);
            
            // إرسال طلب AJAX
            $.ajax({
                url: '<?php echo API_URL; ?>services.php?action=categories&category_action=list',
                type: 'GET',
                dataType: 'json',
                success: function(response) {
                    if (response.status === 'success') {
                        // تحديث عدد الفئات
                        $('#total-categories').text(response.data.length);
                        
                        // عرض الفئات
                        displayCategories(response.data);
                    } else {
                        // عرض رسالة الخطأ
                        $('#categories-list').html(`
                            <tr>
                                <td colspan="4" class="text-center text-danger">
                                    <i class="fas fa-exclamation-triangle me-2"></i>
                                    ${response.message || 'حدث خطأ أثناء تحميل البيانات'}
                                </td>
                            </tr>
                        `);
                    }
                },
                error: function(xhr, status, error) {
                    // عرض رسالة الخطأ
                    $('#categories-list').html(`
                        <tr>
                            <td colspan="4" class="text-center text-danger">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                حدث خطأ أثناء الاتصال بالخادم
                            </td>
                        </tr>
                    `);
                    console.error('AJAX Error:', error);
                }
            });
        }
        
        // عرض الفئات في الجدول
        function displayCategories(categories) {
            let html = '';
            
            if (categories.length === 0) {
                html = '<tr><td colspan="4" class="text-center">لا توجد فئات للعرض</td></tr>';
            } else {
                $.each(categories, function(index, category) {
                    html += `
                        <tr>
                            <td>${index + 1}</td>
                            <td>${category.name}</td>
                            <td>${category.description || '-'}</td>
                            <td>
                                <button type="button" class="btn btn-sm btn-primary edit-category" data-id="${category.id}" data-name="${category.name}" data-description="${category.description || ''}">
                                    <i class="fas fa-edit"></i> تعديل
                                </button>
                                <button type="button" class="btn btn-sm btn-danger ms-1 delete-category" data-id="${category.id}" data-name="${category.name}">
                                    <i class="fas fa-trash"></i> حذف
                                </button>
                            </td>
                        </tr>
                    `;
                });
            }
            
            $('#categories-table tbody').html(html);
        }
        
        // إضافة فئة جديدة
        $('#btn-add-category').on('click', function() {
            // التحقق من صحة النموذج
            if (!$('#add-category-form')[0].checkValidity()) {
                $('#add-category-form')[0].reportValidity();
                return;
            }
            
            // جمع بيانات النموذج
            const categoryData = {
                name: $('#category_name').val(),
                description: $('#category_description').val()
            };
            
            // إرسال البيانات باستخدام AJAX
            $.ajax({
                url: '<?php echo API_URL; ?>services.php?action=categories&category_action=add',
                type: 'POST',
                data: categoryData,
                dataType: 'json',
                beforeSend: function() {
                    // تعطيل زر الإضافة وإظهار مؤشر التحميل
                    $('#btn-add-category').prop('disabled', true).html('<span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span> جاري الإضافة...');
                },
                success: function(response) {
                    // إعادة تفعيل زر الإضافة
                    $('#btn-add-category').prop('disabled', false).html('إضافة');
                    
                    if (response.status === 'success') {
                        // إغلاق الموديل
                        $('#addCategoryModal').modal('hide');
                        
                        // إعادة تعيين النموذج
                        $('#add-category-form')[0].reset();
                        
                        // عرض رسالة النجاح
                        showToast('نجاح', response.message, 'success');
                        
                        // إعادة تحميل الفئات
                        loadCategories();
                    } else {
                        // عرض رسالة الخطأ
                        showToast('خطأ', response.message, 'error');
                    }
                },
                error: function(xhr, status, error) {
                    // إعادة تفعيل زر الإضافة
                    $('#btn-add-category').prop('disabled', false).html('إضافة');
                    
                    let errorMessage = 'حدث خطأ أثناء إضافة الفئة';
                    try {
                        const response = JSON.parse(xhr.responseText);
                        if (response.message) {
                            errorMessage = response.message;
                        }
                    } catch (e) {
                        console.error(e);
                    }
                    
                    showToast('خطأ', errorMessage, 'error');
                }
            });
        });
        
        // تهيئة موديل تعديل الفئة
        $(document).on('click', '.edit-category', function() {
            const categoryId = $(this).data('id');
            const categoryName = $(this).data('name');
            const categoryDescription = $(this).data('description');
            
            $('#edit_category_id').val(categoryId);
            $('#edit_category_name').val(categoryName);
            $('#edit_category_description').val(categoryDescription);
            
            $('#editCategoryModal').modal('show');
        });
        
        // تحديث فئة
        $('#btn-update-category').on('click', function() {
            // التحقق من صحة النموذج
            if (!$('#edit-category-form')[0].checkValidity()) {
                $('#edit-category-form')[0].reportValidity();
                return;
            }
            
            // جمع بيانات النموذج
            const categoryData = {
                id: $('#edit_category_id').val(),
                name: $('#edit_category_name').val(),
                description: $('#edit_category_description').val()
            };
            
            // إرسال البيانات باستخدام AJAX
            $.ajax({
                url: '<?php echo API_URL; ?>services.php?action=categories&category_action=update',
                type: 'POST',
                data: categoryData,
                dataType: 'json',
                beforeSend: function() {
                    // تعطيل زر التحديث وإظهار مؤشر التحميل
                    $('#btn-update-category').prop('disabled', true).html('<span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span> جاري التحديث...');
                },
                success: function(response) {
                    // إعادة تفعيل زر التحديث
                    $('#btn-update-category').prop('disabled', false).html('حفظ التغييرات');
                    
                    if (response.status === 'success') {
                        // إغلاق الموديل
                        $('#editCategoryModal').modal('hide');
                        
                        // عرض رسالة النجاح
                        showToast('نجاح', response.message, 'success');
                        
                        // إعادة تحميل الفئات
                        loadCategories();
                    } else {
                        // عرض رسالة الخطأ
                        showToast('خطأ', response.message, 'error');
                    }
                },
                error: function(xhr, status, error) {
                    // إعادة تفعيل زر التحديث
                    $('#btn-update-category').prop('disabled', false).html('حفظ التغييرات');
                    
                    let errorMessage = 'حدث خطأ أثناء تحديث الفئة';
                    try {
                        const response = JSON.parse(xhr.responseText);
                        if (response.message) {
                            errorMessage = response.message;
                        }
                    } catch (e) {
                        console.error(e);
                    }
                    
                    showToast('خطأ', errorMessage, 'error');
                }
            });
        });
        
        // تهيئة موديل حذف الفئة
        $(document).on('click', '.delete-category', function() {
            const categoryId = $(this).data('id');
            const categoryName = $(this).data('name');
            
            $('#delete-category-name').text(categoryName);
            $('#confirm-delete').data('id', categoryId);
            
            $('#deleteCategoryModal').modal('show');
        });
        
        // تأكيد حذف الفئة
        $('#confirm-delete').on('click', function() {
            const categoryId = $(this).data('id');
            
            $.ajax({
                url: '<?php echo API_URL; ?>services.php?action=categories&category_action=delete',
                type: 'POST',
                data: { id: categoryId },
                dataType: 'json',
                beforeSend: function() {
                    // تعطيل زر الحذف وإظهار مؤشر التحميل
                    $('#confirm-delete').prop('disabled', true).html('<span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span> جاري الحذف...');
                },
                success: function(response) {
                    // إعادة تفعيل زر الحذف
                    $('#confirm-delete').prop('disabled', false).html('تأكيد الحذف');
                    
                    // إغلاق الموديل
                    $('#deleteCategoryModal').modal('hide');
                    
                    if (response.status === 'success') {
                        // عرض رسالة النجاح
                        showToast('نجاح', response.message, 'success');
                        
                        // إعادة تحميل الفئات
                        loadCategories();
                    } else {
                        // عرض رسالة الخطأ
                        showToast('خطأ', response.message, 'error');
                    }
                },
                error: function(xhr, status, error) {
                    // إعادة تفعيل زر الحذف
                    $('#confirm-delete').prop('disabled', false).html('تأكيد الحذف');
                    
                    // إغلاق الموديل
                    $('#deleteCategoryModal').modal('hide');
                    
                    let errorMessage = 'حدث خطأ أثناء حذف الفئة';
                    try {
                        const response = JSON.parse(xhr.responseText);
                        if (response.message) {
                            errorMessage = response.message;
                        }
                    } catch (e) {
                        console.error(e);
                    }
                    
                    showToast('خطأ', errorMessage, 'error');
                }
            });
        });
        
        // عرض رسالة توست باستخدام Bootstrap
        function showToast(title, message, type) {
            // تحديد لون الخلفية حسب النوع
            let bgClass = 'bg-info';
            if (type === 'success') bgClass = 'bg-success';
            if (type === 'error') bgClass = 'bg-danger';
            if (type === 'warning') bgClass = 'bg-warning';
            
            // إنشاء حاوية للتنبيهات إذا لم تكن موجودة
            if (!document.getElementById('toastContainer')) {
                const toastContainer = document.createElement('div');
                toastContainer.id = 'toastContainer';
                toastContainer.className = 'toast-container position-fixed top-0 start-0 p-3';
                toastContainer.style.zIndex = '1050';
                document.body.appendChild(toastContainer);
            }
            
            // إنشاء معرف فريد للتنبيه
            const toastId = 'toast-' + new Date().getTime();
            
            // إنشاء عنصر التنبيه
            const toastHtml = `
                <div id="${toastId}" class="toast ${bgClass} text-white" role="alert" aria-live="assertive" aria-atomic="true">
                    <div class="toast-header">
                        <strong class="me-auto">${title}</strong>
                        <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"></button>
                    </div>
                    <div class="toast-body">
                        ${message}
                    </div>
                </div>
            `;
            
            // إضافة التنبيه إلى الحاوية
            document.getElementById('toastContainer').innerHTML += toastHtml;
            
            // تهيئة وعرض التنبيه
            const toastElement = document.getElementById(toastId);
            const toast = new bootstrap.Toast(toastElement, { delay: 3000 });
            toast.show();
            
            // إزالة التنبيه من DOM بعد إخفائه
            toastElement.addEventListener('hidden.bs.toast', function () {
                toastElement.remove();
            });
        }
    };
</script>

<?php
// استدعاء قالب الذيل
include_once '../../includes/templates/footer.php';
?>
