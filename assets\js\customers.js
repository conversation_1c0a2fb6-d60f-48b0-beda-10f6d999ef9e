/**
 * Salon Management System - Customers Management
 * Handles customer-related operations using API endpoints
 */
(function($) {
    'use strict';

    const CustomersModule = {
        // Configuration settings
        config: {
            currentPage: 1,
            itemsPerPage: 10,
            selectedCustomerId: null
        },

        // Initialize customers module
        init: function() {
            this.bindEvents();
            this.loadCustomers();
        },

        // Bind DOM events
        bindEvents: function() {
            // Add customer button
            $(document).on('click', '#add-customer-btn', this.openAddCustomerModal.bind(this));

            // Edit customer button
            $(document).on('click', '.edit-customer-btn', this.openEditCustomerModal.bind(this));

            // Delete customer button
            $(document).on('click', '.delete-customer-btn', this.handleCustomerDeletion.bind(this));

            // Customer form submission
            $(document).on('submit', '#customer-form', this.handleCustomerFormSubmission.bind(this));

            // Phone number validation
            $(document).on('blur', '#customer-phone', this.validatePhoneNumber.bind(this));

            // Pagination buttons
            $(document).on('click', '.pagination-btn', this.handlePagination.bind(this));

            // Customer search
            $(document).on('input', '#customer-search', this.handleCustomerSearch.bind(this));

            // View customer details
            $(document).on('click', '.view-customer-btn', this.viewCustomerDetails.bind(this));

            // Add customer visit
            $(document).on('click', '#add-visit-btn', this.openAddVisitModal.bind(this));

            // Customer visit form submission
            $(document).on('submit', '#customer-visit-form', this.handleCustomerVisitSubmission.bind(this));

            // Add loyalty points
            $(document).on('click', '#add-loyalty-points-btn', this.openAddLoyaltyPointsModal.bind(this));

            // Loyalty points form submission
            $(document).on('submit', '#loyalty-points-form', this.handleLoyaltyPointsSubmission.bind(this));
        },

        // Load customers list
        loadCustomers: function(filters = {}) {
            const params = {
                action: 'get_customers',
                ...filters,
                limit: this.config.itemsPerPage,
                offset: (this.config.currentPage - 1) * this.config.itemsPerPage
            };

            AjaxHandler.post('customers', params, {
                success: this.renderCustomersList.bind(this)
            });
        },

        // Render customers list
        renderCustomersList: function(response) {
            const $customerTableBody = $('#customers-table-body');
            $customerTableBody.empty();

            if (response.status === 'success' && response.customers.length > 0) {
                response.customers.forEach(customer => {
                    const row = `
                        <tr data-customer-id="${customer.id}">
                            <td>${customer.name}</td>
                            <td>${customer.phone}</td>
                            <td>${customer.email || 'غير محدد'}</td>
                            <td>${customer.loyalty_points || 0}</td>
                            <td>
                                <div class="btn-group">
                                    <button class="btn btn-sm btn-info view-customer-btn" 
                                            data-id="${customer.id}">
                                        <i class="fa fa-eye"></i>
                                    </button>
                                    <button class="btn btn-sm btn-warning edit-customer-btn" 
                                            data-id="${customer.id}">
                                        <i class="fa fa-edit"></i>
                                    </button>
                                    <button class="btn btn-sm btn-danger delete-customer-btn" 
                                            data-id="${customer.id}">
                                        <i class="fa fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                    `;
                    $customerTableBody.append(row);
                });

                // Render pagination
                this.renderPagination(response.total);
            } else {
                $customerTableBody.append(`
                    <tr>
                        <td colspan="5" class="text-center">لا توجد عملاء</td>
                    </tr>
                `);
            }
        },

        // Render pagination
        renderPagination: function(totalCustomers) {
            const $paginationContainer = $('#customers-pagination');
            $paginationContainer.empty();

            const totalPages = Math.ceil(totalCustomers / this.config.itemsPerPage);

            if (totalPages > 1) {
                // Previous button
                if (this.config.currentPage > 1) {
                    $paginationContainer.append(`
                        <button class="btn btn-sm btn-secondary pagination-btn" 
                                data-page="${this.config.currentPage - 1}">
                            السابق
                        </button>
                    `);
                }

                // Page numbers
                for (let i = 1; i <= totalPages; i++) {
                    $paginationContainer.append(`
                        <button class="btn btn-sm ${i === this.config.currentPage ? 'btn-primary' : 'btn-secondary'} pagination-btn" 
                                data-page="${i}">
                            ${i}
                        </button>
                    `);
                }

                // Next button
                if (this.config.currentPage < totalPages) {
                    $paginationContainer.append(`
                        <button class="btn btn-sm btn-secondary pagination-btn" 
                                data-page="${this.config.currentPage + 1}">
                            التالي
                        </button>
                    `);
                }
            }
        },

        // Handle pagination
        handlePagination: function(event) {
            const page = $(event.target).data('page');
            this.config.currentPage = page;
            this.loadCustomers();
        },

        // Validate phone number
        validatePhoneNumber: function() {
            const phone = $('#customer-phone').val().trim();
            const customerId = $('#customer-id').val();
            
            AjaxHandler.post('customers', {
                action: 'check_phone_exists',
                phone: phone,
                customer_id: customerId
            }, {
                success: (response) => {
                    if (response.exists) {
                        AjaxHandler.showAlert('رقم الهاتف مستخدم بالفعل', 'warning');
                        $('#customer-phone').addClass('is-invalid');
                    } else {
                        $('#customer-phone').removeClass('is-invalid');
                    }
                }
            });
        },

        // Open add customer modal
        openAddCustomerModal: function() {
            // Reset form
            $('#customer-form')[0].reset();
            $('#customer-id').val('');
            $('#customer-modal-title').text('إضافة عميل جديد');
            $('#customer-modal').modal('show');
        },

        // Open edit customer modal
        openEditCustomerModal: function(event) {
            const customerId = $(event.currentTarget).data('id');
            
            AjaxHandler.post('customers', {
                action: 'get_customer',
                customer_id: customerId
            }, {
                success: (response) => {
                    const customer = response.customer;
                    
                    $('#customer-id').val(customer.id);
                    $('#customer-name').val(customer.name);
                    $('#customer-phone').val(customer.phone);
                    $('#customer-email').val(customer.email);
                    $('#customer-birthday').val(customer.birthday);
                    $('#customer-notes').val(customer.notes);
                    
                    $('#customer-modal-title').text('تعديل بيانات العميل');
                    $('#customer-modal').modal('show');
                }
            });
        },

        // Handle customer form submission
        handleCustomerFormSubmission: function(event) {
            event.preventDefault();

            const customerId = $('#customer-id').val();
            const action = customerId ? 'update_customer' : 'add_customer';

            const customerData = {
                action: action,
                name: $('#customer-name').val().trim(),
                phone: $('#customer-phone').val().trim(),
                email: $('#customer-email').val().trim() || null,
                birthday: $('#customer-birthday').val() || null,
                notes: $('#customer-notes').val().trim() || null
            };

            if (customerId) {
                customerData.customer_id = customerId;
            }

            AjaxHandler.post('customers', customerData, {
                success: (response) => {
                    AjaxHandler.showAlert(response.message, 'success');
                    $('#customer-modal').modal('hide');
                    this.loadCustomers();
                }
            });
        },

        // Handle customer deletion
        handleCustomerDeletion: function(event) {
            const customerId = $(event.currentTarget).data('id');

            if (!confirm('هل أنت متأكد من حذف هذا العميل؟')) {
                return;
            }

            AjaxHandler.post('customers', {
                action: 'delete_customer',
                customer_id: customerId
            }, {
                success: (response) => {
                    AjaxHandler.showAlert(response.message, 'success');
                    this.loadCustomers();
                }
            });
        },

        // View customer details
        viewCustomerDetails: function(event) {
            const customerId = $(event.currentTarget).data('id');

            AjaxHandler.post('customers', {
                action: 'get_customer',
                customer_id: customerId,
                with_visits: true
            }, {
                success: (response) => {
                    const customer = response.customer;
                    
                    // Populate customer details modal
                    $('#customer-details-modal-body').html(`
                        <div class="row">
                            <div class="col-md-6">
                                <h5>معلومات العميل</h5>
                                <p><strong>الاسم:</strong> ${customer.name}</p>
                                <p><strong>رقم الهاتف:</strong> ${customer.phone}</p>
                                <p><strong>البريد الإلكتروني:</strong> ${customer.email || 'غير محدد'}</p>
                                <p><strong>تاريخ الميلاد:</strong> ${customer.birthday || 'غير محدد'}</p>
                                <p><strong>نقاط الولاء:</strong> ${customer.loyalty_points || 0}</p>
                            </div>
                            <div class="col-md-6">
                                <h5>آخر الزيارات</h5>
                                <ul class="list-group">
                                    ${(customer.visits || []).map(visit => `
                                        <li class="list-group-item">
                                            ${visit.visit_date} - ${visit.notes || 'لا توجد ملاحظات'}
                                        </li>
                                    `).join('')}
                                </ul>
                            </div>
                        </div>
                        <div class="mt-3">
                            <button id="add-visit-btn" data-id="${customer.id}" class="btn btn-primary ml-2">تسجيل زيارة</button>
                            <button id="add-loyalty-points-btn" data-id="${customer.id}" class="btn btn-success">إضافة نقاط ولاء</button>
                        </div>
                    `);
                    
                    $('#customer-details-modal').modal('show');
                }
            });
        },

        // Open add visit modal
        openAddVisitModal: function(event) {
            const customerId = $(event.currentTarget).data('id');
            this.config.selectedCustomerId = customerId;
            
            $('#customer-visit-modal').modal('show');
        },

        // Handle customer visit submission
        handleCustomerVisitSubmission: function(event) {
            event.preventDefault();

            const visitData = {
                action: 'add_visit',
                customer_id: this.config.selectedCustomerId,
                notes: $('#visit-notes').val().trim()
            };

            AjaxHandler.post('customers', visitData, {
                success: (response) => {
                    AjaxHandler.showAlert(response.message, 'success');
                    $('#customer-visit-modal').modal('hide');
                    // Optionally refresh customer details
                    this.viewCustomerDetails({ currentTarget: $(`[data-id="${this.config.selectedCustomerId}"]`) });
                }
            });
        },

        // Open add loyalty points modal
        openAddLoyaltyPointsModal: function(event) {
            const customerId = $(event.currentTarget).data('id');
            this.config.selectedCustomerId = customerId;
            
            $('#loyalty-points-modal').modal('show');
        },

        // Handle loyalty points submission
        handleLoyaltyPointsSubmission: function(event) {
            event.preventDefault();

            const pointsData = {
                action: 'add_loyalty_points',
                customer_id: this.config.selectedCustomerId,
                points: parseInt($('#loyalty-points').val(), 10)
            };

            AjaxHandler.post('customers.php', pointsData, {
                success: (response) => {
                    AjaxHandler.showAlert(response.message, 'success');
                    $('#loyalty-points-modal').modal('hide');
                    // Optionally refresh customer details
                    this.viewCustomerDetails({ currentTarget: $(`[data-id="${this.config.selectedCustomerId}"]`) });
                }
            });
        },

        // Handle customer search
        handleCustomerSearch: function() {
            const searchTerm = $('#customer-search').val().trim();
            
            if (searchTerm) {
                this.loadCustomers({ search: searchTerm });
            } else {
                this.loadCustomers();
            }
        }
    };

    // Document ready initialization
    $(document).ready(function() {
        CustomersModule.init();
    });

})(jQuery);