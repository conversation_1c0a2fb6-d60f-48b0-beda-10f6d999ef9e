<?php
// منع الوصول المباشر للملف
if (!defined('BASEPATH')) {
    exit('لا يمكن الوصول المباشر لهذا الملف');
}
?>
    </div> <!-- .content END -->

    <!-- jQuery -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
    <!-- jQuery UI -->
    <script src="https://code.jquery.com/ui/1.13.2/jquery-ui.min.js"></script>
    <!-- Bootstrap Bundle JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.2/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Toastr JS -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.js"></script>
    <!-- Hammer.js for Touch Gestures -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/hammer.js/2.0.8/hammer.min.js"></script>
    <!-- DataTables -->
    <script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap5.min.js"></script>
    <!-- Select2 -->
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
    <!-- Moment.js -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.4/moment.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.4/locale/ar.min.js"></script>
    <!-- DateRangePicker -->
    <script src="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.min.js"></script>
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- وظائف JavaScript مشتركة للنظام -->

    <script src="<?php echo BASE_URL; ?>assets/js/common.js"></script>
    <script src="<?php echo BASE_URL; ?>assets/js/notifications.js"></script>
    <script src="<?php echo BASE_URL; ?>assets/js/background-tasks.js"></script>
    <script src="<?php echo BASE_URL; ?>assets/js/sidebar-responsive.js"></script>
    <!-- Tutorial System -->
    <script src="<?php echo BASE_URL; ?>assets/js/tutorial.js"></script>
    <!-- Demo Mode System - يتم تحميله في جميع الصفحات لاعتراض العمليات -->
    <script src="<?php echo BASE_URL; ?>assets/js/demo-mode.js"></script>

    <?php
    // تضمين معالج الإشعارات إذا كان مفعلاً
    if (isLoggedIn()) {
        $settingsModel = new Settings($db);
        $allSettings = $settingsModel->getAllSettings();

        // استخراج إعدادات الإشعارات
        $notificationSettings = [];
        foreach ($allSettings as $key => $value) {
            if (strpos($key, 'notification_') === 0) {
                $shortKey = substr($key, strlen('notification_'));
                $notificationSettings[$shortKey] = $value;
            }
        }

        // التحقق من تفعيل تذكير المواعيد وطريقة الإرسال
        $reminderEnabled = isset($notificationSettings['appointment_reminder']) && $notificationSettings['appointment_reminder'] == '1';
        $reminderMethod = $notificationSettings['reminder_method'] ?? 'cron';

        if ($reminderEnabled && $reminderMethod == 'js') {
            // تضمين ملف JavaScript لمعالج الإشعارات
            echo '<script src="' . BASE_URL . 'assets/js/notification-processor.js"></script>';

            // تكوين معالج الإشعارات
            $interval = isset($notificationSettings['check_interval']) ? (int)$notificationSettings['check_interval'] * 60 * 1000 : 5 * 60 * 1000; // تحويل الدقائق إلى ملي ثانية
            $debug = isset($notificationSettings['debug_mode']) && $notificationSettings['debug_mode'] == '1';

            echo '<script id="notification-processor-config" type="application/json">';
            echo json_encode([
                'enabled' => true,
                'apiUrl' => BASE_URL . 'api/notifications.php',
                'interval' => $interval,
                'debug' => $debug
            ]);
            echo '</script>';
        }
    }
    ?>

    <script>
        $(document).ready(function() {
            // Toggle sidebar on mobile - تم نقله إلى ملف sidebar-responsive.js

            // Initialize Select2
            $('.select2').select2({
                dir: "rtl",
                language: "ar"
            });

            // Initialize DataTables with Arabic language
            $('.data-table').DataTable({
                language: {
                    url: 'https://cdn.datatables.net/plug-ins/1.11.5/i18n/ar.json'
                },
                "ordering": true,
                "responsive": true
            });

            // Initialize DateRangePicker with Arabic locale
            $('.date-picker').daterangepicker({
                singleDatePicker: true,
                showDropdowns: true,
                locale: {
                    format: 'YYYY/MM/DD',
                    applyLabel: 'تطبيق',
                    cancelLabel: 'إلغاء',
                    fromLabel: 'من',
                    toLabel: 'إلى',
                    customRangeLabel: 'مخصص',
                    daysOfWeek: ['أحد', 'إثنين', 'ثلاثاء', 'أربعاء', 'خميس', 'جمعة', 'سبت'],
                    monthNames: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو', 'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'],
                    firstDay: 0
                }
            });

            // Initialize jQuery UI Datepicker with Arabic locale
            $.datepicker.regional['ar'] = {
                closeText: 'إغلاق',
                prevText: '&#x3C;السابق',
                nextText: 'التالي&#x3E;',
                currentText: 'اليوم',
                monthNames: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو', 'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'],
                monthNamesShort: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو', 'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'],
                dayNames: ['الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت'],
                dayNamesShort: ['أحد', 'اثنين', 'ثلاثاء', 'أربعاء', 'خميس', 'جمعة', 'سبت'],
                dayNamesMin: ['ح', 'ن', 'ث', 'ر', 'خ', 'ج', 'س'],
                weekHeader: 'أسبوع',
                dateFormat: 'yy-mm-dd',
                firstDay: 6,
                isRTL: true,
                showMonthAfterYear: false,
                yearSuffix: ''
            };
            $.datepicker.setDefaults($.datepicker.regional['ar']);

            // Initialize datepicker for all elements with class 'datepicker'
            $('.datepicker').datepicker({
                dateFormat: 'yy-mm-dd',
                changeMonth: true,
                changeYear: true
            });

            // Auto-hide alerts after 5 seconds
            setTimeout(function() {
                $('.alert-dismissible').fadeOut('slow');
            }, 5000);

            // AJAX setup to handle CSRF token
            $.ajaxSetup({
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                }
            });
        });

        // Function to show alerts
        function showAlert(message, type = 'success') {
            const alertHTML = `
                <div class="alert alert-${type} alert-dismissible fade show" role="alert">
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            `;

            $('#alerts-container').html(alertHTML);

            // Auto-hide after 5 seconds
            setTimeout(function() {
                $('.alert-dismissible').fadeOut('slow');
            }, 5000);
        }

        // Function for confirmation dialogs
        function confirmAction(message, callback) {
            if (confirm(message)) {
                callback();
            }
        }
    </script>
</body>
</html>