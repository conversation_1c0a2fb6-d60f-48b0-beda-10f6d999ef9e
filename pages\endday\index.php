<?php
/**
 * صفحة إدارة نهاية اليوم
 */

// تعريف ثابت لمنع الوصول المباشر للملفات المرتبطة
const BASEPATH = true;

// استدعاء ملفات التهيئة
require_once '../../config/init.php';

// التحقق من تسجيل الدخول
requireLogin();

// التحقق من الصلاحيات
requirePermission('endday_manage');

// عنوان الصفحة
$pageTitle = 'إدارة نهاية اليوم';

// إضافة أنماط CSS مخصصة
$customStyles = <<<CSS
<style>
    .table-responsive {
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
    }

    .table-bordered {
        border-collapse: collapse;
        width: 100%;
    }

    .table-bordered th,
    .table-bordered td {
        border: 1px solid #dee2e6;
        padding: 8px;
        vertical-align: middle;
    }

    .table-bordered th {
        background-color: #f8f9fa;
        font-weight: bold;
        text-align: center;
    }

    @media screen and (max-width: 768px) {
        .table-responsive {
            width: 100%;
            margin-bottom: 15px;
            overflow-y: hidden;
            border: 1px solid #dee2e6;
        }

        .table-bordered {
            margin-bottom: 0;
        }
    }

    /* تنسيقات شاشة التحميل */
    .loading-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(247, 249, 252, 0.9);
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        z-index: 9999;
        transition: opacity 0.5s ease, visibility 0.5s ease;
    }

    .loading-overlay.hide {
        opacity: 0;
        visibility: hidden;
    }

    .loading-spinner {
        position: relative;
        width: 80px;
        height: 80px;
        margin-bottom: 20px;
    }

    .loading-spinner:before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        border-radius: 50%;
        border: 4px solid transparent;
        border-top-color: #3498db;
        border-bottom-color: #3498db;
        animation: spin 1.5s linear infinite;
    }

    .loading-spinner:after {
        content: "";
        position: absolute;
        top: 10px;
        left: 10px;
        right: 10px;
        bottom: 10px;
        border-radius: 50%;
        border: 4px solid transparent;
        border-left-color: #2980b9;
        border-right-color: #2980b9;
        animation: spin 1s linear infinite reverse;
    }

    .loading-logo {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        color: #3498db;
        font-size: 30px;
    }

    .loading-text {
        font-size: 18px;
        color: #333;
        font-weight: 500;
        text-align: center;
        margin-bottom: 5px;
    }

    .loading-brand {
        font-size: 22px;
        color: #3498db;
        font-weight: 700;
        text-align: center;
        letter-spacing: 1px;
        animation: pulse 1.5s infinite alternate;
    }

    @keyframes pulse {
        from {
            opacity: 0.8;
            transform: scale(0.98);
        }
        to {
            opacity: 1;
            transform: scale(1);
        }
    }

    @keyframes spin {
        0% {
            transform: rotate(0deg);
        }
        100% {
            transform: rotate(360deg);
        }
    }
</style>
CSS;

// إنشاء كائنات النماذج
$db = new Database();
$invoiceModel = new Invoice($db);
$expenseModel = new Expense($db);
$endDayModel = new EndDay($db);

// التحقق من صلاحيات المستخدم
$isAdmin = hasPermission('admin_access');

// الفرع الحالي
$branchId = $_SESSION['user_branch_id'] ?? 0;

// إذا كان المستخدم مديراً، يمكنه اختيار الفرع
$selectedBranchId = $branchId;
if ($isAdmin && isset($_GET['branch_id'])) {
    $selectedBranchId = intval($_GET['branch_id']);
}

// إذا لم يتم تحديد فرع وكان المستخدم مديراً، نختار أول فرع متاح
if ($isAdmin && (!$selectedBranchId || $selectedBranchId <= 0)) {
    // الحصول على أول فرع متاح
    $db->prepare("SELECT id FROM branches WHERE is_active = 1 ORDER BY id LIMIT 1");
    $firstBranch = $db->fetch();
    if ($firstBranch) {
        $selectedBranchId = $firstBranch['id'];
    }
}

// الحصول على قائمة الفروع إذا كان المستخدم مديراً
$branches = [];
if ($isAdmin) {
    $db->prepare("SELECT id, name FROM branches WHERE is_active = 1 ORDER BY name");
    $branches = $db->fetchAll();
}

// استعلام عن حالة نهاية اليوم الحالي
// استخدام استعلام مباشر بدلاً من الدالة غير الموجودة
$db->prepare("SELECT * FROM end_days
             WHERE branch_id = :branch_id
             AND closed_at IS NULL
             ORDER BY date DESC, id DESC
             LIMIT 1");
$db->bind(':branch_id', $selectedBranchId);
$currentEndDay = $db->fetch();

// إذا لم يكن هناك يوم مفتوح، نتحقق من آخر يوم مغلق
if (!$currentEndDay) {
    // نتحقق من آخر يوم مغلق للعرض في التاريخ
    $db->prepare("SELECT * FROM end_days
                 WHERE branch_id = :branch_id
                 AND closed_at IS NOT NULL
                 ORDER BY date DESC, id DESC
                 LIMIT 1");
    $db->bind(':branch_id', $selectedBranchId);
    $lastClosedDay = $db->fetch();

    // نتحقق إذا كان آخر يوم مغلق هو اليوم الحالي
    $isToday = false;
    if ($lastClosedDay) {
        $isToday = date('Y-m-d', strtotime($lastClosedDay['date'])) == date('Y-m-d');
    }
}

// ضبط معلمات البحث
$searchParams = [
    'branch_id' => $selectedBranchId,
    'page' => isset($_GET['page']) ? intval($_GET['page']) : 1,
    'limit' => 10, // عرض آخر 10 سجلات افتراضيا
    'is_cashier' => ($_SESSION['user_role'] === ROLE_CASHIER) // إضافة معلمة للتحقق من دور المستخدم
];

// تسجيل معلومات للتشخيص
error_log('EndDay Index - User role: ' . $_SESSION['user_role'] . ', isCashier: ' . ($searchParams['is_cashier'] ? 'true' : 'false'));

// إضافة فلاتر التاريخ إذا تم تحديدها
if (isset($_GET['start_date']) && isset($_GET['end_date'])) {
    $searchParams['start_date'] = $_GET['start_date'];
    $searchParams['end_date'] = $_GET['end_date'];
}

// حساب الإزاحة للصفحات
$searchParams['offset'] = ($searchParams['page'] - 1) * $searchParams['limit'];

// استعلام قائمة نهايات الأيام باستخدام النموذج
$endDays = $endDayModel->getEndDays($searchParams);

// حساب إجمالي عدد السجلات
$totalRecords = $endDayModel->countEndDays($searchParams);
$totalPages = ceil($totalRecords / $searchParams['limit']);

// استدعاء رأس الصفحة
include '../../includes/templates/header.php';

// إضافة الأنماط المخصصة
echo $customStyles;
?>

<!-- شاشة التحميل -->
<div class="loading-overlay hide">
    <div class="loading-spinner">
        <div class="loading-logo">
            <i class="fas fa-calendar-day"></i>
        </div>
    </div>
    <div class="loading-text">جاري معالجة الطلب...</div>
    <div class="loading-brand mt-3">نظام إدارة الصالون</div>
</div>

<!-- المحتوى الرئيسي للصفحة -->
<div class="container-fluid">
    <!-- حاوية لعرض التنبيهات -->
    <div id="alertContainer" class="mb-3"></div>

    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2 class="h3 mb-0 text-gray-800">إدارة نهاية اليوم</h2>

        <?php if ($isAdmin && !empty($branches)): ?>
        <!-- اختيار الفرع للمدير -->
        <div class="d-flex align-items-center">
            <form id="branchSelectorForm" class="d-flex align-items-center">
                <label for="branch_selector" class="me-2">الفرع:</label>
                <select id="branch_selector" name="branch_id" class="form-select form-select-sm" style="width: 200px;">
                    <?php foreach ($branches as $branch): ?>
                    <option value="<?php echo $branch['id']; ?>" <?php echo ($selectedBranchId == $branch['id']) ? 'selected' : ''; ?>>
                        <?php echo htmlspecialchars($branch['name']); ?>
                    </option>
                    <?php endforeach; ?>
                </select>
                <button type="submit" class="btn btn-sm btn-primary ms-2">
                    <i class="fas fa-filter"></i> عرض
                </button>
            </form>
        </div>
        <?php endif; ?>
    </div>

    <?php if (!$selectedBranchId || $selectedBranchId <= 0): ?>
    <!-- عرض رسالة إذا لم يتم تحديد فرع -->
    <div class="alert alert-warning">
        <i class="fas fa-exclamation-triangle me-2"></i>
        لم يتم تحديد فرع. يرجى التواصل مع مدير النظام لتعيينك لفرع أو إنشاء فرع جديد.
    </div>
    <?php else: ?>

    <!-- بطاقة حالة نهاية اليوم الحالي -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-primary text-white py-3">
                    <h5 class="mb-0">حالة نهاية اليوم الحالي</h5>
                </div>
                <div class="card-body">
                    <?php if (!$currentEndDay): ?>
                        <?php if (isset($isToday) && $isToday): ?>
                            <div class="alert alert-info mb-3">
                                <i class="fas fa-info-circle me-2"></i>
                                تم إغلاق يوم العمل اليوم بالفعل في <?php echo date('h:i A', strtotime($lastClosedDay['closed_at'])); ?>
                            </div>
                        <?php else: ?>
                            <div class="alert alert-warning mb-0">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                لم يتم بدء نهاية اليوم بعد!
                            </div>
                            <div class="mt-3">
                                <div class="row">
                                    <div class="col-md-4 mb-2">
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="fas fa-calendar"></i></span>
                                            <input type="text" class="form-control datepicker" id="workdayDate" placeholder="تاريخ يوم العمل" value="<?php echo date('Y-m-d'); ?>">
                                        </div>
                                        <small class="form-text text-muted">يمكنك تحديد تاريخ مختلف ليوم العمل</small>
                                    </div>
                                    <div class="col-md-4">
                                        <button type="button" class="btn btn-primary" id="startEndDayBtn">
                                            <i class="fas fa-play me-1"></i> بدء نهاية اليوم
                                        </button>
                                    </div>
                                </div>
                            </div>
                        <?php endif; ?>
                    <?php else: ?>
                        <!-- نهاية اليوم مفتوحة -->
                        <div class="row">
                            <div class="col-md-8">
                                <div class="alert alert-info mb-3">
                                    <i class="fas fa-info-circle me-2"></i>
                                    يوم العمل مفتوح حال- تم البدء: <?php echo date('h:i A', strtotime($currentEndDay['created_at'])); ?>
                                </div>

                                <div class="row">
                                    <div class="col-md-4 mb-3">
                                        <div class="card bg-light">
                                            <div class="card-body text-center">
                                                <h6 class="text-muted mb-2">إجمالي المبيعات</h6>
                                                <h4 class="mb-0"><?php echo number_format($currentEndDay['total_sales'], 2); ?></h4>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-4 mb-3">
                                        <div class="card bg-light">
                                            <div class="card-body text-center">
                                                <h6 class="text-muted mb-2">مدفوعات نقدية</h6>
                                                <h4 class="mb-0"><?php echo number_format($currentEndDay['cash_amount'], 2); ?></h4>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-4 mb-3">
                                        <div class="card bg-light">
                                            <div class="card-body text-center">
                                                <h6 class="text-muted mb-2">مدفوعات بطاقة</h6>
                                                <h4 class="mb-0"><?php echo number_format($currentEndDay['card_amount'], 2); ?></h4>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="card h-100 border-0 bg-light">
                                    <div class="card-body">
                                        <h5 class="card-title">إغلاق نهاية اليوم</h5>
                                        <p class="text-muted">أدخل أي ملاحظات قبل إغلاق يوم العمل.</p>
                                        <form id="closeEndDayForm">
                                            <input type="hidden" name="end_day_id" value="<?php echo $currentEndDay['id']; ?>">
                                            <div class="mb-3">
                                                <textarea class="form-control" name="notes" rows="3" placeholder="ملاحظات (اختياري)"></textarea>
                                            </div>
                                            <div class="mb-3 form-check">
                                                <input type="checkbox" class="form-check-input" id="autoBackup" name="auto_backup" checked>
                                                <label class="form-check-label" for="autoBackup">إنشاء نسخة احتياطية تلقائية من قاعدة البيانات</label>
                                            </div>
                                            <button type="submit" class="btn btn-success w-100">
                                                <i class="fas fa-check-circle me-1"></i> إغلاق نهاية اليوم
                                            </button>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- بطاقة سجلات نهاية اليوم السابقة -->
    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-transparent py-3">
                    <h5 class="mb-0">
                        <?php if ($_SESSION['user_role'] === ROLE_CASHIER): ?>
                            سجلات نهاية اليوم المفتوحة
                        <?php else: ?>
                            سجلات نهاية اليوم السابقة
                        <?php endif; ?>
                    </h5>
                </div>
                <div class="card-body">
                    <!-- نموذج البحث -->
                    <form method="get" class="mb-4">
                        <div class="row g-2">
                            <?php if ($isAdmin): ?>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="branch_id">الفرع:</label>
                                    <select id="branch_id" name="branch_id" class="form-select">
                                        <?php foreach ($branches as $branch): ?>
                                        <option value="<?php echo $branch['id']; ?>" <?php echo ($selectedBranchId == $branch['id']) ? 'selected' : ''; ?>>
                                            <?php echo htmlspecialchars($branch['name']); ?>
                                        </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                            </div>
                            <?php endif; ?>
                            <div class="col-md-<?php echo $isAdmin ? '3' : '4'; ?>">
                                <div class="form-group">
                                    <label for="start_date">من تاريخ: (اختياري)</label>
                                    <input type="date" id="start_date" name="start_date" class="form-control"
                                           value="<?php echo $searchParams['start_date'] ?? ''; ?>">
                                </div>
                            </div>
                            <div class="col-md-<?php echo $isAdmin ? '3' : '4'; ?>">
                                <div class="form-group">
                                    <label for="end_date">إلى تاريخ: (اختياري)</label>
                                    <input type="date" id="end_date" name="end_date" class="form-control"
                                           value="<?php echo $searchParams['end_date'] ?? ''; ?>">
                                </div>
                            </div>
                            <div class="col-md-<?php echo $isAdmin ? '3' : '4'; ?>">
                                <div class="form-group d-flex align-items-end h-100">
                                    <button type="submit" class="btn btn-primary w-100">
                                        <i class="fas fa-search me-1"></i> بحث متقدم
                                    </button>
                                    <a href="index.php<?php echo $isAdmin && $selectedBranchId ? "?branch_id=$selectedBranchId" : ''; ?>" class="btn btn-outline-secondary w-100 mt-2">
                                        <i class="fas fa-redo me-1"></i> عرض الكل
                                    </a>
                                </div>
                            </div>
                        </div>
                    </form>

                    <!-- جدول النتائج -->
                    <h4 class="mt-4 mb-2">
                        <?php if (isset($searchParams['start_date']) && isset($searchParams['end_date'])): ?>
                            نتائج البحث من <?php echo $searchParams['start_date']; ?> إلى <?php echo $searchParams['end_date']; ?>
                        <?php elseif ($_SESSION['user_role'] === ROLE_CASHIER): ?>
                            سجلات نهاية اليوم المفتوحة أو اليوم الحالي
                        <?php else: ?>
                            سجلات نهاية اليوم السابقة (آخر <?php echo $searchParams['limit']; ?> سجلات)
                        <?php endif; ?>
                    </h4>
                    <div class="table-responsive" style="overflow-x: auto; width: 100%;">
                        <table class="table table-hover table-bordered">
                            <thead class="table-light">
                                <tr>
                                    <th style="min-width: 100px;">التاريخ</th>
                                    <th style="min-width: 120px;">إجمالي المبيعات</th>
                                    <th style="min-width: 120px;">المصروفات</th>
                                    <th style="min-width: 120px;">الخصومات</th>
                                    <th style="min-width: 100px;">نقدي</th>
                                    <th style="min-width: 100px;">بطاقة</th>
                                    <th style="min-width: 100px;">أخرى</th>
                                    <th style="min-width: 150px;">تاريخ الإغلاق</th>
                                    <th style="min-width: 80px;">الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (empty($endDays)): ?>
                                    <tr>
                                        <td colspan="8" class="text-center py-4">
                                            <i class="fas fa-info-circle mb-2 text-muted fa-2x"></i>
                                            <p class="mb-0 text-muted">لا توجد سجلات لعرضها</p>
                                        </td>
                                    </tr>
                                <?php else: ?>
                                    <?php foreach ($endDays as $endDay): ?>
                                        <tr>
                                            <td style="text-align: center;"><?php echo date('Y-m-d', strtotime($endDay['date'])); ?></td>
                                            <td style="text-align: center;"><?php echo number_format($endDay['total_sales'], 2); ?></td>
                                            <td style="text-align: center;"><?php echo number_format($endDay['total_expenses'], 2); ?></td>
                                            <td style="text-align: center;"><?php echo number_format($endDay['total_discounts'] ?? 0, 2); ?></td>
                                            <td style="text-align: center;"><?php echo number_format($endDay['cash_amount'], 2); ?></td>
                                            <td style="text-align: center;"><?php echo number_format($endDay['card_amount'], 2); ?></td>
                                            <td style="text-align: center;"><?php echo number_format($endDay['other_amount'], 2); ?></td>
                                            <td style="text-align: center;">
                                                <?php if (!empty($endDay['closed_at'])): ?>
                                                    <?php echo date('Y-m-d h:i A', strtotime($endDay['closed_at'])); ?>
                                                <?php else: ?>
                                                    <span class="badge bg-warning">مفتوح</span>
                                                <?php endif; ?>
                                            </td>
                                            <td style="text-align: center;">
                                                <div class="btn-group">
                                                    <a href="view.php?id=<?php echo $endDay['id']; ?>" class="btn btn-sm btn-primary" title="عرض التفاصيل">
                                                        <i class="fas fa-eye"></i>
                                                    </a>

                                                    <?php if (!empty($endDay['closed_at'])): ?>
                                                        <!-- زر إعادة فتح يوم العمل -->
                                                        <button type="button" class="btn btn-sm btn-success reopen-endday-btn"
                                                                data-id="<?php echo $endDay['id']; ?>"
                                                                title="إعادة فتح يوم العمل">
                                                            <i class="fas fa-unlock"></i>
                                                        </button>
                                                    <?php endif; ?>

                                                    <?php if ($isAdmin): ?>
                                                        <!-- زر حذف يوم العمل (للمدير فقط) -->
                                                        <button type="button" class="btn btn-sm btn-danger delete-endday-btn"
                                                                data-id="<?php echo $endDay['id']; ?>"
                                                                data-date="<?php echo date('Y-m-d', strtotime($endDay['date'])); ?>"
                                                                title="حذف يوم العمل">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    <?php endif; ?>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>

                    <!-- الصفحات -->
                    <?php if ($totalPages > 1): ?>
                        <nav aria-label="Page navigation" class="mt-4">
                            <ul class="pagination justify-content-center">
                                <?php if ($searchParams['page'] > 1): ?>
                                    <li class="page-item">
                                        <a class="page-link" href="?page=1<?php echo isset($searchParams['start_date']) ? '&start_date=' . $searchParams['start_date'] . '&end_date=' . $searchParams['end_date'] : ''; ?><?php echo $isAdmin ? '&branch_id=' . $selectedBranchId : ''; ?>">
                                            <i class="fas fa-angle-double-right"></i>
                                        </a>
                                    </li>
                                    <li class="page-item">
                                        <a class="page-link" href="?page=<?php echo $searchParams['page'] - 1; ?><?php echo isset($searchParams['start_date']) ? '&start_date=' . $searchParams['start_date'] . '&end_date=' . $searchParams['end_date'] : ''; ?><?php echo $isAdmin ? '&branch_id=' . $selectedBranchId : ''; ?>">
                                            <i class="fas fa-angle-right"></i>
                                        </a>
                                    </li>
                                <?php endif; ?>

                                <?php
                                $startPage = max(1, $searchParams['page'] - 2);
                                $endPage = min($totalPages, $searchParams['page'] + 2);

                                for ($i = $startPage; $i <= $endPage; $i++):
                                ?>
                                    <li class="page-item <?php echo ($i == $searchParams['page']) ? 'active' : ''; ?>">
                                        <a class="page-link" href="?page=<?php echo $i; ?><?php echo isset($searchParams['start_date']) ? '&start_date=' . $searchParams['start_date'] . '&end_date=' . $searchParams['end_date'] : ''; ?><?php echo $isAdmin ? '&branch_id=' . $selectedBranchId : ''; ?>">
                                            <?php echo $i; ?>
                                        </a>
                                    </li>
                                <?php endfor; ?>

                                <?php if ($searchParams['page'] < $totalPages): ?>
                                    <li class="page-item">
                                        <a class="page-link" href="?page=<?php echo $searchParams['page'] + 1; ?><?php echo isset($searchParams['start_date']) ? '&start_date=' . $searchParams['start_date'] . '&end_date=' . $searchParams['end_date'] : ''; ?><?php echo $isAdmin ? '&branch_id=' . $selectedBranchId : ''; ?>">
                                            <i class="fas fa-angle-left"></i>
                                        </a>
                                    </li>
                                    <li class="page-item">
                                        <a class="page-link" href="?page=<?php echo $totalPages; ?><?php echo isset($searchParams['start_date']) ? '&start_date=' . $searchParams['start_date'] . '&end_date=' . $searchParams['end_date'] : ''; ?><?php echo $isAdmin ? '&branch_id=' . $selectedBranchId : ''; ?>">
                                            <i class="fas fa-angle-double-left"></i>
                                        </a>
                                    </li>
                                <?php endif; ?>
                            </ul>
                        </nav>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>
</div>

<!-- استدعاء قالب الفوتر -->
<?php include '../../includes/templates/footer.php'; ?>

<!-- إضافة مكتبة إشعارات WhatsApp لنهاية اليوم -->
<script src="<?php echo BASE_URL; ?>assets/js/whatsapp-endday-notification.js"></script>

<!-- JavaScript -->
<script>
const SYSTEM_BASE_URL = '<?php echo BASE_URL; ?>';

// دالة مساعدة لعرض التنبيهات
function showAlert(type, message) {
    const alertDiv = $('<div class="alert alert-' + type + ' alert-dismissible fade show" role="alert">' +
        message +
        '<button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>' +
        '</div>');

    $('#alertContainer').append(alertDiv);

    // إزالة التنبيه بعد 5 ثواني
    setTimeout(function() {
        alertDiv.alert('close');
    }, 5000);
}

$(document).ready(function() {
    // منتقي التاريخ تم تهيئته في قالب الفوتر

    // بدء نهاية اليوم
    $('#startEndDayBtn').on('click', function() {
        // الحصول على التاريخ المحدد
        const workdayDate = $('#workdayDate').val();

        if (confirm('هل أنت متأكد من بدء نهاية اليوم؟')) {
            // إظهار شاشة التحميل
            $('.loading-overlay').removeClass('hide');
            $('.loading-text').text('جاري بدء يوم العمل...');

            $.ajax({
                url: '../../api/endday.php?action=start',
                type: 'POST',
                data: {
                    branch_id: <?php echo $selectedBranchId; ?>,
                    date: workdayDate
                },
                dataType: 'json',
                success: function(response) {
                    if (response.status === 'success') {
                        showAlert('success', response.message);

                        // تحديث نص التحميل
                        $('.loading-text').text('جاري إرسال إشعارات WhatsApp...');

                        // إرسال إشعار WhatsApp للمدراء عن فتح يوم العمل
                        if (typeof WhatsAppEndDayNotification !== 'undefined') {
                            // التحقق من وجود أرقام هواتف للمدراء قبل محاولة الإرسال
                            WhatsAppEndDayNotification.checkAdminPhones()
                                .then(function(hasAdminPhones) {
                                    if (!hasAdminPhones) {
                                        console.warn('لا توجد أرقام هواتف متاحة للمدراء. لا يمكن إرسال الإشعارات.');
                                        // إخفاء شاشة التحميل وإعادة تحميل الصفحة
                                        setTimeout(function() {
                                            $('.loading-overlay').addClass('hide');
                                            location.reload();
                                        }, 500);
                                        return;
                                    }

                                    // إرسال الإشعار
                                    WhatsAppEndDayNotification.sendOpenDayNotification(response.end_day_id)
                                        .then(function(result) {
                                            console.log('تم إرسال إشعار فتح يوم العمل بنجاح', result);
                                            // تحديث نص التحميل
                                            $('.loading-text').text('تم إرسال الإشعارات بنجاح');
                                            // إخفاء شاشة التحميل وإعادة تحميل الصفحة
                                            setTimeout(function() {
                                                $('.loading-overlay').addClass('hide');
                                                location.reload();
                                            }, 1000);
                                        })
                                        .catch(function(error) {
                                            console.error('خطأ في إرسال إشعار فتح يوم العمل:', error);
                                            // تحديث نص التحميل
                                            $('.loading-text').text('تم بدء يوم العمل ولكن حدث خطأ في إرسال الإشعارات');
                                            // إخفاء شاشة التحميل وإعادة تحميل الصفحة
                                            setTimeout(function() {
                                                $('.loading-overlay').addClass('hide');
                                                location.reload();
                                            }, 1500);
                                        });
                                })
                                .catch(function() {
                                    // إخفاء شاشة التحميل وإعادة تحميل الصفحة
                                    setTimeout(function() {
                                        $('.loading-overlay').addClass('hide');
                                        location.reload();
                                    }, 1000);
                                });
                        } else {
                            // إذا لم تكن مكتبة WhatsApp متاحة، إخفاء شاشة التحميل وإعادة تحميل الصفحة
                            setTimeout(function() {
                                $('.loading-overlay').addClass('hide');
                                location.reload();
                            }, 1000);
                        }
                    } else {
                        // إخفاء شاشة التحميل
                        $('.loading-overlay').addClass('hide');
                        showAlert('danger', response.message);
                    }
                },
                error: function(xhr) {
                    // إخفاء شاشة التحميل
                    $('.loading-overlay').addClass('hide');

                    let errorMessage = 'حدث خطأ أثناء معالجة الطلب';
                    try {
                        const response = JSON.parse(xhr.responseText);
                        if (response.message) {
                            errorMessage = response.message;
                        }
                    } catch (e) {}
                    showAlert('danger', errorMessage);
                }
            });
        }
    });

    // دالة إغلاق نهاية اليوم
    function closeEndDay(endDayId, notes, autoBackup, branchId) {
        return new Promise((resolve, reject) => {
            // إظهار شاشة التحميل
            $('.loading-overlay').removeClass('hide');
            $('.loading-text').text('جاري إغلاق يوم العمل...');

            $.ajax({
                url: '../../api/endday.php?action=close',
                type: 'POST',
                data: {
                    branch_id: branchId,
                    notes: notes,
                    auto_backup: autoBackup ? 'on' : 'off'
                },
                dataType: 'json',
                success: function(response) {
                    if (response.status === 'success') {
                        showAlert('success', response.message);

                        // إذا تم طلب نسخة احتياطية، نقوم بإنشائها
                        if (autoBackup) {
                            $('.loading-text').text('جاري إنشاء النسخة الاحتياطية...');
                            $.ajax({
                                url: '../../api/backup.php?action=create',
                                type: 'GET',
                                dataType: 'json',
                                success: function(backupResponse) {
                                    if (backupResponse.status === 'success') {
                                        $('.loading-text').text('جاري إرسال النسخة الاحتياطية إلى الخادم...');
                                        // الحصول على اسم الملف والرابط المباشر للتحميل من PHP
                                        const filename = backupResponse.backup_file.split('/').pop();
                                        const fileUrl = SYSTEM_BASE_URL + 'backups/' + filename;
                                        console.log('سيتم إرسال النسخة الاحتياطية إلى الخادم الخارجي...');
                                        fetch(`http://localhost:3001/download?url=${encodeURIComponent(fileUrl)}`)
                                            .then(response => response.json())
                                            .then(result => {
                                                console.log('نتيجة الخادم الخارجي:', result);
                                                if (result.status === 'success') {
                                                    $('.loading-text').text('تم حفظ النسخة الاحتياطية على الخادم الخارجي بنجاح');
                                                } else {
                                                    $('.loading-text').text('تم إنشاء النسخة الاحتياطية ولكن لم يتم حفظها على الخادم الخارجي');
                                                }
                                            })
                                            .catch((err) => {
                                                console.error('خطأ أثناء إرسال النسخة للخادم الخارجي:', err);
                                                $('.loading-text').text('تم إنشاء النسخة الاحتياطية ولكن حدث خطأ أثناء إرسالها للخادم الخارجي');
                                            });
                                    } else {
                                        $('.loading-text').text('تم إغلاق يوم العمل ولكن حدث خطأ في إنشاء النسخة الاحتياطية');
                                    }
                                },
                                error: function() {
                                    $('.loading-text').text('تم إغلاق يوم العمل ولكن حدث خطأ في إنشاء النسخة الاحتياطية');
                                }
                            });
                        }

                        // تحديث نص التحميل
                        $('.loading-text').text('جاري إرسال إشعارات WhatsApp...');

                        // إرسال إشعار WhatsApp للمدراء عن إغلاق يوم العمل
                        if (typeof WhatsAppEndDayNotification !== 'undefined') {
                            // التحقق من وجود أرقام هواتف للمدراء قبل محاولة الإرسال
                            WhatsAppEndDayNotification.checkAdminPhones()
                                .then(function(hasAdminPhones) {
                                    if (!hasAdminPhones) {
                                        console.warn('لا توجد أرقام هواتف متاحة للمدراء. لا يمكن إرسال الإشعارات.');
                                        // إخفاء شاشة التحميل وإعادة تحميل الصفحة
                                        setTimeout(function() {
                                            $('.loading-overlay').addClass('hide');
                                            location.reload();
                                        }, 500);
                                        resolve();
                                        return;
                                    }

                                    // إضافة تأخير قصير لضمان تحديث البيانات في قاعدة البيانات
                                    $('.loading-text').text('جاري تحديث البيانات...');

                                    // انتظار ثانية واحدة قبل إرسال الإشعار لضمان تحديث البيانات
                                    setTimeout(function() {
                                        // إرسال الإشعار
                                        $('.loading-text').text('جاري إرسال إشعارات WhatsApp...');

                                        WhatsAppEndDayNotification.sendCloseDayNotification(endDayId)
                                            .then(function(result) {
                                                console.log('تم إرسال إشعار إغلاق يوم العمل بنجاح', result);
                                                // تحديث نص التحميل
                                                $('.loading-text').text('تم إرسال الإشعارات بنجاح');
                                                // إخفاء شاشة التحميل وإعادة تحميل الصفحة
                                                setTimeout(function() {
                                                    $('.loading-overlay').addClass('hide');
                                                    location.reload();
                                                }, 1000);
                                                resolve();
                                            })
                                            .catch(function(error) {
                                                console.error('خطأ في إرسال إشعار إغلاق يوم العمل:', error);
                                                // تحديث نص التحميل
                                                $('.loading-text').text('تم إغلاق يوم العمل ولكن حدث خطأ في إرسال الإشعارات');
                                                // إخفاء شاشة التحميل وإعادة تحميل الصفحة
                                                setTimeout(function() {
                                                    $('.loading-overlay').addClass('hide');
                                                    location.reload();
                                                }, 1500);
                                                resolve();
                                            });
                                    }, 1000); // انتظار ثانية واحدة
                                })
                                .catch(function() {
                                    // إخفاء شاشة التحميل وإعادة تحميل الصفحة
                                    setTimeout(function() {
                                        $('.loading-overlay').addClass('hide');
                                        location.reload();
                                    }, 1000);
                                    resolve();
                                });
                        } else {
                            // إذا لم تكن مكتبة WhatsApp متاحة، إخفاء شاشة التحميل وإعادة تحميل الصفحة
                            setTimeout(function() {
                                $('.loading-overlay').addClass('hide');
                                location.reload();
                            }, 1000);
                            resolve();
                        }
                    } else {
                        // إخفاء شاشة التحميل
                        $('.loading-overlay').addClass('hide');
                        showAlert('danger', response.message);
                        reject(response.message);
                    }
                },
                error: function(xhr) {
                    // إخفاء شاشة التحميل
                    $('.loading-overlay').addClass('hide');

                    let errorMessage = 'حدث خطأ أثناء معالجة الطلب';
                    try {
                        const response = JSON.parse(xhr.responseText);
                        if (response.message) {
                            errorMessage = response.message;
                        }
                    } catch (e) {}
                    showAlert('danger', errorMessage);
                    reject(errorMessage);
                }
            });
        });
    }

    // معالجة نموذج إغلاق نهاية اليوم
    $('#closeEndDayForm').on('submit', function(e) {
        e.preventDefault();

        if (confirm('هل أنت متأكد من إغلاق نهاية اليوم؟')) {
            const formData = new FormData(this);
            const endDayId = formData.get('end_day_id');
            const notes = formData.get('notes');
            const autoBackup = $('#autoBackup').is(':checked'); // استخدام jQuery للتحقق من حالة الـ checkbox

            closeEndDay(endDayId, notes, autoBackup, <?php echo $selectedBranchId; ?>)
                .catch(error => {
                    console.error('خطأ في إغلاق نهاية اليوم:', error);
                });
        }
    });

    // معالجة تغيير الفرع للمدير
    $('#branchSelectorForm').on('submit', function(e) {
        e.preventDefault();
        const branchId = $('#branch_selector').val();
        window.location.href = `index.php?branch_id=${branchId}`;
    });

    // تفعيل التغيير المباشر عند تغيير الفرع
    $('#branch_selector').on('change', function() {
        $('#branchSelectorForm').submit();
    });

    // إعادة فتح يوم عمل مغلق
    $('.reopen-endday-btn').on('click', function() {
        const endDayId = $(this).data('id');

        if (confirm('هل أنت متأكد من إعادة فتح يوم العمل هذا؟')) {
            $.ajax({
                url: '../../api/endday.php?action=reopen',
                type: 'POST',
                data: {
                    end_day_id: endDayId
                },
                success: function(response) {
                    if (response.status === 'success') {
                        showAlert('success', 'تم إعادة فتح يوم العمل بنجاح');
                        setTimeout(function() {
                            location.reload();
                        }, 1500);
                    } else {
                        showAlert('danger', response.message || 'حدث خطأ أثناء إعادة فتح يوم العمل');
                    }
                },
                error: function(xhr) {
                    let errorMessage = 'حدث خطأ أثناء إعادة فتح يوم العمل';
                    try {
                        const response = JSON.parse(xhr.responseText);
                        if (response.message) {
                            errorMessage = response.message;
                        }
                    } catch (e) {}
                    showAlert('danger', errorMessage);
                }
            });
        }
    });

    // حذف يوم عمل
    $('.delete-endday-btn').on('click', function() {
        const endDayId = $(this).data('id');
        const endDayDate = $(this).data('date');

        if (confirm('هل أنت متأكد من حذف يوم العمل ' + endDayDate + '\nسيتم حذف جميع الفواتير والمصروفات المرتبطة بهذا اليوم!')) {
            $.ajax({
                url: '../../api/endday.php?action=delete',
                type: 'POST',
                data: {
                    end_day_id: endDayId
                },
                success: function(response) {
                    if (response.status === 'success') {
                        showAlert('success', 'تم حذف يوم العمل وجميع البيانات المرتبطة به بنجاح');
                        setTimeout(function() {
                            location.reload();
                        }, 1500);
                    } else {
                        showAlert('danger', response.message || 'حدث خطأ أثناء حذف يوم العمل');
                    }
                },
                error: function(xhr) {
                    let errorMessage = 'حدث خطأ أثناء حذف يوم العمل';
                    try {
                        const response = JSON.parse(xhr.responseText);
                        if (response.message) {
                            errorMessage = response.message;
                        }
                    } catch (e) {}
                    showAlert('danger', errorMessage);
                }
            });
        }
    });
});
</script>
