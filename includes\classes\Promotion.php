<?php
/**
 * نموذج العروض والخصومات
 *
 * يتعامل مع عمليات إدارة العروض والخصومات
 */

class Promotion {
    private $db;

    /**
     * إنشاء كائن جديد
     * @param Database $db كائن قاعدة البيانات
     */
    public function __construct($db) {
        $this->db = $db;
    }

    /**
     * الحصول على قائمة العروض والخصومات
     *
     * @param array $filters فلاتر البحث (اختياري)
     * @return array قائمة العروض والخصومات
     */
    public function getPromotions($filters = []) {
        try {
            $sql = "SELECT p.*, b.name as branch_name
                    FROM promotions p
                    LEFT JOIN branches b ON p.branch_id = b.id";

            $whereConditions = [];
            $bindings = [];

            // تطبيق الفلاتر
            if (!empty($filters['branch_id'])) {
                $whereConditions[] = "(p.branch_id = :branch_id OR p.branch_id IS NULL)";
                $bindings[':branch_id'] = $filters['branch_id'];
            }

            if (isset($filters['is_active'])) {
                $whereConditions[] = "p.is_active = :is_active";
                $bindings[':is_active'] = $filters['is_active'];
            }

            if (!empty($filters['search'])) {
                $whereConditions[] = "(p.name LIKE :search OR p.description LIKE :search)";
                $bindings[':search'] = '%' . $filters['search'] . '%';
            }

            // فلتر التاريخ الحالي (للعروض النشطة حاليًا)
            if (!empty($filters['current_date'])) {
                $whereConditions[] = "(p.start_date IS NULL OR p.start_date <= :start_date_param)";
                $whereConditions[] = "(p.end_date IS NULL OR p.end_date >= :end_date_param)";
                $bindings[':start_date_param'] = $filters['current_date'];
                $bindings[':end_date_param'] = $filters['current_date'];
            } else if (isset($filters['check_date'])) {
                // إضافة فلتر التاريخ الحالي تلقائيًا
                $currentDate = date('Y-m-d');
                $whereConditions[] = "(p.start_date IS NULL OR p.start_date <= :start_date_param)";
                $whereConditions[] = "(p.end_date IS NULL OR p.end_date >= :end_date_param)";
                $bindings[':start_date_param'] = $currentDate;
                $bindings[':end_date_param'] = $currentDate;
            }

            // إضافة شروط WHERE إذا وجدت
            if (!empty($whereConditions)) {
                $sql .= " WHERE " . implode(' AND ', $whereConditions);
            }

            // ترتيب النتائج
            $sql .= " ORDER BY p.is_active DESC, p.created_at DESC";

            // تطبيق الصفحات
            if (!empty($filters['limit'])) {
                $offset = !empty($filters['offset']) ? $filters['offset'] : 0;
                $sql .= " LIMIT :offset, :limit";
                $bindings[':offset'] = $offset;
                $bindings[':limit'] = $filters['limit'];
            }

            $this->db->prepare($sql);

            // ربط القيم
            foreach ($bindings as $param => $value) {
                $this->db->bind($param, $value);
            }

            return $this->db->fetchAll();
        } catch (Exception $e) {
            log_error('خطأ أثناء استرجاع العروض والخصومات: ' . $e->getMessage(), $e);
            throw $e;
        }
    }

    /**
     * الحصول على عرض محدد بواسطة المعرف
     *
     * @param int $id معرف العرض
     * @return array|false بيانات العرض أو false إذا لم يتم العثور عليه
     */
    public function getPromotionById($id) {
        try {
            $this->db->prepare("SELECT p.*, b.name as branch_name
                                FROM promotions p
                                LEFT JOIN branches b ON p.branch_id = b.id
                                WHERE p.id = :id");
            $this->db->bind(':id', $id);
            return $this->db->fetch();
        } catch (Exception $e) {
            log_error('خطأ أثناء استرجاع العرض: ' . $e->getMessage(), $e);
            throw $e;
        }
    }

    /**
     * إضافة عرض جديد
     *
     * @param array $data بيانات العرض
     * @return int|false معرف العرض الجديد أو false إذا فشلت العملية
     */
    public function addPromotion($data) {
        try {
            $this->db->prepare("INSERT INTO promotions (
                                name, description, condition_type, condition_value, condition_max_value,
                                discount_type, discount_value, start_date, end_date,
                                is_active, branch_id, specific_item_id
                            ) VALUES (
                                :name, :description, :condition_type, :condition_value, :condition_max_value,
                                :discount_type, :discount_value, :start_date, :end_date,
                                :is_active, :branch_id, :specific_item_id
                            )");

            $this->db->bind(':name', $data['name']);
            $this->db->bind(':description', $data['description']);
            $this->db->bind(':condition_type', $data['condition_type']);
            $this->db->bind(':condition_value', $data['condition_value']);
            $this->db->bind(':condition_max_value', !empty($data['condition_max_value']) ? $data['condition_max_value'] : null);
            $this->db->bind(':discount_type', $data['discount_type']);
            $this->db->bind(':discount_value', $data['discount_value']);
            $this->db->bind(':start_date', !empty($data['start_date']) ? $data['start_date'] : null);
            $this->db->bind(':end_date', !empty($data['end_date']) ? $data['end_date'] : null);
            $this->db->bind(':is_active', isset($data['is_active']) ? $data['is_active'] : 1);
            $this->db->bind(':branch_id', !empty($data['branch_id']) ? $data['branch_id'] : null);
            $this->db->bind(':specific_item_id', !empty($data['specific_item_id']) ? $data['specific_item_id'] : null);

            if ($this->db->execute()) {
                return $this->db->lastInsertId();
            }

            return false;
        } catch (Exception $e) {
            log_error('خطأ أثناء إضافة العرض: ' . $e->getMessage(), $e);
            throw $e;
        }
    }

    /**
     * تحديث عرض موجود
     *
     * @param int $id معرف العرض
     * @param array $data بيانات العرض المحدثة
     * @return bool نجاح أو فشل العملية
     */
    public function updatePromotion($id, $data) {
        try {
            $this->db->prepare("UPDATE promotions SET
                                name = :name,
                                description = :description,
                                condition_type = :condition_type,
                                condition_value = :condition_value,
                                condition_max_value = :condition_max_value,
                                discount_type = :discount_type,
                                discount_value = :discount_value,
                                start_date = :start_date,
                                end_date = :end_date,
                                is_active = :is_active,
                                branch_id = :branch_id,
                                specific_item_id = :specific_item_id
                            WHERE id = :id");

            $this->db->bind(':id', $id);
            $this->db->bind(':name', $data['name']);
            $this->db->bind(':description', $data['description']);
            $this->db->bind(':condition_type', $data['condition_type']);
            $this->db->bind(':condition_value', $data['condition_value']);
            $this->db->bind(':condition_max_value', !empty($data['condition_max_value']) ? $data['condition_max_value'] : null);
            $this->db->bind(':discount_type', $data['discount_type']);
            $this->db->bind(':discount_value', $data['discount_value']);
            $this->db->bind(':start_date', !empty($data['start_date']) ? $data['start_date'] : null);
            $this->db->bind(':end_date', !empty($data['end_date']) ? $data['end_date'] : null);
            $this->db->bind(':is_active', isset($data['is_active']) ? $data['is_active'] : 1);
            $this->db->bind(':branch_id', !empty($data['branch_id']) ? $data['branch_id'] : null);
            $this->db->bind(':specific_item_id', !empty($data['specific_item_id']) ? $data['specific_item_id'] : null);

            return $this->db->execute();
        } catch (Exception $e) {
            log_error('خطأ أثناء تحديث العرض: ' . $e->getMessage(), $e);
            throw $e;
        }
    }

    /**
     * حذف عرض
     *
     * @param int $id معرف العرض
     * @return bool نجاح أو فشل العملية
     */
    public function deletePromotion($id) {
        try {
            $this->db->prepare("DELETE FROM promotions WHERE id = :id");
            $this->db->bind(':id', $id);
            return $this->db->execute();
        } catch (Exception $e) {
            log_error('خطأ أثناء حذف العرض: ' . $e->getMessage(), $e);
            throw $e;
        }
    }

    /**
     * تغيير حالة العرض (نشط/غير نشط)
     *
     * @param int $id معرف العرض
     * @param bool $isActive الحالة الجديدة
     * @return bool نجاح أو فشل العملية
     */
    public function togglePromotionStatus($id, $isActive) {
        try {
            $this->db->prepare("UPDATE promotions SET is_active = :is_active WHERE id = :id");
            $this->db->bind(':id', $id);
            $this->db->bind(':is_active', $isActive ? 1 : 0);
            return $this->db->execute();
        } catch (Exception $e) {
            log_error('خطأ أثناء تغيير حالة العرض: ' . $e->getMessage(), $e);
            throw $e;
        }
    }

    /**
     * الحصول على العروض المتاحة للفاتورة
     *
     * @param float $totalAmount إجمالي قيمة الفاتورة
     * @param int $itemsCount عدد العناصر في الفاتورة
     * @param string $items عناصر الفاتورة (المنتجات والخدمات) - سلسلة JSON
     * @param int $branchId معرف الفرع
     * @return array قائمة العروض المتاحة
     */
    public function getAvailablePromotionsForInvoice($totalAmount, $itemsCount, $items, $branchId) {
        try {
            // تسجيل معلومات للتصحيح
            log_debug('getAvailablePromotionsForInvoice called with:', [
                'totalAmount' => $totalAmount,
                'itemsCount' => $itemsCount,
                'branchId' => $branchId,
                'items' => $items
            ]);

            // تحويل سلسلة JSON إلى مصفوفة إذا كانت سلسلة
            $itemsArray = is_string($items) ? json_decode($items, true) : $items;

            // الحصول على العروض النشطة
            $query = "SELECT * FROM promotions WHERE is_active = 1";

            // إضافة شرط التاريخ الحالي
            $currentDate = date('Y-m-d');
            $query .= " AND (start_date IS NULL OR start_date <= '$currentDate')";
            $query .= " AND (end_date IS NULL OR end_date >= '$currentDate')";

            // إضافة شرط الفرع إذا كان محددًا
            if ($branchId) {
                $query .= " AND (branch_id IS NULL OR branch_id = :branch_id)";
            }

            $this->db->prepare($query);

            if ($branchId) {
                $this->db->bind(':branch_id', $branchId);
            }

            $promotions = $this->db->fetchAll();
            log_debug('Found active promotions:', $promotions);

            $availablePromotions = [];

            foreach ($promotions as $promotion) {
                $isApplicable = false;

                switch ($promotion['condition_type']) {
                    case 'total_amount':
                        // التحقق من إجمالي قيمة الفاتورة
                        log_debug('Checking total_amount promotion', $promotion);
                        $minValue = floatval($promotion['condition_value']);
                        $isApplicable = $totalAmount >= $minValue;

                        // التحقق من الحد الأقصى إذا كان محددًا
                        if ($isApplicable && !empty($promotion['condition_max_value']) && $promotion['condition_max_value'] != 'NULL') {
                            $maxValue = floatval($promotion['condition_max_value']);
                            $isApplicable = $totalAmount <= $maxValue;
                            log_debug("Total amount max check: $totalAmount <= $maxValue = " . ($isApplicable ? 'true' : 'false'));
                        } else {
                            log_debug("Total amount check: $totalAmount >= $minValue = " . ($isApplicable ? 'true' : 'false'));
                        }
                        break;

                    case 'items_count':
                        // التحقق من عدد العناصر
                        $minCount = intval($promotion['condition_value']);
                        $isApplicable = $itemsCount >= $minCount;
                        log_debug("Items count check: $itemsCount >= $minCount = " . ($isApplicable ? 'true' : 'false'));
                        break;

                    case 'specific_product':
                    case 'specific_service':
                        // التحقق من وجود منتج أو خدمة محددة
                        if (!empty($promotion['specific_item_id'])) {
                            $specificItemId = $promotion['specific_item_id'];
                            $itemType = ($promotion['condition_type'] === 'specific_product') ? 'product' : 'service';

                            foreach ($itemsArray as $item) {
                                if ($item['item_id'] == $specificItemId && $item['item_type'] == $itemType) {
                                    $isApplicable = true;
                                    break;
                                }
                            }
                        }
                        break;
                }

                if ($isApplicable) {
                    // حساب قيمة الخصم
                    $discountAmount = 0;

                    if ($promotion['discount_type'] === 'percentage') {
                        $discountAmount = $totalAmount * ($promotion['discount_value'] / 100);
                    } else { // fixed
                        $discountAmount = $promotion['discount_value'];
                    }

                    // إضافة معلومات إضافية للعرض
                    $promotion['discount_amount'] = $discountAmount;
                    $promotion['final_amount'] = $totalAmount - $discountAmount;

                    $availablePromotions[] = $promotion;
                    log_debug('Added promotion to available promotions: ' . $promotion['name']);
                }
            }

            return $availablePromotions;
        } catch (Exception $e) {
            log_error('خطأ أثناء استرجاع العروض المتاحة للفاتورة: ' . $e->getMessage(), $e);
            return [];
        }
    }

    /**
     * تسجيل استخدام عرض ترويجي
     * @param int $promotionId معرف العرض الترويجي
     * @param int $invoiceId معرف الفاتورة
     * @param int $customerId معرف العميل (اختياري)
     * @param float $discountAmount قيمة الخصم المطبق
     * @return bool نجاح أو فشل العملية
     */
    public function recordPromotionUsage($promotionId, $invoiceId, $customerId, $discountAmount) {
        try {
            // التحقق مما إذا كانت هناك معاملة نشطة بالفعل
            $transactionStartedHere = false;
            if (!$this->db->inTransaction()) {
                $this->db->beginTransaction();
                $transactionStartedHere = true;
                error_log('Started new transaction in recordPromotionUsage');
            } else {
                error_log('Using existing transaction in recordPromotionUsage');
            }

            // تسجيل استخدام العرض
            $this->db->prepare("
                INSERT INTO promotion_usage (
                    promotion_id, customer_id, invoice_id, discount_amount
                ) VALUES (
                    :promotion_id, :customer_id, :invoice_id, :discount_amount
                )
            ");
            $this->db->bind(':promotion_id', $promotionId);
            $this->db->bind(':customer_id', $customerId);
            $this->db->bind(':invoice_id', $invoiceId);
            $this->db->bind(':discount_amount', $discountAmount);
            $this->db->execute();

            // الحصول على بيانات العرض الترويجي
            $promotion = $this->getPromotionById($promotionId);
            $promotionName = $promotion ? $promotion['name'] : 'hok1';

            // تحديث ملاحظات الفاتورة لتشمل معرف العرض الترويجي
            $this->db->prepare("
                UPDATE invoices
                SET notes = CONCAT(IFNULL(notes, ''), '\n[PROMOTION_ID:' || :promotion_id || '] تم تطبيق عرض: ' || :promotion_name)
                WHERE id = :invoice_id
            ");
            $this->db->bind(':promotion_id', $promotionId);
            $this->db->bind(':promotion_name', $promotionName);
            $this->db->bind(':invoice_id', $invoiceId);
            $this->db->execute();

            // إذا كان العرض من نوع النسبة المئوية، أضف قيمة الخصم الأصلية إلى الملاحظات
            if ($promotion && $promotion['discount_type'] === 'percentage') {
                $originalDiscountValue = $promotion['discount_value'];
                error_log("Adding original discount value to invoice #{$invoiceId} notes: {$originalDiscountValue}%");

                $this->db->prepare("
                    UPDATE invoices
                    SET notes = CONCAT(notes, '\n[ORIGINAL_DISCOUNT:' || :discount_value || ']')
                    WHERE id = :invoice_id
                ");
                $this->db->bind(':discount_value', $originalDiscountValue);
                $this->db->bind(':invoice_id', $invoiceId);
                $this->db->execute();
            }

            // إنهاء المعاملة فقط إذا بدأناها هنا
            if ($transactionStartedHere) {
                $this->db->commit();
                error_log('Committed transaction in recordPromotionUsage');
            }

            return true;
        } catch (Exception $e) {
            // التراجع عن المعاملة فقط إذا بدأناها هنا
            if ($transactionStartedHere && $this->db->inTransaction()) {
                $this->db->rollBack();
                error_log('Rolled back transaction in recordPromotionUsage');
            }
            error_log('خطأ أثناء تسجيل استخدام عرض ترويجي: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * الحصول على سجل استخدام عرض ترويجي
     * @param int $promotionId معرف العرض الترويجي
     * @return array سجل الاستخدام
     */
    public function getPromotionUsageHistory($promotionId) {
        try {
            $this->db->prepare("
                SELECT pu.*, c.name as customer_name, i.invoice_number
                FROM promotion_usage pu
                LEFT JOIN customers c ON pu.customer_id = c.id
                JOIN invoices i ON pu.invoice_id = i.id
                WHERE pu.promotion_id = :promotion_id
                ORDER BY pu.created_at DESC
            ");
            $this->db->bind(':promotion_id', $promotionId);
            return $this->db->fetchAll();
        } catch (Exception $e) {
            error_log('خطأ أثناء استرجاع سجل استخدام عرض ترويجي: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * حساب عدد العروض
     *
     * @param array $filters فلاتر البحث (اختياري)
     * @return int عدد العروض
     */
    public function countPromotions($filters = []) {
        try {
            $sql = "SELECT COUNT(*) FROM promotions p";

            $whereConditions = [];
            $bindings = [];

            // تطبيق الفلاتر
            if (!empty($filters['branch_id'])) {
                $whereConditions[] = "(p.branch_id = :branch_id OR p.branch_id IS NULL)";
                $bindings[':branch_id'] = $filters['branch_id'];
            }

            if (isset($filters['is_active'])) {
                $whereConditions[] = "p.is_active = :is_active";
                $bindings[':is_active'] = $filters['is_active'];
            }

            if (!empty($filters['search'])) {
                $whereConditions[] = "(p.name LIKE :search OR p.description LIKE :search)";
                $bindings[':search'] = '%' . $filters['search'] . '%';
            }

            // فلتر التاريخ الحالي (للعروض النشطة حاليًا)
            if (!empty($filters['current_date'])) {
                $whereConditions[] = "(p.start_date IS NULL OR p.start_date <= :start_date_param)";
                $whereConditions[] = "(p.end_date IS NULL OR p.end_date >= :end_date_param)";
                $bindings[':start_date_param'] = $filters['current_date'];
                $bindings[':end_date_param'] = $filters['current_date'];
            }

            // إضافة شروط WHERE إذا وجدت
            if (!empty($whereConditions)) {
                $sql .= " WHERE " . implode(' AND ', $whereConditions);
            }

            $this->db->prepare($sql);

            // ربط القيم
            foreach ($bindings as $param => $value) {
                $this->db->bind($param, $value);
            }

            return $this->db->fetchColumn();
        } catch (Exception $e) {
            log_error('خطأ أثناء حساب عدد العروض: ' . $e->getMessage(), $e);
            throw $e;
        }
    }
}

