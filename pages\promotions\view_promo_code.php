<?php
/**
 * صفحة عرض تفاصيل كود ترويج
 */

// تعريف ثابت لمنع الوصول المباشر للملفات المرتبطة
define('BASEPATH', true);

// استدعاء ملفات التهيئة
require_once '../../config/init.php';

// التحقق من تسجيل الدخول
requireLogin();

// التحقق من الصلاحيات
requirePermission('promotions_view');

// التحقق من وجود معرف كود الترويج
if (!isset($_GET['id']) || empty($_GET['id'])) {
    redirect('pages/promotions/promo_codes.php');
}

$promoCodeId = intval($_GET['id']);

// إنشاء كائنات النماذج
$promoCodeModel = new PromoCode($db);
$branchModel = new Branch($db);

// الحصول على بيانات كود الترويج
$promoCode = $promoCodeModel->getPromoCodeById($promoCodeId);

// التحقق من وجود كود الترويج
if (!$promoCode) {
    setFlashMessage('error', 'كود الترويج غير موجود.');
    redirect('pages/promotions/promo_codes.php');
}

// الحصول على سجل استخدام كود الترويج
$usageHistory = $promoCodeModel->getPromoCodeUsageHistory($promoCodeId);

// الحصول على اسم الفرع
$branchName = 'جميع الفروع';
if ($promoCode['branch_id']) {
    $branch = $branchModel->getBranchById($promoCode['branch_id']);
    if ($branch) {
        $branchName = $branch['name'];
    }
}

// عنوان الصفحة
$pageTitle = 'تفاصيل كود الترويج: ' . $promoCode['name'];

// استدعاء رأس الصفحة
include '../../includes/templates/header.php';
?>

<!-- المحتوى الرئيسي للصفحة -->
<div class="container-fluid">
    <!-- عنوان الصفحة -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2 class="h3 mb-0 text-gray-800">تفاصيل كود الترويج</h2>
        <div>
            <?php if (hasPermission('promotions_edit')): ?>
            <a href="edit_promo_code.php?id=<?php echo $promoCodeId; ?>" class="btn btn-primary me-2">
                <i class="fas fa-edit me-1"></i> تعديل
            </a>
            <?php endif; ?>
            <a href="promo_codes.php" class="btn btn-secondary">
                <i class="fas fa-arrow-right me-1"></i> العودة إلى القائمة
            </a>
        </div>
    </div>

    <!-- بطاقة تفاصيل كود الترويج -->
    <div class="row">
        <div class="col-lg-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex justify-content-between align-items-center">
                    <h6 class="m-0 font-weight-bold text-primary">معلومات كود الترويج</h6>
                    <span class="badge <?php echo $promoCode['is_active'] ? 'bg-success' : 'bg-danger'; ?>">
                        <?php echo $promoCode['is_active'] ? 'نشط' : 'غير نشط'; ?>
                    </span>
                </div>
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <h5>الكود</h5>
                            <p class="badge bg-secondary fs-6"><?php echo htmlspecialchars($promoCode['code']); ?></p>
                        </div>
                        <div class="col-md-6">
                            <h5>اسم العرض</h5>
                            <p><?php echo htmlspecialchars($promoCode['name']); ?></p>
                        </div>
                    </div>

                    <?php if (!empty($promoCode['description'])): ?>
                    <div class="row mb-3">
                        <div class="col-md-12">
                            <h5>وصف العرض</h5>
                            <p><?php echo nl2br(htmlspecialchars($promoCode['description'])); ?></p>
                        </div>
                    </div>
                    <?php endif; ?>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <h5>نوع الخصم</h5>
                            <p>
                                <?php
                                if ($promoCode['discount_type'] === 'percentage') {
                                    echo 'نسبة مئوية (%)';
                                } else {
                                    echo 'مبلغ ثابت (ج.م)';
                                }
                                ?>
                            </p>
                        </div>
                        <div class="col-md-6">
                            <h5>قيمة الخصم</h5>
                            <p>
                                <?php
                                if ($promoCode['discount_type'] === 'percentage') {
                                    echo $promoCode['discount_value'] . '%';
                                } else {
                                    echo number_format($promoCode['discount_value'], 2) . ' ج.م';
                                }
                                ?>
                            </p>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <h5>الحد الأدنى لقيمة الفاتورة</h5>
                            <p>
                                <?php
                                if ($promoCode['min_invoice_amount']) {
                                    echo number_format($promoCode['min_invoice_amount'], 2) . ' ج.م';
                                } else {
                                    echo '<span class="text-muted">غير محدد</span>';
                                }
                                ?>
                            </p>
                        </div>
                        <div class="col-md-6">
                            <h5>الحد الأقصى لقيمة الفاتورة</h5>
                            <p>
                                <?php
                                if ($promoCode['max_invoice_amount']) {
                                    echo number_format($promoCode['max_invoice_amount'], 2) . ' ج.م';
                                } else {
                                    echo '<span class="text-muted">غير محدد</span>';
                                }
                                ?>
                            </p>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <h5>الحد الأقصى لعدد مرات الاستخدام</h5>
                            <p>
                                <?php
                                if ($promoCode['max_uses']) {
                                    echo $promoCode['max_uses'] . ' مرة';
                                } else {
                                    echo '<span class="text-muted">غير محدود</span>';
                                }
                                ?>
                            </p>
                        </div>
                        <div class="col-md-6">
                            <h5>عدد مرات الاستخدام الحالية</h5>
                            <p><?php echo $promoCode['current_uses']; ?> مرة</p>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <h5>استخدام مرة واحدة لكل عميل</h5>
                            <p>
                                <?php if (isset($promoCode['one_use_per_customer']) && $promoCode['one_use_per_customer']): ?>
                                    <span class="badge bg-success">نعم</span>
                                <?php else: ?>
                                    <span class="badge bg-secondary">لا</span>
                                <?php endif; ?>
                            </p>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <h5>تاريخ بداية العرض</h5>
                            <p>
                                <?php
                                if ($promoCode['start_date']) {
                                    echo date('Y-m-d', strtotime($promoCode['start_date']));
                                } else {
                                    echo '<span class="text-muted">غير محدد</span>';
                                }
                                ?>
                            </p>
                        </div>
                        <div class="col-md-6">
                            <h5>تاريخ نهاية العرض</h5>
                            <p>
                                <?php
                                if ($promoCode['end_date']) {
                                    echo date('Y-m-d', strtotime($promoCode['end_date']));
                                } else {
                                    echo '<span class="text-muted">غير محدد</span>';
                                }
                                ?>
                            </p>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <h5>نقاط الولاء المطلوبة</h5>
                            <p>
                                <?php
                                if ($promoCode['required_loyalty_points']) {
                                    echo $promoCode['required_loyalty_points'] . ' نقطة';
                                } else {
                                    echo '<span class="text-muted">لا يتطلب نقاط</span>';
                                }
                                ?>
                            </p>
                        </div>
                        <div class="col-md-6">
                            <h5>الفرع</h5>
                            <p><?php echo htmlspecialchars($branchName); ?></p>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <h5>تاريخ الإنشاء</h5>
                            <p><?php echo date('Y-m-d H:i', strtotime($promoCode['created_at'])); ?></p>
                        </div>
                        <div class="col-md-6">
                            <h5>آخر تحديث</h5>
                            <p><?php echo date('Y-m-d H:i', strtotime($promoCode['updated_at'])); ?></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <?php if ($promoCode['is_for_specific_customers'] && !empty($promoCode['customers'])): ?>
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">العملاء المسموح لهم باستخدام الكود</h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>الاسم</th>
                                    <th>رقم الهاتف</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($promoCode['customers'] as $customer): ?>
                                <tr>
                                    <td><?php echo htmlspecialchars($customer['name']); ?></td>
                                    <td><?php echo htmlspecialchars($customer['phone']); ?></td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            <?php endif; ?>

            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">حالة الكود</h6>
                </div>
                <div class="card-body">
                    <?php
                    $today = date('Y-m-d');
                    $isExpired = $promoCode['end_date'] && $promoCode['end_date'] < $today;
                    $isNotStarted = $promoCode['start_date'] && $promoCode['start_date'] > $today;
                    $isMaxUsesReached = $promoCode['max_uses'] && $promoCode['current_uses'] >= $promoCode['max_uses'];
                    $isActive = $promoCode['is_active'] && !$isExpired && !$isNotStarted && !$isMaxUsesReached;
                    ?>

                    <div class="alert <?php echo $isActive ? 'alert-success' : 'alert-danger'; ?>">
                        <?php if ($isActive): ?>
                        <i class="fas fa-check-circle me-1"></i> الكود نشط وصالح للاستخدام
                        <?php else: ?>
                        <i class="fas fa-times-circle me-1"></i> الكود غير صالح للاستخدام
                        <?php endif; ?>
                    </div>

                    <ul class="list-group">
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            حالة التفعيل
                            <?php if ($promoCode['is_active']): ?>
                            <span class="badge bg-success">نشط</span>
                            <?php else: ?>
                            <span class="badge bg-danger">غير نشط</span>
                            <?php endif; ?>
                        </li>

                        <?php if ($promoCode['start_date']): ?>
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            بداية الصلاحية
                            <?php if ($isNotStarted): ?>
                            <span class="badge bg-warning">لم يبدأ بعد</span>
                            <?php else: ?>
                            <span class="badge bg-success">بدأ</span>
                            <?php endif; ?>
                        </li>
                        <?php endif; ?>

                        <?php if ($promoCode['end_date']): ?>
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            نهاية الصلاحية
                            <?php if ($isExpired): ?>
                            <span class="badge bg-danger">منتهي</span>
                            <?php else: ?>
                            <span class="badge bg-success">ساري</span>
                            <?php endif; ?>
                        </li>
                        <?php endif; ?>

                        <?php if ($promoCode['max_uses']): ?>
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            عدد مرات الاستخدام
                            <?php if ($isMaxUsesReached): ?>
                            <span class="badge bg-danger">تم الوصول للحد الأقصى</span>
                            <?php else: ?>
                            <span class="badge bg-success">متاح (<?php echo $promoCode['current_uses']; ?>/<?php echo $promoCode['max_uses']; ?>)</span>
                            <?php endif; ?>
                        </li>
                        <?php endif; ?>

                        <?php if (isset($promoCode['one_use_per_customer']) && $promoCode['one_use_per_customer']): ?>
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            استخدام مرة واحدة لكل عميل
                            <span class="badge bg-info">مفعل</span>
                        </li>
                        <?php endif; ?>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <!-- سجل استخدام كود الترويج -->
    <?php if (!empty($usageHistory)): ?>
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">سجل استخدام كود الترويج</h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered table-hover">
                    <thead class="table-light">
                        <tr>
                            <th>#</th>
                            <th>رقم الفاتورة</th>
                            <th>العميل</th>
                            <th>قيمة الخصم</th>
                            <th>تاريخ الاستخدام</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($usageHistory as $index => $usage): ?>
                        <tr>
                            <td><?php echo $index + 1; ?></td>
                            <td>
                                <a href="../invoices/view.php?id=<?php echo $usage['invoice_id']; ?>" target="_blank">
                                    <?php echo $usage['invoice_number']; ?>
                                </a>
                            </td>
                            <td>
                                <?php
                                if ($usage['customer_id']) {
                                    echo htmlspecialchars($usage['customer_name']);
                                } else {
                                    echo '<span class="text-muted">غير محدد</span>';
                                }
                                ?>
                            </td>
                            <td><?php echo number_format($usage['discount_amount'], 2) . ' ج.م'; ?></td>
                            <td><?php echo date('Y-m-d H:i', strtotime($usage['created_at'])); ?></td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    <?php else: ?>
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">سجل استخدام كود الترويج</h6>
        </div>
        <div class="card-body">
            <div class="alert alert-info text-center">
                <i class="fas fa-info-circle mb-2 fa-2x"></i>
                <p class="mb-0">لم يتم استخدام هذا الكود بعد.</p>
            </div>
        </div>
    </div>
    <?php endif; ?>
</div>

<!-- استدعاء قالب الفوتر -->
<?php include '../../includes/templates/footer.php'; ?>
