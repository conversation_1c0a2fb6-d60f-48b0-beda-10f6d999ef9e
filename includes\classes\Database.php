<?php
/**
 * فئة قاعدة البيانات
 * تتعامل مع الاتصال بقاعدة البيانات وتنفيذ الاستعلامات
 */

// منع الوصول المباشر للملف
if (!defined('BASEPATH')) {
    exit('لا يمكن الوصول المباشر لهذا الملف');
}

class Database {
    private $connection;
    private $stmt;
    private $lastQuery;

    /**
     * إنشاء اتصال بقاعدة البيانات
     */
    public function __construct() {
        $dsn = 'mysql:host=' . DB_HOST . ';dbname=' . DB_NAME . ';charset=' . DB_CHARSET;
        $options = [
            PDO::ATTR_PERSISTENT => true,
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::ATTR_EMULATE_PREPARES => false
        ];

        try {
            $this->connection = new PDO($dsn, DB_USER, DB_PASS, $options);
        } catch (PDOException $e) {
            die('فشل الاتصال بقاعدة البيانات: ' . $e->getMessage());
        }
    }

    /**
     * تحضير استعلام SQL
     * @param string $sql استعلام SQL
     * @return void
     */
    public function prepare($sql) {
        $this->lastQuery = $sql;
        $this->stmt = $this->connection->prepare($sql);
    }

    /**
     * الحصول على آخر استعلام SQL تم تنفيذه
     * @return string الاستعلام SQL
     */
    public function getLastQuery() {
        return $this->lastQuery;
    }

    /**
     * ربط قيمة بمعامل في الاستعلام
     * @param string $param اسم المعامل
     * @param mixed $value القيمة
     * @param mixed $type نوع البيانات (اختياري)
     * @return void
     */
    public function bind($param, $value, $type = null) {
        if (is_null($type)) {
            switch (true) {
                case is_int($value):
                    $type = PDO::PARAM_INT;
                    break;
                case is_bool($value):
                    $type = PDO::PARAM_BOOL;
                    break;
                case is_null($value):
                    $type = PDO::PARAM_NULL;
                    break;
                default:
                    $type = PDO::PARAM_STR;
            }
        }

        $this->stmt->bindValue($param, $value, $type);
    }

    /**
     * تنفيذ الاستعلام
     * @return bool نجاح أو فشل التنفيذ
     */
    public function execute() {
        try {
            return $this->stmt->execute();
        } catch (PDOException $e) {
            log_error('خطأ SQL: ' . $e->getMessage() . ' - الاستعلام: ' . $this->stmt->queryString, $e);
            throw $e;
        }
    }

    /**
     * استعلام يرجع مجموعة نتائج
     * @return array مصفوفة بالنتائج
     */
    public function fetchAll() {
        $this->execute();
        return $this->stmt->fetchAll();
    }

    /**
     * استعلام يرجع صف واحد
     * @return array|false مصفوفة بالصف أو false إذا لم يتم العثور على نتائج
     */
    public function fetch() {
        $this->execute();
        return $this->stmt->fetch();
    }

    /**
     * استعلام يرجع قيمة واحدة
     * @return mixed القيمة المطلوبة
     */
    public function fetchColumn() {
        $this->execute();
        return $this->stmt->fetchColumn();
    }

    /**
     * حساب عدد الصفوف المتأثرة
     * @return int عدد الصفوف
     */
    public function rowCount() {
        return $this->stmt->rowCount();
    }

    /**
     * الحصول على معرف آخر صف تم إدراجه
     * @param string|null $name اسم التسلسل (اختياري)
     * @return string معرف آخر صف
     */
    public function lastInsertId($name = null) {
        return $this->connection->lastInsertId($name);
    }

    /**
     * بدء معاملة
     * @return bool نجاح أو فشل العملية
     */
    public function beginTransaction() {
        return $this->connection->beginTransaction();
    }

    /**
     * إنهاء معاملة بنجاح
     * @return bool نجاح أو فشل العملية
     */
    public function commit() {
        return $this->connection->commit();
    }

    /**
     * التراجع عن معاملة
     * @return bool نجاح أو فشل العملية
     */
    public function rollBack() {
        return $this->connection->rollBack();
    }

    /**
     * تنفيذ استعلام مباشر (للاستعلامات البسيطة)
     * @param string $sql استعلام SQL
     * @return PDOStatement كائن PDOStatement
     */
    public function query($sql) {
        return $this->connection->query($sql);
    }

    /**
     * إغلاق الاتصال بقاعدة البيانات
     * @return void
     */
    public function close() {
        $this->connection = null;
        $this->stmt = null;
    }

    /**
     * التحقق مما إذا كانت هناك معاملة نشطة
     * @return bool
     */
    public function inTransaction() {
        return $this->connection->inTransaction();
    }
}
