<?php
/**
 * صفحة إعدادات WATI
 */

// تعريف ثابت لمنع الوصول المباشر للملفات المرتبطة
define('BASEPATH', true);

// استدعاء ملفات التهيئة
require_once '../../config/init.php';

// التحقق من تسجيل الدخول
requireLogin();

// التحقق من وجود صلاحية إدارة الإعدادات
requirePermission('settings_manage');

// عنوان الصفحة
$pageTitle = 'إعدادات WATI';

// إنشاء كائن الإعدادات
$settingsModel = new Settings($db);

// معالجة النموذج
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    try {
        // جمع البيانات من النموذج
        $settings = [
            'wati_enabled' => isset($_POST['wati_enabled']) ? 1 : 0,
            'wati_base_url' => sanitizeInput($_POST['wati_base_url']),
            'wati_access_token' => sanitizeInput($_POST['wati_access_token']),
            'notification_enable_whatsapp' => isset($_POST['notification_enable_whatsapp']) ? 1 : 0,
            'notification_reminder_hours' => intval($_POST['notification_reminder_hours'])
        ];

        // حفظ الإعدادات
        foreach ($settings as $key => $value) {
            $settingsModel->updateSetting($key, $value);
        }

        // رسالة نجاح
        $_SESSION['success_message'] = 'تم حفظ إعدادات WATI بنجاح';
    } catch (Exception $e) {
        // رسالة خطأ
        $_SESSION['error_message'] = 'حدث خطأ أثناء حفظ الإعدادات: ' . $e->getMessage();
    }
}

// الحصول على الإعدادات الحالية
$currentSettings = $settingsModel->getAllSettings();

// استدعاء رأس الصفحة
include '../../includes/templates/header.php';
?>

<!-- المحتوى الرئيسي للصفحة -->
<div class="container-fluid">
    <!-- رأس الصفحة -->
    <div class="card border-0 shadow-sm mb-4">
        <div class="card-body d-flex justify-content-between align-items-center">
            <h5 class="mb-0">
                <i class="fab fa-whatsapp me-2 text-success"></i> إعدادات WATI
                <span class="badge bg-warning text-dark ms-2">النسخة التجريبية</span>
            </h5>
            <a href="index.php" class="btn btn-secondary">
                <i class="fas fa-arrow-right me-1"></i> العودة للإعدادات
            </a>
        </div>
    </div>

    <!-- تحذير النسخة التجريبية -->
    <div class="alert alert-warning border-0 shadow-sm mb-4">
        <div class="d-flex align-items-center">
            <i class="fas fa-exclamation-triangle fa-2x text-warning me-3"></i>
            <div>
                <h5 class="alert-heading mb-2">ميزة غير متاحة في النسخة التجريبية</h5>
                <p class="mb-2">إعدادات وخدمات WATI متوفرة فقط في النسخة المدفوعة من النظام.</p>
                <hr>
                <div class="row">
                    <div class="col-md-6">
                        <h6 class="mb-2">المميزات المتوفرة في النسخة المدفوعة:</h6>
                        <ul class="mb-0">
                            <li>تكامل مع WATI.io</li>
                            <li>إرسال رسائل WhatsApp Business</li>
                            <li>قوالب رسائل احترافية</li>
                            <li>تتبع حالة الرسائل</li>
                            <li>إحصائيات مفصلة</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6 class="mb-2">للحصول على النسخة الكاملة:</h6>
                        <div class="d-grid gap-2">
                            <a href="https://wa.me/201556262660" target="_blank" class="btn btn-success">
                                <i class="fab fa-whatsapp me-2"></i>تواصل عبر WhatsApp
                            </a>
                            <a href="tel:+201556262660" class="btn btn-outline-primary">
                                <i class="fas fa-phone me-2"></i>اتصال مباشر
                            </a>
                            <a href="mailto:<EMAIL>" class="btn btn-outline-secondary">
                                <i class="fas fa-envelope me-2"></i>إرسال بريد إلكتروني
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- نموذج الإعدادات المعطل -->
    <div class="card border-0 shadow-sm">
        <div class="card-header bg-light">
            <h6 class="mb-0 text-muted">
                <i class="fas fa-lock me-2"></i>إعدادات WATI (متوفرة في النسخة المدفوعة فقط)
            </h6>
        </div>
        <div class="card-body">
            <div class="alert alert-info">
                <i class="fas fa-info-circle me-2"></i>
                <strong>معاينة الإعدادات:</strong> هذه معاينة لإعدادات WATI المتوفرة في النسخة المدفوعة. جميع الحقول معطلة في النسخة التجريبية.
            </div>

            <form method="post" action="" class="demo-disabled-form">
                <div class="alert alert-secondary">
                    <i class="fas fa-info-circle me-2"></i>
                    في النسخة المدفوعة: يتم التكامل مع منصة WATI.io لإرسال رسائل WhatsApp Business بشكل احترافي.
                </div>

                <h6 class="border-bottom pb-2 mb-3">إعدادات الاتصال بخدمة WATI</h6>

                <div class="form-check form-switch mb-3">
                    <input class="form-check-input" type="checkbox" id="wati_enabled" name="wati_enabled" disabled>
                    <label class="form-check-label text-muted" for="wati_enabled">تفعيل خدمة WATI</label>
                </div>

                <div class="row mb-3">
                    <div class="col-md-6">
                        <label for="wati_base_url" class="form-label text-muted">عنوان URL الأساسي</label>
                        <input type="text" id="wati_base_url" name="wati_base_url" class="form-control" placeholder="مثال: https://app.wati.io" disabled>
                        <div class="form-text">عنوان URL الأساسي لحسابك في WATI</div>
                    </div>
                    <div class="col-md-6">
                        <label for="wati_access_token" class="form-label text-muted">رمز الوصول (Access Token)</label>
                        <input type="password" id="wati_access_token" name="wati_access_token" class="form-control" placeholder="••••••••••••••••" disabled>
                        <div class="form-text">رمز الوصول الخاص بحسابك في WATI</div>
                    </div>
                </div>

                <h6 class="border-bottom pb-2 mb-3 mt-4">إعدادات تذكيرات المواعيد</h6>

                <div class="form-check form-switch mb-3">
                    <input class="form-check-input" type="checkbox" id="notification_enable_whatsapp" name="notification_enable_whatsapp" disabled>
                    <label class="form-check-label text-muted" for="notification_enable_whatsapp">إرسال تذكيرات المواعيد عبر WhatsApp</label>
                </div>

                <div class="mb-3">
                    <label for="notification_reminder_hours" class="form-label text-muted">إرسال التذكير قبل الموعد بـ (ساعات)</label>
                    <input type="number" id="notification_reminder_hours" name="notification_reminder_hours" class="form-control" value="24" min="1" max="72" disabled>
                    <div class="form-text">عدد الساعات قبل الموعد لإرسال التذكير</div>
                </div>

                <div class="text-end">
                    <button type="button" class="btn btn-secondary" disabled>
                        <i class="fas fa-lock me-1"></i> متوفر في النسخة المدفوعة فقط
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- قسم الاختبار -->
    <div class="card border-0 shadow-sm mt-4">
        <div class="card-header bg-light">
            <h6 class="mb-0 text-muted">
                <i class="fas fa-lock me-2"></i>اختبار إرسال رسالة WATI (متوفر في النسخة المدفوعة فقط)
            </h6>
        </div>
        <div class="card-body">
            <div class="alert alert-info">
                <i class="fas fa-info-circle me-2"></i>
                <strong>في النسخة المدفوعة:</strong> يمكن اختبار إرسال رسائل WhatsApp عبر WATI مباشرة من لوحة التحكم.
            </div>

            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="test_phone" class="form-label text-muted">رقم الهاتف</label>
                        <input type="text" id="test_phone" class="form-control" placeholder="أدخل رقم الهاتف" disabled>
                    </div>
                    <div class="mb-3">
                        <label for="test_message" class="form-label text-muted">الرسالة</label>
                        <textarea id="test_message" class="form-control" rows="3" placeholder="أدخل نص الرسالة" disabled>مرحبا، هذه رسالة اختبار من نظام إدارة الصالون.</textarea>
                    </div>
                    <button type="button" class="btn btn-secondary" disabled>
                        <i class="fab fa-whatsapp me-1"></i> إرسال رسالة اختبار
                    </button>
                </div>
                <div class="col-md-6">
                    <div class="alert alert-secondary">
                        <h6>مميزات WATI في النسخة المدفوعة:</h6>
                        <ul>
                            <li>تكامل مع WhatsApp Business API</li>
                            <li>قوالب رسائل معتمدة</li>
                            <li>إرسال رسائل جماعية</li>
                            <li>تتبع حالة التسليم</li>
                            <li>إحصائيات مفصلة</li>
                            <li>دعم الوسائط المتعددة</li>
                        </ul>
                    </div>

                    <div class="alert alert-warning">
                        <i class="fas fa-crown me-2"></i>
                        <strong>احصل على النسخة الكاملة:</strong>
                        <div class="mt-2">
                            <a href="https://wa.me/201556262660" target="_blank" class="btn btn-success btn-sm">
                                <i class="fab fa-whatsapp me-1"></i>واتساب
                            </a>
                            <a href="tel:+201556262660" class="btn btn-primary btn-sm">
                                <i class="fas fa-phone me-1"></i>اتصال
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- استدعاء قالب الفوتر -->
<?php include '../../includes/templates/footer.php'; ?>

<!-- إضافة CSS للنموذج المعطل -->
<style>
.demo-disabled-form input:disabled,
.demo-disabled-form textarea:disabled,
.demo-disabled-form select:disabled {
    background-color: #f8f9fa;
    opacity: 0.6;
}

.demo-disabled-form .form-check-input:disabled {
    opacity: 0.5;
}

.text-muted {
    color: #6c757d !important;
}
</style>

<!-- رسالة للمستخدمين الذين يحاولون استخدام الميزات المعطلة -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // إضافة رسالة عند النقر على العناصر المعطلة
    const disabledElements = document.querySelectorAll('input:disabled, textarea:disabled, button:disabled');

    disabledElements.forEach(element => {
        element.addEventListener('click', function(e) {
            e.preventDefault();

            // إنشاء نافذة تنبيه
            const modal = document.createElement('div');
            modal.className = 'modal fade';
            modal.innerHTML = `
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header bg-warning text-dark">
                            <h5 class="modal-title">
                                <i class="fas fa-crown me-2"></i>ميزة متوفرة في النسخة المدفوعة فقط
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <p>هذه الميزة متوفرة فقط في النسخة المدفوعة من النظام.</p>
                            <p><strong>للحصول على النسخة الكاملة مع جميع مميزات WATI:</strong></p>
                            <div class="d-grid gap-2">
                                <a href="https://wa.me/201556262660" target="_blank" class="btn btn-success">
                                    <i class="fab fa-whatsapp me-2"></i>تواصل عبر WhatsApp
                                </a>
                                <a href="tel:+201556262660" class="btn btn-primary">
                                    <i class="fas fa-phone me-2"></i>اتصال مباشر: +201556262660
                                </a>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                        </div>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);
            const bsModal = new bootstrap.Modal(modal);
            bsModal.show();

            modal.addEventListener('hidden.bs.modal', () => {
                modal.remove();
            });
        });
    });
});
</script>
