<?php
/**
 * صفحة إعدادات WATI
 */

// تعريف ثابت لمنع الوصول المباشر للملفات المرتبطة
define('BASEPATH', true);

// استدعاء ملفات التهيئة
require_once '../../config/init.php';

// التحقق من تسجيل الدخول
requireLogin();

// التحقق من وجود صلاحية إدارة الإعدادات
requirePermission('settings_manage');

// عنوان الصفحة
$pageTitle = 'إعدادات WATI';

// إنشاء كائن الإعدادات
$settingsModel = new Settings($db);

// معالجة النموذج
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    try {
        // جمع البيانات من النموذج
        $settings = [
            'wati_enabled' => isset($_POST['wati_enabled']) ? 1 : 0,
            'wati_base_url' => sanitizeInput($_POST['wati_base_url']),
            'wati_access_token' => sanitizeInput($_POST['wati_access_token']),
            'notification_enable_whatsapp' => isset($_POST['notification_enable_whatsapp']) ? 1 : 0,
            'notification_reminder_hours' => intval($_POST['notification_reminder_hours'])
        ];

        // حفظ الإعدادات
        foreach ($settings as $key => $value) {
            $settingsModel->updateSetting($key, $value);
        }

        // رسالة نجاح
        $_SESSION['success_message'] = 'تم حفظ إعدادات WATI بنجاح';
    } catch (Exception $e) {
        // رسالة خطأ
        $_SESSION['error_message'] = 'حدث خطأ أثناء حفظ الإعدادات: ' . $e->getMessage();
    }
}

// الحصول على الإعدادات الحالية
$currentSettings = $settingsModel->getAllSettings();

// استدعاء رأس الصفحة
include '../../includes/templates/header.php';
?>

<!-- المحتوى الرئيسي للصفحة -->
<div class="container-fluid">
    <!-- رأس الصفحة -->
    <div class="card border-0 shadow-sm mb-4">
        <div class="card-body d-flex justify-content-between align-items-center">
            <h5 class="mb-0">
                <i class="fab fa-whatsapp me-2 text-success"></i> إعدادات WATI
            </h5>
            <a href="index.php" class="btn btn-secondary">
                <i class="fas fa-arrow-right me-1"></i> العودة للإعدادات
            </a>
        </div>
    </div>

    <!-- نموذج الإعدادات -->
    <div class="card border-0 shadow-sm">
        <div class="card-body">
            <form method="post" action="">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    WATI هي منصة تتيح لك إرسال رسائل WhatsApp بشكل آلي باستخدام واجهة برمجة تطبيقات WhatsApp Business. تحتاج إلى إنشاء حساب في <a href="https://www.wati.io/" target="_blank">WATI.io</a> والحصول على معلومات الوصول.
                </div>

                <h6 class="border-bottom pb-2 mb-3">إعدادات الاتصال بخدمة WATI</h6>

                <div class="form-check form-switch mb-3">
                    <input class="form-check-input" type="checkbox" id="wati_enabled" name="wati_enabled" <?php echo isset($currentSettings['wati_enabled']) && $currentSettings['wati_enabled'] ? 'checked' : ''; ?>>
                    <label class="form-check-label" for="wati_enabled">تفعيل خدمة WATI</label>
                </div>

                <div class="row mb-3">
                    <div class="col-md-6">
                        <label for="wati_base_url" class="form-label">عنوان URL الأساسي</label>
                        <input type="text" id="wati_base_url" name="wati_base_url" class="form-control" value="<?php echo htmlspecialchars($currentSettings['wati_base_url'] ?? ''); ?>" placeholder="مثال: https://app.wati.io">
                        <div class="form-text">عنوان URL الأساسي لحسابك في WATI</div>
                    </div>
                    <div class="col-md-6">
                        <label for="wati_access_token" class="form-label">رمز الوصول (Access Token)</label>
                        <input type="password" id="wati_access_token" name="wati_access_token" class="form-control" value="<?php echo htmlspecialchars($currentSettings['wati_access_token'] ?? ''); ?>">
                        <div class="form-text">رمز الوصول الخاص بحسابك في WATI</div>
                    </div>
                </div>

                <h6 class="border-bottom pb-2 mb-3 mt-4">إعدادات تذكيرات المواعيد</h6>

                <div class="form-check form-switch mb-3">
                    <input class="form-check-input" type="checkbox" id="notification_enable_whatsapp" name="notification_enable_whatsapp" <?php echo isset($currentSettings['notification_enable_whatsapp']) && $currentSettings['notification_enable_whatsapp'] ? 'checked' : ''; ?>>
                    <label class="form-check-label" for="notification_enable_whatsapp">إرسال تذكيرات المواعيد عبر WhatsApp</label>
                </div>

                <div class="mb-3">
                    <label for="notification_reminder_hours" class="form-label">إرسال التذكير قبل الموعد بـ (ساعات)</label>
                    <input type="number" id="notification_reminder_hours" name="notification_reminder_hours" class="form-control" value="<?php echo htmlspecialchars($currentSettings['notification_reminder_hours'] ?? '24'); ?>" min="1" max="72">
                    <div class="form-text">عدد الساعات قبل الموعد لإرسال التذكير</div>
                </div>

                <div class="text-end">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-1"></i> حفظ الإعدادات
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- قسم الاختبار -->
    <div class="card border-0 shadow-sm mt-4">
        <div class="card-body">
            <h6 class="border-bottom pb-2 mb-3">اختبار إرسال رسالة WhatsApp</h6>

            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="test_phone" class="form-label">رقم الهاتف</label>
                        <input type="text" id="test_phone" class="form-control" placeholder="أدخل رقم الهاتف">
                    </div>
                    <div class="mb-3">
                        <label for="test_message" class="form-label">الرسالة</label>
                        <textarea id="test_message" class="form-control" rows="3" placeholder="أدخل نص الرسالة">مرحبا، هذه رسالة اختبار من نظام إدارة صالون البدرواي.</textarea>
                    </div>
                    <button type="button" id="send_test_message" class="btn btn-success">
                        <i class="fab fa-whatsapp me-1"></i> إرسال رسالة اختبار
                    </button>
                    <div id="test_result" class="mt-3"></div>
                </div>
                <div class="col-md-6">
                    <div class="alert alert-secondary">
                        <h6>تعليمات الاختبار:</h6>
                        <ol>
                            <li>تأكد من إدخال جميع بيانات الاتصال بخدمة WATI وحفظها.</li>
                            <li>أدخل رقم هاتف صحيح بتنسيق دولي (مثال: 201234567890).</li>
                            <li>اكتب رسالة اختبار قصيرة.</li>
                            <li>انقر على زر "إرسال رسالة اختبار".</li>
                            <li>ستظهر نتيجة الاختبار أدناه.</li>
                        </ol>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- سكريبت اختبار إرسال رسالة WhatsApp -->
<script type="text/javascript">
document.addEventListener('DOMContentLoaded', function() {
    // التأكد من تحميل jQuery
    if (typeof jQuery !== 'undefined') {
        // إضافة مستمع النقر لزر إرسال رسالة الاختبار
        jQuery('#send_test_message').on('click', function() {
            const phone = jQuery('#test_phone').val().trim();
            const message = jQuery('#test_message').val().trim();

            if (!phone) {
                showAlert('يرجى إدخال رقم الهاتف', 'warning');
                return;
            }

            if (!message) {
                showAlert('يرجى إدخال نص الرسالة', 'warning');
                return;
            }

            // عرض مؤشر التحميل
            jQuery('#test_result').html('<div class="spinner-border spinner-border-sm text-primary" role="status"></div> جاري إرسال الرسالة...');

            // إرسال طلب AJAX
            jQuery.ajax({
                url: '../../api/whatsapp.php',
                type: 'POST',
                data: {
                    action: 'send_test',
                    phone: phone,
                    message: message
                },
                dataType: 'json',
                success: function(response) {
                    if (response.status === 'success') {
                        jQuery('#test_result').html('<div class="alert alert-success">تم إرسال الرسالة بنجاح!</div>');
                    } else {
                        jQuery('#test_result').html('<div class="alert alert-danger">فشل إرسال الرسالة: ' + response.message + '</div>');
                    }
                },
                error: function() {
                    jQuery('#test_result').html('<div class="alert alert-danger">حدث خطأ أثناء الاتصال بالخادم</div>');
                }
            });
        });
    } else {
        console.error('jQuery is not loaded!');
    }
});
</script>

<!-- استدعاء قالب الفوتر -->
<?php include '../../includes/templates/footer.php'; ?>
