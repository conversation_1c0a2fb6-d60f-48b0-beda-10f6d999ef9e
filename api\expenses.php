<?php
/**
 * نقاط النهاية الخاصة بإدارة المصروفات
 */

// تأكيد تحميل الملفات الأساسية
require_once __DIR__ . '/../config/init.php';

// التحقق من نوع الطلب (POST أو GET أو PUT أو DELETE)
$action = isset($_GET['action']) ? $_GET['action'] : '';

// معالجة الطلبات
try {
    // إعداد رأس الاستجابة
    header('Content-Type: application/json');

    // إنشاء كائنات النماذج
    $db = new Database();
    $expenseModel = new Expense($db);
    $endDayModel = new EndDay($db);

    // التحقق من صلاحيات المستخدم
    if (!isLoggedIn()) {
        throw new Exception('يجب تسجيل الدخول أولاً', 401);
    }

    switch ($action) {
        // إضافة مصروف جديد
        case 'add':
            // التأكد من وجود الصلاحية
            requirePermission('expenses_add');

            // التأكد من أن الطلب عبر POST
            if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
                throw new Exception('الطريقة غير مسموح بها', 405);
            }

            // التحقق من صحة المدخلات
            $branchId = input('branch_id') ?? $_SESSION['user_branch_id'];
            $date = input('date') ?? date('Y-m-d');

            // التحقق من وجود يوم مفتوح للفرع
            $currentEndDay = $endDayModel->getCurrentEndDay($branchId, $date);

            if (!$currentEndDay) {
                throw new Exception('لا يوجد يوم عمل مفتوح لهذا الفرع. يجب فتح اليوم أولاً قبل إضافة مصروفات.', 400);
            }

            if ($currentEndDay['closed_at']) {
                throw new Exception('تم إغلاق يوم العمل بالفعل. لا يمكن إضافة مصروفات جديدة.', 400);
            }

            $expenseData = [
                'category_id' => input('category_id'),
                'amount' => input('amount'),
                'description' => input('description'),
                'date' => $date,
                'payment_method' => input('payment_method') ?? 'cash',
                'branch_id' => $branchId,
                'end_day_id' => $currentEndDay['id'] // ربط المصروف بيوم العمل المفتوح
            ];

            // التحقق من الإدخالات الإلزامية
            if (empty($expenseData['category_id']) || empty($expenseData['amount'])) {
                throw new Exception('فئة المصروف والمبلغ مطلوبان', 400);
            }

            // إضافة المصروف
            $expenseId = $expenseModel->addExpense($expenseData);

            echo json_encode([
                'status' => 'success',
                'message' => 'تمت إضافة المصروف بنجاح',
                'expense_id' => $expenseId
            ]);
            break;

        // تحديث مصروف
        case 'update':
            // التأكد من وجود الصلاحية
            requirePermission('expenses_edit');

            // التأكد من أن الطلب عبر PUT
            if ($_SERVER['REQUEST_METHOD'] !== 'PUT') {
                throw new Exception('الطريقة غير مسموح بها', 405);
            }

            // قراءة البيانات من الطلب
            parse_str(file_get_contents('php://input'), $putData);

            // التحقق من وجود معرف المصروف
            $expenseId = $putData['id'] ?? null;
            if (!$expenseId) {
                throw new Exception('معرف المصروف مطلوب', 400);
            }

            // التحقق من صحة المدخلات
            $branchId = $putData['branch_id'] ?? $_SESSION['user_branch_id'];
            $date = $putData['date'] ?? date('Y-m-d');

            // التحقق من وجود يوم مفتوح للفرع
            $currentEndDay = $endDayModel->getCurrentEndDay($branchId, $date);

            if (!$currentEndDay) {
                throw new Exception('لا يوجد يوم عمل مفتوح لهذا الفرع. يجب فتح اليوم أولاً قبل تحديث مصروفات.', 400);
            }

            if ($currentEndDay['closed_at']) {
                throw new Exception('تم إغلاق يوم العمل بالفعل. لا يمكن تحديث مصروفات.', 400);
            }

            $expenseData = [
                'category_id' => $putData['category_id'],
                'amount' => $putData['amount'],
                'description' => $putData['description'] ?? null,
                'date' => $date,
                'payment_method' => $putData['payment_method'] ?? 'cash',
                'branch_id' => $branchId,
                'end_day_id' => $currentEndDay['id'] // ربط المصروف بيوم العمل المفتوح
            ];

            // التحقق من الإدخالات الإلزامية
            if (empty($expenseData['category_id']) || empty($expenseData['amount'])) {
                throw new Exception('فئة المصروف والمبلغ مطلوبان', 400);
            }

            // تحديث المصروف
            $expenseModel->updateExpense($expenseId, $expenseData);

            echo json_encode([
                'status' => 'success',
                'message' => 'تم تحديث المصروف بنجاح'
            ]);
            break;

        // حذف مصروف
        case 'delete':
            // التأكد من وجود الصلاحية
            requirePermission('expenses_delete');

            // التأكد من أن الطلب عبر DELETE
            if ($_SERVER['REQUEST_METHOD'] !== 'DELETE') {
                throw new Exception('الطريقة غير مسموح بها', 405);
            }

            // قراءة البيانات من الطلب
            parse_str(file_get_contents('php://input'), $deleteData);

            // التحقق من وجود معرف المصروف
            $expenseId = $deleteData['id'] ?? null;
            if (!$expenseId) {
                throw new Exception('معرف المصروف مطلوب', 400);
            }

            // حذف المصروف
            $expenseModel->deleteExpense($expenseId);

            echo json_encode([
                'status' => 'success',
                'message' => 'تم حذف المصروف بنجاح'
            ]);
            break;

        // استرجاع قائمة المصروفات
        case 'list':
            // التأكد من وجود الصلاحية
            requirePermission('expenses_view');

            // معلمات الفلترة والترتيب
            $filters = [
                'search' => $_GET['search'] ?? null,
                'category_id' => $_GET['category_id'] ?? null,
                'branch_id' => $_GET['branch_id'] ?? $_SESSION['user_branch_id'],
                'payment_method' => $_GET['payment_method'] ?? null,
                'start_date' => $_GET['start_date'] ?? null,
                'end_date' => $_GET['end_date'] ?? null,
                'sort_by' => $_GET['sort_by'] ?? 'date',
                'sort_dir' => $_GET['sort_dir'] ?? 'DESC',
                'limit' => $_GET['limit'] ?? 50,
                'offset' => $_GET['offset'] ?? 0
            ];

            // استرجاع المصروفات
            $expenses = $expenseModel->getExpenses($filters);
            $totalCount = $expenseModel->getExpensesCount($filters);
            $totalAmount = $expenseModel->getTotalExpenses($filters);

            echo json_encode([
                'status' => 'success',
                'expenses' => $expenses,
                'total_count' => $totalCount,
                'total_amount' => $totalAmount
            ]);
            break;

        // استرجاع تفاصيل مصروف محدد
        case 'view':
            // التأكد من وجود الصلاحية
            requirePermission('expenses_view');

            // التحقق من وجود معرف المصروف
            $expenseId = $_GET['id'];
            if (!$expenseId) {
                throw new Exception('معرف المصروف مطلوب', 400);
            }

            // استرجاع بيانات المصروف
            $expense = $expenseModel->getExpenseById($expenseId);

            if (!$expense) {
                throw new Exception('لم يتم العثور على المصروف', 404);
            }

            echo json_encode([
                'status' => 'success',
                'expense' => $expense
            ]);
            break;

        // إضافة فئة مصروفات جديدة
        case 'add-category':
            // التأكد من وجود الصلاحية
            requirePermission('expenses_add');

            // التأكد من أن الطلب عبر POST
            if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
                throw new Exception('الطريقة غير مسموح بها', 405);
            }

            // التحقق من صحة المدخلات
            $categoryData = [
                'name' => input('name'),
                'description' => input('description')
            ];

            // التحقق من الإدخالات الإلزامية
            if (empty($categoryData['name'])) {
                throw new Exception('اسم الفئة مطلوب', 400);
            }

            // إضافة فئة المصروفات
            $categoryId = $expenseModel->addExpenseCategory($categoryData);

            echo json_encode([
                'status' => 'success',
                'message' => 'تمت إضافة فئة المصروفات بنجاح',
                'category_id' => $categoryId
            ]);
            break;

        // استرجاع فئات المصروفات
        case 'list-categories':
            // استرجاع قائمة فئات المصروفات
            $categories = $expenseModel->getExpenseCategories();

            echo json_encode([
                'status' => 'success',
                'categories' => $categories
            ]);
            break;

        // إنشاء تقرير المصروفات
        case 'report':
            // التأكد من وجود الصلاحية
            requirePermission('expenses_view');

            // معلمات التقرير
            $filters = [
                'branch_id' => input('branch_id') ?? $_SESSION['user_branch_id'],
                'start_date' => input('start_date'),
                'end_date' => input('end_date'),
                'month' => input('month'),
                'year' => input('year')
            ];

            // إنشاء التقرير
            $report = $expenseModel->generateExpensesReport($filters);

            echo json_encode([
                'status' => 'success',
                'report' => $report
            ]);
            break;

        // الافتراضي: إجراء غير معروف
        default:
            throw new Exception('إجراء غير معروف', 404);
    }
} catch (Exception $e) {
    // معالجة الأخطاء
    http_response_code($e->getCode() ?: 500);

    echo json_encode([
        'status' => 'error',
        'message' => $e->getMessage(),
        'code' => $e->getCode() ?: 500
    ]);
}
