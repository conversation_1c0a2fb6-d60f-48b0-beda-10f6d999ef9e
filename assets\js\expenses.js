/**
 * ملف JavaScript للتعامل مع وظائف إدارة المصروفات
 */

// تنفيذ الكود عند اكتمال تحميل الصفحة
$(document).ready(function() {
    // تهيئة عناصر الصفحة
    ExpensesHandler.initializeComponents();
    
    // إعداد الأحداث
    ExpensesHandler.setupEventListeners();
    
    // تحميل البيانات الأولية
    ExpensesHandler.loadInitialData();
});

/**
 * المعالج الرئيسي لوظائف إدارة المصروفات
 */
const ExpensesHandler = {
    /**
     * تهيئة مكونات الصفحة
     */
    initializeComponents: function() {
        // تهيئة عناصر التاريخ
        if ($.fn.datepicker) {
            $('.datepicker').datepicker({
                format: 'yyyy-mm-dd',
                autoclose: true,
                todayHighlight: true,
                rtl: true
            });
        }
        
        // تهيئة عناصر القائمة المنسدلة
        if ($.fn.select2) {
            $('.select2').select2({
                dir: "rtl",
                width: '100%'
            });
        }
        
        // تهيئة جدول المصروفات إذا كان موجودًا
        if ($.fn.DataTable && $('#expenses-table').length) {
            $('#expenses-table').DataTable({
                processing: true,
                serverSide: false,
                ordering: true,
                paging: true,
                pageLength: 25,
                responsive: true,
                language: {
                    url: '/assets/plugins/datatables/ar.json'
                },
                columns: [
                    { data: 'id' },
                    { data: 'date' },
                    { data: 'category_name' },
                    { data: 'amount' },
                    { data: 'payment_method' },
                    { data: 'description' },
                    { data: 'branch_name' },
                    { data: 'actions', orderable: false }
                ]
            });
        }
        
        // تهيئة مخططات الإحصائيات إذا كانت موجودة
        if ($('#expense-stats-container').length) {
            this.initializeCharts();
        }
    },
    
    /**
     * إعداد مستمعي الأحداث
     */
    setupEventListeners: function() {
        // النموذج الرئيسي للمصروف (إضافة/تعديل)
        $('#expense-form').on('submit', function(e) {
            e.preventDefault();
            ExpensesHandler.saveExpense();
        });
        
        // حدث النقر على زر الحذف
        $(document).on('click', '.btn-delete-expense', function() {
            const expenseId = $(this).data('id');
            ExpensesHandler.confirmDeleteExpense(expenseId);
        });
        
        // حدث النقر على زر التعديل
        $(document).on('click', '.btn-edit-expense', function() {
            const expenseId = $(this).data('id');
            ExpensesHandler.showEditExpenseModal(expenseId);
        });
        
        // نموذج فئة المصروفات
        $('#category-form').on('submit', function(e) {
            e.preventDefault();
            ExpensesHandler.saveCategory();
        });
        
        // مستمع أحداث فلاتر المصروفات
        $('#branch_filter, #category_filter, #date_range_filter, #payment_method_filter').on('change', function() {
            ExpensesHandler.loadExpenses();
        });
        
        // مستمع أحداث للبحث عن المصروفات
        let searchTimeout;
        $('#expense-search').on('keyup', function() {
            const searchTerm = $(this).val().trim();
            clearTimeout(searchTimeout);
            
            if (searchTerm.length >= 2 || searchTerm.length === 0) {
                searchTimeout = setTimeout(function() {
                    ExpensesHandler.loadExpenses(searchTerm);
                }, 500);
            }
        });
        
        // حدث النقر على زر إضافة فئة جديدة
        $('#btn-add-category').on('click', function() {
            ExpensesHandler.showCategoryModal();
        });
        
        // حدث تغيير فلتر التاريخ
        $('#date_range_type').on('change', function() {
            ExpensesHandler.toggleDateRangeFields();
        });
    },
    
    /**
     * تحميل البيانات الأولية للصفحة
     */
    loadInitialData: function() {
        // تحميل فئات المصروفات
        this.loadCategories();
        
        // إذا كانت صفحة قائمة المصروفات
        if ($('#expenses-table').length) {
            this.loadExpenses();
        }
        
        // إذا كانت صفحة تعديل مصروف
        if ($('#expense-form').length && $('#expense_id').length && $('#expense_id').val() !== '') {
            const expenseId = $('#expense_id').val();
            this.loadExpenseDetails(expenseId);
        }
        
        // إذا كانت صفحة التقارير
        if ($('#expense-report-container').length) {
            this.loadExpensesReport();
        }
        
        // تفعيل فلتر التاريخ
        this.toggleDateRangeFields();
    },
    
    /**
     * تبديل حقول فلتر التاريخ حسب النوع
     */
    toggleDateRangeFields: function() {
        const dateRangeType = $('#date_range_type').val();
        
        // إخفاء جميع الحقول أولاً
        $('.date-range-field').addClass('d-none');
        
        // إظهار الحقول المناسبة حسب النوع
        switch (dateRangeType) {
            case 'custom':
                $('.custom-date-range-field').removeClass('d-none');
                break;
            case 'month':
                $('.month-year-field').removeClass('d-none');
                break;
            case 'year':
                $('.year-field').removeClass('d-none');
                break;
        }
    },
    
    /**
     * تنسيق طريقة الدفع لعرضها في الجدول
     * @param {string} paymentMethod طريقة الدفع
     * @returns {string} النص المنسق
     */
    formatPaymentMethod: function(paymentMethod) {
        switch (paymentMethod) {
            case 'cash':
                return 'نقدي';
            case 'card':
                return 'بطاقة ائتمان';
            case 'other':
                return 'أخرى';
            default:
                return paymentMethod || '-';
        }
    },
    
    /**
     * إنشاء أزرار الإجراءات للمصروف
     * @param {Object} expense بيانات المصروف
     * @returns {string} HTML للأزرار
     */
    generateExpenseActions: function(expense) {
        let actionsHtml = '<div class="btn-group" role="group">';
        
        // زر التعديل
        actionsHtml += `<button type="button" class="btn btn-sm btn-primary btn-edit-expense" 
                         data-id="${expense.id}" title="تعديل">
                          <i class="fa fa-edit"></i>
                        </button>`;
        
        // زر حذف
        actionsHtml += `<button type="button" class="btn btn-sm btn-danger btn-delete-expense" 
                         data-id="${expense.id}" title="حذف">
                          <i class="fa fa-trash"></i>
                        </button>`;
        
        actionsHtml += '</div>';
        
        return actionsHtml;
    },
    
    /**
     * تحميل قائمة المصروفات
     * @param {string} searchTerm - كلمة البحث (اختياري)
     */
    loadExpenses: function(searchTerm = '') {
        const tableElement = $('#expenses-table');
        if (!tableElement.length) return;
        
        const dataTable = tableElement.DataTable();
        
        // عرض مؤشر التحميل
        showLoading();
        
        // جمع معلمات الفلترة
        const data = {
            action: 'list',
            category_id: $('#category_filter').val() || '',
            branch_id: $('#branch_filter').val() || '',
            payment_method: $('#payment_method_filter').val() || ''
        };
        
        // إضافة فلتر التاريخ حسب النوع
        const dateRangeType = $('#date_range_type').val();
        if (dateRangeType === 'custom') {
            data.start_date = $('#start_date').val();
            data.end_date = $('#end_date').val();
        } else if (dateRangeType === 'month') {
            data.month = $('#month').val();
            data.year = $('#year').val();
        } else if (dateRangeType === 'year') {
            data.year = $('#year_only').val();
        }
        
        // إضافة مصطلح البحث إذا وجد
        if (searchTerm) {
            data.search = searchTerm;
        }
        
        // استخدام AjaxHandler للحصول على البيانات
        AjaxHandler.get('expenses.php', data)
            .then(response => {
                // إخفاء مؤشر التحميل
                hideLoading();
                
                // مسح الجدول وإعادة تعبئته
                dataTable.clear();
                
                if (response.expenses && response.expenses.length > 0) {
                    const expenses = response.expenses.map(expense => {
                        // تعديل بيانات المصروف لعرضها في الجدول
                        return {
                            id: expense.id,
                            date: formatDate(expense.date),
                            category_name: expense.category_name || '-',
                            amount: formatCurrency(expense.amount),
                            payment_method: this.formatPaymentMethod(expense.payment_method),
                            description: expense.description || '-',
                            branch_name: expense.branch_name || '-',
                            actions: this.generateExpenseActions(expense)
                        };
                    });
                    
                    // إضافة البيانات للجدول
                    dataTable.rows.add(expenses).draw();
                    
                    // تحديث إجمالي المصروفات
                    $('#total-expenses').text(formatCurrency(response.total_amount || 0));
                    $('#expenses-count').text(response.total_count || 0);
                } else {
                    dataTable.draw();
                    $('#total-expenses').text('0.00');
                    $('#expenses-count').text('0');
                    
                    if (searchTerm) {
                        showMessage('لا توجد نتائج تطابق البحث', 'info');
                    } else {
                        showMessage('لا توجد مصروفات للعرض', 'info');
                    }
                }
            })
            .catch(error => {
                hideLoading();
                showMessage('حدث خطأ أثناء تحميل بيانات المصروفات: ' + error.message, 'error');
            });
    },
    
    /**
     * تحميل قائمة فئات المصروفات
     */
    loadCategories: function() {
        // استخدام AjaxHandler للحصول على البيانات
        AjaxHandler.get('expenses.php', { action: 'list-categories' })
            .then(response => {
                if (response.categories && response.categories.length > 0) {
                    // تحديث قائمة الفئات في عناصر الاختيار
                    const categories = response.categories;
                    this.updateCategoryDropdowns(categories);
                }
            })
            .catch(error => {
                console.error('خطأ في تحميل فئات المصروفات:', error);
            });
    },
    
    /**
     * تحديث القوائم المنسدلة للفئات
     * @param {Array} categories قائمة الفئات
     */
    updateCategoryDropdowns: function(categories) {
        // تحديث قائمة الفئات في نموذج المصروفات
        const formDropdown = $('#category_id');
        if (formDropdown.length) {
            // الاحتفاظ بالقيمة المحددة حاليًا
            const currentValue = formDropdown.val();
            
            // إفراغ القائمة
            formDropdown.empty();
            
            // إضافة خيار افتراضي فارغ
            formDropdown.append('<option value="">اختر الفئة</option>');
            
            // إضافة الفئات
            categories.forEach(category => {
                formDropdown.append(`<option value="${category.id}">${category.name}</option>`);
            });
            
            // إعادة تحديد القيمة السابقة إذا كانت موجودة
            if (currentValue) {
                formDropdown.val(currentValue);
            }
            
            // تحديث Select2 إذا كان موجودًا
            if ($.fn.select2) {
                formDropdown.trigger('change');
            }
        }
        
        // تحديث قائمة الفئات في فلتر المصروفات
        const filterDropdown = $('#category_filter');
        if (filterDropdown.length) {
            // الاحتفاظ بالقيمة المحددة حاليًا
            const currentValue = filterDropdown.val();
            
            // إفراغ القائمة
            filterDropdown.empty();
            
            // إضافة خيار افتراضي فارغ
            filterDropdown.append('<option value="">جميع الفئات</option>');
            
            // إضافة الفئات
            categories.forEach(category => {
                filterDropdown.append(`<option value="${category.id}">${category.name}</option>`);
            });
            
            // إعادة تحديد القيمة السابقة إذا كانت موجودة
            if (currentValue) {
                filterDropdown.val(currentValue);
            }
            
            // تحديث Select2 إذا كان موجودًا
            if ($.fn.select2) {
                filterDropdown.trigger('change');
            }
        }
    },
    
    /**
     * عرض نافذة تعديل المصروف
     * @param {number} expenseId معرف المصروف
     */
    showEditExpenseModal: function(expenseId) {
        // عرض مؤشر التحميل
        showLoading();
        
        // استخدام AjaxHandler للحصول على بيانات المصروف
        AjaxHandler.get('expenses.php', { action: 'view', id: expenseId })
            .then(response => {
                hideLoading();
                
                if (response.expense) {
                    // عرض نافذة التعديل مع بيانات المصروف
                    this.showExpenseModal(response.expense);
                } else {
                    showMessage('لم يتم العثور على بيانات المصروف', 'error');
                }
            })
            .catch(error => {
                hideLoading();
                showMessage('حدث خطأ أثناء تحميل بيانات المصروف: ' + error.message, 'error');
            });
    },
    
    /**
     * عرض نافذة إضافة/تعديل المصروف
     * @param {Object} expense بيانات المصروف (اختياري لحالة التعديل)
     */
    showExpenseModal: function(expense = null) {
        const isEdit = expense !== null;
        const modalTitle = isEdit ? 'تعديل المصروف' : 'إضافة مصروف جديد';
        
        // إنشاء نافذة المصروف
        const modalHtml = `
        <div class="modal fade" id="expenseModal" tabindex="-1" role="dialog" aria-labelledby="expenseModalLabel" aria-hidden="true">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="expenseModalLabel">${modalTitle}</h5>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <div class="modal-body">
                        <form id="expense-modal-form">
                            ${isEdit ? `<input type="hidden" name="id" value="${expense.id}">` : ''}
                            
                            <div class="form-group">
                                <label for="modal-category_id">فئة المصروف</label>
                                <select class="form-control select2" id="modal-category_id" name="category_id" required>
                                    <option value="">اختر الفئة</option>
                                    ${this.getCategoryOptions(expense ? expense.category_id : null)}
                                </select>
                            </div>
                            
                            <div class="form-group">
                                <label for="modal-amount">المبلغ</label>
                                <input type="number" class="form-control" id="modal-amount" name="amount" step="0.01" required value="${isEdit ? expense.amount : ''}">
                            </div>
                            
                            <div class="form-group">
                                <label for="modal-date">التاريخ</label>
                                <input type="text" class="form-control datepicker" id="modal-date" name="date" required value="${isEdit ? expense.date : formatDate(new Date())}">
                            </div>
                            
                            <div class="form-group">
                                <label for="modal-payment_method">طريقة الدفع</label>
                                <select class="form-control" id="modal-payment_method" name="payment_method">
                                    <option value="cash" ${isEdit && expense.payment_method === 'cash' ? 'selected' : ''}>نقدي</option>
                                    <option value="card" ${isEdit && expense.payment_method === 'card' ? 'selected' : ''}>بطاقة ائتمان</option>
                                    <option value="other" ${isEdit && expense.payment_method === 'other' ? 'selected' : ''}>أخرى</option>
                                </select>
                            </div>
                            
                            <div class="form-group">
                                <label for="modal-description">الوصف</label>
                                <textarea class="form-control" id="modal-description" name="description" rows="2">${isEdit ? (expense.description || '') : ''}</textarea>
                            </div>
                            
                            <div class="form-group">
                                <label for="modal-branch_id">الفرع</label>
                                <select class="form-control" id="modal-branch_id" name="branch_id">
                                    ${this.getBranchOptions(expense ? expense.branch_id : null)}
                                </select>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-primary" onclick="ExpensesHandler.saveModalExpense(${isEdit})">حفظ</button>
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">إلغاء</button>
                    </div>
                </div>
            </div>
        </div>
        `;
        
        // إضافة النافذة للصفحة
        $('body').append(modalHtml);
        
        // تهيئة عناصر التاريخ
        $('.datepicker').datepicker({
            format: 'yyyy-mm-dd',
            autoclose: true,
            todayHighlight: true,
            rtl: true
        });
        
        // تهيئة عناصر القائمة المنسدلة
        if ($.fn.select2) {
            $('.select2').select2({
                dir: "rtl",
                width: '100%',
                dropdownParent: $('#expenseModal')
            });
        }
        
        // عرض النافذة
        $('#expenseModal').modal('show');
        
        // إزالة النافذة عند إغلاقها
        $('#expenseModal').on('hidden.bs.modal', function() {
            $(this).remove();
        });
    },
    
    /**
     * الحصول على خيارات الفئات
     * @param {number} selectedId معرف الفئة المحددة (اختياري)
     * @returns {string} HTML لخيارات الفئات
     */
    getCategoryOptions: function(selectedId = null) {
        let options = '';
        
        // البحث عن قائمة الفئات في الصفحة
        const categoryDropdown = $('#category_id');
        if (categoryDropdown.length) {
            // نسخ الخيارات من القائمة الموجودة
            categoryDropdown.find('option').each(function() {
                const id = $(this).val();
                const name = $(this).text();
                
                if (id) { // تجاهل الخيار الفارغ
                    const selected = id == selectedId ? 'selected' : '';
                    options += `<option value="${id}" ${selected}>${name}</option>`;
                }
            });
        }
        
        return options;
    },
    
    /**
     * الحصول على خيارات الفروع
     * @param {number} selectedId معرف الفرع المحدد (اختياري)
     * @returns {string} HTML لخيارات الفروع
     */
    getBranchOptions: function(selectedId = null) {
        let options = '';
        
        // البحث عن قائمة الفروع في الصفحة
        const branchDropdown = $('#branch_id, #branch_filter');
        if (branchDropdown.length) {
            // نسخ الخيارات من القائمة الموجودة
            branchDropdown.find('option').each(function() {
                const id = $(this).val();
                const name = $(this).text();
                
                if (id) { // تجاهل الخيار الفارغ
                    const selected = id == selectedId ? 'selected' : '';
                    options += `<option value="${id}" ${selected}>${name}</option>`;
                }
            });
        }
        
        // إذا لم تكن هناك خيارات، إضافة خيار الفرع الحالي
        if (!options) {
            const currentBranchId = _currentBranchId || ''; // متغير عام للفرع الحالي
            const currentBranchName = _currentBranchName || 'الفرع الحالي'; // متغير عام لاسم الفرع الحالي
            options = `<option value="${currentBranchId}" selected>${currentBranchName}</option>`;
        }
        
        return options;
    },
    
    /**
     * حفظ المصروف من النافذة المنبثقة
     * @param {boolean} isEdit هل هو تعديل أم إضافة
     */
    saveModalExpense: function(isEdit) {
        // جمع بيانات النموذج
        const formData = new FormData(document.getElementById('expense-modal-form'));
        
        // تحديد الإجراء والطريقة حسب العملية
        const action = isEdit ? 'update' : 'add';
        const method = isEdit ? 'PUT' : 'POST';
        
        // إضافة الإجراء
        formData.append('action', action);
        
        // عرض مؤشر التحميل
        showLoading();
        
        // تحويل FormData إلى كائن JSON
        const data = {};
        formData.forEach((value, key) => {
            data[key] = value;
        });
        
        // استخدام AjaxHandler لإرسال البيانات
        AjaxHandler[method.toLowerCase()]('expenses.php', data)
            .then(response => {
                hideLoading();
                
                if (response.status === 'success') {
                    // إغلاق النافذة
                    $('#expenseModal').modal('hide');
                    
                    showMessage(response.message, 'success');
                    
                    // تحديث قائمة المصروفات
                    this.loadExpenses();
                } else {
                    showMessage(response.message || 'حدث خطأ أثناء حفظ بيانات المصروف', 'error');
                }
            })
            .catch(error => {
                hideLoading();
                showMessage('حدث خطأ في الاتصال بالخادم: ' + error.message, 'error');
            });
    },
    
    /**
     * تأكيد حذف مصروف
     * @param {number} expenseId معرف المصروف
     */
    confirmDeleteExpense: function(expenseId) {
        if (confirm('هل أنت متأكد من حذف هذا المصروف؟ لا يمكن التراجع عن هذا الإجراء.')) {
            this.deleteExpense(expenseId);
        }
    },
    
    /**
     * حذف مصروف
     * @param {number} expenseId معرف المصروف
     */
    deleteExpense: function(expenseId) {
        // عرض مؤشر التحميل
        showLoading();
        
        // استخدام AjaxHandler لحذف المصروف
        AjaxHandler.delete('expenses.php', { id: expenseId })
            .then(response => {
                hideLoading();
                
                if (response.status === 'success') {
                    showMessage(response.message, 'success');
                    
                    // تحديث قائمة المصروفات
                    this.loadExpenses();
                } else {
                    showMessage(response.message || 'حدث خطأ أثناء حذف المصروف', 'error');
                }
            })
            .catch(error => {
                hideLoading();
                showMessage('حدث خطأ في الاتصال بالخادم: ' + error.message, 'error');
            });
    },
    
    /**
     * عرض نافذة إضافة/تعديل فئة المصروفات
     * @param {Object} category بيانات الفئة (اختياري لحالة التعديل)
     */
    showCategoryModal: function(category = null) {
        const isEdit = category !== null;
        const modalTitle = isEdit ? 'تعديل فئة المصروفات' : 'إضافة فئة مصروفات جديدة';
        
        // إنشاء نافذة الفئة
        const modalHtml = `
        <div class="modal fade" id="categoryModal" tabindex="-1" role="dialog" aria-labelledby="categoryModalLabel" aria-hidden="true">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="categoryModalLabel">${modalTitle}</h5>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <div class="modal-body">
                        <form id="category-modal-form">
                            ${isEdit ? `<input type="hidden" name="id" value="${category.id}">` : ''}
                            
                            <div class="form-group">
                                <label for="category-name">اسم الفئة</label>
                                <input type="text" class="form-control" id="category-name" name="name" required value="${isEdit ? category.name : ''}">
                            </div>
                            
                            <div class="form-group">
                                <label for="category-description">الوصف</label>
                                <textarea class="form-control" id="category-description" name="description" rows="2">${isEdit ? (category.description || '') : ''}</textarea>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-primary" onclick="ExpensesHandler.saveCategory()">حفظ</button>
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">إلغاء</button>
                    </div>
                </div>
            </div>
        </div>
        `;
        
// إضافة النافذة للصفحة
$('body').append(modalHtml);
        
// عرض النافذة
$('#categoryModal').modal('show');

// إزالة النافذة عند إغلاقها
$('#categoryModal').on('hidden.bs.modal', function() {
    $(this).remove();
});
},

/**
* حفظ فئة المصروفات
*/
saveCategory: function() {
// جمع بيانات النموذج
const formData = new FormData(document.getElementById('category-modal-form'));

// تحديد الإجراء
const isEdit = formData.has('id');
const action = isEdit ? 'update-category' : 'add-category';

// إضافة الإجراء
formData.append('action', action);

// عرض مؤشر التحميل
showLoading();

// تحويل FormData إلى كائن JSON
const data = {};
formData.forEach((value, key) => {
    data[key] = value;
});

// استخدام AjaxHandler لإرسال البيانات
AjaxHandler.post('expenses.php', data)
    .then(response => {
        hideLoading();
        
        if (response.status === 'success') {
            // إغلاق النافذة
            $('#categoryModal').modal('hide');
            
            showMessage(response.message, 'success');
            
            // تحديث قائمة الفئات
            this.loadCategories();
        } else {
            showMessage(response.message || 'حدث خطأ أثناء حفظ بيانات الفئة', 'error');
        }
    })
    .catch(error => {
        hideLoading();
        showMessage('حدث خطأ في الاتصال بالخادم: ' + error.message, 'error');
    });
},

/**
* تحميل بيانات المصروف
* @param {number} expenseId معرف المصروف
*/
loadExpenseDetails: function(expenseId) {
// عرض مؤشر التحميل
showLoading();

// استخدام AjaxHandler للحصول على بيانات المصروف
AjaxHandler.get('expenses.php', { action: 'view', id: expenseId })
    .then(response => {
        hideLoading();
        
        if (response.expense) {
            // ملء نموذج المصروف
            this.fillExpenseForm(response.expense);
        } else {
            showMessage('لم يتم العثور على بيانات المصروف', 'error');
        }
    })
    .catch(error => {
        hideLoading();
        showMessage('حدث خطأ أثناء تحميل بيانات المصروف: ' + error.message, 'error');
    });
},

/**
* ملء نموذج المصروف ببيانات
* @param {Object} expense بيانات المصروف
*/
fillExpenseForm: function(expense) {
$('#category_id').val(expense.category_id);
$('#amount').val(expense.amount);
$('#date').val(expense.date);
$('#payment_method').val(expense.payment_method);
$('#description').val(expense.description);
$('#branch_id').val(expense.branch_id);

// تحديث القوائم المنسدلة إذا كانت موجودة
if ($.fn.select2) {
    $('#category_id, #branch_id').trigger('change');
}
},

/**
* حفظ بيانات المصروف
*/
saveExpense: function() {
// جمع بيانات النموذج
const form = $('#expense-form');
const formData = new FormData(form[0]);

// تحديد الإجراء
const expenseId = $('#expense_id').val();
const action = expenseId ? 'update' : 'add';
const method = expenseId ? 'PUT' : 'POST';

// إضافة الإجراء
formData.append('action', action);

// التحقق من صحة البيانات
if (!this.validateExpenseForm()) {
    return;
}

// عرض مؤشر التحميل
showLoading();

// تحويل FormData إلى كائن JSON
const data = {};
formData.forEach((value, key) => {
    data[key] = value;
});

// استخدام AjaxHandler لإرسال البيانات
AjaxHandler[method.toLowerCase()]('expenses.php', data)
    .then(response => {
        hideLoading();
        
        if (response.status === 'success') {
            showMessage(response.message, 'success');
            
            // إذا كان إضافة، انتقل لصفحة القائمة بعد ثانيتين
            if (!expenseId) {
                setTimeout(function() {
                    window.location.href = 'index.php';
                }, 2000);
            }
        } else {
            showMessage(response.message || 'حدث خطأ أثناء حفظ بيانات المصروف', 'error');
        }
    })
    .catch(error => {
        hideLoading();
        showMessage('حدث خطأ في الاتصال بالخادم: ' + error.message, 'error');
    });
},

/**
* التحقق من صحة بيانات نموذج المصروف
* @returns {boolean} نتيجة التحقق
*/
validateExpenseForm: function() {
// الحقول المطلوبة
const categoryId = $('#category_id').val();
const amount = $('#amount').val();
const date = $('#date').val();

let isValid = true;

// التحقق من الفئة
if (!categoryId) {
    this.showFieldError('category_id', 'فئة المصروف مطلوبة');
    isValid = false;
} else {
    this.clearFieldError('category_id');
}

// التحقق من المبلغ
if (!amount) {
    this.showFieldError('amount', 'المبلغ مطلوب');
    isValid = false;
} else if (parseFloat(amount) <= 0) {
    this.showFieldError('amount', 'المبلغ يجب أن يكون أكبر من صفر');
    isValid = false;
} else {
    this.clearFieldError('amount');
}

// التحقق من التاريخ
if (!date) {
    this.showFieldError('date', 'التاريخ مطلوب');
    isValid = false;
} else {
    this.clearFieldError('date');
}

return isValid;
},

/**
* عرض خطأ في حقل معين
* @param {string} fieldId معرف الحقل
* @param {string} errorMessage رسالة الخطأ
*/
showFieldError: function(fieldId, errorMessage) {
const field = $('#' + fieldId);
field.addClass('is-invalid');

// إضافة رسالة الخطأ
let errorDiv = field.next('.invalid-feedback');
if (errorDiv.length === 0) {
    field.after('<div class="invalid-feedback">' + errorMessage + '</div>');
} else {
    errorDiv.text(errorMessage);
}
},

/**
* إزالة خطأ من حقل معين
* @param {string} fieldId معرف الحقل
*/
clearFieldError: function(fieldId) {
const field = $('#' + fieldId);
field.removeClass('is-invalid');
},

/**
* تحميل تقرير المصروفات
*/
loadExpensesReport: function() {
// عرض مؤشر التحميل
showLoading();

// جمع معلمات الفلترة
const data = {
    action: 'report',
    branch_id: $('#branch_filter').val() || ''
};

// إضافة فلتر التاريخ حسب النوع
const dateRangeType = $('#date_range_type').val();
if (dateRangeType === 'custom') {
    data.start_date = $('#start_date').val();
    data.end_date = $('#end_date').val();
} else if (dateRangeType === 'month') {
    data.month = $('#month').val();
    data.year = $('#year').val();
} else if (dateRangeType === 'year') {
    data.year = $('#year_only').val();
}

// استخدام AjaxHandler للحصول على البيانات
AjaxHandler.get('expenses.php', data)
    .then(response => {
        hideLoading();
        
        if (response.status === 'success' && response.report) {
            // عرض تقرير المصروفات
            this.renderExpensesReport(response.report);
        } else {
            showMessage('لا توجد بيانات للتقرير', 'info');
        }
    })
    .catch(error => {
        hideLoading();
        showMessage('حدث خطأ أثناء تحميل بيانات التقرير: ' + error.message, 'error');
    });
},

/**
* عرض تقرير المصروفات في الصفحة
* @param {Object} report بيانات التقرير
*/
renderExpensesReport: function(report) {
const reportContainer = $('#expense-report-container');
if (!reportContainer.length) return;

// عرض إجمالي المصروفات
$('#total-expenses-report').text(formatCurrency(report.total_amount || 0));

// عرض تفاصيل المصروفات حسب الفئة
if (report.by_category && report.by_category.length > 0) {
    let categoryHtml = '<div class="table-responsive"><table class="table table-bordered table-striped">';
    categoryHtml += '<thead><tr><th>الفئة</th><th>المبلغ</th><th>النسبة</th></tr></thead>';
    categoryHtml += '<tbody>';
    
    report.by_category.forEach(category => {
        const percentage = (category.amount / report.total_amount * 100).toFixed(2);
        categoryHtml += `<tr>
            <td>${category.category_name || 'غير معروف'}</td>
            <td>${formatCurrency(category.amount)}</td>
            <td>${percentage}%</td>
        </tr>`;
    });
    
    categoryHtml += '</tbody></table></div>';
    $('#expenses-by-category').html(categoryHtml);
    
    // عرض الرسم البياني للمصروفات حسب الفئة
    this.renderCategoryChart(report.by_category);
} else {
    $('#expenses-by-category').html('<div class="alert alert-info">لا توجد بيانات للمصروفات حسب الفئة</div>');
}

// عرض تفاصيل المصروفات حسب طريقة الدفع
if (report.by_payment_method && report.by_payment_method.length > 0) {
    let paymentHtml = '<div class="table-responsive"><table class="table table-bordered table-striped">';
    paymentHtml += '<thead><tr><th>طريقة الدفع</th><th>المبلغ</th><th>النسبة</th></tr></thead>';
    paymentHtml += '<tbody>';
    
    report.by_payment_method.forEach(payment => {
        const percentage = (payment.amount / report.total_amount * 100).toFixed(2);
        paymentHtml += `<tr>
            <td>${this.formatPaymentMethod(payment.payment_method)}</td>
            <td>${formatCurrency(payment.amount)}</td>
            <td>${percentage}%</td>
        </tr>`;
    });
    
    paymentHtml += '</tbody></table></div>';
    $('#expenses-by-payment').html(paymentHtml);
    
    // عرض الرسم البياني للمصروفات حسب طريقة الدفع
    this.renderPaymentChart(report.by_payment_method);
} else {
    $('#expenses-by-payment').html('<div class="alert alert-info">لا توجد بيانات للمصروفات حسب طريقة الدفع</div>');
}

// عرض مخطط المصروفات عبر الزمن
if (report.by_date && report.by_date.length > 0) {
    this.renderTimelineChart(report.by_date);
}
},

/**
* عرض مخطط المصروفات حسب الفئة
* @param {Array} categoryData بيانات المصروفات حسب الفئة
*/
renderCategoryChart: function(categoryData) {
if (!categoryData || !$('#category-chart').length) return;

const ctx = document.getElementById('category-chart').getContext('2d');

// إزالة المخطط السابق إذا وجد
if (window.categoryChart) {
    window.categoryChart.destroy();
}

window.categoryChart = new Chart(ctx, {
    type: 'pie',
    data: {
        labels: categoryData.map(item => item.category_name || 'غير معروف'),
        datasets: [{
            data: categoryData.map(item => item.amount),
            backgroundColor: [
                'rgba(255, 99, 132, 0.8)',
                'rgba(54, 162, 235, 0.8)',
                'rgba(255, 206, 86, 0.8)',
                'rgba(75, 192, 192, 0.8)',
                'rgba(153, 102, 255, 0.8)',
                'rgba(255, 159, 64, 0.8)',
                'rgba(199, 199, 199, 0.8)',
                'rgba(83, 102, 255, 0.8)',
                'rgba(40, 159, 201, 0.8)',
                'rgba(210, 105, 30, 0.8)'
            ],
            borderColor: [
                'rgba(255, 99, 132, 1)',
                'rgba(54, 162, 235, 1)',
                'rgba(255, 206, 86, 1)',
                'rgba(75, 192, 192, 1)',
                'rgba(153, 102, 255, 1)',
                'rgba(255, 159, 64, 1)',
                'rgba(199, 199, 199, 1)',
                'rgba(83, 102, 255, 1)',
                'rgba(40, 159, 201, 1)',
                'rgba(210, 105, 30, 1)'
            ],
            borderWidth: 1
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        title: {
            display: true,
            text: 'المصروفات حسب الفئة',
            position: 'top',
            fontFamily: 'Arial',
            fontSize: 16,
            fontColor: '#666'
        },
        legend: {
            position: 'right',
            labels: {
                fontFamily: 'Arial',
                fontSize: 12
            }
        },
        tooltips: {
            callbacks: {
                label: function(tooltipItem, data) {
                    const dataset = data.datasets[tooltipItem.datasetIndex];
                    const total = dataset.data.reduce((acc, value) => acc + value, 0);
                    const currentValue = dataset.data[tooltipItem.index];
                    const percentage = Math.round((currentValue / total) * 100);
                    return `${data.labels[tooltipItem.index]}: ${formatCurrency(currentValue)} (${percentage}%)`;
                }
            }
        }
    }
});
},

/**
* عرض مخطط المصروفات حسب طريقة الدفع
* @param {Array} paymentData بيانات المصروفات حسب طريقة الدفع
*/
renderPaymentChart: function(paymentData) {
if (!paymentData || !$('#payment-chart').length) return;

const ctx = document.getElementById('payment-chart').getContext('2d');

// إزالة المخطط السابق إذا وجد
if (window.paymentChart) {
    window.paymentChart.destroy();
}

window.paymentChart = new Chart(ctx, {
    type: 'doughnut',
    data: {
        labels: paymentData.map(item => this.formatPaymentMethod(item.payment_method)),
        datasets: [{
            data: paymentData.map(item => item.amount),
            backgroundColor: [
                'rgba(54, 162, 235, 0.8)',
                'rgba(255, 99, 132, 0.8)',
                'rgba(255, 206, 86, 0.8)'
            ],
            borderColor: [
                'rgba(54, 162, 235, 1)',
                'rgba(255, 99, 132, 1)',
                'rgba(255, 206, 86, 1)'
            ],
            borderWidth: 1
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        title: {
            display: true,
            text: 'المصروفات حسب طريقة الدفع',
            position: 'top',
            fontFamily: 'Arial',
            fontSize: 16,
            fontColor: '#666'
        },
        legend: {
            position: 'right',
            labels: {
                fontFamily: 'Arial',
                fontSize: 12
            }
        },
        tooltips: {
            callbacks: {
                label: function(tooltipItem, data) {
                    const dataset = data.datasets[tooltipItem.datasetIndex];
                    const total = dataset.data.reduce((acc, value) => acc + value, 0);
                    const currentValue = dataset.data[tooltipItem.index];
                    const percentage = Math.round((currentValue / total) * 100);
                    return `${data.labels[tooltipItem.index]}: ${formatCurrency(currentValue)} (${percentage}%)`;
                }
            }
        }
    }
});
},

/**
* عرض مخطط المصروفات عبر الزمن
* @param {Array} timelineData بيانات المصروفات عبر الزمن
*/
renderTimelineChart: function(timelineData) {
if (!timelineData || !$('#timeline-chart').length) return;

const ctx = document.getElementById('timeline-chart').getContext('2d');

// إزالة المخطط السابق إذا وجد
if (window.timelineChart) {
    window.timelineChart.destroy();
}

window.timelineChart = new Chart(ctx, {
    type: 'line',
    data: {
        labels: timelineData.map(item => item.date),
        datasets: [{
            label: 'المصروفات',
            data: timelineData.map(item => item.amount),
            backgroundColor: 'rgba(75, 192, 192, 0.2)',
            borderColor: 'rgba(75, 192, 192, 1)',
            borderWidth: 2,
            fill: true,
            pointRadius: 4,
            pointBackgroundColor: 'rgba(75, 192, 192, 1)',
            pointBorderColor: '#fff',
            pointBorderWidth: 1,
            pointHoverRadius: 6,
            pointHoverBackgroundColor: 'rgba(75, 192, 192, 1)',
            pointHoverBorderColor: '#fff',
            pointHoverBorderWidth: 2,
            tension: 0.4
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        title: {
            display: true,
            text: 'المصروفات عبر الزمن',
            position: 'top',
            fontFamily: 'Arial',
            fontSize: 16,
            fontColor: '#666'
        },
        legend: {
            display: false
        },
        scales: {
            xAxes: [{
                ticks: {
                    fontFamily: 'Arial',
                    fontSize: 12
                },
                gridLines: {
                    drawOnChartArea: false
                }
            }],
            yAxes: [{
                ticks: {
                    fontFamily: 'Arial',
                    fontSize: 12,
                    beginAtZero: true,
                    callback: function(value) {
                        return formatCurrency(value);
                    }
                },
                gridLines: {
                    drawBorder: false
                }
            }]
        },
        tooltips: {
            callbacks: {
                label: function(tooltipItem, data) {
                    return `المصروفات: ${formatCurrency(tooltipItem.yLabel)}`;
                }
            }
        }
    }
});
},

/**
* تهيئة المخططات البيانية
*/
initializeCharts: function() {
// تهيئة مخططات التقارير إذا كانت موجودة
if ($('#expense-report-container').length) {
    this.loadExpensesReport();
}
}
};

/**
* تنسيق التاريخ
* @param {string|Date} date التاريخ
* @returns {string} التاريخ المنسق
*/
function formatDate(date) {
if (!date) return '';

const d = typeof date === 'string' ? new Date(date) : date;

if (isNaN(d.getTime())) return date; // إعادة النص الأصلي إذا كان التاريخ غير صالح

const year = d.getFullYear();
const month = String(d.getMonth() + 1).padStart(2, '0');
const day = String(d.getDate()).padStart(2, '0');

return `${year}-${month}-${day}`;
}

/**
* تنسيق المبالغ المالية
* @param {number} amount المبلغ
* @returns {string} المبلغ المنسق
*/
function formatCurrency(amount) {
if (!amount) return '0.00';
return parseFloat(amount).toFixed(2);
}

/**
* عرض مؤشر التحميل
*/
function showLoading() {
// تحقق مما إذا كان هناك مؤشر تحميل موجود بالفعل
if ($('#loading-spinner').length === 0) {
const spinner = `
<div id="loading-spinner" class="loading-overlay">
    <div class="spinner-border text-primary" role="status">
        <span class="sr-only">جاري التحميل...</span>
    </div>
</div>
`;

$('body').append(spinner);
}

$('#loading-spinner').show();
}

/**
* إخفاء مؤشر التحميل
*/
function hideLoading() {
$('#loading-spinner').hide();
}

/**
* عرض رسالة للمستخدم
* @param {string} message نص الرسالة
* @param {string} type نوع الرسالة (success, error, info, warning)
*/
function showMessage(message, type = 'info') {
// تحويل النوع إلى صنف Bootstrap
let alertClass = 'alert-info';

switch (type) {
case 'success':
    alertClass = 'alert-success';
    break;
case 'error':
    alertClass = 'alert-danger';
    break;
case 'warning':
    alertClass = 'alert-warning';
    break;
}

// إنشاء عنصر التنبيه
const alertHtml = `
<div class="alert ${alertClass} alert-dismissible fade show">
${message}
<button type="button" class="close" data-dismiss="alert" aria-label="Close">
    <span aria-hidden="true">&times;</span>
</button>
</div>
`;

// إضافة التنبيه للصفحة
const alertContainer = $('#alerts-container');

if (alertContainer.length) {
alertContainer.append(alertHtml);

// إزالة التنبيه بعد 5 ثوانٍ
setTimeout(() => {
    alertContainer.find('.alert').first().alert('close');
}, 5000);
} else {
// إذا لم يكن هناك حاوية للتنبيهات، استخدم تنبيه منبثق من Toastr إن كان متاحاً
if (typeof toastr !== 'undefined') {
    toastr[type](message);
} else {
    alert(message);
}
}
}