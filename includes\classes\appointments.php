<?php
/**
 * فئة الفرع
 * تتعامل مع إدارة فروع صالون الحلاقة
 */

// منع الوصول المباشر للملف
if (!defined('BASEPATH')) {
    exit('لا يمكن الوصول المباشر لهذا الملف');
}
class Appointment {
    // كائن قاعدة البيانات
    private $db;

    /**
     * نشئ
     * @param Database $database كائن قاعدة البيانات
     */
    public function __construct(Database $database) {
        $this->db = $database;
    }

    /** إنشاء موعد جديد
    * 
    * @param array $data بيانات الموعد
    * @return int|bool معرف الموعد الجديد أو false في حالة الفشل
    */
   public function createAppointment($data) {
       // استرجاع مدة الخدمة
       $this->db->prepare("SELECT duration FROM services WHERE id = :service_id");
       $this->db->bind(':service_id', $data['service_id']);
       $serviceDuration = $this->db->fetchColumn();
       
       if (!$serviceDuration) {
           $serviceDuration = 30; // المدة الافتراضية بالدقائق
       }
       
       // حساب وقت الانتهاء
       $endTime = date('H:i:s', strtotime($data['start_time']) + ($serviceDuration * 60));
       
       // إنشاء الموعد
       $this->db->prepare("INSERT INTO appointments (
                   customer_id, 
                   employee_id, 
                   service_id, 
                   date, 
                   start_time, 
                   end_time, 
                   status, 
                   notes, 
                   branch_id, 
                   created_at
               ) VALUES (
                   :customer_id, 
                   :employee_id, 
                   :service_id, 
                   :date, 
                   :start_time, 
                   :end_time, 
                   :status, 
                   :notes, 
                   :branch_id, 
                   NOW()
               )");
       
       $this->db->bind(':customer_id', $data['customer_id']);
       $this->db->bind(':employee_id', $data['employee_id']);
       $this->db->bind(':service_id', $data['service_id']);
       $this->db->bind(':date', $data['date']);
       $this->db->bind(':start_time', $data['start_time']);
       $this->db->bind(':end_time', $endTime);
       $this->db->bind(':status', $data['status'] ?? 'booked');
       $this->db->bind(':notes', $data['notes'] ?? null);
       $this->db->bind(':branch_id', $data['branch_id']);
       
       if ($this->db->execute()) {
           return (int)$this->db->lastInsertId();
       }
       
       return false;
   }

    /**
     * تحديث بيانات موعد
     * 
     * @param int $appointmentId معرف الموعد
     * @param array $data بيانات الموعد المحدثة
     * @return bool نجاح العملية
     * @throws Exception في حالة وجود خطأ في البيانات
     */
    public function updateAppointment($appointmentId, $data) {
        // التحقق من وجود الموعد
        $originalAppointment = $this->getAppointmentById($appointmentId);
        if (!$originalAppointment) {
            throw new Exception('الموعد غير موجود');
        }
        
        // التحقق من البيانات الأساسية
        if (empty($data['customer_id'])) {
            throw new Exception('معرف العميل مطلوب');
        }
        
        if (empty($data['service_id'])) {
            throw new Exception('معرف الخدمة مطلوب');
        }
        
        if (empty($data['date'])) {
            throw new Exception('تاريخ الموعد مطلوب');
        }
        
        if (empty($data['start_time'])) {
            throw new Exception('وقت بدء الموعد مطلوب');
        }
        
        // استرجاع مدة الخدمة
        $this->db->prepare("SELECT duration FROM services WHERE id = :service_id");
        $this->db->bind(':service_id', $data['service_id']);
        $serviceDuration = $this->db->fetchColumn();
        
        if (!$serviceDuration) {
            $serviceDuration = 30; // المدة الافتراضية بالدقائق
        }
        
        // حساب وقت الانتهاء
        $endTime = date('H:i:s', strtotime($data['start_time']) + ($serviceDuration * 60));
        
        // تجهيز البيانات للتحديث
        $updateData = [
            'id' => $appointmentId,
            'customer_id' => $data['customer_id'],
            'service_id' => $data['service_id'],
            'employee_id' => $data['employee_id'] ?? null,
            'date' => $data['date'],
            'start_time' => $data['start_time'],
            'end_time' => $endTime,
            'status' => $data['status'] ?? 'booked',
            'notes' => $data['notes'] ?? null,
            'branch_id' => $data['branch_id'] ?? ($_SESSION['user_branch_id'] ?? $originalAppointment['branch_id'])
        ];
        
        // تحديث بيانات الموعد
        $query = "UPDATE appointments 
                SET customer_id = :customer_id, 
                    service_id = :service_id, 
                    employee_id = :employee_id, 
                    date = :date, 
                    start_time = :start_time, 
                    end_time = :end_time, 
                    status = :status, 
                    notes = :notes, 
                    branch_id = :branch_id, 
                    updated_at = NOW() 
                WHERE id = :id";
        
        $this->db->prepare($query);
        
        // ربط المعلمات
        foreach ($updateData as $key => $value) {
            $this->db->bind(':' . $key, $value);
        }
        
        $result = $this->db->execute();
        
        // تسجيل التحديث في سجل التغييرات
        if ($result) {
            $this->logAppointmentUpdate($appointmentId, $originalAppointment, $updateData);
            
            // إرسال إشعار للعميل إذا تغير الموعد
            if ($originalAppointment['date'] != $data['date'] || 
                $originalAppointment['start_time'] != $data['start_time']) {
                $this->sendAppointmentUpdateNotification($appointmentId, $updateData);
            }
        }
        
        return $result;
    }

    /**
     * استرجاع بيانات موعد محدد
     * 
     * @param int $appointmentId معرف الموعد
     * @return array|false بيانات الموعد أو false إذا لم يتم العثور عليه
     */
    public function getAppointmentById($appointmentId) {
        $this->db->prepare("SELECT a.*, 
                       c.name AS customer_name, 
                       c.phone AS customer_phone,
                       s.name AS service_name, 
                       s.duration,
                       e.name AS employee_name,
                       b.name AS branch_name
                FROM appointments a
                LEFT JOIN customers c ON a.customer_id = c.id
                LEFT JOIN services s ON a.service_id = s.id
                LEFT JOIN employees e ON a.employee_id = e.id
                LEFT JOIN branches b ON a.branch_id = b.id
                WHERE a.id = :id");
        $this->db->bind(':id', $appointmentId);
        
        return $this->db->fetch();
    }
    /**
     * استرجاع قائمة المواعيد
     * 
     * @param array $filters فلاتر البحث
     * @return array قائمة المواعيد
     */
    public function getAppointments($filters = []) {
        // إنشاء مسار ملف السجل
        $logDir = dirname(__DIR__, 2) . '/logs';
        if (!is_dir($logDir)) {
            mkdir($logDir, 0755, true);
        }
        $logFile = $logDir . '/appointments_get_' . date('Y-m-d') . '.log';
        
        // دالة لتسجيل المعلومات في الملف
        $logToFile = function($message) use ($logFile) {
            $timestamp = date('Y-m-d H:i:s');
            file_put_contents(
                $logFile, 
                "[$timestamp] $message" . PHP_EOL, 
                FILE_APPEND
            );
        };
        
        // بدء تسجيل التصحيح
        $logToFile("بدء استرجاع المواعيد مع الفلاتر: " . json_encode($filters, JSON_UNESCAPED_UNICODE));
        error_log("بدء استرجاع المواعيد مع الفلاتر: " . json_encode($filters, JSON_UNESCAPED_UNICODE));
        
        $sql = "SELECT a.*, 
                    c.name as customer_name, 
                    e.name as employee_name, 
                    s.name as service_name,
                    b.name as branch_name
                FROM appointments a
                LEFT JOIN customers c ON a.customer_id = c.id
                LEFT JOIN employees e ON a.employee_id = e.id
                LEFT JOIN services s ON a.service_id = s.id
                LEFT JOIN branches b ON a.branch_id = b.id
                WHERE 1=1";
        
        $params = [];
        
        // إضافة فلاتر
        if (!empty($filters['branch_id'])) {
            $sql .= " AND a.branch_id = :branch_id";
            $params[':branch_id'] = $filters['branch_id'];
            $logToFile("إضافة فلتر الفرع: " . $filters['branch_id']);
        }
        
        if (!empty($filters['customer_id'])) {
            $sql .= " AND a.customer_id = :customer_id";
            $params[':customer_id'] = $filters['customer_id'];
            $logToFile("إضافة فلتر العميل: " . $filters['customer_id']);
        }
        
        if (!empty($filters['employee_id'])) {
            $sql .= " AND a.employee_id = :employee_id";
            $params[':employee_id'] = $filters['employee_id'];
            $logToFile("إضافة فلتر الموظف: " . $filters['employee_id']);
        }
        
        if (!empty($filters['service_id'])) {
            $sql .= " AND a.service_id = :service_id";
            $params[':service_id'] = $filters['service_id'];
            $logToFile("إضافة فلتر الخدمة: " . $filters['service_id']);
        }
        
        if (!empty($filters['status'])) {
            $sql .= " AND a.status = :status";
            $params[':status'] = $filters['status'];
            $logToFile("إضافة فلتر الحالة: " . $filters['status']);
        }
        
        // فلتر التاريخ
        if (!empty($filters['date']) && !empty($filters['exact_date'])) {
            $sql .= " AND a.date = :date";
            $params[':date'] = $filters['date'];
            $logToFile("إضافة فلتر التاريخ المحدد: " . $filters['date']);
        } else {
            if (!empty($filters['start_date'])) {
                $sql .= " AND a.date >= :start_date";
                $params[':start_date'] = $filters['start_date'];
                $logToFile("إضافة فلتر تاريخ البداية: " . $filters['start_date']);
            }
            
            if (!empty($filters['end_date'])) {
                $sql .= " AND a.date <= :end_date";
                $params[':end_date'] = $filters['end_date'];
                $logToFile("إضافة فلتر تاريخ النهاية: " . $filters['end_date']);
            }
        }
        
        // ترتيب النتائج
        $sql .= " ORDER BY a.date DESC, a.start_time ASC";
        $logToFile("إضافة ترتيب النتائج: a.date DESC, a.start_time ASC");
        
        // حدود النتائج
        if (!empty($filters['limit'])) {
            $sql .= " LIMIT :limit";
            $params[':limit'] = (int)$filters['limit'];
            $logToFile("إضافة حد النتائج: " . $filters['limit']);
            
            if (!empty($filters['offset'])) {
                $sql .= " OFFSET :offset";
                $params[':offset'] = (int)$filters['offset'];
                $logToFile("إضافة إزاحة النتائج: " . $filters['offset']);
            }
        }
        
        $logToFile("استعلام SQL النهائي: " . $sql);
        $logToFile("المعلمات: " . json_encode($params, JSON_UNESCAPED_UNICODE));
        error_log("استعلام SQL النهائي: " . $sql);
        
        // تنفيذ الاستعلام
        $this->db->prepare($sql);
        
        // ربط المعلمات
        foreach ($params as $param => $value) {
            $this->db->bind($param, $value);
            $logToFile("ربط المعلمة: $param = $value");
        }
        
        // استرجاع النتائج
        $results = $this->db->fetchAll();
        $logToFile("تم استرجاع " . count($results) . " موعد");
        error_log("تم استرجاع " . count($results) . " موعد");
        
        return $results;
    }

    /**
     * استرجاع عدد المواعيد
     * 
     * @param array $filters معايير الفلترة
     * @return int عدد المواعيد
     */
    public function getAppointmentsCount($filters = []) {
        $sql = "SELECT COUNT(*) FROM appointments a WHERE 1=1";
        
        $params = [];
        
        // إضافة فلاتر
        if (!empty($filters['branch_id'])) {
            $sql .= " AND a.branch_id = :branch_id";
            $params[':branch_id'] = $filters['branch_id'];
        }
        
        if (!empty($filters['customer_id'])) {
            $sql .= " AND a.customer_id = :customer_id";
            $params[':customer_id'] = $filters['customer_id'];
        }
        
        if (!empty($filters['employee_id'])) {
            $sql .= " AND a.employee_id = :employee_id";
            $params[':employee_id'] = $filters['employee_id'];
        }
        
        if (!empty($filters['service_id'])) {
            $sql .= " AND a.service_id = :service_id";
            $params[':service_id'] = $filters['service_id'];
        }
        
        if (!empty($filters['status'])) {
            $sql .= " AND a.status = :status";
            $params[':status'] = $filters['status'];
        }
        
        // فلتر التاريخ المحدد
        if (!empty($filters['date'])) {
            $sql .= " AND a.date = :date";
            $params[':date'] = $filters['date'];
        } else {
            // فلتر نطاق التاريخ
            if (!empty($filters['start_date'])) {
                $sql .= " AND a.date >= :start_date";
                $params[':start_date'] = $filters['start_date'];
            }
            
            if (!empty($filters['end_date'])) {
                $sql .= " AND a.date <= :end_date";
                $params[':end_date'] = $filters['end_date'];
            }
        }
        
        // تنفيذ الاستعلام
        $this->db->prepare($sql);
        
        // ربط المعلمات
        foreach ($params as $param => $value) {
            $this->db->bind($param, $value);
        }
        
        // استرجاع العدد
        return (int)$this->db->fetchColumn();
    }
    /**
     * تنسيق بيانات الموعد للعرض
     * 
     * @param array $appointment بيانات الموعد
     * @return array البيانات المنسقة
     */
    public function formatAppointmentData($appointment) {
        // التأكد من وجود جميع المفاتيح المطلوبة
        $formattedData = [
            'id' => $appointment['id'] ?? 0,
            'customer_id' => $appointment['customer_id'] ?? 0,
            'customer_name' => $appointment['customer_name'] ?? 'غير محدد',
            'customer_phone' => $appointment['customer_phone'] ?? '',
            'service_id' => $appointment['service_id'] ?? 0,
            'service_name' => $appointment['service_name'] ?? 'غير محدد',
            'employee_id' => $appointment['employee_id'] ?? 0,
            'employee_name' => $appointment['employee_name'] ?? 'غير محدد',
            'branch_id' => $appointment['branch_id'] ?? 0,
            'branch_name' => $appointment['branch_name'] ?? 'غير محدد',
            'date' => $appointment['date'] ?? date('Y-m-d'),
            'start_time' => $appointment['start_time'] ?? '00:00:00',
            'end_time' => $appointment['end_time'] ?? '00:00:00',
            'status' => $appointment['status'] ?? 'booked',
            'notes' => $appointment['notes'] ?? '',
            'created_at' => $appointment['created_at'] ?? '',
            'updated_at' => $appointment['updated_at'] ?? ''
        ];
        
        // إضافة معلومات إضافية مفيدة للواجهة
        $formattedData['status_text'] = $this->getStatusText($formattedData['status']);
        $formattedData['status_class'] = $this->getStatusClass($formattedData['status']);
        $formattedData['formatted_date'] = date('Y/m/d', strtotime($formattedData['date']));
        $formattedData['formatted_time'] = date('h:i A', strtotime($formattedData['start_time'])) . 
                                          ' - ' . 
                                          date('h:i A', strtotime($formattedData['end_time']));
        
        return $formattedData;
    }
    
    /**
     * الحصول على النص المقابل لحالة الموعد
     * 
     * @param string $status رمز الحالة
     * @return string النص المقابل للحالة
     */
    public function getStatusText($status) {
        $statusMap = [
            'booked' => 'محجوز',
            'waiting' => 'في الانتظار',
            'completed' => 'مكتمل',
            'cancelled' => 'ملغي'
        ];
        
        return $statusMap[$status] ?? 'غير معروف';
    }
    
    /**
     * الحصول على فئة CSS المقابلة لحالة الموعد
     * 
     * @param string $status رمز الحالة
     * @return string فئة CSS
     */
    public function getStatusClass($status) {
        $classMap = [
            'booked' => 'badge-primary',
            'waiting' => 'badge-warning',
            'completed' => 'badge-success',
            'cancelled' => 'badge-danger'
        ];
        
        return $classMap[$status] ?? 'badge-secondary';
    }
    /**
     * استرجاع المواعيد حسب تاريخ محدد
     * 
     * @param string $date التاريخ المطلوب
     * @param array $filters فلاتر إضافية
     * @return array قائمة المواعيد
     */
    public function getAppointmentsByDate($date, $filters = []) {
        $sql = "SELECT a.*, 
                c.name as customer_name, 
                e.name as employee_name, 
                s.name as service_name,
                b.name as branch_name
            FROM appointments a
            LEFT JOIN customers c ON a.customer_id = c.id
            LEFT JOIN employees e ON a.employee_id = e.id
            LEFT JOIN services s ON a.service_id = s.id
            LEFT JOIN branches b ON a.branch_id = b.id
            WHERE a.date = :date";
    
        $params = [':date' => $date];
    
        // إضافة فلاتر إضافية
        if (!empty($filters['branch_id'])) {
            $sql .= " AND a.branch_id = :branch_id";
            $params[':branch_id'] = $filters['branch_id'];
        }
        
        if (!empty($filters['employee_id'])) {
            $sql .= " AND a.employee_id = :employee_id";
            $params[':employee_id'] = $filters['employee_id'];
        }
        
        if (!empty($filters['status'])) {
            $sql .= " AND a.status = :status";
            $params[':status'] = $filters['status'];
        }
        
        // ترتيب النتائج حسب وقت البدء
        $sql .= " ORDER BY a.start_time ASC";
        
        // تنفيذ الاستعلام
        $this->db->prepare($sql);
        
        // ربط المعلمات
        foreach ($params as $param => $value) {
            $this->db->bind($param, $value);
        }
        
        return $this->db->fetchAll();
    }
    /**
     * تحديث حالة موعد
     * 
     * @param int $appointmentId معرف الموعد
     * @param string $status الحالة الجديدة
     * @return bool نجاح العملية
     */
    public function updateAppointmentStatus($appointmentId, $status) {
        // التحقق من صحة الحالة
        $validStatuses = ['booked', 'waiting', 'completed', 'cancelled'];
        if (!in_array($status, $validStatuses)) {
            return false;
        }
        
        // تحديث حالة الموعد
        $this->db->prepare("UPDATE appointments 
                          SET status = :status, 
                              updated_at = NOW() 
                          WHERE id = :id");
        
        $this->db->bind(':id', $appointmentId);
        $this->db->bind(':status', $status);
        
        return $this->db->execute();
    }
    /**
     * إلغاء موعد
     * 
     * @param int $appointmentId معرف الموعد
     * @return bool نجاح العملية
     */
    public function cancelAppointment($appointmentId) {
        // إنشاء مسار ملف السجل
        $logDir = dirname(__DIR__, 2) . '/logs';
        if (!is_dir($logDir)) {
            mkdir($logDir, 0755, true);
        }
        $logFile = $logDir . '/appointments_cancel_' . date('Y-m-d') . '.log';
        
        // دالة لتسجيل المعلومات في الملف
        $logToFile = function($message) use ($logFile) {
            $timestamp = date('Y-m-d H:i:s');
            file_put_contents(
                $logFile, 
                "[$timestamp] $message" . PHP_EOL, 
                FILE_APPEND
            );
        };
        
        // بدء تسجيل التصحيح
        $logToFile("بدء إلغاء الموعد بالمعرف: $appointmentId");
        error_log("بدء إلغاء الموعد بالمعرف: $appointmentId");
        
        // التحقق من وجود الموعد
        $appointment = $this->getAppointmentById($appointmentId);
        if (!$appointment) {
            $logToFile("فشل في العثور على الموعد بالمعرف: $appointmentId");
            error_log("فشل في العثور على الموعد بالمعرف: $appointmentId");
            return false;
        }
        
        $logToFile("تم العثور على الموعد: " . json_encode($appointment, JSON_UNESCAPED_UNICODE));
        error_log("تم العثور على الموعد: " . json_encode($appointment, JSON_UNESCAPED_UNICODE));
        
        // تحديث حالة الموعد إلى ملغي
        $this->db->prepare("UPDATE appointments SET status = 'cancelled', updated_at = NOW() WHERE id = :id");
        $this->db->bind(':id', $appointmentId);
        
        $result = $this->db->execute();
        $logToFile("نتيجة إلغاء الموعد: " . ($result ? "نجاح" : "فشل"));
        error_log("نتيجة إلغاء الموعد: " . ($result ? "نجاح" : "فشل"));
        
        return $result;
    }

    /**
     * التحقق من توفر الفترة الزمنية
     * 
     * @param string $date التاريخ
     * @param string $startTime وقت البدء
     * @param string $endTime وقت الانتهاء
     * @param int|null $employeeId معرف الموظف
     * @param int|null $excludeAppointmentId معرف الموعد المستثنى من التحقق
     * @return bool توفر الفترة الزمنية
     */
    private function isTimeSlotAvailable($date, $startTime, $endTime, $employeeId, $excludeAppointmentId = null) {
        // إذا لم يتم تحديد موظف، فالفترة متاحة
        if (empty($employeeId)) {
            return true;
        }
        
        $sql = "SELECT COUNT(*) FROM appointments 
                WHERE date = :date 
                AND employee_id = :employee_id 
                AND status != 'cancelled'
                AND (
                    (start_time <= :start_time AND end_time > :start_time) OR
                    (start_time < :end_time AND end_time >= :end_time) OR
                    (start_time >= :start_time AND end_time <= :end_time)
                )";
        
        $params = [
            ':date' => $date,
            ':employee_id' => $employeeId,
            ':start_time' => $startTime,
            ':end_time' => $endTime
        ];
        
        // استثناء الموعد الحالي من التحقق
        if ($excludeAppointmentId) {
            $sql .= " AND id != :exclude_id";
            $params[':exclude_id'] = $excludeAppointmentId;
        }
        
        $this->db->prepare($sql);
        
        foreach ($params as $param => $value) {
            $this->db->bind($param, $value);
        }
        
        $count = $this->db->fetchColumn();
        
        return $count == 0;
    }

    /**
     * تسجيل تحديث الموعد
     * 
     * @param int $appointmentId معرف الموعد
     * @param array $oldData البيانات القديمة
     * @param array $newData البيانات الجديدة
     * @return void
     */
    private function logAppointmentUpdate($appointmentId, $oldData, $newData) {
        $logDir = dirname(__DIR__, 2) . '/logs';
        if (!is_dir($logDir)) {
            mkdir($logDir, 0755, true);
        }
        
        $logFile = $logDir . '/appointments_updates_' . date('Y-m-d') . '.log';
        $timestamp = date('Y-m-d H:i:s');
        
        $changes = [];
        foreach ($newData as $key => $value) {
            if (isset($oldData[$key]) && $oldData[$key] != $value) {
                $changes[$key] = [
                    'old' => $oldData[$key],
                    'new' => $value
                ];
            }
        }
        
        $message = "[$timestamp] تحديث الموعد #$appointmentId: " . json_encode($changes, JSON_UNESCAPED_UNICODE);
        file_put_contents($logFile, $message . PHP_EOL, FILE_APPEND);
    }

    /**
     * إرسال إشعار بتحديث الموعد
     * 
     * @param int $appointmentId معرف الموعد
     * @param array $appointmentData بيانات الموعد
     * @return void
     */
    private function sendAppointmentUpdateNotification($appointmentId, $appointmentData) {
        // الحصول على بيانات العميل
        $this->db->prepare("SELECT name, phone, email FROM customers WHERE id = :customer_id");
        $this->db->bind(':customer_id', $appointmentData['customer_id']);
        $customer = $this->db->fetch();
        
        if (!$customer) {
            return;
        }
        
        // الحصول على اسم الخدمة
        $this->db->prepare("SELECT name FROM services WHERE id = :service_id");
        $this->db->bind(':service_id', $appointmentData['service_id']);
        $serviceName = $this->db->fetchColumn();
        
        // إنشاء رسالة الإشعار
        $message = "عزيزي {$customer['name']}، تم تحديث موعدك للخدمة: $serviceName ليصبح بتاريخ {$appointmentData['date']} الساعة {$appointmentData['start_time']}";
        
        // يمكن إضافة كود لإرسال رسالة SMS أو بريد إلكتروني هنا
        // مثال: $this->sendSMS($customer['phone'], $message);
        
        // تسجيل الإشعار
        $logDir = dirname(__DIR__, 2) . '/logs';
        $logFile = $logDir . '/notifications_' . date('Y-m-d') . '.log';
        $timestamp = date('Y-m-d H:i:s');
        
        $logMessage = "[$timestamp] إشعار تحديث الموعد #$appointmentId للعميل {$customer['name']}: $message";
        file_put_contents($logFile, $logMessage . PHP_EOL, FILE_APPEND);
    }

    /**
     * الحصول على الأوقات المتاحة للحجز
     * 
     * @param int $serviceId معرف الخدمة
     * @param string $date التاريخ
     * @param int $branchId معرف الفرع
     * @param int $employeeId معرف الموظف (اختياري)
     * @return array قائمة بالأوقات المتاحة
     */
    public function getAvailableTimes($serviceId, $date, $branchId, $employeeId = 0) {
        // الحصول على معلومات الخدمة
        $this->db->prepare("SELECT duration FROM services WHERE id = :service_id");
        $this->db->bind(':service_id', $serviceId);
        $service = $this->db->fetch();
        
        if (!$service) {
            return [];
        }
        
        $serviceDuration = $service['duration'] ?? 30; // المدة الافتراضية 30 دقيقة
        
        // الحصول على معلومات الفرع
        $this->db->prepare("SELECT id FROM branches WHERE id = :branch_id AND is_active = 1");
        $this->db->bind(':branch_id', $branchId);
        $branch = $this->db->fetch();
        
        if (!$branch) {
            return [];
        }
        
        // استخدام ساعات عمل افتراضية للفرع
        $openTime = '09:00:00';  // 9 صباحًا
        $closeTime = '21:00:00'; // 9<|im_start|>
        
        // يمكن استرجاع ساعات العمل من جدول settings إذا كان موجودًا
        $this->db->prepare("SELECT setting_value FROM settings WHERE setting_key = :key");
        $this->db->bind(':key', 'branch_' . $branchId . '_open_time');
        $customOpenTime = $this->db->fetchColumn();
        
        if ($customOpenTime) {
            $openTime = $customOpenTime;
        }
        
        $this->db->prepare("SELECT setting_value FROM settings WHERE setting_key = :key");
        $this->db->bind(':key', 'branch_' . $branchId . '_close_time');
        $customCloseTime = $this->db->fetchColumn();
        
        if ($customCloseTime) {
            $closeTime = $customCloseTime;
        }
        
        // التحقق من أيام الإجازة
        $dayOfWeek = date('N', strtotime($date)); // 1 (الاثنين) إلى 7 (الأحد)
        
        // التحقق من إذا كان اليوم إجازة للفرع
        $this->db->prepare("SELECT setting_value FROM settings WHERE setting_key = :key");
        $this->db->bind(':key', 'branch_' . $branchId . '_day_off_' . $dayOfWeek);
        $isDayOff = $this->db->fetchColumn();
        
        if ($isDayOff == '1') {
            return []; // الفرع مغلق في هذا اليوم
        }
        
        // إنشاء قائمة بجميع الفترات الزمنية المتاحة
        $availableSlots = [];
        $currentTime = strtotime($openTime);
        $endTime = strtotime($closeTime);
        
        // إضافة فترات زمنية بفاصل 30 دقيقة
        $interval = 30 * 60; // 30 دقيقة بالثواني
        
        while ($currentTime + ($serviceDuration * 60) <= $endTime) {
            $timeSlot = date('H:i:s', $currentTime);
            $availableSlots[] = $timeSlot;
            $currentTime += $interval;
        }
        
        // إذا كان التاريخ هو اليوم، استبعاد الأوقات السابقة للوقت الحالي
        if (date('Y-m-d') === $date) {
            $currentHour = date('H:i:s');
            foreach ($availableSlots as $index => $slot) {
                if ($slot <= $currentHour) {
                    unset($availableSlots[$index]);
                }
            }
        }
        
        // إعادة ترتيب المصفوفة
        return array_values($availableSlots);
    }
}
