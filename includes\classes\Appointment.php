<?php
/**
 * فئة إدارة المواعيد
 * تتعامل مع عمليات إنشاء وتعديل وحذف وعرض المواعيد
 */

// منع الوصول المباشر للملف
if (!defined('BASEPATH')) {
    exit('لا يمكن الوصول المباشر لهذا الملف');
}

class Appointment {
    /**
     * كائن الاتصال بقاعدة البيانات
     * @var Database
     */
    private $db;

    /**
     * المُنشئ: يأخذ كائن الاتصال بقاعدة البيانات
     *
     * @param Database $db كائن الاتصال بقاعدة البيانات
     */
    public function __construct($db) {
        $this->db = $db;
    }

    /**
     * إنشاء موعد جديد
     *
     * @param array $data بيانات الموعد
     * @return int|bool معرف الموعد الجديد أو false في حالة الفشل
     */
    public function createAppointment($data) {
        try {
            // التحقق من توفر الوقت
            if (!$this->checkAppointmentAvailability($data)) {
                return false;
            }

            // استرجاع مدة الخدمة
            $this->db->prepare("SELECT duration FROM services WHERE id = :service_id");
            $this->db->bind(':service_id', $data['service_id']);
            $serviceDuration = $this->db->fetchColumn();

            if (!$serviceDuration) {
                $serviceDuration = 30; // المدة الافتراضية بالدقائق
            }

            // حساب وقت الانتهاء
            $endTime = date('H:i:s', strtotime($data['start_time']) + ($serviceDuration * 60));

            // إنشاء الموعد
            $currentDateTime = date('Y-m-d H:i:s');

            $this->db->prepare("INSERT INTO appointments (
                            customer_id,
                            employee_id,
                            service_id,
                            date,
                            start_time,
                            end_time,
                            status,
                            notes,
                            branch_id,
                            created_at
                        ) VALUES (
                            :customer_id,
                            :employee_id,
                            :service_id,
                            :date,
                            :start_time,
                            :end_time,
                            :status,
                            :notes,
                            :branch_id,
                            :created_at
                        )");

            $this->db->bind(':customer_id', $data['customer_id']);
            $this->db->bind(':employee_id', $data['employee_id']);
            $this->db->bind(':service_id', $data['service_id']);
            $this->db->bind(':date', $data['date']);
            $this->db->bind(':start_time', $data['start_time']);
            $this->db->bind(':end_time', $endTime);
            $this->db->bind(':status', $data['status'] ?? 'booked');
            $this->db->bind(':notes', $data['notes'] ?? null);
            $this->db->bind(':branch_id', $data['branch_id']);
            $this->db->bind(':created_at', $currentDateTime);

            $this->db->execute();
            return $this->db->lastInsertId();
        } catch (Exception $e) {
            error_log('خطأ في إنشاء موعد جديد: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * التحقق من توفر وقت محدد لموعد
     *
     * @param array $data بيانات الموعد
     * @return bool متوفر أم لا
     */
    private function checkAppointmentAvailability($data) {
        try {
            // استخراج البيانات
            $branchId = intval($data['branch_id']);
            $serviceId = intval($data['service_id']);
            $employeeId = intval($data['employee_id']);
            $date = $data['date'];
            $startTime = $data['start_time'];

            // استرجاع مدة الخدمة
            $this->db->prepare("SELECT duration FROM services WHERE id = :service_id");
            $this->db->bind(':service_id', $serviceId);
            $serviceDuration = $this->db->fetchColumn();

            if (!$serviceDuration) {
                $serviceDuration = 30; // المدة الافتراضية بالدقائق
            }

            // حساب وقت الانتهاء
            $endTime = date('H:i:s', strtotime($startTime) + ($serviceDuration * 60));

            // التحقق من تعارض المواعيد للموظف
            if ($employeeId > 0) {
                $this->db->prepare("SELECT COUNT(*) FROM appointments 
                                WHERE employee_id = :employee_id 
                                AND date = :date 
                                AND status != 'cancelled'
                                AND (
                                    (start_time <= :start_time AND end_time > :start_time) OR
                                    (start_time < :end_time AND end_time >= :end_time) OR
                                    (start_time >= :start_time AND end_time <= :end_time)
                                )");
                $this->db->bind(':employee_id', $employeeId);
                $this->db->bind(':date', $date);
                $this->db->bind(':start_time', $startTime);
                $this->db->bind(':end_time', $endTime);

                $count = $this->db->fetchColumn();
                if ($count > 0) {
                    return false; // الموظف مشغول في هذا الوقت
                }
            }

            return true; // الوقت متاح
        } catch (Exception $e) {
            error_log('خطأ في التحقق من توفر الموعد: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * تحديث موعد
     *
     * @param int $appointmentId معرف الموعد
     * @param array $data بيانات الموعد الجديدة
     * @return bool نجاح العملية
     */
    public function updateAppointment($appointmentId, $data) {
        try {
            // الحصول على بيانات الموعد الحالي
            $this->db->prepare("SELECT * FROM appointments WHERE id = :id");
            $this->db->bind(':id', $appointmentId);
            $originalAppointment = $this->db->fetch();

            if (!$originalAppointment) {
                return false;
            }

            // إنشاء استعلام التحديث
            $sql = "UPDATE appointments SET ";
            $params = [];
            $updateFields = [];

            // تحديد الحقول المراد تحديثها
            foreach ($data as $key => $value) {
                if ($key !== 'id') { // تجاهل معرف الموعد
                    $updateFields[] = "$key = :$key";
                    $params[":$key"] = $value;
                }
            }

            // إضافة معرف الموعد للاستعلام
            $sql .= implode(', ', $updateFields) . " WHERE id = :id";
            $params[':id'] = $appointmentId;

            // تنفيذ الاستعلام
            $this->db->prepare($sql);
            foreach ($params as $key => $value) {
                $this->db->bind($key, $value);
            }

            return $this->db->execute();
        } catch (Exception $e) {
            error_log('خطأ في تحديث الموعد: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * حذف موعد
     *
     * @param int $appointmentId معرف الموعد
     * @return bool نجاح العملية
     */
    public function deleteAppointment($appointmentId) {
        try {
            $this->db->prepare("DELETE FROM appointments WHERE id = :id");
            $this->db->bind(':id', $appointmentId);
            return $this->db->execute();
        } catch (Exception $e) {
            error_log('خطأ في حذف الموعد: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * الحصول على موعد بواسطة المعرف
     *
     * @param int $appointmentId معرف الموعد
     * @return array|bool بيانات الموعد أو false في حالة عدم وجوده
     */
    public function getAppointmentById($appointmentId) {
        try {
            $this->db->prepare("SELECT a.*,
                               c.name AS customer_name,
                               c.phone AS customer_phone,
                               c.email AS customer_email,
                               s.name AS service_name,
                               s.duration,
                               e.name AS employee_name,
                               b.name AS branch_name
                        FROM appointments a
                        LEFT JOIN customers c ON a.customer_id = c.id
                        LEFT JOIN services s ON a.service_id = s.id
                        LEFT JOIN employees e ON a.employee_id = e.id
                        LEFT JOIN branches b ON a.branch_id = b.id
                        WHERE a.id = :id");
            $this->db->bind(':id', $appointmentId);
            return $this->db->fetch();
        } catch (Exception $e) {
            error_log('خطأ في استرجاع الموعد: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * استرجاع قائمة المواعيد
     *
     * @param array $filters فلاتر البحث
     * @return array قائمة المواعيد
     */
    public function getAppointments($filters = []) {
        try {
            $sql = "SELECT a.*,
                           c.name AS customer_name,
                           c.phone AS customer_phone,
                           s.name AS service_name,
                           s.duration,
                           e.name AS employee_name,
                           b.name AS branch_name
                    FROM appointments a
                    LEFT JOIN customers c ON a.customer_id = c.id
                    LEFT JOIN services s ON a.service_id = s.id
                    LEFT JOIN employees e ON a.employee_id = e.id
                    LEFT JOIN branches b ON a.branch_id = b.id";

            $whereConditions = [];
            $params = [];

            // تطبيق الفلاتر
            if (!empty($filters['branch_id'])) {
                $whereConditions[] = "a.branch_id = :branch_id";
                $params[':branch_id'] = $filters['branch_id'];
            }

            if (!empty($filters['date'])) {
                $whereConditions[] = "a.date = :date";
                $params[':date'] = $filters['date'];
            }

            if (!empty($filters['customer_id'])) {
                $whereConditions[] = "a.customer_id = :customer_id";
                $params[':customer_id'] = $filters['customer_id'];
            }

            if (!empty($filters['employee_id'])) {
                $whereConditions[] = "a.employee_id = :employee_id";
                $params[':employee_id'] = $filters['employee_id'];
            }

            if (!empty($filters['status'])) {
                $whereConditions[] = "a.status = :status";
                $params[':status'] = $filters['status'];
            }

            // إضافة شروط البحث إلى الاستعلام
            if (!empty($whereConditions)) {
                $sql .= " WHERE " . implode(' AND ', $whereConditions);
            }

            // ترتيب النتائج
            $sql .= " ORDER BY a.date DESC, a.start_time ASC";

            // تنفيذ الاستعلام
            $this->db->prepare($sql);
            foreach ($params as $key => $value) {
                $this->db->bind($key, $value);
            }

            return $this->db->fetchAll();
        } catch (Exception $e) {
            error_log('خطأ في استرجاع المواعيد: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * استرجاع المواعيد حسب تاريخ محدد
     *
     * @param string $date التاريخ المطلوب
     * @param array $filters فلاتر إضافية
     * @return array قائمة المواعيد
     */
    public function getAppointmentsByDate($date, $filters = []) {
        try {
            $sql = "SELECT a.*, 
                    c.name as customer_name, 
                    c.phone as customer_phone,
                    e.name as employee_name, 
                    s.name as service_name,
                    b.name as branch_name
                FROM appointments a
                LEFT JOIN customers c ON a.customer_id = c.id
                LEFT JOIN employees e ON a.employee_id = e.id
                LEFT JOIN services s ON a.service_id = s.id
                LEFT JOIN branches b ON a.branch_id = b.id
                WHERE a.date = :date";
        
            $params = [':date' => $date];
        
            // إضافة فلاتر إضافية
            if (!empty($filters['branch_id'])) {
                $sql .= " AND a.branch_id = :branch_id";
                $params[':branch_id'] = $filters['branch_id'];
            }
        
            if (!empty($filters['status'])) {
                $sql .= " AND a.status = :status";
                $params[':status'] = $filters['status'];
            }
        
            // ترتيب النتائج حسب وقت البدء
            $sql .= " ORDER BY a.start_time ASC";
        
            // تنفيذ الاستعلام
            $this->db->prepare($sql);
            foreach ($params as $key => $value) {
                $this->db->bind($key, $value);
            }
        
            return $this->db->fetchAll();
        } catch (Exception $e) {
            error_log('خطأ في استرجاع المواعيد حسب التاريخ: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * الحصول على المواعيد القادمة التي تحتاج إلى تذكير
     *
     * @param string $startTime وقت البداية
     * @param string $endTime وقت النهاية
     * @return array قائمة المواعيد
     */
    public function getUpcomingAppointmentsForReminder($startTime, $endTime) {
        try {
            $sql = "SELECT a.*,
                           c.name AS customer_name,
                           c.phone AS customer_phone,
                           c.email AS customer_email,
                           s.name AS service_name,
                           e.name AS employee_name,
                           b.name AS branch_name,
                           b.country_code
                    FROM appointments a
                    LEFT JOIN customers c ON a.customer_id = c.id
                    LEFT JOIN services s ON a.service_id = s.id
                    LEFT JOIN employees e ON a.employee_id = e.id
                    LEFT JOIN branches b ON a.branch_id = b.id
                    WHERE a.status = 'booked'
                    AND a.reminder_sent = 0
                    AND CONCAT(a.date, ' ', a.start_time) BETWEEN :start_time AND :end_time";

            $this->db->prepare($sql);
            $this->db->bind(':start_time', $startTime);
            $this->db->bind(':end_time', $endTime);

            return $this->db->fetchAll();
        } catch (Exception $e) {
            error_log('خطأ في استرجاع المواعيد القادمة للتذكير: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * إلغاء موعد
     *
     * @param int $appointmentId معرف الموعد
     * @return bool نجاح العملية
     */
    public function cancelAppointment($appointmentId) {
        try {
            return $this->updateAppointment($appointmentId, [
                'status' => 'cancelled'
            ]);
        } catch (Exception $e) {
            error_log('خطأ في إلغاء الموعد: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * تنسيق بيانات الموعد للعرض
     *
     * @param array $appointment بيانات الموعد
     * @return array البيانات المنسقة
     */
    public function formatAppointmentData($appointment) {
        // التأكد من وجود جميع المفاتيح المطلوبة
        $formattedData = [
            'id' => $appointment['id'] ?? 0,
            'customer_id' => $appointment['customer_id'] ?? 0,
            'customer_name' => $appointment['customer_name'] ?? 'غير محدد',
            'customer_phone' => $appointment['customer_phone'] ?? '',
            'service_id' => $appointment['service_id'] ?? 0,
            'service_name' => $appointment['service_name'] ?? 'غير محدد',
            'employee_id' => $appointment['employee_id'] ?? 0,
            'employee_name' => $appointment['employee_name'] ?? 'غير محدد',
            'branch_id' => $appointment['branch_id'] ?? 0,
            'branch_name' => $appointment['branch_name'] ?? 'غير محدد',
            'date' => $appointment['date'] ?? '',
            'start_time' => $appointment['start_time'] ?? '',
            'end_time' => $appointment['end_time'] ?? '',
            'status' => $appointment['status'] ?? 'booked',
            'notes' => $appointment['notes'] ?? '',
            'created_at' => $appointment['created_at'] ?? '',
            'updated_at' => $appointment['updated_at'] ?? ''
        ];

        return $formattedData;
    }
}
