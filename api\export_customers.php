<?php
/**
 * واجهة برمجة التطبيقات (API) لتصدير بيانات العملاء إلى ملف Excel
 */

// تحديد المسار الأساسي
define('BASEPATH', true);

// استدعاء ملف التهيئة
require_once __DIR__ . '/../config/init.php';

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    header('HTTP/1.0 403 Forbidden');
    exit('يجب تسجيل الدخول أولاً');
}

// التحقق من صلاحية عرض العملاء
if (!hasPermission('customers_view')) {
    header('HTTP/1.0 403 Forbidden');
    exit('ليس لديك صلاحية لعرض العملاء');
}

// إنشاء كائن العميل
$customerModel = new Customer($db);

// تحديد الفلاتر
$filters = [];

// إذا كان المستخدم ليس مديرًا، يتم تقييد العرض بالفرع الخاص به
if ($_SESSION['user_role'] !== ROLE_ADMIN) {
    $filters['branch_id'] = $_SESSION['user_branch_id'];
} else if (isset($_GET['branch_id']) && !empty($_GET['branch_id'])) {
    $filters['branch_id'] = intval($_GET['branch_id']);
}

// فلتر البحث
if (isset($_GET['search']) && !empty($_GET['search'])) {
    $filters['search'] = $_GET['search'];
}

// فلتر تاريخ التسجيل
if (isset($_GET['date_from']) && !empty($_GET['date_from'])) {
    // التحقق من وجود الدالة formatDateToDb
    if (function_exists('formatDateToDb')) {
        $filters['date_from'] = formatDateToDb($_GET['date_from']);
    } else {
        // إذا لم تكن الدالة موجودة، نقوم بتنسيق التاريخ يدويًا
        $date = DateTime::createFromFormat('d/m/Y', $_GET['date_from']);
        $filters['date_from'] = $date ? $date->format('Y-m-d') : $_GET['date_from'];
    }
}

if (isset($_GET['date_to']) && !empty($_GET['date_to'])) {
    // التحقق من وجود الدالة formatDateToDb
    if (function_exists('formatDateToDb')) {
        $filters['date_to'] = formatDateToDb($_GET['date_to']);
    } else {
        // إذا لم تكن الدالة موجودة، نقوم بتنسيق التاريخ يدويًا
        $date = DateTime::createFromFormat('d/m/Y', $_GET['date_to']);
        $filters['date_to'] = $date ? $date->format('Y-m-d') : $_GET['date_to'];
    }
}

// فلتر نقاط الولاء
if (isset($_GET['loyalty_points_min']) && is_numeric($_GET['loyalty_points_min'])) {
    $filters['loyalty_points_min'] = intval($_GET['loyalty_points_min']);
}

if (isset($_GET['loyalty_points_max']) && is_numeric($_GET['loyalty_points_max'])) {
    $filters['loyalty_points_max'] = intval($_GET['loyalty_points_max']);
}

// فلتر عدد الزيارات
if (isset($_GET['visits_min']) && is_numeric($_GET['visits_min'])) {
    $filters['visits_min'] = intval($_GET['visits_min']);
}

if (isset($_GET['visits_max']) && is_numeric($_GET['visits_max'])) {
    $filters['visits_max'] = intval($_GET['visits_max']);
}

try {
    // الحصول على بيانات العملاء
    $customers = $customerModel->getCustomers($filters);

    // تعيين نوع المحتوى وإعدادات التنزيل
    header('Content-Type: application/vnd.ms-excel; charset=UTF-8');
    header('Content-Disposition: attachment; filename="customers_export_' . date('Y-m-d') . '.xls"');
    header('Pragma: no-cache');
    header('Expires: 0');

    // إنشاء ملف Excel
    echo '<html xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:x="urn:schemas-microsoft-com:office:excel" xmlns="http://www.w3.org/TR/REC-html40">';
    echo '<head>';
    echo '<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">';
    echo '</head>';
    echo '<body>';
    echo '<table border="1">';

    // رأس الجدول
    echo '<tr>';
    echo '<th style="background-color: #f2f2f2; font-weight: bold;">الرقم</th>';
    echo '<th style="background-color: #f2f2f2; font-weight: bold;">الاسم</th>';
    echo '<th style="background-color: #f2f2f2; font-weight: bold;">رقم الهاتف</th>';
    echo '<th style="background-color: #f2f2f2; font-weight: bold;">البريد الإلكتروني</th>';
    echo '<th style="background-color: #f2f2f2; font-weight: bold;">تاريخ الميلاد</th>';
    echo '<th style="background-color: #f2f2f2; font-weight: bold;">الفرع</th>';
    echo '<th style="background-color: #f2f2f2; font-weight: bold;">عدد الزيارات</th>';
    echo '<th style="background-color: #f2f2f2; font-weight: bold;">نقاط الولاء</th>';
    echo '<th style="background-color: #f2f2f2; font-weight: bold;">إجمالي المشتريات</th>';
    echo '<th style="background-color: #f2f2f2; font-weight: bold;">تاريخ التسجيل</th>';
    echo '<th style="background-color: #f2f2f2; font-weight: bold;">ملاحظات</th>';
    echo '</tr>';

    // بيانات العملاء
    foreach ($customers as $customer) {
        echo '<tr>';
        echo '<td>' . $customer['id'] . '</td>';
        echo '<td>' . htmlspecialchars($customer['name']) . '</td>';
        echo '<td>' . htmlspecialchars($customer['phone']) . '</td>';
        echo '<td>' . htmlspecialchars($customer['email'] ?? '') . '</td>';
        echo '<td>' . ($customer['birthday'] ? date('Y-m-d', strtotime($customer['birthday'])) : '') . '</td>';
        echo '<td>' . htmlspecialchars($customer['branch_name'] ?? '') . '</td>';
        echo '<td>' . ($customer['visits_count'] ?? 0) . '</td>';
        echo '<td>' . ($customer['loyalty_points'] ?? 0) . '</td>';
        echo '<td>' . ($customer['total_sales'] ?? 0) . '</td>';
        echo '<td>' . date('Y-m-d', strtotime($customer['created_at'])) . '</td>';
        echo '<td>' . htmlspecialchars($customer['notes'] ?? '') . '</td>';
        echo '</tr>';
    }

    echo '</table>';
    echo '</body>';
    echo '</html>';

} catch (Exception $e) {
    // تسجيل الخطأ
    error_log('خطأ أثناء تصدير بيانات العملاء: ' . $e->getMessage());

    // إعادة توجيه المستخدم مع رسالة خطأ
    header('Location: ' . BASE_URL . 'pages/customers/index.php?error=' . urlencode('حدث خطأ أثناء تصدير البيانات: ' . $e->getMessage()));
    exit;
}
