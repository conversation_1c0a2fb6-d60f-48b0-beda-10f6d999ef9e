<?php
/**
 * فئة خدمة WATI
 * تستخدم لإرسال رسائل WhatsApp باستخدام واجهة برمجة تطبيقات WATI
 */

// منع الوصول المباشر للملف
if (!defined('BASEPATH')) {
    exit('لا يمكن الوصول المباشر لهذا الملف');
}

class WatiService {
    private $db;
    private $settings;
    private $accessToken;
    private $baseUrl;
    private $enabled;
    private $logFile;

    /**
     * المُنشئ
     *
     * @param Database $db كائن قاعدة البيانات
     */
    public function __construct($db) {
        $this->db = $db;
        $this->logFile = dirname(__DIR__, 2) . '/logs/wati_messages.log';
        
        // التأكد من وجود مجلد السجلات
        $logDir = dirname($this->logFile);
        if (!is_dir($logDir)) {
            mkdir($logDir, 0755, true);
        }
        
        // تحميل الإعدادات
        $this->loadSettings();
    }

    /**
     * تحميل إعدادات WATI من قاعدة البيانات
     */
    private function loadSettings() {
        try {
            $settingsModel = new Settings($this->db);
            $allSettings = $settingsModel->getAllSettings();
            
            // استخراج إعدادات WATI
            $this->enabled = isset($allSettings['wati_enabled']) ? (bool)$allSettings['wati_enabled'] : false;
            $this->accessToken = $allSettings['wati_access_token'] ?? '';
            $this->baseUrl = $allSettings['wati_base_url'] ?? '';
            
            // تسجيل حالة التحميل
            $this->log("تم تحميل إعدادات WATI. الحالة: " . ($this->enabled ? 'مفعل' : 'غير مفعل'));
        } catch (Exception $e) {
            $this->log("خطأ في تحميل إعدادات WATI: " . $e->getMessage());
            $this->enabled = false;
        }
    }

    /**
     * إرسال رسالة WhatsApp
     *
     * @param string $to رقم الهاتف المستلم (بتنسيق دولي بدون +)
     * @param string $message نص الرسالة
     * @param array $options خيارات إضافية
     * @return bool نجاح أو فشل الإرسال
     */
    public function sendMessage($to, $message, $options = []) {
        // التحقق من تفعيل الخدمة
        if (!$this->enabled) {
            $this->log("محاولة إرسال رسالة لكن الخدمة غير مفعلة. الرقم: $to");
            return false;
        }
        
        // التحقق من وجود المعلومات المطلوبة
        if (empty($this->accessToken) || empty($this->baseUrl)) {
            $this->log("معلومات WATI غير مكتملة. لا يمكن إرسال الرسالة إلى: $to");
            return false;
        }
        
        try {
            // تنسيق رقم الهاتف
            $to = $this->formatPhoneNumber($to, $options['country_code'] ?? '+20');
            
            // تسجيل محاولة الإرسال
            $this->log("محاولة إرسال رسالة إلى $to: " . substr($message, 0, 50) . "...");
            
            // إعداد بيانات الطلب
            $endpoint = "{$this->baseUrl}/api/v1/sendSessionMessage/{$to}";
            $data = [
                'messageText' => $message
            ];
            
            // إضافة معلومات إضافية إذا كانت موجودة
            if (isset($options['template_name'])) {
                $endpoint = "{$this->baseUrl}/api/v1/sendTemplateMessage/{$to}";
                $data = [
                    'template_name' => $options['template_name'],
                    'broadcast_name' => $options['broadcast_name'] ?? 'appointment_reminder',
                    'parameters' => $options['parameters'] ?? []
                ];
            }
            
            // إرسال الطلب إلى واجهة برمجة تطبيقات WATI
            $response = $this->makeApiRequest($endpoint, $data);
            
            // التحقق من نجاح الإرسال
            $success = isset($response['result']) && $response['result'] === true;
            
            if ($success) {
                $this->log("تم إرسال الرسالة بنجاح إلى: $to");
            } else {
                $errorMessage = isset($response['info']) ? $response['info'] : 'خطأ غير معروف';
                $this->log("فشل إرسال الرسالة إلى: $to. الخطأ: $errorMessage");
            }
            
            return $success;
        } catch (Exception $e) {
            $this->log("خطأ في إرسال رسالة WhatsApp إلى $to: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * إرسال طلب إلى واجهة برمجة تطبيقات WATI
     *
     * @param string $endpoint نقطة النهاية
     * @param array $data بيانات الطلب
     * @return array استجابة الطلب
     */
    private function makeApiRequest($endpoint, $data) {
        // إعداد طلب cURL
        $ch = curl_init($endpoint);
        
        // إعداد خيارات cURL
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Authorization: Bearer ' . $this->accessToken,
            'Content-Type: application/json'
        ]);
        
        // تنفيذ الطلب
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        
        // التحقق من وجود أخطاء
        if (curl_errno($ch)) {
            $error = curl_error($ch);
            curl_close($ch);
            throw new Exception("خطأ في طلب cURL: $error");
        }
        
        // إغلاق الاتصال
        curl_close($ch);
        
        // تحليل الاستجابة
        $responseData = json_decode($response, true);
        
        // تسجيل الاستجابة
        $this->log("استجابة WATI (HTTP $httpCode): " . substr($response, 0, 200));
        
        // التحقق من نجاح الطلب
        if ($httpCode < 200 || $httpCode >= 300) {
            $errorMessage = isset($responseData['message']) ? $responseData['message'] : 'خطأ غير معروف';
            throw new Exception("خطأ في طلب WATI (HTTP $httpCode): $errorMessage");
        }
        
        return $responseData;
    }
    
    /**
     * تنسيق رقم الهاتف للاستخدام مع WhatsApp
     *
     * @param string $phone رقم الهاتف
     * @param string $countryCode رمز البلد (اختياري)
     * @return string رقم الهاتف المنسق
     */
    public function formatPhoneNumber($phone, $countryCode = '+20') {
        // إزالة أي أحرف غير رقمية
        $phone = preg_replace('/[^0-9]/', '', $phone);
        
        // إزالة الصفر الأول إذا كان موجودًا
        if (substr($phone, 0, 1) === '0') {
            $phone = substr($phone, 1);
        }
        
        // إضافة رمز البلد إذا تم تمريره
        if ($countryCode) {
            // إزالة علامة + إذا كانت موجودة
            $countryCode = str_replace('+', '', $countryCode);
            
            // إضافة رمز البلد إذا لم يكن موجودًا بالفعل
            if (strpos($phone, $countryCode) !== 0) {
                $phone = $countryCode . $phone;
            }
        }
        
        return $phone;
    }
    
    /**
     * تسجيل رسالة في ملف السجل
     *
     * @param string $message الرسالة المراد تسجيلها
     */
    private function log($message) {
        $timestamp = date('Y-m-d H:i:s');
        file_put_contents($this->logFile, "[$timestamp] $message" . PHP_EOL, FILE_APPEND);
    }
}
