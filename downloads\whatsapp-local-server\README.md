# خادم WhatsApp المحلي

هذا الخادم المحلي يسمح بإرسال رسائل WhatsApp من جهاز المستخدم بدلاً من الخادم.

## متطلبات النظام

- تثبيت [Node.js](https://nodejs.org/) (الإصدار 16 أو أحدث)
- متصفح حديث (Chrome أو Firefox)
- اتصال إنترنت مستقر
- حساب WhatsApp نشط على هاتفك

## طرق التثبيت والتشغيل

### الطريقة 1: استخدام ملفات التشغيل التلقائي

1. انقر نقرًا مزدوجًا على ملف `install-and-start.bat` لتثبيت الحزم وتشغيل الخادم
2. أو انقر نقرًا مزدوجًا على ملف `start-server.bat` إذا كنت قد قمت بتثبيت الحزم مسبقًا
3. أو انقر نقرًا مزدوجًا على ملف `start-server-cmd.bat` لتشغيل الخادم في نافذة موجه الأوامر (CMD)
4. سيفتح المتصفح تلقائيًا مع صفحة WhatsApp Web
5. قم بمسح رمز QR باستخدام تطبيق WhatsApp على هاتفك

### الطريقة 2: التثبيت والتشغيل اليدوي

1. افتح موجه الأوامر (CMD) في مجلد الخادم
2. قم بتثبيت الحزم:
   ```
   npm install
   ```
3. قم بتشغيل الخادم:
   ```
   npm start
   ```
   أو
   ```
   node whatsapp-local-server.js
   ```

## استخدام الخادم

بعد تشغيل الخادم، يمكنك استخدام النقاط النهائية التالية:

- `GET /status`: للتحقق من حالة تسجيل الدخول
- `GET /open`: لفتح WhatsApp Web يدويًا
- `POST /send`: لإرسال رسالة WhatsApp
- `POST /reset`: لإعادة تعيين جلسة WhatsApp

### إرسال رسالة

لإرسال رسالة، قم بإرسال طلب POST إلى `/send` مع البيانات التالية:

```json
{
  "phone": "رقم الهاتف مع رمز البلد",
  "message": "نص الرسالة"
}
```

مثال:

```json
{
  "phone": "201234567890",
  "message": "مرحبًا! هذه رسالة اختبار."
}
```

## ملاحظات هامة

- **مهم جدًا**: يجب أن تبقى نافذة موجه الأوامر مفتوحة أثناء استخدام الخادم. إذا أغلقتها، سيتوقف الخادم عن العمل.
- يجب أن تكون متصلاً بـ WhatsApp Web قبل إرسال الرسائل
- إذا واجهت أي مشاكل، يمكنك استخدام `/reset` لإعادة تعيين الجلسة
- إذا لم يظهر رمز QR بشكل صحيح، جرب استخدام ملف `start-server-cmd.bat` بدلاً من `start-server.bat`
