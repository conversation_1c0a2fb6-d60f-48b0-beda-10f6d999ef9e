/**
 * إدارة إعدادات WhatsApp من جانب العميل
 * يتعامل هذا الملف مع جميع عمليات WhatsApp في صفحة الإعدادات
 * مثل تسجيل الدخول، إعادة تعيين الجلسة، وإرسال رسائل اختبار
 */

const WhatsAppSettings = {
    // عنوان الخادم المحلي
    serverUrl: 'http://localhost:3000',

    // حالة آخر عملية
    lastOperationStatus: null,

    // خيارات التكوين
    options: {
        debug: true,                // تمكين رسائل التصحيح
        maxRetries: 2,              // الحد الأقصى لعدد إعادة المحاولات
        retryDelay: 2000,           // الوقت بين إعادة المحاولات (بالمللي ثانية)
        showSuccessMessage: true    // عرض رسالة نجاح للمستخدم
    },

    /**
     * تهيئة الوحدة
     * @param {Object} options خيارات التكوين
     */
    init: function(options) {
        // دمج الخيارات المقدمة مع الخيارات الافتراضية
        if (options) {
            this.options = { ...this.options, ...options };
        }

        if (this.options.debug) {
            console.log('تم تهيئة وحدة إعدادات WhatsApp');
        }

        // تسجيل الأحداث
        this._registerEvents();

        // التحقق من حالة اتصال الخادم المحلي
        this._checkServerConnection();
    },

    /**
     * تسجيل الأحداث للتفاعل مع واجهة المستخدم
     * @private
     */
    _registerEvents: function() {
        // زر تسجيل الدخول إلى WhatsApp Web
        const loginButton = document.getElementById('login_whatsapp');
        if (loginButton) {
            loginButton.addEventListener('click', (e) => {
                e.preventDefault();
                this.loginToWhatsApp();
            });
        }

        // زر إعادة تعيين جلسة WhatsApp
        const resetButton = document.getElementById('reset_whatsapp');
        if (resetButton) {
            resetButton.addEventListener('click', (e) => {
                e.preventDefault();
                this.resetWhatsAppSession();
            });
        }

        // زر إرسال رسالة اختبار
        const sendTestButton = document.getElementById('send_test_message');
        if (sendTestButton) {
            sendTestButton.addEventListener('click', (e) => {
                e.preventDefault();
                this.sendTestMessage();
            });
        }

        // التحقق من حالة تسجيل الدخول عند تحميل الصفحة
        this.checkLoginStatus();
    },

    /**
     * التحقق من اتصال الخادم المحلي
     * @private
     */
    _checkServerConnection: function() {
        try {
            const xhr = new XMLHttpRequest();
            xhr.open('GET', `${this.serverUrl}/status`, true);
            xhr.timeout = 5000; // 5 ثوانٍ كحد أقصى
            xhr.responseType = 'json';

            xhr.onload = () => {
                if (xhr.status === 200) {
                    const response = xhr.response;
                    if (response && response.status === 'success') {
                        if (this.options.debug) {
                            console.log('تم الاتصال بالخادم المحلي بنجاح:', response);
                        }
                        this._updateServerStatus(true, response.isLoggedIn);
                    } else {
                        this._updateServerStatus(false);
                    }
                } else {
                    this._updateServerStatus(false);
                }
            };

            xhr.ontimeout = xhr.onerror = () => {
                this._updateServerStatus(false);
            };

            xhr.send();
        } catch (error) {
            if (this.options.debug) {
                console.error('خطأ أثناء التحقق من اتصال الخادم المحلي:', error);
            }
            this._updateServerStatus(false);
        }
    },

    /**
     * تحديث حالة الخادم في واجهة المستخدم
     * @private
     * @param {boolean} isConnected حالة الاتصال
     * @param {boolean} isLoggedIn حالة تسجيل الدخول (اختياري)
     */
    _updateServerStatus: function(isConnected, isLoggedIn = false) {
        const statusElement = document.getElementById('server_status');
        if (statusElement) {
            if (isConnected) {
                statusElement.innerHTML = '<span class="badge bg-success">متصل</span>';
                
                // تحديث حالة تسجيل الدخول إذا كانت متوفرة
                if (isLoggedIn !== undefined) {
                    const loginStatusElement = document.getElementById('login_status');
                    if (loginStatusElement) {
                        loginStatusElement.innerHTML = isLoggedIn 
                            ? '<span class="badge bg-success">مسجل الدخول</span>'
                            : '<span class="badge bg-warning">غير مسجل الدخول</span>';
                    }
                }
            } else {
                statusElement.innerHTML = '<span class="badge bg-danger">غير متصل</span>';
                
                // عرض رسالة تنبيه للمستخدم
                const serverAlert = document.getElementById('server_alert');
                if (serverAlert) {
                    serverAlert.classList.remove('d-none');
                }
            }
        }
    },

    /**
     * تسجيل الدخول إلى WhatsApp Web
     */
    loginToWhatsApp: function() {
        // تعطيل الزر وعرض مؤشر التحميل
        const loginButton = document.getElementById('login_whatsapp');
        if (loginButton) {
            loginButton.disabled = true;
            loginButton.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> جاري فتح WhatsApp Web...';
        }

        // عرض رسالة للمستخدم
        const resultElement = document.getElementById('login_result');
        if (resultElement) {
            resultElement.innerHTML = '<div class="alert alert-info">جاري فتح نافذة WhatsApp Web. يرجى مسح رمز QR باستخدام تطبيق WhatsApp على هاتفك.</div>';
        }

        // فتح نافذة جديدة للخادم المحلي
        window.open(this.serverUrl, '_blank');

        // إعادة تحميل الصفحة بعد 30 ثانية للتحقق من حالة تسجيل الدخول
        setTimeout(() => {
            location.reload();
        }, 30000);
    },

    /**
     * إعادة تعيين جلسة WhatsApp
     */
    resetWhatsAppSession: function() {
        if (!confirm('هل أنت متأكد من رغبتك في إعادة تعيين جلسة WhatsApp؟\n\nسيؤدي هذا إلى حذف بيانات الجلسة الحالية وستحتاج إلى إعادة تسجيل الدخول مرة أخرى.')) {
            return;
        }

        // تعطيل الزر وعرض مؤشر التحميل
        const resetButton = document.getElementById('reset_whatsapp');
        if (resetButton) {
            resetButton.disabled = true;
            resetButton.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> جاري إعادة التعيين...';
        }

        // عرض رسالة للمستخدم
        const resultElement = document.getElementById('login_result');
        if (resultElement) {
            resultElement.innerHTML = '<div class="alert alert-info">جاري إعادة تعيين جلسة WhatsApp...</div>';
        }

        try {
            // إرسال طلب إلى الخادم المحلي
            const xhr = new XMLHttpRequest();
            xhr.open('POST', `${this.serverUrl}/reset`, true);
            xhr.setRequestHeader('Content-Type', 'application/json');
            xhr.responseType = 'json';

            xhr.onload = () => {
                if (xhr.status === 200) {
                    const response = xhr.response;
                    if (response && response.status === 'success') {
                        if (resultElement) {
                            resultElement.innerHTML = '<div class="alert alert-success">تم إعادة تعيين جلسة WhatsApp بنجاح. يرجى إعادة تسجيل الدخول مرة أخرى.</div>';
                        }

                        // إعادة تحميل الصفحة بعد 2 ثانية
                        setTimeout(() => {
                            location.reload();
                        }, 2000);
                    } else {
                        if (resultElement) {
                            resultElement.innerHTML = `<div class="alert alert-danger">فشل إعادة تعيين جلسة WhatsApp: ${response ? response.message : 'خطأ غير معروف'}</div>`;
                        }
                        if (resetButton) {
                            resetButton.disabled = false;
                            resetButton.innerHTML = '<i class="fas fa-sync-alt me-1"></i> إعادة تعيين الجلسة';
                        }
                    }
                } else {
                    if (resultElement) {
                        resultElement.innerHTML = `<div class="alert alert-danger">حدث خطأ أثناء الاتصال بالخادم المحلي</div>`;
                    }
                    if (resetButton) {
                        resetButton.disabled = false;
                        resetButton.innerHTML = '<i class="fas fa-sync-alt me-1"></i> إعادة تعيين الجلسة';
                    }
                }
            };

            xhr.onerror = () => {
                if (resultElement) {
                    resultElement.innerHTML = '<div class="alert alert-danger">حدث خطأ أثناء الاتصال بالخادم المحلي. تأكد من تشغيل الخادم المحلي.</div>';
                }
                if (resetButton) {
                    resetButton.disabled = false;
                    resetButton.innerHTML = '<i class="fas fa-sync-alt me-1"></i> إعادة تعيين الجلسة';
                }
            };

            // إرسال البيانات
            xhr.send(JSON.stringify({}));
        } catch (error) {
            if (resultElement) {
                resultElement.innerHTML = `<div class="alert alert-danger">حدث خطأ: ${error.message}</div>`;
            }
            if (resetButton) {
                resetButton.disabled = false;
                resetButton.innerHTML = '<i class="fas fa-sync-alt me-1"></i> إعادة تعيين الجلسة';
            }
        }
    },

    /**
     * التحقق من حالة تسجيل الدخول إلى WhatsApp
     */
    checkLoginStatus: function() {
        try {
            const xhr = new XMLHttpRequest();
            xhr.open('GET', `${this.serverUrl}/status`, true);
            xhr.responseType = 'json';

            xhr.onload = () => {
                if (xhr.status === 200) {
                    const response = xhr.response;
                    if (response && response.status === 'success') {
                        const isLoggedIn = response.isLoggedIn;
                        
                        // تحديث حالة تسجيل الدخول في واجهة المستخدم
                        const loginStatusElement = document.getElementById('login_status');
                        if (loginStatusElement) {
                            loginStatusElement.innerHTML = isLoggedIn 
                                ? '<span class="badge bg-success">مسجل الدخول</span>'
                                : '<span class="badge bg-warning">غير مسجل الدخول</span>';
                        }

                        // تحديث زر تسجيل الدخول
                        const loginButton = document.getElementById('login_whatsapp');
                        if (loginButton) {
                            loginButton.disabled = isLoggedIn;
                            if (isLoggedIn) {
                                loginButton.innerHTML = '<i class="fas fa-check me-1"></i> تم تسجيل الدخول';
                            } else {
                                loginButton.innerHTML = '<i class="fab fa-whatsapp me-1"></i> تسجيل الدخول إلى WhatsApp Web';
                            }
                        }
                    }
                }
            };

            xhr.send();
        } catch (error) {
            if (this.options.debug) {
                console.error('خطأ أثناء التحقق من حالة تسجيل الدخول:', error);
            }
        }
    },

    /**
     * إرسال رسالة اختبار
     */
    sendTestMessage: function() {
        const phone = document.getElementById('test_phone').value.trim();
        const message = document.getElementById('test_message').value.trim();

        if (!phone) {
            this._showAlert('يرجى إدخال رقم الهاتف', 'warning');
            return;
        }

        if (!message) {
            this._showAlert('يرجى إدخال نص الرسالة', 'warning');
            return;
        }

        // عرض مؤشر التحميل
        const resultElement = document.getElementById('test_result');
        if (resultElement) {
            resultElement.innerHTML = '<div class="spinner-border spinner-border-sm text-primary" role="status"></div> جاري إرسال الرسالة...';
        }

        try {
            // إرسال طلب إلى الخادم المحلي
            const xhr = new XMLHttpRequest();
            xhr.open('POST', `${this.serverUrl}/send`, true);
            xhr.setRequestHeader('Content-Type', 'application/json');
            xhr.responseType = 'json';

            xhr.onload = () => {
                if (xhr.status === 200) {
                    const response = xhr.response;
                    if (response && response.status === 'success') {
                        if (resultElement) {
                            resultElement.innerHTML = '<div class="alert alert-success">تم إرسال الرسالة بنجاح!</div>';
                        }
                    } else {
                        if (resultElement) {
                            resultElement.innerHTML = `<div class="alert alert-danger">فشل إرسال الرسالة: ${response ? response.message : 'خطأ غير معروف'}</div>`;
                        }
                    }
                } else {
                    if (resultElement) {
                        resultElement.innerHTML = '<div class="alert alert-danger">حدث خطأ أثناء الاتصال بالخادم المحلي</div>';
                    }
                }
            };

            xhr.onerror = () => {
                if (resultElement) {
                    resultElement.innerHTML = '<div class="alert alert-danger">حدث خطأ أثناء الاتصال بالخادم المحلي. تأكد من تشغيل الخادم المحلي.</div>';
                }
            };

            // إرسال البيانات
            const formattedPhone = this._formatPhoneNumber(phone);
            const sendData = {
                phone: formattedPhone,
                message: message
            };

            if (this.options.debug) {
                console.log('إرسال بيانات إلى الخادم:', sendData);
            }

            xhr.send(JSON.stringify(sendData));
        } catch (error) {
            if (resultElement) {
                resultElement.innerHTML = `<div class="alert alert-danger">حدث خطأ: ${error.message}</div>`;
            }
        }
    },

    /**
     * تنسيق رقم الهاتف لاستخدامه مع WhatsApp
     * @private
     * @param {string} phone رقم الهاتف
     * @returns {string} رقم الهاتف المنسق
     */
    _formatPhoneNumber: function(phone) {
        if (!phone) return '';

        // إزالة جميع الأحرف غير الرقمية باستثناء علامة +
        let cleaned = phone.replace(/[^\d+]/g, '');

        // إذا كان الرقم لا يبدأ بـ +، إضافة رمز البلد الافتراضي (+20 لمصر)
        if (!cleaned.startsWith('+')) {
            // إذا كان يبدأ بـ 0، إزالته
            if (cleaned.startsWith('0')) {
                cleaned = cleaned.substring(1);
            }

            // إضافة رمز البلد
            cleaned = '+20' + cleaned;
        }

        // التأكد من أن الرقم له التنسيق الصحيح لـ WhatsApp
        // WhatsApp يتوقع التنسيق الدولي بدون علامة +
        return cleaned.replace(/^\+/, '');
    },

    /**
     * عرض رسالة تنبيه للمستخدم
     * @private
     * @param {string} message نص الرسالة
     * @param {string} type نوع الرسالة (success, info, warning, danger)
     */
    _showAlert: function(message, type = 'info') {
        const alertElement = document.createElement('div');
        alertElement.className = `alert alert-${type} alert-dismissible fade show`;
        alertElement.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        `;

        // إضافة التنبيه إلى الصفحة
        const container = document.querySelector('.container-fluid');
        if (container) {
            container.insertBefore(alertElement, container.firstChild);
        } else {
            document.body.insertBefore(alertElement, document.body.firstChild);
        }

        // إزالة التنبيه بعد 5 ثوانٍ
        setTimeout(() => {
            alertElement.remove();
        }, 5000);
    }
};

// تهيئة وحدة إعدادات WhatsApp عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    WhatsAppSettings.init();
});
