<?php
/**
 * صفحة إدارة المصروفات
 */

// تعريف ثابت لمنع الوصول المباشر للملفات المرتبطة
define('BASEPATH', true);

// استدعاء ملفات التهيئة
require_once '../../config/init.php';

// التحقق من تسجيل الدخول
requireLogin();

// التحقق من صلاحيات المستخدم
if (!hasPermission('expenses_view')) {
    $_SESSION['error_message'] = ACCESS_DENIED_MSG;
    header('Location: ' . BASE_URL . 'pages/dashboard.php');
    exit;
}

// عنوان الصفحة
$pageTitle = 'إدارة المصروفات';

// استدعاء رأس الصفحة
include '../../includes/templates/header.php';

// تحديد الفلاتر الافتراضية
$filters = [
    'branch_id' => $_SESSION['user_branch_id'] ?? null,
    'start_date' => isset($_GET['start_date']) ? $_GET['start_date'] : date('Y-m-01'),
    'end_date' => isset($_GET['end_date']) ? $_GET['end_date'] : date('Y-m-t'),
    'category_id' => isset($_GET['category_id']) ? intval($_GET['category_id']) : null,
    'payment_method' => isset($_GET['payment_method']) ? $_GET['payment_method'] : null,
    'search' => isset($_GET['search']) ? $_GET['search'] : null,
    'sort_by' => isset($_GET['sort_by']) ? $_GET['sort_by'] : 'date',
    'sort_dir' => isset($_GET['sort_dir']) ? $_GET['sort_dir'] : 'DESC',
    'limit' => 50
];

// استرجاع قائمة الفروع إذا كان المستخدم مديرًا
$branches = [];
if ($_SESSION['user_role'] === ROLE_ADMIN || $_SESSION['user_role'] === ROLE_MANAGER) {
    $branchModel = new Branch($db);
    $branches = $branchModel->getBranches(['is_active' => 1]);
}
?>

<!-- المحتوى الرئيسي للصفحة -->
<div class="container-fluid">
    <!-- عنوان الصفحة والأزرار -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-money-bill-wave ml-2"></i> إدارة المصروفات
        </h1>
        <div>
            <?php if (hasPermission('expenses_add')): ?>
            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addExpenseModal">
                <i class="fas fa-plus-circle ml-1"></i> إضافة مصروف جديد
            </button>
            <?php endif; ?>
            <?php if (hasPermission('expenses_add')): ?>
            <button type="button" class="btn btn-outline-secondary" data-bs-toggle="modal" data-bs-target="#addCategoryModal">
                <i class="fas fa-folder-plus ml-1"></i> إضافة فئة جديدة
            </button>
            <?php endif; ?>
        </div>
    </div>

    <!-- تنبيه للتأكد من فتح اليوم -->
    <div class="alert alert-info mb-4">
        <i class="fas fa-info-circle ml-1"></i> ملاحظة هامة: يجب فتح يوم العمل للفرع أولاً قبل إضافة مصروفات جديدة. سيتم ربط جميع المصروفات بيوم العمل المفتوح للفرع.
        <a href="../endday/index.php" class="alert-link">انتقل إلى صفحة نهاية اليوم</a>
    </div>

    <!-- فلاتر البحث -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">فلتر المصروفات</h6>
        </div>
        <div class="card-body">
            <form id="expenses-filter-form" method="GET" action="">
                <div class="row">
                    <!-- فلتر التاريخ -->
                    <div class="col-md-3 mb-3">
                        <label for="start_date" class="form-label">من تاريخ</label>
                        <input type="date" class="form-control" id="start_date" name="start_date" value="<?php echo $filters['start_date']; ?>">
                    </div>
                    <div class="col-md-3 mb-3">
                        <label for="end_date" class="form-label">إلى تاريخ</label>
                        <input type="date" class="form-control" id="end_date" name="end_date" value="<?php echo $filters['end_date']; ?>">
                    </div>

                    <!-- فلتر الفئة -->
                    <div class="col-md-3 mb-3">
                        <label for="category_id" class="form-label">الفئة</label>
                        <select class="form-select" id="category_id" name="category_id">
                            <option value="">جميع الفئات</option>
                            <!-- سيتم تعبئة الفئات هنا عن طريق JavaScript -->
                        </select>
                    </div>

                    <!-- فلتر طريقة الدفع -->
                    <div class="col-md-3 mb-3">
                        <label for="payment_method" class="form-label">طريقة الدفع</label>
                        <select class="form-select" id="payment_method" name="payment_method">
                            <option value="">جميع الطرق</option>
                            <option value="cash" <?php echo ($filters['payment_method'] == 'cash') ? 'selected' : ''; ?>>نقد</option>
                            <option value="card" <?php echo ($filters['payment_method'] == 'card') ? 'selected' : ''; ?>>بطاقة</option>
                            <option value="other" <?php echo ($filters['payment_method'] == 'other') ? 'selected' : ''; ?>>أخرى</option>
                        </select>
                    </div>

                    <!-- فلتر البحث -->
                    <div class="col-md-6 mb-3">
                        <label for="search" class="form-label">بحث</label>
                        <input type="text" class="form-control" id="search" name="search" placeholder="بحث في الوصف..." value="<?php echo $filters['search']; ?>">
                    </div>

                    <!-- فلتر الفرع (للمدير) -->
                    <?php if (!empty($branches)): ?>
                    <div class="col-md-3 mb-3">
                        <label for="branch_id" class="form-label">الفرع</label>
                        <select class="form-select" id="branch_id" name="branch_id">
                            <option value="">جميع الفروع</option>
                            <?php foreach ($branches as $branch): ?>
                                <option value="<?php echo $branch['id']; ?>" <?php echo ($filters['branch_id'] == $branch['id']) ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($branch['name']); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <?php endif; ?>

                    <!-- أزرار التصفية -->
                    <div class="col-md-3 mb-3 d-flex align-items-end">
                        <button type="submit" class="btn btn-primary me-2">
                            <i class="fas fa-filter ml-1"></i> تصفية
                        </button>
                        <a href="index.php" class="btn btn-outline-secondary">
                            <i class="fas fa-redo ml-1"></i> إعادة تعيين
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- عرض المصروفات -->
    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex justify-content-between align-items-center">
            <h6 class="m-0 font-weight-bold text-primary">قائمة المصروفات</h6>
            <div class="text-primary">
                <strong>إجمالي المصروفات:</strong> <span class="badge bg-primary p-2" id="total-expenses-amount">0.00</span>
            </div>
        </div>
        <div class="card-body">
            <div id="expenses-loading" class="text-center py-5">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">جاري التحميل...</span>
                </div>
                <p class="mt-2">جاري تحميل البيانات...</p>
            </div>

            <div id="expenses-container" style="display: none;">
                <div id="no-expenses-message" class="alert alert-info text-center" style="display: none;">
                    لا توجد مصروفات في الفترة المحددة
                </div>

                <div class="table-responsive">
                    <table class="table table-bordered table-hover" id="expensesTable" width="100%" cellspacing="0">
                        <thead class="table-light">
                            <tr>
                                <th>التاريخ</th>
                                <th>الفئة</th>
                                <th>المبلغ</th>
                                <th>الوصف</th>
                                <th>طريقة الدفع</th>
                                <?php if ($_SESSION['user_role'] === ROLE_ADMIN || $_SESSION['user_role'] === ROLE_MANAGER): ?>
                                <th>الفرع</th>
                                <?php endif; ?>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="expenses-list">
                            <!-- سيتم تعبئة البيانات هنا عن طريق JavaScript -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- مودال إضافة مصروف جديد -->
<?php if (hasPermission('expenses_add')): ?>
<div class="modal fade" id="addExpenseModal" tabindex="-1" aria-labelledby="addExpenseModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addExpenseModalLabel">إضافة مصروف جديد</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="add-expense-form">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="category_id_add" class="form-label">الفئة</label>
                        <select class="form-select" id="category_id_add" name="category_id" required>
                            <option value="">اختر الفئة</option>
                            <!-- سيتم تعبئة الفئات هنا عن طريق JavaScript -->
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="amount" class="form-label">المبلغ</label>
                        <input type="number" step="0.01" min="0.01" class="form-control" id="amount" name="amount" required>
                    </div>
                    <div class="mb-3">
                        <label for="date" class="form-label">التاريخ</label>
                        <input type="date" class="form-control" id="date" name="date" value="<?php echo date('Y-m-d'); ?>" required>
                    </div>
                    <div class="mb-3">
                        <label for="payment_method_add" class="form-label">طريقة الدفع</label>
                        <select class="form-select" id="payment_method_add" name="payment_method" required>
                            <option value="cash">نقد</option>
                            <option value="card">بطاقة</option>
                            <option value="other">أخرى</option>
                        </select>
                    </div>
                    <?php if ($_SESSION['user_role'] === ROLE_ADMIN || $_SESSION['user_role'] === ROLE_MANAGER): ?>
                    <div class="mb-3">
                        <label for="branch_id_add" class="form-label">الفرع</label>
                        <select class="form-select" id="branch_id_add" name="branch_id" required>
                            <?php foreach ($branches as $branch): ?>
                                <option value="<?php echo $branch['id']; ?>" <?php echo ($_SESSION['user_branch_id'] == $branch['id']) ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($branch['name']); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <?php endif; ?>
                    <div class="mb-3">
                        <label for="description" class="form-label">الوصف</label>
                        <textarea class="form-control" id="description" name="description" rows="3"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">إضافة</button>
                </div>
            </form>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- مودال تعديل مصروف -->
<?php if (hasPermission('expenses_edit')): ?>
<div class="modal fade" id="editExpenseModal" tabindex="-1" aria-labelledby="editExpenseModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editExpenseModalLabel">تعديل مصروف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="edit-expense-form">
                <input type="hidden" id="edit_expense_id" name="id">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="category_id_edit" class="form-label">الفئة</label>
                        <select class="form-select" id="category_id_edit" name="category_id" required>
                            <option value="">اختر الفئة</option>
                            <!-- سيتم تعبئة الفئات هنا عن طريق JavaScript -->
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="amount_edit" class="form-label">المبلغ</label>
                        <input type="number" step="0.01" min="0.01" class="form-control" id="amount_edit" name="amount" required>
                    </div>
                    <div class="mb-3">
                        <label for="date_edit" class="form-label">التاريخ</label>
                        <input type="date" class="form-control" id="date_edit" name="date" required>
                    </div>
                    <div class="mb-3">
                        <label for="payment_method_edit" class="form-label">طريقة الدفع</label>
                        <select class="form-select" id="payment_method_edit" name="payment_method" required>
                            <option value="cash">نقد</option>
                            <option value="card">بطاقة</option>
                            <option value="other">أخرى</option>
                        </select>
                    </div>
                    <?php if ($_SESSION['user_role'] === ROLE_ADMIN || $_SESSION['user_role'] === ROLE_MANAGER): ?>
                    <div class="mb-3">
                        <label for="branch_id_edit" class="form-label">الفرع</label>
                        <select class="form-select" id="branch_id_edit" name="branch_id" required>
                            <?php foreach ($branches as $branch): ?>
                                <option value="<?php echo $branch['id']; ?>"><?php echo htmlspecialchars($branch['name']); ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <?php endif; ?>
                    <div class="mb-3">
                        <label for="description_edit" class="form-label">الوصف</label>
                        <textarea class="form-control" id="description_edit" name="description" rows="3"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">حفظ التغييرات</button>
                </div>
            </form>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- مودال إضافة فئة جديدة -->
<?php if (hasPermission('expenses_add')): ?>
<div class="modal fade" id="addCategoryModal" tabindex="-1" aria-labelledby="addCategoryModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addCategoryModalLabel">إضافة فئة مصروفات جديدة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="add-category-form">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="name" class="form-label">اسم الفئة</label>
                        <input type="text" class="form-control" id="name" name="name" required>
                    </div>
                    <div class="mb-3">
                        <label for="description_category" class="form-label">الوصف</label>
                        <textarea class="form-control" id="description_category" name="description" rows="3"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">إضافة</button>
                </div>
            </form>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- مودال حذف مصروف -->
<?php if (hasPermission('expenses_delete')): ?>
<div class="modal fade" id="deleteExpenseModal" tabindex="-1" aria-labelledby="deleteExpenseModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteExpenseModalLabel">حذف مصروف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من رغبتك في حذف هذا المصروف؟</p>
                <p class="text-danger">لا يمكن التراجع عن هذا الإجراء.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-danger" id="confirm-delete">تأكيد الحذف</button>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- استدعاء قالب الفوتر -->
<?php include '../../includes/templates/footer.php'; ?>

<script>
$(document).ready(function() {
    // متغيرات عامة
    var expensesTable;
    var categories = [];
    var deleteExpenseId = null;

    // تحميل فئات المصروفات
    loadCategories();

    // تحميل قائمة المصروفات
    loadExpenses();

    // إضافة مصروف جديد
    $('#add-expense-form').on('submit', function(e) {
        e.preventDefault();

        $.ajax({
            url: '../../api/expenses.php?action=add',
            type: 'POST',
            data: $(this).serialize(),
            dataType: 'json',
            success: function(response) {
                if (response.status === 'success') {
                    showSuccessAlert(response.message);
                    $('#addExpenseModal').modal('hide');
                    $('#add-expense-form')[0].reset();

                    // إعادة تحميل قائمة المصروفات
                    loadExpenses();
                } else {
                    showErrorAlert(response.message);
                }
            },
            error: function(xhr, status, error) {
                showErrorAlert('حدث خطأ أثناء إضافة المصروف. يرجى المحاولة مرة أخرى.');
                console.error(xhr.responseText);
            }
        });
    });

    // تعديل المصروف
    $('#edit-expense-form').on('submit', function(e) {
        e.preventDefault();

        var expenseId = $('#edit_expense_id').val();
        var formData = $(this).serialize();

        $.ajax({
            url: '../../api/expenses.php?action=update',
            type: 'PUT',
            data: formData,
            dataType: 'json',
            success: function(response) {
                if (response.status === 'success') {
                    showSuccessAlert(response.message);
                    $('#editExpenseModal').modal('hide');

                    // إعادة تحميل قائمة المصروفات
                    loadExpenses();
                } else {
                    showErrorAlert(response.message);
                }
            },
            error: function(xhr, status, error) {
                showErrorAlert('حدث خطأ أثناء تعديل المصروف. يرجى المحاولة مرة أخرى.');
                console.error(xhr.responseText);
            }
        });
    });

    // تأكيد حذف المصروف
    $('#confirm-delete').on('click', function() {
        if (deleteExpenseId !== null) {
            $.ajax({
                url: '../../api/expenses.php?action=delete',
                type: 'DELETE',
                data: {id: deleteExpenseId},
                dataType: 'json',
                success: function(response) {
                    if (response.status === 'success') {
                        showSuccessAlert(response.message);
                        $('#deleteExpenseModal').modal('hide');

                        // إعادة تحميل قائمة المصروفات
                        loadExpenses();
                    } else {
                        showErrorAlert(response.message);
                    }
                },
                error: function(xhr, status, error) {
                    showErrorAlert('حدث خطأ أثناء حذف المصروف. يرجى المحاولة مرة أخرى.');
                    console.error(xhr.responseText);
                }
            });
        }
    });

    // إضافة فئة جديدة
    $('#add-category-form').on('submit', function(e) {
        e.preventDefault();

        $.ajax({
            url: '../../api/expenses.php?action=add-category',
            type: 'POST',
            data: $(this).serialize(),
            dataType: 'json',
            success: function(response) {
                if (response.status === 'success') {
                    showSuccessAlert(response.message);
                    $('#addCategoryModal').modal('hide');

                    // إعادة تحميل فئات المصروفات
                    loadCategories();

                    // إعادة تعيين النموذج
                    $('#add-category-form')[0].reset();
                } else {
                    showErrorAlert(response.message);
                }
            },
            error: function(xhr, status, error) {
                showErrorAlert('حدث خطأ أثناء إضافة الفئة. يرجى المحاولة مرة أخرى.');
                console.error(xhr.responseText);
            }
        });
    });

    // فلترة المصروفات
    $('#expenses-filter-form').on('submit', function(e) {
        e.preventDefault();
        loadExpenses();
    });

    // دالة تحميل فئات المصروفات
    function loadCategories() {
        $.ajax({
            url: '../../api/expenses.php?action=list-categories',
            type: 'GET',
            dataType: 'json',
            success: function(response) {
                if (response.status === 'success') {
                    categories = response.categories;

                    // تعبئة قوائم الفئات في النماذج
                    var categoriesHtml = '<option value="">اختر الفئة</option>';
                    $.each(categories, function(index, category) {
                        categoriesHtml += '<option value="' + category.id + '">' + category.name + '</option>';
                    });

                    $('#category_id_add').html(categoriesHtml);
                    $('#category_id_edit').html(categoriesHtml);
                    $('#category_id').html('<option value="">جميع الفئات</option>' + categoriesHtml);
                } else {
                    showErrorAlert('حدث خطأ أثناء تحميل فئات المصروفات');
                }
            },
            error: function(xhr, status, error) {
                showErrorAlert('حدث خطأ أثناء تحميل فئات المصروفات');
                console.error(xhr.responseText);
            }
        });
    }

    // دالة تحميل المصروفات
    function loadExpenses() {
        // إظهار شاشة التحميل
        $('#expenses-loading').show();
        $('#expenses-container').hide();

        // جمع بيانات الفلاتر
        var filters = $('#expenses-filter-form').serialize();

        $.ajax({
            url: '../../api/expenses.php?action=list&' + filters,
            type: 'GET',
            dataType: 'json',
            success: function(response) {
                if (response.status === 'success') {
                    var expenses = response.expenses;
                    var totalAmount = response.total_amount;

                    // تحديث إجمالي المبلغ
                    $('#total-expenses-amount').text(formatCurrency(totalAmount));

                    // إخفاء شاشة التحميل وإظهار المحتوى
                    $('#expenses-loading').hide();
                    $('#expenses-container').show();

                    // التحقق من وجود مصروفات
                    if (expenses.length === 0) {
                        $('#no-expenses-message').show();
                        $('#expensesTable').hide();
                    } else {
                        $('#no-expenses-message').hide();
                        $('#expensesTable').show();

                        // تعبئة جدول المصروفات
                        renderExpensesTable(expenses);
                    }
                } else {
                    showErrorAlert('حدث خطأ أثناء تحميل المصروفات: ' + response.message);
                    $('#expenses-loading').hide();
                }
            },
            error: function(xhr, status, error) {
                showErrorAlert('حدث خطأ أثناء تحميل المصروفات.');
                console.error(xhr.responseText);
                $('#expenses-loading').hide();
            }
        });
    }

    // دالة عرض المصروفات في الجدول
    function renderExpensesTable(expenses) {
        // تدمير الجدول إذا كان موجود مسبقًا
        if ($.fn.DataTable.isDataTable('#expensesTable')) {
            expensesTable.destroy();
        }

        // بناء صفوف الجدول
        var tableBody = '';

        $.each(expenses, function(index, expense) {
            tableBody += '<tr>';
            tableBody += '<td>' + formatDate(expense.date) + '</td>';
            tableBody += '<td>' + (expense.category_name || 'غير محدد') + '</td>';
            tableBody += '<td>' + formatCurrency(expense.amount) + '</td>';
            tableBody += '<td>' + (expense.description || '') + '</td>';
            tableBody += '<td>' + getPaymentMethodLabel(expense.payment_method) + '</td>';

            // عرض اسم الفرع إذا كان المستخدم مدير
            <?php if ($_SESSION['user_role'] === ROLE_ADMIN || $_SESSION['user_role'] === ROLE_MANAGER): ?>
            tableBody += '<td>' + (expense.branch_name || '') + '</td>';
            <?php endif; ?>

            // أزرار الإجراءات
            tableBody += '<td>';
            <?php if (hasPermission('expenses_edit')): ?>
            tableBody += '<button class="btn btn-sm btn-outline-primary edit-expense me-1" data-id="' + expense.id + '"><i class="fas fa-edit"></i></button>';
            <?php endif; ?>

            <?php if (hasPermission('expenses_delete')): ?>
            tableBody += '<button class="btn btn-sm btn-outline-danger delete-expense" data-id="' + expense.id + '"><i class="fas fa-trash-alt"></i></button>';
            <?php endif; ?>
            tableBody += '</td>';

            tableBody += '</tr>';
        });

        // تحديث محتوى الجدول
        $('#expenses-list').html(tableBody);

        // تهيئة جدول البيانات
        expensesTable = $('#expensesTable').DataTable({
            "language": {
                "url": "//cdn.datatables.net/plug-ins/1.10.25/i18n/Arabic.json"
            },
            "order": [[ 0, "desc" ]],
            "pageLength": 25,
            "retrieve": true
        });

        // إضافة مستمعي الأحداث للأزرار
        attachEventListeners();
    }

    // دالة إضافة مستمعي الأحداث بعد تحديث الجدول
    function attachEventListeners() {
        // إضافة حدث تعديل المصروف
        $('.edit-expense').on('click', function() {
            var expenseId = $(this).data('id');
            loadExpenseData(expenseId);
        });

        // إضافة حدث حذف المصروف
        $('.delete-expense').on('click', function() {
            var expenseId = $(this).data('id');
            showDeleteConfirmation(expenseId);
        });
    }

    // تحميل بيانات المصروف للتعديل
    function loadExpenseData(expenseId) {
        $.ajax({
            url: '../../api/expenses.php?action=view&id=' + expenseId,
            type: 'GET',
            dataType: 'json',
            success: function(response) {
                if (response.status === 'success') {
                    var expense = response.expense;

                    $('#edit_expense_id').val(expense.id);
                    $('#category_id_edit').val(expense.category_id);
                    $('#amount_edit').val(expense.amount);
                    $('#date_edit').val(expense.date);
                    $('#payment_method_edit').val(expense.payment_method);
                    $('#description_edit').val(expense.description);

                    if ($('#branch_id_edit').length) {
                        $('#branch_id_edit').val(expense.branch_id);
                    }

                    $('#editExpenseModal').modal('show');
                } else {
                    showErrorAlert(response.message);
                }
            },
            error: function(xhr, status, error) {
                showErrorAlert('حدث خطأ أثناء تحميل بيانات المصروف. يرجى المحاولة مرة أخرى.');
                console.error(xhr.responseText);
            }
        });
    }

    // إظهار تأكيد الحذف
    function showDeleteConfirmation(expenseId) {
        deleteExpenseId = expenseId;
        $('#deleteExpenseModal').modal('show');
    }

    // دالة تنسيق العملة
    function formatCurrency(amount) {
        return Number(amount).toFixed(2).replace(/\d(?=(\d{3})+\.)/g, '$&,');
    }

    // دالة تنسيق التاريخ
    function formatDate(dateString) {
        var date = new Date(dateString);
        return date.toISOString().split('T')[0];
    }

    // دالة الحصول على اسم طريقة الدفع
    function getPaymentMethodLabel(method) {
        const methods = {
            'cash': 'نقد',
            'card': 'بطاقة',
            'other': 'أخرى'
        };
        return methods[method] || method;
    }

    // دوال عرض رسائل النجاح والخطأ
    function showSuccessAlert(message) {
        var alertHtml = '<div class="alert alert-success alert-dismissible fade show" role="alert">';
        alertHtml += message;
        alertHtml += '<button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>';
        alertHtml += '</div>';

        $('#alerts-container').html(alertHtml);

        // إخفاء التنبيه تلقائيًا بعد 3 ثوانٍ
        setTimeout(function() {
            $('.alert').alert('close');
        }, 3000);
    }

    function showErrorAlert(message) {
        var alertHtml = '<div class="alert alert-danger alert-dismissible fade show" role="alert">';
        alertHtml += message;
        alertHtml += '<button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>';
        alertHtml += '</div>';

        $('#alerts-container').html(alertHtml);
    }
});
</script>