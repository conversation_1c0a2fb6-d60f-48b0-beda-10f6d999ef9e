/**
 * WhatsApp Direct Report Notification
 * Este script maneja el envío de notificaciones de informes a través de WhatsApp
 * directamente al servidor local de WhatsApp en localhost:3000, sin pasar por la API
 */

const WhatsAppDirectReportNotification = {
    // URL del servidor local de WhatsApp
    serverUrl: 'http://localhost:3000',

    // URL base de la API del sistema
    apiUrl: BASE_URL + 'api/',

    // Estado de la última operación
    lastOperationStatus: null,

    // Conjunto para rastrear informes ya notificados y evitar duplicados
    notifiedReports: new Set(),

    // أرقام هواتف المدراء المخزنة مؤقتًا
    adminPhones: null,

    // Opciones de configuración
    options: {
        debug: true,                // Habilitar mensajes de depuración
        maxRetries: 2,              // Número máximo de reintentos
        retryDelay: 2000,           // Tiempo entre reintentos (ms)
        timeout: 60000,             // Tiempo de espera para solicitudes (ms) - 60 segundos
        showSuccessMessage: true    // Mostrar mensaje de éxito al usuario
    },

    /**
     * Inicializar el módulo
     * @param {Object} options Opciones de configuración
     */
    init: function(options) {
        // Combinar opciones proporcionadas con las predeterminadas
        if (options) {
            this.options = { ...this.options, ...options };
        }

        // Forzar el modo de depuración para diagnosticar problemas
        this.options.debug = true;

        console.log('WhatsAppDirectReportNotification inicializado con opciones:', this.options);
        console.log('URL del servidor local de WhatsApp:', this.serverUrl);
        console.log('URL base de la API del sistema:', this.apiUrl);

        // جلب أرقام هواتف المدراء عند التهيئة
        this.checkAdminPhones();
    },

    /**
     * التحقق من وجود أرقام هواتف للمدراء
     * @returns {Promise<boolean>} وعد يعيد true إذا كانت هناك أرقام هواتف متاحة
     */
    checkAdminPhones: function() {
        return new Promise((resolve, reject) => {
            // إذا كانت الأرقام مخزنة مسبقًا، نعيدها مباشرة
            if (this.adminPhones !== null) {
                console.log('استخدام الأرقام المخزنة مسبقًا:', this.adminPhones);
                resolve(this.adminPhones.length > 0);
                return;
            }

            // للتجربة: إضافة أرقام افتراضية للمدراء إذا فشل الاتصال بالخادم
            const useHardcodedPhones = true; // ضع هذا على false لإيقاف استخدام الأرقام الافتراضية

            // استعلام عن أرقام هواتف المدراء من الخادم
            console.log('جاري الاستعلام عن أرقام هواتف المدراء من الخادم...');
            const xhr = new XMLHttpRequest();
            xhr.open('GET', this.apiUrl + 'endday_notification.php?action=check_admin_phones', true);
            xhr.responseType = 'json';

            xhr.onload = () => {
                if (xhr.status === 200) {
                    const response = xhr.response;
                    console.log('استجابة الخادم:', response);

                    if (response && response.status === 'success') {
                        this.adminPhones = response.data.admin_phones || [];

                        if (this.options.debug) {
                            console.log('تم الحصول على أرقام هواتف المدراء:', this.adminPhones);
                        }

                        // إذا لم يتم العثور على أرقام وتم تمكين الأرقام الافتراضية
                        if (this.adminPhones.length === 0 && useHardcodedPhones) {
                            console.log('لم يتم العثور على أرقام هواتف المدراء، استخدام الأرقام الافتراضية');
                            this.adminPhones = [
                                { phone: '201555262660', name: 'المدير (افتراضي)' }
                            ];
                        }

                        resolve(this.adminPhones.length > 0);
                    } else {
                        console.warn('فشل الحصول على أرقام هواتف المدراء من الخادم');

                        // استخدام الأرقام الافتراضية إذا تم تمكينها
                        if (useHardcodedPhones) {
                            console.log('استخدام الأرقام الافتراضية للمدراء');
                            this.adminPhones = [
                                { phone: '201555262660', name: 'المدير (افتراضي)' }
                            ];
                            resolve(true);
                        } else {
                            this.adminPhones = [];
                            const errorMsg = response ? response.message : 'خطأ غير معروف في الحصول على أرقام هواتف المدراء';
                            console.warn(errorMsg);
                            resolve(false);
                        }
                    }
                } else {
                    console.warn(`خطأ في الحصول على أرقام هواتف المدراء: ${xhr.status}`);

                    // استخدام الأرقام الافتراضية إذا تم تمكينها
                    if (useHardcodedPhones) {
                        console.log('استخدام الأرقام الافتراضية للمدراء بسبب خطأ الخادم');
                        this.adminPhones = [
                            { phone: '201555262660', name: 'المدير (افتراضي)' }
                        ];
                        resolve(true);
                    } else {
                        this.adminPhones = [];
                        resolve(false);
                    }
                }
            };

            xhr.onerror = () => {
                console.warn('خطأ في الشبكة أثناء الحصول على أرقام هواتف المدراء');

                // استخدام الأرقام الافتراضية إذا تم تمكينها
                if (useHardcodedPhones) {
                    console.log('استخدام الأرقام الافتراضية للمدراء بسبب خطأ الشبكة');
                    this.adminPhones = [
                        { phone: '201555262660', name: 'المدير (افتراضي)' }
                    ];
                    resolve(true);
                } else {
                    this.adminPhones = [];
                    resolve(false);
                }
            };

            xhr.send();
        });
    },

    /**
     * Enviar notificación de informe a los administradores
     * @param {string} reportTitle Título del informe
     * @param {string} reportContent Contenido del informe
     * @returns {Promise<Object>} Resultado de la operación
     */
    sendReportNotification: function(reportTitle, reportContent) {
        return new Promise((resolve, reject) => {
            console.log(`=== INICIO PROCESO DE ENVÍO DE INFORME: ${reportTitle} ===`);
            
            // Verificar si ya se ha enviado una notificación para este informe
            const reportId = this._generateReportId(reportTitle, reportContent);
            if (this.notifiedReports.has(reportId)) {
                console.log(`DUPLICADO DETECTADO: Notificación para informe "${reportTitle}" ya fue enviada anteriormente.`);
                console.log(`Informes ya notificados: [${Array.from(this.notifiedReports).join(', ')}]`);
                resolve({ status: 'success', message: 'Notificación ya enviada anteriormente' });
                return;
            }

            console.log(`Preparando notificación de WhatsApp para informe: ${reportTitle}`);
            
            // التحقق من وجود أرقام هواتف للمدراء قبل محاولة الإرسال
            this.checkAdminPhones()
                .then(hasAdminPhones => {
                    if (!hasAdminPhones) {
                        throw new Error('لا توجد أرقام هواتف متاحة للمدراء. لا يمكن إرسال الإشعارات.');
                    }

                    // Preparar el mensaje
                    const message = `*${reportTitle}*\n\n${reportContent}\n\n_تم إنشاء هذا التقرير بواسطة ${this._getUserName()} في ${new Date().toLocaleString()}_`;
                    
                    // Enviar mensajes a todos los administradores
                    const sendPromises = this.adminPhones.map(admin => {
                        return this._sendWhatsAppMessage(
                            admin.phone,
                            message,
                            reportId
                        );
                    });

                    return Promise.all(sendPromises);
                })
                .then(results => {
                    console.log(`Mensajes enviados a todos los administradores:`, results);
                    
                    // Marcar este informe como ya notificado
                    this.notifiedReports.add(reportId);
                    console.log(`Informe "${reportTitle}" marcado como notificado correctamente`);
                    
                    // Mostrar mensaje de éxito si está habilitado
                    if (this.options.showSuccessMessage) {
                        this._showSuccessMessage();
                    }

                    this.lastOperationStatus = 'success';
                    console.log(`=== FIN PROCESO DE ENVÍO DE INFORME: ${reportTitle} - ÉXITO ===`);
                    resolve({ status: 'success', message: 'Informe enviado correctamente' });
                })
                .catch(error => {
                    console.error(`Error al enviar informe "${reportTitle}":`, error);
                    
                    // Verificar si es un error de timeout
                    if (error.message && error.message.includes('Tiempo de espera agotado')) {
                        console.warn('Error de timeout - El informe podría haberse enviado correctamente');
                        
                        this.lastOperationStatus = 'warning';
                        console.log(`=== FIN PROCESO DE ENVÍO DE INFORME: ${reportTitle} - ADVERTENCIA (TIMEOUT) ===`);
                        resolve({
                            status: 'warning',
                            message: 'Tiempo de espera agotado, pero el informe podría haberse enviado correctamente'
                        });
                    } else {
                        this.lastOperationStatus = 'error';
                        console.log(`=== FIN PROCESO DE ENVÍO DE INFORME: ${reportTitle} - ERROR ===`);
                        reject(error);
                    }
                });
        });
    },

    /**
     * Enviar mensaje de WhatsApp a través del servidor local
     * @private
     * @param {string} phone Número de teléfono del destinatario
     * @param {string} message Mensaje a enviar
     * @param {string} reportId ID del informe (para referencia)
     * @returns {Promise<Object>} Resultado del envío
     */
    _sendWhatsAppMessage: function(phone, message, reportId) {
        return new Promise((resolve, reject) => {
            try {
                console.log(`=== INICIO ENVÍO DE MENSAJE ===`);
                console.log(`Enviando mensaje de WhatsApp a ${phone} para informe ${reportId}`);
                console.log(`Mensaje a enviar:`, message.substring(0, 100) + '...');

                // Formatear el número de teléfono correctamente para WhatsApp
                const formattedPhone = this._formatPhoneNumber(phone);
                console.log(`Número original: ${phone}`);
                console.log(`Número formateado: ${formattedPhone}`);

                // Verificar que el servidor local esté configurado correctamente
                if (!this.serverUrl || !this.serverUrl.startsWith('http')) {
                    console.error(`URL del servidor local inválida: ${this.serverUrl}`);
                    throw new Error(`URL del servidor local inválida: ${this.serverUrl}`);
                }

                // Realizar una solicitud al servidor local para enviar el mensaje
                const xhr = new XMLHttpRequest();
                const url = `${this.serverUrl}/send`;
                console.log(`URL de envío de mensaje: ${url}`);

                xhr.open('POST', url, true);
                xhr.setRequestHeader('Content-Type', 'application/json');
                xhr.responseType = 'json';
                xhr.timeout = this.options.timeout; // Usar el tiempo de espera configurado en las opciones
                console.log(`Tiempo de espera configurado: ${this.options.timeout}ms`);

                xhr.onload = () => {
                    console.log(`Respuesta recibida, código de estado: ${xhr.status}`);

                    if (xhr.status === 200) {
                        let response;
                        console.log('Respuesta completa:', xhr.response);

                        // Manejar diferentes tipos de respuestas
                        if (typeof xhr.response === 'object') {
                            response = xhr.response;
                            console.log('Respuesta interpretada como objeto JSON');
                        } else if (typeof xhr.responseText === 'string') {
                            console.log('Respuesta recibida como texto:', xhr.responseText);
                            try {
                                response = JSON.parse(xhr.responseText);
                                console.log('Texto convertido a objeto JSON:', response);
                            } catch (e) {
                                console.error('Error al analizar la respuesta JSON:', e);
                                response = { status: 'error', message: 'Respuesta no válida del servidor' };
                            }
                        } else {
                            console.warn('Tipo de respuesta desconocido:', typeof xhr.response);
                            response = { status: 'error', message: 'Respuesta desconocida del servidor' };
                        }

                        if (response && response.status === 'success') {
                            console.log(`¡Éxito! Mensaje enviado a ${formattedPhone}:`, response);
                            resolve(response);
                        } else {
                            const errorMsg = response ? response.message : 'Error desconocido al enviar mensaje';
                            console.error(`Error al enviar mensaje a ${formattedPhone}: ${errorMsg}`);
                            reject(new Error(errorMsg));
                        }
                    } else {
                        const errorMsg = `Error al enviar mensaje: ${xhr.status}`;
                        console.error(errorMsg);
                        reject(new Error(errorMsg));
                    }
                };

                xhr.ontimeout = () => {
                    const errorMsg = 'Tiempo de espera agotado al enviar mensaje';
                    console.error(errorMsg);

                    // A pesar del timeout, el mensaje podría haberse enviado correctamente
                    // Registrar como advertencia en lugar de error y resolver la promesa
                    console.warn('El mensaje podría haberse enviado a pesar del timeout');
                    resolve({
                        status: 'warning',
                        message: 'Tiempo de espera agotado, pero el mensaje podría haberse enviado correctamente'
                    });
                };

                xhr.onerror = (e) => {
                    const errorMsg = 'Error de red al enviar mensaje';
                    console.error(errorMsg, e);
                    reject(new Error(errorMsg));
                };

                // Enviar los datos
                const sendData = {
                    phone: formattedPhone,
                    message: message
                };

                const jsonData = JSON.stringify(sendData);
                console.log('Enviando datos al servidor (JSON):', jsonData);

                xhr.send(jsonData);
                console.log('Solicitud enviada, esperando respuesta...');
            } catch (error) {
                console.error('Error inesperado al enviar mensaje:', error);
                reject(error);
            }
        });
    },

    /**
     * Formatear número de teléfono para WhatsApp
     * @private
     * @param {string} phone Número de teléfono a formatear
     * @returns {string} Número de teléfono formateado
     */
    _formatPhoneNumber: function(phone) {
        if (!phone) return '';

        // Eliminar todos los caracteres no numéricos excepto el signo +
        let cleaned = phone.replace(/[^\d+]/g, '');

        // Si el número no comienza con +, agregar el código de país para Egipto (+20)
        if (!cleaned.startsWith('+')) {
            // Si comienza con 0, eliminarlo
            if (cleaned.startsWith('0')) {
                cleaned = cleaned.substring(1);
            }
            
            // Si no comienza con 20 (código de Egipto), agregarlo
            if (!cleaned.startsWith('20')) {
                cleaned = '20' + cleaned;
            }
        } else {
            // Si comienza con +, eliminar el signo + para WhatsApp
            cleaned = cleaned.substring(1);
        }

        return cleaned;
    },

    /**
     * Generar un ID único para el informe basado en su título y contenido
     * @private
     * @param {string} title Título del informe
     * @param {string} content Contenido del informe
     * @returns {string} ID único para el informe
     */
    _generateReportId: function(title, content) {
        // Usar una combinación del título y los primeros 50 caracteres del contenido
        return title + '_' + content.substring(0, 50).replace(/\s+/g, '_');
    },

    /**
     * Obtener el nombre del usuario actual
     * @private
     * @returns {string} Nombre del usuario
     */
    _getUserName: function() {
        // Intentar obtener el nombre del usuario de la sesión
        if (typeof userName !== 'undefined') {
            return userName;
        }
        
        // Si no está disponible, usar un valor predeterminado
        return 'مستخدم النظام';
    },

    /**
     * Mostrar mensaje de éxito
     * @private
     * @param {string} message Mensaje a mostrar (opcional)
     */
    _showSuccessMessage: function(message) {
        const successMessage = message || 'تم إرسال التقرير بنجاح للمدراء';
        
        // Verificar si toastr está disponible
        if (typeof toastr !== 'undefined') {
            toastr.success(successMessage);
        } else {
            alert(successMessage);
        }
    }
};
