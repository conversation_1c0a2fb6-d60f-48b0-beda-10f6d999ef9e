<?php
/**
 * صفحة إضافة عرض جديد
 */

// تعريف ثابت لمنع الوصول المباشر للملفات المرتبطة
define('BASEPATH', true);

// استدعاء ملفات التهيئة
require_once '../../config/init.php';

// التحقق من تسجيل الدخول
requireLogin();

// التحقق من الصلاحيات
requirePermission('promotions_create');

// عنوان الصفحة
$pageTitle = 'إضافة عرض جديد';

// إنشاء كائنات النماذج
$branchModel = new Branch($db);
$productModel = new Product($db);
$serviceModel = new Service($db);

// التحقق من صلاحيات المستخدم
$isAdmin = hasPermission('admin_access');

// الفرع الحالي
$branchId = $_SESSION['user_branch_id'] ?? null;

// الحصول على قائمة الفروع إذا كان المستخدم مديراً
$branches = [];
if ($isAdmin) {
    $branches = $branchModel->getBranches(['is_active' => 1]);
}

// الحصول على قائمة المنتجات والخدمات
$products = $productModel->getProducts(['branch_id' => $branchId, 'is_active' => 1]);
$services = $serviceModel->getServices(['branch_id' => $branchId, 'is_active' => 1]);

// استدعاء رأس الصفحة
include '../../includes/templates/header.php';
?>

<!-- المحتوى الرئيسي للصفحة -->
<div class="container-fluid">
    <!-- عنوان الصفحة -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2 class="h3 mb-0 text-gray-800">إضافة عرض جديد</h2>
        <a href="index.php" class="btn btn-secondary">
            <i class="fas fa-arrow-right me-1"></i> العودة للقائمة
        </a>
    </div>

    <!-- بطاقة إضافة عرض جديد -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">معلومات العرض</h6>
        </div>
        <div class="card-body">
            <form id="addPromotionForm">
                <!-- معلومات أساسية -->
                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="name" class="form-label">اسم العرض <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="name" name="name" required>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="branch_id" class="form-label">الفرع</label>
                            <select class="form-select" id="branch_id" name="branch_id">
                                <option value="">جميع الفروع</option>
                                <?php if ($isAdmin && !empty($branches)): ?>
                                    <?php foreach ($branches as $branch): ?>
                                    <option value="<?php echo $branch['id']; ?>" <?php echo ($branchId == $branch['id']) ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($branch['name']); ?>
                                    </option>
                                    <?php endforeach; ?>
                                <?php elseif (!$isAdmin && $branchId): ?>
                                    <option value="<?php echo $branchId; ?>" selected>
                                        <?php echo htmlspecialchars($_SESSION['user_branch_name']); ?>
                                    </option>
                                <?php endif; ?>
                            </select>
                            <div class="form-text">اترك الحقل فارغًا لتطبيق العرض على جميع الفروع</div>
                        </div>
                    </div>
                </div>

                <div class="mb-3">
                    <label for="description" class="form-label">وصف العرض</label>
                    <textarea class="form-control" id="description" name="description" rows="3"></textarea>
                </div>

                <!-- شروط العرض -->
                <div class="row mb-4">
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label for="condition_type" class="form-label">نوع الشرط <span class="text-danger">*</span></label>
                            <select class="form-select" id="condition_type" name="condition_type" required>
                                <option value="total_amount">إجمالي قيمة الفاتورة</option>
                                <option value="items_count">عدد العناصر في الفاتورة</option>
                                <option value="specific_product">منتج محدد</option>
                                <option value="specific_service">خدمة محددة</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label for="condition_value" class="form-label">الحد الأدنى للشرط <span class="text-danger">*</span></label>
                            <input type="number" class="form-control" id="condition_value" name="condition_value" step="0.01" min="0" required>
                            <div class="form-text" id="condition_value_help">أدخل الحد الأدنى للشرط (مثال: 500 جنيه للإجمالي)</div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label for="condition_max_value" class="form-label">الحد الأقصى للشرط</label>
                            <input type="number" class="form-control" id="condition_max_value" name="condition_max_value" step="0.01" min="0">
                            <div class="form-text" id="condition_max_value_help">أدخل الحد الأقصى للشرط (مثال: 1000 جنيه للإجمالي) - اختياري</div>
                        </div>
                    </div>
                </div>

                <!-- العنصر المحدد (للمنتج أو الخدمة) -->
                <div class="mb-4" id="specific_item_container" style="display: none;">
                    <div class="mb-3">
                        <label for="specific_item_id" class="form-label">اختر العنصر <span class="text-danger">*</span></label>
                        <select class="form-select" id="specific_item_id" name="specific_item_id">
                            <option value="">-- اختر --</option>
                            <!-- سيتم ملء هذه القائمة ديناميكيًا بناءً على نوع الشرط -->
                        </select>
                    </div>
                </div>

                <!-- معلومات الخصم -->
                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="discount_type" class="form-label">نوع الخصم <span class="text-danger">*</span></label>
                            <select class="form-select" id="discount_type" name="discount_type" required>
                                <option value="percentage">نسبة مئوية (%)</option>
                                <option value="fixed">مبلغ ثابت (ج.م)</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="discount_value" class="form-label">قيمة الخصم <span class="text-danger">*</span></label>
                            <input type="number" class="form-control" id="discount_value" name="discount_value" step="0.01" min="0" required>
                            <div class="form-text" id="discount_value_help">أدخل قيمة الخصم (مثال: 15 للنسبة المئوية، 100 للمبلغ الثابت)</div>
                        </div>
                    </div>
                </div>

                <!-- فترة العرض -->
                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="start_date" class="form-label">تاريخ بداية العرض</label>
                            <input type="date" class="form-control" id="start_date" name="start_date">
                            <div class="form-text">اترك الحقل فارغًا إذا كان العرض غير محدد البداية</div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="end_date" class="form-label">تاريخ نهاية العرض</label>
                            <input type="date" class="form-control" id="end_date" name="end_date">
                            <div class="form-text">اترك الحقل فارغًا إذا كان العرض غير محدد النهاية</div>
                        </div>
                    </div>
                </div>

                <!-- حالة العرض -->
                <div class="mb-4">
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="is_active" name="is_active" value="1" checked>
                        <label class="form-check-label" for="is_active">
                            العرض نشط
                        </label>
                    </div>
                </div>

                <!-- أزرار الإجراءات -->
                <div class="d-flex justify-content-end">
                    <button type="button" class="btn btn-secondary me-2" onclick="window.location.href='index.php'">إلغاء</button>
                    <button type="submit" class="btn btn-primary">حفظ العرض</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- استدعاء قالب الفوتر -->
<?php include '../../includes/templates/footer.php'; ?>

<script>
$(document).ready(function() {
    // قائمة المنتجات والخدمات
    const products = <?php echo json_encode($products); ?>;
    const services = <?php echo json_encode($services); ?>;

    // عند تغيير نوع الشرط
    $('#condition_type').on('change', function() {
        const conditionType = $(this).val();
        const specificItemContainer = $('#specific_item_container');
        const specificItemSelect = $('#specific_item_id');

        // تحديث نص المساعدة لقيمة الشرط
        if (conditionType === 'total_amount') {
            $('#condition_value_help').text('أدخل الحد الأدنى لقيمة الفاتورة بالجنيه المصري');
            $('#condition_max_value_help').text('أدخل الحد الأقصى لقيمة الفاتورة بالجنيه المصري - اختياري');
        } else if (conditionType === 'items_count') {
            $('#condition_value_help').text('أدخل الحد الأدنى لعدد العناصر في الفاتورة');
            $('#condition_max_value_help').text('أدخل الحد الأقصى لعدد العناصر في الفاتورة - اختياري');
        } else {
            $('#condition_value_help').text('أدخل الحد الأدنى للكمية المطلوبة من المنتج/الخدمة');
            $('#condition_max_value_help').text('أدخل الحد الأقصى للكمية المطلوبة من المنتج/الخدمة - اختياري');
        }

        // إظهار/إخفاء حقل اختيار العنصر المحدد
        if (conditionType === 'specific_product' || conditionType === 'specific_service') {
            specificItemContainer.show();
            specificItemSelect.prop('required', true);

            // تفريغ القائمة
            specificItemSelect.empty();
            specificItemSelect.append('<option value="">-- اختر --</option>');

            // ملء القائمة بالعناصر المناسبة
            if (conditionType === 'specific_product') {
                products.forEach(function(product) {
                    specificItemSelect.append(`<option value="${product.id}">${product.name}</option>`);
                });
            } else if (conditionType === 'specific_service') {
                services.forEach(function(service) {
                    specificItemSelect.append(`<option value="${service.id}">${service.name}</option>`);
                });
            }
        } else {
            specificItemContainer.hide();
            specificItemSelect.prop('required', false);
        }
    });

    // عند تغيير نوع الخصم
    $('#discount_type').on('change', function() {
        const discountType = $(this).val();

        // تحديث نص المساعدة لقيمة الخصم
        if (discountType === 'percentage') {
            $('#discount_value_help').text('أدخل نسبة الخصم (مثال: 15 تعني 15%)');
        } else {
            $('#discount_value_help').text('أدخل قيمة الخصم بالجنيه المصري');
        }
    });

    // التحقق من تواريخ البداية والنهاية
    $('#end_date').on('change', function() {
        const startDate = $('#start_date').val();
        const endDate = $(this).val();

        if (startDate && endDate && new Date(endDate) < new Date(startDate)) {
            alert('تاريخ نهاية العرض يجب أن يكون بعد تاريخ البداية');
            $(this).val('');
        }
    });

    $('#start_date').on('change', function() {
        const startDate = $(this).val();
        const endDate = $('#end_date').val();

        if (startDate && endDate && new Date(endDate) < new Date(startDate)) {
            alert('تاريخ بداية العرض يجب أن يكون قبل تاريخ النهاية');
            $(this).val('');
        }
    });

    // إرسال النموذج
    $('#addPromotionForm').on('submit', function(e) {
        e.preventDefault();

        // التحقق من صحة البيانات
        const conditionType = $('#condition_type').val();
        if ((conditionType === 'specific_product' || conditionType === 'specific_service') && !$('#specific_item_id').val()) {
            alert('يرجى اختيار العنصر المحدد');
            return;
        }

        // جمع بيانات النموذج
        const formData = {
            name: $('#name').val(),
            description: $('#description').val(),
            condition_type: conditionType,
            condition_value: $('#condition_value').val(),
            condition_max_value: $('#condition_max_value').val() || null,
            discount_type: $('#discount_type').val(),
            discount_value: $('#discount_value').val(),
            start_date: $('#start_date').val() || null,
            end_date: $('#end_date').val() || null,
            is_active: $('#is_active').is(':checked') ? 1 : 0,
            branch_id: $('#branch_id').val() || null,
            specific_item_id: $('#specific_item_id').val() || null
        };

        // إرسال البيانات
        $.ajax({
            url: '../../api/promotions.php?action=add',
            type: 'POST',
            data: formData,
            dataType: 'json',
            success: function(response) {
                if (response.status === 'success') {
                    // عرض رسالة نجاح
                    showAlert('success', response.message);

                    // إعادة توجيه المستخدم إلى صفحة القائمة بعد ثانيتين
                    setTimeout(function() {
                        window.location.href = 'index.php';
                    }, 2000);
                } else {
                    // عرض رسالة الخطأ
                    showAlert('danger', response.message);
                }
            },
            error: function() {
                // عرض رسالة خطأ عامة
                showAlert('danger', 'حدث خطأ أثناء معالجة الطلب. يرجى المحاولة مرة أخرى.');
            }
        });
    });

    // دالة لعرض التنبيهات
    function showAlert(type, message) {
        const alertHtml = `
            <div class="alert alert-${type} alert-dismissible fade show" role="alert">
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        `;

        // إضافة التنبيه في أعلى الصفحة
        $('.container-fluid').prepend(alertHtml);

        // التمرير إلى أعلى الصفحة
        window.scrollTo(0, 0);

        // إزالة التنبيه تلقائيًا بعد 5 ثوانٍ
        setTimeout(function() {
            $('.alert').alert('close');
        }, 5000);
    }

    // تشغيل التغييرات الأولية
    $('#condition_type').trigger('change');
    $('#discount_type').trigger('change');
});
</script>
