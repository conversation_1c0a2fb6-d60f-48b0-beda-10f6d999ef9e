<?php

/**

 * فئة العميل

 * تتعامل مع إدارة العملاء ونقاط الولاء

 */



// منع الوصول المباشر للملف

if (!defined('BASEPATH')) {

    exit('لا يمكن الوصول المباشر لهذا الملف');

}



class Customer {

    private $db;



    /**

     * إنشاء كائن من فئة العميل

     * @param Database $db كائن قاعدة البيانات

     */

    public function __construct($db) {

        $this->db = $db;

    }



    /**

     * إضافة عميل جديد

     * @param array $customerData بيانات العميل

     * @return int|false معرف العميل الجديد أو false إذا فشلت العملية

     */

    public function addCustomer($customerData) {

        try {

            // التحقق من وجود العميل بنفس رقم الهاتف

            $this->db->prepare("SELECT id FROM customers WHERE phone = :phone");

            $this->db->bind(':phone', $customerData['phone']);

            if ($this->db->fetch()) {

                throw new Exception('يوجد عميل مسجل بنفس رقم الهاتف');

            }



            $this->db->prepare("INSERT INTO customers (name, phone, email, address, birthday, notes, branch_id)
                              VALUES (:name, :phone, :email, :address, :birthday, :notes, :branch_id)");

            $this->db->bind(':name', $customerData['name']);
            $this->db->bind(':phone', $customerData['phone']);
            $this->db->bind(':email', $customerData['email'] ?? null);
            $this->db->bind(':address', $customerData['address'] ?? null);
            $this->db->bind(':birthday', $customerData['birthday'] ?? null);
            $this->db->bind(':notes', $customerData['notes'] ?? null);
            $this->db->bind(':branch_id', $customerData['branch_id'] ?? null);

            $this->db->execute();

            return $this->db->lastInsertId();

        } catch (Exception $e) {

            error_log('خطأ أثناء إضافة العميل: ' . $e->getMessage());

            throw $e;

        }

    }



    /**

     * تحديث بيانات عميل

     * @param int $customerId معرف العميل

     * @param array $customerData البيانات المحدثة

     * @return bool نجاح أو فشل العملية

     */

    public function updateCustomer($customerId, $customerData) {

        try {

            // التحقق من وجود العميل بنفس رقم الهاتف (مع استثناء العميل الحالي)

            $this->db->prepare("SELECT id FROM customers WHERE phone = :phone AND id != :id");

            $this->db->bind(':phone', $customerData['phone']);

            $this->db->bind(':id', $customerId);

            if ($this->db->fetch()) {

                throw new Exception('يوجد عميل آخر مسجل بنفس رقم الهاتف');

            }



            $this->db->prepare("UPDATE customers
                              SET name = :name,
                                  phone = :phone,
                                  email = :email,
                                  address = :address,
                                  birthday = :birthday,
                                  notes = :notes,
                                  branch_id = :branch_id
                              WHERE id = :id");

            $this->db->bind(':name', $customerData['name']);
            $this->db->bind(':phone', $customerData['phone']);
            $this->db->bind(':email', $customerData['email'] ?? null);
            $this->db->bind(':address', $customerData['address'] ?? null);
            $this->db->bind(':birthday', $customerData['birthday'] ?? null);
            $this->db->bind(':notes', $customerData['notes'] ?? null);
            $this->db->bind(':branch_id', $customerData['branch_id'] ?? null);
            $this->db->bind(':id', $customerId);

            return $this->db->execute();

        } catch (Exception $e) {

            error_log('خطأ أثناء تحديث بيانات العميل: ' . $e->getMessage());

            throw $e;

        }

    }



    /**

     * حذف عميل

     * @param int $customerId معرف العميل

     * @return bool نجاح أو فشل العملية

     */

    public function deleteCustomer($customerId) {

        try {

            $this->db->prepare("DELETE FROM customers WHERE id = :id");

            $this->db->bind(':id', $customerId);

            return $this->db->execute();

        } catch (Exception $e) {

            error_log('خطأ أثناء حذف العميل: ' . $e->getMessage());

            throw $e;

        }

    }



    /**

     * الحصول على بيانات عميل بواسطة المعرف

     * @param int $customerId معرف العميل

     * @return array|false بيانات العميل أو false إذا لم يتم العثور عليه

     */

    public function getCustomerById($customerId) {

        try {

            // استعلام محسن للحصول على بيانات العميل مع عدد الزيارات وإجمالي المبيعات
            $this->db->prepare("SELECT c.*, b.name as branch_name,
                                COUNT(DISTINCT i.id) as visits_count,
                                SUM(i.final_amount) as total_sales
                              FROM customers c
                              LEFT JOIN branches b ON c.branch_id = b.id
                              LEFT JOIN invoices i ON c.id = i.customer_id
                              WHERE c.id = :id
                              GROUP BY c.id");

            $this->db->bind(':id', $customerId);

            return $this->db->fetch();

        } catch (Exception $e) {

            error_log('خطأ أثناء استرجاع بيانات العميل: ' . $e->getMessage());

            throw $e;

        }

    }



    /**

     * البحث عن عميل بواسطة رقم الهاتف

     * @param string $phone رقم الهاتف

     * @return array|false بيانات العميل أو false إذا لم يتم العثور عليه

     */

    public function getCustomerByPhone($phone) {

        try {

            $this->db->prepare("SELECT c.*, b.name as branch_name,

                                COUNT(DISTINCT i.id) as visits_count,

                                SUM(i.final_amount) as total_sales

                              FROM customers c

                              LEFT JOIN branches b ON c.branch_id = b.id

                              LEFT JOIN invoices i ON c.id = i.customer_id

                              WHERE c.phone = :phone

                              GROUP BY c.id");

            $this->db->bind(':phone', $phone);

            return $this->db->fetch();

        } catch (Exception $e) {

            error_log('خطأ أثناء البحث عن العميل: ' . $e->getMessage());

            throw $e;

        }

    }



    /**

     * الحصول على قائمة العملاء

     * @param array $filters فلاتر البحث (اختياري)

     * @return array قائمة العملاء

     */

    public function getCustomers($filters = []) {

        try {

            // تحديد المسار الكامل لملف السجل

            $logFile = dirname(dirname(__DIR__)) . '/api/customer_debug.log';



            // التأكد من وجود المجلد

            $logDir = dirname($logFile);

            if (!is_dir($logDir)) {

                mkdir($logDir, 0777, true);

            }



            // كتابة سجل تصحيح مخصص

            file_put_contents($logFile, "=== بدء استعلام العملاء ===\n", FILE_APPEND);

            file_put_contents($logFile, "الفلاتر المستلمة: " . json_encode($filters, JSON_UNESCAPED_UNICODE) . "\n", FILE_APPEND);



            $sql = "SELECT c.*, b.name as branch_name,

                    COUNT(DISTINCT i.id) as visits_count,

                    SUM(i.final_amount) as total_sales

                    FROM customers c

                    LEFT JOIN branches b ON c.branch_id = b.id

                    LEFT JOIN invoices i ON c.id = i.customer_id";



            $whereConditions = [];

            $bindings = [];



            // تطبيق الفلاتر

            if (!empty($filters['search'])) {

                $searchValue = '%' . $filters['search'] . '%';

                $whereConditions[] = "(c.name LIKE :search_name OR c.phone LIKE :search_phone OR c.email LIKE :search_email)";

                $bindings[':search_name'] = $searchValue;

                $bindings[':search_phone'] = $searchValue;

                $bindings[':search_email'] = $searchValue;

                file_put_contents($logFile, "إضافة فلتر البحث: " . $filters['search'] . "\n", FILE_APPEND);

            }



            if (!empty($filters['branch_id'])) {

                $whereConditions[] = "c.branch_id = :branch_id";

                $bindings[':branch_id'] = $filters['branch_id'];

                file_put_contents($logFile, "إضافة فلتر الفرع: " . $filters['branch_id'] . "\n", FILE_APPEND);

            }



            // فلتر تاريخ التسجيل

            if (!empty($filters['date_from'])) {

                // استخدام التاريخ كما هو بدون محاولة تنسيقه

                $dateFrom = $filters['date_from'];

                file_put_contents($logFile, "فلتر تاريخ من المستلم: " . $dateFrom . "\n", FILE_APPEND);



                $whereConditions[] = "DATE(c.created_at) >= :date_from";

                $bindings[':date_from'] = $dateFrom;

                file_put_contents($logFile, "إضافة فلتر تاريخ من: " . $dateFrom . "\n", FILE_APPEND);

            }



            if (!empty($filters['date_to'])) {

                // استخدام التاريخ كما هو بدون محاولة تنسيقه

                $dateTo = $filters['date_to'];

                file_put_contents($logFile, "فلتر تاريخ إلى المستلم: " . $dateTo . "\n", FILE_APPEND);



                $whereConditions[] = "DATE(c.created_at) <= :date_to";

                $bindings[':date_to'] = $dateTo;

                file_put_contents($logFile, "إضافة فلتر تاريخ إلى: " . $dateTo . "\n", FILE_APPEND);

            }



            // فلتر نقاط الولاء

            if (isset($filters['loyalty_points_min']) && $filters['loyalty_points_min'] >= 0) {

                $whereConditions[] = "c.loyalty_points >= :loyalty_points_min";

                $bindings[':loyalty_points_min'] = $filters['loyalty_points_min'];

                file_put_contents($logFile, "إضافة فلتر نقاط الولاء من: " . $filters['loyalty_points_min'] . "\n", FILE_APPEND);

            }



            if (isset($filters['loyalty_points_max']) && $filters['loyalty_points_max'] > 0) {

                $whereConditions[] = "c.loyalty_points <= :loyalty_points_max";

                $bindings[':loyalty_points_max'] = $filters['loyalty_points_max'];

                file_put_contents($logFile, "إضافة فلتر نقاط الولاء إلى: " . $filters['loyalty_points_max'] . "\n", FILE_APPEND);

            }



            // فلتر عدد الزيارات

            // ملاحظة: هذه الفلاتر ستطبق بعد الـ GROUP BY باستخدام HAVING

            if (isset($filters['visits_min']) && $filters['visits_min'] >= 0) {

                $havingConditions[] = "COUNT(DISTINCT i.id) >= :visits_min";

                $bindings[':visits_min'] = $filters['visits_min'];

                file_put_contents($logFile, "إضافة فلتر عدد الزيارات من: " . $filters['visits_min'] . "\n", FILE_APPEND);

            }



            if (isset($filters['visits_max']) && $filters['visits_max'] > 0) {

                $havingConditions[] = "COUNT(DISTINCT i.id) <= :visits_max";

                $bindings[':visits_max'] = $filters['visits_max'];

                file_put_contents($logFile, "إضافة فلتر عدد الزيارات إلى: " . $filters['visits_max'] . "\n", FILE_APPEND);

            }



            // تهيئة متغير لشروط HAVING إذا لم يكن موجودًا

            if (!isset($havingConditions)) {

                $havingConditions = [];

            }



            // إضافة شروط WHERE إذا وجدت

            if (!empty($whereConditions)) {

                $sql .= " WHERE " . implode(' AND ', $whereConditions);

            }



            // إضافة GROUP BY

            $sql .= " GROUP BY c.id";



            // إضافة شروط HAVING إذا وجدت

            if (!empty($havingConditions)) {

                $sql .= " HAVING " . implode(' AND ', $havingConditions);

            }



            // ترتيب النتائج

            $sql .= " ORDER BY c.name ASC";



            // إضافة الحد والإزاحة إذا وجدت

            if (!empty($filters['limit'])) {

                $sql .= " LIMIT :limit";

                $bindings[':limit'] = (int)$filters['limit'];



                if (!empty($filters['offset'])) {

                    $sql .= " OFFSET :offset";

                    $bindings[':offset'] = (int)$filters['offset'];

                }

            }



            file_put_contents($logFile, "استعلام SQL النهائي: " . $sql . "\n", FILE_APPEND);

            file_put_contents($logFile, "المعلمات: " . json_encode($bindings, JSON_UNESCAPED_UNICODE) . "\n", FILE_APPEND);



            $this->db->prepare($sql);



            // ربط القيم

            foreach ($bindings as $param => $value) {

                $paramType = is_int($value) ? PDO::PARAM_INT : PDO::PARAM_STR;

                $this->db->bind($param, $value, $paramType);

                file_put_contents($logFile, "ربط المعلمة: " . $param . " = " . $value . " (النوع: " . $paramType . ")\n", FILE_APPEND);

            }



            try {

                $result = $this->db->fetchAll();

                file_put_contents($logFile, "تم استرجاع " . count($result) . " عميل\n", FILE_APPEND);

                file_put_contents($logFile, "=== نهاية استعلام العملاء ===\n\n", FILE_APPEND);

                return $result;

            } catch (Exception $innerEx) {

                file_put_contents($logFile, "خطأ أثناء تنفيذ الاستعلام: " . $innerEx->getMessage() . "\n", FILE_APPEND);

                file_put_contents($logFile, "تتبع الخطأ: " . $innerEx->getTraceAsString() . "\n", FILE_APPEND);

                throw $innerEx;

            }

        } catch (Exception $e) {

            // استخدام error_log بدلاً من file_put_contents في حالة الخطأ

            error_log("خطأ أثناء استرجاع قائمة العملاء: " . $e->getMessage());

            error_log("تتبع الخطأ: " . $e->getTraceAsString());

            throw $e;

        }

    }



    /**

     * إضافة زيارة للعميل

     * @param int $customerId معرف العميل

     * @param array $visitData بيانات الزيارة

     * @return string|int|false معرف الزيارة الجديدة أو false إذا فشلت العملية

     */

    public function addCustomerVisit($customerId, $visitData) {

        try {

            $this->db->prepare("INSERT INTO customer_visits (customer_id, notes, branch_id)

                              VALUES (:customer_id, :notes, :branch_id)");

            $this->db->bind(':customer_id', $customerId);

            $this->db->bind(':notes', $visitData['notes'] ?? null);

            $this->db->bind(':branch_id', $visitData['branch_id'] ?? null);



            $this->db->execute();

            return $this->db->lastInsertId();

        } catch (Exception $e) {

            error_log('خطأ أثناء إضافة زيارة للعميل: ' . $e->getMessage());

            throw $e;

        }

    }



    /**

     * الحصول على زيارات العميل

     * @param int $customerId معرف العميل

     * @param string $startDate تاريخ البداية (اختياري)

     * @param string $endDate تاريخ النهاية (اختياري)

     * @param int $limit عدد الزيارات المطلوبة (الافتراضي: 50)

     * @return array قائمة زيارات العميل

     */

    public function getCustomerVisits($customerId, $startDate = null, $endDate = null, $limit = 50) {

        try {

            $sql = "SELECT cv.*, b.name as branch_name, e.name as employee_name

                   FROM customer_visits cv

                   LEFT JOIN branches b ON cv.branch_id = b.id

                   LEFT JOIN employees e ON cv.employee_id = e.id

                   WHERE cv.customer_id = :customer_id";

            

            $bindings = [':customer_id' => $customerId];

            

            // إضافة فلتر التاريخ إذا تم تحديده

            if ($startDate && $endDate) {

                $sql .= " AND cv.visit_date BETWEEN :start_date AND :end_date";

                $bindings[':start_date'] = $startDate . ' 00:00:00';

                $bindings[':end_date'] = $endDate . ' 23:59:59';

            }

            

            $sql .= " ORDER BY cv.visit_date DESC LIMIT :limit";

            $bindings[':limit'] = $limit;

            

            $this->db->prepare($sql);

            foreach ($bindings as $key => $value) {

                if ($key === ':limit') {

                    $this->db->bind($key, $value, PDO::PARAM_INT);

                } else {

                    $this->db->bind($key, $value);

                }

            }

            

            return $this->db->fetchAll();

        } catch (Exception $e) {

            error_log('خطأ أثناء استرجاع زيارات العميل: ' . $e->getMessage());

            return [];

        }

    }



    /**

     * إضافة نقاط ولاء للعميل

     * @param int $customerId معرف العميل

     * @param int $points عدد النقاط

     * @return bool نجاح أو فشل العملية

     */

    public function addLoyaltyPoints($customerId, $points) {

        try {

            $this->db->prepare("UPDATE customers

                              SET loyalty_points = loyalty_points + :points

                              WHERE id = :id");

            $this->db->bind(':points', $points, PDO::PARAM_INT);

            $this->db->bind(':id', $customerId);



            return $this->db->execute();

        } catch (Exception $e) {

            error_log('خطأ أثناء إضافة نقاط ولاء للعميل: ' . $e->getMessage());

            throw $e;

        }

    }



    /**

     * استخدام نقاط ولاء للعميل

     * @param int $customerId معرف العميل

     * @param int $points عدد النقاط

     * @return bool نجاح أو فشل العملية

     */

    public function useLoyaltyPoints($customerId, $points) {

        try {

            // التحقق من وجود نقاط كافية

            $this->db->prepare("SELECT loyalty_points FROM customers WHERE id = :id");

            $this->db->bind(':id', $customerId);

            $customer = $this->db->fetch();



            if (!$customer || $customer['loyalty_points'] < $points) {

                throw new Exception('لا توجد نقاط ولاء كافية');

            }



            $this->db->prepare("UPDATE customers

                              SET loyalty_points = loyalty_points - :points

                              WHERE id = :id");

            $this->db->bind(':points', $points, PDO::PARAM_INT);

            $this->db->bind(':id', $customerId);



            return $this->db->execute();

        } catch (Exception $e) {

            error_log('خطأ أثناء استخدام نقاط ولاء للعميل: ' . $e->getMessage());

            throw $e;

        }

    }



    /**

     * الحصول على إجمالي عدد العملاء

     * @param array $filters فلاتر البحث (اختياري)

     * @return int عدد العملاء

     */

    public function getCustomersCount($filters = []) {

        try {

            // تحديد المسار الكامل لملف السجل

            $logFile = dirname(dirname(__DIR__)) . '/api/customer_count_debug.log';



            // التأكد من وجود المجلد

            $logDir = dirname($logFile);

            if (!is_dir($logDir)) {

                mkdir($logDir, 0777, true);

            }



            // كتابة سجل تصحيح مخصص

            file_put_contents($logFile, "=== بدء استعلام عدد العملاء ===\n", FILE_APPEND);

            file_put_contents($logFile, "الفلاتر المستلمة: " . json_encode($filters, JSON_UNESCAPED_UNICODE) . "\n", FILE_APPEND);



            // إذا كان هناك فلاتر لعدد الزيارات، نستخدم نفس الاستعلام من getCustomers

            if (isset($filters['visits_min']) || isset($filters['visits_max'])) {

                // استخدام نفس الاستعلام من getCustomers ولكن مع COUNT(*)

                $sql = "SELECT COUNT(*) FROM (SELECT c.id

                      FROM customers c

                      LEFT JOIN invoices i ON c.id = i.customer_id";



                $whereConditions = [];

                $havingConditions = [];

                $bindings = [];



                // تطبيق الفلاتر

                if (!empty($filters['search'])) {

                    $whereConditions[] = "(c.name LIKE :search_name OR c.phone LIKE :search_phone OR c.email LIKE :search_email)";

                    $bindings[':search_name'] = '%' . $filters['search'] . '%';

                    $bindings[':search_phone'] = '%' . $filters['search'] . '%';

                    $bindings[':search_email'] = '%' . $filters['search'] . '%';

                    file_put_contents($logFile, "إضافة فلتر البحث: " . $filters['search'] . "\n", FILE_APPEND);

                }



                if (!empty($filters['branch_id'])) {

                    $whereConditions[] = "c.branch_id = :branch_id";

                    $bindings[':branch_id'] = $filters['branch_id'];

                    file_put_contents($logFile, "إضافة فلتر الفرع: " . $filters['branch_id'] . "\n", FILE_APPEND);

                }



                // فلتر تاريخ التسجيل

                if (!empty($filters['date_from'])) {

                    // استخدام التاريخ كما هو بدون محاولة تنسيقه

                    $dateFrom = $filters['date_from'];

                    file_put_contents($logFile, "فلتر تاريخ من المستلم: " . $dateFrom . "\n", FILE_APPEND);



                    $whereConditions[] = "DATE(c.created_at) >= :date_from";

                    $bindings[':date_from'] = $dateFrom;

                    file_put_contents($logFile, "إضافة فلتر تاريخ من: " . $dateFrom . "\n", FILE_APPEND);

                }



                if (!empty($filters['date_to'])) {

                    // استخدام التاريخ كما هو بدون محاولة تنسيقه

                    $dateTo = $filters['date_to'];

                    file_put_contents($logFile, "فلتر تاريخ إلى المستلم: " . $dateTo . "\n", FILE_APPEND);



                    $whereConditions[] = "DATE(c.created_at) <= :date_to";

                    $bindings[':date_to'] = $dateTo;

                    file_put_contents($logFile, "إضافة فلتر تاريخ إلى: " . $dateTo . "\n", FILE_APPEND);

                }



                // فلتر نقاط الولاء

                if (isset($filters['loyalty_points_min']) && $filters['loyalty_points_min'] >= 0) {

                    $whereConditions[] = "c.loyalty_points >= :loyalty_points_min";

                    $bindings[':loyalty_points_min'] = $filters['loyalty_points_min'];

                    file_put_contents($logFile, "إضافة فلتر نقاط الولاء من: " . $filters['loyalty_points_min'] . "\n", FILE_APPEND);

                }



                if (isset($filters['loyalty_points_max']) && $filters['loyalty_points_max'] > 0) {

                    $whereConditions[] = "c.loyalty_points <= :loyalty_points_max";

                    $bindings[':loyalty_points_max'] = $filters['loyalty_points_max'];

                    file_put_contents($logFile, "إضافة فلتر نقاط الولاء إلى: " . $filters['loyalty_points_max'] . "\n", FILE_APPEND);

                }



                // فلتر عدد الزيارات

                if (isset($filters['visits_min']) && $filters['visits_min'] >= 0) {

                    $havingConditions[] = "COUNT(DISTINCT i.id) >= :visits_min";

                    $bindings[':visits_min'] = $filters['visits_min'];

                    file_put_contents($logFile, "إضافة فلتر عدد الزيارات من: " . $filters['visits_min'] . "\n", FILE_APPEND);

                }



                if (isset($filters['visits_max']) && $filters['visits_max'] > 0) {

                    $havingConditions[] = "COUNT(DISTINCT i.id) <= :visits_max";

                    $bindings[':visits_max'] = $filters['visits_max'];

                    file_put_contents($logFile, "إضافة فلتر عدد الزيارات إلى: " . $filters['visits_max'] . "\n", FILE_APPEND);

                }



                // إضافة شروط WHERE إذا وجدت

                if (!empty($whereConditions)) {

                    $sql .= " WHERE " . implode(' AND ', $whereConditions);

                }



                // إضافة GROUP BY

                $sql .= " GROUP BY c.id";



                // إضافة شروط HAVING إذا وجدت

                if (!empty($havingConditions)) {

                    $sql .= " HAVING " . implode(' AND ', $havingConditions);

                }



                $sql .= ") AS filtered_customers";

            } else {

                // استعلام بسيط لعدد العملاء

                $sql = "SELECT COUNT(*) FROM customers c";



                $whereConditions = [];

                $bindings = [];



                // تطبيق الفلاتر

                if (!empty($filters['search'])) {

                    $whereConditions[] = "(c.name LIKE :search_name OR c.phone LIKE :search_phone OR c.email LIKE :search_email)";

                    $bindings[':search_name'] = '%' . $filters['search'] . '%';

                    $bindings[':search_phone'] = '%' . $filters['search'] . '%';

                    $bindings[':search_email'] = '%' . $filters['search'] . '%';

                    file_put_contents($logFile, "إضافة فلتر البحث: " . $filters['search'] . "\n", FILE_APPEND);

                }



                if (!empty($filters['branch_id'])) {

                    $whereConditions[] = "c.branch_id = :branch_id";

                    $bindings[':branch_id'] = $filters['branch_id'];

                    file_put_contents($logFile, "إضافة فلتر الفرع: " . $filters['branch_id'] . "\n", FILE_APPEND);

                }



                // فلتر تاريخ التسجيل

                if (!empty($filters['date_from'])) {

                    // استخدام التاريخ كما هو بدون محاولة تنسيقه

                    $dateFrom = $filters['date_from'];

                    file_put_contents($logFile, "فلتر تاريخ من المستلم: " . $dateFrom . "\n", FILE_APPEND);



                    $whereConditions[] = "DATE(c.created_at) >= :date_from";

                    $bindings[':date_from'] = $dateFrom;

                    file_put_contents($logFile, "إضافة فلتر تاريخ من: " . $dateFrom . "\n", FILE_APPEND);

                }



                if (!empty($filters['date_to'])) {

                    // استخدام التاريخ كما هو بدون محاولة تنسيقه

                    $dateTo = $filters['date_to'];

                    file_put_contents($logFile, "فلتر تاريخ إلى المستلم: " . $dateTo . "\n", FILE_APPEND);



                    $whereConditions[] = "DATE(c.created_at) <= :date_to";

                    $bindings[':date_to'] = $dateTo;

                    file_put_contents($logFile, "إضافة فلتر تاريخ إلى: " . $dateTo . "\n", FILE_APPEND);

                }



                // فلتر نقاط الولاء

                if (isset($filters['loyalty_points_min']) && $filters['loyalty_points_min'] >= 0) {

                    $whereConditions[] = "c.loyalty_points >= :loyalty_points_min";

                    $bindings[':loyalty_points_min'] = $filters['loyalty_points_min'];

                    file_put_contents($logFile, "إضافة فلتر نقاط الولاء من: " . $filters['loyalty_points_min'] . "\n", FILE_APPEND);

                }



                if (isset($filters['loyalty_points_max']) && $filters['loyalty_points_max'] > 0) {

                    $whereConditions[] = "c.loyalty_points <= :loyalty_points_max";

                    $bindings[':loyalty_points_max'] = $filters['loyalty_points_max'];

                    file_put_contents($logFile, "إضافة فلتر نقاط الولاء إلى: " . $filters['loyalty_points_max'] . "\n", FILE_APPEND);

                }



                // إضافة شروط WHERE إذا وجدت

                if (!empty($whereConditions)) {

                    $sql .= " WHERE " . implode(' AND ', $whereConditions);

                }

            }



            file_put_contents($logFile, "استعلام SQL النهائي: " . $sql . "\n", FILE_APPEND);

            file_put_contents($logFile, "المعلمات: " . json_encode($bindings, JSON_UNESCAPED_UNICODE) . "\n", FILE_APPEND);



            $this->db->prepare($sql);



            // ربط القيم

            foreach ($bindings as $param => $value) {

                $paramType = is_int($value) ? PDO::PARAM_INT : PDO::PARAM_STR;

                $this->db->bind($param, $value, $paramType);

                file_put_contents($logFile, "ربط المعلمة: " . $param . " = " . $value . " (النوع: " . $paramType . ")\n", FILE_APPEND);

            }



            try {

                $result = $this->db->fetchColumn();

                file_put_contents($logFile, "تم استرجاع العدد: " . $result . "\n", FILE_APPEND);

                file_put_contents($logFile, "=== نهاية استعلام عدد العملاء ===\n\n", FILE_APPEND);

                return $result;

            } catch (Exception $innerEx) {

                file_put_contents($logFile, "خطأ أثناء تنفيذ الاستعلام: " . $innerEx->getMessage() . "\n", FILE_APPEND);

                file_put_contents($logFile, "تتبع الخطأ: " . $innerEx->getTraceAsString() . "\n", FILE_APPEND);

                throw $innerEx;

            }

        } catch (Exception $e) {

            error_log('خطأ أثناء حساب عدد العملاء: ' . $e->getMessage());

            error_log('تتبع الخطأ: ' . $e->getTraceAsString());

            throw $e;

        }

    }



    /**

     * الحصول على العملاء الأكثر زيارة

     * @param int $limit عدد النتائج

     * @param int $branchId معرف الفرع (اختياري)

     * @return array قائمة العملاء

     */

    public function getTopCustomers($limit = 5, $branchId = null) {

        try {

            // استخدام استعلام محسن لحساب عدد الزيارات ونقاط الولاء بشكل صحيح

            $sql = "SELECT c.*,

                      COUNT(DISTINCT i.id) as visits_count,

                      SUM(i.final_amount) as total_sales

                    FROM customers c

                    LEFT JOIN invoices i ON c.id = i.customer_id";



            $whereConditions = [];

            $bindings = [];



            if ($branchId) {

                $whereConditions[] = "c.branch_id = :branch_id";

                $bindings[':branch_id'] = $branchId;

            }



            // إضافة شروط WHERE إذا وجدت

            if (!empty($whereConditions)) {

                $sql .= " WHERE " . implode(' AND ', $whereConditions);

            }



            $sql .= " GROUP BY c.id

                      ORDER BY total_sales DESC

                      LIMIT :limit";



            $this->db->prepare($sql);



            // ربط القيم

            foreach ($bindings as $param => $value) {

                $this->db->bind($param, $value);

            }



            $this->db->bind(':limit', $limit, PDO::PARAM_INT);



            return $this->db->fetchAll();

        } catch (Exception $e) {

            error_log('خطأ أثناء استرجاع العملاء الأكثر زيارة: ' . $e->getMessage());

            throw $e;

        }

    }



    /**
     * الحصول على إجمالي مشتريات العميل خلال فترة محددة
     * @param int $customerId معرف العميل
     * @param string $startDate تاريخ البداية (اختياري)
     * @param string $endDate تاريخ النهاية (اختياري)
     * @return float إجمالي المشتريات
     */
    public function getCustomerTotalSpending($customerId, $startDate = null, $endDate = null) {
        try {
            // تسجيل الاستدعاء للتصحيح
            error_log("getCustomerTotalSpending - Customer ID: $customerId, Start Date: $startDate, End Date: $endDate");
            
            $sql = "SELECT SUM(final_amount) as total_spending 
                    FROM invoices 
                    WHERE customer_id = :customer_id";
            
            $bindings = [':customer_id' => $customerId];
            
            // إضافة فلتر التاريخ إذا تم تحديده
            if ($startDate && $endDate) {
                $sql .= " AND created_at BETWEEN :start_date AND :end_date";
                $bindings[':start_date'] = $startDate . ' 00:00:00';
                $bindings[':end_date'] = $endDate . ' 23:59:59';
                error_log("Filtering by date range: {$startDate} to {$endDate}");
            }
            
            // تسجيل الاستعلام للتصحيح
            error_log("getCustomerTotalSpending - SQL: $sql");
            error_log("getCustomerTotalSpending - Bindings: " . json_encode($bindings));
            
            $this->db->prepare($sql);
            foreach ($bindings as $key => $value) {
                $this->db->bind($key, $value);
            }
            
            $result = $this->db->fetch();
            $totalSpending = $result ? floatval($result['total_spending']) : 0;
            
            error_log("getCustomerTotalSpending - Result: " . ($result ? json_encode($result) : 'null'));
            error_log("getCustomerTotalSpending - Total Spending: $totalSpending");
            
            return $totalSpending;
        } catch (Exception $e) {
            error_log('خطأ أثناء استرجاع إجمالي مشتريات العميل: ' . $e->getMessage());
            error_log('Stack trace: ' . $e->getTraceAsString());
            return 0;
        }
    }
    
    /**
     * الحصول على ملخص مشتريات العميل حسب الفئة خلال فترة محددة
     * @param int $customerId معرف العميل
     * @param string $startDate تاريخ البداية (اختياري)
     * @param string $endDate تاريخ النهاية (اختياري)
     * @return array مصفوفة تحتوي على المشتريات مقسمة حسب الفئة
     */
    public function getCustomerSpendingByCategory($customerId, $startDate = null, $endDate = null) {
        try {
            // تسجيل الاستدعاء للتصحيح
            error_log("getCustomerSpendingByCategory - Customer ID: $customerId, Start Date: $startDate, End Date: $endDate");
            
            $sql = "SELECT 
                    CASE 
                        WHEN ii.item_type = 'service' THEN COALESCE(sc.name, 'خدمات أخرى')
                        WHEN ii.item_type = 'product' THEN COALESCE(pc.name, 'منتجات أخرى')
                        ELSE ii.item_type
                    END as category_name,
                    SUM(ii.total) as total_amount
                FROM invoices i
                JOIN invoice_items ii ON i.id = ii.invoice_id
                LEFT JOIN services s ON ii.item_type = 'service' AND ii.item_id = s.id
                LEFT JOIN products p ON ii.item_type = 'product' AND ii.item_id = p.id
                LEFT JOIN service_categories sc ON s.category_id = sc.id
                LEFT JOIN product_categories pc ON p.category_id = pc.id
                WHERE i.customer_id = :customer_id";
            
            $bindings = [':customer_id' => $customerId];
            
            // إضافة فلتر التاريخ إذا تم تحديده
            if ($startDate && $endDate) {
                $sql .= " AND i.created_at BETWEEN :start_date AND :end_date";
                $bindings[':start_date'] = $startDate . ' 00:00:00';
                $bindings[':end_date'] = $endDate . ' 23:59:59';
                error_log("Filtering by date range: {$startDate} to {$endDate}");
            }
            
            $sql .= " GROUP BY category_name HAVING total_amount > 0 ORDER BY total_amount DESC";
            
            // تسجيل الاستعلام للتصحيح
            error_log("getCustomerSpendingByCategory - SQL: $sql");
            error_log("getCustomerSpendingByCategory - Bindings: " . json_encode($bindings));
            
            $this->db->prepare($sql);
            foreach ($bindings as $key => $value) {
                $this->db->bind($key, $value);
            }
            
            $results = $this->db->fetchAll();
            error_log("getCustomerSpendingByCategory - Results count: " . count($results));
            if (count($results) > 0) {
                error_log("getCustomerSpendingByCategory - First result: " . json_encode($results[0]));
            }
            
            return $results;
        } catch (Exception $e) {
            error_log('خطأ أثناء استرجاع مشتريات العميل حسب الفئة: ' . $e->getMessage());
            error_log('Stack trace: ' . $e->getTraceAsString());
            return [];
        }
    }
    
    /**
     * الحصول على ملخص مشتريات العميل شهريًا
     * @param int $customerId معرف العميل
     * @param int $limit عدد الأشهر المطلوبة (الافتراضي: 12)
     * @return array مصفوفة تحتوي على المشتريات الشهرية
     */
    public function getCustomerMonthlySpending($customerId, $limit = 12) {
        try {
            $sql = "SELECT 
                    DATE_FORMAT(created_at, '%Y-%m') as month,
                    DATE_FORMAT(created_at, '%M %Y') as month_label,
                    SUM(final_amount) as total_amount
                FROM invoices
                WHERE customer_id = :customer_id
                AND status != 'cancelled'
                GROUP BY month
                ORDER BY month DESC
                LIMIT :limit";
            
            $this->db->prepare($sql);
            $this->db->bind(':customer_id', $customerId);
            $this->db->bind(':limit', $limit, PDO::PARAM_INT);
            
            $results = $this->db->fetchAll();
            
            // ترتيب النتائج بشكل تصاعدي للعرض في الرسم البياني
            $results = array_reverse($results);
            
            return $results;
        } catch (Exception $e) {
            error_log('خطأ أثناء استرجاع المشتريات الشهرية للعميل: ' . $e->getMessage());
            return [];
        }
    }
    
    /**
     * الحصول على فواتير العميل
     * @param int $customerId معرف العميل
     * @param string $startDate تاريخ البداية (اختياري)
     * @param string $endDate تاريخ النهاية (اختياري)
     * @param int $limit عدد الفواتير المطلوبة (الافتراضي: 50)
     * @return array مصفوفة تحتوي على فواتير العميل
     */
    public function getCustomerInvoices($customerId, $startDate = null, $endDate = null, $limit = 50) {
        try {
            // تسجيل الاستدعاء للتصحيح
            error_log("getCustomerInvoices - Customer ID: $customerId, Start Date: $startDate, End Date: $endDate, Limit: $limit");
            
            // تحقق من وجود جدول الفواتير وهيكله
            try {
                $this->db->prepare("DESCRIBE invoices");
                $this->db->execute();
                $columns = $this->db->fetchAll();
                $columnNames = array_column($columns, 'Field');
                error_log("Invoice table columns: " . implode(", ", $columnNames));
            } catch (Exception $e) {
                error_log("Error describing invoices table: " . $e->getMessage());
            }
            
            $sql = "SELECT i.*, 
                   e.name as employee_name, 
                   b.name as branch_name
                  FROM invoices i
                  LEFT JOIN employees e ON i.employee_id = e.id
                  LEFT JOIN branches b ON i.branch_id = b.id
                  WHERE i.customer_id = :customer_id";
            
            $bindings = [':customer_id' => $customerId];
            
            // إضافة فلتر التاريخ إذا تم تحديده
            if ($startDate && $endDate) {
                $sql .= " AND i.created_at BETWEEN :start_date AND :end_date";
                $bindings[':start_date'] = $startDate . ' 00:00:00';
                $bindings[':end_date'] = $endDate . ' 23:59:59';
            }
            
            $sql .= " ORDER BY i.created_at DESC LIMIT :limit";
            $bindings[':limit'] = $limit;
            
            // تسجيل الاستعلام للتصحيح
            error_log("getCustomerInvoices - SQL: $sql");
            error_log("getCustomerInvoices - Bindings: " . json_encode($bindings));
            
            $this->db->prepare($sql);
            foreach ($bindings as $key => $value) {
                if ($key === ':limit') {
                    $this->db->bind($key, $value, PDO::PARAM_INT);
                } else {
                    $this->db->bind($key, $value);
                }
            }
            
            $results = $this->db->fetchAll();
            error_log("getCustomerInvoices - Results count: " . count($results));
            
            return $results;
        } catch (Exception $e) {
            error_log('خطأ أثناء استرجاع فواتير العميل: ' . $e->getMessage());
            error_log('Stack trace: ' . $e->getTraceAsString());
            return [];
        }
    }

    /**
     * استرجاع إجمالي الخصومات للعميل ضمن فترة محددة
     *
     * @param int $customerId
     * @param string $startDate
     * @param string $endDate
     * @return float
     */
    public function getCustomerTotalDiscounts($customerId, $startDate = null, $endDate = null) {
        try {
            error_log("getCustomerTotalDiscounts - Start: Customer ID: $customerId, Start Date: $startDate, End Date: $endDate");
            
            // جمع الخصومات من عناصر الفواتير
            $sql = "SELECT SUM(ii.discount) AS item_discounts 
                    FROM invoice_items ii 
                    JOIN invoices i ON ii.invoice_id = i.id 
                    WHERE i.customer_id = :customer_id";
            
            // إضافة خصم الفاتورة نفسها
            $sql2 = "SELECT SUM(discount_amount) AS invoice_discounts 
                    FROM invoices 
                    WHERE customer_id = :customer_id";
            
            $bindings = [':customer_id' => $customerId];
            $bindings2 = [':customer_id' => $customerId];
            
            // إضافة فلتر التاريخ إذا تم تحديده
            if ($startDate && $endDate) {
                $sql .= " AND i.created_at BETWEEN :start_date AND :end_date";
                $sql2 .= " AND created_at BETWEEN :start_date AND :end_date";
                
                $bindings[':start_date'] = $startDate . ' 00:00:00';
                $bindings[':end_date'] = $endDate . ' 23:59:59';
                
                $bindings2[':start_date'] = $startDate . ' 00:00:00';
                $bindings2[':end_date'] = $endDate . ' 23:59:59';
                
                error_log("Filtering discounts by date range: {$startDate} to {$endDate}");
            }
            
            error_log("getCustomerTotalDiscounts - SQL Item Discounts: $sql");
            error_log("getCustomerTotalDiscounts - SQL Invoice Discounts: $sql2");
            
            // استرجاع الخصومات على مستوى المنتجات/الخدمات
            $this->db->prepare($sql);
            foreach ($bindings as $key => $value) {
                $this->db->bind($key, $value);
            }
            
            $itemDiscounts = 0;
            $result = $this->db->fetch();
            if ($result) {
                $itemDiscounts = (float)$result['item_discounts'];
                error_log("getCustomerTotalDiscounts - Item Discounts: $itemDiscounts");
            }
            
            // استرجاع الخصومات على مستوى الفاتورة
            $this->db->prepare($sql2);
            foreach ($bindings2 as $key => $value) {
                $this->db->bind($key, $value);
            }
            
            $invoiceDiscounts = 0;
            $result2 = $this->db->fetch();
            if ($result2) {
                $invoiceDiscounts = (float)$result2['invoice_discounts'];
                error_log("getCustomerTotalDiscounts - Invoice Discounts: $invoiceDiscounts");
            }
            
            // إجمالي الخصومات = خصومات المنتجات + خصومات الفاتورة
            $totalDiscounts = $itemDiscounts + $invoiceDiscounts;
            error_log("getCustomerTotalDiscounts - Total Discounts: $totalDiscounts");
            
            return $totalDiscounts;
        } catch (Exception $e) {
            error_log("getCustomerTotalDiscounts - Exception: " . $e->getMessage());
            error_log("getCustomerTotalDiscounts - Stack trace: " . $e->getTraceAsString());
            return 0;
        }
    }

}

