<?php
/**
 * صفحة إدارة رواتب الموظفين
 */

// تعريف ثابت لمنع الوصول المباشر للملفات المرتبطة
define('BASEPATH', true);

// استدعاء ملفات التهيئة
require_once '../../config/init.php';

// التحقق من تسجيل الدخول
requireLogin();

// التحقق من وجود صلاحية عرض الرواتب
requirePermission('employees_salaries');

// عنوان الصفحة
$pageTitle = 'إدارة رواتب الموظفين';

// استدعاء رأس الصفحة
include '../../includes/templates/header.php';

// إنشاء كائن الموظفين
$employeeModel = new Employee($db);

// الحصول على رمز العملة واسمها من الإعدادات
$settingsModel = new Settings($db);
$currencySymbol = $settingsModel->get('system_currency_symbol', 'ر.س');
$currencyName = $settingsModel->get('system_currency', 'ريال سعودي');

// فلتر الفرع الحالي
$branchId = isset($_GET['branch_id']) && $_GET['branch_id'] !== '' ? $_GET['branch_id'] : null;

// الحصول على الشهر والسنة الحالية
$currentMonth = date('n');
$currentYear = date('Y');

// التحقق من وجود فلترة للشهر والسنة
$month = isset($_GET['month']) ? $_GET['month'] : 'all';
$year = isset($_GET['year']) ? intval($_GET['year']) : $currentYear;

// تحويل الشهر إلى رقم إذا لم يكن "all"
if ($month !== 'all') {
    $month = intval($month);
}

// التحقق من وجود موظف محدد
$selectedEmployeeId = isset($_GET['employee_id']) ? intval($_GET['employee_id']) : null;

// الحصول على قائمة الشهور بالعربية
$months = [
    '1' => 'يناير', '2' => 'فبراير', '3' => 'مارس',
    '4' => 'أبريل', '5' => 'مايو', '6' => 'يونيو',
    '7' => 'يوليو', '8' => 'أغسطس', '9' => 'سبتمبر',
    '10' => 'أكتوبر', '11' => 'نوفمبر', '12' => 'ديسمبر'
];

// الحصول على أنواع الرواتب
$salaryTypes = [
    'fixed' => 'ثابت',
    'percentage' => 'نسبة',
    'both' => 'ثابت ونسبة'
];

// أنواع حالات الدفع
$paymentStatuses = [
    'paid' => 'مدفوع',
    'unpaid' => 'غير مدفوع'
];
?>

<!-- المحتوى الرئيسي للصفحة -->
<div class="container-fluid">
    <!-- رأس الصفحة -->
    <div class="card border-0 shadow-sm mb-4">
        <div class="card-body d-flex justify-content-between align-items-center">
            <h5 class="mb-0">
                <i class="fas fa-money-bill-wave me-2"></i> رواتب الموظفين
            </h5>
            <div>
                <a href="index.php" class="btn btn-outline-primary">
                    <i class="fas fa-arrow-right me-1"></i> العودة لقائمة الموظفين
                </a>
            </div>
        </div>
    </div>

    <!-- بطاقة الفلترة والبحث -->
    <div class="card border-0 shadow-sm mb-4">
        <div class="card-body">
            <form id="filterForm" class="row align-items-end">
                <div class="col-md-2 mb-3">
                    <label for="select_month" class="form-label">الشهر</label>
                    <select id="select_month" name="month" class="form-select">
                        <option value="all" <?php echo ($month == 'all' || !isset($_GET['month'])) ? 'selected' : ''; ?>>جميع الشهور</option>
                        <?php foreach ($months as $num => $name): ?>
                            <option value="<?php echo $num; ?>" <?php echo ($month == $num) ? 'selected' : ''; ?>>
                                <?php echo $name; ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>

                <div class="col-md-2 mb-3">
                    <label for="select_year" class="form-label">السنة</label>
                    <select id="select_year" name="year" class="form-select">
                        <?php for ($i = date('Y') - 3; $i <= date('Y') + 1; $i++): ?>
                            <option value="<?php echo $i; ?>" <?php echo ($year == $i) ? 'selected' : ''; ?>>
                                <?php echo $i; ?>
                            </option>
                        <?php endfor; ?>
                    </select>
                </div>

                <div class="col-md-3 mb-3">
                    <label for="employee_filter" class="form-label">الموظف</label>
                    <select id="employee_filter" name="employee_id" class="form-select select2">
                        <option value="">جميع الموظفين</option>
                        <?php
                        // استرجاع قائمة الموظفين النشطين
                        $employees = $employeeModel->getEmployees(['is_active' => 1, 'branch_id' => $branchId]);
                        foreach ($employees as $employee):
                        ?>
                            <option value="<?php echo $employee['id']; ?>" <?php echo ($selectedEmployeeId == $employee['id']) ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($employee['name']); ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>

                <?php if (isAdmin()): ?>
                <div class="col-md-3 mb-3">
                    <label for="branch_filter" class="form-label">الفرع</label>
                    <select id="branch_filter" name="branch_id" class="form-select">
                        <option value="" <?php echo (!isset($_GET['branch_id']) || $_GET['branch_id'] === '') ? 'selected' : ''; ?>>جميع الفروع</option>
                        <?php
                        // استرجاع قائمة الفروع
                        $branchModel = new Branch($db);
                        $branches = $branchModel->getBranches();
                        foreach ($branches as $branch):
                        ?>
                            <option value="<?php echo $branch['id']; ?>" <?php echo (isset($_GET['branch_id']) && $_GET['branch_id'] == $branch['id']) ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($branch['name']); ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                <?php else: ?>
                <input type="hidden" name="branch_id" value="<?php echo $branchId; ?>">
                <?php endif; ?>

                <div class="col-md-2 mb-3">
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="fas fa-filter me-1"></i> تطبيق الفلتر
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- جدول الرواتب -->
    <div class="card border-0 shadow-sm">
        <div class="card-header bg-white py-3">
            <div class="d-flex justify-content-between align-items-center">
                <h6 class="mb-0 text-primary">
                    <i class="fas fa-list me-1"></i>
                    قائمة رواتب <?php echo $months[$month] ?? ''; ?> <?php echo $year; ?>
                </h6>

                <div class="d-flex gap-2">
                    <button class="btn btn-sm btn-outline-primary" id="calculateUnpaidSalaries">
                        <i class="fas fa-calculator me-1"></i> حساب رواتب غير مدفوعة
                    </button>
                    <button class="btn btn-sm btn-success" id="exportSalaries">
                        <i class="fas fa-file-excel me-1"></i> تصدير لإكسل
                    </button>
                </div>
            </div>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table id="salariesTable" class="table table-hover">
                    <thead class="table-light">
                        <tr>
                            <th>#</th>
                            <th>اي دي الموظف</th>
                            <th>الموظف</th>
                            <th>الوظيفة</th>
                            <th>الشهر</th>
                            <th>السنة</th>
                            <th>نوع الراتب</th>
                            <th>المبلغ الثابت</th>
                            <th>العمولة</th>
                            <th>مكافآت</th>
                            <th>خصومات</th>
                            <th>الإجمالي</th>
                            <th>حالة الدفع</th>
                            <th>تاريخ الدفع</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <!-- سيتم تعبئة البيانات عبر AJAX -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
<!-- مودال تعديل الراتب -->
<div class="modal fade" id="updateSalaryModal" tabindex="-1" aria-labelledby="updateSalaryModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="updateSalaryModalLabel">تعديل بيانات الراتب</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="إغلاق"></button>
            </div>
            <div class="modal-body">
                <form id="updateSalaryForm">
                    <input type="hidden" id="update_salary_id" name="salary_id">
                    <input type="hidden" id="update_employee_id" name="employee_id">

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="update_fixed_amount" class="form-label">الراتب الثابت</label>
                            <div class="input-group">
                                <input type="number" class="form-control" id="update_fixed_amount" name="fixed_amount" step="0.01" min="0" required>
                                <span class="input-group-text"><?php echo $currencySymbol; ?></span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <label for="update_commission_amount" class="form-label">العمولة</label>
                            <div class="input-group">
                                <input type="number" class="form-control" id="update_commission_amount" name="commission_amount" step="0.01" min="0" required>
                                <span class="input-group-text"><?php echo $currencySymbol; ?></span>
                            </div>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="update_bonuses" class="form-label">المكافآت</label>
                            <div class="input-group">
                                <input type="number" class="form-control" id="update_bonuses" name="bonuses" step="0.01" min="0" required>
                                <span class="input-group-text"><?php echo $currencySymbol; ?></span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <label for="update_deductions" class="form-label">الخصومات</label>
                            <div class="input-group">
                                <input type="number" class="form-control" id="update_deductions" name="deductions" step="0.01" min="0" required>
                                <span class="input-group-text"><?php echo $currencySymbol; ?></span>
                            </div>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="update_total_amount" class="form-label">إجمالي الراتب</label>
                            <div class="input-group">
                                <input type="number" class="form-control" id="update_total_amount" name="total_amount" step="0.01" min="0" readonly>
                                <span class="input-group-text"><?php echo $currencySymbol; ?></span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <label for="update_payment_status" class="form-label">حالة الدفع</label>
                            <select class="form-select" id="update_payment_status" name="payment_status" required>
                                <option value="unpaid">غير مدفوع</option>
                                <option value="paid">مدفوع</option>
                            </select>
                        </div>
                    </div>

                    <div class="row mb-3" id="update_payment_date_container">
                        <div class="col-md-6">
                            <label for="update_payment_date" class="form-label">تاريخ الدفع</label>
                            <input type="text" class="form-control date-picker" id="update_payment_date" name="payment_date">
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="update_notes" class="form-label">ملاحظات</label>
                        <textarea class="form-control" id="update_notes" name="notes" rows="3"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                <button type="button" class="btn btn-primary" id="submitUpdateSalary">حفظ التعديلات</button>
            </div>
        </div>
    </div>
</div>
<!-- مودال حساب الراتب -->
<div class="modal fade" id="calculateSalaryModal" tabindex="-1" aria-labelledby="calculateSalaryModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="calculateSalaryModalLabel">حساب راتب موظف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="إغلاق"></button>
            </div>
            <div class="modal-body">
                <div class="employee-info mb-4">
                    <h6 class="mb-3">معلومات الموظف</h6>
                    <div class="row">
                        <div class="col-md-6 mb-2">
                            <strong>الاسم:</strong> <span id="calc_employee_name"></span>
                        </div>
                        <div class="col-md-6 mb-2">
                            <strong>الوظيفة:</strong> <span id="calc_employee_position"></span>
                        </div>
                        <div class="col-md-6 mb-2">
                            <strong>نوع الراتب:</strong> <span id="calc_salary_type"></span>
                        </div>
                        <div class="col-md-6 mb-2">
                            <strong>الفترة:</strong> <span id="calc_salary_period"></span>
                        </div>
                    </div>
                </div>

                <h6 class="mb-3">تفاصيل الراتب</h6>
                <form id="calculateSalaryForm">
                    <input type="hidden" id="calc_employee_id" name="employee_id">
                    <input type="hidden" id="calc_month" name="month">
                    <input type="hidden" id="calc_year" name="year">

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="fixed_amount" class="form-label">المبلغ الثابت</label>
                            <div class="input-group">
                                <input type="number" class="form-control" id="fixed_amount" name="fixed_amount" step="0.01" min="0" readonly>
                                <span class="input-group-text"><?php echo $currencySymbol; ?></span>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="commission_amount" class="form-label">مبلغ العمولة</label>
                            <div class="input-group">
                                <input type="number" class="form-control" id="commission_amount" name="commission_amount" step="0.01" min="0" readonly>
                                <span class="input-group-text"><?php echo $currencySymbol; ?></span>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="bonuses" class="form-label">المكافآت</label>
                            <div class="input-group">
                                <input type="number" class="form-control" id="bonuses" name="bonuses" step="0.01" min="0" value="0">
                                <span class="input-group-text"><?php echo $currencySymbol; ?></span>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="deductions" class="form-label">الخصومات</label>
                            <div class="input-group">
                                <input type="number" class="form-control" id="deductions" name="deductions" step="0.01" min="0" value="0">
                                <span class="input-group-text"><?php echo $currencySymbol; ?></span>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="total_amount" class="form-label">إجمالي الراتب</label>
                            <div class="input-group">
                                <input type="number" class="form-control" id="total_amount" name="total_amount" step="0.01" min="0" readonly>
                                <span class="input-group-text"><?php echo $currencySymbol; ?></span>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="payment_status" class="form-label">حالة الدفع</label>
                            <select class="form-select" id="payment_status" name="payment_status">
                                <?php foreach ($paymentStatuses as $value => $label): ?>
                                    <option value="<?php echo $value; ?>"><?php echo $label; ?></option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    </div>

                    <div class="row payment-date-row" style="display: none;">
                        <div class="col-md-6 mb-3">
                            <label for="payment_date" class="form-label">تاريخ الدفع</label>
                            <input type="text" class="form-control date-picker" id="payment_date" name="payment_date" value="<?php echo date('Y/m/d'); ?>">
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-12 mb-3">
                            <label for="notes" class="form-label">ملاحظات</label>
                            <textarea class="form-control" id="notes" name="notes" rows="3"></textarea>
                        </div>
                    </div>
                </form>

                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-1"></i>
                    <small>يتم حساب مبلغ العمولة بناءً على نسبة العمولة المحددة للموظف من إجمالي المبيعات خلال الشهر.</small>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" id="saveSalaryBtn">حفظ الراتب</button>
            </div>
        </div>
    </div>
</div>

<!-- مودال عرض تفاصيل الراتب -->
<div class="modal fade" id="viewSalaryModal" tabindex="-1" aria-labelledby="viewSalaryModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="viewSalaryModalLabel">تفاصيل راتب موظف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="إغلاق"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-6 mb-2">
                        <strong>الاسم:</strong> <span id="view_employee_name"></span>
                    </div>
                    <div class="col-md-6 mb-2">
                        <strong>الوظيفة:</strong> <span id="view_employee_position"></span>
                    </div>
                    <div class="col-md-6 mb-2">
                        <strong>الفترة:</strong> <span id="view_salary_period"></span>
                    </div>
                    <div class="col-md-6 mb-2">
                        <strong>نوع الراتب:</strong> <span id="view_salary_type"></span>
                    </div>
                </div>

                <hr>

                <div class="row">
                    <div class="col-md-6 mb-2">
                        <strong>المبلغ الثابت:</strong> <span id="view_fixed_amount"></span>
                    </div>
                    <div class="col-md-6 mb-2">
                        <strong>مبلغ العمولة:</strong> <span id="view_commission_amount"></span>
                    </div>
                    <div class="col-md-6 mb-2">
                        <strong>المكافآت:</strong> <span id="view_bonuses"></span>
                    </div>
                    <div class="col-md-6 mb-2">
                        <strong>الخصومات:</strong> <span id="view_deductions"></span>
                    </div>
                    <div class="col-md-6 mb-2">
                        <strong>إجمالي الراتب:</strong> <span id="view_total_amount" class="fw-bold text-success"></span>
                    </div>
                    <div class="col-md-6 mb-2">
                        <strong>حالة الدفع:</strong> <span id="view_payment_status"></span>
                    </div>
                </div>

                <div id="view_payment_details" class="mb-2">
                    <strong>تاريخ الدفع:</strong> <span id="view_payment_date"></span>
                </div>

                <div class="mb-2">
                    <strong>ملاحظات:</strong>
                    <p id="view_notes" class="mb-0"></p>
                </div>

                <hr>

                <h6 class="mb-3">تفاصيل مبيعات الموظف</h6>
                <div class="table-responsive">
                    <table class="table table-sm table-striped">
                        <thead>
                            <tr>
                                <th>تاريخ الفاتورة</th>
                                <th>رقم الفاتورة</th>
                                <th>المبلغ</th>
                                <th>العمولة</th>
                            </tr>
                        </thead>
                        <tbody id="view_sales_details">
                            <!-- سيتم تعبئة البيانات عبر JavaScript -->
                        </tbody>
                        <tfoot>
                            <tr class="table-primary">
                                <th colspan="2">الإجمالي</th>
                                <th id="view_total_sales"></th>
                                <th id="view_total_commission"></th>
                            </tr>
                        </tfoot>
                    </table>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                <button type="button" class="btn btn-primary" id="updateSalaryBtn">تعديل الراتب</button>
                <button type="button" class="btn btn-success" id="printSalaryBtn">
                    <i class="fas fa-print me-1"></i> طباعة
                </button>
            </div>
        </div>
    </div>
</div>

<!-- مودال تحديث حالة الدفع -->
<div class="modal fade" id="updateStatusModal" tabindex="-1" aria-labelledby="updateStatusModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="updateStatusModalLabel">تحديث حالة دفع الراتب</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="إغلاق"></button>
            </div>
            <div class="modal-body">
                <form id="updateStatusForm">
                    <input type="hidden" id="update_salary_id" name="salary_id">

                    <div class="mb-3">
                        <label for="update_payment_status" class="form-label">حالة الدفع</label>
                        <select class="form-select" id="update_payment_status" name="status">
                            <?php foreach ($paymentStatuses as $value => $label): ?>
                                <option value="<?php echo $value; ?>"><?php echo $label; ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>

                    <div class="mb-3 payment-date-row" style="display: none;">
                        <label for="update_payment_date" class="form-label">تاريخ الدفع</label>
                        <input type="text" class="form-control date-picker" id="update_payment_date" name="payment_date" value="<?php echo date('Y/m/d'); ?>">
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" id="submitUpdateStatus">حفظ التغييرات</button>
            </div>
        </div>
    </div>
</div>

<!-- استدعاء قالب الفوتر -->
<?php include '../../includes/templates/footer.php'; ?>

<script>
    // تعريف متغيرات عالمية للعملة
    var currencySymbol = '<?php echo $currencySymbol; ?>';
    var currencyName = '<?php echo $currencyName; ?>';

    // دالة تنسيق العملة
    function formatCurrency(amount) {
        return parseFloat(amount).toFixed(2) + ' ' + currencySymbol;
    }

    $(document).ready(function() {

        // تهيئة جدول الرواتب
        const salariesTable = $('#salariesTable').DataTable({
        processing: true,
        serverSide: false,
        ajax: {
            url: '../../api/employees.php',
            type: 'POST',
            data: function(d) {
                d.action = 'get_employees_salaries';
                d.month = $('#select_month').val();
                d.year = $('#select_year').val();
                d.branch_id = $('#branch_filter').val();
                d.employee_id = $('#employee_filter').val() || null;
            },
            dataSrc: function(response) {
                if (response && response.status === 'success' && response.salaries) {
                    return response.salaries;
                }
                return [];
            }
        },
        columns: [
            { data: 'id' },
            { data: 'employee_id' },
            { data: 'employee_name' },
            { data: 'position' },
            {
                data: 'month',
                render: function(data) {
                    const months = {
                        '1': 'يناير', '2': 'فبراير', '3': 'مارس',
                        '4': 'أبريل', '5': 'مايو', '6': 'يونيو',
                        '7': 'يوليو', '8': 'أغسطس', '9': 'سبتمبر',
                        '10': 'أكتوبر', '11': 'نوفمبر', '12': 'ديسمبر'
                    };
                    return months[data] || data;
                }
            },
            { data: 'year' },
            {
                data: 'salary_type',
                render: function(data) {
                    switch(data) {
                        case 'fixed': return 'ثابت';
                        case 'percentage': return 'نسبة';
                        case 'both': return 'ثابت ونسبة';
                        default: return 'غير محدد';
                    }
                }
            },
            {
                data: 'fixed_amount',
                render: function(data) {
                    return formatCurrency(data);
                }
            },
            {
                data: 'commission_amount',
                render: function(data) {
                    return formatCurrency(data);
                }
            },
            {
                data: 'bonuses',
                render: function(data) {
                    return formatCurrency(data);
                }
            },
            {
                data: 'deductions',
                render: function(data) {
                    return formatCurrency(data);
                }
            },
            {
                data: 'total_amount',
                render: function(data) {
                    return '<span class="fw-bold text-success">' + formatCurrency(data) + '</span>';
                }
            },
            {
                data: 'payment_status',
                render: function(data) {
                    if (data === 'paid') {
                        return '<span class="badge bg-success">مدفوع</span>';
                    } else {
                        return '<span class="badge bg-warning text-dark">غير مدفوع</span>';
                    }
                }
            },
            {
                data: 'payment_date',
                render: function(data) {
                    if (data) {
                        return moment(data).format('YYYY-MM-DD');
                    }
                    return '-';
                }
            },
            {
                data: null,
                orderable: false,
                render: function(data) {
                    let actions = `
                        <div class="dropdown">
                            <button class="btn btn-sm btn-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                                الإجراءات
                            </button>
                            <ul class="dropdown-menu dropdown-menu-end">
                                <li><a class="dropdown-item view-salary" href="javascript:void(0)" data-month="${data.month}" data-employee_id="${data.employee_id}" data-id="${data.id}">
                                    <i class="fas fa-eye me-2"></i> عرض التفاصيل
                                </a>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item print-salary" href="javascript:void(0)" data-id="${data.id}">
                                    <i class="fas fa-print me-2"></i> طباعة
                                </a></li>
                            </ul>
                        </div>`;

                    return actions;
                }
            }
        ],
        language: {
            url: 'https://cdn.datatables.net/plug-ins/1.11.5/i18n/ar.json'
        }
    });
        // تطبيق الفلتر
        $('#filterForm').on('submit', function(e) {
            e.preventDefault();

            // تحديث الرابط مع معلمات الفلتر
            const month = $('#select_month').val();
            const year = $('#select_year').val();
            const employeeId = $('#employee_filter').val();
            const branchId = $('#branch_filter').val();

            let url = window.location.pathname + '?month=' + month + '&year=' + year;

            if (employeeId) {
                url += '&employee_id=' + employeeId;
            }

            if (branchId) {
                url += '&branch_id=' + branchId;
            }

            window.history.pushState({}, '', url);

            // إعادة تحميل الجدول
            salariesTable.ajax.reload();
        });

        // حساب رواتب غير مدفوعة
        $('#calculateUnpaidSalaries').on('click', function() {
            confirmAction('هل أنت متأكد من حساب رواتب الموظفين غير المدفوعة لهذا الشهر؟', function() {
                const month = $('#select_month').val();
                const year = $('#select_year').val();
                const branchId = $('#branch_filter').val();

                // التحقق من اختيار شهر محدد
                if (month === 'all') {
                    showAlert('يرجى اختيار شهر محدد لحساب الرواتب', 'warning');
                    return;
                }

                // استرجاع قائمة الموظفين وحساب رواتبهم
                $.ajax({
                    url: '../../api/employees.php',
                    type: 'POST',
                    data: {
                        action: 'get_employees',
                        is_active: 1,
                        branch_id: branchId
                    },
                    dataType: 'json',
                    success: function(response) {
                        if (response.status === 'success' && response.employees) {
                            const employees = response.employees;
                            let processedCount = 0;
                            let successCount = 0;
                            let updatedCount = 0;

                            // عرض مؤشر تقدم العملية
                            showLoadingAlert('جاري حساب الرواتب... (0/' + employees.length + ')');

                            // معالجة كل موظف على حدة بشكل متسلسل
                            function processNextEmployee(index) {
                                if (index >= employees.length) {
                                    // انتهت المعالجة
                                    showAlert('تم حساب ' + successCount + ' راتب جديد وتحديث ' + updatedCount + ' راتب من إجمالي ' + employees.length + ' موظف', 'success');
                                    salariesTable.ajax.reload();

                                    // عرض نافذة الإجمالي العام للرواتب
                                    showSalariesSummary(month, year, branchId);
                                    return;
                                }

                                const employee = employees[index];

                                // التحقق من وجود راتب محسوب مسبقاً لهذا الشهر
                                $.ajax({
                                    url: '../../api/employees.php',
                                    type: 'POST',
                                    data: {
                                        action: 'get_employee_salary',
                                        employee_id: employee.id,
                                        month: month,
                                        year: year
                                    },
                                    dataType: 'json',
                                    success: function(existingSalaryResponse) {
                                        // حساب راتب الموظف
                                        $.ajax({
                                            url: '../../api/employees.php',
                                            type: 'POST',
                                            data: {
                                                action: 'calculate_salary',
                                                employee_id: employee.id,
                                                month: month,
                                                year: year
                                            },
                                            dataType: 'json',
                                            success: function(salaryResponse) {
                                                if (salaryResponse.status === 'success' && salaryResponse.salary) {
                                                    const salary = salaryResponse.salary;
                                                    const existingSalary = existingSalaryResponse.status === 'success' ? existingSalaryResponse.salary : null;

                                                    if (existingSalary && existingSalary.id) {
                                                        // إذا كان الراتب مدفوعاً بالفعل، نتخطى هذا الموظف
                                                        if (existingSalary.payment_status === 'paid') {
                                                            processedCount++;
                                                            updateLoadingAlert('جاري حساب الرواتب... (' + processedCount + '/' + employees.length + ')');
                                                            processNextEmployee(index + 1);
                                                            return;
                                                        }

                                                        // تحديث الراتب الموجود
                                                        $.ajax({
                                                            url: '../../api/employees.php',
                                                            type: 'POST',
                                                            data: {
                                                                action: 'update_salary',
                                                                salary_id: existingSalary.id,
                                                                fixed_amount: salary.fixed_amount,
                                                                commission_amount: salary.commission_amount,
                                                                bonuses: existingSalary.bonuses || 0,
                                                                deductions: existingSalary.deductions || 0,
                                                                total_amount: parseFloat(salary.fixed_amount) + parseFloat(salary.commission_amount) + parseFloat(existingSalary.bonuses || 0) - parseFloat(existingSalary.deductions || 0),
                                                                payment_status: 'unpaid',
                                                                notes: existingSalary.notes || ''
                                                            },
                                                            dataType: 'json',
                                                            success: function() {
                                                                updatedCount++;
                                                                processedCount++;
                                                                updateLoadingAlert('جاري حساب الرواتب... (' + processedCount + '/' + employees.length + ')');
                                                                processNextEmployee(index + 1);
                                                            },
                                                            error: function() {
                                                                processedCount++;
                                                                updateLoadingAlert('جاري حساب الرواتب... (' + processedCount + '/' + employees.length + ')');
                                                                processNextEmployee(index + 1);
                                                            }
                                                        });
                                                    } else {
                                                        // إنشاء راتب جديد
                                                        $.ajax({
                                                            url: '../../api/employees.php',
                                                            type: 'POST',
                                                            data: {
                                                                action: 'save_salary',
                                                                employee_id: employee.id,
                                                                month: month,
                                                                year: year,
                                                                fixed_amount: salary.fixed_amount,
                                                                commission_amount: salary.commission_amount,
                                                                bonuses: 0,
                                                                deductions: 0,
                                                                total_amount: parseFloat(salary.fixed_amount) + parseFloat(salary.commission_amount),
                                                                payment_status: 'unpaid'
                                                            },
                                                            dataType: 'json',
                                                            success: function() {
                                                                successCount++;
                                                                processedCount++;
                                                                updateLoadingAlert('جاري حساب الرواتب... (' + processedCount + '/' + employees.length + ')');
                                                                processNextEmployee(index + 1);
                                                            },
                                                            error: function() {
                                                                processedCount++;
                                                                updateLoadingAlert('جاري حساب الرواتب... (' + processedCount + '/' + employees.length + ')');
                                                                processNextEmployee(index + 1);
                                                            }
                                                        });
                                                    }
                                                } else {
                                                    processedCount++;
                                                    updateLoadingAlert('جاري حساب الرواتب... (' + processedCount + '/' + employees.length + ')');
                                                    processNextEmployee(index + 1);
                                                }
                                            },
                                            error: function() {
                                                processedCount++;
                                                updateLoadingAlert('جاري حساب الرواتب... (' + processedCount + '/' + employees.length + ')');
                                                processNextEmployee(index + 1);
                                            }
                                        });
                                    },
                                    error: function() {
                                        processedCount++;
                                        updateLoadingAlert('جاري حساب الرواتب... (' + processedCount + '/' + employees.length + ')');
                                        processNextEmployee(index + 1);
                                    }
                                });
                            }

                            // بدء المعالجة من الموظف الأول
                            processNextEmployee(0);
                        } else {
                            showAlert('حدث خطأ أثناء جلب قائمة الموظفين', 'danger');
                        }
                    },
                    error: function() {
                        showAlert('حدث خطأ أثناء جلب قائمة الموظفين', 'danger');
                    }
                });
            });
        });
        // إعادة حساب الإجمالي عند تغيير المكافآت أو الخصومات
        $('#bonuses, #deductions').on('change keyup', function() {
            const fixedAmount = parseFloat($('#fixed_amount').val()) || 0;
            const commissionAmount = parseFloat($('#commission_amount').val()) || 0;
            const bonuses = parseFloat($('#bonuses').val()) || 0;
            const deductions = parseFloat($('#deductions').val()) || 0;

            const totalAmount = fixedAmount + commissionAmount + bonuses - deductions;
            $('#total_amount').val(totalAmount.toFixed(2));
        });
            // تحديث إجمالي الراتب عند تغيير أي من القيم
        $('#update_fixed_amount, #update_commission_amount, #update_bonuses, #update_deductions').on('input', function() {
            const fixedAmount = parseFloat($('#update_fixed_amount').val()) || 0;
            const commissionAmount = parseFloat($('#update_commission_amount').val()) || 0;
            const bonuses = parseFloat($('#update_bonuses').val()) || 0;
            const deductions = parseFloat($('#update_deductions').val()) || 0;

            const totalAmount = fixedAmount + commissionAmount + bonuses - deductions;
            $('#update_total_amount').val(totalAmount.toFixed(2));
        });

        // عرض/إخفاء حقل تاريخ الدفع بناءً على حالة الدفع
        $('#payment_status, #update_payment_status').on('change', function() {
            const paymentStatus = $(this).val();
            const rowSelector = $(this).attr('id') === 'payment_status' ? '.payment-date-row' : '.payment-date-row';
            $('#update_payment_date').val(moment().format('YYYY-MM-DD'));
            if (paymentStatus === 'paid') {
                $(rowSelector).show();
                $('#update_payment_date_container').show();


            } else {
                $(rowSelector).hide();
                $('#update_payment_date_container').hide();
                $('#update_payment_date').val('');

            }
        });
        // حفظ تعديلات الراتب
        $('#submitUpdateSalary').on('click', function() {
            const form = $('#updateSalaryForm')[0];
            if (!form.checkValidity()) {
                form.reportValidity();
                return;
            }

            const formData = new FormData(form);
            formData.append('action', 'update_salary');

            $.ajax({
                url: '../../api/employees.php',
                type: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                dataType: 'json',
                success: function(response) {
                    if (response.status === 'success') {
                        $('#updateSalaryModal').modal('hide');
                        showAlert(response.message, 'success');
                        salariesTable.ajax.reload();
                    } else {
                        showAlert(response.message, 'danger');
                    }
                },
                error: function() {
                    showAlert('حدث خطأ أثناء تحديث بيانات الراتب', 'danger');
                }
            });
        });
        // حفظ الراتب
        $('#saveSalaryBtn').on('click', function() {
            const form = $('#calculateSalaryForm')[0];
            if (!form.checkValidity()) {
                form.reportValidity();
                return;
            }

            const formData = new FormData(form);
            formData.append('action', 'save_salary');

            $.ajax({
                url: '../../api/employees.php',
                type: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                dataType: 'json',
                success: function(response) {
                    if (response.status === 'success') {
                        $('#calculateSalaryModal').modal('hide');
                        showAlert(response.message, 'success');
                        salariesTable.ajax.reload();
                    } else {
                        showAlert(response.message, 'danger');
                    }
                },
                error: function() {
                    showAlert('حدث خطأ أثناء حفظ الراتب', 'danger');
                }
            });
        });

        // عرض تفاصيل الراتب
        $(document).on('click', '.view-salary', function() {
            const salaryId = $(this).data('id');

            // استرجاع بيانات الراتب
            $.ajax({
                url: '../../api/employees.php',
                type: 'POST',
                data: {
                    action: 'get_employee_salary',
                    employee_id: $(this).data('employee_id') || 0,
                    month: $(this).data('month') || $('#select_month').val(),
                    year: $('#select_year').val()
                },
                dataType: 'json',
                success: function(response) {
                    if (response.status === 'success' && response.salary) {
                        const salary = response.salary;

                        // عرض بيانات الموظف
                        $('#view_employee_name').text(salary.employee_name);
                        $('#view_employee_position').text(salary.position);

                        // عرض بيانات الراتب
                        let monthName = $('#select_month option:selected').text();
                        if (monthName === 'جميع الشهور') {
                            // استخدام اسم الشهر من بيانات الراتب
                            const months = {
                                '1': 'يناير', '2': 'فبراير', '3': 'مارس',
                                '4': 'أبريل', '5': 'مايو', '6': 'يونيو',
                                '7': 'يوليو', '8': 'أغسطس', '9': 'سبتمبر',
                                '10': 'أكتوبر', '11': 'نوفمبر', '12': 'ديسمبر'
                            };
                            monthName = months[salary.month] || salary.month;
                        }
                        $('#view_salary_period').text(monthName + ' ' + salary.year);

                        let salaryType = '';
                        switch(salary.salary_type) {
                            case 'fixed': salaryType = 'ثابت'; break;
                            case 'percentage': salaryType = 'نسبة'; break;
                            case 'both': salaryType = 'ثابت ونسبة'; break;
                            default: salaryType = salary.salary_type;
                        }
                        $('#view_salary_type').text(salaryType);

                        $('#view_fixed_amount').text(parseFloat(salary.fixed_amount).toFixed(2) + '<?php echo $currencySymbol; ?>');
                        $('#view_commission_amount').text(parseFloat(salary.commission_amount).toFixed(2) + '<?php echo $currencySymbol; ?>');
                        $('#view_bonuses').text(parseFloat(salary.bonuses).toFixed(2) + '<?php echo $currencySymbol; ?>');
                        $('#view_deductions').text(parseFloat(salary.deductions).toFixed(2) + '<?php echo $currencySymbol; ?>');
                        $('#view_total_amount').text(parseFloat(salary.total_amount).toFixed(2) + '<?php echo $currencySymbol; ?>');

                        // عرض حالة الدفع
                        if (salary.payment_status === 'paid') {
                            $('#view_payment_status').html('<span class="badge bg-success">مدفوع</span>');
                            $('#view_payment_details').show();
                            $('#view_payment_date').text(moment(salary.payment_date).format('YYYY-MM-DD'));
                        } else {
                            $('#view_payment_status').html('<span class="badge bg-warning text-dark">غير مدفوع</span>');
                            $('#view_payment_details').hide();
                        }

                        // عرض الملاحظات
                        $('#view_notes').text(salary.notes || 'لا توجد ملاحظات');

                        // عرض تفاصيل المبيعات
                        if (salary.invoices_details && salary.invoices_details.length > 0) {
                            let salesHtml = '';
                            let totalSales = 0;
                            let totalCommission = 0;

                            salary.invoices_details.forEach(sale => {
                                const saleAmount = parseFloat(sale.service_total);
                                const commission = parseFloat(sale.commission_amount);

                                totalSales += saleAmount;
                                totalCommission += commission;

                                salesHtml += `
                                    <tr>
                                        <td>${moment(sale.invoice_created_at).format('YYYY-MM-DD')}</td>
                                        <td><a href="${BASE_URL}pages/invoices/view.php?id=${sale.invoice_id}">${sale.invoice_number}</a></td>
                                        <td>${formatCurrency(saleAmount)}</td>
                                        <td>${formatCurrency(commission)}</td>
                                    </tr>
                                `;
                            });

                            $('#view_sales_details').html(salesHtml);
                            $('#view_total_sales').text(formatCurrency(totalSales));
                            $('#view_total_commission').text(formatCurrency(totalCommission));
                        } else {
                            $('#view_sales_details').html('<tr><td colspan="4" class="text-center">لا توجد مبيعات لهذا الشهر</td></tr>');
                            $('#view_total_sales').text(formatCurrency(0));
                            $('#view_total_commission').text(formatCurrency(0));
                        }

                        // تحديث زر التعديل
                        $('#updateSalaryBtn').data('id', salary.id);

                        // إظهار المودال
                        $('#viewSalaryModal').modal('show');
                    } else {
                        showAlert('حدث خطأ أثناء تحميل بيانات الراتب', 'danger');
                    }
                },
                error: function() {
                    showAlert('حدث خطأ أثناء تحميل بيانات الراتب', 'danger');
                }
            });
        });
        // فتح مودال تعديل الراتب
        $('#updateSalaryBtn').on('click', function() {
            const salaryId = $(this).data('id');
            $('#viewSalaryModal').modal('hide');

            // استرجاع بيانات الراتب للتعديل
            $.ajax({
                url: '../../api/employees.php',
                type: 'POST',
                data: {
                    action: 'get_salary_by_id',
                    salary_id: salaryId
                },
                dataType: 'json',
                success: function(response) {
                    if (response.status === 'success' && response.salary) {
                        const salary = response.salary;

                        // تعبئة نموذج التعديل
                        $('#update_salary_id').val(salary.id);
                        $('#update_employee_id').val(salary.employee_id);
                        $('#update_fixed_amount').val(parseFloat(salary.fixed_amount).toFixed(2));
                        $('#update_commission_amount').val(parseFloat(salary.commission_amount).toFixed(2));
                        $('#update_bonuses').val(parseFloat(salary.bonuses).toFixed(2));
                        $('#update_deductions').val(parseFloat(salary.deductions).toFixed(2));
                        $('#update_total_amount').val(parseFloat(salary.total_amount).toFixed(2));
                        $('#update_payment_status').val(salary.payment_status).trigger('change');

                        if (salary.payment_date) {
                            $('#update_payment_date').val(moment(salary.payment_date).format('YYYY-MM-DD'));
                        }

                        $('#update_notes').val(salary.notes);

                        // عرض المودال
                        $('#updateSalaryModal').modal('show');
                    } else {
                        showAlert('حدث خطأ أثناء تحميل بيانات الراتب للتعديل', 'danger');
                    }
                },
                error: function() {
                    showAlert('حدث خطأ أثناء تحميل بيانات الراتب للتعديل', 'danger');
                }
            });
        });

        // طباعة إيصال الراتب
        $(document).on('click', '.print-salary, #printSalaryBtn', function() {
            const salaryId = $(this).data('id');

            // يمكن تنفيذ طباعة الإيصال هنا
            // في هذا المثال سنفترض أنه يتم الطباعة من خلال نافذة جديدة

            // إنشاء محتوى الطباعة
            let printContent = `
                <div class="p-4" dir="rtl">
                    <div class="text-center mb-4">
                        <h3>إيصال صرف راتب</h3>
                        <p>شهر: ${$('#select_month option:selected').text()} ${$('#select_year').val()}</p>
                    </div>

                    <div class="row">
                        <div class="col-6 mb-3">
                            <strong>الموظف:</strong> ${$('#view_employee_name').text()}
                        </div>
                        <div class="col-6 mb-3">
                            <strong>الوظيفة:</strong> ${$('#view_employee_position').text()}
                        </div>
                    </div>

                    <table class="table table-bordered">
                        <tr>
                            <th>البند</th>
                            <th>المبلغ</th>
                        </tr>
                        <tr>
                            <td>الراتب الثابت</td>
                            <td>${$('#view_fixed_amount').text()}</td>
                        </tr>
                        <tr>
                            <td>العمولة</td>
                            <td>${$('#view_commission_amount').text()}</td>
                        </tr>
                        <tr>
                            <td>المكافآت</td>
                            <td>${$('#view_bonuses').text()}</td>
                        </tr>
                        <tr>
                            <td>الخصومات</td>
                            <td>${$('#view_deductions').text()}</td>
                        </tr>
                        <tr class="table-active">
                            <th>الإجمالي</th>
                            <th>${$('#view_total_amount').text()}</th>
                        </tr>
                    </table>

                    <div class="row mt-4">
                        <div class="col-6">
                            <strong>حالة الدفع:</strong> ${$('#view_payment_status').text()}
                        </div>
                        <div class="col-6">
                            <strong>تاريخ الدفع:</strong> ${$('#view_payment_date').text() || '-'}
                        </div>
                    </div>

                    <div class="row mt-5">
                        <div class="col-6 text-center">
                            <p class="mb-5">توقيع المدير</p>
                            <div style="border-top: 1px solid #000; width: 50%; margin: 0 auto;"></div>
                        </div>
                        <div class="col-6 text-center">
                            <p class="mb-5">توقيع المستلم</p>
                            <div style="border-top: 1px solid #000; width: 50%; margin: 0 auto;"></div>
                        </div>
                    </div>
                </div>
            `;

            // فتح نافذة الطباعة
            const printWindow = window.open('', '_blank');
            printWindow.document.write(`
                <!DOCTYPE html>
                <html>
                <head>
                    <title>إيصال راتب</title>
                    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.0.2/dist/css/bootstrap.rtl.min.css">
                    <style>
                        @media print {
                            body {
                                font-family: Arial, sans-serif;
                            }
                            .print-header {
                                text-align: center;
                                margin-bottom: 20px;
                            }
                            table {
                                width: 100%;
                                border-collapse: collapse;
                            }
                            table, th, td {
                                border: 1px solid #000;
                            }
                            th, td {
                                padding: 8px;
                                text-align: right;
                            }
                            .table-active {
                                background-color: #f2f2f2;
                            }
                        }
                    </style>
                </head>
                <body>
                    ${printContent}
                    <script>
                        window.onload = function() {
                            window.print();
                            setTimeout(function() {
                                window.close();
                            }, 1000);
                        }
                </body>
                </html>
            `);
        });

        // عرض مودال تحديث حالة الدفع
        $(document).on('click', '.update-status', function() {
            const salaryId = $(this).data('id');
            $('#update_salary_id').val(salaryId);

            // إعادة ضبط النموذج
            $('#update_payment_status').val('paid').trigger('change');
            $('#update_payment_date').val(moment().format('YYYY-MM-DD'));

            // عرض المودال
            $('#updateStatusModal').modal('show');
        });
        $('#update_payment_date').on('change', function() {
            // تحويل الأرقام العربية إلى أرقام إنجليزية في حقل التاريخ
            let dateValue = $(this).val();

            if (dateValue) {
                // تحويل الأرقام العربية إلى إنجليزية
                const arabicNumbers = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٢', '٩'];
                const englishNumbers = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'];

                for (let i = 0; i < arabicNumbers.length; i++) {
                    dateValue = dateValue.replace(new RegExp(arabicNumbers[i], 'g'), englishNumbers[i]);
                }

                // تأكد من أن التنسيق هو YYYY-MM-DD
                if (dateValue.includes('/')) {
                    const parts = dateValue.split('/');
                    if (parts.length === 3) {
                        // إذا كان التنسيق DD/MM/YYYY
                        if (parts[0].length === 2 && parts[2].length === 4) {
                            dateValue = `${parts[2]}-${parts[1]}-${parts[0]}`;
                        }
                        // إذا كان التنسيق YYYY/MM/DD
                        else if (parts[0].length === 4) {
                            dateValue = `${parts[0]}-${parts[1]}-${parts[2]}`;
                        }
                    }
                }

                $(this).val(dateValue);
            }
        });

        // حفظ تحديث حالة الدفع
        $('#submitUpdateStatus').on('click', function() {
            const form = $('#updateStatusForm')[0];
            if (!form.checkValidity()) {
                form.reportValidity();
                return;
            }

            const formData = new FormData(form);
            formData.append('action', 'update_salary_status');

            $.ajax({
                url: '../../api/employees.php',
                type: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                dataType: 'json',
                success: function(response) {
                    if (response.status === 'success') {
                        $('#updateStatusModal').modal('hide');
                        showAlert(response.message, 'success');
                        salariesTable.ajax.reload();
                    } else {
                        showAlert(response.message, 'danger');
                    }
                },
                error: function() {
                    showAlert('حدث خطأ أثناء تحديث حالة الدفع', 'danger');
                }
            });
        });

        // تهيئة Select2 للموظفين
        $('#employee_filter').select2({
            dir: "rtl",
            language: "ar",
            placeholder: "اختر الموظف",
            width: '100%'
        });

        // تهيئة عناصر التاريخ
        $('.date-picker').daterangepicker({
            singleDatePicker: true,
            showDropdowns: true,
            locale: {
                format: 'YYYY/MM/DD',
                applyLabel: 'تطبيق',
                cancelLabel: 'إلغاء',
                fromLabel: 'من',
                toLabel: 'إلى',
                customRangeLabel: 'مخصص',
                daysOfWeek: ['أح', 'إث', 'ثل', 'أر', 'خم', 'جم', 'سب'],
                monthNames: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو', 'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'],
                firstDay: 0
            }
        });
    });
    // إضافة دالة عرض تنبيه التحميل
    function showLoadingAlert(message) {
        // إزالة أي تنبيهات سابقة
        $('.alert-loading').remove();

        const alertHtml = `
            <div class="alert alert-info alert-loading alert-dismissible fade show" role="alert">
                <div class="d-flex align-items-center">
                    <div class="spinner-border spinner-border-sm me-2" role="status">
                        <span class="visually-hidden">جاري التحميل...</span>
                    </div>
                    <span id="loading-message">${message}</span>
                </div>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="إغلاق"></button>
            </div>
        `;

        $('#alertContainer').html(alertHtml);
    }

    // إضافة دالة تحديث تنبيه التحميل
    function updateLoadingAlert(message) {
        $('#loading-message').text(message);
    }

    // دالة عرض ملخص الرواتب
    function showSalariesSummary(month, year, branchId) {
        // استرجاع بيانات ملخص الرواتب
        $.ajax({
            url: '../../api/employees.php',
            type: 'POST',
            data: {
                action: 'get_salaries_summary',
                month: month,
                year: year,
                branch_id: branchId
            },
            dataType: 'json',
            success: function(response) {
                if (response.status === 'success') {
                    // إنشاء محتوى النافذة
                    let modalContent = `
                    <div class="modal fade" id="salariesSummaryModal" tabindex="-1" aria-labelledby="salariesSummaryModalLabel" aria-hidden="true">
                        <div class="modal-dialog modal-lg">
                            <div class="modal-content">
                                <div class="modal-header bg-primary text-white">
                                    <h5 class="modal-title" id="salariesSummaryModalLabel">ملخص الرواتب</h5>
                                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="إغلاق"></button>
                                </div>
                                <div class="modal-body">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="card mb-3">
                                                <div class="card-header bg-light">
                                                    <h6 class="mb-0">الشهر الحالي (${$('#select_month option:selected').text()} ${year})</h6>
                                                </div>
                                                <div class="card-body">
                                                    <table class="table table-bordered">
                                                        <tbody>
                                                            <tr>
                                                                <th>إجمالي الرواتب الثابتة</th>
                                                                <td>${parseFloat(response.current_month.total_fixed_amount).toFixed(2)}<?php echo $currencySymbol; ?></td>
                                                            </tr>
                                                            <tr>
                                                                <th>إجمالي العمولات</th>
                                                                <td>${parseFloat(response.current_month.total_commission_amount).toFixed(2)}<?php echo $currencySymbol; ?>/td>
                                                            </tr>
                                                            <tr>
                                                                <th>إجمالي المكافآت</th>
                                                                <td>${parseFloat(response.current_month.total_bonuses).toFixed(2)}<?php echo $currencySymbol; ?>/td>
                                                            </tr>
                                                            <tr>
                                                                <th>إجمالي الخصومات</th>
                                                                <td>${parseFloat(response.current_month.total_deductions).toFixed(2)}<?php echo $currencySymbol; ?></td>
                                                            </tr>
                                                            <tr class="table-success">
                                                                <th>الإجمالي العام</th>
                                                                <td class="fw-bold">${parseFloat(response.current_month.total_amount).toFixed(2)}<?php echo $currencySymbol; ?></td>
                                                            </tr>
                                                            <tr>
                                                                <th>عدد الموظفين</th>
                                                                <td>${response.current_month.employees_count}</td>
                                                            </tr>
                                                            <tr>
                                                                <th>الرواتب المدفوعة</th>
                                                                <td>${parseFloat(response.current_month.total_paid).toFixed(2)} <?php echo $currencySymbol; ?> (${response.current_month.paid_count} موظف)</td>
                                                            </tr>
                                                            <tr>
                                                                <th>الرواتب غير المدفوعة</th>
                                                                <td>${parseFloat(response.current_month.total_unpaid).toFixed(2)} <?php echo $currencySymbol; ?> (${response.current_month.unpaid_count} موظف)</td>
                                                            </tr>
                                                        </tbody>
                                                    </table>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="card mb-3">
                                                <div class="card-header bg-light">
                                                    <h6 class="mb-0">الشهر السابق</h6>
                                                </div>
                                                <div class="card-body">
                                                    <table class="table table-bordered">
                                                        <tbody>
                                                            <tr>
                                                                <th>إجمالي الرواتب الثابتة</th>
                                                                <td>${formatCurrency(response.previous_month.total_fixed_amount)}</td>
                                                            </tr>
                                                            <tr>
                                                                <th>إجمالي العمولات</th>
                                                                <td>${formatCurrency(response.previous_month.total_commission_amount)}</td>
                                                            </tr>
                                                            <tr>
                                                                <th>إجمالي المكافآت</th>
                                                                <td>${formatCurrency(response.previous_month.total_bonuses)}</td>
                                                            </tr>
                                                            <tr>
                                                                <th>إجمالي الخصومات</th>
                                                                <td>${formatCurrency(response.previous_month.total_deductions)}</td>
                                                            </tr>
                                                            <tr class="table-success">
                                                                <th>الإجمالي العام</th>
                                                                <td class="fw-bold">${formatCurrency(response.previous_month.total_amount)}</td>
                                                            </tr>
                                                            <tr>
                                                                <th>عدد الموظفين</th>
                                                                <td>${response.previous_month.employees_count}</td>
                                                            </tr>
                                                        </tbody>
                                                    </table>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="row mt-3">
                                        <div class="col-md-12">
                                            <div class="card">
                                                <div class="card-header bg-light">
                                                    <h6 class="mb-0">توزيع أنواع الرواتب (الشهر الحالي)</h6>
                                                </div>
                                                <div class="card-body">
                                                    <div class="row">
                                                        <div class="col-md-4 text-center">
                                                            <div class="p-3 border rounded mb-2">
                                                                <h5>راتب ثابت</h5>
                                                                <h3>${response.current_month.salary_types.fixed}</h3>
                                                                <small>موظف</small>
                                                            </div>
                                                        </div>
                                                        <div class="col-md-4 text-center">
                                                            <div class="p-3 border rounded mb-2">
                                                                <h5>عمولة</h5>
                                                                <h3>${response.current_month.salary_types.commission}</h3>
                                                                <small>موظف</small>
                                                            </div>
                                                        </div>
                                                        <div class="col-md-4 text-center">
                                                            <div class="p-3 border rounded mb-2">
                                                                <h5>ثابت وعمولة</h5>
                                                                <h3>${response.current_month.salary_types.mixed}</h3>
                                                                <small>موظف</small>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>`;

                    // إضافة قسم المبلغ المطلوب دفعه إذا كان اليوم هو يوم الدفع
                    if (response.is_payday) {
                        modalContent += `
                        <div class="alert alert-warning mt-3">
                            <div class="d-flex align-items-center">
                                <div class="me-3">
                                    <i class="fas fa-money-bill-wave fa-2x"></i>
                                </div>
                                <div>
                                    <h5 class="alert-heading mb-1">اليوم هو يوم الدفع!</h5>
                                    <p class="mb-0">المبلغ المطلوب دفعه كرواتب: <span class="fw-bold">${parseFloat(response.current_month.total_unpaid).toFixed(2)} ر.س</span></p>
                                </div>
                            </div>
                        </div>`;
                    }

                    modalContent += `
                                </div>
                                <div class="modal-footer">
                                    <button type="button" class="btn btn-primary" id="printSalariesSummary">
                                        <i class="fas fa-print me-1"></i> طباعة الملخص
                                    </button>
                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                                </div>
                            </div>
                        </div>
                    </div>`;

                    // إضافة النافذة للصفحة وعرضها
                    $('body').append(modalContent);
                    const modal = new bootstrap.Modal(document.getElementById('salariesSummaryModal'));
                    modal.show();

                    // إضافة حدث الطباعة
                    $('#printSalariesSummary').on('click', function() {
                        window.open('print_salaries_summary.php?month=' + month + '&year=' + year + '&branch_id=' + branchId, '_blank');
                    });

                    // حذف النافذة عند إغلاقها
                    $('#salariesSummaryModal').on('hidden.bs.modal', function() {
                        $(this).remove();
                    });
                } else {
                    showAlert('حدث خطأ أثناء استرجاع ملخص الرواتب', 'danger');
                }
            },
            error: function() {
                showAlert('حدث خطأ أثناء الاتصال بالخادم', 'danger');
            }
        });
    }
</script>
<script src="../../assets/js/employees.js"></script>
