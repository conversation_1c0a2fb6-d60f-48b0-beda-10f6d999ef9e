/**
 * مكتبة الطباعة الحرارية
 */
(function(window, $) {
    'use strict';

    // Thermal Printing Utility
    const ThermalPrinter = {
        /**
         * Print invoice using thermal printer
         * @param {Object} invoiceData - Invoice data
         * @param {Object} options - Printing options
         * @returns {Promise} - Promise resolved when printing is done
         */
        printInvoice: function(invoiceData, options = {}) {
            return new Promise((resolve, reject) => {
                try {
                    // تحديد عرض الإيصال من الإعدادات
                    const receiptWidth = options.receiptWidth || 80; // القيمة الافتراضية 80mm
                    
                    // تنسيق محتوى الفاتورة باستخدام HTML
                    const content = this.formatInvoiceContent(invoiceData);
                    
                    // طباعة المحتوى
                    this.printWithBrowser(content, { 
                        preview: options.preview || false,
                        receiptWidth: receiptWidth
                    });
                    
                    resolve();
                } catch (error) {
                    reject(error);
                }
            });
        },
        
        /**
         * Format invoice content
         * @param {Object} invoice - Invoice data
         * @returns {string} - Formatted content
         */
        formatInvoiceContent: function(invoice) {
            // تنسيق محتوى الفاتورة باستخدام HTML
            let content = `
                <div style="text-align: center; margin-bottom: 10px;">
                    <h2 style="margin: 5px 0;">${invoice.system_name  || 'نظام المبيعات'}</h2>
                    <p style="margin: 5px 0;">${invoice.branch_name || 'الفرع الرئيسي'}</p>
                </div>
                
                <div style="margin-bottom: 10px;">
                    <div><strong>رقم الفاتورة:</strong> ${invoice.invoice_number}</div>
                    <div><strong>التاريخ:</strong> ${invoice.created_at}</div>
                    <div><strong>العميل:</strong> ${invoice.customer_name || 'عميل غير مسجل'}</div>
                    <div><strong>الموظف:</strong> ${invoice.employee_name || '-'}</div>
                    <div><strong>الكاشير:</strong> ${invoice.cashier_name || '-'}</div>
                </div>
                
                <table style="width: 100%; border-collapse: collapse; margin-bottom: 10px; direction: rtl;">
                    <thead>
                        <tr style="border-bottom: 1px solid #000;">
                            <th style="text-align: right; padding: 3px; font-weight: bold;">الصنف</th>
                            <th style="text-align: left; padding: 3px; font-weight: bold;">السعر</th>
                            <th style="text-align: left; padding: 3px; font-weight: bold;">الكمية</th>
                            <th style="text-align: left; padding: 3px; font-weight: bold;">الإجمالي</th>
                        </tr>
                    </thead>
                    <tbody>`;
            
            // إضافة عناصر الفاتورة
            if (invoice.items && Array.isArray(invoice.items)) {
                invoice.items.forEach(item => {
                    const itemName = item.service_name || item.product_name || 'غير محدد';
                    const price = parseFloat(item.price || 0);
                    const quantity = parseInt(item.quantity || 0);
                    const total = parseFloat(item.total || 0);
                    
                    content += `
                        <tr style="border-bottom: 1px dashed #ccc;">
                            <td style="text-align: right; padding: 3px; word-break: break-word;">${itemName}</td>
                            <td style="text-align: left; padding: 3px;">${price.toFixed(2)}</td>
                            <td style="text-align: left; padding: 3px;">${quantity}</td>
                            <td style="text-align: left; padding: 3px;">${total.toFixed(2)}</td>
                        </tr>`;
                });
            }
            
            // إضافة ملخص الفاتورة
            content += `
                    </tbody>
                </table>
                
                <div style="margin-top: 10px; border-top: 1px solid #000; padding-top: 5px;">
                    <div style="display: flex; justify-content: space-between;">
                        <span><strong>الإجمالي:</strong></span>
                        <span>${(invoice.total_amount || 0).toFixed(2)}</span>
                    </div>`;
            
            // إضافة الخصم إذا وجد
            if (invoice.discount_amount && invoice.discount_amount > 0) {
                content += `
                    <div style="display: flex; justify-content: space-between;">
                        <span><strong>الخصم:</strong></span>
                        <span>${invoice.discount_amount.toFixed(2)}</span>
                    </div>`;
            }
            
            // إضافة الضريبة إذا وجدت
            if (invoice.tax_amount && invoice.tax_amount > 0) {
                content += `
                    <div style="display: flex; justify-content: space-between;">
                        <span><strong>الضريبة:</strong></span>
                        <span>${invoice.tax_amount.toFixed(2)}</span>
                    </div>`;
            }
            
            // إضافة المبلغ النهائي
            content += `
                    <div style="display: flex; justify-content: space-between; font-weight: bold; margin-top: 5px; border-top: 1px dashed #000; padding-top: 5px;">
                        <span>المبلغ النهائي:</span>
                        <span>${invoice.final_amount.toFixed(2)}</span>
                    </div>
                </div>
                
                <div style="margin-top: 10px;">
                    <div><strong>طريقة الدفع:</strong> ${invoice.payment_method === 'cash' ? 'نقدي' : invoice.payment_method === 'card' ? 'بطاقة' : 'أخرى'}</div>
                    <div><strong>حالة الدفع:</strong> ${invoice.payment_status === 'paid' ? 'مدفوع' : invoice.payment_status === 'partial' ? 'مدفوع جزئي' : 'غير مدفوع'}</div>
                </div>
                
                <div style="margin-top: 15px; text-align: center; border-top: 1px solid #000; padding-top: 5px;">
                    <div style="margin-bottom: 5px; font-weight: bold;">
                    </div>
                    
                    ${invoice.branch_phone ? `<div style="margin-bottom: 3px;"><strong>هاتف:</strong> ${invoice.branch_phone}</div>` : ''}
                    ${invoice.branch_address ? `<div style="margin-bottom: 5px;">${invoice.branch_address}</div>` : ''}
                    
                    <p>شكراً لزيارتكم - نتطلع لخدمتكم مرة أخرى</p>
                </div>`;
            
            return content;
        },
        
        /**
         * Print using browser's print dialog
         * @param {string} content - Formatted print content
         * @param {Object} options - Printing options
         */
        printWithBrowser: function(content, options) {
            // Create print window
            const printWindow = window.open('', '_blank', 'width=600,height=800');
            
            if (!printWindow) {
                throw new Error('يرجى السماح بالنوافذ المنبثقة لطباعة الفاتورة');
            }
            
            // تحديد عرض الطباعة بناءً على إعدادات النظام (80mm أو 58mm)
            const receiptWidth = options.receiptWidth || 80; // القيمة الافتراضية 80mm
            const contentWidth = receiptWidth === 80 ? '72mm' : '48mm';
            
            // Style for thermal printer-like look
            printWindow.document.write(`
                <!DOCTYPE html>
                <html>
                    <head>
                        <title>فاتورة حرارية</title>
                        <meta charset="UTF-8">
                        <style>
                            @page {
                                size: ${receiptWidth}mm auto;
                                margin: 0;
                            }
                            * {
                                margin: 0;
                                padding: 0;
                                box-sizing: border-box;
                            }
                            html, body {
                                width: 100%;
                                margin: 0;
                                padding: 0;
                                direction: rtl;
                            }
                            body {
                                font-family: Arial, sans-serif;
                                font-weight: bold;
                                font-size: 12px;
                                line-height: 1.3;
                                color: #000;
                                padding: 0;
                                margin: 0;
                                background-color: white;
                            }
                            .receipt-container {
                                width: ${contentWidth};
                                margin: 0 auto;
                                padding: 0;
                                overflow: hidden;
                            }
                            h2 {
                                font-size: 16px;
                                font-weight: bold;
                                margin: 3px 0;
                                text-align: center;
                            }
                            p {
                                margin: 3px 0;
                            }
                            table {
                                width: 100%;
                                border-collapse: collapse;
                                margin: 5px 0;
                                table-layout: fixed;
                            }
                            th, td {
                                padding: 2px 1px;
                                font-weight: bold;
                            }
                            th {
                                text-align: right;
                                font-size: 12px;
                            }
                            td {
                                text-align: right;
                                font-size: 12px;
                            }
                            .text-left {
                                text-align: left;
                            }
                            .text-right {
                                text-align: right;
                            }
                            .text-center {
                                text-align: center;
                            }
                            .border-bottom {
                                border-bottom: 1px solid #000;
                            }
                            .dashed-border {
                                border-bottom: 1px dashed #000;
                            }
                            .summary {
                                margin-top: 5px;
                                border-top: 1px solid #000;
                                padding-top: 3px;
                            }
                            .summary-row {
                                display: flex;
                                justify-content: space-between;
                                margin: 2px 0;
                            }
                            .footer {
                                margin-top: 10px;
                                text-align: center;
                                border-top: 1px solid #000;
                                padding-top: 3px;
                                font-size: 12px;
                            }
                            @media print {
                                @page {
                                    size: ${receiptWidth}mm auto;
                                    margin: 0;
                                }
                                html, body {
                                    width: 100%;
                                    margin: 0 !important;
                                    padding: 0 !important;
                                }
                                .receipt-container {
                                    width: 100%;
                                    max-width: ${contentWidth};
                                    margin: 0 !important;
                                    padding: 0 !important;
                                }
                                .no-print {
                                    display: none;
                                }
                            }
                            .print-btn {
                                display: block;
                                width: 100%;
                                padding: 10px;
                                background: #007bff;
                                color: white;
                                text-align: center;
                                margin: 10px 0;
                                cursor: pointer;
                                border: none;
                                border-radius: 4px;
                            }
                        </style>
                    </head>
                    <body>
                        <div class="receipt-container">
                            ${content}
                        </div>
                        <div class="no-print" style="text-align: center; margin-top: 20px;">
                            <button class="print-btn" onclick="window.print();">طباعة</button>
                        </div>
                        <script>
                            window.onload = function() {
                                ${options.preview ? '' : 'setTimeout(() => { window.print(); setTimeout(() => window.close(), 500); }, 500);'}
                            }
                        </script>
                    </body>
                </html>
            `);
            
            printWindow.document.close();
        },
    };

    // Expose ThermalPrinter to global scope
    window.ThermalPrinter = ThermalPrinter;

})(window, jQuery);
