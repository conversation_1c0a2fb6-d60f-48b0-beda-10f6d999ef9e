<?php
/**
 * API لتصدير فواتير العميل إلى Excel
 */

// تعريف ثابت لمنع الوصول المباشر للملفات المرتبطة
define('BASEPATH', true);

// استدعاء ملفات التهيئة
require_once '../config/init.php';

// التحقق من تسجيل الدخول
requireLogin();

// التحقق من وجود صلاحية عرض العملاء وتصدير الفواتير
requirePermission('customers_view');
requirePermission('invoices_export');

// استدعاء مكتبة PHPExcel عبر Composer
require_once '../vendor/autoload.php';

use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;

try {
    // التحقق من وجود معرف العميل
    if (!isset($_GET['customer_id']) || empty($_GET['customer_id'])) {
        throw new Exception('لم يتم تحديد العميل');
    }

    $customerId = intval($_GET['customer_id']);
    $startDate = isset($_GET['date_from']) ? sanitizeInput($_GET['date_from']) : null;
    $endDate = isset($_GET['date_to']) ? sanitizeInput($_GET['date_to']) : null;

    // إنشاء كائن العميل
    $customerModel = new Customer($db);

    // الحصول على بيانات العميل
    $customer = $customerModel->getCustomerById($customerId);
    if (!$customer) {
        throw new Exception('العميل غير موجود');
    }

    // استرجاع فواتير العميل
    $invoices = $customerModel->getCustomerInvoices($customerId, $startDate, $endDate, 1000);

    // إنشاء ملف Excel
    $spreadsheet = new Spreadsheet();
    $sheet = $spreadsheet->getActiveSheet();
    
    // إعداد عنوان الملف
    $title = 'فواتير العميل: ' . $customer['name'];
    if ($startDate && $endDate) {
        $title .= ' (من ' . $startDate . ' إلى ' . $endDate . ')';
    }
    
    $sheet->setTitle('Invoices');
    
    // إعداد ترويسة الملف
    $sheet->setCellValue('A1', $title);
    $sheet->mergeCells('A1:I1');
    $sheet->getStyle('A1')->getFont()->setBold(true)->setSize(14);
    $sheet->getStyle('A1')->getAlignment()->setHorizontal(\PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER);
    
    // إعداد العناوين
    $headers = [
        'A2' => 'رقم الفاتورة',
        'B2' => 'التاريخ',
        'C2' => 'الوقت',
        'D2' => 'الفرع',
        'E2' => 'المبلغ الإجمالي',
        'F2' => 'الخصم',
        'G2' => 'الضريبة',
        'H2' => 'المبلغ النهائي',
        'I2' => 'طريقة الدفع'
    ];
    
    foreach ($headers as $cell => $value) {
        $sheet->setCellValue($cell, $value);
        $sheet->getStyle($cell)->getFont()->setBold(true);
    }
    
    // تنسيق العناوين
    $sheet->getStyle('A2:I2')->getAlignment()->setHorizontal(\PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER);
    $sheet->getStyle('A2:I2')->getFill()->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)->getStartColor()->setRGB('EEEEEE');
    
    // إضافة البيانات
    $row = 3;
    foreach ($invoices as $invoice) {
        $datetime = new DateTime($invoice['created_at']);
        
        $sheet->setCellValue('A' . $row, $invoice['invoice_number']);
        $sheet->setCellValue('B' . $row, $datetime->format('Y-m-d'));
        $sheet->setCellValue('C' . $row, $datetime->format('H:i:s'));
        $sheet->setCellValue('D' . $row, $invoice['branch_id']);
        $sheet->setCellValue('E' . $row, $invoice['total_amount']);
        $sheet->setCellValue('F' . $row, $invoice['discount_amount']);
        $sheet->setCellValue('G' . $row, $invoice['tax_amount']);
        $sheet->setCellValue('H' . $row, $invoice['final_amount']);
        
        $paymentMethod = '';
        switch ($invoice['payment_method']) {
            case 'cash': $paymentMethod = 'نقد'; break;
            case 'card': $paymentMethod = 'بطاقة ائتمان'; break;
            case 'bank': $paymentMethod = 'تحويل بنكي'; break;
            default: $paymentMethod = $invoice['payment_method'];
        }
        
        $sheet->setCellValue('I' . $row, $paymentMethod);
        
        $row++;
    }
    
    // تنسيق خلايا البيانات
    $sheet->getStyle('A3:I' . ($row - 1))->getAlignment()->setHorizontal(\PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER);
    $sheet->getStyle('E3:H' . ($row - 1))->getNumberFormat()->setFormatCode(\PhpOffice\PhpSpreadsheet\Style\NumberFormat::FORMAT_NUMBER_COMMA_SEPARATED1);
    
    // تنسيق الحدود لجميع الخلايا
    $styleArray = [
        'borders' => [
            'allBorders' => [
                'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
            ],
        ],
    ];
    $sheet->getStyle('A1:I' . ($row - 1))->applyFromArray($styleArray);
    
    // ضبط عرض الأعمدة
    foreach (range('A', 'I') as $col) {
        $sheet->getColumnDimension($col)->setAutoSize(true);
    }
    
    // إضافة صف للإجماليات
    $sheet->setCellValue('A' . $row, 'الإجمالي');
    $sheet->mergeCells('A' . $row . ':D' . $row);
    $sheet->getStyle('A' . $row)->getFont()->setBold(true);
    $sheet->getStyle('A' . $row)->getAlignment()->setHorizontal(\PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER);
    
    $sheet->setCellValue('E' . $row, '=SUM(E3:E' . ($row - 1) . ')');
    $sheet->setCellValue('F' . $row, '=SUM(F3:F' . ($row - 1) . ')');
    $sheet->setCellValue('G' . $row, '=SUM(G3:G' . ($row - 1) . ')');
    $sheet->setCellValue('H' . $row, '=SUM(H3:H' . ($row - 1) . ')');
    
    $sheet->getStyle('E' . $row . ':H' . $row)->getFont()->setBold(true);
    $sheet->getStyle('E' . $row . ':H' . $row)->getNumberFormat()->setFormatCode(\PhpOffice\PhpSpreadsheet\Style\NumberFormat::FORMAT_NUMBER_COMMA_SEPARATED1);
    $sheet->getStyle('A' . $row . ':I' . $row)->getFill()->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)->getStartColor()->setRGB('EEEEEE');
    $sheet->getStyle('A' . $row . ':I' . $row)->applyFromArray($styleArray);
    
    // تعيين اسم الملف
    $fileName = 'فواتير_العميل_' . $customer['name'] . '_' . date('Y-m-d_H-i-s') . '.xlsx';
    
    // إرسال الملف للتحميل
    header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    header('Content-Disposition: attachment;filename="' . $fileName . '"');
    header('Cache-Control: max-age=0');
    
    $writer = new Xlsx($spreadsheet);
    $writer->save('php://output');
    exit;
    
} catch (Exception $e) {
    // إعادة توجيه إلى صفحة الخطأ
    setFlashMessage($e->getMessage(), 'danger');
    redirect('../pages/customers/view.php?id=' . $customerId);
} 