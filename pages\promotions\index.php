<?php
/**
 * صفحة إدارة العروض والخصومات
 */

// تعريف ثابت لمنع الوصول المباشر للملفات المرتبطة
define('BASEPATH', true);

// استدعاء ملفات التهيئة
require_once '../../config/init.php';

// التحقق من تسجيل الدخول
requireLogin();

// التحقق من الصلاحيات
requirePermission('promotions_view');

// عنوان الصفحة
$pageTitle = 'إدارة العروض والخصومات';

// إنشاء كائنات النماذج
$promotionModel = new Promotion($db);
$branchModel = new Branch($db);

// التحقق من صلاحيات المستخدم
$isAdmin = hasPermission('admin_access');

// الفرع الحالي
$branchId = $_SESSION['user_branch_id'] ?? null;

// إذا كان المستخدم مديراً، يمكنه اختيار الفرع
$selectedBranchId = $branchId;
if ($isAdmin && isset($_GET['branch_id'])) {
    $selectedBranchId = intval($_GET['branch_id']);
}

// الحصول على قائمة الفروع إذا كان المستخدم مديراً
$branches = [];
if ($isAdmin) {
    $branches = $branchModel->getBranches(['is_active' => 1]);
}

// ضبط معلمات البحث
$searchParams = [
    'branch_id' => $selectedBranchId,
    'page' => isset($_GET['page']) ? intval($_GET['page']) : 1,
    'limit' => 10 // عرض 10 سجلات في الصفحة
];

// إضافة فلتر البحث إذا تم تحديده
if (isset($_GET['search']) && !empty($_GET['search'])) {
    $searchParams['search'] = $_GET['search'];
}

// إضافة فلتر الحالة إذا تم تحديده
if (isset($_GET['status']) && in_array($_GET['status'], ['active', 'inactive'])) {
    $searchParams['is_active'] = ($_GET['status'] === 'active') ? 1 : 0;
}

// حساب الإزاحة للصفحات
$searchParams['offset'] = ($searchParams['page'] - 1) * $searchParams['limit'];

// استعلام قائمة العروض والخصومات
$promotions = $promotionModel->getPromotions($searchParams);

// حساب إجمالي عدد السجلات
$totalRecords = $promotionModel->countPromotions($searchParams);
$totalPages = ceil($totalRecords / $searchParams['limit']);

// استدعاء رأس الصفحة
include '../../includes/templates/header.php';
?>

<!-- المحتوى الرئيسي للصفحة -->
<div class="container-fluid">
    <!-- عنوان الصفحة -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2 class="h3 mb-0 text-gray-800">إدارة العروض والخصومات</h2>

        <?php if (hasPermission('promotions_create')): ?>
        <a href="add.php" class="btn btn-primary">
            <i class="fas fa-plus-circle me-1"></i> إضافة عرض جديد
        </a>
        <?php endif; ?>
    </div>

    <!-- بطاقة البحث والفلترة -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">بحث وفلترة</h6>
        </div>
        <div class="card-body">
            <form method="get" class="mb-0">
                <div class="row g-3 align-items-end">
                    <?php if ($isAdmin && !empty($branches)): ?>
                    <div class="col-md-3">
                        <label for="branch_id" class="form-label">الفرع</label>
                        <select id="branch_id" name="branch_id" class="form-select">
                            <option value="">جميع الفروع</option>
                            <?php foreach ($branches as $branch): ?>
                            <option value="<?php echo $branch['id']; ?>" <?php echo ($selectedBranchId == $branch['id']) ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($branch['name']); ?>
                            </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <?php endif; ?>

                    <div class="col-md-3">
                        <label for="status" class="form-label">الحالة</label>
                        <select id="status" name="status" class="form-select">
                            <option value="">جميع الحالات</option>
                            <option value="active" <?php echo (isset($_GET['status']) && $_GET['status'] === 'active') ? 'selected' : ''; ?>>نشط</option>
                            <option value="inactive" <?php echo (isset($_GET['status']) && $_GET['status'] === 'inactive') ? 'selected' : ''; ?>>غير نشط</option>
                        </select>
                    </div>

                    <div class="col-md-4">
                        <label for="search" class="form-label">بحث</label>
                        <input type="text" id="search" name="search" class="form-control" placeholder="ابحث عن اسم أو وصف العرض..." value="<?php echo isset($_GET['search']) ? htmlspecialchars($_GET['search']) : ''; ?>">
                    </div>

                    <div class="col-md-2">
                        <button type="submit" class="btn btn-primary w-100">
                            <i class="fas fa-search me-1"></i> بحث
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- بطاقة قائمة العروض والخصومات -->
    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex justify-content-between align-items-center">
            <h6 class="m-0 font-weight-bold text-primary">قائمة العروض والخصومات</h6>
            <span class="badge bg-primary"><?php echo $totalRecords; ?> عرض</span>
        </div>
        <div class="card-body">
            <?php if (empty($promotions)): ?>
            <div class="alert alert-info text-center">
                <i class="fas fa-info-circle mb-2 fa-2x"></i>
                <p class="mb-0">لا توجد عروض وخصومات متاحة حاليًا.</p>
                <?php if (hasPermission('promotions_create')): ?>
                <div class="mt-3">
                    <a href="add.php" class="btn btn-primary">
                        <i class="fas fa-plus-circle me-1"></i> إضافة عرض جديد
                    </a>
                </div>
                <?php endif; ?>
            </div>
            <?php else: ?>
            <div class="table-responsive">
                <table class="table table-bordered table-hover">
                    <thead class="table-light">
                        <tr>
                            <th width="5%">#</th>
                            <th width="15%">اسم العرض</th>
                            <th width="10%">نوع الشرط</th>
                            <th width="15%">نطاق الشرط</th>
                            <th width="15%">الخصم</th>
                            <th width="15%">الفترة</th>
                            <th width="10%">الحالة</th>
                            <th width="15%">الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($promotions as $index => $promotion): ?>
                        <tr>
                            <td><?php echo ($searchParams['page'] - 1) * $searchParams['limit'] + $index + 1; ?></td>
                            <td>
                                <strong><?php echo htmlspecialchars($promotion['name']); ?></strong>
                                <?php if (!empty($promotion['description'])): ?>
                                <div class="small text-muted"><?php echo htmlspecialchars(substr($promotion['description'], 0, 50)) . (strlen($promotion['description']) > 50 ? '...' : ''); ?></div>
                                <?php endif; ?>
                            </td>
                            <td>
                                <?php
                                $conditionTypes = [
                                    'total_amount' => 'إجمالي الفاتورة',
                                    'items_count' => 'عدد العناصر',
                                    'specific_product' => 'منتج محدد',
                                    'specific_service' => 'خدمة محددة'
                                ];
                                echo $conditionTypes[$promotion['condition_type']] ?? $promotion['condition_type'];
                                ?>
                            </td>
                            <td>
                                <?php
                                // عرض الحد الأدنى
                                if ($promotion['condition_type'] === 'total_amount') {
                                    echo 'من: ' . number_format($promotion['condition_value'], 2) . ' ج.م';
                                } else {
                                    echo 'من: ' . $promotion['condition_value'];
                                }

                                // عرض الحد الأقصى إذا كان محدداً
                                if (!empty($promotion['condition_max_value'])) {
                                    echo '<br>';
                                    if ($promotion['condition_type'] === 'total_amount') {
                                        echo 'إلى: ' . number_format($promotion['condition_max_value'], 2) . ' ج.م';
                                    } else {
                                        echo 'إلى: ' . $promotion['condition_max_value'];
                                    }
                                }
                                ?>
                            </td>
                            <td>
                                <?php
                                if ($promotion['discount_type'] === 'percentage') {
                                    echo $promotion['discount_value'] . '%';
                                } else {
                                    echo number_format($promotion['discount_value'], 2) . ' ج.م';
                                }
                                ?>
                            </td>
                            <td>
                                <?php
                                if (empty($promotion['start_date']) && empty($promotion['end_date'])) {
                                    echo 'غير محدد';
                                } else {
                                    if (!empty($promotion['start_date'])) {
                                        echo 'من: ' . date('Y-m-d', strtotime($promotion['start_date'])) . '<br>';
                                    }
                                    if (!empty($promotion['end_date'])) {
                                        echo 'إلى: ' . date('Y-m-d', strtotime($promotion['end_date']));
                                    }
                                }
                                ?>
                            </td>
                            <td>
                                <?php if ($promotion['is_active']): ?>
                                <span class="badge bg-success">نشط</span>
                                <?php else: ?>
                                <span class="badge bg-danger">غير نشط</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <div class="btn-group">
                                    <?php if (hasPermission('promotions_edit')): ?>
                                    <a href="edit.php?id=<?php echo $promotion['id']; ?>" class="btn btn-sm btn-primary" title="تعديل">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <?php endif; ?>

                                    <?php if (hasPermission('promotions_delete')): ?>
                                    <button type="button" class="btn btn-sm btn-danger delete-promotion" data-id="<?php echo $promotion['id']; ?>" data-name="<?php echo htmlspecialchars($promotion['name']); ?>" title="حذف">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                    <?php endif; ?>

                                    <?php if (hasPermission('promotions_edit')): ?>
                                    <button type="button" class="btn btn-sm <?php echo $promotion['is_active'] ? 'btn-warning' : 'btn-success'; ?> toggle-status" data-id="<?php echo $promotion['id']; ?>" data-status="<?php echo $promotion['is_active']; ?>" title="<?php echo $promotion['is_active'] ? 'تعطيل' : 'تفعيل'; ?>">
                                        <i class="fas <?php echo $promotion['is_active'] ? 'fa-ban' : 'fa-check'; ?>"></i>
                                    </button>
                                    <?php endif; ?>

                                    <?php if (hasPermission('promotions_view')): ?>
                                    <a href="usage_history.php?id=<?php echo $promotion['id']; ?>" class="btn btn-sm btn-info" title="سجل الاستخدام">
                                        <i class="fas fa-history"></i>
                                    </a>
                                    <?php endif; ?>
                                </div>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>

            <!-- الصفحات -->
            <?php if ($totalPages > 1): ?>
            <nav aria-label="Page navigation" class="mt-4">
                <ul class="pagination justify-content-center">
                    <?php if ($searchParams['page'] > 1): ?>
                    <li class="page-item">
                        <a class="page-link" href="?page=1<?php echo isset($_GET['search']) ? '&search=' . urlencode($_GET['search']) : ''; ?><?php echo isset($_GET['status']) ? '&status=' . $_GET['status'] : ''; ?><?php echo $isAdmin && $selectedBranchId ? '&branch_id=' . $selectedBranchId : ''; ?>">
                            <i class="fas fa-angle-double-right"></i>
                        </a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="?page=<?php echo $searchParams['page'] - 1; ?><?php echo isset($_GET['search']) ? '&search=' . urlencode($_GET['search']) : ''; ?><?php echo isset($_GET['status']) ? '&status=' . $_GET['status'] : ''; ?><?php echo $isAdmin && $selectedBranchId ? '&branch_id=' . $selectedBranchId : ''; ?>">
                            <i class="fas fa-angle-right"></i>
                        </a>
                    </li>
                    <?php endif; ?>

                    <?php
                    $startPage = max(1, $searchParams['page'] - 2);
                    $endPage = min($totalPages, $searchParams['page'] + 2);

                    for ($i = $startPage; $i <= $endPage; $i++):
                    ?>
                    <li class="page-item <?php echo ($i == $searchParams['page']) ? 'active' : ''; ?>">
                        <a class="page-link" href="?page=<?php echo $i; ?><?php echo isset($_GET['search']) ? '&search=' . urlencode($_GET['search']) : ''; ?><?php echo isset($_GET['status']) ? '&status=' . $_GET['status'] : ''; ?><?php echo $isAdmin && $selectedBranchId ? '&branch_id=' . $selectedBranchId : ''; ?>">
                            <?php echo $i; ?>
                        </a>
                    </li>
                    <?php endfor; ?>

                    <?php if ($searchParams['page'] < $totalPages): ?>
                    <li class="page-item">
                        <a class="page-link" href="?page=<?php echo $searchParams['page'] + 1; ?><?php echo isset($_GET['search']) ? '&search=' . urlencode($_GET['search']) : ''; ?><?php echo isset($_GET['status']) ? '&status=' . $_GET['status'] : ''; ?><?php echo $isAdmin && $selectedBranchId ? '&branch_id=' . $selectedBranchId : ''; ?>">
                            <i class="fas fa-angle-left"></i>
                        </a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="?page=<?php echo $totalPages; ?><?php echo isset($_GET['search']) ? '&search=' . urlencode($_GET['search']) : ''; ?><?php echo isset($_GET['status']) ? '&status=' . $_GET['status'] : ''; ?><?php echo $isAdmin && $selectedBranchId ? '&branch_id=' . $selectedBranchId : ''; ?>">
                            <i class="fas fa-angle-double-left"></i>
                        </a>
                    </li>
                    <?php endif; ?>
                </ul>
            </nav>
            <?php endif; ?>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- نافذة تأكيد الحذف -->
<div class="modal fade" id="deletePromotionModal" tabindex="-1" aria-labelledby="deletePromotionModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deletePromotionModalLabel">تأكيد الحذف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من حذف العرض: <strong id="promotionNameToDelete"></strong>؟</p>
                <p class="text-danger">هذا الإجراء لا يمكن التراجع عنه.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-danger" id="confirmDeletePromotion">تأكيد الحذف</button>
            </div>
        </div>
    </div>
</div>

<!-- استدعاء قالب الفوتر -->
<?php include '../../includes/templates/footer.php'; ?>

<script>
$(document).ready(function() {
    // متغير لتخزين معرف العرض المراد حذفه
    let promotionIdToDelete = null;

    // عند النقر على زر الحذف
    $('.delete-promotion').on('click', function() {
        promotionIdToDelete = $(this).data('id');
        const promotionName = $(this).data('name');

        // عرض اسم العرض في نافذة التأكيد
        $('#promotionNameToDelete').text(promotionName);

        // عرض نافذة تأكيد الحذف
        $('#deletePromotionModal').modal('show');
    });

    // عند تأكيد الحذف
    $('#confirmDeletePromotion').on('click', function() {
        if (promotionIdToDelete) {
            // إرسال طلب الحذف
            $.ajax({
                url: '../../api/promotions.php?action=delete',
                type: 'POST',
                data: {
                    id: promotionIdToDelete
                },
                dataType: 'json',
                success: function(response) {
                    if (response.status === 'success') {
                        // إغلاق النافذة
                        $('#deletePromotionModal').modal('hide');

                        // عرض رسالة نجاح
                        showAlert('success', response.message);

                        // إعادة تحميل الصفحة بعد ثانيتين
                        setTimeout(function() {
                            window.location.reload();
                        }, 2000);
                    } else {
                        // عرض رسالة الخطأ
                        showAlert('danger', response.message);
                    }
                },
                error: function() {
                    // عرض رسالة خطأ عامة
                    showAlert('danger', 'حدث خطأ أثناء معالجة الطلب. يرجى المحاولة مرة أخرى.');
                }
            });
        }
    });

    // تغيير حالة العرض (نشط/غير نشط)
    $('.toggle-status').on('click', function() {
        const promotionId = $(this).data('id');
        const currentStatus = $(this).data('status');
        const newStatus = currentStatus == 1 ? 0 : 1;
        const statusText = newStatus == 1 ? 'تفعيل' : 'تعطيل';

        // تأكيد تغيير الحالة
        if (confirm(`هل أنت متأكد من ${statusText} هذا العرض؟`)) {
            // إرسال طلب تغيير الحالة
            $.ajax({
                url: '../../api/promotions.php?action=toggle_status',
                type: 'POST',
                data: {
                    id: promotionId,
                    is_active: newStatus
                },
                dataType: 'json',
                success: function(response) {
                    if (response.status === 'success') {
                        // عرض رسالة نجاح
                        showAlert('success', response.message);

                        // إعادة تحميل الصفحة بعد ثانيتين
                        setTimeout(function() {
                            window.location.reload();
                        }, 2000);
                    } else {
                        // عرض رسالة الخطأ
                        showAlert('danger', response.message);
                    }
                },
                error: function() {
                    // عرض رسالة خطأ عامة
                    showAlert('danger', 'حدث خطأ أثناء معالجة الطلب. يرجى المحاولة مرة أخرى.');
                }
            });
        }
    });

    // دالة لعرض التنبيهات
    function showAlert(type, message) {
        const alertHtml = `
            <div class="alert alert-${type} alert-dismissible fade show" role="alert">
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        `;

        // إضافة التنبيه في أعلى الصفحة
        $('.container-fluid').prepend(alertHtml);

        // إزالة التنبيه تلقائيًا بعد 5 ثوانٍ
        setTimeout(function() {
            $('.alert').alert('close');
        }, 5000);
    }
});
</script>
