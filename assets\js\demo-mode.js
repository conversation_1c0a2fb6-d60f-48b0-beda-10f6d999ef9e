/**
 * JavaScript للنسخة التجريبية
 * يحتوي على الوظائف التفاعلية والتحذيرات الخاصة بالنسخة التجريبية
 */

// متغيرات عامة للنسخة التجريبية
const DEMO_CONFIG = {
    warningInterval: 300000, // 5 دقائق
    resetWarningTime: 24, // 24 ساعة
    maxDemoRecords: 100,
    restrictedActions: ['delete_core_data', 'backup', 'restore', 'system_reset']
};

// تهيئة النسخة التجريبية عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    initDemoMode();
});

/**
 * تهيئة وضع النسخة التجريبية
 */
function initDemoMode() {
    // إضافة كلاس النسخة التجريبية للجسم
    document.body.classList.add('demo-mode');
    
    // إضافة شريط التحذير
    addDemoWarningBar();
    
    // إضافة العلامة المائية
    addDemoWatermark();
    
    // إضافة شارة النسخة التجريبية
    addDemoBadge();
    
    // تفعيل التحذيرات الدورية
    startPeriodicWarnings();
    
    // تقييد العمليات الخطيرة
    restrictDangerousActions();
    
    // إضافة معلومات إضافية للجداول
    enhanceTables();
    
    // تخصيص النماذج
    customizeForms();
    
    // إضافة تأثيرات بصرية
    addVisualEffects();
}

/**
 * إضافة شريط التحذير في أعلى الصفحة
 */
function addDemoWarningBar() {
    const warningBar = document.createElement('div');
    warningBar.className = 'demo-warning-bar';
    warningBar.innerHTML = `
        <i class="fas fa-exclamation-triangle"></i>
        تحذير: هذه نسخة تجريبية - جميع البيانات وهمية لأغراض العرض فقط
        <button onclick="hideDemoWarning()" style="float: left; background: none; border: none; color: white; font-size: 16px;">
            <i class="fas fa-times"></i>
        </button>
    `;
    
    document.body.insertBefore(warningBar, document.body.firstChild);
}

/**
 * إضافة العلامة المائية
 */
function addDemoWatermark() {
    const watermark = document.createElement('div');
    watermark.className = 'demo-watermark';
    watermark.textContent = 'نسخة تجريبية';
    document.body.appendChild(watermark);
}

/**
 * إضافة شارة النسخة التجريبية
 */
function addDemoBadge() {
    const badge = document.createElement('div');
    badge.className = 'demo-badge';
    badge.innerHTML = '<i class="fas fa-flask"></i> DEMO';
    document.body.appendChild(badge);
}

/**
 * بدء التحذيرات الدورية
 */
function startPeriodicWarnings() {
    setInterval(() => {
        showDemoReminder();
    }, DEMO_CONFIG.warningInterval);
}

/**
 * إظهار تذكير النسخة التجريبية
 */
function showDemoReminder() {
    const modal = createDemoModal(
        'تذكير النسخة التجريبية',
        `
        <div style="text-align: center; padding: 20px;">
            <i class="fas fa-info-circle" style="font-size: 48px; color: #3498db; margin-bottom: 15px;"></i>
            <h4>هذه نسخة تجريبية</h4>
            <p>جميع البيانات المعروضة وهمية لأغراض العرض والتجربة فقط.</p>
            <p>سيتم إعادة تعيين البيانات كل ${DEMO_CONFIG.resetWarningTime} ساعة.</p>
            <small class="text-muted">للحصول على النسخة الكاملة، يرجى التواصل معنا.</small>
        </div>
        `,
        'info'
    );
    
    document.body.appendChild(modal);
    setTimeout(() => modal.remove(), 5000);
}

/**
 * تقييد العمليات الخطيرة
 */
function restrictDangerousActions() {
    // تقييد أزرار الحذف
    document.querySelectorAll('.btn-danger, .delete-btn').forEach(btn => {
        btn.addEventListener('click', function(e) {
            if (this.dataset.action && DEMO_CONFIG.restrictedActions.includes(this.dataset.action)) {
                e.preventDefault();
                showDemoRestrictionWarning(this.dataset.action);
                return false;
            }
        });
    });
    
    // تقييد النماذج الحساسة
    document.querySelectorAll('form[data-sensitive="true"]').forEach(form => {
        form.addEventListener('submit', function(e) {
            e.preventDefault();
            showDemoRestrictionWarning('sensitive_form');
            return false;
        });
    });
}

/**
 * إظهار تحذير تقييد العملية
 */
function showDemoRestrictionWarning(action) {
    const actionNames = {
        'delete_core_data': 'حذف البيانات الأساسية',
        'backup': 'إنشاء نسخة احتياطية',
        'restore': 'استعادة نسخة احتياطية',
        'system_reset': 'إعادة تعيين النظام',
        'sensitive_form': 'تعديل الإعدادات الحساسة'
    };
    
    const modal = createDemoModal(
        'عملية مقيدة',
        `
        <div style="text-align: center; padding: 20px;">
            <i class="fas fa-ban" style="font-size: 48px; color: #e74c3c; margin-bottom: 15px;"></i>
            <h4>عذراً، هذه العملية مقيدة</h4>
            <p>العملية "${actionNames[action] || action}" غير متاحة في النسخة التجريبية.</p>
            <p>للوصول لجميع الميزات، يرجى الحصول على النسخة الكاملة.</p>
        </div>
        `,
        'warning'
    );
    
    document.body.appendChild(modal);
    setTimeout(() => modal.remove(), 4000);
}

/**
 * تحسين الجداول بمعلومات إضافية
 */
function enhanceTables() {
    document.querySelectorAll('table').forEach(table => {
        // إضافة عداد السجلات
        const rowCount = table.querySelectorAll('tbody tr').length;
        if (rowCount > 0) {
            const caption = document.createElement('caption');
            caption.innerHTML = `
                <small class="text-muted">
                    <i class="fas fa-database"></i> 
                    ${rowCount} سجل تجريبي | 
                    <i class="fas fa-clock"></i> 
                    آخر تحديث: ${new Date().toLocaleDateString('ar-SA')}
                </small>
            `;
            table.insertBefore(caption, table.firstChild);
        }
    });
}

/**
 * تخصيص النماذج
 */
function customizeForms() {
    document.querySelectorAll('form').forEach(form => {
        // إضافة تحذير للنماذج
        const warning = document.createElement('div');
        warning.className = 'alert alert-info demo-form-warning';
        warning.innerHTML = `
            <i class="fas fa-info-circle"></i>
            <strong>ملاحظة:</strong> هذا نموذج تجريبي. البيانات المدخلة لن تؤثر على النظام الفعلي.
        `;
        form.insertBefore(warning, form.firstChild);
    });
}

/**
 * إضافة تأثيرات بصرية
 */
function addVisualEffects() {
    // إضافة تأثير الوميض للعناصر المهمة
    document.querySelectorAll('.stat-card, .card-header').forEach(element => {
        element.style.position = 'relative';
        element.style.overflow = 'hidden';
    });
    
    // إضافة تأثير التمرير للأزرار
    document.querySelectorAll('.btn').forEach(btn => {
        btn.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-2px)';
            this.style.boxShadow = '0 4px 8px rgba(0,0,0,0.2)';
        });
        
        btn.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
            this.style.boxShadow = 'none';
        });
    });
}

/**
 * إنشاء نافذة منبثقة للنسخة التجريبية
 */
function createDemoModal(title, content, type = 'info') {
    const modal = document.createElement('div');
    modal.className = 'demo-modal';
    modal.style.cssText = `
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: white;
        border-radius: 10px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        z-index: 10000;
        max-width: 400px;
        width: 90%;
        animation: fadeInScale 0.3s ease-out;
    `;
    
    const typeColors = {
        'info': '#3498db',
        'warning': '#f39c12',
        'error': '#e74c3c',
        'success': '#27ae60'
    };
    
    modal.innerHTML = `
        <div style="background: ${typeColors[type]}; color: white; padding: 15px; border-radius: 10px 10px 0 0;">
            <h5 style="margin: 0; text-align: center;">${title}</h5>
        </div>
        <div style="padding: 0;">
            ${content}
        </div>
        <div style="padding: 15px; text-align: center; border-top: 1px solid #eee;">
            <button onclick="this.closest('.demo-modal').remove()" 
                    style="background: ${typeColors[type]}; color: white; border: none; padding: 8px 20px; border-radius: 5px; cursor: pointer;">
                حسناً
            </button>
        </div>
    `;
    
    // إضافة الأنيميشن
    const style = document.createElement('style');
    style.textContent = `
        @keyframes fadeInScale {
            from {
                opacity: 0;
                transform: translate(-50%, -50%) scale(0.8);
            }
            to {
                opacity: 1;
                transform: translate(-50%, -50%) scale(1);
            }
        }
    `;
    document.head.appendChild(style);
    
    return modal;
}

/**
 * إخفاء شريط التحذير
 */
function hideDemoWarning() {
    const warningBar = document.querySelector('.demo-warning-bar');
    if (warningBar) {
        warningBar.style.animation = 'slideUp 0.3s ease-out forwards';
        setTimeout(() => warningBar.remove(), 300);
    }
}

/**
 * إظهار معلومات النسخة التجريبية
 */
function showDemoInfo() {
    const modal = createDemoModal(
        'معلومات النسخة التجريبية',
        `
        <div style="padding: 20px;">
            <h6><i class="fas fa-info-circle"></i> حول هذه النسخة:</h6>
            <ul style="text-align: right; margin: 15px 0;">
                <li>جميع البيانات وهمية لأغراض العرض</li>
                <li>بعض الوظائف مقيدة لأغراض الأمان</li>
                <li>يتم إعادة تعيين البيانات دورياً</li>
                <li>الأداء قد يختلف عن النسخة الفعلية</li>
            </ul>
            
            <h6><i class="fas fa-lock"></i> العمليات المقيدة:</h6>
            <ul style="text-align: right; margin: 15px 0;">
                <li>حذف البيانات الأساسية</li>
                <li>إنشاء واستعادة النسخ الاحتياطية</li>
                <li>تعديل إعدادات النظام الحساسة</li>
                <li>إعادة تعيين النظام</li>
            </ul>
            
            <div style="background: #f8f9fa; padding: 10px; border-radius: 5px; margin-top: 15px;">
                <small class="text-muted">
                    <i class="fas fa-envelope"></i>
                    للحصول على النسخة الكاملة أو الاستفسار: 
                    <a href="mailto:<EMAIL>"><EMAIL></a>
                </small>
            </div>
        </div>
        `,
        'info'
    );
    
    document.body.appendChild(modal);
}

/**
 * إضافة زر معلومات النسخة التجريبية
 */
function addDemoInfoButton() {
    const infoBtn = document.createElement('button');
    infoBtn.className = 'btn btn-sm btn-outline-info demo-info-btn';
    infoBtn.innerHTML = '<i class="fas fa-question-circle"></i> معلومات النسخة التجريبية';
    infoBtn.onclick = showDemoInfo;
    infoBtn.style.cssText = `
        position: fixed;
        bottom: 20px;
        left: 20px;
        z-index: 1000;
        border-radius: 20px;
    `;
    
    document.body.appendChild(infoBtn);
}

// تهيئة زر المعلومات عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(addDemoInfoButton, 1000);
});

/**
 * تتبع الأنشطة في النسخة التجريبية
 */
function trackDemoActivity(action, details = {}) {
    const activity = {
        timestamp: new Date().toISOString(),
        action: action,
        details: details,
        userAgent: navigator.userAgent,
        url: window.location.href
    };
    
    // حفظ النشاط في localStorage للمراجعة
    const activities = JSON.parse(localStorage.getItem('demo_activities') || '[]');
    activities.push(activity);
    
    // الاحتفاظ بآخر 100 نشاط فقط
    if (activities.length > 100) {
        activities.splice(0, activities.length - 100);
    }
    
    localStorage.setItem('demo_activities', JSON.stringify(activities));
}

// تتبع النقرات والتفاعلات
document.addEventListener('click', function(e) {
    if (e.target.matches('.btn, a, .nav-link')) {
        trackDemoActivity('click', {
            element: e.target.tagName,
            text: e.target.textContent.trim().substring(0, 50),
            className: e.target.className
        });
    }
});

// إضافة أنيميشن للانتقال بين الصفحات
window.addEventListener('beforeunload', function() {
    document.body.style.opacity = '0.5';
    document.body.style.transform = 'scale(0.95)';
    document.body.style.transition = 'all 0.3s ease-out';
});
