/**
 * Salon Management System - Reports Page Functionality
 *
 * @version 1.0.0
 * <AUTHOR> Management System Team
 */

// Currency symbol from database
const CURRENCY_SYMBOL = typeof currencySymbol !== 'undefined' ? currencySymbol : 'ر.س';

$(document).ready(function() {
    // Initialize report filters
    initReportFilters();

    // Sales Report Handling
    $('#sales-report-form').on('submit', function(e) {
        e.preventDefault();
        generateSalesReport();
    });

    // Services Report Handling
    $('#services-report-form').on('submit', function(e) {
        e.preventDefault();
        generateServicesReport();
    });

    // Employees Report Handling
    $('#employees-report-form').on('submit', function(e) {
        e.preventDefault();
        generateEmployeesReport();
    });

    // Products Report Handling
    $('#products-report-form').on('submit', function(e) {
        e.preventDefault();
        generateProductsReport();
    });

    // Expenses Report Handling
    $('#expenses-report-form').on('submit', function(e) {
        e.preventDefault();
        generateExpensesReport();
    });

    /**
     * Initialize report filter components
     */
    function initReportFilters() {
        // Initialize date range picker
        $('.datepicker-range').daterangepicker({
            autoUpdateInput: false,
            locale: {
                cancelLabel: 'مسح',
                applyLabel: 'تطبيق',
                format: 'YYYY-MM-DD'
            }
        });

        // Set default date range to current month
        $('.datepicker-range').on('apply.daterangepicker', function(ev, picker) {
            $(this).val(picker.startDate.format('YYYY-MM-DD') + ' - ' + picker.endDate.format('YYYY-MM-DD'));
        });

        // Clear date range
        $('.datepicker-range').on('cancel.daterangepicker', function() {
            $(this).val('');
        });

        // Populate branch select
        populateBranchSelect();

        // Populate categories selects
        populateCategories();
    }

    /**
     * Populate branch select dropdown
     */
    function populateBranchSelect() {
        AjaxHandler.get('branches', {}, {
            dataType: 'json'
        }).then(function(response) {
            if (response.success && response.data) {
                const $branchSelects = $('.branch-select');
                $branchSelects.each(function() {
                    const $select = $(this);
                    $select.append('<option value="">جميع الفروع</option>');
                    response.data.forEach(function(branch) {
                        $select.append(`<option value="${branch.id}">${branch.name}</option>`);
                    });
                });
            }
        }).catch(function(error) {
            console.error('Error loading branches:', error);
        });
    }

    /**
     * Populate categories selects for different report types
     */
    function populateCategories() {
        // Populate service categories
        AjaxHandler.get('services/categories', {}, {
            dataType: 'json'
        }).then(function(response) {
            if (response.success && response.data) {
                const $serviceCategories = $('#services-category-select');
                $serviceCategories.append('<option value="">جميع الفئات</option>');
                response.data.forEach(function(category) {
                    $serviceCategories.append(`<option value="${category.id}">${category.name}</option>`);
                });
            }
        }).catch(function(error) {
            console.error('Error loading service categories:', error);
        });

        // Populate product categories
        AjaxHandler.get('products/categories', {}, {
            dataType: 'json'
        }).then(function(response) {
            if (response.success && response.data) {
                const $productCategories = $('#products-category-select');
                $productCategories.append('<option value="">جميع الفئات</option>');
                response.data.forEach(function(category) {
                    $productCategories.append(`<option value="${category.id}">${category.name}</option>`);
                });
            }
        }).catch(function(error) {
            console.error('Error loading product categories:', error);
        });

        // Populate expense categories
        AjaxHandler.get('expenses/categories', {}, {
            dataType: 'json'
        }).then(function(response) {
            if (response.success && response.data) {
                const $expenseCategories = $('#expenses-category-select');
                $expenseCategories.append('<option value="">جميع الفئات</option>');
                response.data.forEach(function(category) {
                    $expenseCategories.append(`<option value="${category.id}">${category.name}</option>`);
                });
            }
        }).catch(function(error) {
            console.error('Error loading expense categories:', error);
        });
    }

    /**
     * Generate Sales Report
     */
    function generateSalesReport() {
        const formData = {
            action: 'report',
            branch_id: $('#sales-branch-select').val(),
            start_date: $('#sales-date-range').data('daterangepicker').startDate.format('YYYY-MM-DD'),
            end_date: $('#sales-date-range').data('daterangepicker').endDate.format('YYYY-MM-DD'),
            payment_method: $('#sales-payment-method').val()
        };

        AjaxHandler.get('reports/sales', formData)
            .then(renderSalesReport)
            .catch(handleReportError);
    }

    /**
     * Render Sales Report
     * @param {Object} response - Sales report data
     */
    function renderSalesReport(response) {
        if (!response.success) {
            AjaxHandler.showAlert(response.message || 'حدث خطأ في إنشاء التقرير', 'danger');
            return;
        }

        const reportData = response.data;
        const $reportContainer = $('#sales-report-results');
        $reportContainer.empty();

        // Create summary section
        const summaryHtml = `
            <div class="report-summary">
                <h3>ملخص المبيعات</h3>
                <div class="row">
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-body">
                                <h5 class="card-title">إجمالي المبيعات</h5>
                                <p class="card-text">${reportData.total_sales.toFixed(2)} ${CURRENCY_SYMBOL}</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-body">
                                <h5 class="card-title">عدد الفواتير</h5>
                                <p class="card-text">${reportData.total_invoices}</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-body">
                                <h5 class="card-title">متوسط قيمة الفاتورة</h5>
                                <p class="card-text">${reportData.average_invoice_value.toFixed(2)} ${CURRENCY_SYMBOL}</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
        $reportContainer.append(summaryHtml);

        // Render detailed sales table
        const tableHtml = `
            <div class="report-details mt-4">
                <h3>تفاصيل المبيعات</h3>
                <table class="table table-striped table-bordered">
                    <thead>
                        <tr>
                            <th>التاريخ</th>
                            <th>عدد الفواتير</th>
                            <th>إجمالي المبيعات</th>
                            <th>طريقة الدفع</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${reportData.details.map(detail => `
                            <tr>
                                <td>${detail.date}</td>
                                <td>${detail.invoice_count}</td>
                                <td>${detail.total_sales.toFixed(2)} ${CURRENCY_SYMBOL}</td>
                                <td>${detail.payment_method}</td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            </div>
        `;
        $reportContainer.append(tableHtml);

        // Optionally render chart if chart library is available
        renderSalesChart(reportData);
    }

    /**
     * Render Sales Chart (using Chart.js if available)
     * @param {Object} reportData - Sales report data
     */
    function renderSalesChart(reportData) {
        if (typeof Chart === 'undefined') return;

        const $chartContainer = $('<canvas id="sales-chart" class="mt-4"></canvas>');
        $('#sales-report-results').append($chartContainer);

        new Chart($chartContainer, {
            type: 'line',
            data: {
                labels: reportData.details.map(d => d.date),
                datasets: [{
                    label: 'المبيعات اليومية',
                    data: reportData.details.map(d => d.total_sales),
                    borderColor: 'rgb(75, 192, 192)',
                    tension: 0.1
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    title: {
                        display: true,
                        text: 'تقرير المبيعات'
                    }
                }
            }
        });
    }

    /**
     * Generate Services Report
     */
    function generateServicesReport() {
        const formData = {
            action: 'report',
            branch_id: $('#services-branch-select').val(),
            start_date: $('#services-date-range').data('daterangepicker').startDate.format('YYYY-MM-DD'),
            end_date: $('#services-date-range').data('daterangepicker').endDate.format('YYYY-MM-DD'),
            category_id: $('#services-category-select').val()
        };

        AjaxHandler.get('reports/services', formData)
            .then(renderServicesReport)
            .catch(handleReportError);
    }

    /**
     * Render Services Report
     * @param {Object} response - Services report data
     */
    function renderServicesReport(response) {
        if (!response.success) {
            AjaxHandler.showAlert(response.message || 'حدث خطأ في إنشاء التقرير', 'danger');
            return;
        }

        const reportData = response.data;
        const $reportContainer = $('#services-report-results');
        $reportContainer.empty();

        // Create summary section
        const summaryHtml = `
            <div class="report-summary">
                <h3>ملخص الخدمات</h3>
                <div class="row">
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-body">
                                <h5 class="card-title">إجمالي الخدمات</h5>
                                <p class="card-text">${reportData.total_services}</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-body">
                                <h5 class="card-title">إجمالي الإيرادات</h5>
                                <p class="card-text">${reportData.total_revenue.toFixed(2)} ${CURRENCY_SYMBOL}</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-body">
                                <h5 class="card-title">متوسط سعر الخدمة</h5>
                                <p class="card-text">${reportData.average_service_price.toFixed(2)} ${CURRENCY_SYMBOL}</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
        $reportContainer.append(summaryHtml);

        // Render detailed services table
        const tableHtml = `
            <div class="report-details mt-4">
                <h3>تفاصيل الخدمات</h3>
                <table class="table table-striped table-bordered">
                    <thead>
                        <tr>
                            <th>الخدمة</th>
                            <th>الفئة</th>
                            <th>عدد المرات</th>
                            <th>الإيرادات</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${reportData.details.map(service => `
                            <tr>
                                <td>${service.name}</td>
                                <td>${service.category_name || 'غير مصنف'}</td>
                                <td>${service.count}</td>
                                <td>${service.total_revenue.toFixed(2)} ${CURRENCY_SYMBOL}</td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            </div>
        `;
        $reportContainer.append(tableHtml);

        // Render services chart
        renderServicesChart(reportData);
    }

    /**
     * Render Services Chart
     * @param {Object} reportData - Services report data
     */
    function renderServicesChart(reportData) {
        if (typeof Chart === 'undefined') return;

        const $chartContainer = $('<canvas id="services-chart" class="mt-4"></canvas>');
        $('#services-report-results').append($chartContainer);

        new Chart($chartContainer, {
            type: 'bar',
            data: {
                labels: reportData.details.map(d => d.name),
                datasets: [{
                    label: 'عدد الخدمات',
                    data: reportData.details.map(d => d.count),
                    backgroundColor: 'rgba(54, 162, 235, 0.5)'
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    title: {
                        display: true,
                        text: 'تقرير الخدمات'
                    }
                }
            }
        });
    }
/**
     * Generate Employees Report
     */
function generateEmployeesReport() {
    const formData = {
        action: 'report',
        branch_id: $('#employees-branch-select').val(),
        start_date: $('#employees-date-range').data('daterangepicker').startDate.format('YYYY-MM-DD'),
        end_date: $('#employees-date-range').data('daterangepicker').endDate.format('YYYY-MM-DD')
    };

    AjaxHandler.get('reports/employees', formData)
        .then(renderEmployeesReport)
        .catch(handleReportError);
}

/**
 * Render Employees Report
 * @param {Object} response - Employees report data
 */
function renderEmployeesReport(response) {
    if (!response.success) {
        AjaxHandler.showAlert(response.message || 'حدث خطأ في إنشاء التقرير', 'danger');
        return;
    }

    const reportData = response.data;
    const $reportContainer = $('#employees-report-results');
    $reportContainer.empty();

    // Create summary section
    const summaryHtml = `
        <div class="report-summary">
            <h3>ملخص الموظفين</h3>
            <div class="row">
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title">عدد الموظفين</h5>
                            <p class="card-text">${reportData.total_employees}</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title">إجمالي العمولات</h5>
                            <p class="card-text">${reportData.total_commissions.toFixed(2)} ${CURRENCY_SYMBOL}</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title">متوسط العمولة</h5>
                            <p class="card-text">${reportData.average_commission.toFixed(2)} ${CURRENCY_SYMBOL}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;
    $reportContainer.append(summaryHtml);

    // Render detailed employees table
    const tableHtml = `
        <div class="report-details mt-4">
            <h3>تفاصيل الموظفين</h3>
            <table class="table table-striped table-bordered">
                <thead>
                    <tr>
                        <th>اسم الموظف</th>
                        <th>الوظيفة</th>
                        <th>عدد الخدمات</th>
                        <th>إجمالي العمولات</th>
                        <th>متوسط سعر الخدمة</th>
                    </tr>
                </thead>
                <tbody>
                    ${reportData.details.map(employee => `
                        <tr>
                            <td>${employee.name}</td>
                            <td>${employee.position || 'غير محدد'}</td>
                            <td>${employee.service_count}</td>
                            <td>${employee.total_commission.toFixed(2)} ${CURRENCY_SYMBOL}</td>
                            <td>${employee.average_service_price.toFixed(2)} ${CURRENCY_SYMBOL}</td>
                        </tr>
                    `).join('')}
                </tbody>
            </table>
        </div>
    `;
    $reportContainer.append(tableHtml);

    // Render employees chart
    renderEmployeesChart(reportData);
}

/**
 * Render Employees Chart
 * @param {Object} reportData - Employees report data
 */
function renderEmployeesChart(reportData) {
    if (typeof Chart === 'undefined') return;

    const $chartContainer = $('<canvas id="employees-chart" class="mt-4"></canvas>');
    $('#employees-report-results').append($chartContainer);

    new Chart($chartContainer, {
        type: 'bar',
        data: {
            labels: reportData.details.map(d => d.name),
            datasets: [{
                label: 'العمولات',
                data: reportData.details.map(d => d.total_commission),
                backgroundColor: 'rgba(255, 99, 132, 0.5)'
            }]
        },
        options: {
            responsive: true,
            plugins: {
                title: {
                    display: true,
                    text: 'تقرير الموظفين'
                }
            }
        }
    });
}

/**
 * Generate Products Report
 */
function generateProductsReport() {
    const formData = {
        action: 'report',
        branch_id: $('#products-branch-select').val(),
        category_id: $('#products-category-select').val(),
        start_date: $('#products-date-range').data('daterangepicker').startDate.format('YYYY-MM-DD'),
        end_date: $('#products-date-range').data('daterangepicker').endDate.format('YYYY-MM-DD')
    };

    AjaxHandler.get('reports/products', formData)
        .then(renderProductsReport)
        .catch(handleReportError);
}

/**
 * Render Products Report
 * @param {Object} response - Products report data
 */
function renderProductsReport(response) {
    if (!response.success) {
        AjaxHandler.showAlert(response.message || 'حدث خطأ في إنشاء التقرير', 'danger');
        return;
    }

    const reportData = response.data;
    const $reportContainer = $('#products-report-results');
    $reportContainer.empty();

    // Create summary section
    const summaryHtml = `
        <div class="report-summary">
            <h3>ملخص المنتجات</h3>
            <div class="row">
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title">إجمالي المنتجات المباعة</h5>
                            <p class="card-text">${reportData.total_products_sold}</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title">إجمالي الإيرادات</h5>
                            <p class="card-text">${reportData.total_revenue.toFixed(2)} ${CURRENCY_SYMBOL}</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title">متوسط سعر المنتج</h5>
                            <p class="card-text">${reportData.average_product_price.toFixed(2)} ${CURRENCY_SYMBOL}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;
    $reportContainer.append(summaryHtml);

    // Render detailed products table
    const tableHtml = `
        <div class="report-details mt-4">
            <h3>تفاصيل المنتجات</h3>
            <table class="table table-striped table-bordered">
                <thead>
                    <tr>
                        <th>المنتج</th>
                        <th>الفئة</th>
                        <th>الكمية المباعة</th>
                        <th>الإيرادات</th>
                        <th>صافي الربح</th>
                    </tr>
                </thead>
                <tbody>
                    ${reportData.details.map(product => `
                        <tr>
                            <td>${product.name}</td>
                            <td>${product.category_name || 'غير مصنف'}</td>
                            <td>${product.quantity_sold}</td>
                            <td>${product.total_revenue.toFixed(2)} ر.س</td>
                            <td>${product.net_profit.toFixed(2)} ر.س</td>
                        </tr>
                    `).join('')}
                </tbody>
            </table>
        </div>
    `;
    $reportContainer.append(tableHtml);

    // Render products chart
    renderProductsChart(reportData);
}

/**
 * Render Products Chart
 * @param {Object} reportData - Products report data
 */
function renderProductsChart(reportData) {
    if (typeof Chart === 'undefined') return;

    const $chartContainer = $('<canvas id="products-chart" class="mt-4"></canvas>');
    $('#products-report-results').append($chartContainer);

    new Chart($chartContainer, {
        type: 'pie',
        data: {
            labels: reportData.details.map(d => d.name),
            datasets: [{
                label: 'مبيعات المنتجات',
                data: reportData.details.map(d => d.quantity_sold),
                backgroundColor: [
                    'rgba(255, 99, 132, 0.5)',
                    'rgba(54, 162, 235, 0.5)',
                    'rgba(255, 206, 86, 0.5)',
                    'rgba(75, 192, 192, 0.5)',
                    'rgba(153, 102, 255, 0.5)'
                ]
            }]
        },
        options: {
            responsive: true,
            plugins: {
                title: {
                    display: true,
                    text: 'تقرير المنتجات'
                }
            }
        }
    });
}

/**
 * Generate Expenses Report
 */
function generateExpensesReport() {
    const formData = {
        action: 'report',
        branch_id: $('#expenses-branch-select').val(),
        category_id: $('#expenses-category-select').val(),
        start_date: $('#expenses-date-range').data('daterangepicker').startDate.format('YYYY-MM-DD'),
        end_date: $('#expenses-date-range').data('daterangepicker').endDate.format('YYYY-MM-DD')
    };

    AjaxHandler.get('reports/expenses', formData)
        .then(renderExpensesReport)
        .catch(handleReportError);
}

/**
 * Render Expenses Report
 * @param {Object} response - Expenses report data
 */
function renderExpensesReport(response) {
    if (!response.success) {
        AjaxHandler.showAlert(response.message || 'حدث خطأ في إنشاء التقرير', 'danger');
        return;
    }

    const reportData = response.data;
    const $reportContainer = $('#expenses-report-results');
    $reportContainer.empty();

    // Create summary section
    const summaryHtml = `
        <div class="report-summary">
            <h3>ملخص المصروفات</h3>
            <div class="row">
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title">إجمالي المصروفات</h5>
                            <p class="card-text">${reportData.total_expenses.toFixed(2)} ${CURRENCY_SYMBOL}</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title">عدد المصروفات</h5>
                            <p class="card-text">${reportData.total_expense_count}</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title">متوسط المصروفات</h5>
                            <p class="card-text">${reportData.average_expense.toFixed(2)} ${CURRENCY_SYMBOL}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;
    $reportContainer.append(summaryHtml);

    // Render detailed expenses table
    const tableHtml = `
        <div class="report-details mt-4">
            <h3>تفاصيل المصروفات</h3>
            <table class="table table-striped table-bordered">
                <thead>
                    <tr>
                        <th>الفئة</th>
                        <th>عدد المصروفات</th>
                        <th>المبلغ الإجمالي</th>
                        <th>المتوسط</th>
                    </tr>
                </thead>
                <tbody>
                    ${reportData.details.map(category => `
                        <tr>
                            <td>${category.name || 'غير مصنف'}</td>
                            <td>${category.count}</td>
                            <td>${category.total_amount.toFixed(2)} ${CURRENCY_SYMBOL}</td>
                            <td>${category.average_amount.toFixed(2)} ${CURRENCY_SYMBOL}</td>
                        </tr>
                    `).join('')}
                </tbody>
            </table>
        </div>
    `;
    $reportContainer.append(tableHtml);

    // Render expenses chart
    renderExpensesChart(reportData);
}

/**
 * Render Expenses Chart
 * @param {Object} reportData - Expenses report data
 */
function renderExpensesChart(reportData) {
    if (typeof Chart === 'undefined') return;

    const $chartContainer = $('<canvas id="expenses-chart" class="mt-4"></canvas>');
    $('#expenses-report-results').append($chartContainer);

    new Chart($chartContainer, {
        type: 'doughnut',
        data: {
            labels: reportData.details.map(d => d.name || 'غير مصنف'),
            datasets: [{
                label: 'المصروفات حسب الفئة',
                data: reportData.details.map(d => d.total_amount),
                backgroundColor: [
                    'rgba(255, 99, 132, 0.5)',
                    'rgba(54, 162, 235, 0.5)',
                    'rgba(255, 206, 86, 0.5)',
        'rgba(75, 192, 192, 0.5)',
        'rgba(153, 102, 255, 0.5)'
    ]
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    title: {
                        display: true,
                        text: 'تقرير المصروفات'
                    }
                }
            }
        });
    }

    /**
     * Export Report to CSV
     * @param {string} reportType - Type of report to export
     */
    function exportReportToCSV(reportType) {
        let $container;
        let filename;

        switch(reportType) {
            case 'sales':
                $container = $('#sales-report-results');
                filename = `sales_report_${new Date().toISOString().split('T')[0]}.csv`;
                break;
            case 'services':
                $container = $('#services-report-results');
                filename = `services_report_${new Date().toISOString().split('T')[0]}.csv`;
                break;
            case 'employees':
                $container = $('#employees-report-results');
                filename = `employees_report_${new Date().toISOString().split('T')[0]}.csv`;
                break;
            case 'products':
                $container = $('#products-report-results');
                filename = `products_report_${new Date().toISOString().split('T')[0]}.csv`;
                break;
            case 'expenses':
                $container = $('#expenses-report-results');
                filename = `expenses_report_${new Date().toISOString().split('T')[0]}.csv`;
                break;
            default:
                AjaxHandler.showAlert('نوع التقرير غير صالح', 'danger');
                return;
        }

        // Find the table in the report results
        const $table = $container.find('table');

        if ($table.length === 0) {
            AjaxHandler.showAlert('لا يوجد بيانات للتصدير', 'warning');
            return;
        }

        // Convert table to CSV
        const csv = [];
        const $headers = $table.find('thead tr th');
        const headerRow = [];

        $headers.each(function() {
            headerRow.push($(this).text());
        });
        csv.push(headerRow.join(','));

        // Add table rows
        const $rows = $table.find('tbody tr');
        $rows.each(function() {
            const row = [];
            $(this).find('td').each(function() {
                // Remove commas and escape special characters
                let cellText = $(this).text().trim().replace(/,/g, '').replace(/\n/g, ' ');
                row.push(cellText);
            });
            csv.push(row.join(','));
        });

        // Create and download CSV file
        const csvContent = csv.join('\n');
        const blob = new Blob(['\uFEFF' + csvContent], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');

        if (navigator.msSaveBlob) { // For IE 10+
            navigator.msSaveBlob(blob, filename);
        } else {
            link.href = URL.createObjectURL(blob);
            link.download = filename;
            link.style.display = 'none';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }
    }

    /**
     * Handle report generation errors
     * @param {Object} error - Error object
     */
    function handleReportError(error) {
        console.error('Report Generation Error:', error);
        AjaxHandler.showAlert('حدث خطأ أثناء إنشاء التقرير. يرجى المحاولة مرة أخرى.', 'danger');
    }

    /**
     * Print current report
     * @param {string} reportType - Type of report to print
     */
    function printReport(reportType) {
        let $container;

        switch(reportType) {
            case 'sales':
                $container = $('#sales-report-results');
                break;
            case 'services':
                $container = $('#services-report-results');
                break;
            case 'employees':
                $container = $('#employees-report-results');
                break;
            case 'products':
                $container = $('#products-report-results');
                break;
            case 'expenses':
                $container = $('#expenses-report-results');
                break;
            default:
                AjaxHandler.showAlert('نوع التقرير غير صالح', 'danger');
                return;
        }

        // Check if report exists
        if ($container.children().length === 0) {
            AjaxHandler.showAlert('لا يوجد تقرير للطباعة', 'warning');
            return;
        }

        // Create a new window for printing
        const printWindow = window.open('', '_blank', 'width=800,height=600');

        // Basic print styling
        printWindow.document.write(`
            <html>
                <head>
                    <title>تقرير ${getReportTitle(reportType)}</title>
                    <style>
                        body { font-family: Arial, sans-serif; direction: rtl; }
                        .report-summary, .report-details { margin-bottom: 20px; }
                        .card { border: 1px solid #ddd; margin-bottom: 10px; padding: 10px; }
                        .card-title { margin-bottom: 5px; font-weight: bold; }
                        table { width: 100%; border-collapse: collapse; margin-top: 20px; }
                        table, th, td { border: 1px solid #ddd; }
                        th, td { padding: 8px; text-align: right; }
                        th { background-color: #f2f2f2; }
                    </style>
                </head>
                <body>
                    <div class="report-header">
                        <h1>تقرير ${getReportTitle(reportType)}</h1>
                        <p>تاريخ التقرير: ${new Date().toLocaleDateString()}</p>
                    </div>
                    ${$container.html()}
                </body>
            </html>
        `);

        printWindow.document.close();
        printWindow.print();
    }

    /**
     * Get report title based on report type
     * @param {string} reportType - Type of report
     * @returns {string} Report title
     */
    function getReportTitle(reportType) {
        switch(reportType) {
            case 'sales': return 'المبيعات';
            case 'services': return 'الخدمات';
            case 'employees': return 'الموظفين';
            case 'products': return 'المنتجات';
            case 'expenses': return 'المصروفات';
            default: return 'تقرير';
        }
    }

    // Event listeners for export and print buttons
    $('.export-report-btn').on('click', function() {
        const reportType = $(this).data('report-type');
        exportReportToCSV(reportType);
    });

    $('.print-report-btn').on('click', function() {
        const reportType = $(this).data('report-type');
        printReport(reportType);
    });
});