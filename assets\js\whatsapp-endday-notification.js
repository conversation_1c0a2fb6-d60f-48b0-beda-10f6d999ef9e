/**
 * WhatsApp End Day Notification
 * Este script maneja el envío de notificaciones de WhatsApp para aperturas y cierres de día
 * desde el lado del cliente, comunicándose directamente con el servidor local
 * de WhatsApp en localhost:3000
 */

const WhatsAppEndDayNotification = {
    // URL del servidor local de WhatsApp
    serverUrl: 'http://localhost:3000',

    // URL base de la API del sistema
    apiUrl: BASE_URL + 'api/',

    // Estado de la última operación
    lastOperationStatus: null,

    // أرقام هواتف المدراء المخزنة مؤقتًا
    adminPhones: null,

    // Opciones de configuración
    options: {
        debug: true,                // Habilitar mensajes de depuración
        maxRetries: 2,              // Número máximo de reintentos
        retryDelay: 2000,           // Tiempo entre reintentos (ms)
        timeout: 60000,             // Tiempo de espera para solicitudes (ms) - 60 segundos
        showSuccessMessage: false   // Mostrar mensaje de éxito al usuario
    },

    /**
     * Inicializar el módulo
     * @param {Object} options Opciones de configuración
     */
    init: function(options) {
        // Combinar opciones proporcionadas con las predeterminadas
        if (options) {
            this.options = { ...this.options, ...options };
        }

        // Forzar el modo de depuración para diagnosticar problemas
        this.options.debug = true;

        console.log('WhatsAppEndDayNotification inicializado con opciones:', this.options);
        console.log('URL del servidor local de WhatsApp:', this.serverUrl);
        console.log('URL base de la API del sistema:', this.apiUrl);

        // جلب أرقام هواتف المدراء عند التهيئة
        this.checkAdminPhones();
    },

    /**
     * التحقق من وجود أرقام هواتف للمدراء
     * @returns {Promise<boolean>} وعد يعيد true إذا كانت هناك أرقام هواتف متاحة
     */
    checkAdminPhones: function() {
        return new Promise((resolve, reject) => {
            // إذا كانت الأرقام مخزنة مسبقًا، نعيدها مباشرة
            if (this.adminPhones !== null) {
                console.log('استخدام الأرقام المخزنة مسبقًا:', this.adminPhones);
                resolve(this.adminPhones.length > 0);
                return;
            }

            // للتجربة: إضافة أرقام افتراضية للمدراء إذا فشل الاتصال بالخادم
            const useHardcodedPhones = true; // ضع هذا على false لإيقاف استخدام الأرقام الافتراضية

            // استعلام عن أرقام هواتف المدراء من الخادم
            console.log('جاري الاستعلام عن أرقام هواتف المدراء من الخادم...');
            const xhr = new XMLHttpRequest();
            xhr.open('GET', this.apiUrl + 'endday_notification.php?action=check_admin_phones', true);
            xhr.responseType = 'json';

            xhr.onload = () => {
                if (xhr.status === 200) {
                    const response = xhr.response;
                    console.log('استجابة الخادم:', response);

                    if (response && response.status === 'success') {
                        this.adminPhones = response.data.admin_phones || [];

                        if (this.options.debug) {
                            console.log('تم الحصول على أرقام هواتف المدراء:', this.adminPhones);
                        }

                        // إذا لم يتم العثور على أرقام وتم تمكين الأرقام الافتراضية
                        if (this.adminPhones.length === 0 && useHardcodedPhones) {
                            console.log('لم يتم العثور على أرقام هواتف المدراء، استخدام الأرقام الافتراضية');
                            this.adminPhones = [
                                { phone: '201555262660', name: 'المدير (افتراضي)' }
                            ];
                        }

                        resolve(this.adminPhones.length > 0);
                    } else {
                        console.warn('فشل الحصول على أرقام هواتف المدراء من الخادم');

                        // استخدام الأرقام الافتراضية إذا تم تمكينها
                        if (useHardcodedPhones) {
                            console.log('استخدام الأرقام الافتراضية للمدراء');
                            this.adminPhones = [
                                { phone: '201555262660', name: 'المدير (افتراضي)' }
                            ];
                            resolve(true);
                        } else {
                            this.adminPhones = [];
                            const errorMsg = response ? response.message : 'خطأ غير معروف في الحصول على أرقام هواتف المدراء';
                            console.warn(errorMsg);
                            resolve(false);
                        }
                    }
                } else {
                    console.warn(`خطأ في الحصول على أرقام هواتف المدراء: ${xhr.status}`);

                    // استخدام الأرقام الافتراضية إذا تم تمكينها
                    if (useHardcodedPhones) {
                        console.log('استخدام الأرقام الافتراضية للمدراء بسبب خطأ الخادم');
                        this.adminPhones = [
                            { phone: '201555262660', name: 'المدير (افتراضي)' }
                        ];
                        resolve(true);
                    } else {
                        this.adminPhones = [];
                        resolve(false);
                    }
                }
            };

            xhr.onerror = () => {
                console.warn('خطأ في الشبكة أثناء الحصول على أرقام هواتف المدراء');

                // استخدام الأرقام الافتراضية إذا تم تمكينها
                if (useHardcodedPhones) {
                    console.log('استخدام الأرقام الافتراضية للمدراء بسبب خطأ الشبكة');
                    this.adminPhones = [
                        { phone: '201555262660', name: 'المدير (افتراضي)' }
                    ];
                    resolve(true);
                } else {
                    this.adminPhones = [];
                    resolve(false);
                }
            };

            xhr.send();
        });
    },

    /**
     * Enviar notificación de apertura de día de trabajo
     * @param {number} endDayId ID del día de trabajo
     * @returns {Promise<Object>} Resultado de la operación
     */
    sendOpenDayNotification: function(endDayId) {
        return new Promise((resolve, reject) => {
            if (this.options.debug) {
                console.log(`Preparando notificación de WhatsApp para apertura de día #${endDayId}`);
            }

            // التحقق من وجود أرقام هواتف للمدراء قبل محاولة الإرسال
            this.checkAdminPhones()
                .then(hasAdminPhones => {
                    if (!hasAdminPhones) {
                        throw new Error('لا توجد أرقام هواتف متاحة للمدراء. لا يمكن إرسال الإشعارات.');
                    }

                    // Paso 1: Obtener los datos del día de trabajo desde la API
                    return this._getEndDayData(endDayId, 'open');
                })
                .then(endDayData => {
                    // التحقق من وجود مدراء في البيانات المسترجعة
                    if (!endDayData.admins || endDayData.admins.length === 0) {
                        throw new Error('لا توجد أرقام هواتف متاحة للمدراء في البيانات المسترجعة.');
                    }

                    // Paso 2: Enviar mensajes a todos los administradores
                    const sendPromises = endDayData.admins.map(admin => {
                        return this._sendWhatsAppMessage(
                            admin.phone,
                            endDayData.message,
                            endDayId
                        );
                    });

                    return Promise.all(sendPromises);
                })
                .then(results => {
                    // Paso 3: Registrar el resultado en el sistema
                    return this._logNotificationResult(endDayId, true, { type: 'open_day', results });
                })
                .then(logResult => {
                    if (this.options.debug) {
                        console.log(`Notificaciones de apertura de día enviadas y registradas para día #${endDayId}`, logResult);
                    }

                    // Mostrar mensaje de éxito si está habilitado
                    if (this.options.showSuccessMessage) {
                        this._showSuccessMessage('تم إرسال إشعارات فتح يوم العمل بنجاح');
                    }

                    this.lastOperationStatus = 'success';
                    resolve({ status: 'success', endDayId: endDayId });
                })
                .catch(error => {
                    console.error(`Error al enviar notificaciones de apertura de día #${endDayId}:`, error);

                    // Verificar si es un error de timeout
                    if (error.message && error.message.includes('Tiempo de espera agotado')) {
                        console.warn('Error de timeout - Los mensajes podrían haberse enviado correctamente');

                        // Registrar como advertencia en lugar de error
                        this._logNotificationResult(endDayId, true, {
                            type: 'open_day',
                            status: 'warning',
                            message: 'Tiempo de espera agotado, pero los mensajes podrían haberse enviado correctamente'
                        });

                        this.lastOperationStatus = 'warning';
                        resolve({
                            status: 'warning',
                            message: 'Tiempo de espera agotado, pero los mensajes podrían haberse enviado correctamente',
                            endDayId: endDayId
                        });
                    } else {
                        // Registrar el error
                        this._logNotificationResult(endDayId, false, { type: 'open_day', error: error.message });
                        this.lastOperationStatus = 'error';
                        reject(error);
                    }
                });
        });
    },

    /**
     * Enviar notificación de cierre de día de trabajo
     * @param {number} endDayId ID del día de trabajo
     * @returns {Promise<Object>} Resultado de la operación
     */
    sendCloseDayNotification: function(endDayId) {
        return new Promise((resolve, reject) => {
            if (this.options.debug) {
                console.log(`Preparando notificación de WhatsApp para cierre de día #${endDayId}`);
            }

            // التحقق من وجود أرقام هواتف للمدراء قبل محاولة الإرسال
            this.checkAdminPhones()
                .then(hasAdminPhones => {
                    if (!hasAdminPhones) {
                        throw new Error('لا توجد أرقام هواتف متاحة للمدراء. لا يمكن إرسال الإشعارات.');
                    }

                    // Paso 1: Obtener los datos del día de trabajo desde la API
                    return this._getEndDayData(endDayId, 'close');
                })
                .then(endDayData => {
                    // التحقق من وجود مدراء في البيانات المسترجعة
                    if (!endDayData.admins || endDayData.admins.length === 0) {
                        throw new Error('لا توجد أرقام هواتف متاحة للمدراء في البيانات المسترجعة.');
                    }

                    // Paso 2: Enviar mensajes a todos los administradores
                    const sendPromises = endDayData.admins.map(admin => {
                        return this._sendWhatsAppMessage(
                            admin.phone,
                            endDayData.message,
                            endDayId
                        );
                    });

                    return Promise.all(sendPromises);
                })
                .then(results => {
                    // Paso 3: Registrar el resultado en el sistema
                    return this._logNotificationResult(endDayId, true, { type: 'close_day', results });
                })
                .then(logResult => {
                    if (this.options.debug) {
                        console.log(`Notificaciones de cierre de día enviadas y registradas para día #${endDayId}`, logResult);
                    }

                    // Mostrar mensaje de éxito si está habilitado
                    if (this.options.showSuccessMessage) {
                        this._showSuccessMessage('تم إرسال إشعارات إغلاق يوم العمل بنجاح');
                    }

                    this.lastOperationStatus = 'success';
                    resolve({ status: 'success', endDayId: endDayId });
                })
                .catch(error => {
                    console.error(`Error al enviar notificaciones de cierre de día #${endDayId}:`, error);

                    // Verificar si es un error de timeout
                    if (error.message && error.message.includes('Tiempo de espera agotado')) {
                        console.warn('Error de timeout - Los mensajes podrían haberse enviado correctamente');

                        // Registrar como advertencia en lugar de error
                        this._logNotificationResult(endDayId, true, {
                            type: 'close_day',
                            status: 'warning',
                            message: 'Tiempo de espera agotado, pero los mensajes podrían haberse enviado correctamente'
                        });

                        this.lastOperationStatus = 'warning';
                        resolve({
                            status: 'warning',
                            message: 'Tiempo de espera agotado, pero los mensajes podrían haberse enviado correctamente',
                            endDayId: endDayId
                        });
                    } else {
                        // Registrar el error
                        this._logNotificationResult(endDayId, false, { type: 'close_day', error: error.message });
                        this.lastOperationStatus = 'error';
                        reject(error);
                    }
                });
        });
    },

    /**
     * Obtener los datos del día de trabajo desde la API
     * @private
     * @param {number} endDayId ID del día de trabajo
     * @param {string} type Tipo de notificación ('open' o 'close')
     * @returns {Promise<Object>} Datos del día de trabajo
     */
    _getEndDayData: function(endDayId, type) {
        return new Promise((resolve, reject) => {
            console.log(`Obteniendo datos del día de trabajo #${endDayId} para notificación de tipo '${type}'`);

            const xhr = new XMLHttpRequest();
            const url = this.apiUrl + 'endday_notification.php?action=get_endday_data';
            console.log('URL de la solicitud:', url);

            xhr.open('POST', url, true);
            xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');
            xhr.responseType = 'json';

            xhr.onload = () => {
                console.log(`Respuesta recibida para día #${endDayId}, código de estado:`, xhr.status);

                if (xhr.status === 200) {
                    const response = xhr.response;
                    console.log('Respuesta completa:', response);

                    if (response && response.status === 'success') {
                        console.log(`Datos de día de trabajo (${type}) obtenidos correctamente:`, response.data);

                        // Verificar si hay números de teléfono en la respuesta
                        if (response.data && response.data.admins) {
                            console.log(`Se encontraron ${response.data.admins.length} números de teléfono para notificación:`,
                                response.data.admins);
                        } else {
                            console.warn('No se encontraron números de teléfono en la respuesta');
                        }

                        resolve(response.data);
                    } else {
                        const errorMsg = response ? response.message : `Error desconocido al obtener datos de día de trabajo (${type})`;
                        console.error('Error en la respuesta:', errorMsg);
                        reject(new Error(errorMsg));
                    }
                } else {
                    const errorMsg = `Error al obtener datos de día de trabajo (${type}): ${xhr.status}`;
                    console.error(errorMsg);
                    reject(new Error(errorMsg));
                }
            };

            xhr.onerror = (e) => {
                console.error(`Error de red al obtener datos de día de trabajo (${type}):`, e);
                reject(new Error(`Error de red al obtener datos de día de trabajo (${type})`));
            };

            // Enviar la solicitud
            const formData = new URLSearchParams();
            formData.append('end_day_id', endDayId);
            formData.append('notification_type', type);

            const requestData = formData.toString();
            console.log('Datos de la solicitud:', requestData);

            xhr.send(requestData);
        });
    },

    /**
     * Enviar mensaje de WhatsApp a través del servidor local
     * @private
     * @param {string} phone Número de teléfono del destinatario
     * @param {string} message Mensaje a enviar
     * @param {number} endDayId ID del día de trabajo (para referencia)
     * @returns {Promise<Object>} Resultado del envío
     */
    _sendWhatsAppMessage: function(phone, message, endDayId) {
        return new Promise((resolve, reject) => {
            try {
                console.log(`=== INICIO ENVÍO DE MENSAJE ===`);
                console.log(`Enviando mensaje de WhatsApp a ${phone} para día de trabajo #${endDayId}`);
                console.log(`Mensaje a enviar:`, message);

                // Formatear el número de teléfono correctamente para WhatsApp
                const formattedPhone = this._formatPhoneNumber(phone);
                console.log(`Número original: ${phone}`);
                console.log(`Número formateado: ${formattedPhone}`);

                // Verificar que el servidor local esté configurado correctamente
                if (!this.serverUrl || !this.serverUrl.startsWith('http')) {
                    console.error(`URL del servidor local inválida: ${this.serverUrl}`);
                    throw new Error(`URL del servidor local inválida: ${this.serverUrl}`);
                }

                // Realizar una solicitud al servidor local para enviar el mensaje
                const xhr = new XMLHttpRequest();
                const url = `${this.serverUrl}/send`;
                console.log(`URL de envío de mensaje: ${url}`);

                xhr.open('POST', url, true);
                xhr.setRequestHeader('Content-Type', 'application/json');
                xhr.responseType = 'json';
                xhr.timeout = this.options.timeout; // Usar el tiempo de espera configurado en las opciones
                console.log(`Tiempo de espera configurado: ${this.options.timeout}ms`);

                xhr.onload = () => {
                    console.log(`Respuesta recibida, código de estado: ${xhr.status}`);

                    if (xhr.status === 200) {
                        let response;
                        console.log('Respuesta completa:', xhr.response);

                        // Manejar diferentes tipos de respuestas
                        if (typeof xhr.response === 'object') {
                            response = xhr.response;
                            console.log('Respuesta interpretada como objeto JSON');
                        } else if (typeof xhr.responseText === 'string') {
                            console.log('Respuesta recibida como texto:', xhr.responseText);
                            try {
                                response = JSON.parse(xhr.responseText);
                                console.log('Texto convertido a objeto JSON:', response);
                            } catch (e) {
                                console.error('Error al analizar la respuesta JSON:', e);
                                response = { status: 'error', message: 'Respuesta no válida del servidor' };
                            }
                        } else {
                            console.warn('Tipo de respuesta desconocido:', typeof xhr.response);
                            response = { status: 'error', message: 'Respuesta desconocida del servidor' };
                        }

                        if (response && response.status === 'success') {
                            console.log(`¡Éxito! Mensaje enviado a ${formattedPhone}:`, response);
                            resolve(response);
                        } else {
                            const errorMsg = response ? response.message : 'Error desconocido al enviar mensaje';
                            console.error(`Error al enviar mensaje a ${formattedPhone}: ${errorMsg}`);
                            reject(new Error(errorMsg));
                        }
                    } else {
                        const errorMsg = `Error al enviar mensaje: ${xhr.status}`;
                        console.error(errorMsg);
                        reject(new Error(errorMsg));
                    }
                };

                xhr.ontimeout = () => {
                    const errorMsg = 'Tiempo de espera agotado al enviar mensaje';
                    console.error(errorMsg);

                    // A pesar del timeout, el mensaje podría haberse enviado correctamente
                    // Registrar como advertencia en lugar de error y resolver la promesa
                    console.warn('El mensaje podría haberse enviado a pesar del timeout');
                    resolve({
                        status: 'warning',
                        message: 'Tiempo de espera agotado, pero el mensaje podría haberse enviado correctamente'
                    });
                };

                xhr.onerror = (e) => {
                    const errorMsg = 'Error de red al enviar mensaje';
                    console.error(errorMsg, e);
                    reject(new Error(errorMsg));
                };

                // Enviar los datos
                const sendData = {
                    phone: formattedPhone,
                    message: message
                };

                const jsonData = JSON.stringify(sendData);
                console.log('Enviando datos al servidor (JSON):', jsonData);

                xhr.send(jsonData);
                console.log('Solicitud enviada, esperando respuesta...');
            } catch (error) {
                console.error('Error inesperado al enviar mensaje:', error);
                reject(error);
            }
        });
    },

    /**
     * Registrar el resultado de la notificación en el sistema
     * @private
     * @param {number} endDayId ID del día de trabajo
     * @param {boolean} success Indica si la operación fue exitosa
     * @param {Object} result Resultado de la operación
     * @returns {Promise<Object>} Resultado del registro
     */
    _logNotificationResult: function(endDayId, success, result) {
        return new Promise((resolve, reject) => {
            const xhr = new XMLHttpRequest();
            xhr.open('POST', this.apiUrl + 'endday_notification.php?action=log_notification', true);
            xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');
            xhr.responseType = 'json';

            xhr.onload = () => {
                if (xhr.status === 200) {
                    const response = xhr.response;

                    if (response && response.status === 'success') {
                        resolve(response);
                    } else {
                        // No rechazamos la promesa aquí para no interrumpir el flujo principal
                        console.warn('Error al registrar notificación:', response ? response.message : 'Error desconocido');
                        resolve({ status: 'warning', message: 'Error al registrar notificación' });
                    }
                } else {
                    console.warn(`Error al registrar notificación: ${xhr.status}`);
                    resolve({ status: 'warning', message: `Error al registrar notificación: ${xhr.status}` });
                }
            };

            xhr.onerror = () => {
                console.warn('Error de red al registrar notificación');
                resolve({ status: 'warning', message: 'Error de red al registrar notificación' });
            };

            // Enviar la solicitud
            const formData = new URLSearchParams();
            formData.append('end_day_id', endDayId);
            formData.append('success', success ? 1 : 0);
            formData.append('details', JSON.stringify(result));
            xhr.send(formData.toString());
        });
    },

    /**
     * Mostrar mensaje de éxito al usuario
     * @private
     * @param {string} message Mensaje a mostrar
     */
    _showSuccessMessage: function(message) {
        // Verificar si ya existe un mensaje
        let messageContainer = document.querySelector('.whatsapp-notification-message');

        if (!messageContainer) {
            // Crear el contenedor del mensaje
            messageContainer = document.createElement('div');
            messageContainer.className = 'whatsapp-notification-message';
            messageContainer.innerHTML = `
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="fab fa-whatsapp"></i> ${message || 'تم إرسال الإشعار بنجاح'}
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
            `;

            // Añadir estilos
            const style = document.createElement('style');
            style.textContent = `
                .whatsapp-notification-message {
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    z-index: 9999;
                    max-width: 350px;
                }

                .whatsapp-notification-message .alert {
                    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
                    border-left: 4px solid #25D366;
                }

                .whatsapp-notification-message .fa-whatsapp {
                    color: #25D366;
                    margin-right: 8px;
                }
            `;

            document.head.appendChild(style);
            document.body.appendChild(messageContainer);

            // Eliminar el mensaje después de 5 segundos
            setTimeout(() => {
                if (document.body.contains(messageContainer)) {
                    messageContainer.remove();
                }
            }, 5000);
        }
    },

    /**
     * Formatear número de teléfono para WhatsApp
     * @private
     * @param {string} phone Número de teléfono
     * @returns {string} Número de teléfono formateado
     */
    _formatPhoneNumber: function(phone) {
        if (!phone) {
            console.warn('Número de teléfono vacío');
            return '';
        }

        console.log('Formateando número de teléfono:', phone);

        // Eliminar todos los caracteres no numéricos excepto el signo +
        let cleaned = phone.replace(/[^\d+]/g, '');
        console.log('Número limpio (solo dígitos y +):', cleaned);

        // Si el número no comienza con +, agregar el código de país de Egipto (+20)
        if (!cleaned.startsWith('+')) {
            // Si comienza con 0, eliminarlo
            if (cleaned.startsWith('0')) {
                console.log('Eliminando 0 inicial');
                cleaned = cleaned.substring(1);
            }

            // Si comienza con 20, verificar si es el código de país
            if (cleaned.startsWith('20')) {
                console.log('El número ya comienza con 20 (código de Egipto)');
            } else {
                console.log('Agregando código de país +20');
                cleaned = '20' + cleaned;
            }
        } else {
            // Si comienza con +, eliminar el signo + para WhatsApp
            console.log('Eliminando signo + inicial');
            cleaned = cleaned.substring(1);
        }

        // Verificar la longitud del número
        if (cleaned.length < 10) {
            console.warn('El número formateado es demasiado corto:', cleaned);
        } else if (cleaned.length > 15) {
            console.warn('El número formateado es demasiado largo:', cleaned);
        }

        console.log('Número final formateado para WhatsApp:', cleaned);
        return cleaned;
    }
};

// Inicializar el módulo cuando el DOM esté listo
document.addEventListener('DOMContentLoaded', function() {
    WhatsAppEndDayNotification.init();
});
