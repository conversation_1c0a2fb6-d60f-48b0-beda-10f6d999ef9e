/**
 * معالج الإشعارات في الخلفية
 * يستخدم عندما لا يكون Cron متاحًا في بيئة الاستضافة
 */

class NotificationProcessor {
    constructor(options = {}) {
        // الإعدادات الافتراضية
        this.options = {
            apiUrl: 'api/notifications.php',
            interval: options.interval || 5 * 60 * 1000, // 5 دقائق افتراضيًا
            enabled: options.enabled || false,
            debug: options.debug || false
        };
        
        // دمج الخيارات المخصصة
        Object.assign(this.options, options);
        
        // معرف المؤقت
        this.timerId = null;
        
        // عداد المحاولات
        this.processCount = 0;
        
        // تسجيل الدخول
        this.log('تم تهيئة معالج الإشعارات');
    }
    
    /**
     * بدء معالجة الإشعارات
     */
    start() {
        if (!this.options.enabled) {
            this.log('معالج الإشعارات غير مفعل');
            return;
        }
        
        if (this.timerId !== null) {
            this.log('معالج الإشعارات قيد التشغيل بالفعل');
            return;
        }
        
        this.log('بدء معالج الإشعارات');
        
        // معالجة فورية عند البدء
        this.processReminders();
        
        // بدء المعالجة الدورية
        this.timerId = setInterval(() => {
            this.processReminders();
        }, this.options.interval);
    }
    
    /**
     * إيقاف معالجة الإشعارات
     */
    stop() {
        if (this.timerId === null) {
            this.log('معالج الإشعارات متوقف بالفعل');
            return;
        }
        
        this.log('إيقاف معالج الإشعارات');
        
        clearInterval(this.timerId);
        this.timerId = null;
    }
    
    /**
     * معالجة التذكيرات
     */
    processReminders() {
        this.processCount++;
        this.log(`معالجة التذكيرات (محاولة #${this.processCount})`);
        
        fetch(`${this.options.apiUrl}?action=process_reminders`, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            },
            credentials: 'same-origin'
        })
        .then(response => response.json())
        .then(data => {
            if (data.status === 'success') {
                this.log('تم معالجة التذكيرات بنجاح', data.data);
            } else {
                this.log('فشل معالجة التذكيرات', data.message);
            }
        })
        .catch(error => {
            this.log('خطأ أثناء معالجة التذكيرات', error);
        });
    }
    
    /**
     * تسجيل الأحداث
     */
    log(message, data = null) {
        if (!this.options.debug) {
            return;
        }
        
        const timestamp = new Date().toLocaleTimeString();
        console.log(`[${timestamp}] [NotificationProcessor] ${message}`);
        
        if (data) {
            console.log(data);
        }
    }
}

// إنشاء كائن عام للاستخدام في الصفحات
window.notificationProcessor = null;

// تهيئة معالج الإشعارات عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // التحقق من وجود عنصر التكوين
    const configElement = document.getElementById('notification-processor-config');
    
    if (!configElement) {
        console.log('لم يتم العثور على عنصر تكوين معالج الإشعارات');
        return;
    }
    
    try {
        // استخراج التكوين من العنصر
        const config = JSON.parse(configElement.textContent);
        
        // إنشاء معالج الإشعارات
        window.notificationProcessor = new NotificationProcessor(config);
        
        // بدء المعالج إذا كان مفعلاً
        if (config.enabled) {
            window.notificationProcessor.start();
        }
    } catch (error) {
        console.error('خطأ في تهيئة معالج الإشعارات:', error);
    }
});
