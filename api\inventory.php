<?php
/**
 * نقاط النهاية الخاصة بإدارة المخزون
 */

// تأكيد تحميل الملفات الأساسية
require_once __DIR__ . '/../config/init.php';

// التحقق من نوع الطلب (POST أو GET أو PUT أو DELETE)
$action = isset($_GET['action']) ? $_GET['action'] : '';

// معالجة الطلبات
try {
    // إعداد رأس الاستجابة
    header('Content-Type: application/json');

    // إنشاء كائنات النماذج
    $db = new Database();
    $inventoryModel = new Inventory($db);
    $productModel = new Product($db);

    // التحقق من صلاحيات المستخدم
    if (!isLoggedIn()) {
        throw new Exception('يجب تسجيل الدخول أولاً', 401);
    }

    switch ($action) {
        // إضافة مخزون للمنتج
        case 'add-stock':
            // التأكد من وجود الصلاحية
            requirePermission('inventory_adjust');

            // التأكد من أن الطلب عبر POST
            if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
                throw new Exception('الطريقة غير مسموح بها', 405);
            }

            // التحقق من صحة المدخلات
            $productId = input('product_id');
            $quantity = input('quantity');
            $branchId = input('branch_id') ?? $_SESSION['user_branch_id'];
            $notes = input('notes') ?? 'إضافة مخزون';

            // التحقق من الإدخالات الإلزامية
            if (empty($productId) || empty($quantity)) {
                throw new Exception('معرف المنتج والكمية مطلوبان', 400);
            }

            // إضافة المخزون
            $inventoryModel->addStock($productId, $quantity, $branchId, $notes);

            echo json_encode([
                'status' => 'success',
                'message' => 'تمت إضافة المخزون بنجاح'
            ]);
            break;

        // سحب مخزون من المنتج
        case 'remove-stock':
            // التأكد من وجود الصلاحية
            requirePermission('inventory_adjust');

            // التأكد من أن الطلب عبر POST
            if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
                throw new Exception('الطريقة غير مسموح بها', 405);
            }

            // التحقق من صحة المدخلات
            $productId = input('product_id');
            $quantity = input('quantity');
            $branchId = input('branch_id') ?? $_SESSION['user_branch_id'];
            $notes = input('notes') ?? 'سحب مخزون';

            // التحقق من الإدخالات الإلزامية
            if (empty($productId) || empty($quantity)) {
                throw new Exception('معرف المنتج والكمية مطلوبان', 400);
            }

            // سحب المخزون
            $inventoryModel->removeStock($productId, $quantity, $branchId, $notes);

            echo json_encode([
                'status' => 'success',
                'message' => 'تم سحب المخزون بنجاح'
            ]);
            break;

        // تسوية المخزون
        case 'adjust':
            // التأكد من وجود الصلاحية
            requirePermission('inventory_adjust');

            // التأكد من أن الطلب عبر POST
            if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
                throw new Exception('الطريقة غير مسموح بها', 405);
            }

            // التحقق من صحة المدخلات
            $productId = input('product_id');
            $actualQuantity = input('actual_quantity');
            $branchId = input('branch_id') ?? $_SESSION['user_branch_id'];
            $notes = input('notes') ?? 'تسوية مخزون';

            // التحقق من الإدخالات الإلزامية
            if (empty($productId) || $actualQuantity === null) {
                throw new Exception('معرف المنتج والكمية الفعلية مطلوبان', 400);
            }

            // تسوية المخزون
            $inventoryModel->adjustInventory($productId, $actualQuantity, $branchId, $notes);

            echo json_encode([
                'status' => 'success',
                'message' => 'تمت تسوية المخزون بنجاح'
            ]);
            break;

        // نقل المخزون بين الفروع
        case 'transfer':
            // التأكد من وجود الصلاحية
            requirePermission('inventory_adjust');

            // التأكد من أن الطلب عبر POST
            if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
                throw new Exception('الطريقة غير مسموح بها', 405);
            }

            // التحقق من صحة المدخلات
            $productId = input('product_id');
            $quantity = input('quantity');
            $sourceBranchId = input('source_branch_id');
            $targetBranchId = input('target_branch_id');
            $notes = input('notes') ?? 'نقل مخزون بين الفروع';

            // التحقق من الإدخالات الإلزامية
            if (empty($productId) || empty($quantity) ||
                empty($sourceBranchId) || empty($targetBranchId)) {
                throw new Exception('جميع المعلومات مطلوبة لنقل المخزون', 400);
            }

            // نقل المخزون
            $inventoryModel->transferStock(
                $productId,
                $sourceBranchId,
                $targetBranchId,
                $quantity,
                $notes
            );

            echo json_encode([
                'status' => 'success',
                'message' => 'تم نقل المخزون بنجاح'
            ]);
            break;

        // استرجاع قائمة المخزون
        case 'list':
            // التأكد من وجود الصلاحية
            requirePermission('inventory_view');

            // معلمات الفلترة والترتيب
            $filters = [
                'search' => input('search'),
                'category_id' => input('category_id'),
                'branch_id' => $_GET['branch_id'] === '0' ? 0 : ($_GET['branch_id'] !== '' ? $_GET['branch_id']: null),
                'low_stock' => input('low_stock'),
                'out_of_stock' => input('out_of_stock'),
                'is_for_sale' => input('is_for_sale'),
                'min_quantity' => input('min_quantity'),
                'max_quantity' => input('max_quantity'),
                'limit' => input('limit') ?? 50,
                'offset' => input('offset') ?? 0
            ];

            // إضافة سجل تصحيح للفلاتر المستخدمة
            $logFile = dirname(__FILE__) . '/../includes/classes/inventory_api_debug.log';
            file_put_contents($logFile, "=== " . date('Y-m-d H:i:s') . " ===\n", FILE_APPEND);
            file_put_contents($logFile, "API Filters: " . json_encode($filters) . "\n", FILE_APPEND);
            file_put_contents($logFile, "Session branch_id: " . ($_SESSION['branch_id'] ?? 'not set') . "\n", FILE_APPEND);

            // استرجاع المخزون
            $inventory = $inventoryModel->getInventory($filters);
            $totalCount = $inventoryModel->getInventoryCount($filters);

            echo json_encode([
                'status' => 'success',
                'inventory' => $inventory,
                'total_count' => $totalCount
            ]);
            break;

        // استرجاع حركات مخزون منتج محدد
        case 'product-transactions':
            // التأكد من وجود الصلاحية
            requirePermission('inventory_view');

            // التحقق من وجود معرف المنتج
            $productId = $_GET['product_id'];

            // معالجة branch_id بشكل خاص للقيمة 0 (كل الفروع)
            $branchId = isset($_GET['branch_id']) && $_GET['branch_id'] === '0' ?
                null : (input('branch_id') ?? $_SESSION['user_branch_id']);

            $limit = input('limit') ?? 20;

            if (!$productId) {
                throw new Exception('معرف المنتج مطلوب', 400);
            }

            // استرجاع حركات المخزون
            $transactions = $inventoryModel->getProductTransactions(
                $productId,
                $branchId,
                $limit
            );

            echo json_encode([
                'status' => 'success',
                'transactions' => $transactions
            ]);
            break;

        // إنشاء تقرير المخزون
        case 'report':
            // التأكد من وجود الصلاحية
            requirePermission('inventory_view');

            // معلمات التقرير
            $filters = [
                'branch_id' => input('branch_id') ?? $_SESSION['user_branch_id'],
                'category_id' => input('category_id')
            ];

            // إنشاء التقرير
            $report = $inventoryModel->getInventoryReport($filters);

            echo json_encode([
                'status' => 'success',
                'report' => $report
            ]);
            break;

        // جرد المخزون
        case 'stock-take':
            // التأكد من وجود الصلاحية
            requirePermission('inventory_adjust');

            // التأكد من أن الطلب عبر POST
            if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
                throw new Exception('الطريقة غير مسموح بها', 405);
            }

            // التحقق من صحة المدخلات
            $branchId = input('branch_id') ?? $_SESSION['user_branch_id'];
            $productIds = input('product_ids') ? explode(',', input('product_ids')) : [];
            $notes = input('notes') ?? 'جرد مخزون';

            // إجراء جرد المخزون
            $stockTakeResults = $inventoryModel->stockTake($branchId, $productIds);

            echo json_encode([
                'status' => 'success',
                'stock_take' => $stockTakeResults
            ]);
            break;

        // معالجة نتائج جرد المخزون
        case 'process-stock-take':
            // التأكد من وجود الصلاحية
            requirePermission('inventory_adjust');

            // التأكد من أن الطلب عبر POST
            if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
                throw new Exception('الطريقة غير مسموح بها', 405);
            }

            // التحقق من صحة المدخلات
            $branchId = isset($_POST['branch_id']) ? $_POST['branch_id'] : $_SESSION['user_branch_id'];
            $stockTakeResults = isset($_POST['stock_take_results']) ? $_POST['stock_take_results'] : null;
            $notes = isset($_POST['notes']) ? $_POST['notes'] : 'معالجة نتائج جرد المخزون';

            // التحقق من وجود نتائج جرد المخزون
            if (empty($stockTakeResults)) {
                throw new Exception('نتائج جرد المخزون مطلوبة', 400);
            }

            // معالجة نتائج الجرد
            $inventoryModel->processStockTakeResults(
                $stockTakeResults,
                $branchId,
                $notes
            );

            echo json_encode([
                'status' => 'success',
                'message' => 'تمت معالجة نتائج جرد المخزون بنجاح'
            ]);
            break;

        // إحصائيات المخزون
        case 'stats':
            // التأكد من وجود الصلاحية
            requirePermission('inventory_view');

            // الحصول على معرف الفرع
            $branchId = $_GET['branch_id'] === '0' ? 0 : ($_GET['branch_id'] ?? $_SESSION['user_branch_id']);

            // استرجاع إحصائيات المخزون
            $stats = $inventoryModel->getInventoryStats($branchId);

            echo json_encode([
                'status' => 'success',
                'stats' => $stats
            ]);
            break;

        // الحصول على مخزون منتج محدد
        case 'get_product_stock':
            // التأكد من وجود الصلاحية
            requirePermission('inventory_view');

            // التحقق من وجود معرف المنتج
            $productId = $_GET['product_id'];
            if (!$productId) {
                throw new Exception('معرف المنتج مطلوب', 400);
            }

            // الحصول على معرف الفرع
            $branchId = $_GET['branch_id'] ?? $_SESSION['user_branch_id'];

            // الحصول على كمية المخزون
            $quantity = $inventoryModel->getProductStock($productId, $branchId);

            echo json_encode([
                'status' => 'success',
                'quantity' => $quantity
            ]);
            break;

        // التحقق من توفر منتج في المخزون
        case 'check-availability':
            // التأكد من وجود الصلاحية
            requirePermission('inventory_view');

            // التحقق من وجود معرف المنتج والكمية
            $productId = input('product_id');
            $requiredQuantity = input('quantity') ?? 1;
            if (!$productId) {
                throw new Exception('معرف المنتج مطلوب', 400);
            }

            // الحصول على معرف الفرع
            $branchId = input('branch_id') ?? $_SESSION['user_branch_id'];

            // التحقق من توفر المنتج
            $available = $inventoryModel->isProductAvailable($productId, $requiredQuantity, $branchId);
            $availableQuantity = $inventoryModel->getProductStock($productId, $branchId);

            echo json_encode([
                'status' => 'success',
                'available' => $available,
                'available_quantity' => $availableQuantity,
                'required_quantity' => $requiredQuantity
            ]);
            break;

        // الافتراضي: إجراء غير معروف
        default:
            throw new Exception('إجراء غير معروف', 404);
    }
} catch (Exception $e) {
    // معالجة الأخطاء
    http_response_code($e->getCode() ?: 500);

    echo json_encode([
        'status' => 'error',
        'message' => $e->getMessage(),
        'code' => $e->getCode() ?: 500
    ]);
}
