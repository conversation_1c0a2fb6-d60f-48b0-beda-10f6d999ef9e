<?php
/**
 * ملف دوال المصادقة
 * يحتوي على الدوال الخاصة بالمصادقة وصلاحيات المستخدمين
 */

// منع الوصول المباشر للملف
if (!defined('BASEPATH')) {
    exit('لا يمكن الوصول المباشر لهذا الملف');
}

/**
 * التحقق إذا كان المستخدم قد قام بتسجيل الدخول
 * @return bool
 */
function isAuthenticated() {
    return isset($_SESSION['user_id']) && !empty($_SESSION['user_id']);
}

/**
 * التحقق من دور المستخدم
 * @param string|array $roles الدور أو الأدوار المسموح بها
 * @return bool
 */
function hasRole($roles) {
    if (!isAuthenticated()) {
        return false;
    }
    
    if (!is_array($roles)) {
        $roles = [$roles];
    }
    
    return in_array($_SESSION['user_role'], $roles);
}

/**
 * التحقق من صلاحية المستخدم
 * @param string $permission الصلاحية المطلوبة
 * @return bool
 */
function auth_hasPermission($permission) {
    if (!isAuthenticated()) {
        return false;
    }
    
    // المدير لديه جميع الصلاحيات
    if ($_SESSION['user_role'] === ROLE_ADMIN) {
        return true;
    }
    
    // التحقق من وجود الصلاحية
    return isset($_SESSION['user_permissions']) && in_array($permission, $_SESSION['user_permissions']);
}

/**
 * التحقق من صلاحيات متعددة (يجب توفرها جميعًا)
 * @param array $permissions مصفوفة الصلاحيات المطلوبة
 * @return bool
 */
function hasAllPermissions($permissions) {
    foreach ($permissions as $permission) {
        if (!hasPermission($permission)) {
            return false;
        }
    }
    
    return true;
}

/**
 * التحقق من صلاحية واحدة على الأقل من مجموعة صلاحيات
 * @param array $permissions مصفوفة الصلاحيات
 * @return bool
 */
function hasAnyPermission($permissions) {
    foreach ($permissions as $permission) {
        if (hasPermission($permission)) {
            return true;
        }
    }
    
    return false;
}

/**
 * التحقق من ملكية السجل (إذا كان المستخدم هو منشئ السجل)
 * @param string $tableName اسم الجدول
 * @param int $recordId معرف السجل
 * @param string $userField اسم حقل المستخدم (اختياري)
 * @return bool
 */
function isRecordOwner($tableName, $recordId, $userField = 'user_id') {
    global $db;
    
    if (!isAuthenticated()) {
        return false;
    }
    
    $db->prepare("SELECT COUNT(*) FROM {$tableName} WHERE id = :id AND {$userField} = :user_id");
    $db->bind(':id', $recordId);
    $db->bind(':user_id', $_SESSION['user_id']);
    
    return $db->fetchColumn() > 0;
}

/**
 * التحقق من انتماء السجل للفرع الذي يديره المستخدم
 * @param string $tableName اسم الجدول
 * @param int $recordId معرف السجل
 * @param string $branchField اسم حقل الفرع (اختياري)
 * @return bool
 */
function isRecordInUserBranch($tableName, $recordId, $branchField = 'branch_id') {
    global $db;
    
    if (!isAuthenticated() || !isset($_SESSION['user_branch_id'])) {
        return false;
    }
    
    // المدير يمكنه الوصول لأي فرع
    if ($_SESSION['user_role'] === ROLE_ADMIN) {
        return true;
    }
    
    $db->prepare("SELECT COUNT(*) FROM {$tableName} WHERE id = :id AND {$branchField} = :branch_id");
    $db->bind(':id', $recordId);
    $db->bind(':branch_id', $_SESSION['user_branch_id']);
    
    return $db->fetchColumn() > 0;
}

/**
 * إعادة توجيه المستخدم إذا لم يكن مسجل الدخول
 * @param string $redirectTo عنوان الصفحة للتوجيه إليها (اختياري)
 */
function requireLogin($redirectTo = null) {
    if (!isAuthenticated()) {
        if ($redirectTo === null) {
            $redirectTo = BASE_URL . 'pages/auth/login.php';
        }
        
        $_SESSION['redirect_after_login'] = $_SERVER['REQUEST_URI'];
        redirect($redirectTo);
    }
}

/**
 * إعادة توجيه المستخدم إذا لم يكن لديه الدور المطلوب
 * @param string|array $roles الدور أو الأدوار المطلوبة
 * @param string $redirectTo عنوان الصفحة للتوجيه إليها (اختياري)
 */
function requireRole($roles, $redirectTo = null) {
    requireLogin();
    
    if (!hasRole($roles)) {
        if ($redirectTo === null) {
            $redirectTo = BASE_URL . 'pages/dashboard.php';
        }
        
        setErrorMessage('ليس لديك صلاحية للوصول لهذه الصفحة');
        redirect($redirectTo);
    }
}

/**
 * إعادة توجيه المستخدم إذا لم يكن لديه الصلاحية المطلوبة
 * @param string $permission الصلاحية المطلوبة
 * @param string $redirectTo عنوان الصفحة للتوجيه إليها (اختياري)
 */
function requirePermission($permission, $redirectTo = null) {
    requireLogin();
    
    if (!hasPermission($permission)) {
        if ($redirectTo === null) {
            $redirectTo = BASE_URL . 'pages/dashboard.php';
        }
        
        setErrorMessage('ليس لديك صلاحية للوصول لهذه الصفحة');
        redirect($redirectTo);
    }
}

/**
 * تسجيل الخروج
 * @param string $redirectTo عنوان الصفحة للتوجيه إليها (اختياري)
 */
function logout($redirectTo = null) {
    // تدمير الجلسة
    session_unset();
    session_destroy();
    
    if ($redirectTo === null) {
        $redirectTo = BASE_URL . 'pages/auth/login.php';
    }
    
    redirect($redirectTo);
}

/**
 * الحصول على اسم المستخدم الحالي
 * @return string اسم المستخدم
 */
function getCurrentUserName() {
    return isset($_SESSION['user_name']) ? $_SESSION['user_name'] : 'زائر';
}

/**
 * الحصول على دور المستخدم الحالي
 * @return string اسم الدور
 */
function getCurrentUserRole() {
    if (!isset($_SESSION['user_role'])) {
        return '';
    }
    
    $roles = [
        ROLE_ADMIN => 'مدير',
        ROLE_MANAGER => 'مدير فرع',
        ROLE_CASHIER => 'كاشير',
        ROLE_EMPLOYEE => 'موظف'
    ];
    
    return $roles[$_SESSION['user_role']] ?? $_SESSION['user_role'];
}

/**
 * الحصول على معرف الفرع الحالي للمستخدم
 * @return int|null معرف الفرع
 */
function getCurrentUserBranchId() {
    return isset($_SESSION['user_branch_id']) ? $_SESSION['user_branch_id'] : null;
}

/**
 * الحصول على صلاحيات المستخدم الحالي
 * @return array مصفوفة الصلاحيات
 */
function getCurrentUserPermissions() {
    return isset($_SESSION['user_permissions']) ? $_SESSION['user_permissions'] : [];
}

/**
 * تحقق مما إذا كان المستخدم مدير
 * @return bool
 */
function isAdmin() {
    return isAuthenticated() && $_SESSION['user_role'] === ROLE_ADMIN;
}

/**
 * تحقق مما إذا كان المستخدم مدير فرع
 * @return bool
 */
function isBranchManager() {
    return isAuthenticated() && $_SESSION['user_role'] === ROLE_MANAGER;
}

/**
 * تحقق مما إذا كان المستخدم كاشير
 * @return bool
 */
function isCashier() {
    return isAuthenticated() && $_SESSION['user_role'] === ROLE_CASHIER;
}

/**
 * تحقق مما إذا كان المستخدم موظف
 * @return bool
 */
function isEmployee() {
    return isAuthenticated() && $_SESSION['user_role'] === ROLE_EMPLOYEE;
}

/**
 * عرض صورة المستخدم الرمزية
 * @param int $userId معرف المستخدم (اختياري)
 * @return string مسار الصورة
 */
function getUserAvatar($userId = null) {
    if ($userId === null && isAuthenticated()) {
        $userId = $_SESSION['user_id'];
    }
    
    // يمكن تعديل هذه الدالة لاسترجاع صورة المستخدم من قاعدة البيانات
    // حاليًا ترجع صورة افتراضية
    return ASSETS_URL . 'imgs/avatar.png';
}

/**
 * تحقق من انتهاء صلاحية الجلسة
 * @param int $inactiveTime الوقت بالثواني (30 دقيقة افتراض)
 * @return bool
 */
function isSessionExpired($inactiveTime = 1800) {
    return isset($_SESSION['last_activity']) && (time() - $_SESSION['last_activity'] > $inactiveTime);
}

/**
 * تحديث وقت آخر نشاط للمستخدم
 */
function updateLastActivity() {
    $_SESSION['last_activity'] = time();
}
