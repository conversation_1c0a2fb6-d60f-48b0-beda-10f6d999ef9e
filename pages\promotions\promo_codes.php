<?php
/**
 * صفحة إدارة أكواد الترويج (Promo Codes)
 */

// تعريف ثابت لمنع الوصول المباشر للملفات المرتبطة
define('BASEPATH', true);

// استدعاء ملفات التهيئة
require_once '../../config/init.php';

// التحقق من تسجيل الدخول
requireLogin();

// التحقق من الصلاحيات
requirePermission('promotions_view');

// عنوان الصفحة
$pageTitle = 'إدارة أكواد الترويج';

// إنشاء كائنات النماذج
$promoCodeModel = new PromoCode($db);
$customerModel = new Customer($db);
$branchModel = new Branch($db);

// التحقق من صلاحيات المستخدم
$isAdmin = hasPermission('admin_access');

// الفرع الحالي
$branchId = $_SESSION['user_branch_id'] ?? null;

// إذا كان المستخدم مديراً، يمكنه اختيار الفرع
$selectedBranchId = $branchId;
if ($isAdmin && isset($_GET['branch_id'])) {
    $selectedBranchId = intval($_GET['branch_id']);
}

// الحصول على قائمة الفروع إذا كان المستخدم مديراً
$branches = [];
if ($isAdmin) {
    $branches = $branchModel->getBranches(['is_active' => 1]);
}

// ضبط معلمات البحث
$searchParams = [
    'branch_id' => $selectedBranchId,
    'page' => isset($_GET['page']) ? intval($_GET['page']) : 1,
    'limit' => 10 // عرض 10 سجلات في الصفحة
];

// إضافة فلتر البحث إذا تم تحديده
if (isset($_GET['search']) && !empty($_GET['search'])) {
    $searchParams['search'] = $_GET['search'];
}

// إضافة فلتر الحالة إذا تم تحديده
if (isset($_GET['status']) && in_array($_GET['status'], ['active', 'inactive'])) {
    $searchParams['is_active'] = ($_GET['status'] === 'active') ? 1 : 0;
}

// حساب الإزاحة للصفحات
$searchParams['offset'] = ($searchParams['page'] - 1) * $searchParams['limit'];

// استعلام قائمة أكواد الترويج
$promoCodes = $promoCodeModel->getPromoCodes($searchParams);

// حساب إجمالي عدد السجلات
$totalRecords = $promoCodeModel->countPromoCodes($searchParams);
$totalPages = ceil($totalRecords / $searchParams['limit']);

// استدعاء رأس الصفحة
include '../../includes/templates/header.php';
?>

<!-- المحتوى الرئيسي للصفحة -->
<div class="container-fluid">
    <!-- عنوان الصفحة -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2 class="h3 mb-0 text-gray-800">إدارة أكواد الترويج</h2>

        <?php if (hasPermission('promotions_create')): ?>
        <a href="add_promo_code.php" class="btn btn-primary">
            <i class="fas fa-plus-circle me-1"></i> إضافة كود ترويج جديد
        </a>
        <?php endif; ?>
    </div>

    <!-- بطاقة البحث والفلترة -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">بحث وفلترة</h6>
        </div>
        <div class="card-body">
            <form method="get" class="mb-0">
                <div class="row g-3 align-items-end">
                    <?php if ($isAdmin && !empty($branches)): ?>
                    <div class="col-md-3">
                        <label for="branch_id" class="form-label">الفرع</label>
                        <select id="branch_id" name="branch_id" class="form-select">
                            <option value="">جميع الفروع</option>
                            <?php foreach ($branches as $branch): ?>
                            <option value="<?php echo $branch['id']; ?>" <?php echo ($selectedBranchId == $branch['id']) ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($branch['name']); ?>
                            </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <?php endif; ?>

                    <div class="col-md-3">
                        <label for="status" class="form-label">الحالة</label>
                        <select id="status" name="status" class="form-select">
                            <option value="">جميع الحالات</option>
                            <option value="active" <?php echo (isset($_GET['status']) && $_GET['status'] === 'active') ? 'selected' : ''; ?>>نشط</option>
                            <option value="inactive" <?php echo (isset($_GET['status']) && $_GET['status'] === 'inactive') ? 'selected' : ''; ?>>غير نشط</option>
                        </select>
                    </div>

                    <div class="col-md-4">
                        <label for="search" class="form-label">بحث</label>
                        <input type="text" id="search" name="search" class="form-control" placeholder="ابحث عن كود أو اسم..." value="<?php echo isset($_GET['search']) ? htmlspecialchars($_GET['search']) : ''; ?>">
                    </div>

                    <div class="col-md-2">
                        <button type="submit" class="btn btn-primary w-100">
                            <i class="fas fa-search me-1"></i> بحث
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- بطاقة قائمة أكواد الترويج -->
    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex justify-content-between align-items-center">
            <h6 class="m-0 font-weight-bold text-primary">قائمة أكواد الترويج</h6>
            <span class="badge bg-primary"><?php echo $totalRecords; ?> كود</span>
        </div>
        <div class="card-body">
            <?php if (empty($promoCodes)): ?>
            <div class="alert alert-info text-center">
                <i class="fas fa-info-circle mb-2 fa-2x"></i>
                <p class="mb-0">لا توجد أكواد ترويج متاحة حاليًا.</p>
                <?php if (hasPermission('promotions_create')): ?>
                <div class="mt-3">
                    <a href="add_promo_code.php" class="btn btn-primary">
                        <i class="fas fa-plus-circle me-1"></i> إضافة كود ترويج جديد
                    </a>
                </div>
                <?php endif; ?>
            </div>
            <?php else: ?>
            <div class="table-responsive">
                <table class="table table-bordered table-hover">
                    <thead class="table-light">
                        <tr>
                            <th width="5%">#</th>
                            <th width="15%">الكود</th>
                            <th width="15%">الاسم</th>
                            <th width="15%">الخصم</th>
                            <th width="15%">الاستخدامات</th>
                            <th width="15%">الفترة</th>
                            <th width="10%">الحالة</th>
                            <th width="15%">الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($promoCodes as $index => $promoCode): ?>
                        <tr>
                            <td><?php echo ($searchParams['page'] - 1) * $searchParams['limit'] + $index + 1; ?></td>
                            <td>
                                <span class="badge bg-secondary"><?php echo htmlspecialchars($promoCode['code']); ?></span>
                            </td>
                            <td>
                                <strong><?php echo htmlspecialchars($promoCode['name']); ?></strong>
                                <?php if (!empty($promoCode['description'])): ?>
                                <div class="small text-muted"><?php echo htmlspecialchars(substr($promoCode['description'], 0, 50)) . (strlen($promoCode['description']) > 50 ? '...' : ''); ?></div>
                                <?php endif; ?>
                            </td>
                            <td>
                                <?php
                                if ($promoCode['discount_type'] === 'percentage') {
                                    echo $promoCode['discount_value'] . '%';
                                } else {
                                    echo number_format($promoCode['discount_value'], 2) . ' ج.م';
                                }
                                ?>
                            </td>
                            <td>
                                <?php
                                if ($promoCode['max_uses']) {
                                    echo $promoCode['current_uses'] . ' / ' . $promoCode['max_uses'];
                                } else {
                                    echo $promoCode['current_uses'] . ' <small class="text-muted">(غير محدود)</small>';
                                }
                                ?>
                            </td>
                            <td>
                                <?php
                                if (empty($promoCode['start_date']) && empty($promoCode['end_date'])) {
                                    echo 'غير محدد';
                                } else {
                                    if (!empty($promoCode['start_date'])) {
                                        echo 'من: ' . date('Y-m-d', strtotime($promoCode['start_date'])) . '<br>';
                                    }
                                    if (!empty($promoCode['end_date'])) {
                                        echo 'إلى: ' . date('Y-m-d', strtotime($promoCode['end_date']));
                                    }
                                }
                                ?>
                            </td>
                            <td>
                                <?php if ($promoCode['is_active']): ?>
                                <span class="badge bg-success">نشط</span>
                                <?php else: ?>
                                <span class="badge bg-danger">غير نشط</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <div class="btn-group">
                                    <?php if (hasPermission('promotions_view')): ?>
                                    <a href="view_promo_code.php?id=<?php echo $promoCode['id']; ?>" class="btn btn-sm btn-info" title="عرض">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <?php endif; ?>

                                    <?php if (hasPermission('promotions_edit')): ?>
                                    <a href="edit_promo_code.php?id=<?php echo $promoCode['id']; ?>" class="btn btn-sm btn-primary" title="تعديل">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <?php endif; ?>

                                    <?php if (hasPermission('promotions_delete')): ?>
                                    <button type="button" class="btn btn-sm btn-danger delete-promo-code" data-id="<?php echo $promoCode['id']; ?>" data-name="<?php echo htmlspecialchars($promoCode['name']); ?>" title="حذف">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                    <?php endif; ?>

                                    <?php if (hasPermission('promotions_edit')): ?>
                                    <button type="button" class="btn btn-sm <?php echo $promoCode['is_active'] ? 'btn-warning' : 'btn-success'; ?> toggle-status" data-id="<?php echo $promoCode['id']; ?>" data-status="<?php echo $promoCode['is_active']; ?>" title="<?php echo $promoCode['is_active'] ? 'تعطيل' : 'تفعيل'; ?>">
                                        <i class="fas <?php echo $promoCode['is_active'] ? 'fa-ban' : 'fa-check'; ?>"></i>
                                    </button>
                                    <?php endif; ?>
                                </div>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>

            <!-- الصفحات -->
            <?php if ($totalPages > 1): ?>
            <nav aria-label="Page navigation" class="mt-4">
                <ul class="pagination justify-content-center">
                    <?php if ($searchParams['page'] > 1): ?>
                    <li class="page-item">
                        <a class="page-link" href="?page=1<?php echo isset($_GET['search']) ? '&search=' . urlencode($_GET['search']) : ''; ?><?php echo isset($_GET['status']) ? '&status=' . $_GET['status'] : ''; ?><?php echo $isAdmin && $selectedBranchId ? '&branch_id=' . $selectedBranchId : ''; ?>">
                            <i class="fas fa-angle-double-right"></i>
                        </a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="?page=<?php echo $searchParams['page'] - 1; ?><?php echo isset($_GET['search']) ? '&search=' . urlencode($_GET['search']) : ''; ?><?php echo isset($_GET['status']) ? '&status=' . $_GET['status'] : ''; ?><?php echo $isAdmin && $selectedBranchId ? '&branch_id=' . $selectedBranchId : ''; ?>">
                            <i class="fas fa-angle-right"></i>
                        </a>
                    </li>
                    <?php endif; ?>

                    <?php
                    $startPage = max(1, $searchParams['page'] - 2);
                    $endPage = min($totalPages, $searchParams['page'] + 2);

                    for ($i = $startPage; $i <= $endPage; $i++):
                    ?>
                    <li class="page-item <?php echo ($i == $searchParams['page']) ? 'active' : ''; ?>">
                        <a class="page-link" href="?page=<?php echo $i; ?><?php echo isset($_GET['search']) ? '&search=' . urlencode($_GET['search']) : ''; ?><?php echo isset($_GET['status']) ? '&status=' . $_GET['status'] : ''; ?><?php echo $isAdmin && $selectedBranchId ? '&branch_id=' . $selectedBranchId : ''; ?>">
                            <?php echo $i; ?>
                        </a>
                    </li>
                    <?php endfor; ?>

                    <?php if ($searchParams['page'] < $totalPages): ?>
                    <li class="page-item">
                        <a class="page-link" href="?page=<?php echo $searchParams['page'] + 1; ?><?php echo isset($_GET['search']) ? '&search=' . urlencode($_GET['search']) : ''; ?><?php echo isset($_GET['status']) ? '&status=' . $_GET['status'] : ''; ?><?php echo $isAdmin && $selectedBranchId ? '&branch_id=' . $selectedBranchId : ''; ?>">
                            <i class="fas fa-angle-left"></i>
                        </a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="?page=<?php echo $totalPages; ?><?php echo isset($_GET['search']) ? '&search=' . urlencode($_GET['search']) : ''; ?><?php echo isset($_GET['status']) ? '&status=' . $_GET['status'] : ''; ?><?php echo $isAdmin && $selectedBranchId ? '&branch_id=' . $selectedBranchId : ''; ?>">
                            <i class="fas fa-angle-double-left"></i>
                        </a>
                    </li>
                    <?php endif; ?>
                </ul>
            </nav>
            <?php endif; ?>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- نافذة تأكيد الحذف -->
<div class="modal fade" id="deletePromoCodeModal" tabindex="-1" aria-labelledby="deletePromoCodeModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deletePromoCodeModalLabel">تأكيد الحذف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من حذف كود الترويج: <strong id="promoCodeNameToDelete"></strong>؟</p>
                <p class="text-danger">هذا الإجراء لا يمكن التراجع عنه.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-danger" id="confirmDeletePromoCode">تأكيد الحذف</button>
            </div>
        </div>
    </div>
</div>

<!-- استدعاء قالب الفوتر -->
<?php include '../../includes/templates/footer.php'; ?>

<script>
$(document).ready(function() {
    // متغير لتخزين معرف كود الترويج المراد حذفه
    let promoCodeIdToDelete = null;

    // عند النقر على زر الحذف
    $('.delete-promo-code').on('click', function() {
        promoCodeIdToDelete = $(this).data('id');
        const promoCodeName = $(this).data('name');

        // عرض اسم كود الترويج في نافذة التأكيد
        $('#promoCodeNameToDelete').text(promoCodeName);

        // عرض نافذة تأكيد الحذف
        $('#deletePromoCodeModal').modal('show');
    });

    // عند تأكيد الحذف
    $('#confirmDeletePromoCode').on('click', function() {
        if (promoCodeIdToDelete) {
            // إرسال طلب الحذف
            $.ajax({
                url: '../../api/promo_codes.php?action=delete',
                type: 'POST',
                data: {
                    id: promoCodeIdToDelete
                },
                dataType: 'json',
                success: function(response) {
                    if (response.status === 'success') {
                        // إغلاق النافذة
                        $('#deletePromoCodeModal').modal('hide');

                        // عرض رسالة نجاح
                        showAlert('success', response.message);

                        // إعادة تحميل الصفحة بعد ثانيتين
                        setTimeout(function() {
                            window.location.reload();
                        }, 2000);
                    } else {
                        // عرض رسالة الخطأ
                        showAlert('danger', response.message);
                    }
                },
                error: function() {
                    // عرض رسالة خطأ عامة
                    showAlert('danger', 'حدث خطأ أثناء معالجة الطلب. يرجى المحاولة مرة أخرى.');
                }
            });
        }
    });

    // تغيير حالة كود الترويج (نشط/غير نشط)
    $('.toggle-status').on('click', function() {
        const promoCodeId = $(this).data('id');
        const currentStatus = $(this).data('status');
        const newStatus = currentStatus == 1 ? 0 : 1;
        const statusText = newStatus == 1 ? 'تفعيل' : 'تعطيل';

        // تأكيد تغيير الحالة
        if (confirm(`هل أنت متأكد من ${statusText} كود الترويج؟`)) {
            // إرسال طلب تغيير الحالة
            $.ajax({
                url: '../../api/promo_codes.php?action=toggle_status',
                type: 'POST',
                data: {
                    id: promoCodeId,
                    is_active: newStatus
                },
                dataType: 'json',
                success: function(response) {
                    if (response.status === 'success') {
                        // عرض رسالة نجاح
                        showAlert('success', response.message);

                        // إعادة تحميل الصفحة بعد ثانيتين
                        setTimeout(function() {
                            window.location.reload();
                        }, 2000);
                    } else {
                        // عرض رسالة الخطأ
                        showAlert('danger', response.message);
                    }
                },
                error: function() {
                    // عرض رسالة خطأ عامة
                    showAlert('danger', 'حدث خطأ أثناء معالجة الطلب. يرجى المحاولة مرة أخرى.');
                }
            });
        }
    });

    // دالة لعرض التنبيهات
    function showAlert(type, message) {
        const alertHtml = `
            <div class="alert alert-${type} alert-dismissible fade show" role="alert">
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        `;

        // إضافة التنبيه في أعلى الصفحة
        $('.container-fluid').prepend(alertHtml);

        // إزالة التنبيه تلقائيًا بعد 5 ثوانٍ
        setTimeout(function() {
            $('.alert').alert('close');
        }, 5000);
    }
});
</script>
