<?php
/**
 * ملف التكوين الرئيسي للنظام
 * يحتوي على إعدادات قاعدة البيانات والثوابت العامة
 */

// منع الوصول المباشر للملف
if (!defined('BASEPATH')) {
    exit('لا يمكن الوصول المباشر لهذا الملف');
}

// معلومات الاتصال بقاعدة البيانات
define('DB_HOST', 'localhost');
define('DB_USER', 'root');
define('DB_PASS', '');
define('DB_NAME', 'salon_system');
define('DB_CHARSET', 'utf8mb4');

// إعدادات النظام
define('SYSTEM_NAME', 'نظام إدارة صالونات الحلاقة');
define('SYSTEM_VERSION', '1.0.0');
define('DEFAULT_TIMEZONE', 'Asia/Riyadh');
define('DEVELOPMENT_MODE', true); // إضافة ثابت لوضع التطوير

// إعدادات المسارات
define('BASE_URL', 'http://localhost/salon_system/');
define('API_URL', BASE_URL . 'api/');
define('ASSETS_URL', BASE_URL . 'assets/');

// إعدادات الرسائل
define('SUCCESS_MSG', 'تمت العملية بنجاح');
define('ERROR_MSG', 'حدث خطأ أثناء تنفيذ العملية');
define('DELETE_MSG', 'تم الحذف بنجاح');
define('ACCESS_DENIED_MSG', 'ليس لديك صلاحية للوصول لهذه الصفحة');

// إعدادات الأدوار
define('ROLE_ADMIN', 'admin');
define('ROLE_MANAGER', 'manager');
define('ROLE_CASHIER', 'cashier');
define('ROLE_EMPLOYEE', 'employee');

// إعدادات الطباعة الحرارية
define('RECEIPT_WIDTH_80', 80);
define('RECEIPT_WIDTH_58', 58);
define('DEFAULT_RECEIPT_WIDTH', RECEIPT_WIDTH_80);

// إعدادات الواتساب (إذا تم تفعيل ميزة إرسال الفواتير)
define('WHATSAPP_ENABLED', false);
define('WHATSAPP_API_URL', '');
define('WHATSAPP_API_KEY', '');

// ضبط المنطقة الزمنية
date_default_timezone_set(DEFAULT_TIMEZONE);

/**
 * دالة للحصول على رسالة الخطأ المفصلة أو العامة حسب وضع التطوير
 * @param Exception $e الاستثناء
 * @return string رسالة الخطأ
 */
function getErrorMessage($e) {
    // في وضع التطوير، أظهر كافة تفاصيل الخطأ
    if (DEVELOPMENT_MODE) {
        return $e->getMessage() . ' في الملف ' . $e->getFile() . ' على السطر ' . $e->getLine();
    } else {
        return ERROR_MSG;
    }
}
