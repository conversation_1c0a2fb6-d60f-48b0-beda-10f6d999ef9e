<?php
/**
 * صفحة عرض تفاصيل العميل
 */

// تعريف ثابت لمنع الوصول المباشر للملفات المرتبطة
define('BASEPATH', true);

// استدعاء ملفات التهيئة
require_once '../../config/init.php';

// التحقق من تسجيل الدخول
requireLogin();

// التحقق من وجود صلاحية عرض العملاء
requirePermission('customers_view');

// التحقق من وجود معرف العميل
if (!isset($_GET['id']) || empty($_GET['id'])) {
    redirect('../customers/');
}

$customerId = intval($_GET['id']);

// إنشاء كائن العميل
$customerModel = new Customer($db);

// استرجاع بيانات العميل
$customer = $customerModel->getCustomerById($customerId);

// التحقق من وجود العميل
if (!$customer) {
    setFlashMessage('العميل غير موجود', 'danger');
    redirect('../customers/');
}

// عنوان الصفحة
$pageTitle = 'تفاصيل العميل: ' . $customer['name'];

// استدعاء رأس الصفحة
include '../../includes/templates/header.php';

// استرجاع الفترة الزمنية للفلتر (الافتراضي: الشهر الحالي)
$today = date('Y-m-d');
$firstDayOfMonth = date('Y-m-01');

$startDate = isset($_GET['date_from']) && !empty($_GET['date_from']) ? $_GET['date_from'] : $firstDayOfMonth;
$endDate = isset($_GET['date_to']) && !empty($_GET['date_to']) ? $_GET['date_to'] : $today;

// استرجاع إجمالي المشتريات خلال الفترة المحددة
$totalSpending = $customerModel->getCustomerTotalSpending($customerId, $startDate, $endDate);

// استرجاع إجمالي الخصومات خلال الفترة المحددة
try {
    error_log("view.php - Calling getCustomerTotalDiscounts - Customer ID: $customerId, Start Date: $startDate, End Date: $endDate");
    $totalDiscounts = $customerModel->getCustomerTotalDiscounts($customerId, $startDate, $endDate);
    error_log("view.php - getCustomerTotalDiscounts result: $totalDiscounts");
} catch (Exception $e) {
    error_log("view.php - Error in getCustomerTotalDiscounts: " . $e->getMessage());
    $totalDiscounts = 0;
}

// استرجاع إعدادات العملة
$settingsModel = new Settings($db);
$allSettings = $settingsModel->getAllSettings();
$currencyCode = $allSettings['system_currency_symbol'] ?? 'ر.س';
$currencyName = $allSettings['system_currency'] ?? 'ريال سعودي';

// استرجاع ملخص الإنفاق حسب الفئات
$spendingByCategory = $customerModel->getCustomerSpendingByCategory($customerId, $startDate, $endDate);

// استرجاع ملخص الإنفاق الشهري
$monthlySpending = $customerModel->getCustomerMonthlySpending($customerId);
?>

<!-- المحتوى الرئيسي للصفحة -->
<div class="container-fluid">
    <!-- رأس الصفحة -->
    <div class="card border-0 shadow-sm mb-4">
        <div class="card-body d-flex justify-content-between align-items-center">
            <h5 class="mb-0">
                <i class="fas fa-user me-2"></i> تفاصيل العميل
            </h5>
            <div>
                <button type="button" id="sendWhatsAppBtn" class="btn btn-success me-2" 
                    data-customer-id="<?php echo $customerId; ?>" 
                    data-customer-phone="<?php echo $customer['phone']; ?>"
                    data-branch-name="<?php echo isset($customer['branch_name']) ? htmlspecialchars($customer['branch_name']) : htmlspecialchars($allSettings['system_name'] ?? 'الصالون'); ?>">
                    <i class="fab fa-whatsapp me-1"></i> إرسال رسالة واتساب
                </button>
                <a href="../customers/" class="btn btn-secondary">
                    <i class="fas fa-arrow-right me-1"></i> العودة لقائمة العملاء
                </a>
            </div>
        </div>
    </div>
    <div class="card border-0 shadow-sm mb-4">
        <div class="card-header bg-light">
            <h6 class="mb-0">تصفية البيانات حسب الفترة الزمنية</h6>
        </div>
        <div class="card-body">
            <form id="filterForm" class="row" method="get">
                <input type="hidden" name="id" value="<?php echo $customerId; ?>">
                
                <!-- اختيار فترات زمنية مُحددة مسبقًا -->
                <div class="col-md-4 mb-3">
                    <label class="form-label">الفترة الزمنية</label>
                    <select id="predefinedPeriod" class="form-select">
                        <option value="custom">فترة مخصصة</option>
                        <option value="this_month" selected>الشهر الحالي</option>
                        <option value="last_month">الشهر الماضي</option>
                        <option value="this_year">السنة الحالية</option>
                        <option value="last_year">السنة الماضية</option>
                        <option value="all_time">كل الفترات</option>
                    </select>
                </div>
                
                <!-- تاريخ البداية -->
                <div class="col-md-3 mb-3">
                    <label for="date_from" class="form-label">من تاريخ</label>
                    <div class="input-group">
                        <input type="text" id="date_from" name="date_from" class="form-control date-picker" 
                               value="<?php echo $startDate; ?>" readonly>
                        <span class="input-group-text"><i class="fas fa-calendar-alt"></i></span>
                    </div>
                </div>
                
                <!-- تاريخ النهاية -->
                <div class="col-md-3 mb-3">
                    <label for="date_to" class="form-label">إلى تاريخ</label>
                    <div class="input-group">
                        <input type="text" id="date_to" name="date_to" class="form-control date-picker" 
                               value="<?php echo $endDate; ?>" readonly>
                        <span class="input-group-text"><i class="fas fa-calendar-alt"></i></span>
                    </div>
                </div>
                
                <!-- زر البحث -->
                <div class="col-md-2 mb-3">
                    <label class="form-label d-block">&nbsp;</label>
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="fas fa-filter me-1"></i> تصفية
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- بطاقة البيانات الأساسية -->
    <div class="row mb-4">
        <div class="col-lg-4">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-header bg-light">
                    <h6 class="mb-0">البيانات الأساسية</h6>
                </div>
                <div class="card-body">
                    <div class="d-flex align-items-center mb-4">
                        <div class="bg-light text-primary rounded-circle p-3 me-3">
                            <i class="fas fa-user fa-2x"></i>
                        </div>
                        <div>
                            <h5 class="mb-1"><?php echo htmlspecialchars($customer['name']); ?></h5>
                            <p class="text-muted mb-0">
                                <i class="fas fa-calendar-alt me-1"></i> 
                                تاريخ التسجيل: <?php echo date('Y/m/d', strtotime($customer['created_at'])); ?>
                            </p>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label fw-bold">رقم الهاتف:</label>
                        <p dir="ltr" class="mb-0"><?php echo $customer['phone'] ?: 'غير متوفر'; ?></p>
                    </div>
                    <div class="mb-3">
                        <label class="form-label fw-bold">البريد الإلكتروني:</label>
                        <p dir="ltr" class="mb-0"><?php echo $customer['email'] ?: 'غير متوفر'; ?></p>
                    </div>
                    <div class="mb-3">
                        <label class="form-label fw-bold">العنوان:</label>
                        <p class="mb-0"><?php echo $customer['address'] ?: 'غير متوفر'; ?></p>
                    </div>
                    <div class="mb-3">
                        <label class="form-label fw-bold">تاريخ الميلاد:</label>
                        <p class="mb-0">
                            <?php echo $customer['birthday'] ? date('Y/m/d', strtotime($customer['birthday'])) : 'غير متوفر'; ?>
                        </p>
                    </div>
                    <div class="mb-3">
                        <label class="form-label fw-bold">نقاط الولاء:</label>
                        <p class="mb-0">
                            <span class="badge bg-success rounded-pill fs-6 px-3 py-2">
                                <?php echo $customer['loyalty_points'] ?: 0; ?> نقطة
                            </span>
                        </p>
                    </div>
                    <?php if (hasPermission('customers_edit')): ?>
                    <div class="mt-4">
                        <button class="btn btn-outline-primary w-100" id="addLoyaltyPointsBtn" data-id="<?php echo $customerId; ?>">
                            <i class="fas fa-gift me-1"></i> إضافة نقاط ولاء
                        </button>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        
        <div class="col-lg-8">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-header bg-light">
                    <h6 class="mb-0">ملخص النشاط</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 mb-4">
                            <div class="card bg-primary text-white">
                                <div class="card-body py-3">
                                    <h3 class="fs-4 mb-2 fw-bold">
                                        <?php echo $customer['visits_count'] ?: 0; ?>
                                    </h3>
                                    <p class="mb-0 fs-6"> عدد الزيارات (الكلي)</p>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-3 mb-4">
                            <div class="card bg-success text-white">
                                <div class="card-body py-3">
                                    <h3 class="fs-4 mb-2 fw-bold" id="totalSpendingValue">
                                        <?php echo $totalSpending ? number_format($totalSpending, 2) : '0.00'; ?> <?php echo $currencyCode; ?>
                                    </h3>
                                    <p class="mb-0 fs-6">إجمالي المشتريات (الفترة الحالية)</p>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-3 mb-4">
                            <div class="card bg-warning text-white">
                                <div class="card-body py-3">
                                    <h3 class="fs-4 mb-2 fw-bold" id="totalDiscountsValue">
                                        <?php echo $totalDiscounts ? number_format($totalDiscounts, 2) : '0.00'; ?> <?php echo $currencyCode; ?>
                                    </h3>
                                    <p class="mb-0 fs-6">إجمالي الخصومات (الفترة الحالية)</p>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-3 mb-4">
                            <div class="card bg-info text-white">
                                <div class="card-body py-3">
                                    <h3 class="fs-4 mb-2 fw-bold">
                                        <?php echo $customer['total_sales'] ? number_format($customer['total_sales'], 2) : '0.00'; ?> <?php echo $currencyCode; ?>
                                    </h3>
                                    <p class="mb-0 fs-6">إجمالي المشتريات (الكلي)</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- الملاحظات -->
                    <div class="mb-3">
                        <label class="form-label fw-bold">ملاحظات:</label>
                        <div class="card bg-light">
                            <div class="card-body py-2">
                                <p class="mb-0"><?php echo $customer['notes'] ?: 'لا توجد ملاحظات'; ?></p>
                            </div>
                        </div>
                    </div>
                    
                    <!-- الإجراءات السريعة -->
                    <div class="mt-4">
                        <div class="row">
                            <div class="col-md-6 mb-2">
                                <a href="../appointments/add.php?customer_id=<?php echo $customerId; ?>" class="btn btn-outline-info w-100">
                                    <i class="fas fa-calendar-plus me-1"></i> حجز موعد
                                </a>
                            </div>
                            
                            <div class="col-md-6 mb-2">
                                <a href="../pos/index.php?customer_id=<?php echo $customerId; ?>" class="btn btn-outline-success w-100">
                                    <i class="fas fa-cash-register me-1"></i> إنشاء فاتورة
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- بطاقة الفلتر -->

    <!-- سجل المشتريات والخدمات -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-light py-2">
                    <h6 class="mb-0 fw-bold">تفاصيل المشتريات والخدمات</h6>
                </div>
                <div class="card-body p-0">
                    <!-- جدول المشتريات -->
                    <div class="purchases-table-wrapper">
                        <table id="purchasesDetailsTable" class="table table-borderless table-striped dt-responsive nowrap w-100 mb-0">
                            <thead>
                                <tr class="bg-light">
                                    <th width="12%" class="all text-center">رقم الفاتورة</th>
                                    <th width="10%" class="all text-center">التاريخ</th>
                                    <th width="8%" class="min-tablet text-center">النوع</th>
                                    <th width="20%" class="all">الاسم</th>
                                    <th width="12%" class="min-tablet">الفئة</th>
                                    <th width="8%" class="desktop text-center">الكمية</th>
                                    <th width="10%" class="desktop text-center">السعر</th>
                                    <th width="8%" class="desktop text-center">الخصم</th>
                                    <th width="12%" class="all text-center">الإجمالي</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- سيتم تعبئة البيانات عبر AJAX -->
                            </tbody>
                            <tfoot>
                                <tr class="bg-light">
                                    <td colspan="8" class="text-end fw-bold border-0">الإجمالي:</td>
                                    <td id="total-amount-cell" class="text-center fw-bold border-0"></td>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                    
                    <!-- رسالة عدم وجود بيانات -->
                    <div id="noPurchasesData" class="text-center text-muted py-5" style="display: none;">
                        <i class="fas fa-receipt fa-3x mb-3 text-light"></i>
                        <p class="mb-0">لا توجد مشتريات متاحة للفترة المحددة</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- جدول الفواتير -->
    <div class="card border-0 shadow-sm mb-4">
        <div class="card-header bg-light">
            <h6 class="mb-0">قائمة الفواتير</h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table id="invoicesTable" class="table table-hover">
                    <thead class="table-light">
                        <tr>
                            <th>رقم الفاتورة</th>
                            <th>التاريخ</th>
                            <th>المبلغ</th>
                            <th>الخصم</th>
                            <th>المبلغ النهائي</th>
                            <th>طريقة الدفع</th>
                            <th>الحالة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <!-- سيتم تعبئة البيانات عبر AJAX -->
                    </tbody>
                </table>
            </div>
            <div id="noInvoicesMessage" class="text-center text-muted py-4" style="display: none;">
                لا توجد فواتير مسجلة لهذا العميل خلال الفترة المحددة
            </div>
        </div>
    </div>


</div>

<!-- مودال إضافة نقاط ولاء -->
<div class="modal fade" id="addLoyaltyPointsModal" tabindex="-1" aria-labelledby="addLoyaltyPointsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-sm">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addLoyaltyPointsModalLabel">إضافة نقاط ولاء</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="إغلاق"></button>
            </div>
            <div class="modal-body">
                <form id="addLoyaltyPointsForm">
                    <input type="hidden" id="loyalty_customer_id" name="customer_id" value="<?php echo $customerId; ?>">
                    <div class="mb-3">
                        <label for="points" class="form-label">عدد النقاط <span class="text-danger">*</span></label>
                        <input type="number" class="form-control" id="points" name="points" required min="1" value="10">
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" id="submitAddLoyaltyPoints">إضافة</button>
            </div>
        </div>
    </div>
</div>

<!-- نافذة منبثقة لإرسال رسالة واتساب للعميل -->
<div class="modal fade" id="whatsAppModal" tabindex="-1" aria-labelledby="whatsAppModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-success text-white">
                <h5 class="modal-title" id="whatsAppModalLabel">إرسال رسالة واتساب للعميل</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="whatsAppMessageForm">
                    <div class="mb-3">
                        <label for="recipient" class="form-label">رقم الجوال</label>
                        <input type="text" class="form-control" id="recipient" readonly>
                    </div>
                    <div class="mb-3">
                        <label for="messageTemplate" class="form-label">قالب الرسالة</label>
                        <select class="form-select" id="messageTemplate">
                            <option value="">اختر قالب...</option>
                            <option value="greeting">رسالة ترحيب</option>
                            <option value="reminder">تذكير بموعد</option>
                            <option value="promotion">عرض خاص</option>
                            <option value="custom">رسالة مخصصة</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="messageText" class="form-label">نص الرسالة</label>
                        <textarea class="form-control" id="messageText" rows="6"></textarea>
                        <div class="form-text">يمكنك استخدام {name} ليتم استبداله باسم العميل.</div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <div class="d-flex justify-content-between w-100">
                    <div id="sendStatus" class="text-success" style="display: none;"></div>
                    <div>
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="button" class="btn btn-success" id="sendWhatsAppMessage">
                            <i class="fab fa-whatsapp me-1"></i> إرسال الرسالة
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- سكريبت إشعارات الواتساب -->
<script src="<?php echo BASE_URL; ?>assets/js/whatsapp-custom-message.js"></script>

<!-- تأكد من تحميل مكتبات Bootstrap بالترتيب الصحيح -->
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.2/dist/js/bootstrap.bundle.min.js"></script>

<!-- التأكد من أن مودال الواتساب جاهز -->
<script>
    // عند اكتمال تحميل الصفحة
    document.addEventListener('DOMContentLoaded', function() {
        console.log('DOM جاهز، التحقق من أن المودال متاح');
        // محاولة العثور على المودال
        var modalElement = document.getElementById('whatsAppModal');
        if (modalElement) {
            console.log('تم العثور على المودال في DOM');
            
            // إضافة معالج حدث لإغلاق المودال (تحديث الصفحة عند الإغلاق)
            modalElement.addEventListener('hidden.bs.modal', function () {
                console.log('تم إغلاق المودال، جاري تحديث الصفحة...');
                // تأخير قصير قبل تحديث الصفحة ليسمح بعرض أي إشعارات
                setTimeout(function() {
                    location.reload();
                }, 500);
            });
            
            // إعداد زر الواتساب مباشرة
            var whatsappButton = document.getElementById('sendWhatsAppBtn');
            if (whatsappButton) {
                console.log('تم العثور على زر الواتساب، إضافة معالج حدث');
                
                whatsappButton.addEventListener('click', function(e) {
                    e.preventDefault();
                    console.log('تم النقر على زر الواتساب (مباشرة من DOM)');
                    
                    try {
                        // محاولة فتح المودال باستخدام Bootstrap 5
                        if (typeof bootstrap !== 'undefined') {
                            var modal = new bootstrap.Modal(modalElement);
                            modal.show();
                            console.log('تم فتح المودال باستخدام Bootstrap API');
                        } else {
                            // استخدام jQuery كخطة بديلة
                            $(modalElement).modal('show');
                            console.log('تم فتح المودال باستخدام jQuery');
                        }
                    } catch(err) {
                        console.error('خطأ عند فتح المودال:', err);
                        
                        // خطة أخيرة - محاولة تغيير خصائص العرض مباشرة
                        try {
                            modalElement.style.display = 'block';
                            modalElement.classList.add('show');
                            document.body.classList.add('modal-open');
                            
                            // إضافة خلفية للمودال
                            var backdrop = document.createElement('div');
                            backdrop.className = 'modal-backdrop fade show';
                            document.body.appendChild(backdrop);
                            
                            console.log('تم فتح المودال يدويًا');
                        } catch(err2) {
                            console.error('فشل جميع محاولات فتح المودال:', err2);
                            alert('لا يمكن فتح نافذة إرسال الواتساب. يرجى تحديث الصفحة والمحاولة مرة أخرى.');
                        }
                    }
                });
            } else {
                console.error('زر الواتساب غير موجود في الصفحة!');
            }
        } else {
            console.error('لم يتم العثور على مودال الواتساب في DOM!');
        }
    });
</script>

<script>
    // دالة لتحويل الأرقام العربية/الفارسية إلى أرقام غربية
    function convertArabicToWesternNumerals(str) {
        if (!str) return str;
        
        return str.replace(/[٠١٢٣٤٥٦٧٨٩]/g, function(d) {
            return d.charCodeAt(0) - 1632; // تحويل Unicode للأرقام العربية
        }).replace(/[۰۱۲۳۴۵۶۷۸۹]/g, function(d) {
            return d.charCodeAt(0) - 1776; // تحويل Unicode للأرقام الفارسية
        }).replace(/\//g, '-'); // تبديل / بـ - للتوافق مع تنسيق التاريخ في قاعدة البيانات
    }

    $(document).ready(function() {
        // تعريف متغيرات العملة
        const currencySymbol = '<?php echo $currencyCode; ?>';
        const currencyName = '<?php echo $currencyName; ?>';
        
        // تحويل قيم التاريخ الحالية عند تحميل الصفحة
        $('#date_from').val(convertArabicToWesternNumerals($('#date_from').val()));
        $('#date_to').val(convertArabicToWesternNumerals($('#date_to').val()));

        // تنفيذ جديد وبسيط لمعالجة مشكلة التقويم
        
        // 1. إضافة أنماط CSS لضمان عرض تقويم واحد فقط
        $('head').append(`
            <style id="fix-datepicker-styles">
                /* منع ظهور تقويمات متعددة */
                .ui-datepicker:not(:last-of-type) {
                    display: none !important;
                }
                
                /* تحسين مظهر التقويم */
                .ui-datepicker {
                    z-index: 9999 !important;
                    box-shadow: 0 0 10px rgba(0,0,0,0.2);
                    border: none !important;
                }
                
                /* تحسين مؤشر الماوس لحقول التاريخ */
                .date-picker, .input-group-text {
                    cursor: pointer !important;
                }
            </style>
        `);
        
        // 2. إزالة أي مثيلات jQuery UI datepicker سابقة بأمان
        try {
            $('.date-picker').each(function() {
                try {
                    if ($(this).hasClass('hasDatepicker')) {
                        $(this).datepicker('destroy');
                    }
                } catch (err) {
                    console.log('حدث خطأ أثناء تدمير التقويم:', err);
                }
            });
            
            // إزالة أي تقويمات مفتوحة
            $('.ui-datepicker').remove();
        } catch (e) {
            console.log('خطأ أثناء تنظيف التقويمات:', e);
        }
        
        // 3. تهيئة التقويم لكل حقل بطريقة صحيحة
        $('.date-picker').each(function(index) {
            const $this = $(this);
            
            // تأخير بسيط لتجنب تعارضات التهيئة
            setTimeout(function() {
                try {
                    $this.datepicker({
                        dateFormat: 'yy-mm-dd',
                        changeMonth: true,
                        changeYear: true,
                        isRTL: true,
                        showOtherMonths: true,
                        selectOtherMonths: true,
                        closeText: 'إغلاق',
                        currentText: 'اليوم',
                        monthNames: ['يناير', 'فبراير', 'مارس', 'إبريل', 'مايو', 'يونيو', 'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'],
                        monthNamesShort: ['ينا', 'فبر', 'مار', 'إبر', 'ماي', 'يون', 'يول', 'أغس', 'سبت', 'أكت', 'نوف', 'ديس'],
                        dayNames: ['الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت'],
                        dayNamesShort: ['أحد', 'اثنين', 'ثلاثاء', 'أربعاء', 'خميس', 'جمعة', 'سبت'],
                        dayNamesMin: ['أح', 'اث', 'ثل', 'أر', 'خم', 'جم', 'سب'],
                        firstDay: 6,
                        beforeShow: function(input, inst) {
                            // إزالة أي تقويمات أخرى مفتوحة
                            $('.ui-datepicker:not(:last)').remove();
                            
                            // تحديث z-index لضمان ظهور التقويم فوق كل شيء
                            setTimeout(function() {
                                $('.ui-datepicker').css('z-index', 9999);
                            }, 10);
                        },
                        onSelect: function(dateText) {
                            // تحويل التاريخ من أي أرقام عربية محتملة
                            let westernDate = convertArabicToWesternNumerals(dateText);
                            $(this).val(westernDate);
                            console.log('تم اختيار التاريخ:', westernDate);
                            
                            // تفعيل حدث التغيير يدويًا للتأكد من تحديث البيانات
                            if ($(this).closest('#filterForm').length) {
                                // تحديث قائمة الفترة المحددة مسبقًا إلى "فترة مخصصة"
                                $('#predefinedPeriod').val('custom');
                                $(this).trigger('change');
                            }
                        }
                    });
                } catch (err) {
                    console.log('خطأ في تهيئة التقويم لحقل #' + (index + 1), err);
                }
            }, index * 50); // تأخير متدرج
        });
        
        // 4. ربط أيقونات التقويم بحقول الإدخال
        $('.input-group-text').off('click').on('click', function() {
            const $inputField = $(this).closest('.input-group').find('.date-picker');
            if ($inputField.length) {
                // إغلاق أي تقويمات أخرى مفتوحة أولاً
                $('.ui-datepicker').hide();
                
                // فتح التقويم المرتبط بهذا الحقل
                $inputField.datepicker('show');
            }
        });
        
        // 5. إغلاق التقويم عند النقر خارجه
        $(document).on('mousedown', function(e) {
            if (!$(e.target).closest('.ui-datepicker, .date-picker, .input-group-text').length) {
                $('.ui-datepicker').hide();
            }
        });

        // معالجة اختيار الفترة الزمنية المحددة مسبقًا
        $('#predefinedPeriod').change(function() {
            console.log('تم تغيير الفترة المحددة إلى:', $(this).val());
            
            // إعادة تهيئة مكون datepicker لضمان عمله بشكل صحيح
            try {
                // محاولة إعادة تهيئة التقويم
                if ($('#date_from').hasClass('hasDatepicker')) {
                    $('#date_from').datepicker('destroy');
                    $('#date_from').removeClass('hasDatepicker');
                }
                
                if ($('#date_to').hasClass('hasDatepicker')) {
                    $('#date_to').datepicker('destroy');
                    $('#date_to').removeClass('hasDatepicker');
                }
                
                // إعادة تهيئة التقويم لكلا الحقلين
                $('#date_from, #date_to').datepicker({
                    dateFormat: 'yy-mm-dd',
                    changeMonth: true,
                    changeYear: true,
                    isRTL: true
                });
                
                console.log('تم إعادة تهيئة التقويم بنجاح');
            } catch (error) {
                console.error('خطأ في إعادة تهيئة التقويم:', error);
            }
            
            const today = new Date();
            let startDate, endDate;
            
            switch($(this).val()) {
                case 'this_month':
                    startDate = new Date(today.getFullYear(), today.getMonth(), 1);
                    endDate = today;
                    console.log('تم تعيين فترة الشهر الحالي:', startDate, endDate);
                    break;
                case 'last_month':
                    startDate = new Date(today.getFullYear(), today.getMonth() - 1, 1);
                    endDate = new Date(today.getFullYear(), today.getMonth(), 0);
                    break;
                case 'this_year':
                    startDate = new Date(today.getFullYear(), 0, 1);
                    endDate = today;
                    break;
                case 'last_year':
                    startDate = new Date(today.getFullYear() - 1, 0, 1);
                    endDate = new Date(today.getFullYear() - 1, 11, 31);
                    break;
                case 'all_time':
                    startDate = '';
                    endDate = '';
                    break;
                default: // custom
                    return; // لا تغير التواريخ
            }
            
            // إعادة تعيين القيم السابقة
            $('#date_from, #date_to').datepicker('setDate', null);
            $('#date_from, #date_to').val('');
            
            if (startDate) {
                try {
                    // تعيين تاريخ البداية باستخدام API التقويم
                    $('#date_from').datepicker('setDate', startDate);
                    
                    // تخزين تنسيق التاريخ المعياري في الحقل
                    const startDateStr = startDate.getFullYear() + '-' + 
                        String(startDate.getMonth() + 1).padStart(2, '0') + '-' + 
                        String(startDate.getDate()).padStart(2, '0');
                    $('#date_from').val(startDateStr);
                    
                    console.log('تم تعيين تاريخ البداية:', startDateStr);
                } catch (error) {
                    console.error('خطأ في تعيين تاريخ البداية:', error);
                }
            } else {
                $('#date_from').val('');
            }
            
            if (endDate) {
                try {
                    // تعيين تاريخ النهاية باستخدام API التقويم
                    $('#date_to').datepicker('setDate', endDate);
                    
                    // تخزين تنسيق التاريخ المعياري في الحقل
                    const endDateStr = endDate.getFullYear() + '-' + 
                        String(endDate.getMonth() + 1).padStart(2, '0') + '-' + 
                        String(endDate.getDate()).padStart(2, '0');
                    $('#date_to').val(endDateStr);
                    
                    console.log('تم تعيين تاريخ النهاية:', endDateStr);
                } catch (error) {
                    console.error('خطأ في تعيين تاريخ النهاية:', error);
                }
            } else {
                $('#date_to').val('');
            }
            
            // تأكيد من أن القيم تم تعيينها بشكل صحيح
            setTimeout(function() {
                console.log('القيم النهائية:', {
                    date_from: $('#date_from').val(),
                    date_to: $('#date_to').val()
                });
                
                // تقديم النموذج تلقائيًا
                $('#filterForm').trigger('submit');
            }, 100);
        });

        // تهيئة جدول الفواتير
        const invoicesTable = $('#invoicesTable').DataTable({
            processing: true,
            serverSide: false,
            ajax: {
                url: '../../api/customers.php',
                type: 'POST',
                data: function(d) {
                    const postData = {
                        action: 'get_customer_invoices',
                        customer_id: <?php echo $customerId; ?>,
                        date_from: convertArabicToWesternNumerals($('#date_from').val()),
                        date_to: convertArabicToWesternNumerals($('#date_to').val())
                    };
                    console.log('Sending request to get invoices:', postData);
                    return postData;
                },
                dataSrc: function(response) {
                    console.log('Received invoice response:', response);
                    if (response.status === 'success' && response.invoices) {
                        if (response.invoices.length === 0) {
                            $('#noInvoicesMessage').show();
                            console.log('No invoices found');
                        } else {
                            $('#noInvoicesMessage').hide();
                            console.log('Found ' + response.invoices.length + ' invoices');
                        }
                        return response.invoices;
                    } else {
                        $('#noInvoicesMessage').show();
                        console.log('Invalid response or no invoices returned');
                        return [];
                    }
                },
                error: function(xhr, status, error) {
                    console.error('Error fetching invoices:', status, error);
                    console.log('XHR Response:', xhr.responseText);
                    $('#noInvoicesMessage').show();
                }
            },
            columns: [
                { data: 'invoice_number' },
                { 
                    data: 'created_at',
                    render: function(data) {
                        return moment(data).format('YYYY/MM/DD');
                    }
                },
                { 
                    data: 'total_amount',
                    render: function(data) {
                        return parseFloat(data).toFixed(2) + ' ' + currencySymbol;
                    }
                },
                { 
                    data: 'discount_amount',
                    render: function(data) {
                        return parseFloat(data).toFixed(2) + ' ' + currencySymbol;
                    }
                },
                { 
                    data: 'final_amount',
                    render: function(data) {
                        return parseFloat(data).toFixed(2) + ' ' + currencySymbol;
                    }
                },
                { 
                    data: 'payment_method',
                    render: function(data) {
                        switch(data) {
                            case 'cash': return 'نقد';
                            case 'card': return 'بطاقة ائتمان';
                            case 'bank': return 'تحويل بنكي';
                            default: return data;
                        }
                    }
                },
                { 
                    data: 'status',
                    render: function(data) {
                        let statusClass = '';
                        let statusText = '';
                        
                        switch(data) {
                            case 'completed':
                                statusClass = 'success';
                                statusText = 'مكتملة';
                                break;
                            case 'pending':
                                statusClass = 'warning';
                                statusText = 'معلقة';
                                break;
                            case 'cancelled':
                                statusClass = 'danger';
                                statusText = 'ملغية';
                                break;
                            default:
                                statusClass = 'primary';
                                statusText = data || 'مكتملة';
                        }
                        
                        return `<span class="badge bg-${statusClass}">${statusText}</span>`;
                    }
                },
                {
                    data: null,
                    orderable: false,
                    render: function(data) {
                        return `
                            <a href="../invoices/view.php?id=${data.id}" class="btn btn-sm btn-info">
                                <i class="fas fa-eye"></i>
                            </a>
                            <?php if (hasPermission('invoices_print')): ?>
                            <a href="../invoices/print.php?id=${data.id}" class="btn btn-sm btn-secondary" target="_blank">
                                <i class="fas fa-print"></i>
                            </a>
                            <?php endif; ?>
                        `;
                    }
                }
            ],
            language: {
                url: 'https://cdn.datatables.net/plug-ins/1.11.5/i18n/ar.json'
            },
            order: [[1, 'desc']]
        });

        // تهيئة جدول الزيارات
        const visitsTable = $('#visitsTable').DataTable({
            processing: true,
            serverSide: false,
            ajax: {
                url: '../../api/customers.php',
                type: 'POST',
                data: function(d) {
                    return {
                        action: 'get_customer_visits',
                        customer_id: <?php echo $customerId; ?>,
                        date_from: convertArabicToWesternNumerals($('#date_from').val()),
                        date_to: convertArabicToWesternNumerals($('#date_to').val())
                    };
                },
                dataSrc: function(response) {
                    if (response.status === 'success' && response.visits) {
                        if (response.visits.length === 0) {
                            $('#noVisitsMessage').show();
                        } else {
                            $('#noVisitsMessage').hide();
                        }
                        return response.visits;
                    } else {
                        $('#noVisitsMessage').show();
                        return [];
                    }
                }
            },
            columns: [
                { 
                    data: 'visit_date',
                    render: function(data) {
                        return moment(data).format('YYYY/MM/DD');
                    }
                },
                { data: 'branch_name' },
                { data: 'employee_name' },
                { 
                    data: 'notes',
                    render: function(data) {
                        return data || '-';
                    }
                }
            ],
            language: {
                url: 'https://cdn.datatables.net/plug-ins/1.11.5/i18n/ar.json'
            },
            order: [[0, 'desc']]
        });

        // تحميل بيانات الرسوم البيانية
        loadCharts();

        // دالة لتحميل الرسوم البيانية
        function loadCharts() {
            // تحديث إجمالي المشتريات والخصومات للفترة المحددة
            let dateFrom = convertArabicToWesternNumerals($('#date_from').val());
            let dateTo = convertArabicToWesternNumerals($('#date_to').val());
            
            // تنسيق التواريخ للتأكد من أنها بالصيغة الصحيحة (yyyy-mm-dd)
            if (dateFrom && dateFrom.includes('/')) {
                dateFrom = dateFrom.split('/').join('-');
            }
            if (dateTo && dateTo.includes('/')) {
                dateTo = dateTo.split('/').join('-');
            }
            
            console.log('Using date range for totals:', { dateFrom, dateTo });
            
            // استعلام إجمالي المشتريات
            $.ajax({
                url: '../../api/customers.php',
                type: 'POST',
                data: {
                    action: 'get_customer_total_spending',
                    customer_id: <?php echo $customerId; ?>,
                    date_from: dateFrom,
                    date_to: dateTo
                },
                dataType: 'json',
                success: function(response) {
                    console.log('Total Spending Response:', response);
                    if (response.status === 'success') {
                        // تحديث قيمة إجمالي المشتريات في البطاقة
                        const totalSpending = parseFloat(response.total_spending) || 0;
                        console.log('Updating total spending to: ' + totalSpending.toFixed(2));
                        $('#totalSpendingValue').text(totalSpending.toFixed(2) + ' ' + currencySymbol);
                    } else {
                        console.error('خطأ في استجابة إجمالي المشتريات:', response.message || 'خطأ غير معروف');
                    }
                },
                error: function(xhr, status, error) {
                    console.error('خطأ في استدعاء إجمالي المشتريات:', status, error);
                }
            });
            
            // استعلام إجمالي الخصومات
            $.ajax({
                url: '../../api/customers.php',
                type: 'POST',
                data: {
                    action: 'get_customer_total_discounts',
                    customer_id: <?php echo $customerId; ?>,
                    date_from: dateFrom,
                    date_to: dateTo
                },
                dataType: 'json',
                success: function(response) {
                    console.log('Total Discounts Response:', response);
                    if (response.status === 'success') {
                        // تحديث قيمة إجمالي الخصومات في البطاقة
                        const totalDiscounts = parseFloat(response.total_discounts) || 0;
                        console.log('Updating total discounts to: ' + totalDiscounts.toFixed(2));
                        $('#totalDiscountsValue').text(totalDiscounts.toFixed(2) + ' ' + currencySymbol);
                    } else {
                        console.error('خطأ في استجابة إجمالي الخصومات:', response.message || 'خطأ غير معروف');
                    }
                },
                error: function(xhr, status, error) {
                    console.error('خطأ في استدعاء إجمالي الخصومات:', status, error);
                }
            });

            // جلب تفاصيل المشتريات والخدمات
            $.ajax({
                url: '../../api/customers.php',
                type: 'POST',
                data: {
                    action: 'get_customer_purchases_details',
                    customer_id: <?php echo $customerId; ?>,
                    date_from: dateFrom,
                    date_to: dateTo
                },
                dataType: 'json',
                success: function(response) {
                    console.log('API Response:', response);
                    if (response.status === 'success' && response.data && response.data.length > 0) {
                        $('#noPurchasesData').hide();
                        renderPurchasesTable(response.data);
                    } else {
                        $('#noPurchasesData').show();
                    }
                },
                error: function(xhr, status, error) {
                    console.error('خطأ في استدعاء تفاصيل المشتريات:', status, error);
                    $('#noPurchasesData').show();
                }
            });

            // رسم بياني للمشتريات الشهرية
            $.ajax({
                url: '../../api/customers.php',
                type: 'POST',
                data: {
                    action: 'get_customer_monthly_spending',
                    customer_id: <?php echo $customerId; ?>
                },
                dataType: 'json',
                success: function(response) {
                    if (response.status === 'success' && response.data && response.data.length > 0) {
                        $('#noMonthlyData').hide();
                        renderMonthlyChart(response.data);
                    } else {
                        $('#noMonthlyData').show();
                    }
                }
            });
        }

        // دالة لرسم بياني للمشتريات حسب الفئة
        function renderCategoryChart(data) {
            // تحقق من وجود قابلية تهيئة DataTable
            if ($.fn.DataTable) {
                // إذا كان الجدول موجودًا، تدميره أولاً
                if ($.fn.DataTable.isDataTable('#categorySpendingTable')) {
                    $('#categorySpendingTable').DataTable().destroy();
                }
                
                // حساب المجموع الكلي للمشتريات
                const totalAmount = data.reduce((sum, item) => sum + parseFloat(item.total_amount), 0);
                
                // إنشاء جدول مع DataTables
                $('#categorySpendingTable').DataTable({
                    data: data,
                    columns: [
                        { data: 'category_name' },
                        { 
                            data: 'total_amount',
                            render: function(data) {
                                return parseFloat(data).toFixed(2) + ' ' + currencySymbol;
                            }
                        },
                        {
                            data: 'total_amount',
                            render: function(data) {
                                const percentage = (parseFloat(data) / totalAmount * 100);
                                return percentage.toFixed(1) + '%';
                            }
                        }
                    ],
                    ordering: true,
                    order: [[1, 'desc']], // ترتيب حسب القيمة تنازليًا
                    searching: false,
                    paging: data.length > 10, // تمكين الصفحات فقط إذا كان هناك أكثر من 10 عناصر
                    lengthChange: false,
                    pageLength: 10,
                    language: {
                        url: 'https://cdn.datatables.net/plug-ins/1.11.5/i18n/ar.json'
                    },
                    dom: 'rtp',
                    "initComplete": function() {
                        // إضافة الصف الإجمالي في نهاية الجدول
                        let footer = '<tfoot><tr class="fw-bold table-light">';
                        footer += '<td>الإجمالي</td>';
                        footer += '<td>' + totalAmount.toFixed(2) + ' ' + currencySymbol + '</td>';
                        footer += '<td>100%</td>';
                        footer += '</tr></tfoot>';
                        
                        $(this).append(footer);
                    }
                });
            } else {
                // إذا لم تكن مكتبة DataTables متاحة، استخدم عرض جدول بسيط
                const tableBody = $('#categorySpendingTable tbody');
                tableBody.empty();
                
                // حساب المجموع الكلي
                const totalAmount = data.reduce((sum, item) => sum + parseFloat(item.total_amount), 0);
                
                // إضافة صفوف البيانات
                data.forEach(function(item) {
                    const percentage = (parseFloat(item.total_amount) / totalAmount * 100).toFixed(1);
                    const row = `
                        <tr>
                            <td>${item.category_name}</td>
                            <td>${parseFloat(item.total_amount).toFixed(2)} ${currencySymbol}</td>
                            <td>${percentage}%</td>
                        </tr>
                    `;
                    tableBody.append(row);
                });
                
                // إضافة صف الإجمالي
                const totalRow = `
                    <tr class="fw-bold table-light">
                        <td>الإجمالي</td>
                        <td>${totalAmount.toFixed(2)} ${currencySymbol}</td>
                        <td>100%</td>
                    </tr>
                `;
                tableBody.append(totalRow);
            }
            
            // تم إلغاء خاصية التصدير
        }

            // دالة لإضافة CSS مخصص للجدول الرسبونسف
    function addCustomResponsiveStyles() {
                    // إضافة أنماط CSS مخصصة إذا لم تكن موجودة
            if ($('#customResponsiveTableStyles').length === 0) {
                $('head').append(`
                    <style id="customResponsiveTableStyles">
                        /* تحسين شكل الجدول ليتوافق مع التصميم العام */
                        #purchasesDetailsTable {
                            width: 100% !important;
                            margin-bottom: 0;
                        }
                        
                        #purchasesDetailsTable thead th {
                            font-weight: 600;
                            white-space: nowrap;
                            border-bottom-width: 1px;
                        }
                        
                        #purchasesDetailsTable tbody tr {
                            transition: background-color 0.2s;
                        }
                        
                        #purchasesDetailsTable tbody tr:hover {
                            background-color: rgba(13, 110, 253, 0.05);
                        }
                        
                        #purchasesDetailsTable tbody td {
                            vertical-align: middle;
                            padding: 0.6rem 0.75rem;
                            border-bottom: 1px solid #f0f0f0;
                        }
                        
                        /* تحسين التعامل مع الحقول الرقمية */
                        #purchasesDetailsTable td.text-center, 
                        #purchasesDetailsTable th.text-center {
                            text-align: center !important;
                        }
                        
                        /* تحسين الاستجابة للجداول على الشاشات الصغيرة */
                        @media screen and (max-width: 767px) {
                            .dataTables_wrapper .dataTables_length, 
                            .dataTables_wrapper .dataTables_filter,
                            .dataTables_wrapper .dataTables_info,
                            .dataTables_wrapper .dataTables_paginate {
                                text-align: center;
                                display: block;
                                margin-top: 0.5rem;
                                margin-bottom: 0.5rem;
                                float: none;
                            }
                            
                            .dataTables_wrapper .dataTables_filter input {
                                margin-left: 0;
                                width: 100%;
                                max-width: 300px;
                            }
                            
                            table.dataTable.dtr-inline.collapsed > tbody > tr > td.dtr-control:before,
                            table.dataTable.dtr-inline.collapsed > tbody > tr > th.dtr-control:before {
                                background-color: #198754;
                                margin-top: -3px;
                                box-shadow: 0 0 0.25rem rgba(0, 0, 0, 0.1);
                            }
                            
                            .dt-responsive tbody tr {
                                border-bottom: 1px solid #f0f0f0;
                            }
                            
                            /* تنسيق البيانات المفصلة عند النقر على الصف */
                            .dtr-details {
                                width: 100%;
                                margin-bottom: 0.5rem;
                            }
                            
                            .dtr-details li {
                                padding: 0.5rem 0;
                                border-bottom: 1px solid #f5f5f5;
                            }
                            
                            .dtr-title {
                                font-weight: 600;
                                margin-left: 0.5rem;
                            }
                        }
                        
                        /* تنسيق أزرار الترقيم والتنقل */
                        .dataTables_paginate .paginate_button {
                            padding: 0.25rem 0.5rem;
                            margin: 0 0.25rem;
                            border-radius: 0.25rem;
                        }
                        
                        .dataTables_paginate .paginate_button.current {
                            background-color: #0d6efd !important;
                            border-color: #0d6efd !important;
                            color: white !important;
                        }
                        
                        /* تنسيق مجموع الجدول */
                        #purchasesDetailsTable tfoot {
                            border-top: 2px solid #dee2e6;
                        }
                        
                        #purchasesDetailsTable tfoot td {
                            padding: 0.75rem;
                        }
                    </style>
                `);
        }
    }

    // دالة لعرض جدول تفاصيل المشتريات والخدمات
    function renderPurchasesTable(data) {
        // إضافة أنماط CSS مخصصة للجداول الرسبونسف
        addCustomResponsiveStyles();
        
        // تحقق من وجود قابلية تهيئة DataTable
        if ($.fn.DataTable) {
            // إذا كان الجدول موجودًا، تدميره أولاً
            if ($.fn.DataTable.isDataTable('#purchasesDetailsTable')) {
                $('#purchasesDetailsTable').DataTable().destroy();
            }
            
            // حساب المجموع الكلي للمشتريات
            const totalAmount = data.reduce((sum, item) => sum + parseFloat(item.total), 0);
                
                // إنشاء جدول مع DataTables
                $('#purchasesDetailsTable').DataTable({
                    data: data,
                    responsive: true,
                    autoWidth: false,
                    columns: [
                        { 
                            data: 'invoice_number',
                            responsivePriority: 3,
                            className: 'text-center text-nowrap'
                        },
                        { 
                            data: 'invoice_date',
                            render: function(data) {
                                return moment(data).format('YYYY/MM/DD');
                            },
                            responsivePriority: 4,
                            className: 'text-center text-nowrap'
                        },
                        { 
                            data: 'type',
                            render: function(data) {
                                return data === 'service' ? 'خدمة' : (data === 'product' ? 'منتج' : data);
                            },
                            responsivePriority: 5,
                            className: 'text-center'
                        },
                        { 
                            data: 'item_name',
                            responsivePriority: 1
                        },
                        { 
                            data: 'category_name',
                            responsivePriority: 6
                        },
                        { 
                            data: 'quantity',
                            responsivePriority: 7,
                            className: 'text-center'
                        },
                        { 
                            data: 'price',
                            render: function(data) {
                                return parseFloat(data).toFixed(2) + ' ' + currencySymbol;
                            },
                            responsivePriority: 8,
                            className: 'text-center text-nowrap'
                        },
                        { 
                            data: 'discount',
                            render: function(data) {
                                return parseFloat(data).toFixed(2) + ' ' + currencySymbol;
                            },
                            responsivePriority: 9,
                            className: 'text-center text-nowrap'
                        },
                        { 
                            data: 'total',
                            render: function(data) {
                                return parseFloat(data).toFixed(2) + ' ' + currencySymbol;
                            },
                            responsivePriority: 2,
                            className: 'text-center fw-bold text-nowrap'
                        }
                    ],
                    ordering: true,
                    order: [[1, 'desc']], // ترتيب حسب التاريخ تنازليًا
                    searching: true,
                    paging: true,
                    lengthChange: true,
                    pageLength: 10,
                    language: {
                        url: 'https://cdn.datatables.net/plug-ins/1.11.5/i18n/ar.json'
                    },
                    dom: '<"row mb-3"<"col-md-6"l><"col-md-6 text-start"f>>rtip',
                    buttons: [],
                    "initComplete": function() {
                        // تحديث إجمالي القيمة في الصف الإجمالي
                        $('#total-amount-cell').html(totalAmount.toFixed(2) + ' ' + currencySymbol);
                        
                        // تحسين مظهر البحث
                        $('.dataTables_filter input').addClass('form-control form-control-sm ms-2');
                        $('.dataTables_length select').addClass('form-select form-select-sm me-2');
                    }
                });
            } else {
                // إذا لم تكن مكتبة DataTables متاحة، استخدم عرض جدول بسيط
                const tableBody = $('#purchasesDetailsTable tbody');
                tableBody.empty();
                
                // حساب المجموع الكلي
                const totalAmount = data.reduce((sum, item) => sum + parseFloat(item.total), 0);
                
                // إضافة صفوف البيانات
                data.forEach(function(item) {
                    const row = `
                        <tr>
                            <td>${item.invoice_number}</td>
                            <td>${moment(item.invoice_date).format('YYYY/MM/DD')}</td>
                            <td>${item.type === 'service' ? 'خدمة' : (item.type === 'product' ? 'منتج' : item.type)}</td>
                            <td>${item.item_name}</td>
                            <td>${item.category_name}</td>
                            <td>${item.quantity}</td>
                            <td>${parseFloat(item.price).toFixed(2)} ${currencySymbol}</td>
                            <td>${parseFloat(item.discount).toFixed(2)} ${currencySymbol}</td>
                            <td>${parseFloat(item.total).toFixed(2)} ${currencySymbol}</td>
                        </tr>
                    `;
                    tableBody.append(row);
                });
                
                // إضافة صف الإجمالي
                const totalRow = `
                    <tr class="fw-bold table-light">
                        <td colspan="8">الإجمالي</td>
                        <td>${totalAmount.toFixed(2)} ${currencySymbol}</td>
                    </tr>
                `;
                tableBody.append(totalRow);
            }
            
                    // تم إلغاء خاصية التصدير
        }

        // دالة لرسم جدول للمشتريات الشهرية
        function renderMonthlyChart(data) {
            // تحقق من وجود قابلية تهيئة DataTable
            if ($.fn.DataTable) {
                // إذا كان الجدول موجودًا، تدميره أولاً
                if ($.fn.DataTable.isDataTable('#monthlySpendingTable')) {
                    $('#monthlySpendingTable').DataTable().destroy();
                }
                
                // حساب المجموع الكلي للمشتريات
                const totalAmount = data.reduce((sum, item) => sum + parseFloat(item.total_amount), 0);
                
                // إنشاء جدول مع DataTables
                $('#monthlySpendingTable').DataTable({
                    data: data,
                    columns: [
                        { data: 'month_label' },
                        { 
                            data: 'total_amount',
                            render: function(data) {
                                return parseFloat(data).toFixed(2) + ' ' + currencySymbol;
                            }
                        }
                    ],
                    ordering: true,
                    order: [[0, 'asc']], // ترتيب حسب التاريخ تصاعديًا
                    searching: false,
                    paging: data.length > 10, // تمكين الصفحات فقط إذا كان هناك أكثر من 10 عناصر
                    lengthChange: false,
                    pageLength: 12,
                    language: {
                        url: 'https://cdn.datatables.net/plug-ins/1.11.5/i18n/ar.json'
                    },
                    dom: 'rtp',
                    "initComplete": function() {
                        // إضافة الصف الإجمالي في نهاية الجدول
                        let footer = '<tfoot><tr class="fw-bold table-light">';
                        footer += '<td>الإجمالي</td>';
                        footer += '<td>' + totalAmount.toFixed(2) + ' ' + currencySymbol + '</td>';
                        footer += '</tr></tfoot>';
                        
                        $(this).append(footer);
                    }
                });
            } else {
                // إذا لم تكن مكتبة DataTables متاحة، استخدم عرض جدول بسيط
                const tableBody = $('#monthlySpendingTable tbody');
                tableBody.empty();
                
                // حساب المجموع الكلي
                const totalAmount = data.reduce((sum, item) => sum + parseFloat(item.total_amount), 0);
                
                // إضافة صفوف البيانات
                data.forEach(function(item) {
                    const row = `
                        <tr>
                            <td>${item.month_label}</td>
                            <td>${parseFloat(item.total_amount).toFixed(2)} ${currencySymbol}</td>
                        </tr>
                    `;
                    tableBody.append(row);
                });
                
                // إضافة صف الإجمالي
                const totalRow = `
                    <tr class="fw-bold table-light">
                        <td>الإجمالي</td>
                        <td>${totalAmount.toFixed(2)} ${currencySymbol}</td>
                    </tr>
                `;
                tableBody.append(totalRow);
            }
            
            // تم إلغاء خاصية التصدير
        }

        // تم إلغاء دالة تصدير الجدول إلى Excel

        // تم إلغاء خاصية تصدير الفواتير

        // فتح مودال إضافة نقاط الولاء
        $('#addLoyaltyPointsBtn').on('click', function() {
            $('#addLoyaltyPointsModal').modal('show');
        });

        // إضافة نقاط ولاء
        $('#submitAddLoyaltyPoints').on('click', function() {
            const form = $('#addLoyaltyPointsForm')[0];
            if (!form.checkValidity()) {
                form.reportValidity();
                return;
            }

            const customerId = $('#loyalty_customer_id').val();
            const points = $('#points').val();

            $.ajax({
                url: '../../api/customers.php',
                type: 'POST',
                data: {
                    action: 'add_loyalty_points',
                    customer_id: customerId,
                    points: points
                },
                dataType: 'json',
                success: function(response) {
                    if (response.status === 'success') {
                        $('#addLoyaltyPointsModal').modal('hide');
                        showAlert(response.message);
                        
                        // تحديث نقاط الولاء على الصفحة
                        setTimeout(function() {
                            location.reload();
                        }, 1500);
                    } else {
                        showAlert(response.message, 'danger');
                    }
                },
                error: function() {
                    showAlert('حدث خطأ أثناء إضافة نقاط الولاء', 'danger');
                }
            });
        });

        // معالج زر إرسال رسالة واتساب
        $(document).on('click', '#sendWhatsAppBtn', function() {
            console.log('تم النقر على زر إرسال رسالة واتساب');
            
            const customerId = $(this).data('customer-id');
            const customerPhone = $(this).data('customer-phone');
            const customerName = '<?php echo htmlspecialchars($customer['name']); ?>';
            const branchName = $(this).data('branch-name');
            
            console.log('بيانات العميل:', {
                id: customerId,
                phone: customerPhone,
                name: customerName,
                branch: branchName
            });
            
            // تحضير النافذة المنبثقة
            $('#recipient').val(customerPhone);
            
            // إعداد رسالة افتراضية فوراً
            let defaultMessage = `مرحبا ${customerName}،\n\nشكراً لزيارتك ${branchName}. نتمنى أن تكون قد استمتعت بخدماتنا.\n\nمع تحيات فريق الصالون`;
            $('#messageText').val(defaultMessage);
            
            // تحميل قوالب الرسائل - تعيين معالج الحدث مرة واحدة فقط
            $('#messageTemplate').off('change').on('change', function() {
                const template = $(this).val();
                let messageText = '';
                
                switch (template) {
                    case 'greeting':
                        messageText = `مرحبا {name}،\nشكراً لزيارتك ${branchName}. نتمنى أن تكون قد استمتعت بخدماتنا.\nنتطلع لرؤيتك مجدداً قريباً!`;
                        break;
                    case 'reminder':
                        messageText = `مرحبا {name}،\nهذا تذكير بموعدك القادم في ${branchName}.\nيرجى الاتصال بنا إذا كنت ترغب في إعادة جدولة الموعد.`;
                        break;
                    case 'promotion':
                        messageText = `مرحبا {name}،\nلدينا عرض خاص هذا الأسبوع في ${branchName}! احصل على خصم 15% على خدماتنا.\nالعرض ساري حتى نهاية الأسبوع. نتطلع لرؤيتك قريباً!`;
                        break;
                    case 'custom':
                        messageText = `مرحبا {name}،\n\n\nمع تحيات فريق ${branchName}`;
                        break;
                }
                
                // استبدال {name} باسم العميل
                messageText = messageText.replace(/{name}/g, customerName);
                
                $('#messageText').val(messageText);
            });
            
            // عرض النافذة المنبثقة
            try {
                if (typeof bootstrap !== 'undefined') {
                    // استخدام Bootstrap 5
                    var whatsAppModal = new bootstrap.Modal(document.getElementById('whatsAppModal'));
                    whatsAppModal.show();
                } else {
                    // استخدام jQuery للإصدارات السابقة
                    $('#whatsAppModal').modal('show');
                }
                console.log('تم محاولة عرض المودال');
            } catch (e) {
                console.error('خطأ عند محاولة عرض المودال:', e);
                alert('حدث خطأ عند فتح نافذة إرسال الرسالة. يرجى المحاولة مرة أخرى.');
            }
        });
        
        // معالج زر إرسال الرسالة
        $('#sendWhatsAppMessage').on('click', function() {
            const phone = $('#recipient').val().trim();
            const message = $('#messageText').val().trim();
            
            if (!phone) {
                alert('رقم الهاتف غير صحيح');
                return;
            }
            
            if (!message) {
                alert('الرجاء إدخال نص الرسالة');
                return;
            }
            
            // تغيير حالة الزر
            const $btn = $(this);
            $btn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin me-1"></i> جاري الإرسال...');
            
            // إرسال الرسالة
            sendCustomWhatsAppMessage(phone, message)
                .then(function(result) {
                    console.log('تم إرسال رسالة واتساب بنجاح');
                    $('#sendStatus').text('تم إرسال الرسالة بنجاح!').removeClass('text-danger').addClass('text-success').show();
                    
                    // إغلاق النافذة بعد ثانيتين
                    setTimeout(function() {
                        // عرض إشعار نجاح خارج المودال
                        showAlert('<i class="fab fa-whatsapp text-success me-2"></i> تم إرسال رسالة واتساب بنجاح إلى ' + customerName, 'success');
                        
                        // إعادة تعيين حالة الزر
                        $btn.prop('disabled', false).html('<i class="fab fa-whatsapp me-1"></i> إرسال الرسالة');
                        $('#sendStatus').hide();
                        
                        // إغلاق المودال - سيؤدي تلقائيًا لتحديث الصفحة بفضل معالج الحدث hidden.bs.modal
                        $('#whatsAppModal').modal('hide');
                    }, 100);
                })
                .catch(function(error) {
                    console.error('خطأ في إرسال رسالة واتساب:', error);
                    $('#sendStatus').text('فشل إرسال الرسالة. تأكد من تشغيل خادم الواتساب المحلي.').removeClass('text-success').addClass('text-danger').show();
                    $btn.prop('disabled', false).html('<i class="fab fa-whatsapp me-1"></i> إعادة المحاولة');
                    
                    // عرض إشعار فشل خارج المودال
                    showAlert('<i class="fab fa-whatsapp text-danger me-2"></i> فشل إرسال رسالة واتساب. تأكد من تشغيل خادم الواتساب المحلي.', 'danger');
                });
        });
    });
    
    // دالة لإرسال رسالة واتساب مخصصة باستخدام نفس طريقة WhatsAppInvoiceNotification
    function sendCustomWhatsAppMessage(phone, message) {
        return new Promise((resolve, reject) => {
            const serverUrl = 'http://localhost:3000';
            const timeout = 60000; // 60 seconds timeout
            
            try {
                console.log(`جاري إرسال رسالة واتساب إلى ${phone}`);
                
                // التحقق من وجود رقم هاتف صحيح
                if (!phone || phone === 'null' || phone === 'undefined' || phone.trim() === '') {
                    console.error('لا يمكن إرسال الرسالة: رقم الهاتف غير متوفر');
                    return reject(new Error('رقم الهاتف غير متوفر'));
                }
                
                // تنسيق رقم الهاتف بشكل صحيح للواتساب
                const formattedPhone = formatPhoneNumber(phone);
                console.log(`الرقم بعد التنسيق: ${formattedPhone}`);
                
                // التحقق من صحة الرقم بعد التنسيق
                if (!formattedPhone || formattedPhone.trim() === '') {
                    console.error('لا يمكن إرسال الرسالة: رقم الهاتف غير صالح بعد التنسيق');
                    return reject(new Error('رقم الهاتف غير صالح بعد التنسيق'));
                }
                
                // إرسال الرسالة إلى خادم الواتساب المحلي
                const xhr = new XMLHttpRequest();
                xhr.open('POST', `${serverUrl}/send`, true);
                xhr.setRequestHeader('Content-Type', 'application/json');
                xhr.responseType = 'json';
                xhr.timeout = timeout;
                
                xhr.onload = () => {
                    if (xhr.status === 200) {
                        let response;
                        
                        // معالجة الردود المختلفة
                        if (typeof xhr.response === 'object') {
                            response = xhr.response;
                        } else if (typeof xhr.responseText === 'string') {
                            try {
                                response = JSON.parse(xhr.responseText);
                            } catch (e) {
                                response = { status: 'error', message: 'استجابة غير صالحة من الخادم' };
                            }
                        } else {
                            response = { status: 'error', message: 'استجابة غير معروفة من الخادم' };
                        }
                        
                        if (response && response.status === 'success') {
                            console.log(`تم إرسال الرسالة بنجاح إلى ${formattedPhone}:`, response);
                            resolve(response);
                        } else {
                            const errorMsg = response ? response.message : 'خطأ غير معروف عند إرسال الرسالة';
                            console.error(`خطأ عند إرسال الرسالة إلى ${formattedPhone}: ${errorMsg}`);
                            reject(new Error(errorMsg));
                        }
                    } else {
                        const errorMsg = `خطأ عند إرسال الرسالة: ${xhr.status}`;
                        console.error(errorMsg);
                        reject(new Error(errorMsg));
                    }
                };
                
                xhr.ontimeout = () => {
                    const errorMsg = 'انتهت مهلة الانتظار عند إرسال الرسالة';
                    console.error(errorMsg);
                    
                    // رغم انتهاء المهلة، قد تكون الرسالة قد أرسلت بنجاح
                    console.warn('قد تكون الرسالة قد أرسلت بنجاح رغم انتهاء مهلة الانتظار');
                    resolve({
                        status: 'warning',
                        message: 'انتهت مهلة الانتظار، لكن الرسالة قد تكون أرسلت بنجاح'
                    });
                };
                
                xhr.onerror = () => {
                    const errorMsg = 'خطأ في الشبكة عند إرسال الرسالة';
                    console.error(errorMsg);
                    reject(new Error(errorMsg));
                };
                
                // إرسال البيانات
                const sendData = {
                    phone: formattedPhone,
                    message: message
                };
                
                console.log('إرسال البيانات إلى الخادم:', sendData);
                xhr.send(JSON.stringify(sendData));
                
            } catch (error) {
                console.error('خطأ عند إرسال الرسالة:', error);
                reject(error);
            }
        });
    }
    
    // دالة لتنسيق رقم الهاتف للواتساب (الصيغة المصرية)
    function formatPhoneNumber(phone) {
        if (!phone) return '';
        
        console.log('رقم الهاتف الأصلي:', phone);
        
        // إزالة جميع الأحرف غير الرقمية باستثناء علامة +
        let cleaned = phone.replace(/[^\d+]/g, '');
        console.log('بعد إزالة الأحرف غير الرقمية:', cleaned);
        
        // تحويل الرقم لصيغة مصرية (+20)
        
        // إذا كان الرقم يبدأ بـ +20 بالفعل، نتركه كما هو
        if (cleaned.startsWith('+20')) {
            console.log('الرقم بالفعل بالصيغة المصرية الصحيحة');
        }
        // إذا كان الرقم يبدأ بـ 20، نضيف + فقط
        else if (cleaned.startsWith('20')) {
            if (cleaned.length > 2 && cleaned.charAt(2) === '0') {
                // إزالة الصفر بعد كود الدولة إذا وجد (مثال: 200xxx -> 20xxx)
                cleaned = '+20' + cleaned.substring(3);
            } else {
                cleaned = '+' + cleaned;
            }
        }
        // إذا كان يبدأ بـ 0، نزيل الصفر ونضيف +20 
        else if (cleaned.startsWith('0')) {
            cleaned = '+20' + cleaned.substring(1);
        }
        // إذا كان رقم بدون كود ولا 0 في البداية، نضيف +20 مباشرة
        else {
            // التحقق من طول الرقم للتأكد من أنه رقم مصري
            if (cleaned.length >= 10 && cleaned.length <= 12) {
                cleaned = '+20' + cleaned;
            } else {
                // إذا كان الطول غير عادي، نعالجه كرقم مصري بحذر
                cleaned = '+20' + cleaned;
                console.log('تحذير: طول الرقم غير اعتيادي. تم معالجته كرقم مصري');
            }
        }
        
        console.log('الرقم بعد التنسيق (بكود مصر):', cleaned);
        
        // التأكد من أن الرقم منسق بشكل صحيح للواتساب - إزالة + 
        let result = cleaned.replace(/^\+/, '');
        console.log('الرقم النهائي لإرسال واتساب:', result);
        
        return result;
    }
    
    // دالة لعرض إشعار في أعلى الصفحة
    function showAlert(message, type = 'success') {
        // إنشاء عنصر الإشعار
        const alertDiv = $(`
            <div class="alert alert-${type} alert-dismissible fade show mb-3" role="alert">
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        `);
        
        // معرفة المكان المناسب لوضع الإشعار
        const container = $('.container-fluid').first();
        
        // إضافة الإشعار في بداية الصفحة
        if (container.length) {
            alertDiv.prependTo(container);
        } else {
            // إذا لم نجد الحاوية، نضيفه للجسم مباشرة
            const alertContainer = $('#alerts-container');
            if (alertContainer.length) {
                alertDiv.appendTo(alertContainer);
            } else {
                $('body').prepend($('<div id="alerts-container" class="container mt-3"></div>').append(alertDiv));
            }
        }
        
        // إخفاء الإشعار تلقائيًا بعد 5 ثوان
        setTimeout(function() {
            alertDiv.alert('close');
        }, 5000);
    }

</script>

<!-- إدراج قالب الفوتر -->
<?php include_once __DIR__ . '/../../includes/templates/footer.php'; ?> 