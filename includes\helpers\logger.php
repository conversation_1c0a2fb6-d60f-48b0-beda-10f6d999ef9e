<?php
/**
 * ملف مساعد للتسجيل
 * 
 * يوفر دوال مساعدة لتسجيل الأحداث والأخطاء في ملفات السجل
 */

// التأكد من عدم الوصول المباشر للملف
if (!defined('BASEPATH')) {
    exit('لا يمكن الوصول إلى هذا الملف مباشرة');
}

/**
 * تسجيل رسالة في ملف السجل
 * 
 * @param string $message الرسالة المراد تسجيلها
 * @param string $level مستوى السجل (info, warning, error, debug)
 * @param string $file اسم ملف السجل (بدون امتداد)
 * @return bool نجاح أو فشل عملية التسجيل
 */
function log_message($message, $level = 'info', $file = 'system') {
    // تحديد مسار مجلد السجلات
    $logDir = dirname(dirname(dirname(__FILE__))) . '/logs/';
    
    // التأكد من وجود المجلد
    if (!is_dir($logDir)) {
        mkdir($logDir, 0755, true);
    }
    
    // تنسيق الرسالة
    $date = date('Y-m-d H:i:s');
    $formattedMessage = "[{$date}] [{$level}] {$message}" . PHP_EOL;
    
    // تحديد اسم الملف
    $filename = $logDir . $file . '_' . date('Y-m-d') . '.log';
    
    // كتابة الرسالة في الملف
    return file_put_contents($filename, $formattedMessage, FILE_APPEND);
}

/**
 * تسجيل خطأ في ملف سجل الأخطاء
 * 
 * @param string $message رسالة الخطأ
 * @param Exception|null $exception كائن الاستثناء (اختياري)
 * @return bool نجاح أو فشل عملية التسجيل
 */
function log_error($message, $exception = null) {
    // تسجيل رسالة الخطأ
    $result = log_message($message, 'error', 'error');
    
    // إذا تم تمرير كائن استثناء، قم بتسجيل تفاصيله
    if ($exception instanceof Exception) {
        log_message('Exception: ' . $exception->getMessage(), 'error', 'error');
        log_message('Stack Trace: ' . $exception->getTraceAsString(), 'error', 'error');
    }
    
    return $result;
}

/**
 * تسجيل معلومات تصحيح في ملف سجل التصحيح
 * 
 * @param string $message رسالة التصحيح
 * @param array $data بيانات إضافية للتصحيح (اختياري)
 * @return bool نجاح أو فشل عملية التسجيل
 */
function log_debug($message, $data = null) {
    // تسجيل رسالة التصحيح
    $result = log_message($message, 'debug', 'debug');
    
    // إذا تم تمرير بيانات إضافية، قم بتسجيلها
    if ($data !== null) {
        log_message('Debug Data: ' . json_encode($data, JSON_UNESCAPED_UNICODE), 'debug', 'debug');
    }
    
    return $result;
}
