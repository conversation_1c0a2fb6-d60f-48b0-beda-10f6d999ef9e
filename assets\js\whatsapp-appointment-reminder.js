/**
 * WhatsApp Appointment Reminder
 * Este script maneja el envío de recordatorios de citas a través de WhatsApp
 * directamente al servidor local de WhatsApp en localhost:3000, sin pasar por la API
 */

const WhatsAppAppointmentReminder = {
    // URL del servidor local de WhatsApp
    serverUrl: 'http://localhost:3000',

    // URL base de la API del sistema
    apiUrl: BASE_URL + 'api/',

    // Estado de la última operación
    lastOperationStatus: null,

    // Conjunto para rastrear citas ya notificadas y evitar duplicados
    notifiedAppointments: new Set(),

    // Opciones de configuración
    options: {
        debug: true,                // Habilitar mensajes de depuración
        maxRetries: 2,              // Número máximo de reintentos
        retryDelay: 2000,           // Tiempo entre reintentos (ms)
        timeout: 30000,             // Tiempo de espera para solicitudes (ms)
        showSuccessMessage: true    // Mostrar mensaje de éxito al usuario
    },

    /**
     * Inicializar el módulo
     * @param {Object} options Opciones de configuración
     */
    init: function(options) {
        // Combinar opciones proporcionadas con las predeterminadas
        if (options) {
            this.options = { ...this.options, ...options };
        }

        console.log('WhatsAppAppointmentReminder inicializado con opciones:', this.options);
        console.log('URL del servidor local de WhatsApp:', this.serverUrl);
        console.log('URL base de la API del sistema:', this.apiUrl);
    },

    /**
     * Enviar recordatorio de cita
     * @param {number} appointmentId ID de la cita
     * @returns {Promise<Object>} Resultado de la operación
     */
    sendAppointmentReminder: function(appointmentId) {
        return new Promise((resolve, reject) => {
            console.log(`=== INICIO PROCESO DE ENVÍO DE RECORDATORIO PARA CITA #${appointmentId} ===`);

            // Verificar si ya se ha enviado un recordatorio para esta cita
            if (this.notifiedAppointments.has(appointmentId)) {
                console.log(`DUPLICADO DETECTADO: Recordatorio para cita #${appointmentId} ya fue enviado anteriormente.`);
                console.log(`Citas ya notificadas: [${Array.from(this.notifiedAppointments).join(', ')}]`);
                resolve({ status: 'success', message: 'Recordatorio ya enviado anteriormente' });
                return;
            }

            console.log(`Preparando recordatorio de WhatsApp para cita #${appointmentId}`);

            // Obtener los datos de la cita desde la API
            this._getAppointmentData(appointmentId)
                .then(appointmentData => {
                    console.log('Datos de cita obtenidos:', appointmentData);

                    // Verificar que tenemos los datos necesarios
                    if (!appointmentData.phone) {
                        throw new Error('No hay número de teléfono disponible para esta cita');
                    }

                    // Enviar el mensaje de WhatsApp
                    return this._sendWhatsAppMessage(
                        appointmentData.phone,
                        appointmentData.message,
                        appointmentId
                    );
                })
                .then(result => {
                    console.log(`Recordatorio enviado con éxito para cita #${appointmentId}:`, result);

                    // Marcar esta cita como ya notificada
                    this.notifiedAppointments.add(appointmentId);
                    console.log(`Cita #${appointmentId} marcada como notificada correctamente`);

                    // Mostrar mensaje de éxito si está habilitado
                    if (this.options.showSuccessMessage) {
                        this._showSuccessMessage();
                    }

                    this.lastOperationStatus = 'success';
                    console.log(`=== FIN PROCESO DE ENVÍO DE RECORDATORIO PARA CITA #${appointmentId} - ÉXITO ===`);
                    resolve({ status: 'success', message: 'Recordatorio enviado correctamente' });
                })
                .catch(error => {
                    console.error(`Error al enviar recordatorio para cita #${appointmentId}:`, error);

                    // Verificar si es un error de timeout
                    if (error.message && error.message.includes('Tiempo de espera agotado')) {
                        console.warn('Error de timeout - El recordatorio podría haberse enviado correctamente');

                        this.lastOperationStatus = 'warning';
                        console.log(`=== FIN PROCESO DE ENVÍO DE RECORDATORIO PARA CITA #${appointmentId} - ADVERTENCIA (TIMEOUT) ===`);
                        resolve({
                            status: 'warning',
                            message: 'Tiempo de espera agotado, pero el recordatorio podría haberse enviado correctamente'
                        });
                    } else {
                        this.lastOperationStatus = 'error';
                        console.log(`=== FIN PROCESO DE ENVÍO DE RECORDATORIO PARA CITA #${appointmentId} - ERROR ===`);
                        reject(error);
                    }
                });
        });
    },

    /**
     * Obtener datos de la cita desde la API
     * @private
     * @param {number} appointmentId ID de la cita
     * @returns {Promise<Object>} Datos de la cita
     */
    _getAppointmentData: function(appointmentId) {
        return new Promise((resolve, reject) => {
            console.log(`Obteniendo datos para la cita #${appointmentId}`);

            // Intentar obtener los datos directamente sin usar la API
            this._getAppointmentDataDirect(appointmentId)
                .then(data => {
                    console.log('Datos obtenidos directamente:', data);
                    resolve(data);
                })
                .catch(error => {
                    console.warn('No se pudieron obtener los datos directamente, intentando con la API:', error);

                    // Si falla, intentar con la API
                    // Realizar una solicitud a la API para obtener los datos de la cita
                    const xhr = new XMLHttpRequest();
                    xhr.open('POST', `${this.apiUrl}whatsapp.php`, true);
                    xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');
                    xhr.responseType = 'json';
                    xhr.timeout = this.options.timeout;

                    xhr.onload = () => {
                        if (xhr.status === 200) {
                            const response = xhr.response;
                            console.log('Respuesta completa de la API:', response);

                            if (response && response.status === 'success' && response.data) {
                                // Formatear el número de teléfono para WhatsApp
                                const formattedPhone = this._formatPhoneNumber(response.data.phone);

                                // Devolver los datos formateados
                                resolve({
                                    phone: formattedPhone,
                                    message: response.data.message,
                                    raw: response.data
                                });
                            } else {
                                const errorMsg = response ? response.message : 'Error desconocido al obtener datos de la cita';
                                console.error(`Error al obtener datos de la cita #${appointmentId}: ${errorMsg}`);
                                reject(new Error(errorMsg));
                            }
                        } else {
                            const errorMsg = `Error al obtener datos de la cita: ${xhr.status}`;
                            console.error(errorMsg);
                            reject(new Error(errorMsg));
                        }
                    };

                    xhr.ontimeout = () => {
                        const errorMsg = 'Tiempo de espera agotado al obtener datos de la cita';
                        console.error(errorMsg);
                        reject(new Error(errorMsg));
                    };

                    xhr.onerror = (e) => {
                        const errorMsg = 'Error de red al obtener datos de la cita';
                        console.error(errorMsg, e);
                        reject(new Error(errorMsg));
                    };

                    // Preparar los datos para enviar
                    const formData = new URLSearchParams();
                    formData.append('action', 'send_appointment_reminder');
                    formData.append('appointment_id', appointmentId);

                    console.log('Enviando solicitud POST a la API con datos:', {
                        action: 'send_appointment_reminder',
                        appointment_id: appointmentId
                    });

                    // Enviar la solicitud
                    xhr.send(formData.toString());
                });
        });
    },

    /**
     * Obtener datos de la cita directamente del DOM (sin usar la API)
     * @private
     * @param {number} appointmentId ID de la cita
     * @returns {Promise<Object>} Datos de la cita
     */
    _getAppointmentDataDirect: function(appointmentId) {
        return new Promise((resolve, reject) => {
            console.log(`Intentando obtener datos directamente para la cita #${appointmentId}`);

            // Verificar si estamos en la página de detalles de la cita
            if (document.getElementById('appointment_id') && document.getElementById('appointment_id').value == appointmentId) {
                // Estamos en la página de detalles, obtener los datos del DOM
                const name = document.getElementById('customer_name') ? document.getElementById('customer_name').textContent.trim() : '';
                const phone = document.getElementById('customer_phone') ? document.getElementById('customer_phone').textContent.trim() : '';
                const date = document.getElementById('appointment_date') ? document.getElementById('appointment_date').textContent.trim() : '';
                const time = document.getElementById('appointment_time') ? document.getElementById('appointment_time').textContent.trim() : '';

                if (!phone || phone === '-') {
                    reject(new Error('No hay número de teléfono disponible para esta cita'));
                    return;
                }

                // Formatear el número de teléfono para WhatsApp
                const formattedPhone = this._formatPhoneNumber(phone);

                // Crear el mensaje
                const message = `مرحبا ${name}\n` +
                                `نود تذكيركم بموعدكم القادم في صالون البدرواي\n` +
                                `التاريخ: ${date}\n` +
                                `الوقت: ${time}\n` +
                                `نتطلع لرؤيتكم!`;

                // Devolver los datos formateados
                resolve({
                    phone: formattedPhone,
                    message: message,
                    raw: {
                        phone: phone,
                        name: name,
                        date: date,
                        time: time
                    }
                });
            } else {
                // Verificar si estamos en la página de lista de citas
                const appointmentRow = document.querySelector(`[data-id="${appointmentId}"]`);
                if (appointmentRow) {
                    const name = appointmentRow.getAttribute('data-name') || '';
                    const phone = appointmentRow.getAttribute('data-phone') || '';
                    const date = appointmentRow.getAttribute('data-date') || '';
                    const time = appointmentRow.getAttribute('data-time') || '';

                    if (!phone || phone === '-') {
                        reject(new Error('No hay número de teléfono disponible para esta cita'));
                        return;
                    }

                    // Formatear el número de teléfono para WhatsApp
                    const formattedPhone = this._formatPhoneNumber(phone);

                    // Crear el mensaje
                    const message = `مرحبا ${name}\n` +
                                    `نود تذكيركم بموعدكم القادم في صالون البدرواي\n` +
                                    `التاريخ: ${date}\n` +
                                    `الوقت: ${time}\n` +
                                    `نتطلع لرؤيتكم!`;

                    // Devolver los datos formateados
                    resolve({
                        phone: formattedPhone,
                        message: message,
                        raw: {
                            phone: phone,
                            name: name,
                            date: date,
                            time: time
                        }
                    });
                } else {
                    reject(new Error('No se encontraron datos de la cita en la página actual'));
                }
            }
        });
    },

    /**
     * Enviar mensaje de WhatsApp a través del servidor local
     * @private
     * @param {string} phone Número de teléfono del destinatario
     * @param {string} message Mensaje a enviar
     * @param {number} appointmentId ID de la cita (para referencia)
     * @returns {Promise<Object>} Resultado del envío
     */
    _sendWhatsAppMessage: function(phone, message, appointmentId) {
        return new Promise((resolve, reject) => {
            try {
                console.log(`Enviando mensaje de WhatsApp a ${phone} para cita #${appointmentId}`);

                // Verificar que el servidor local esté configurado correctamente
                if (!this.serverUrl || !this.serverUrl.startsWith('http')) {
                    console.error(`URL del servidor local inválida: ${this.serverUrl}`);
                    throw new Error(`URL del servidor local inválida: ${this.serverUrl}`);
                }

                // Realizar una solicitud al servidor local para enviar el mensaje
                const xhr = new XMLHttpRequest();
                const url = `${this.serverUrl}/send`;
                console.log(`URL de envío de mensaje: ${url}`);

                xhr.open('POST', url, true);
                xhr.setRequestHeader('Content-Type', 'application/json');
                xhr.responseType = 'json';
                xhr.timeout = this.options.timeout;

                xhr.onload = () => {
                    console.log(`Respuesta recibida, código de estado: ${xhr.status}`);

                    if (xhr.status === 200) {
                        let response;
                        console.log('Respuesta completa:', xhr.response);

                        // Manejar diferentes tipos de respuestas
                        if (typeof xhr.response === 'object') {
                            response = xhr.response;
                        } else if (typeof xhr.responseText === 'string') {
                            try {
                                response = JSON.parse(xhr.responseText);
                            } catch (e) {
                                console.error('Error al analizar la respuesta JSON:', e);
                                response = { status: 'error', message: 'Respuesta no válida del servidor' };
                            }
                        } else {
                            console.warn('Tipo de respuesta desconocido:', typeof xhr.response);
                            response = { status: 'error', message: 'Respuesta desconocida del servidor' };
                        }

                        if (response && response.status === 'success') {
                            console.log(`¡Éxito! Mensaje enviado a ${phone}:`, response);
                            resolve(response);
                        } else {
                            const errorMsg = response ? response.message : 'Error desconocido al enviar mensaje';
                            console.error(`Error al enviar mensaje a ${phone}: ${errorMsg}`);
                            reject(new Error(errorMsg));
                        }
                    } else {
                        const errorMsg = `Error al enviar mensaje: ${xhr.status}`;
                        console.error(errorMsg);
                        reject(new Error(errorMsg));
                    }
                };

                xhr.ontimeout = () => {
                    const errorMsg = 'Tiempo de espera agotado al enviar mensaje';
                    console.error(errorMsg);

                    // A pesar del timeout, el mensaje podría haberse enviado correctamente
                    console.warn('El mensaje podría haberse enviado a pesar del timeout');
                    resolve({
                        status: 'warning',
                        message: 'Tiempo de espera agotado, pero el mensaje podría haberse enviado correctamente'
                    });
                };

                xhr.onerror = (e) => {
                    const errorMsg = 'Error de red al enviar mensaje';
                    console.error(errorMsg, e);
                    reject(new Error(errorMsg));
                };

                // Enviar los datos
                const sendData = {
                    phone: phone,
                    message: message
                };

                console.log('Enviando datos al servidor:', sendData);
                xhr.send(JSON.stringify(sendData));
            } catch (error) {
                console.error('Error inesperado al enviar mensaje:', error);
                reject(error);
            }
        });
    },

    /**
     * Formatear número de teléfono para WhatsApp
     * @private
     * @param {string} phone Número de teléfono a formatear
     * @returns {string} Número de teléfono formateado
     */
    _formatPhoneNumber: function(phone) {
        if (!phone) return '';

        // Eliminar todos los caracteres no numéricos excepto el signo +
        let cleaned = phone.replace(/[^\d+]/g, '');

        // Si el número no comienza con +, agregar el código de país para Egipto (+20)
        if (!cleaned.startsWith('+')) {
            // Si comienza con 0, eliminarlo
            if (cleaned.startsWith('0')) {
                cleaned = cleaned.substring(1);
            }

            // Si no comienza con 20 (código de Egipto), agregarlo
            if (!cleaned.startsWith('20')) {
                cleaned = '20' + cleaned;
            }
        } else {
            // Si comienza con +, eliminar el signo + para WhatsApp
            cleaned = cleaned.substring(1);
        }

        return cleaned;
    },

    /**
     * Mostrar mensaje de éxito
     * @private
     */
    _showSuccessMessage: function() {
        // Verificar si toastr está disponible
        if (typeof toastr !== 'undefined') {
            toastr.success('تم إرسال التذكير بنجاح');
        } else if (typeof showAlert === 'function') {
            showAlert('تم إرسال التذكير بنجاح', 'success');
        } else {
            alert('تم إرسال التذكير بنجاح');
        }
    }
};
