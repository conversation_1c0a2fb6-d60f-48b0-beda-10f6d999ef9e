<?php
/**
 * صفحة تعديل منتج
 * تسمح للمستخدمين بتعديل بيانات المنتجات الموجودة
 */

// تعريف ثابت لمنع الوصول المباشر للملفات المرتبطة
define('BASEPATH', true);

// استدعاء ملف التهيئة
require_once '../../config/init.php';

// التحقق من تسجيل الدخول
redirectIfNotLoggedIn();

// التحقق من صلاحية الوصول
if (!hasPermission('products_edit')) {
    $_SESSION['error_message'] = 'ليس لديك صلاحية لتعديل المنتجات';
    header('Location: ' . BASE_URL . 'pages/products/index.php');
    exit;
}

// التحقق من وجود معرف المنتج
if (!isset($_GET['id']) || empty($_GET['id'])) {
    $_SESSION['error_message'] = 'معرف المنتج غير صحيح';
    header('Location: ' . BASE_URL . 'pages/products/index.php');
    exit;
}

// الحصول على بيانات المنتج
$productId = (int)$_GET['id'];
$productObj = new Product($db);
$product = $productObj->getProductById($productId);

// التحقق من وجود المنتج
if (!$product) {
    $_SESSION['error_message'] = 'المنتج غير موجود';
    header('Location: ' . BASE_URL . 'pages/products/index.php');
    exit;
}

// التحقق من صلاحية الوصول للمنتج بناءً على الفرع
$currentBranchId = $_SESSION['user_branch_id'];
$isAdmin = $_SESSION['user_role'] === ROLE_ADMIN;

// إذا لم يكن المستخدم مديرًا، تحقق من أن المنتج ينتمي لنفس الفرع
if (!$isAdmin) {
    // إذا كان المنتج مخصصًا لفرع محدد وليس فرع المستخدم
    if ($product['branch_id'] !== null && $product['branch_id'] != $currentBranchId) {
        $_SESSION['error_message'] = 'ليس لديك صلاحية لتعديل منتجات فرع آخر';
        header('Location: ' . BASE_URL . 'pages/products/index.php');
        exit;
    }
}

// تحديد المتغيرات الإضافية

// الحصول على فئات المنتجات
$categories = $productObj->getProductCategories();

// الحصول على رمز العملة من الإعدادات
$settingsModel = new Settings($db);
$currencySymbol = $settingsModel->get('system_currency_symbol', 'ر.س');
$currencyName = $settingsModel->get('system_currency', 'ريال سعودي');

// الحصول على الفروع (للمدير فقط)
$branches = [];
if ($isAdmin) {
    $branchObj = new Branch($db);
    $branches = $branchObj->getBranches();
}

// عنوان الصفحة
$pageTitle = 'تعديل منتج: ' . $product['name'];

// استدعاء قالب الرأس
include_once '../../includes/templates/header.php';
?>

<!-- محتوى الصفحة -->
<div class="container-fluid py-4">
    <div class="container-fluid">
        <!-- عنوان الصفحة والأزرار -->
        <div class="row mb-4 align-items-center">
            <div class="col-md-6">
                <h4 class="fw-bold mb-2"><?php echo $pageTitle; ?></h4>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb mb-0">
                        <li class="breadcrumb-item">
                            <a href="<?php echo BASE_URL . 'pages/dashboard.php'; ?>" class="text-decoration-none">
                                <i class="fas fa-home me-1"></i>الرئيسية
                            </a>
                        </li>
                        <li class="breadcrumb-item">
                            <a href="<?php echo BASE_URL . 'pages/products/index.php'; ?>" class="text-decoration-none">
                                المنتجات
                            </a>
                        </li>
                        <li class="breadcrumb-item active" aria-current="page">تعديل منتج</li>
                    </ol>
                </nav>
            </div>
            <div class="col-md-6 text-md-end">
                <a href="<?php echo BASE_URL . 'pages/products/index.php'; ?>" class="btn btn-secondary">
                    <i class="fas fa-arrow-right me-1"></i> العودة للقائمة
                </a>
            </div>
        </div>

        <!-- بطاقة تعديل المنتج -->
        <div class="card shadow-sm border-0 mb-4">
            <div class="card-header bg-white py-3">
                <h5 class="mb-0 fw-bold text-primary">
                    <i class="fas fa-edit me-2"></i> تعديل بيانات المنتج
                </h5>
            </div>
            <div class="card-body">
                <form id="product-form" class="needs-validation" novalidate>
                    <!-- معرف المنتج (مخفي) -->
                    <input type="hidden" name="id" value="<?php echo $product['id']; ?>">

                    <!-- البيانات الأساسية -->
                    <div class="row mb-4">
                        <div class="col-md-12">
                            <h6 class="fw-bold mb-3 text-primary">
                                <i class="fas fa-info-circle me-1"></i> البيانات الأساسية
                            </h6>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="name" class="form-label required-field">اسم المنتج</label>
                            <input type="text" class="form-control" id="name" name="name" value="<?php echo htmlspecialchars($product['name']); ?>" required>
                            <div class="invalid-feedback">يرجى إدخال اسم المنتج</div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="category_id" class="form-label">الفئة</label>
                            <div class="input-group">
                                <select class="form-select" id="category_id" name="category_id">
                                    <option value="">بدون فئة</option>
                                    <?php foreach ($categories as $category): ?>
                                        <option value="<?php echo $category['id']; ?>" <?php echo ($product['category_id'] == $category['id']) ? 'selected' : ''; ?>>
                                            <?php echo htmlspecialchars($category['name']); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                                <button class="btn btn-outline-secondary" type="button" id="add-category-btn" title="إضافة فئة جديدة">
                                    <i class="fas fa-plus"></i>
                                </button>
                            </div>
                        </div>
                        <div class="col-md-12 mb-3">
                            <label for="description" class="form-label">وصف المنتج</label>
                            <textarea class="form-control" id="description" name="description" rows="3"><?php echo htmlspecialchars($product['description'] ?? ''); ?></textarea>
                        </div>
                    </div>

                    <!-- بيانات السعر والتكلفة -->
                    <div class="row mb-4">
                        <div class="col-md-12">
                            <h6 class="fw-bold mb-3 text-primary">
                                <i class="fas fa-money-bill-wave me-1"></i> بيانات السعر والتكلفة
                            </h6>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="price" class="form-label required-field">سعر البيع (<?php echo $currencySymbol; ?>)</label>
                            <input type="number" class="form-control" id="price" name="price" value="<?php echo $product['price']; ?>" min="0" step="0.01" required>
                            <div class="invalid-feedback">يرجى إدخال سعر صحيح</div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="cost" class="form-label">تكلفة الشراء (<?php echo $currencySymbol; ?>)</label>
                            <input type="number" class="form-control" id="cost" name="cost" value="<?php echo $product['cost'] ?? ''; ?>" min="0" step="0.01">
                            <div class="form-text">تكلفة شراء المنتج (اختياري)</div>
                        </div>
                    </div>

                    <!-- بيانات المخزون -->
                    <div class="row mb-4">
                        <div class="col-md-12">
                            <h6 class="fw-bold mb-3 text-primary">
                                <i class="fas fa-boxes me-1"></i> بيانات المخزون
                            </h6>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="min_quantity" class="form-label">الحد الأدنى للمخزون</label>
                            <input type="number" class="form-control" id="min_quantity" name="min_quantity" value="<?php echo $product['min_quantity'] ?? 5; ?>" min="0" step="1">
                            <div class="form-text">الحد الأدنى للتنبيه عند انخفاض المخزون</div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="new_quantity" class="form-label">الكمية الحالية</label>
                            <input type="number" class="form-control" id="new_quantity" name="new_quantity" value="<?php echo $product['current_stock'] ?? 0; ?>" min="0" step="1">
                            <div class="form-text">تعديل الكمية الحالية في المخزون</div>
                        </div>
                    </div>

                    <!-- إعدادات إضافية -->
                    <div class="row mb-4">
                        <div class="col-md-12">
                            <h6 class="fw-bold mb-3 text-primary">
                                <i class="fas fa-cog me-1"></i> إعدادات إضافية
                            </h6>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="is_for_sale" class="form-label">متاح للبيع</label>
                            <select class="form-select" id="is_for_sale" name="is_for_sale">
                                <option value="1" <?php echo ($product['is_for_sale'] == 1) ? 'selected' : ''; ?>>نعم</option>
                                <option value="0" <?php echo ($product['is_for_sale'] == 0) ? 'selected' : ''; ?>>لا</option>
                            </select>
                            <div class="form-text">هل المنتج متاح للبيع في نقاط البيع؟</div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="is_active" class="form-label">الحالة</label>
                            <select class="form-select" id="is_active" name="is_active">
                                <option value="1" <?php echo ($product['is_active'] == 1) ? 'selected' : ''; ?>>نشط</option>
                                <option value="0" <?php echo ($product['is_active'] == 0) ? 'selected' : ''; ?>>غير نشط</option>
                            </select>
                        </div>
                        <?php if ($isAdmin): ?>
                        <div class="col-md-6 mb-3">
                            <label for="branch_id" class="form-label">الفرع</label>
                            <select class="form-select" id="branch_id" name="branch_id">
                                <option value="">جميع الفروع</option>
                                <?php foreach ($branches as $branch): ?>
                                    <option value="<?php echo $branch['id']; ?>" <?php echo ($product['branch_id'] == $branch['id']) ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($branch['name']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                            <div class="form-text">اختر الفرع الذي يتوفر فيه هذا المنتج</div>
                        </div>
                        <?php else: ?>
                        <input type="hidden" name="branch_id" value="<?php echo $currentBranchId; ?>">
                        <?php endif; ?>
                    </div>

                    <!-- أزرار الإجراءات -->
                    <div class="row mt-4">
                        <div class="col-12 text-end">
                            <hr>
                            <a href="<?php echo BASE_URL . 'pages/products/index.php'; ?>" class="btn btn-outline-secondary me-2">
                                <i class="fas fa-times me-1"></i> إلغاء
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-1"></i> حفظ التغييرات
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- موديل إضافة فئة جديدة -->
<div class="modal fade" id="addCategoryModal" tabindex="-1" aria-labelledby="addCategoryModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addCategoryModalLabel">إضافة فئة جديدة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="إغلاق"></button>
            </div>
            <div class="modal-body">
                <form id="add-category-form">
                    <div class="mb-3">
                        <label for="category_name" class="form-label required-field">اسم الفئة</label>
                        <input type="text" class="form-control" id="category_name" name="name" required>
                    </div>
                    <div class="mb-3">
                        <label for="category_description" class="form-label">وصف الفئة</label>
                        <textarea class="form-control" id="category_description" name="description" rows="3"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" id="save-category-btn">حفظ</button>
            </div>
        </div>
    </div>
</div>

<!-- جافاسكربت خاص بالصفحة -->
<script>
    // سيتم تنفيذ هذا الكود بعد تحميل jQuery في ملف footer.php
    document.addEventListener('DOMContentLoaded', function() {
        // التأكد من تحميل jQuery قبل تنفيذ الكود
        function initializeProductPage() {
            // تهيئة نموذج المنتج
            if (typeof ProductManager !== 'undefined') {
                ProductManager.initProductForm();
            }

            // معالجة تقديم النموذج
            $('#product-form').on('submit', function(e) {
                e.preventDefault();

                // التحقق من صحة النموذج
                if (typeof ProductManager !== 'undefined' && !ProductManager.validateProductForm()) {
                    return false;
                }

                // بناء بيانات الفورم
                const formData = {
                    id: <?php echo $product['id']; ?>,
                    name: $('#name').val(),
                    description: $('#description').val(),
                    price: $('#price').val(),
                    cost: $('#cost').val(),
                    category_id: $('#category_id').val(),
                    is_for_sale: $('#is_for_sale').val(),
                    min_quantity: $('#min_quantity').val(),
                    is_active: $('#is_active').val(),
                    branch_id: $('select[name="branch_id"]').val() || $('input[name="branch_id"]').val(),
                    new_quantity: $('#new_quantity').val()
                };

                // إرسال البيانات باستخدام AJAX
                $.ajax({
                    url: '<?php echo API_URL; ?>products.php?action=update',
                    type: 'POST',
                    data: formData,
                    dataType: 'json',
                    beforeSend: function() {
                        // إظهار مؤشر التحميل
                        showLoading();
                    },
                    success: function(response) {
                        hideLoading();

                        if (response.success) {
                            // عرض رسالة النجاح
                            showToast('نجاح', response.message, 'success');

                            // إعادة توجيه المستخدم إلى صفحة المنتجات بعد ثانيتين
                            setTimeout(function() {
                                window.location.href = '<?php echo BASE_URL; ?>pages/products/index.php';
                            }, 2000);
                        } else {
                            // عرض رسالة الخطأ
                            showToast('خطأ', response.message, 'error');
                        }
                    },
                    error: function(xhr, status, error) {
                        hideLoading();
                        showToast('خطأ', 'حدث خطأ أثناء معالجة الطلب: ' + error, 'error');
                    }
                });
            });

            // معالجة إضافة فئة جديدة
            $('#add-category-btn').on('click', function() {
                $('#addCategoryModal').modal('show');
            });

            // حفظ الفئة الجديدة
            $('#save-category-btn').on('click', function() {
                const categoryName = $('#category_name').val();
                const categoryDescription = $('#category_description').val();

                if (!categoryName) {
                    alert('يرجى إدخال اسم الفئة');
                    return;
                }

                $.ajax({
                    url: '<?php echo API_URL; ?>products.php?action=add_category',
                    type: 'POST',
                    data: {
                        name: categoryName,
                        description: categoryDescription
                    },
                    dataType: 'json',
                    beforeSend: function() {
                        $('#save-category-btn').prop('disabled', true).html('<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> جاري الحفظ...');
                    },
                    success: function(response) {
                        $('#save-category-btn').prop('disabled', false).html('حفظ');

                        if (response.success) {
                            // إضافة الفئة الجديدة للقائمة المنسدلة
                            $('#category_id').append(new Option(response.data.name, response.data.id, true, true));

                            // إغلاق الموديل وإعادة تعيين النموذج
                            $('#addCategoryModal').modal('hide');
                            $('#add-category-form')[0].reset();

                            // عرض رسالة النجاح
                            showToast('نجاح', response.message, 'success');
                        } else {
                            // عرض رسالة الخطأ
                            showToast('خطأ', response.message, 'error');
                        }
                    },
                    error: function() {
                        $('#save-category-btn').prop('disabled', false).html('حفظ');
                        showToast('خطأ', 'حدث خطأ أثناء معالجة الطلب', 'error');
                    }
                });
            });
        }

        // التحقق من وجود jQuery وتنفيذ الكود عند تحميلها
        function checkJQuery() {
            if (window.jQuery) {
                // jQuery موجودة، تنفيذ الكود
                $(document).ready(function() {
                    initializeProductPage();
                });
            } else {
                // jQuery غير موجودة، إعادة المحاولة بعد 100 مللي ثانية
                setTimeout(checkJQuery, 100);
            }
        }

        // بدء التحقق من وجود jQuery
        checkJQuery();
    });

    // عرض مؤشر التحميل
    function showLoading() {
        if (window.jQuery) {
            if ($('#loading-overlay').length === 0) {
                $('body').append('<div id="loading-overlay" style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; background-color: rgba(0,0,0,0.5); z-index: 9999; display: flex; justify-content: center; align-items: center;"><div class="spinner-border text-light" role="status"><span class="visually-hidden">جاري التحميل...</span></div></div>');
            } else {
                $('#loading-overlay').show();
            }
        }
    }

    // إخفاء مؤشر التحميل
    function hideLoading() {
        if (window.jQuery) {
            $('#loading-overlay').hide();
        }
    }

    // عرض رسالة توست
    function showToast(title, message, type) {
        if (window.jQuery && typeof toastr !== 'undefined') {
            toastr.options = {
                closeButton: true,
                progressBar: true,
                positionClass: 'toast-top-left',
                timeOut: 3000
            };

            if (type === 'success') {
                toastr.success(message, title);
            } else if (type === 'error') {
                toastr.error(message, title);
            } else if (type === 'warning') {
                toastr.warning(message, title);
            } else {
                toastr.info(message, title);
            }
        } else {
            alert(title + ': ' + message);
        }
    }
</script>

<?php
// استدعاء قالب الذيل
include_once '../../includes/templates/footer.php';
?>
