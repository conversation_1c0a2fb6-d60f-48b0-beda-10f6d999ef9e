// دالة لعرض التنبيهات
function showAlert(message, type) {
    // إنشاء التنبيه باستخدام السلسلة العادية بدلاً من القوالب
    var alertHtml = '<div class="alert alert-' + type + ' alert-dismissible fade show" role="alert">' +
        message +
        '<button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>' +
        '</div>';

    // إضافة التنبيه في أعلى الصفحة
    $('#alerts-container').html(alertHtml);

    // إخفاء التنبيه تلقائيًا بعد 5 ثوانٍ
    setTimeout(function() {
        $('.alert').alert('close');
    }, 5000);
}
