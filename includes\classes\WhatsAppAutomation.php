<?php
/**
 * فئة أتمتة WhatsApp
 * تستخدم الخادم المحلي لإرسال رسائل WhatsApp من جانب العميل فقط
 * تم تحديث هذه الفئة لإزالة جميع الوظائف التي تعتمد على جانب الخادم
 */

// منع الوصول المباشر للملف
if (!defined('BASEPATH')) {
    exit('لا يمكن الوصول المباشر لهذا الملف');
}

class WhatsAppAutomation {
    private $db;
    private $settings;
    private $enabled;
    private $logFile;
    private $localServerUrl = 'http://localhost:3000'; // عنوان الخادم المحلي
    private $lastError; // آخر خطأ حدث

    /**
     * المُنشئ
     *
     * @param Database $db كائن قاعدة البيانات
     */
    public function __construct($db) {
        $this->db = $db;

        // تحميل إعدادات WhatsApp من ملف التكوين
        $configFile = dirname(__DIR__, 2) . '/config/whatsapp_config.php';
        if (file_exists($configFile)) {
            $config = require $configFile;

            // تعيين الإعدادات من ملف التكوين
            $this->logFile = $config['log_file'] ?? dirname(__DIR__, 2) . '/logs/whatsapp_automation.log';
            $this->localServerUrl = $config['local_server_url'] ?? 'http://localhost:3000';
        } else {
            // الإعدادات الافتراضية إذا لم يوجد ملف التكوين
            $this->logFile = dirname(__DIR__, 2) . '/logs/whatsapp_automation.log';
        }

        // التأكد من وجود مجلد السجلات
        $logDir = dirname($this->logFile);
        if (!is_dir($logDir)) {
            try {
                mkdir($logDir, 0755, true);
            } catch (Exception $e) {
                // إذا فشل إنشاء المجلد، نستخدم مجلد مؤقت
                $this->logFile = sys_get_temp_dir() . '/whatsapp_automation.log';
                error_log("فشل إنشاء مجلد السجلات. استخدام المجلد المؤقت: " . sys_get_temp_dir());
            }
        }

        // تحميل الإعدادات من قاعدة البيانات
        $this->loadSettings();

        // التأكد من أن مجلد السجلات موجود
        $logDir = dirname($this->logFile);
        if (!is_dir($logDir)) {
            try {
                mkdir($logDir, 0755, true);
            } catch (Exception $e) {
                // إذا فشل إنشاء المجلد، نستخدم مجلد مؤقت
                $this->logFile = sys_get_temp_dir() . '/whatsapp_automation.log';
                error_log("فشل إنشاء مجلد السجلات. استخدام المجلد المؤقت: " . sys_get_temp_dir());
            }
        }
    }

    /**
     * تحميل إعدادات WhatsApp من قاعدة البيانات
     */
    private function loadSettings() {
        try {
            $settingsModel = new Settings($this->db);
            $allSettings = $settingsModel->getAllSettings();

            // استخراج إعدادات WhatsApp
            $this->enabled = isset($allSettings['whatsapp_enabled']) ? (bool)$allSettings['whatsapp_enabled'] : false;

            // تسجيل حالة التحميل
            $this->log("تم تحميل إعدادات WhatsApp. الحالة: " . ($this->enabled ? 'مفعل' : 'غير مفعل'));
        } catch (Exception $e) {
            $this->log("خطأ في تحميل إعدادات WhatsApp: " . $e->getMessage());
            $this->enabled = false;
        }
    }

    /**
     * إرسال رسالة WhatsApp من جانب العميل فقط
     *
     * @param string $to رقم الهاتف المستلم (بتنسيق دولي بدون +)
     * @param string $message نص الرسالة
     * @param array $options خيارات إضافية
     * @return bool نجاح أو فشل الإرسال
     */
    public function sendMessage($to, $message, $options = []) {
        // التحقق من تفعيل الخدمة
        if (!$this->enabled) {
            $this->log("محاولة إرسال رسالة لكن الخدمة غير مفعلة. الرقم: $to");
            return false;
        }

        try {
            // تنسيق رقم الهاتف
            $to = $this->formatPhoneNumber($to, $options['country_code'] ?? '+20');

            // تسجيل محاولة الإرسال
            $this->log("محاولة إرسال رسالة إلى $to: " . substr($message, 0, 50) . "...");

            // إرسال الرسالة من جانب العميل
            try {
                // إعداد بيانات الرسالة
                $data = [
                    'phone' => $to,
                    'message' => $message
                ];

                // إرسال طلب إلى الخادم المحلي
                $response = $this->sendRequest('/send', $data);

                // التحقق من الاستجابة
                $success = $response && isset($response['status']) && $response['status'] === 'success';

                // التحقق من فتح نافذة WhatsApp
                if (!$success && isset($response['message']) && strpos($response['message'], 'waitForTimeout is not a function') !== false) {
                    // إذا كان الخطأ هو waitForTimeout، فهذا يعني أن الرسالة قد تم فتحها في WhatsApp ولكن الخادم المحلي لم يستطع إكمال العملية
                    $this->log("تم فتح نافذة WhatsApp للرقم $to ولكن هناك خطأ في الخادم المحلي: waitForTimeout is not a function");
                    $this->log("يرجى تحديث الخادم المحلي إلى الإصدار الجديد من صفحة الإعدادات");

                    // نعتبر العملية ناجحة لأن الرسالة تم فتحها في WhatsApp
                    return true;
                }

                if ($success) {
                    $this->log("تم إرسال الرسالة بنجاح من جانب العميل إلى: $to");
                } else {
                    $errorMessage = $response['message'] ?? 'خطأ غير معروف';

                    // التحقق من وجود خطأ waitForTimeout
                    if (strpos($errorMessage, 'waitForTimeout is not a function') !== false) {
                        $this->log("خطأ في الخادم المحلي: يجب تحديث الخادم المحلي إلى الإصدار الجديد");
                        $errorMessage .= ". يرجى تحديث الخادم المحلي إلى الإصدار الجديد من صفحة الإعدادات";
                    }

                    $this->log("فشل إرسال الرسالة من جانب العميل إلى: $to. السبب: $errorMessage");
                }

                return $success;
            } catch (Exception $e) {
                $this->log("خطأ في إرسال رسالة WhatsApp من جانب العميل إلى $to: " . $e->getMessage());
                return false;
            }
        } catch (Exception $e) {
            $this->log("خطأ في إرسال رسالة WhatsApp إلى $to: " . $e->getMessage());
            return false;
        }
    }





    /**
     * تنسيق رقم الهاتف للاستخدام مع WhatsApp
     *
     * @param string $phone رقم الهاتف
     * @param string $countryCode رمز البلد (اختياري)
     * @return string رقم الهاتف المنسق
     */
    public function formatPhoneNumber($phone, $countryCode = '+20') {
        // إزالة أي أحرف غير رقمية
        $phone = preg_replace('/[^0-9]/', '', $phone);

        // إزالة الصفر الأول إذا كان موجودًا
        if (substr($phone, 0, 1) === '0') {
            $phone = substr($phone, 1);
        }

        // إضافة رمز البلد إذا تم تمريره
        if ($countryCode) {
            // إزالة علامة + إذا كانت موجودة
            $countryCode = str_replace('+', '', $countryCode);

            // إضافة رمز البلد إذا لم يكن موجودًا بالفعل
            if (strpos($phone, $countryCode) !== 0) {
                $phone = "{$countryCode}{$phone}";
            }
        }

        return $phone;
    }

    /**
     * التحقق من حالة تسجيل الدخول إلى WhatsApp Web
     *
     * @return bool حالة تسجيل الدخول
     */
    public function checkLoginStatus() {
        try {
            $this->log("محاولة فحص حالة تسجيل الدخول");

            // إرسال طلب إلى الخادم المحلي للتحقق من حالة تسجيل الدخول
            $response = $this->sendRequest('/status');

            // التحقق من الاستجابة
            $isLoggedIn = $response && isset($response['status']) && $response['status'] === 'success' && isset($response['isLoggedIn']) && $response['isLoggedIn'];

            $this->log("نتيجة فحص حالة تسجيل الدخول: " . ($isLoggedIn ? 'مسجل الدخول' : 'غير مسجل الدخول'));
            return $isLoggedIn;
        } catch (Exception $e) {
            $this->log("خطأ في فحص حالة تسجيل الدخول: " . $e->getMessage());
            return false;
        }
    }



    /**
     * تسجيل رسالة في ملف السجل
     *
     * @param string $message الرسالة المراد تسجيلها
     */
    private function log($message) {
        // تخزين آخر خطأ إذا كانت الرسالة تحتوي على كلمة خطأ أو فشل
        if (strpos($message, 'خطأ') !== false || strpos($message, 'فشل') !== false ||
            strpos($message, 'waitForTimeout') !== false) {
            $this->lastError = $message;
        }

        $timestamp = date('Y-m-d H:i:s');
        try {
            // محاولة كتابة السجل
            file_put_contents($this->logFile, "[$timestamp] $message" . PHP_EOL, FILE_APPEND);
        } catch (Exception $e) {
            // إذا فشلت الكتابة، نستخدم error_log الافتراضي
            error_log("WhatsAppAutomation: [$timestamp] $message");
        }
    }

    /**
     * الحصول على آخر خطأ حدث
     *
     * @return string|نص آخر خطأ أو null إذا لم يحدث أي خطأ
     */
    public function getLastError() {
        return $this->lastError;
    }

    /**
     * إرسال طلب إلى الخادم المحلي
     *
     * @param string $endpoint نقطة النهاية
     * @param array $data البيانات المرسلة
     * @return array|null الاستجابة أو null في حالة الفشل
     */
    private function sendRequest($endpoint, $data = null) {
        try {
            $url = "{$this->localServerUrl}{$endpoint}";

            // التحقق من توفر cURL
            if (!function_exists('curl_init')) {
                $this->log("خطأ: دالة curl_init غير متوفرة على الخادم");
                return ['status' => 'error', 'message' => 'دالة curl_init غير متوفرة على الخادم'];
            }

            $ch = curl_init($url);

            $options = [
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_TIMEOUT => 30,
                // تجاهل التحقق من SSL لتجنب مشاكل الشهادات
                CURLOPT_SSL_VERIFYPEER => false,
                CURLOPT_SSL_VERIFYHOST => false,
                // إضافة معلومات التشخيص
                CURLOPT_VERBOSE => true
            ];

            if ($data !== null) {
                $jsonData = json_encode($data);
                if ($jsonData === false) {
                    $this->log("خطأ في تحويل البيانات إلى JSON: " . json_last_error_msg());
                    return ['status' => 'error', 'message' => 'خطأ في تحويل البيانات إلى JSON'];
                }

                $options[CURLOPT_POST] = true;
                $options[CURLOPT_POSTFIELDS] = $jsonData;
                $options[CURLOPT_HTTPHEADER] = [
                    'Content-Type: application/json',
                    'Content-Length: ' . strlen($jsonData)
                ];
            }

            curl_setopt_array($ch, $options);

            // إنشاء ملف مؤقت لتخزين معلومات التشخيص
            $verboseLog = fopen('php://temp', 'w+');
            curl_setopt($ch, CURLOPT_STDERR, $verboseLog);

            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $error = curl_error($ch);

            // قراءة معلومات التشخيص
            rewind($verboseLog);
            $verboseOutput = stream_get_contents($verboseLog);
            fclose($verboseLog);

            curl_close($ch);

            if ($error) {
                $this->log("خطأ في الاتصال بالخادم المحلي: $error");
                $this->log("معلومات التشخيص: $verboseOutput");
                return ['status' => 'error', 'message' => $error];
            }

            // التحقق من رمز الحالة HTTP
            if ($httpCode >= 400) {
                $this->log("خطأ في الاستجابة من الخادم المحلي. رمز الحالة: $httpCode");
                $this->log("الاستجابة: $response");
                return ['status' => 'error', 'message' => "خطأ في الاستجابة من الخادم المحلي. رمز الحالة: $httpCode"];
            }

            // محاولة تحليل الاستجابة كـ JSON
            $decodedResponse = json_decode($response, true);
            if ($decodedResponse === null && json_last_error() !== JSON_ERROR_NONE) {
                $this->log("خطأ في تحليل الاستجابة كـ JSON: " . json_last_error_msg());
                $this->log("الاستجابة الأصلية: $response");
                return ['status' => 'error', 'message' => 'خطأ في تحليل الاستجابة من الخادم المحلي'];
            }

            return $decodedResponse;
        } catch (Exception $e) {
            $this->log("خطأ في إرسال الطلب إلى الخادم المحلي: " . $e->getMessage());
            return ['status' => 'error', 'message' => $e->getMessage()];
        }
    }
}
