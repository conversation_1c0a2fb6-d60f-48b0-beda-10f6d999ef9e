<?php
/**
 * واجهة برمجة التطبيقات (API) لإدارة أكواد الترويج
 */

// تحديد المسار الأساسي
define('BASEPATH', true);

// استدعاء ملف التهيئة
require_once __DIR__ . '/../config/init.php';

// التأكد من استخدام طلب AJAX فقط
if (!isAjaxRequest()) {
    header('HTTP/1.0 403 Forbidden');
    exit('غير مسموح بالوصول المباشر');
}

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    echo json_encode([
        'status' => 'error',
        'message' => 'يجب تسجيل الدخول أولاً'
    ]);
    exit;
}

// الحصول على الإجراء المطلوب
$action = isset($_POST['action']) ? $_POST['action'] : (isset($_GET['action']) ? $_GET['action'] : '');

// التحقق من وجود إجراء
if (empty($action)) {
    echo json_encode([
        'status' => 'error',
        'message' => 'لم يتم تحديد الإجراء المطلوب'
    ]);
    exit;
}

// إنشاء كائن من فئة أكواد الترويج
$promoCodeModel = new PromoCode($db);

// معالجة الإجراءات
try {
    switch ($action) {
        case 'add':
            // التحقق من صلاحية الإضافة
            if (!hasPermission('promotions_create')) {
                throw new Exception('ليس لديك صلاحية لإضافة أكواد ترويج');
            }

            // التحقق من البيانات المطلوبة
            $requiredFields = ['code', 'name', 'discount_type', 'discount_value'];
            foreach ($requiredFields as $field) {
                if (!isset($_POST[$field]) || empty($_POST[$field])) {
                    throw new Exception('يرجى إدخال جميع البيانات المطلوبة');
                }
            }

            // إعداد البيانات
            $promoCodeData = [
                'code' => $_POST['code'],
                'name' => $_POST['name'],
                'description' => $_POST['description'] ?? '',
                'discount_type' => $_POST['discount_type'],
                'discount_value' => $_POST['discount_value'],
                'min_invoice_amount' => !empty($_POST['min_invoice_amount']) ? $_POST['min_invoice_amount'] : null,
                'max_invoice_amount' => !empty($_POST['max_invoice_amount']) ? $_POST['max_invoice_amount'] : null,
                'max_uses' => !empty($_POST['max_uses']) ? $_POST['max_uses'] : null,
                'one_use_per_customer' => isset($_POST['one_use_per_customer']) ? 1 : 0,
                'is_for_specific_customers' => isset($_POST['is_for_specific_customers']) ? 1 : 0,
                'required_loyalty_points' => !empty($_POST['required_loyalty_points']) ? $_POST['required_loyalty_points'] : null,
                'start_date' => !empty($_POST['start_date']) ? $_POST['start_date'] : null,
                'end_date' => !empty($_POST['end_date']) ? $_POST['end_date'] : null,
                'is_active' => isset($_POST['is_active']) ? 1 : 0,
                'branch_id' => !empty($_POST['branch_id']) ? $_POST['branch_id'] : null
            ];

            // إضافة العملاء المحددين إذا كان الكود مخصصًا لعملاء محددين
            if ($promoCodeData['is_for_specific_customers'] && isset($_POST['customer_ids'])) {
                $promoCodeData['customer_ids'] = is_array($_POST['customer_ids']) ? $_POST['customer_ids'] : explode(',', $_POST['customer_ids']);
            }

            // إضافة كود الترويج
            $promoCodeId = $promoCodeModel->addPromoCode($promoCodeData);

            echo json_encode([
                'status' => 'success',
                'message' => 'تم إضافة كود الترويج بنجاح',
                'promo_code_id' => $promoCodeId
            ]);
            break;

        case 'update':
            // التحقق من صلاحية التعديل
            if (!hasPermission('promotions_edit')) {
                throw new Exception('ليس لديك صلاحية لتعديل أكواد الترويج');
            }

            // التحقق من البيانات المطلوبة
            if (!isset($_POST['id']) || empty($_POST['id'])) {
                throw new Exception('لم يتم تحديد كود الترويج');
            }

            $requiredFields = ['code', 'name', 'discount_type', 'discount_value'];
            foreach ($requiredFields as $field) {
                if (!isset($_POST[$field]) || empty($_POST[$field])) {
                    throw new Exception('يرجى إدخال جميع البيانات المطلوبة');
                }
            }

            $promoCodeId = intval($_POST['id']);

            // التحقق من وجود كود الترويج
            $promoCode = $promoCodeModel->getPromoCodeById($promoCodeId);
            if (!$promoCode) {
                throw new Exception('كود الترويج غير موجود');
            }

            // إعداد البيانات
            $promoCodeData = [
                'code' => $_POST['code'],
                'name' => $_POST['name'],
                'description' => $_POST['description'] ?? '',
                'discount_type' => $_POST['discount_type'],
                'discount_value' => $_POST['discount_value'],
                'min_invoice_amount' => !empty($_POST['min_invoice_amount']) ? $_POST['min_invoice_amount'] : null,
                'max_invoice_amount' => !empty($_POST['max_invoice_amount']) ? $_POST['max_invoice_amount'] : null,
                'max_uses' => !empty($_POST['max_uses']) ? $_POST['max_uses'] : null,
                'one_use_per_customer' => isset($_POST['one_use_per_customer']) && $_POST['one_use_per_customer'] == 1 ? 1 : 0,
                'is_for_specific_customers' => isset($_POST['is_for_specific_customers']) && $_POST['is_for_specific_customers'] == 1 ? 1 : 0,
                'required_loyalty_points' => !empty($_POST['required_loyalty_points']) ? $_POST['required_loyalty_points'] : null,
                'start_date' => !empty($_POST['start_date']) ? $_POST['start_date'] : null,
                'end_date' => !empty($_POST['end_date']) ? $_POST['end_date'] : null,
                'is_active' => isset($_POST['is_active']) && $_POST['is_active'] == 1 ? 1 : 0,
                'branch_id' => !empty($_POST['branch_id']) ? $_POST['branch_id'] : null
            ];

            // للتأكد من أن قيمة is_for_specific_customers يتم تعيينها بشكل صحيح
            error_log('is_for_specific_customers value: ' . (isset($_POST['is_for_specific_customers']) ? $_POST['is_for_specific_customers'] : 'not set'));

            // إضافة العملاء المحددين إذا كان الكود مخصصًا لعملاء محددين
            if ($promoCodeData['is_for_specific_customers'] == 1 && isset($_POST['customer_ids'])) {
                $promoCodeData['customer_ids'] = is_array($_POST['customer_ids']) ? $_POST['customer_ids'] : explode(',', $_POST['customer_ids']);
                error_log('Adding customer_ids to promoCodeData: ' . json_encode($promoCodeData['customer_ids']));
            } else {
                error_log('Not adding customer_ids to promoCodeData because is_for_specific_customers is not 1');
            }

            // تحديث كود الترويج
            $promoCodeModel->updatePromoCode($promoCodeId, $promoCodeData);

            echo json_encode([
                'status' => 'success',
                'message' => 'تم تحديث كود الترويج بنجاح'
            ]);
            break;

        case 'delete':
            // التحقق من صلاحية الحذف
            if (!hasPermission('promotions_delete')) {
                throw new Exception('ليس لديك صلاحية لحذف أكواد الترويج');
            }

            // التحقق من البيانات المطلوبة
            if (!isset($_POST['id']) || empty($_POST['id'])) {
                throw new Exception('لم يتم تحديد كود الترويج');
            }

            $promoCodeId = intval($_POST['id']);

            // التحقق من وجود كود الترويج
            $promoCode = $promoCodeModel->getPromoCodeById($promoCodeId);
            if (!$promoCode) {
                throw new Exception('كود الترويج غير موجود');
            }

            // حذف كود الترويج
            $promoCodeModel->deletePromoCode($promoCodeId);

            echo json_encode([
                'status' => 'success',
                'message' => 'تم حذف كود الترويج بنجاح'
            ]);
            break;

        case 'toggle_status':
            // التحقق من صلاحية التعديل
            if (!hasPermission('promotions_edit')) {
                throw new Exception('ليس لديك صلاحية لتعديل أكواد الترويج');
            }

            // التحقق من البيانات المطلوبة
            if (!isset($_POST['id']) || empty($_POST['id'])) {
                throw new Exception('لم يتم تحديد كود الترويج');
            }

            if (!isset($_POST['is_active'])) {
                throw new Exception('لم يتم تحديد الحالة الجديدة');
            }

            $promoCodeId = intval($_POST['id']);
            $isActive = intval($_POST['is_active']);

            // التحقق من وجود كود الترويج
            $promoCode = $promoCodeModel->getPromoCodeById($promoCodeId);
            if (!$promoCode) {
                throw new Exception('كود الترويج غير موجود');
            }

            // تغيير حالة كود الترويج
            $promoCodeModel->togglePromoCodeStatus($promoCodeId, $isActive);

            echo json_encode([
                'status' => 'success',
                'message' => 'تم ' . ($isActive ? 'تفعيل' : 'تعطيل') . ' كود الترويج بنجاح'
            ]);
            break;

        case 'get':
            // التحقق من صلاحية العرض
            if (!hasPermission('promotions_view')) {
                throw new Exception('ليس لديك صلاحية لعرض أكواد الترويج');
            }

            // التحقق من البيانات المطلوبة
            if (!isset($_POST['id']) && !isset($_POST['code'])) {
                throw new Exception('لم يتم تحديد كود الترويج');
            }

            // استرجاع كود الترويج حسب المعرف أو الرمز
            if (isset($_POST['id'])) {
                $promoCodeId = intval($_POST['id']);
                $promoCode = $promoCodeModel->getPromoCodeById($promoCodeId);
            } else {
                $code = $_POST['code'];
                $promoCode = $promoCodeModel->getPromoCodeByCode($code);
            }

            if (!$promoCode) {
                throw new Exception('كود الترويج غير موجود');
            }

            // استرجاع سجل الاستخدام إذا كان مطلوبًا
            if (isset($_POST['with_usage']) && $_POST['with_usage']) {
                $promoCode['usage_history'] = $promoCodeModel->getPromoCodeUsageHistory($promoCode['id']);
            }

            echo json_encode([
                'status' => 'success',
                'promo_code' => $promoCode
            ]);
            break;

        case 'list':
            // التحقق من صلاحية العرض
            if (!hasPermission('promotions_view')) {
                throw new Exception('ليس لديك صلاحية لعرض أكواد الترويج');
            }

            // إعداد معلمات البحث
            $params = [];

            if (isset($_POST['search'])) {
                $params['search'] = $_POST['search'];
            }

            if (isset($_POST['branch_id'])) {
                $params['branch_id'] = $_POST['branch_id'];
            }

            if (isset($_POST['is_active'])) {
                $params['is_active'] = $_POST['is_active'];
            }

            if (isset($_POST['date'])) {
                $params['date'] = $_POST['date'];
            }

            // إعداد معلمات الصفحات
            if (isset($_POST['page']) && isset($_POST['limit'])) {
                $params['limit'] = intval($_POST['limit']);
                $params['offset'] = (intval($_POST['page']) - 1) * $params['limit'];
            }

            // استرجاع أكواد الترويج
            $promoCodes = $promoCodeModel->getPromoCodes($params);
            $totalCount = $promoCodeModel->countPromoCodes($params);

            echo json_encode([
                'status' => 'success',
                'promo_codes' => $promoCodes,
                'total' => $totalCount
            ]);
            break;

        case 'validate':
            // التحقق من البيانات المطلوبة
            if (!isset($_POST['code']) || empty($_POST['code'])) {
                throw new Exception('لم يتم تحديد كود الترويج');
            }

            if (!isset($_POST['invoice_amount']) || !is_numeric($_POST['invoice_amount'])) {
                throw new Exception('يرجى تحديد قيمة الفاتورة بشكل صحيح');
            }

            $code = $_POST['code'];
            $invoiceAmount = floatval($_POST['invoice_amount']);
            $customerId = isset($_POST['customer_id']) ? intval($_POST['customer_id']) : null;

            // التحقق من صلاحية كود الترويج
            $promoCode = $promoCodeModel->validatePromoCode($code, $invoiceAmount, $customerId);

            echo json_encode([
                'status' => 'success',
                'message' => 'كود الترويج صالح للاستخدام',
                'promo_code' => $promoCode
            ]);
            break;

        case 'apply':
            // التحقق من صلاحية التعديل
            if (!hasPermission('invoices_edit')) {
                throw new Exception('ليس لديك صلاحية لتطبيق أكواد الترويج');
            }

            // التحقق من البيانات المطلوبة
            if (!isset($_POST['code']) || empty($_POST['code'])) {
                throw new Exception('لم يتم تحديد كود الترويج');
            }

            if (!isset($_POST['invoice_id']) || empty($_POST['invoice_id'])) {
                throw new Exception('لم يتم تحديد الفاتورة');
            }

            if (!isset($_POST['invoice_amount']) || !is_numeric($_POST['invoice_amount'])) {
                throw new Exception('يرجى تحديد قيمة الفاتورة بشكل صحيح');
            }

            $code = $_POST['code'];
            $invoiceId = intval($_POST['invoice_id']);
            $invoiceAmount = floatval($_POST['invoice_amount']);
            $customerId = isset($_POST['customer_id']) ? intval($_POST['customer_id']) : null;

            // التحقق من صلاحية كود الترويج
            $promoCode = $promoCodeModel->validatePromoCode($code, $invoiceAmount, $customerId);

            // تسجيل استخدام كود الترويج
            $promoCodeModel->recordPromoCodeUsage(
                $promoCode['id'],
                $invoiceId,
                $customerId,
                $promoCode['calculated_discount']
            );

            echo json_encode([
                'status' => 'success',
                'message' => 'تم تطبيق كود الترويج بنجاح',
                'discount_amount' => $promoCode['calculated_discount'],
                'promo_code' => $promoCode
            ]);
            break;

        case 'generate_code':
            // التحقق من صلاحية الإضافة
            if (!hasPermission('promotions_create')) {
                throw new Exception('ليس لديك صلاحية لإنشاء أكواد ترويج');
            }

            $length = isset($_POST['length']) ? intval($_POST['length']) : 8;
            $code = $promoCodeModel->generateRandomCode($length);

            echo json_encode([
                'status' => 'success',
                'code' => $code
            ]);
            break;

        case 'check_customer_codes':
            // التحقق من البيانات المطلوبة
            if (!isset($_POST['customer_id']) || empty($_POST['customer_id'])) {
                throw new Exception('لم يتم تحديد العميل');
            }

            $customerId = intval($_POST['customer_id']);
            $invoiceAmount = isset($_POST['invoice_amount']) ? floatval($_POST['invoice_amount']) : 0;

            // استعلام للحصول على أكواد الترويج المتاحة للعميل والتي لم يستخدمها من قبل
            $today = date('Y-m-d');
            $db->prepare("
                SELECT pc.*
                FROM promo_codes pc
                WHERE pc.is_active = 1
                AND (pc.start_date IS NULL OR pc.start_date <= :today)
                AND (pc.end_date IS NULL OR pc.end_date >= :today)
                AND (
                    (pc.is_for_specific_customers = 0) OR
                    (pc.is_for_specific_customers = 1 AND EXISTS (
                        SELECT 1 FROM promo_code_customers pcc
                        WHERE pcc.promo_code_id = pc.id AND pcc.customer_id = :customer_id
                    ))
                )
                AND (pc.max_uses IS NULL OR pc.current_uses < pc.max_uses)
                AND (
                    pc.one_use_per_customer = 0 OR
                    NOT EXISTS (
                        SELECT 1 FROM promo_code_usage pcu
                        WHERE pcu.promo_code_id = pc.id AND pcu.customer_id = :customer_id
                    )
                )
                ORDER BY pc.discount_value DESC, pc.created_at DESC
            ");

            $db->bind(':today', $today);
            $db->bind(':customer_id', $customerId);
            $availablePromoCodes = $db->fetchAll();

            // التحقق من الحد الأدنى والأقصى لقيمة الفاتورة
            $validPromoCodes = [];
            foreach ($availablePromoCodes as $promoCode) {
                $isValid = true;

                // التحقق من الحد الأدنى لقيمة الفاتورة
                if ($promoCode['min_invoice_amount'] && $invoiceAmount < $promoCode['min_invoice_amount']) {
                    $isValid = false;
                }

                // التحقق من الحد الأقصى لقيمة الفاتورة
                if ($promoCode['max_invoice_amount'] && $invoiceAmount > $promoCode['max_invoice_amount']) {
                    $isValid = false;
                }

                // التحقق من نقاط الولاء المطلوبة
                if ($promoCode['required_loyalty_points']) {
                    $db->prepare("SELECT loyalty_points FROM customers WHERE id = :id");
                    $db->bind(':id', $customerId);
                    $customer = $db->fetch();

                    if (!$customer || $customer['loyalty_points'] < $promoCode['required_loyalty_points']) {
                        $isValid = false;
                    }
                }

                if ($isValid) {
                    $validPromoCodes[] = $promoCode;
                }
            }

            echo json_encode([
                'status' => 'success',
                'promo_codes' => $validPromoCodes
            ]);
            break;

        default:
            throw new Exception('الإجراء غير معروف');
    }
} catch (Exception $e) {
    echo json_encode([
        'status' => 'error',
        'message' => $e->getMessage()
    ]);
}
