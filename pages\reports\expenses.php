<!-- محتوى الفلاتر -->
<div class="tab-content" id="reportTabContent">
                    <!-- محتوى نطاق التاريخ -->
                    <div class="tab-pane fade show active" id="date-range-content" role="tabpanel" aria-labelledby="date-range-tab">
                        <form id="dateRangeForm" method="post" class="row align-items-end">
                            <input type="hidden" name="filter_type" value="date_range">
                            
                            <div class="col-md-3 mb-3">
                                <label for="dateRange" class="form-label">نطاق التاريخ</label>
                                <select class="form-select" id="dateRange" name="date_range">
                                    <option value="custom">تخصيص</option>
                                    <option value="today">اليوم</option>
                                    <option value="yesterday">الأمس</option>
                                    <option value="this_week">هذا الأسبوع</option>
                                    <option value="last_week">الأسبوع الماضي</option>
                                    <option value="this_month" selected>هذا الشهر</option>
                                    <option value="last_month">الشهر الماضي</option>
                                    <option value="this_year">هذه السنة</option>
                                </select>
                            </div>
                            
                            <div class="col-md-3 mb-3">
                                <label for="startDate" class="form-label">من تاريخ</label>
                                <input type="date" class="form-control" id="startDate" name="start_date" value="<?php echo $firstDayOfMonth; ?>">
                            </div>
                            
                            <div class="col-md-3 mb-3">
                                <label for="endDate" class="form-label">إلى تاريخ</label>
                                <input type="date" class="form-control" id="endDate" name="end_date" value="<?php echo $lastDayOfMonth; ?>">
                            </div>
                            
                            <?php if (hasPermission('admin')): ?>
                            <div class="col-md-3 mb-3">
                                <label for="drBranchId" class="form-label">الفرع</label>
                                <select class="form-select" id="drBranchId" name="branch_id">
                                    <option value="">جميع الفروع</option>
                                    <?php foreach($branches as $branch): ?>
                                    <option value="<?php echo $branch['id']; ?>" <?php echo ($branch['id'] == $_SESSION['user_branch_id']) ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($branch['name']); ?>
                                    </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <?php else: ?>
                            <input type="hidden" name="branch_id" value="<?php echo $_SESSION['user_branch_id']; ?>">
                            <?php endif; ?>
                            
                            <div class="col-md-3 mb-3">
                                <label for="drCategoryId" class="form-label">فئة المصروفات</label>
                                <select class="form-select" id="drCategoryId" name="category_id">
                                    <option value="">جميع الفئات</option>
                                    <?php foreach($expenseCategories as $category): ?>
                                    <option value="<?php echo $category['id']; ?>">
                                        <?php echo htmlspecialchars($category['name']); ?>
                                    </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            
                            <div class="col-md-3 mb-3">
                                <label for="drPaymentMethod" class="form-label">طريقة الدفع</label>
                                <select class="form-select" id="drPaymentMethod" name="payment_method">
                                    <option value="">جميع الطرق</option>
                                    <option value="cash">نقدي</option>
                                    <option value="card">بطاقة</option>
                                    <option value="other">أخرى</option>
                                </select>
                            </div>
                            
                            <div class="col-md-3 mb-3">
                                <label for="drSortBy" class="form-label">ترتيب حسب</label>
                                <select class="form-select" id="drSortBy" name="sort_by">
                                    <option value="date">التاريخ</option>
                                    <option value="amount">المبلغ</option>
                                    <option value="category">فئة المصروفات</option>
                                </select>
                            </div>
                            
                            <div class="col-md-3 mb-3">
                                <label for="drSortDir" class="form-label">اتجاه الترتيب</label>
                                <select class="form-select" id="drSortDir" name="sort_dir">
                                    <option value="DESC">تنازلي</option>
                                    <option value="ASC">تصاعدي</option>
                                </select>
                            </div>
                            
                            <div class="col-md-3 mb-3">
                                <button type="submit" class="btn btn-primary w-100">
                                    <i class="fas fa-filter me-1"></i> عرض التقرير
                                </button>
                            </div>
                        </form>
                    </div>
                    
                    <!-- محتوى الشهر / السنة -->
                    <div class="tab-pane fade" id="month-year-content" role="tabpanel" aria-labelledby="month-year-tab">
                        <form id="monthYearForm" method="post" class="row align-items-end">
                            <input type="hidden" name="filter_type" value="month_year">
                            
                            <div class="col-md-3 mb-3">
                                <label for="myMonth" class="form-label">الشهر</label>
                                <select class="form-select" id="myMonth" name="month">
                                    <?php foreach($months as $key => $month): ?>
                                    <option value="<?php echo $key; ?>" <?php echo ($key == $currentMonth) ? 'selected' : ''; ?>>
                                        <?php echo $month; ?>
                                    </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            
                            <div class="col-md-3 mb-3">
                                <label for="myYear" class="form-label">السنة</label>
                                <select class="form-select" id="myYear" name="year">
                                    <?php foreach($years as $year): ?>
                                    <option value="<?php echo $year; ?>" <?php echo ($year == $currentYear) ? 'selected' : ''; ?>>
                                        <?php echo $year; ?>
                                    </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            
                            <?php if (hasPermission('admin')): ?>
                            <div class="col-md-3 mb-3">
                                <label for="myBranchId" class="form-label">الفرع</label>
                                <select class="form-select" id="myBranchId" name="branch_id">
                                    <option value="">جميع الفروع</option>
                                    <?php foreach($branches as $branch): ?>
                                    <option value="<?php echo $branch['id']; ?>" <?php echo ($branch['id'] == $_SESSION['user_branch_id']) ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($branch['name']); ?>
                                    </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <?php else: ?>
                            <input type="hidden" name="branch_id" value="<?php echo $_SESSION['user_branch_id']; ?>">
                            <?php endif; ?>
                            
                            <div class="col-md-3 mb-3">
                                <label for="myCategoryId" class="form-label">فئة المصروفات</label>
                                <select class="form-select" id="myCategoryId" name="category_id">
                                    <option value="">جميع الفئات</option>
                                    <?php foreach($expenseCategories as $category): ?>
                                    <option value="<?php echo $category['id']; ?>">
                                        <?php echo htmlspecialchars($category['name']); ?>
                                    </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            
                            <div class="col-md-3 mb-3">
                                <button type="submit" class="btn btn-primary w-100">
                                    <i class="fas fa-filter me-1"></i> عرض التقرير
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="alert alert-info d-none" id="loadingAlert">
            <i class="fas fa-spinner fa-spin me-1"></i> جاري تحميل التقرير...
        </div>
        
        <div class="alert alert-danger d-none" id="errorAlert"></div>
        
        <!-- ملخص التقرير -->
        <div class="row" id="reportSummary">
            <div class="col-md-3 mb-4">
                <div class="card shadow-sm">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="text-muted mb-1">إجمالي المصروفات</h6>
                                <h3 class="display-6 fw-bold mb-0" id="totalExpenses">0.00</h3>
                            </div>
                            <div class="bg-primary bg-opacity-10 p-3 rounded">
                                <i class="fas fa-money-bill-wave text-primary fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-3 mb-4">
                <div class="card shadow-sm">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="text-muted mb-1">عدد المصروفات</h6>
                                <h3 class="display-6 fw-bold mb-0" id="expensesCount">0</h3>
                            </div>
                            <div class="bg-success bg-opacity-10 p-3 rounded">
                                <i class="fas fa-receipt text-success fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-3 mb-4">
                <div class="card shadow-sm">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="text-muted mb-1">متوسط المصروفات</h6>
                                <h3 class="display-6 fw-bold mb-0" id="averageExpense">0.00</h3>
                            </div>
                            <div class="bg-info bg-opacity-10 p-3 rounded">
                                <i class="fas fa-calculator text-info fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-3 mb-4">
                <div class="card shadow-sm">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="text-muted mb-1">أكبر مصروف</h6>
                                <h3 class="display-6 fw-bold mb-0" id="maxExpense">0.00</h3>
                            </div>
                            <div class="bg-warning bg-opacity-10 p-3 rounded">
                                <i class="fas fa-arrow-up text-warning fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- الرسوم البيانية للتقرير -->
        <div class="row">
            <div class="col-md-6 mb-4">
                <div class="card shadow-sm">
                    <div class="card-header bg-white py-3">
                        <h5 class="mb-0">المصروفات حسب الفترة</h5>
                    </div>
                    <div class="card-body">
                        <canvas id="expensesChart" height="300"></canvas>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6 mb-4">
                <div class="card shadow-sm">
                    <div class="card-header bg-white py-3">
                        <h5 class="mb-0">المصروفات حسب الفئة</h5>
                    </div>
                    <div class="card-body">
                        <canvas id="categoriesChart" height="300"></canvas>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- جدول تفاصيل التقرير -->
        <div class="card shadow-sm mb-4">
            <div class="card-header bg-white py-3 d-flex justify-content-between align-items-center">
                <h5 class="mb-0">تفاصيل المصروفات</h5>
                <div>
                    <button type="button" class="btn btn-sm btn-outline-success" id="exportExcel">
                        <i class="fas fa-file-excel me-1"></i> تصدير Excel
                    </button>
                    <button type="button" class="btn btn-sm btn-outline-danger" id="exportPdf">
                        <i class="fas fa-file-pdf me-1"></i> تصدير PDF
                    </button>
                    <button type="button" class="btn btn-sm btn-outline-secondary" id="printReport">
                        <i class="fas fa-print me-1"></i> طباعة
                    </button>
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-bordered table-striped table-hover" id="reportTable">
                        <thead class="table-light">
                            <tr>
                                <th>التاريخ</th>
                                <th>الفئة</th>
                                <th>الوصف</th>
                                <th>المبلغ</th>
                                <th>طريقة الدفع</th>
                                <th>الفرع</th>
                                <th>المستخدم</th>
                            </tr>
                        </thead>
                        <tbody id="reportTableBody">
                            <!-- ستتم تعبئة البيانات هنا عن طريق JavaScript -->
                        </tbody>
                        <tfoot class="table-light">
                            <tr>
                                <th colspan="3">الإجمالي</th>
                                <th id="footerTotalAmount">0.00</th>
                                <th colspan="3"></th>
                            </tr>
                        </tfoot>
                    </table>
                </div>
            </div>
        </div>
        
        <!-- مقارنة المصروفات مع المبيعات -->
        <div class="card shadow-sm mb-4">
            <div class="card-header bg-white py-3">
                <h5 class="mb-0">مقارنة المصروفات مع المبيعات</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <canvas id="expensesVsSalesChart" height="300"></canvas>
                    </div>
                    <div class="col-md-6">
                        <div class="table-responsive">
                            <table class="table table-bordered table-hover mt-3">
                                <thead class="table-light">
                                    <tr>
                                        <th>البيان</th>
                                        <th>القيمة</th>
                                        <th>النسبة</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>إجمالي المبيعات</td>
                                        <td id="comparisonSales">0.00</td>
                                        <td>100%</td>
                                    </tr>
                                    <tr>
                                        <td>إجمالي المصروفات</td>
                                        <td id="comparisonExpenses">0.00</td>
                                        <td id="expensesPercentage">0%</td>
                                    </tr>
                                    <tr class="table-success">
                                        <td>صافي الأرباح</td>
                                        <td id="comparisonProfit">0.00</td>
                                        <td id="profitPercentage">0%</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>