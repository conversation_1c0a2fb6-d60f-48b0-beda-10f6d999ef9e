2025-04-03 00:55:10 - بداية طلب filter
GET: Array
(
    [branch_id] => 
    [date] => ٢٠٢٥-٠٤-٠٣
    [employee_id] => 1
    [service_id] => 
    [status] => 
    [action] => filter
)
POST: Array
(
)
SESSION branch_id: 1
2025-04-03 00:55:10 - قيمة branch_id الأصلية من GET: ''
2025-04-03 00:55:10 - تم اختيار عرض كل الفروع
2025-04-03 00:55:10 - تم تعيين فلتر التاريخ: 2025-04-03
2025-04-03 00:55:10 - الفلاتر النهائية قبل استدعاء getAppointments:
Array
(
    [date] => 2025-04-03
    [start_date] => 2025-04-03
    [end_date] => 2025-04-03
    [exact_date] => 1
)

2025-04-03 00:55:10 - تم استرجاع 1 موعد
2025-04-03 00:55:50 - بداية طلب filter
GET: Array
(
    [branch_id] => 
    [date] => ٢٠٢٥-٠٤-٠٣
    [employee_id] => 1
    [service_id] => 
    [status] => 
    [action] => filter
)
POST: Array
(
)
SESSION branch_id: 1
2025-04-03 00:55:50 - قيمة branch_id الأصلية من GET: ''
2025-04-03 00:55:50 - تم اختيار عرض كل الفروع
2025-04-03 00:55:50 - تم تعيين فلتر التاريخ: 2025-04-03
2025-04-03 00:55:50 - الفلاتر النهائية قبل استدعاء getAppointments:
Array
(
    [date] => 2025-04-03
    [start_date] => 2025-04-03
    [end_date] => 2025-04-03
    [exact_date] => 1
)

2025-04-03 00:55:50 - تم استرجاع 1 موعد
2025-04-03 00:56:50 - بداية طلب filter
GET: Array
(
    [branch_id] => 
    [date] => ٢٠٢٥-٠٤-٠٣
    [employee_id] => 1
    [service_id] => 
    [status] => 
    [action] => filter
)
POST: Array
(
)
SESSION branch_id: 1
2025-04-03 00:56:50 - قيمة branch_id الأصلية من GET: ''
2025-04-03 00:56:50 - تم اختيار عرض كل الفروع
2025-04-03 00:56:50 - تم تعيين فلتر التاريخ: 2025-04-03
2025-04-03 00:56:50 - الفلاتر النهائية قبل استدعاء getAppointments:
Array
(
    [date] => 2025-04-03
    [start_date] => 2025-04-03
    [end_date] => 2025-04-03
    [exact_date] => 1
)

2025-04-03 00:56:50 - تم استرجاع 1 موعد
2025-04-03 00:57:50 - بداية طلب filter
GET: Array
(
    [branch_id] => 
    [date] => ٢٠٢٥-٠٤-٠٣
    [employee_id] => 1
    [service_id] => 
    [status] => 
    [action] => filter
)
POST: Array
(
)
SESSION branch_id: 1
2025-04-03 00:57:50 - قيمة branch_id الأصلية من GET: ''
2025-04-03 00:57:50 - تم اختيار عرض كل الفروع
2025-04-03 00:57:50 - تم تعيين فلتر التاريخ: 2025-04-03
2025-04-03 00:57:50 - الفلاتر النهائية قبل استدعاء getAppointments:
Array
(
    [date] => 2025-04-03
    [start_date] => 2025-04-03
    [end_date] => 2025-04-03
    [exact_date] => 1
)

2025-04-03 00:57:50 - تم استرجاع 1 موعد
2025-04-03 00:58:50 - بداية طلب filter
GET: Array
(
    [branch_id] => 
    [date] => ٢٠٢٥-٠٤-٠٣
    [employee_id] => 1
    [service_id] => 
    [status] => 
    [action] => filter
)
POST: Array
(
)
SESSION branch_id: 1
2025-04-03 00:58:50 - قيمة branch_id الأصلية من GET: ''
2025-04-03 00:58:50 - تم اختيار عرض كل الفروع
2025-04-03 00:58:50 - تم تعيين فلتر التاريخ: 2025-04-03
2025-04-03 00:58:50 - الفلاتر النهائية قبل استدعاء getAppointments:
Array
(
    [date] => 2025-04-03
    [start_date] => 2025-04-03
    [end_date] => 2025-04-03
    [exact_date] => 1
)

2025-04-03 00:58:50 - تم استرجاع 1 موعد
