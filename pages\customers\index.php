<?php
/**
 * صفحة إدارة العملاء
 */

// تعريف ثابت لمنع الوصول المباشر للملفات المرتبطة
define('BASEPATH', true);

// استدعاء ملفات التهيئة
require_once '../../config/init.php';

// التحقق من تسجيل الدخول
requireLogin();

// التحقق من وجود صلاحية عرض العملاء
requirePermission('customers_view');

// عنوان الصفحة
$pageTitle = 'إدارة العملاء';

// استدعاء رأس الصفحة
include '../../includes/templates/header.php';

// إنشاء كائن العملاء
$customerModel = new Customer($db);

// فلتر الفرع الحالي
$branchId = $_SESSION['user_branch_id'] ?? null;
?>

<!-- المحتوى الرئيسي للصفحة -->
<div class="container-fluid">
    <!-- رأس الصفحة -->
    <div class="card border-0 shadow-sm mb-4">
        <div class="card-body d-flex justify-content-between align-items-center">
            <h5 class="mb-0">
                <i class="fas fa-users me-2"></i> قائمة العملاء
            </h5>
            <?php if (hasPermission('customers_add')): ?>
            <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addCustomerModal">
                <i class="fas fa-plus me-1"></i> إضافة عميل
            </button>
            <?php endif; ?>
        </div>
    </div>

    <!-- بطاقة البحث والفلترة - سيتم إعادة كتابتها -->
    <div class="card border-0 shadow-sm mb-4" id="search-filter-card">
        <!-- سيتم إضافة محتوى الفلتر بواسطة JavaScript -->
    </div>

    <!-- جدول العملاء -->
    <div class="card border-0 shadow-sm">
        <div class="card-body">
            <div class="table-responsive">
                <table id="customersTable" class="table table-hover">
                    <thead class="table-light">
                        <tr>
                            <th>#</th>
                            <th>الاسم</th>
                            <th>رقم الهاتف</th>
                            <th>البريد الإلكتروني</th>
                            <th>عدد الزيارات</th>
                            <th>نقاط الولاء</th>
                            <th>تاريخ التسجيل</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <!-- سيتم تعبئة البيانات عبر AJAX -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- مودال إضافة عميل جديد -->
<div class="modal fade" id="addCustomerModal" tabindex="-1" aria-labelledby="addCustomerModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addCustomerModalLabel">إضافة عميل جديد</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="إغلاق"></button>
            </div>
            <div class="modal-body">
                <form id="addCustomerForm">
                    <div class="mb-3">
                        <label for="name" class="form-label">الاسم <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="name" name="name" required>
                    </div>
                    <div class="mb-3">
                        <label for="phone" class="form-label">رقم الهاتف <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="phone" name="phone" dir="ltr" required>
                        <div id="phone_exists_warning" class="text-danger small mt-1" style="display: none;">
                            <i class="fas fa-exclamation-triangle me-1"></i> رقم الهاتف موجود بالفعل!
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="email" class="form-label">البريد الإلكتروني</label>
                        <input type="email" class="form-control" id="email" name="email" dir="ltr">
                    </div>
                    <div class="mb-3">
                        <label for="address" class="form-label">العنوان</label>
                        <input type="text" class="form-control" id="address" name="address">
                    </div>
                    <div class="mb-3">
                        <label for="birthday" class="form-label">تاريخ الميلاد</label>
                        <input type="text" class="form-control date-picker" id="birthday" name="birthday">
                    </div>
                    <div class="mb-3">
                        <label for="notes" class="form-label">ملاحظات</label>
                        <textarea class="form-control" id="notes" name="notes" rows="3"></textarea>
                    </div>
                    <?php if (isAdmin()): ?>
                    <div class="mb-3">
                        <label for="branch_id" class="form-label">الفرع</label>
                        <select class="form-select" id="branch_id" name="branch_id">
                            <?php
                            // استرجاع قائمة الفروع
                            // $branches already defined earlier in the file
                            foreach ($branches as $branch) {
                                $selected = ($_SESSION['user_branch_id'] == $branch['id']) ? 'selected' : '';
                                echo '<option value="' . $branch['id'] . '" ' . $selected . '>' . htmlspecialchars($branch['name']) . '</option>';
                            }
                            ?>
                        </select>
                    </div>
                    <?php else: ?>
                    <input type="hidden" name="branch_id" value="<?php echo $_SESSION['user_branch_id']; ?>">
                    <?php endif; ?>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" id="submitAddCustomer">حفظ</button>
            </div>
        </div>
    </div>
</div>

<!-- مودال تعديل عميل -->
<div class="modal fade" id="editCustomerModal" tabindex="-1" aria-labelledby="editCustomerModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editCustomerModalLabel">تعديل بيانات العميل</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="إغلاق"></button>
            </div>
            <div class="modal-body">
                <form id="editCustomerForm">
                    <input type="hidden" id="edit_customer_id" name="customer_id">
                    <div class="mb-3">
                        <label for="edit_name" class="form-label">الاسم <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="edit_name" name="name" required>
                    </div>
                    <div class="mb-3">
                        <label for="edit_phone" class="form-label">رقم الهاتف <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="edit_phone" name="phone" dir="ltr" required>
                        <div id="edit_phone_exists_warning" class="text-danger small mt-1" style="display: none;">
                            <i class="fas fa-exclamation-triangle me-1"></i> رقم الهاتف موجود بالفعل!
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="edit_email" class="form-label">البريد الإلكتروني</label>
                        <input type="email" class="form-control" id="edit_email" name="email" dir="ltr">
                    </div>
                    <div class="mb-3">
                        <label for="edit_address" class="form-label">العنوان</label>
                        <input type="text" class="form-control" id="edit_address" name="address">
                    </div>
                    <div class="mb-3">
                        <label for="edit_birthday" class="form-label">تاريخ الميلاد</label>
                        <input type="text" class="form-control date-picker" id="edit_birthday" name="birthday">
                    </div>
                    <div class="mb-3">
                        <label for="edit_notes" class="form-label">ملاحظات</label>
                        <textarea class="form-control" id="edit_notes" name="notes" rows="3"></textarea>
                    </div>
                    <?php if (isAdmin()): ?>
                    <div class="mb-3">
                        <label for="edit_branch_id" class="form-label">الفرع</label>
                        <select class="form-select" id="edit_branch_id" name="branch_id">
                            <?php
                            foreach ($branches as $branch) {
                                echo '<option value="' . $branch['id'] . '">' . htmlspecialchars($branch['name']) . '</option>';
                            }
                            ?>
                        </select>
                    </div>
                    <?php endif; ?>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" id="submitEditCustomer">حفظ التعديلات</button>
            </div>
        </div>
    </div>
</div>

<!-- مودال عرض تفاصيل العميل -->
<div class="modal fade" id="viewCustomerModal" tabindex="-1" aria-labelledby="viewCustomerModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="viewCustomerModalLabel">تفاصيل العميل</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="إغلاق"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label fw-bold">الاسم:</label>
                            <p id="view_name" class="mb-0"></p>
                        </div>
                        <div class="mb-3">
                            <label class="form-label fw-bold">رقم الهاتف:</label>
                            <p id="view_phone" dir="ltr" class="mb-0"></p>
                        </div>
                        <div class="mb-3">
                            <label class="form-label fw-bold">البريد الإلكتروني:</label>
                            <p id="view_email" dir="ltr" class="mb-0"></p>
                        </div>
                        <div class="mb-3">
                            <label class="form-label fw-bold">العنوان:</label>
                            <p id="view_address" class="mb-0"></p>
                        </div>
                        <div class="mb-3">
                            <label class="form-label fw-bold">تاريخ الميلاد:</label>
                            <p id="view_birthday" class="mb-0"></p>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label fw-bold">نقاط الولاء:</label>
                            <p id="view_loyalty_points" class="mb-0"></p>
                        </div>
                        <div class="mb-3">
                            <label class="form-label fw-bold">عدد الزيارات:</label>
                            <p id="view_visits_count" class="mb-0"></p>
                        </div>
                        <div class="mb-3">
                            <label class="form-label fw-bold">إجمالي المشتريات:</label>
                            <p id="view_total_sales" class="mb-0"></p>
                        </div>
                        <div class="mb-3">
                            <label class="form-label fw-bold">تاريخ التسجيل:</label>
                            <p id="view_created_at" class="mb-0"></p>
                        </div>
                    </div>
                </div>

                <div class="mb-3">
                    <label class="form-label fw-bold">ملاحظات:</label>
                    <p id="view_notes" class="mb-0"></p>
                </div>

                <!-- علامات التبويب للزيارات والفواتير -->
                <ul class="nav nav-tabs mt-4" id="customerTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="visits-tab" data-bs-toggle="tab" data-bs-target="#visits" type="button" role="tab" aria-controls="visits" aria-selected="true">
                            الزيارات السابقة
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="invoices-tab" data-bs-toggle="tab" data-bs-target="#invoices" type="button" role="tab" aria-controls="invoices" aria-selected="false">
                            الفواتير
                        </button>
                    </li>
                </ul>

                <div class="tab-content p-3 border border-top-0 rounded-bottom" id="customerTabsContent">
                    <!-- محتوى الزيارات -->
                    <div class="tab-pane fade show active" id="visits" role="tabpanel" aria-labelledby="visits-tab">
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>التاريخ</th>
                                        <th>الفرع</th>
                                        <th>ملاحظات</th>
                                    </tr>
                                </thead>
                                <tbody id="visits_table_body">
                                    <!-- سيتم تعبئة البيانات عبر AJAX -->
                                </tbody>
                            </table>
                        </div>
                        <div id="no_visits_message" class="text-center text-muted py-3" style="display: none;">
                            لا توجد زيارات سابقة لهذا العميل
                        </div>
                    </div>

                    <!-- محتوى الفواتير -->
                    <div class="tab-pane fade" id="invoices" role="tabpanel" aria-labelledby="invoices-tab">
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>رقم الفاتورة</th>
                                        <th>التاريخ</th>
                                        <th>المبلغ</th>
                                        <th>طريقة الدفع</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody id="invoices_table_body">
                                    <!-- سيتم تعبئة البيانات عبر AJAX -->
                                </tbody>
                            </table>
                        </div>
                        <div id="no_invoices_message" class="text-center text-muted py-3" style="display: none;">
                            لا توجد فواتير مسجلة لهذا العميل
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-warning me-auto" id="editCustomerBtn">
                    <i class="fas fa-edit me-1"></i> تعديل البيانات
                </button>

                <div class="ms-auto">
                    <button type="button" class="btn btn-info" id="addAppointmentBtn">
                        <i class="fas fa-calendar-plus me-1"></i> حجز موعد
                    </button>

                    <a href="../pos/index.php" class="btn btn-primary" id="createInvoiceBtn">
                        <i class="fas fa-cash-register me-1"></i> إنشاء فاتورة
                    </a>

                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- مودال إضافة نقاط ولاء -->
<div class="modal fade" id="addLoyaltyPointsModal" tabindex="-1" aria-labelledby="addLoyaltyPointsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-sm">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addLoyaltyPointsModalLabel">إضافة نقاط ولاء</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="إغلاق"></button>
            </div>
            <div class="modal-body">
                <form id="addLoyaltyPointsForm">
                    <input type="hidden" id="loyalty_customer_id" name="customer_id">
                    <div class="mb-3">
                        <label for="points" class="form-label">عدد النقاط <span class="text-danger">*</span></label>
                        <input type="number" class="form-control" id="points" name="points" required min="1" value="10">
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" id="submitAddLoyaltyPoints">إضافة</button>
            </div>
        </div>
    </div>
</div>

<!-- استدعاء قالب الفوتر -->
<?php include '../../includes/templates/footer.php'; ?>

<script>
    $(document).ready(function() {
        // إنشاء واجهة الفلتر
        createFilterInterface();

        /**
         * دالة لإنشاء واجهة الفلتر بشكل ديناميكي
         */
        function createFilterInterface() {
            // إنشاء هيكل الفلتر
            const filterCard = $('#search-filter-card');

            // إنشاء رأس البطاقة
            const cardHeader = $(`
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h6 class="mb-0">بحث وتصفية</h6>
                    <button type="button" class="btn btn-sm btn-link" data-bs-toggle="collapse" data-bs-target="#filterCollapse" aria-expanded="false">
                        <i class="fas fa-filter me-1"></i> خيارات التصفية المتقدمة
                    </button>
                </div>
            `);

            // إنشاء جسم البطاقة
            const cardBody = $('<div class="card-body"></div>');

            // إنشاء نموذج البحث
            const searchForm = $('<form id="searchForm" class="row align-items-center"></form>');

            // إضافة حقل البحث
            const searchField = $(`
                <div class="col-md-4 mb-3 mb-md-0">
                    <label for="search_term" class="form-label">بحث</label>
                    <input type="text" id="search_term" class="form-control" placeholder="بحث بالاسم أو رقم الهاتف...">
                </div>
            `);

            // إضافة قائمة الفروع للمدير
            let branchFilter = '';
            <?php if (isAdmin()): ?>
            branchFilter = `
                <div class="col-md-3 mb-3 mb-md-0">
                    <label for="branch_filter" class="form-label">الفرع</label>
                    <select id="branch_filter" class="form-select">
                        <option value="">كل الفروع</option>
                        <?php
                        // استرجاع قائمة الفروع
                        $branchModel = new Branch($db);
                        $branches = $branchModel->getBranches(['is_active' => 1]);
                        foreach ($branches as $branch) {
                            echo '<option value="' . $branch['id'] . '">' . htmlspecialchars($branch['name']) . '</option>';
                        }
                        ?>
                    </select>
                </div>
            `;
            <?php endif; ?>

            // إضافة أزرار البحث وإعادة الضبط
            const searchButtons = $(`
                <div class="col-md-3 mb-3 mb-md-0">
                    <label class="form-label d-block">&nbsp;</label>
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="fas fa-search me-1"></i> بحث
                    </button>
                </div>
                <div class="col-md-2">
                    <label class="form-label d-block">&nbsp;</label>
                    <button type="reset" class="btn btn-secondary w-100">
                        <i class="fas fa-redo me-1"></i> إعادة ضبط
                    </button>
                </div>
            `);

            // إنشاء قسم الفلاتر المتقدمة
            const advancedFilters = $(`
                <div class="collapse mt-3 w-100" id="filterCollapse">
                    <div class="card card-body bg-light">
                        <div class="row">
                            <!-- فلتر تاريخ التسجيل -->
                            <div class="col-md-6 mb-3">
                                <label class="form-label">تاريخ التسجيل</label>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="input-group">
                                            <span class="input-group-text">من</span>
                                            <input type="text" id="date_from" class="form-control date-picker" placeholder="مثال: 01/01/2023">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="input-group">
                                            <span class="input-group-text">إلى</span>
                                            <input type="text" id="date_to" class="form-control date-picker" placeholder="مثال: 31/12/2023">
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- فلتر نقاط الولاء -->
                            <div class="col-md-6 mb-3">
                                <label class="form-label">نقاط الولاء</label>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="input-group">
                                            <span class="input-group-text">من</span>
                                            <input type="number" id="loyalty_points_min" class="form-control" min="0">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="input-group">
                                            <span class="input-group-text">إلى</span>
                                            <input type="number" id="loyalty_points_max" class="form-control" min="0">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <!-- فلتر عدد الزيارات -->
                            <div class="col-md-6 mb-3">
                                <label class="form-label">عدد الزيارات</label>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="input-group">
                                            <span class="input-group-text">من</span>
                                            <input type="number" id="visits_min" class="form-control" min="0">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="input-group">
                                            <span class="input-group-text">إلى</span>
                                            <input type="number" id="visits_max" class="form-control" min="0">
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- زر التصدير إلى Excel -->
                            <div class="col-md-6 d-flex align-items-end">
                                <button type="button" id="exportToExcel" class="btn btn-success w-100">
                                    <i class="fas fa-file-excel me-1"></i> تصدير إلى Excel
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `);

            // تجميع النموذج
            searchForm.append(searchField);
            if (branchFilter) {
                searchForm.append($(branchFilter));
            }
            searchForm.append(searchButtons);
            searchForm.append(advancedFilters);

            // إضافة النموذج إلى جسم البطاقة
            cardBody.append(searchForm);

            // إضافة الرأس والجسم إلى البطاقة
            filterCard.append(cardHeader);
            filterCard.append(cardBody);

            // تهيئة حقول التاريخ بعد إنشاء العناصر
            initializeDatePickers();
        }
        /**
         * دالة لتهيئة حقول التاريخ
         */
        function initializeDatePickers() {
            // تسجيل قيم حقول التاريخ قبل التهيئة
            console.log('Date fields before initialization:');
            console.log('date_from value:', $('#date_from').val());
            console.log('date_to value:', $('#date_to').val());

            // تهيئة حقول التاريخ
            $('.date-picker').datepicker({
                format: 'yyyy-mm-dd',  // تغيير صيغة التاريخ لتتوافق مع صيغة قاعدة البيانات
                autoclose: true,
                todayHighlight: true,
                language: 'ar',
                rtl: true
            }).on('changeDate', function(e) {
                // تسجيل تغيير التاريخ للتصحيح
                console.log('Date changed for ' + $(this).attr('id') + ':', $(this).val());
                console.log('Date object:', e.date);
            });

            // تسجيل قيم حقول التاريخ بعد التهيئة
            console.log('Date fields after initialization:');
            console.log('date_from value:', $('#date_from').val());
            console.log('date_to value:', $('#date_to').val());
        }
        /**
         * تهيئة جدول البيانات
         */
        const customersTable = $('#customersTable').DataTable({
            processing: true,
            serverSide: false,
            ajax: {
                url: '../../api/customers.php',
                type: 'POST',
                data: function(d) {
                    // إضافة الإجراء المطلوب
                    d.action = 'get_customers';

                    // الحصول على جميع الفلاتر الحالية
                    const filters = getFilters();

                    // إضافة الفلاتر إلى طلب البيانات بشكل مباشر
                    d.search = filters.search || '';
                    d.branch_id = filters.branch_id || '';

                    // معالجة خاصة لقيم التاريخ
                    if (filters.date_from && filters.date_from.trim() !== '') {
                        d.date_from = filters.date_from.trim();
                        console.log('Setting date_from in request:', d.date_from);
                    }

                    if (filters.date_to && filters.date_to.trim() !== '') {
                        d.date_to = filters.date_to.trim();
                        console.log('Setting date_to in request:', d.date_to);
                    }

                    // إضافة بقية الفلاتر
                    d.loyalty_points_min = filters.loyalty_points_min || '';
                    d.loyalty_points_max = filters.loyalty_points_max || '';
                    d.visits_min = filters.visits_min || '';
                    d.visits_max = filters.visits_max || '';

                    // تسجيل البيانات المرسلة للتصحيح
                    console.log('DataTable request data:', d);

                    // إضافة معلومات للتصحيح
                    d._debug = true;
                },
                dataSrc: function(response) {
                    // تسجيل الاستجابة للتصحيح
                    console.log('DataTable response:', response);

                    // التحقق من وجود البيانات
                    if (response && response.customers) {
                        return response.customers;
                    } else {
                        console.error('Invalid response format:', response);
                        return [];
                    }
                }
            },
            columns: [
                { data: 'id' },
                { data: 'name' },
                { data: 'phone' },
                { data: 'email',
                  render: function(data) {
                      return data || '<span class="text-muted">-</span>';
                  }
                },
                { data: 'visits_count',
                  render: function(data) {
                      return data || 0;
                  }
                },
                { data: 'loyalty_points',
                  render: function(data) {
                      return data || 0;
                  }
                },
                {
                  data: 'created_at',
                  render: function(data) {
                      return moment(data).format('YYYY/MM/DD');
                  }
                },
                {
                    data: null,
                    orderable: false,
                    render: function(data) {
                        let actions = `
                            <div class="dropdown">
                                <button class="btn btn-sm btn-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                                    الإجراءات
                                </button>
                                <ul class="dropdown-menu dropdown-menu-end">
                                    <li><a class="dropdown-item" href="view.php?id=${data.id}">
                                        <i class="fas fa-eye me-2"></i> عرض التفاصيل
                                    </a></li>`;

                        <?php if (hasPermission('customers_edit')): ?>
                        actions += `
                            <li><a class="dropdown-item edit-customer" href="javascript:void(0)" data-id="${data.id}">
                                <i class="fas fa-edit me-2"></i> تعديل
                            </a></li>
                            <li><a class="dropdown-item add-loyalty-points" href="javascript:void(0)" data-id="${data.id}">
                                <i class="fas fa-gift me-2"></i> إضافة نقاط ولاء
                            </a></li>`;
                        <?php endif; ?>

                        <?php if (hasPermission('appointments_create')): ?>
                        actions += `
                            <li><a class="dropdown-item add-appointment" href="javascript:void(0)" data-id="${data.id}">
                                <i class="fas fa-calendar-plus me-2"></i> حجز موعد
                            </a></li>`;
                        <?php endif; ?>

                        <?php if (hasPermission('pos_view')): ?>
                        actions += `
                            <li><a class="dropdown-item" href="../pos/index.php?customer_id=${data.id}">
                                <i class="fas fa-cash-register me-2"></i> إنشاء فاتورة
                            </a></li>`;
                        <?php endif; ?>

                        <?php if (hasPermission('customers_delete')): ?>
                        actions += `
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item text-danger delete-customer" href="javascript:void(0)" data-id="${data.id}">
                                <i class="fas fa-trash-alt me-2"></i> حذف
                            </a></li>`;
                        <?php endif; ?>

                        actions += `
                                </ul>
                            </div>`;

                        return actions;
                    }
                }
            ],
            language: {
                url: 'https://cdn.datatables.net/plug-ins/1.11.5/i18n/ar.json'
            }
        });

        /**
         * معالجة البحث والفلترة
         */
        function setupFilterHandlers() {
            // البحث
            $('#searchForm').on('submit', function(e) {
                e.preventDefault();
                applyFilters();
            });

            // إعادة ضبط البحث
            $('#searchForm button[type="reset"]').on('click', function() {
                resetFilters();
                // إزالة معلمات URL
                window.history.pushState({}, '', window.location.pathname);
                // إعادة تحميل الجدول
                customersTable.ajax.reload();
            });

            // تصدير البيانات إلى Excel
            $('#exportToExcel').on('click', function() {
                exportToExcel();
            });

            // معالجة تغيير حقول التاريخ
            $('.date-picker').on('changeDate', function() {
                // لا نقوم بتطبيق الفلاتر تلقائيًا عند تغيير التاريخ
                // المستخدم يجب أن ينقر على زر البحث
                console.log('Date changed:', $(this).attr('id'), $(this).val());
            });

            // معالجة تغيير حقول الأرقام
            $('#loyalty_points_min, #loyalty_points_max, #visits_min, #visits_max').on('change', function() {
                // لا نقوم بتطبيق الفلاتر تلقائيًا عند تغيير القيم
                // المستخدم يجب أن ينقر على زر البحث
                console.log('Numeric field changed:', $(this).attr('id'), $(this).val());
            });

            // معالجة تغيير حقل البحث
            $('#search_term').on('keyup', function(e) {
                // تطبيق الفلاتر عند الضغط على Enter
                if (e.key === 'Enter') {
                    applyFilters();
                }
            });

            // معالجة تغيير الفرع
            $('#branch_filter').on('change', function() {
                // لا نقوم بتطبيق الفلاتر تلقائيًا عند تغيير الفرع
                // المستخدم يجب أن ينقر على زر البحث
                console.log('Branch changed:', $(this).val());
            });
        }

        /**
         * تطبيق الفلاتر وإعادة تحميل الجدول
         */
        function applyFilters() {
            try {
                console.log('Starting to apply filters...');

                // جمع الفلاتر
                const filters = getFilters();

                // عرض رسالة توضح الفلاتر المستخدمة
                displayFilterMessage(filters);

                // تسجيل الفلاتر المطبقة في وحدة التحكم
                console.log('Applying filters:', filters);

                // التحقق من وجود فلاتر للتطبيق
                const hasFilters = Object.values(filters).some(value => value !== '');
                console.log('Has filters to apply:', hasFilters);

                // إعادة تحميل الجدول مع الفلاتر الجديدة
                console.log('Reloading DataTable with filters...');

                // إعادة تحميل الجدول مع الفلاتر الجديدة
                customersTable.ajax.reload(function(json) {
                    // تسجيل نتائج الاستعلام بعد إعادة التحميل
                    console.log('DataTable reload complete. Results:', json);
                });

                // تسجيل نجاح تطبيق الفلاتر
                console.log('Filters applied successfully');

                // إضافة معلومات الفلتر إلى URL للمشاركة والحفظ
                updateUrlWithFilters(filters);

                // إضافة طلب AJAX مباشر للتحقق من المشكلة
                console.log('Sending direct AJAX request to verify filter functionality...');

                // إنشاء كائن البيانات للطلب
                const ajaxData = {
                    action: 'get_customers',
                    search: filters.search || '',
                    branch_id: filters.branch_id || '',
                    loyalty_points_min: filters.loyalty_points_min || '',
                    loyalty_points_max: filters.loyalty_points_max || '',
                    visits_min: filters.visits_min || '',
                    visits_max: filters.visits_max || '',
                    _debug: true
                };

                // معالجة خاصة لقيم التاريخ
                if (filters.date_from && filters.date_from.trim() !== '') {
                    ajaxData.date_from = filters.date_from.trim();
                    console.log('Setting date_from in direct AJAX request:', ajaxData.date_from);
                }

                if (filters.date_to && filters.date_to.trim() !== '') {
                    ajaxData.date_to = filters.date_to.trim();
                    console.log('Setting date_to in direct AJAX request:', ajaxData.date_to);
                }

                // تسجيل بيانات الطلب
                console.log('Direct AJAX request data:', ajaxData);

                $.ajax({
                    url: '../../api/customers.php',
                    type: 'POST',
                    data: ajaxData,
                    success: function(response) {
                        console.log('Direct AJAX response:', response);
                    },
                    error: function(xhr, status, error) {
                        console.error('Direct AJAX error:', error);
                        console.error('Error details:', xhr.responseText);
                    }
                });
            } catch (error) {
                console.error('Error applying filters:', error);
                showAlert('حدث خطأ أثناء تطبيق الفلاتر', 'danger');
            }
        }

        /**
         * تحديث URL بمعلومات الفلتر
         * @param {Object} filters كائن يحتوي على قيم الفلاتر
         */
        function updateUrlWithFilters(filters) {
            // إنشاء كائن URL جديد
            const url = new URL(window.location.href);

            // إزالة جميع معلمات الفلتر الحالية
            url.searchParams.delete('search');
            url.searchParams.delete('branch_id');
            url.searchParams.delete('date_from');
            url.searchParams.delete('date_to');
            url.searchParams.delete('loyalty_points_min');
            url.searchParams.delete('loyalty_points_max');
            url.searchParams.delete('visits_min');
            url.searchParams.delete('visits_max');

            // إضافة معلمات الفلتر الجديدة
            Object.keys(filters).forEach(key => {
                if (filters[key]) {
                    url.searchParams.set(key, filters[key]);
                }
            });

            // تحديث URL بدون إعادة تحميل الصفحة
            window.history.pushState({}, '', url.toString());
        }

        /**
         * إعادة ضبط جميع حقول الفلتر
         */
        function resetFilters() {
            $('#search_term').val('');
            $('#branch_filter').val('');
            $('#date_from').val('');
            $('#date_to').val('');
            $('#loyalty_points_min').val('');
            $('#loyalty_points_max').val('');
            $('#visits_min').val('');
            $('#visits_max').val('');
        }

        /**
         * الحصول على قيم الفلاتر الحالية
         * @returns {Object} كائن يحتوي على قيم الفلاتر
         */
        function getFilters() {
            try {
                // جمع الفلاتر من النموذج
                // الحصول على قيم الحقول مباشرة
                const searchVal = $('#search_term').val() ? $('#search_term').val().trim() : '';
                const branchVal = $('#branch_filter').val() ? $('#branch_filter').val() : '';
                const dateFromVal = $('#date_from').val() ? $('#date_from').val().trim() : '';
                const dateToVal = $('#date_to').val() ? $('#date_to').val().trim() : '';
                const loyaltyMinVal = $('#loyalty_points_min').val() ? $('#loyalty_points_min').val().trim() : '';
                const loyaltyMaxVal = $('#loyalty_points_max').val() ? $('#loyalty_points_max').val().trim() : '';
                const visitsMinVal = $('#visits_min').val() ? $('#visits_min').val().trim() : '';
                const visitsMaxVal = $('#visits_max').val() ? $('#visits_max').val().trim() : '';

                // تسجيل قيم التاريخ للتصحيح
                console.log('Date from field value:', dateFromVal);
                console.log('Date to field value:', dateToVal);
                console.log('Date from field DOM element:', document.getElementById('date_from'));
                console.log('Date to field DOM element:', document.getElementById('date_to'));

                // إنشاء كائن الفلاتر
                const filters = {
                    search: searchVal,
                    branch_id: branchVal,
                    date_from: dateFromVal,
                    date_to: dateToVal,
                    loyalty_points_min: loyaltyMinVal,
                    loyalty_points_max: loyaltyMaxVal,
                    visits_min: visitsMinVal,
                    visits_max: visitsMaxVal
                };

                // تسجيل الفلاتر للتصحيح
                console.log('Current filters (before processing):', filters);

                // التحقق من صحة القيم العددية
                if (filters.loyalty_points_min && !isNaN(filters.loyalty_points_min)) {
                    filters.loyalty_points_min = parseInt(filters.loyalty_points_min);
                }

                if (filters.loyalty_points_max && !isNaN(filters.loyalty_points_max)) {
                    filters.loyalty_points_max = parseInt(filters.loyalty_points_max);
                }

                if (filters.visits_min && !isNaN(filters.visits_min)) {
                    filters.visits_min = parseInt(filters.visits_min);
                }

                if (filters.visits_max && !isNaN(filters.visits_max)) {
                    filters.visits_max = parseInt(filters.visits_max);
                }

                // تسجيل الفلاتر بعد المعالجة
                console.log('Current filters (after processing):', filters);

                return filters;
            } catch (error) {
                console.error('Error in getFilters():', error);
                // إرجاع كائن فارغ في حالة الخطأ
                return {
                    search: '',
                    branch_id: '',
                    date_from: '',
                    date_to: '',
                    loyalty_points_min: '',
                    loyalty_points_max: '',
                    visits_min: '',
                    visits_max: ''
                };
            }
        }

        /**
         * عرض رسالة توضح الفلاتر المستخدمة
         * @param {Object} filters كائن يحتوي على قيم الفلاتر
         */
        function displayFilterMessage(filters) {
            let filterMessage = 'تم البحث باستخدام الفلاتر التالية: ';
            let hasFilters = false;

            if (filters.search) {
                filterMessage += 'البحث: ' + filters.search + ', ';
                hasFilters = true;
            }

            if (filters.branch_id) {
                filterMessage += 'الفرع: ' + $('#branch_filter option:selected').text() + ', ';
                hasFilters = true;
            }

            if (filters.date_from) {
                filterMessage += 'تاريخ التسجيل من: ' + filters.date_from + ', ';
                hasFilters = true;
            }

            if (filters.date_to) {
                filterMessage += 'تاريخ التسجيل إلى: ' + filters.date_to + ', ';
                hasFilters = true;
            }

            if (filters.loyalty_points_min) {
                filterMessage += 'نقاط الولاء من: ' + filters.loyalty_points_min + ', ';
                hasFilters = true;
            }

            if (filters.loyalty_points_max) {
                filterMessage += 'نقاط الولاء إلى: ' + filters.loyalty_points_max + ', ';
                hasFilters = true;
            }

            if (filters.visits_min) {
                filterMessage += 'عدد الزيارات من: ' + filters.visits_min + ', ';
                hasFilters = true;
            }

            if (filters.visits_max) {
                filterMessage += 'عدد الزيارات إلى: ' + filters.visits_max + ', ';
                hasFilters = true;
            }

            // إزالة الفاصلة الأخيرة
            if (hasFilters) {
                filterMessage = filterMessage.slice(0, -2);
                showAlert(filterMessage, 'info');
            }
        }

        /**
         * تصدير البيانات إلى Excel
         */
        function exportToExcel() {
            // جمع الفلاتر
            const filters = getFilters();

            // بناء رابط التصدير
            let exportUrl = '../../api/export_customers.php?';

            // إضافة الفلاتر إلى الرابط
            Object.keys(filters).forEach(key => {
                if (filters[key]) {
                    exportUrl += `${key}=${encodeURIComponent(filters[key])}&`;
                }
            });

            // فتح نافذة جديدة للتصدير
            window.open(exportUrl, '_blank');
        }

        /**
         * تحميل الفلاتر من URL عند تحميل الصفحة
         */
        function loadFiltersFromUrl() {
            try {
                // الحصول على معلمات URL
                const urlParams = new URLSearchParams(window.location.search);
                let hasFilters = false;

                // تعيين قيم الفلاتر من URL
                if (urlParams.has('search')) {
                    $('#search_term').val(urlParams.get('search'));
                    hasFilters = true;
                }

                if (urlParams.has('branch_id')) {
                    $('#branch_filter').val(urlParams.get('branch_id'));
                    hasFilters = true;
                }

                if (urlParams.has('date_from')) {
                    $('#date_from').val(urlParams.get('date_from'));
                    hasFilters = true;
                }

                if (urlParams.has('date_to')) {
                    $('#date_to').val(urlParams.get('date_to'));
                    hasFilters = true;
                }

                if (urlParams.has('loyalty_points_min')) {
                    $('#loyalty_points_min').val(urlParams.get('loyalty_points_min'));
                    hasFilters = true;
                }

                if (urlParams.has('loyalty_points_max')) {
                    $('#loyalty_points_max').val(urlParams.get('loyalty_points_max'));
                    hasFilters = true;
                }

                if (urlParams.has('visits_min')) {
                    $('#visits_min').val(urlParams.get('visits_min'));
                    hasFilters = true;
                }

                if (urlParams.has('visits_max')) {
                    $('#visits_max').val(urlParams.get('visits_max'));
                    hasFilters = true;
                }

                // إذا كانت هناك فلاتر، فتح قسم الفلاتر المتقدمة
                if (hasFilters && (urlParams.has('date_from') || urlParams.has('date_to') ||
                    urlParams.has('loyalty_points_min') || urlParams.has('loyalty_points_max') ||
                    urlParams.has('visits_min') || urlParams.has('visits_max'))) {
                    // فتح قسم الفلاتر المتقدمة
                    $('#filterCollapse').collapse('show');
                }

                // تسجيل الفلاتر المحملة
                console.log('Loaded filters from URL:', getFilters());

                // إذا كانت هناك فلاتر، عرض رسالة توضح الفلاتر المستخدمة
                if (hasFilters) {
                    displayFilterMessage(getFilters());
                }
            } catch (error) {
                console.error('Error loading filters from URL:', error);
            }
        }

        // إعداد معالجات الفلتر
        setupFilterHandlers();

        // تحميل الفلاتر من URL عند تحميل الصفحة
        loadFiltersFromUrl();

        // التحقق من وجود رقم الهاتف عند الإضافة
        $('#phone').on('blur', function() {
            const phone = $(this).val();
            if (!phone) return;

            $.ajax({
                url: '../../api/customers.php',
                type: 'POST',
                data: {
                    action: 'check_phone_exists',
                    phone: phone
                },
                dataType: 'json',
                success: function(response) {
                    if (response.status === 'success' && response.exists) {
                        $('#phone_exists_warning').show();
                    } else {
                        $('#phone_exists_warning').hide();
                    }
                }
            });
        });

        // التحقق من وجود رقم الهاتف عند التعديل
        $('#edit_phone').on('blur', function() {
            const phone = $(this).val();
            const customerId = $('#edit_customer_id').val();
            if (!phone) return;

            $.ajax({
                url: '../../api/customers.php',
                type: 'POST',
                data: {
                    action: 'check_phone_exists',
                    phone: phone,
                    customer_id: customerId
                },
                dataType: 'json',
                success: function(response) {
                    if (response.status === 'success' && response.exists) {
                        $('#edit_phone_exists_warning').show();
                    } else {
                        $('#edit_phone_exists_warning').hide();
                    }
                }
            });
        });

        // إضافة عميل جديد
        $('#submitAddCustomer').on('click', function() {
            const form = $('#addCustomerForm')[0];
            if (!form.checkValidity()) {
                form.reportValidity();
                return;
            }

            const formData = new FormData(form);
            formData.append('action', 'add_customer');

            $.ajax({
                url: '../../api/customers.php',
                type: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                dataType: 'json',
                success: function(response) {
                    if (response.status === 'success') {
                        // إغلاق المودال وإعادة تحميل الجدول
                        $('#addCustomerModal').modal('hide');
                        form.reset();

                        showAlert(response.message);
                        customersTable.ajax.reload();
                    } else {
                        showAlert(response.message, 'danger');
                    }
                },
                error: function() {
                    showAlert('حدث خطأ أثناء إضافة العميل', 'danger');
                }
            });
        });

        // تحميل بيانات العميل للتعديل
        $(document).on('click', '.edit-customer', function() {
            const customerId = $(this).data('id');

            $.ajax({
                url: '../../api/customers.php',
                type: 'POST',
                data: {
                    action: 'get_customer',
                    id: customerId
                },
                dataType: 'json',
                success: function(response) {
                    if (response.status === 'success') {
                        const customer = response.customer;

                        $('#edit_customer_id').val(customer.id);
                        $('#edit_name').val(customer.name);
                        $('#edit_phone').val(customer.phone);
                        $('#edit_email').val(customer.email);
                        $('#edit_address').val(customer.address);
                        $('#edit_birthday').val(customer.birthday);
                        $('#edit_notes').val(customer.notes);

                        if ($('#edit_branch_id').length) {
                            $('#edit_branch_id').val(customer.branch_id);
                        }

                        $('#editCustomerModal').modal('show');
                    } else {
                        showAlert(response.message, 'danger');
                    }
                },
                error: function() {
                    showAlert('حدث خطأ أثناء تحميل بيانات العميل', 'danger');
                }
            });
        });

        // تعديل بيانات العميل
        $('#submitEditCustomer').on('click', function() {
            const form = $('#editCustomerForm')[0];
            if (!form.checkValidity()) {
                form.reportValidity();
                return;
            }

            const formData = new FormData(form);
            formData.append('action', 'update_customer');

            $.ajax({
                url: '../../api/customers.php',
                type: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                dataType: 'json',
                success: function(response) {
                    if (response.status === 'success') {
                        // إغلاق المودال وإعادة تحميل الجدول
                        $('#editCustomerModal').modal('hide');

                        showAlert(response.message);
                        customersTable.ajax.reload();
                    } else {
                        showAlert(response.message, 'danger');
                    }
                },
                error: function() {
                    showAlert('حدث خطأ أثناء تعديل بيانات العميل', 'danger');
                }
            });
        });

        // حذف عميل
        $(document).on('click', '.delete-customer', function() {
            const customerId = $(this).data('id');

            confirmAction('هل أنت متأكد من حذف هذا العميل؟', function() {
                $.ajax({
                    url: '../../api/customers.php',
                    type: 'POST',
                    data: {
                        action: 'delete_customer',
                        customer_id: customerId
                    },
                    dataType: 'json',
                    success: function(response) {
                        if (response.status === 'success') {
                            showAlert(response.message);
                            customersTable.ajax.reload();
                        } else {
                            showAlert(response.message, 'danger');
                        }
                    },
                    error: function() {
                        showAlert('حدث خطأ أثناء حذف العميل', 'danger');
                    }
                });
            });
        });

            // Note: The view-customer functionality has been replaced with direct links to view.php

        // تحميل فواتير العميل
        function loadCustomerInvoices(customerId) {
            $.ajax({
                url: '../../api/customers.php',
                type: 'POST',
                data: {
                    action: 'get_customer_invoices',
                    customer_id: customerId
                },
                dataType: 'json',
                success: function(response) {
                    if (response.status === 'success') {
                        const invoicesBody = $('#invoices_table_body');
                        invoicesBody.empty();

                        if (response.invoices && response.invoices.length > 0) {
                            $('#no_invoices_message').hide();
                            response.invoices.forEach(function(invoice) {
                                invoicesBody.append(`
                                    <tr>
                                        <td>${invoice.invoice_number}</td>
                                        <td>${moment(invoice.created_at).format('YYYY/MM/DD')}</td>
                                        <td>${invoice.final_amount}</td>
                                        <td>${getPaymentMethodText(invoice.payment_method)}</td>
                                        <td>
                                            <a href="../invoices/view.php?id=${invoice.id}" class="btn btn-sm btn-info">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                        </td>
                                    </tr>
                                `);
                            });
                        } else {
                            $('#no_invoices_message').show();
                        }
                    }
                }
            });
        }

        // الحصول على نص طريقة الدفع
        function getPaymentMethodText(method) {
            switch (method) {
                case 'cash': return 'نقد';
                case 'card': return 'بطاقة ائتمان';
                case 'other': return 'أخرى';
                default: return method;
            }
        }

                // Note: This functionality is no longer needed as we're redirecting to view.php instead of using a modal

        // فتح مودال إضافة نقاط الولاء
        $(document).on('click', '.add-loyalty-points', function() {
            const customerId = $(this).data('id');
            $('#loyalty_customer_id').val(customerId);
            $('#addLoyaltyPointsModal').modal('show');
        });

        // إضافة نقاط ولاء
        $('#submitAddLoyaltyPoints').on('click', function() {
            const form = $('#addLoyaltyPointsForm')[0];
            if (!form.checkValidity()) {
                form.reportValidity();
                return;
            }

            const customerId = $('#loyalty_customer_id').val();
            const points = $('#points').val();

            $.ajax({
                url: '../../api/customers.php',
                type: 'POST',
                data: {
                    action: 'add_loyalty_points',
                    customer_id: customerId,
                    points: points
                },
                dataType: 'json',
                success: function(response) {
                    if (response.status === 'success') {
                        $('#addLoyaltyPointsModal').modal('hide');
                        showAlert(response.message);
                        customersTable.ajax.reload();
                    } else {
                        showAlert(response.message, 'danger');
                    }
                },
                error: function() {
                    showAlert('حدث خطأ أثناء إضافة نقاط الولاء', 'danger');
                }
            });
        });

        // توجيه إلى صفحة حجز المواعيد
        $(document).on('click', '.add-appointment', function() {
            const customerId = $(this).data('id');
            window.location.href = `../appointments/add.php?customer_id=${customerId}`;
        });

        // توجيه من زر حجز موعد في مودال التفاصيل
        $('#addAppointmentBtn').on('click', function() {
            const customerId = $(this).data('id');
            window.location.href = `../appointments/add.php?customer_id=${customerId}`;
        });
    });
</script>
