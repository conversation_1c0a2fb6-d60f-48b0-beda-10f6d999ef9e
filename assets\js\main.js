/**
 * الملف الرئيسي للتطبيق
 * يحتوي على دوال مشتركة وإعدادات أساسية لجميع صفحات النظام
 *
 * @version 1.0.0
 */

// تنفيذ الكود بعد اكتمال تحميل الصفحة
$(document).ready(function() {
    // تهيئة المكونات المشتركة
    initializeCommonComponents();

    // تنفيذ دوال خاصة بالصفحات
    handlePageSpecificFunctions();

    // تهيئة إعدادات النظام
    setSystemPreferences();

    // تهيئة نظام التنبيهات
    initializeNotificationSystem();

    // تهيئة الدوال المساعدة
    initializeHelperFunctions();

    // تهيئة عناصر التنقل
    initializeNavigation();

    // فحص وجود تنبيهات نظام
    checkSystemAlerts();
});

/**
 * تهيئة المكونات المشتركة لجميع الصفحات
 */
function initializeCommonComponents() {
    // تفعيل tooltips
    $('[data-toggle="tooltip"]').tooltip();

    // تفعيل popovers
    $('[data-toggle="popover"]').popover();

    // تهيئة القوائم المنسدلة
    initializeSelect2();

    // تهيئة حقول التاريخ
    initializeDatepickers();

    // تهيئة الجداول
    initializeDatatables();

    // تهيئة النوافذ المنبثقة (modals)
    initializeModals();

    // تهيئة النماذج وحقول الإدخال
    initializeForms();

    // تهيئة أزرار الإجراءات
    initializeActionButtons();
}

/**
 * تهيئة القوائم المنسدلة باستخدام Select2
 */
function initializeSelect2() {
    // تطبيق إعدادات Select2 الافتراضية
    if ($.fn.select2) {
        // الإعدادات الافتراضية للقوائم المنسدلة
        $.fn.select2.defaults.set("theme", "bootstrap");
        $.fn.select2.defaults.set("allowClear", true);
        $.fn.select2.defaults.set("placeholder", "اختر...");
        $.fn.select2.defaults.set("language", "ar");
        $.fn.select2.defaults.set("dir", "rtl");

        // تهيئة جميع العناصر المطلوبة
        $('select.select2').select2();

        // إعادة تهيئة القوائم المنسدلة داخل النوافذ المنبثقة
        $('body').on('shown.bs.modal', '.modal', function() {
            $(this).find('select.select2').select2({
                dropdownParent: $(this)
            });
        });

        // معالجة حدث الإغلاق
        $('select.select2').on('select2:unselecting', function() {
            $(this).data('unselecting', true);
        }).on('select2:opening', function(e) {
            if ($(this).data('unselecting')) {
                $(this).removeData('unselecting');
                e.preventDefault();
            }
        });
    }
}

/**
 * تهيئة حقول التاريخ
 */
function initializeDatepickers() {
    // تطبيق إعدادات التقويم الهجري إذا كان متاحًا
    if ($.fn.datepicker) {
        // الإعدادات الافتراضية للتقويم
        $.fn.datepicker.defaults.format = "yyyy-mm-dd";
        $.fn.datepicker.defaults.autoclose = true;
        $.fn.datepicker.defaults.language = "ar";
        $.fn.datepicker.defaults.rtl = true;

        // تهيئة حقول التاريخ
        $('.datepicker').datepicker();

        // تهيئة حقول التاريخ المزدوجة (من - إلى)
        $('.date-range').each(function() {
            var startDate = $(this).find('.start-date');
            var endDate = $(this).find('.end-date');

            startDate.datepicker({
                format: 'yyyy-mm-dd',
                autoclose: true
            }).on('changeDate', function(selected) {
                var minDate = new Date(selected.date.valueOf());
                endDate.datepicker('setStartDate', minDate);
            });

            endDate.datepicker({
                format: 'yyyy-mm-dd',
                autoclose: true
            }).on('changeDate', function(selected) {
                var maxDate = new Date(selected.date.valueOf());
                startDate.datepicker('setEndDate', maxDate);
            });
        });
    }
}

/**
 * تهيئة جداول البيانات
 */
function initializeDatatables() {
    if ($.fn.DataTable) {
        // الإعدادات الافتراضية للجداول
        $.extend(true, $.fn.dataTable.defaults, {
            language: {
                url: ASSETS_URL + "plugins/datatables/Arabic.json"
            },
            responsive: true,
            autoWidth: false,
            processing: true,
            serverSide: false,
            dom: "<'row'<'col-sm-12 col-md-6'l><'col-sm-12 col-md-6'f>>" +
                 "<'row'<'col-sm-12'tr>>" +
                 "<'row'<'col-sm-12 col-md-5'i><'col-sm-12 col-md-7'p>>",
            lengthMenu: [[10, 25, 50, 100, -1], [10, 25, 50, 100, "الكل"]]
        });

        // تهيئة الجداول الافتراضية
        $('.datatable').DataTable();
    }
}

/**
 * تهيئة النوافذ المنبثقة
 */
function initializeModals() {
    // تحديث القوائم المنسدلة عند فتح النافذة المنبثقة
    $(document).on('shown.bs.modal', '.modal', function() {
        $(this).find('select.select2').select2({
            dropdownParent: $(this)
        });
    });

    // تهيئة النوافذ المنبثقة الديناميكية
    $(document).on('click', '[data-toggle="ajax-modal"]', function(e) {
        e.preventDefault();
        var url = $(this).attr('href') || $(this).data('url');
        var target = $(this).data('target') || '#ajax-modal';

        if (url) {
            // عرض مؤشر التحميل
            showLoading();

            // جلب محتوى النافذة المنبثقة
            $.get(url, function(response) {
                $(target).html(response).modal('show');
                hideLoading();
            }).fail(function() {
                hideLoading();
                showNotification('error', 'حدث خطأ أثناء جلب البيانات');
            });
        }
    });

    // تنظيف موارد النافذة المنبثقة عند إغلاقها
    $(document).on('hidden.bs.modal', '.modal', function() {
        $(this).removeData('bs.modal');
    });
}

/**
 * تهيئة النماذج وحقول الإدخال
 */
function initializeForms() {
    // أقنعة الإدخال (Input Masks)
    if ($.fn.inputmask) {
        // قناع رقم الهاتف
        $('.phone-mask').inputmask('9999999999', { placeholder: '05xxxxxxxx' });

        // قناع التاريخ
        $('.date-mask').inputmask('9999-99-99', { placeholder: 'yyyy-mm-dd' });

        // قناع الأرقام
        $('.numeric-mask').inputmask('numeric', {
            rightAlign: false,
            groupSeparator: ',',
            autoGroup: true
        });

        // قناع المبالغ المالية
        $('.currency-mask').inputmask('numeric', {
            rightAlign: false,
            groupSeparator: ',',
            autoGroup: true,
            digits: 2,
            digitsOptional: false,
            placeholder: '0.00'
        });
    }

    // التحقق من صحة النماذج
    if ($.fn.validate) {
        // الإعدادات الافتراضية للتحقق
        $.validator.setDefaults({
            errorElement: 'span',
            errorPlacement: function(error, element) {
                error.addClass('invalid-feedback');
                element.closest('.form-group').append(error);
            },
            highlight: function(element, errorClass, validClass) {
                $(element).addClass('is-invalid');
            },
            unhighlight: function(element, errorClass, validClass) {
                $(element).removeClass('is-invalid');
            }
        });

        // رسائل التحقق باللغة العربية
        $.extend($.validator.messages, {
            required: "هذا الحقل مطلوب",
            remote: "يرجى تصحيح هذا الحقل",
            email: "يرجى إدخال عنوان بريد إلكتروني صحيح",
            url: "يرجى إدخال رابط صحيح",
            date: "يرجى إدخال تاريخ صحيح",
            dateISO: "يرجى إدخال تاريخ صحيح (ISO)",
            number: "يرجى إدخال رقم صحيح",
            digits: "يرجى إدخال أرقام فقط",
            creditcard: "يرجى إدخال رقم بطاقة ائتمان صحيح",
            equalTo: "يرجى إدخال نفس القيمة مرة أخرى",
            maxlength: $.validator.format("يرجى إدخال ما لا يزيد عن {0} حرف"),
            minlength: $.validator.format("يرجى إدخال ما لا يقل عن {0} حرف"),
            rangelength: $.validator.format("يرجى إدخال قيمة بين {0} و {1} حرف"),
            range: $.validator.format("يرجى إدخال قيمة بين {0} و {1}"),
            max: $.validator.format("يرجى إدخال قيمة أقل من أو تساوي {0}"),
            min: $.validator.format("يرجى إدخال قيمة أكبر من أو تساوي {0}")
        });

        // تهيئة نماذج التحقق
        $('.validate-form').validate();
    }

    // تهيئة معالجة النماذج بـ AJAX
    $(document).on('submit', '.ajax-form', function(e) {
        e.preventDefault();

        var form = $(this);
        var url = form.attr('action');
        var method = form.attr('method') || 'POST';
        var formData = new FormData(this);
        var submitButton = form.find('[type="submit"]');
        var originalText = submitButton.html();

        // عرض مؤشر التحميل
        submitButton.html('<i class="fas fa-spinner fa-spin"></i> جارِ الإرسال...').prop('disabled', true);

        // إرسال النموذج
        AjaxHandler.request({
            url: url,
            method: method,
            data: formData,
            contentType: false,
            processData: false,
            success: function(response) {
                if (response.status === 'success') {
                    showNotification('success', response.message || 'تم الحفظ بنجاح');

                    // تنظيف النموذج
                    if (form.data('clear-after-submit')) {
                        form[0].reset();
                        form.find('select.select2').val(null).trigger('change');
                    }

                    // إغلاق النافذة المنبثقة إذا وجدت
                    if (form.closest('.modal').length) {
                        setTimeout(function() {
                            form.closest('.modal').modal('hide');
                        }, 1000);
                    }

                    // إعادة تحميل الصفحة إذا كان مطلوبًا
                    if (form.data('reload-after-submit')) {
                        setTimeout(function() {
                            window.location.reload();
                        }, 1000);
                    }

                    // إعادة توجيه إذا كان مطلوبًا
                    if (form.data('redirect-after-submit')) {
                        setTimeout(function() {
                            window.location.href = form.data('redirect-after-submit');
                        }, 1000);
                    }

                    // تنفيذ دالة مخصصة إذا كان مطلوبًا
                    if (form.data('callback-after-submit')) {
                        var callbackFunction = window[form.data('callback-after-submit')];
                        if (typeof callbackFunction === 'function') {
                            callbackFunction(response);
                        }
                    }
                } else {
                    showNotification('error', response.message || 'حدث خطأ أثناء المعالجة');
                }
            },
            error: function(xhr, status, error) {
                showNotification('error', 'حدث خطأ أثناء الاتصال بالخادم');
            },
            complete: function() {
                // إعادة زر الإرسال
                submitButton.html(originalText).prop('disabled', false);
            }
        });
    });
}

/**
 * تهيئة أزرار الإجراءات
 */
function initializeActionButtons() {
    // أزرار الحذف مع التأكيد
    $(document).on('click', '.btn-delete', function(e) {
        e.preventDefault();

        var button = $(this);
        var url = button.attr('href') || button.data('url');
        var message = button.data('confirm-message') || 'هل أنت متأكد من حذف هذا العنصر؟';
        var title = button.data('confirm-title') || 'تأكيد الحذف';

        if (url) {
            // عرض رسالة التأكيد
            swal({
                title: title,
                text: message,
                icon: "warning",
                buttons: {
                    cancel: {
                        text: "إلغاء",
                        visible: true
                    },
                    confirm: {
                        text: "نعم، حذف"
                    }
                },
                dangerMode: true
            }).then(function(confirmed) {
                if (confirmed) {
                    // عرض مؤشر التحميل
                    showLoading();

                    // إرسال طلب الحذف
                    AjaxHandler.request({
                        url: url,
                        method: 'DELETE',
                        success: function(response) {
                            if (response.status === 'success') {
                                showNotification('success', response.message || 'تم الحذف بنجاح');

                                // إزالة الصف من الجدول إذا كان موجودًا
                                if (button.closest('tr').length) {
                                    button.closest('tr').fadeOut(300, function() {
                                        $(this).remove();
                                    });
                                }

                                // إعادة تحميل الصفحة إذا كان مطلوبًا
                                if (button.data('reload-after-delete')) {
                                    setTimeout(function() {
                                        window.location.reload();
                                    }, 1000);
                                }
                            } else {
                                showNotification('error', response.message || 'حدث خطأ أثناء الحذف');
                            }
                        },
                        error: function() {
                            showNotification('error', 'حدث خطأ أثناء الاتصال بالخادم');
                        },
                        complete: function() {
                            hideLoading();
                        }
                    });
                }
            });
        }
    });

    // أزرار الإجراءات العامة
    $(document).on('click', '.btn-action', function(e) {
        e.preventDefault();

        var button = $(this);
        var url = button.attr('href') || button.data('url');
        var method = button.data('method') || 'GET';
        var confirmMessage = button.data('confirm-message');

        var performAction = function() {
            // عرض مؤشر التحميل
            showLoading();

            // إرسال طلب الإجراء
            AjaxHandler.request({
                url: url,
                method: method,
                success: function(response) {
                    if (response.status === 'success') {
                        showNotification('success', response.message || 'تم الإجراء بنجاح');

                        // إعادة تحميل الصفحة إذا كان مطلوبًا
                        if (button.data('reload-after-action')) {
                            setTimeout(function() {
                                window.location.reload();
                            }, 1000);
                        }

                        // إعادة توجيه إذا كان مطلوبًا
                        if (button.data('redirect-after-action')) {
                            setTimeout(function() {
                                window.location.href = button.data('redirect-after-action');
                            }, 1000);
                        }
                    } else {
                        showNotification('error', response.message || 'حدث خطأ أثناء تنفيذ الإجراء');
                    }
                },
                error: function() {
                    showNotification('error', 'حدث خطأ أثناء الاتصال بالخادم');
                },
                complete: function() {
                    hideLoading();
                }
            });
        };

        if (confirmMessage) {
            // عرض رسالة التأكيد
            swal({
                title: button.data('confirm-title') || 'تأكيد الإجراء',
                text: confirmMessage,
                icon: "info",
                buttons: {
                    cancel: {
                        text: "إلغاء",
                        visible: true
                    },
                    confirm: {
                        text: "نعم، تنفيذ"
                    }
                }
            }).then(function(confirmed) {
                if (confirmed) {
                    performAction();
                }
            });
        } else {
            performAction();
        }
    });
}

/**
 * تنفيذ دوال خاصة بكل صفحة
 */
function handlePageSpecificFunctions() {
    // إنشاء مسار الصفحة الحالية
    var currentPath = window.location.pathname;
    var pageName = currentPath.split('/').pop().replace('.php', '');
    var parentDir = currentPath.split('/').slice(-2, -1)[0];

    // تنفيذ دوال مخصصة بناءً على الصفحة الحالية
    switch (parentDir) {
        case 'dashboard':
            initializeDashboard();
            break;
        case 'pos':
            initializePOS();
            break;
    }

    // ضبط الرابط النشط في القائمة الجانبية
    setActiveMenu(currentPath);
}

/**
 * تهيئة لوحة التحكم
 */
function initializeDashboard() {
    // تحميل المؤشرات الرئيسية
    loadDashboardStats();

    // تهيئة المخططات البيانية
    initializeCharts();

    // تهيئة قائمة المواعيد
    initializeAppointmentsCalendar();

    // تحديث البيانات تلقائيًا (كل 5 دقائق)
    setInterval(function() {
        loadDashboardStats();
    }, 300000);
}

/**
 * تحميل إحصائيات لوحة التحكم
 */
function loadDashboardStats() {
    AjaxHandler.request({
        url: 'reports.php',
        method: 'GET',
        data: {
            type: 'dashboard_stats'
        },
        success: function(response) {
            if (response.success) {
                // تحديث إحصائيات المبيعات
                $('#today-sales').text(formatCurrency(response.data.today_sales));
                $('#monthly-sales').text(formatCurrency(response.data.monthly_sales));
                $('#today-invoices').text(response.data.today_invoices);
                $('#today-appointments').text(response.data.today_appointments);

                // تحديث الرسوم البيانية إذا وجدت
                if (window.salesChart) {
                    updateSalesChart(response.data.sales_chart_data);
                }

                if (window.servicesChart) {
                    updateServicesChart(response.data.top_services);
                }
            }
        }
    });
}

/**
 * تهيئة نقطة البيع
 */
function initializePOS() {
    // تهيئة إضافة العناصر للفاتورة
    initializePOSItemsSelection();

    // تهيئة اختيار العميل
    initializeCustomerSelection();

    // تهيئة احتساب المبالغ
    initializeAmountCalculation();

    // تهيئة طباعة الفاتورة
    initializeInvoicePrinting();
}

/**
 * تهيئة اختيار العناصر في نقطة البيع
 */
function initializePOSItemsSelection() {
    // إضافة عنصر للفاتورة
    $(document).on('click', '.add-item-btn', function() {
        var itemType = $(this).data('type');
        var itemId = $(this).data('id');
        var itemName = $(this).data('name');
        var itemPrice = parseFloat($(this).data('price')) || 0;

        addItemToInvoice(itemType, itemId, itemName, itemPrice);
    });

    // حذف عنصر من الفاتورة
    $(document).on('click', '.remove-item-btn', function() {
        $(this).closest('tr').remove();
        calculateTotals();
    });

    // تغيير كمية العنصر
    $(document).on('change', '.item-quantity', function() {
        var row = $(this).closest('tr');
        var quantity = parseInt($(this).val()) || 1;
        var price = parseFloat(row.find('.item-price').text());
        var total = quantity * price;

        row.find('.item-total').text(formatCurrency(total));
        calculateTotals();
    });
}

/**
 * إضافة عنصر للفاتورة
 */
function addItemToInvoice(type, id, name, price) {
    var existingRow = $(`#invoice-items-table tr[data-item-id="${id}"][data-item-type="${type}"]`);

    if (existingRow.length > 0) {
        // تحديث الكمية إذا كان العنصر موجودًا
        var quantityInput = existingRow.find('.item-quantity');
        var currentQuantity = parseInt(quantityInput.val()) || 1;
        quantityInput.val(currentQuantity + 1).trigger('change');
    } else {
        // إضافة صف جديد
        var html = `
            <tr data-item-id="${id}" data-item-type="${type}">
                <td>${name}</td>
                <td class="item-price">${price.toFixed(2)}</td>
                <td>
                    <input type="number" class="form-control form-control-sm item-quantity" min="1" value="1">
                    <input type="hidden" name="items[${type}][${id}][id]" value="${id}">
                    <input type="hidden" name="items[${type}][${id}][type]" value="${type}">
                    <input type="hidden" name="items[${type}][${id}][price]" value="${price.toFixed(2)}">
                </td>
                <td class="item-total">${formatCurrency(price)}</td>
                <td>
                    <button type="button" class="btn btn-sm btn-danger remove-item-btn"><i class="fas fa-times"></i></button>
                </td>
            </tr>
        `;

        $("#invoice-items-table tbody").append(html);
        calculateTotals();
    }
}

/**
 * حساب المبالغ الإجمالية
 */
function calculateTotals() {
    var subtotal = 0;

    // حساب المجموع الفرعي
    $("#invoice-items-table tbody tr").each(function() {
        var price = parseFloat($(this).find('.item-price').text()) || 0;
        var quantity = parseInt($(this).find('.item-quantity').val()) || 1;
        var total = price * quantity;

        subtotal += total;
        $(this).find('.item-total').text(formatCurrency(total));
        $(this).find("input[name$='[quantity]']").val(quantity);
    });

    // تطبيق الخصم والضريبة
    var discountType = $("#discount-type").val();
    var discountValue = parseFloat($("#discount-value").val()) || 0;
    var taxRate = parseFloat($("#tax-rate").val()) || 0;

    var discountAmount = 0;
    if (discountType === 'percentage') {
        discountAmount = subtotal * (discountValue / 100);
    } else {
        discountAmount = discountValue;
    }

    var afterDiscount = subtotal - discountAmount;
    var taxAmount = afterDiscount * (taxRate / 100);
    var total = afterDiscount + taxAmount;

    // تحديث القيم
    $("#subtotal").text(formatCurrency(subtotal));
    $("#discount-amount").text(formatCurrency(discountAmount));
    $("#tax-amount").text(formatCurrency(taxAmount));
    $("#total-amount").text(formatCurrency(total));

    // تحديث الحقول الخفية
    $("#input-subtotal").val(subtotal.toFixed(2));
    $("#input-discount-amount").val(discountAmount.toFixed(2));
    $("#input-tax-amount").val(taxAmount.toFixed(2));
    $("#input-total-amount").val(total.toFixed(2));

    // تفعيل أو تعطيل زر الحفظ
    $("#save-invoice-btn").prop('disabled', subtotal <= 0);
}

/**
 * تهيئة نظام التنبيهات
 */
function initializeNotificationSystem() {
    // عرض التنبيهات من Toastr إذا كان متاحًا
    if (typeof toastr !== 'undefined') {
        // إعدادات Toastr
        toastr.options = {
            "closeButton": true,
            "progressBar": true,
            "positionClass": "toast-top-left",
            "showDuration": "300",
            "hideDuration": "1000",
            "timeOut": "5000",
            "extendedTimeOut": "1000",
            "showEasing": "swing",
            "hideEasing": "linear",
            "showMethod": "fadeIn",
            "hideMethod": "fadeOut",
            "rtl": true
        };
    }

    // إعدادات SweetAlert
    if (typeof swal !== 'undefined') {
        swal.setDefaults({
            buttons: {
                confirm: {
                    text: "نعم",
                    className: "btn btn-primary"
                },
                cancel: {
                    text: "إلغاء",
                    visible: true,
                    className: "btn btn-secondary"
                }
            }
        });
    }
}

/**
 * تهيئة عناصر التنقل
 */
function initializeNavigation() {
    // فتح/إغلاق القائمة الجانبية
    $("#sidebarToggle").on('click', function() {
        $("body").toggleClass("sidebar-minimized");
        $('.sidebar').toggleClass('collapsed');

        // حفظ حالة القائمة في التخزين المحلي
        localStorage.setItem('sidebar-collapsed', $("body").hasClass("sidebar-minimized"));
    });

// استرجاع حالة القائمة من التخزين المحلي
if (localStorage.getItem('sidebar-collapsed') === 'true') {
    $("body").addClass("sidebar-minimized");
    $('.sidebar').addClass('collapsed');
}

// تمدد/طي القوائم الفرعية
$('.sidebar-nav .has-submenu > a').on('click', function(e) {
    e.preventDefault();
    $(this).parent().toggleClass('open');
    $(this).next('.submenu').slideToggle(200);
});

// فتح القائمة الفرعية النشطة تلقائيًا
$('.sidebar-nav .has-submenu').each(function() {
    if ($(this).find('a.active').length) {
        $(this).addClass('open');
        $(this).find('.submenu').show();
    }
});

// التبديل إلى وضع الشاشة الكاملة
$("#fullscreenToggle").on('click', function() {
    toggleFullScreen();
});
}

/**
* ضبط القائمة النشطة
* @param {string} currentPath مسار الصفحة الحالية
*/
function setActiveMenu(currentPath) {
// إزالة الفئة النشطة من جميع الروابط
$('.sidebar-nav .nav-item .nav-link').removeClass('active');

// تحديد العنصر النشط
$('.sidebar-nav .nav-item .nav-link').each(function() {
    var link = $(this).attr('href');

    if (link) {
        // مطابقة بسيطة للمسار
        if (currentPath === link || currentPath.indexOf(link) > -1) {
            $(this).addClass('active');

            // فتح القائمة الأب إذا كانت موجودة
            $(this).parents('.has-submenu').addClass('open');
            $(this).parents('.submenu').show();
        }
    }
});
}

/**
* التبديل إلى وضع ملء الشاشة
*/
function toggleFullScreen() {
if (!document.fullscreenElement &&
    !document.mozFullScreenElement &&
    !document.webkitFullscreenElement &&
    !document.msFullscreenElement) {

    // طلب وضع ملء الشاشة
    if (document.documentElement.requestFullscreen) {
        document.documentElement.requestFullscreen();
    } else if (document.documentElement.msRequestFullscreen) { // Internet Explorer
        document.documentElement.msRequestFullscreen();
    } else if (document.documentElement.mozRequestFullScreen) { // Firefox
        document.documentElement.mozRequestFullScreen();
    } else if (document.documentElement.webkitRequestFullscreen) { // Chrome, Safari, Opera
        document.documentElement.webkitRequestFullscreen(Element.ALLOW_KEYBOARD_INPUT);
    }

    $("#fullscreenToggle i").removeClass('fa-expand').addClass('fa-compress');
} else {
    // إلغاء وضع ملء الشاشة
    if (document.exitFullscreen) {
        document.exitFullscreen();
    } else if (document.msExitFullscreen) { // Internet Explorer
        document.msExitFullscreen();
    } else if (document.mozCancelFullScreen) { // Firefox
        document.mozCancelFullScreen();
    } else if (document.webkitExitFullscreen) { // Chrome, Safari, Opera
        document.webkitExitFullscreen();
    }

    $("#fullscreenToggle i").removeClass('fa-compress').addClass('fa-expand');
}
}

/**
* تهيئة إعدادات النظام
*/
function setSystemPreferences() {
// استرجاع الإعدادات من التخزين المحلي
loadUserPreferences();

// حفظ الإعدادات في التخزين المحلي عند التغيير
$("#settings-form").on('change', 'input, select', function() {
    var preferenceKey = $(this).attr('name');
    var preferenceValue = $(this).val();

    if ($(this).attr('type') === 'checkbox') {
        preferenceValue = $(this).prop('checked') ? 1 : 0;
    }

    // حفظ الإعداد في التخزين المحلي
    saveUserPreference(preferenceKey, preferenceValue);

    // تطبيق الإعداد فورًا
    applyPreferenceSetting(preferenceKey, preferenceValue);
});
}

/**
* تحميل إعدادات المستخدم
*/
function loadUserPreferences() {
// استرجاع إعدادات التقويم
var calendarFirstDay = localStorage.getItem('calendar_first_day') || '0';
$("#calendar-first-day").val(calendarFirstDay);

// استرجاع إعدادات الجداول
var tablePageSize = localStorage.getItem('table_page_size') || '10';
$("#table-page-size").val(tablePageSize);

// استرجاع إعدادات الطباعة
var receiptWidth = localStorage.getItem('receipt_width') || '80';
$("#receipt-width").val(receiptWidth);

// استرجاع إعدادات الإشعارات
var enableNotifications = localStorage.getItem('enable_notifications') !== '0';
$("#enable-notifications").prop('checked', enableNotifications);

// تطبيق الإعدادات
applyUserPreferences();
}

/**
* حفظ إعداد المستخدم
* @param {string} key مفتاح الإعداد
* @param {*} value قيمة الإعداد
*/
function saveUserPreference(key, value) {
localStorage.setItem(key, value);
}

/**
* تطبيق إعدادات المستخدم
*/
function applyUserPreferences() {
// تطبيق إعدادات التقويم
var calendarFirstDay = localStorage.getItem('calendar_first_day') || '0';
if ($.fn.datepicker) {
    $.fn.datepicker.defaults.weekStart = parseInt(calendarFirstDay);
}

// تطبيق إعدادات الجداول
var tablePageSize = localStorage.getItem('table_page_size') || '10';
if ($.fn.DataTable) {
    $.fn.dataTable.defaults.pageLength = parseInt(tablePageSize);
}

// تطبيق إعدادات الإشعارات
var enableNotifications = localStorage.getItem('enable_notifications') !== '0';
if (!enableNotifications && typeof toastr !== 'undefined') {
    toastr.options.timeOut = 0;
    toastr.options.extendedTimeOut = 0;
}
}

/**
* تطبيق إعداد محدد
* @param {string} key مفتاح الإعداد
* @param {*} value قيمة الإعداد
*/
function applyPreferenceSetting(key, value) {
switch (key) {
    case 'calendar_first_day':
        if ($.fn.datepicker) {
            $.fn.datepicker.defaults.weekStart = parseInt(value);
            $('.datepicker').datepicker('update');
        }
        break;

    case 'table_page_size':
        if ($.fn.DataTable) {
            // سيتم تطبيق هذا الإعداد على الجداول الجديدة
            $.fn.dataTable.defaults.pageLength = parseInt(value);

            // إعادة تهيئة الجداول الحالية
            $.fn.dataTable.tables({ api: true }).page.len(parseInt(value)).draw();
        }
        break;

    case 'enable_notifications':
        if (typeof toastr !== 'undefined') {
            if (value === '0' || value === 0) {
                toastr.options.timeOut = 0;
                toastr.options.extendedTimeOut = 0;
            } else {
                toastr.options.timeOut = 5000;
                toastr.options.extendedTimeOut = 1000;
            }
        }
        break;
}
}

/**
* تهيئة الدوال المساعدة
*/
function initializeHelperFunctions() {
// تهيئة دوال تنسيق البيانات
initializeFormatFunctions();

// تهيئة دوال مساعدة للطباعة
initializePrintFunctions();

// تهيئة دوال مساعدة للتصدير
initializeExportFunctions();
}

/**
* تهيئة دوال تنسيق البيانات
*/
function initializeFormatFunctions() {
// تنسيق التاريخ والوقت
if (typeof Intl !== 'undefined') {
    // تنسيق التاريخ حسب اللغة العربية
    window.dateFormatter = new Intl.DateTimeFormat('ar-SA', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    });

    // تنسيق الوقت حسب اللغة العربية
    window.timeFormatter = new Intl.DateTimeFormat('ar-SA', {
        hour: '2-digit',
        minute: '2-digit'
    });

    // تنسيق التاريخ والوقت معًا
    window.dateTimeFormatter = new Intl.DateTimeFormat('ar-SA', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    });

    // تنسيق العملة حسب العملة من قاعدة البيانات
    // استخدام المتغير currencySymbol إذا كان متاحًا
    window.currencySymbol = typeof currencySymbol !== 'undefined' ? currencySymbol : 'ر.س';

    // دالة تنسيق العملة
    window.formatCurrency = function(amount) {
        return parseFloat(amount).toFixed(2) + ' ' + window.currencySymbol;
    };
}
}

/**
* تنسيق التاريخ
* @param {string|Date} date التاريخ المراد تنسيقه
* @return {string} التاريخ المنسق
*/
function formatDate(date) {
if (!date) return '';

var dateObj = typeof date === 'object' ? date : new Date(date);

if (window.dateFormatter) {
    return window.dateFormatter.format(dateObj);
} else {
    var year = dateObj.getFullYear();
    var month = (dateObj.getMonth() + 1).toString().padStart(2, '0');
    var day = dateObj.getDate().toString().padStart(2, '0');
    return year + '-' + month + '-' + day;
}
}

/**
* تنسيق الوقت
* @param {string|Date} time الوقت المراد تنسيقه
* @return {string} الوقت المنسق
*/
function formatTime(time) {
if (!time) return '';

var timeObj = typeof time === 'object' ? time : new Date(time);

if (window.timeFormatter) {
    return window.timeFormatter.format(timeObj);
} else {
    var hours = timeObj.getHours().toString().padStart(2, '0');
    var minutes = timeObj.getMinutes().toString().padStart(2, '0');
    return hours + ':' + minutes;
}
}

/**
* تنسيق التاريخ والوقت
* @param {string|Date} dateTime التاريخ والوقت المراد تنسيقهما
* @return {string} التاريخ والوقت المنسقان
*/
function formatDateTime(dateTime) {
if (!dateTime) return '';

var dateTimeObj = typeof dateTime === 'object' ? dateTime : new Date(dateTime);

if (window.dateTimeFormatter) {
    return window.dateTimeFormatter.format(dateTimeObj);
} else {
    return formatDate(dateTimeObj) + ' ' + formatTime(dateTimeObj);
}
}

/**
* تنسيق المبلغ بصيغة العملة
* @param {number} amount المبلغ المراد تنسيقه
* @return {string} المبلغ المنسق
*/
function formatCurrency(amount) {
if (amount === null || amount === undefined) return '';

if (window.currencyFormatter) {
    return window.currencyFormatter.format(amount);
} else {
    return amount.toFixed(2) + ' ر.س';
}
}

/**
* تنسيق النسبة المئوية
* @param {number} value القيمة المراد تنسيقها
* @return {string} النسبة المنسقة
*/
function formatPercentage(value) {
if (value === null || value === undefined) return '';
return value.toFixed(2) + '%';
}

/**
* تهيئة دوال مساعدة للطباعة
*/
function initializePrintFunctions() {
// طباعة الفاتورة
window.printInvoice = function(invoiceId, width) {
    var url = BASE_URL + 'pages/pos/print.php?id=' + invoiceId;
    if (width) {
        url += '&width=' + width;
    }

    // فتح نافذة الطباعة
    var printWindow = window.open(url, 'invoice_print', 'width=800,height=600');

    // طباعة تلقائية بعد تحميل الصفحة
    printWindow.onload = function() {
        setTimeout(function() {
            printWindow.print();
        }, 500);
    };
};

// طباعة تقرير
window.printReport = function(reportUrl) {
    // فتح نافذة الطباعة
    var printWindow = window.open(reportUrl, 'report_print', 'width=800,height=600');

    // طباعة تلقائية بعد تحميل الصفحة
    printWindow.onload = function() {
        setTimeout(function() {
            printWindow.print();
        }, 500);
    };
};
}

/**
* تهيئة دوال مساعدة للتصدير
*/
function initializeExportFunctions() {
// تصدير جدول إلى Excel
window.exportTableToExcel = function(tableId, fileName) {
    if (!tableId || !document.getElementById(tableId)) {
        console.error('جدول غير موجود: ' + tableId);
        return;
    }

    var table = document.getElementById(tableId);
    var html = table.outerHTML;

    // إضافة ترميز UTF-8 BOM للدعم العربي
    var blob = new Blob(['\ufeff', html], {
        type: 'application/vnd.ms-excel'
    });

    // إنشاء رابط تنزيل
    var a = document.createElement('a');
    var url = URL.createObjectURL(blob);
    a.href = url;
    a.download = (fileName || 'export') + '.xls';
    document.body.appendChild(a);
    a.click();

    // تنظيف الموارد
    setTimeout(function() {
        document.body.removeChild(a);
        window.URL.revokeObjectURL(url);
    }, 0);
};

// تصدير جدول إلى PDF
window.exportTableToPdf = function(tableId, fileName, orientation) {
    if (!tableId || !document.getElementById(tableId)) {
        console.error('جدول غير موجود: ' + tableId);
        return;
    }

    var table = document.getElementById(tableId);

    // تهيئة إعدادات PDF
    var opt = {
        margin: 10,
        filename: (fileName || 'export') + '.pdf',
        image: { type: 'jpeg', quality: 0.98 },
        html2canvas: { scale: 2, useCORS: true },
        jsPDF: { unit: 'mm', format: 'a4', orientation: orientation || 'portrait' }
    };

    // تصدير الجدول
    html2pdf().set(opt).from(table).save();
};
}

/**
* فحص وجود تنبيهات نظام
*/
function checkSystemAlerts() {
// تحقق من المخزون المنخفض
checkLowInventory();

// تحقق من المواعيد القادمة
checkUpcomingAppointments();

// تحقق من تحديثات النظام
checkSystemUpdates();
}

/**
* التحقق من المخزون المنخفض
*/
function checkLowInventory() {
// جلب بيانات المخزون من API
AjaxHandler.request({
    url: 'inventory.php',
    method: 'GET',
    data: {
        action: 'check_low_stock'
    },
    success: function(response) {
        if (response.status === 'success' && response.data && response.data.length > 0) {
            // عرض إشعار بالمنتجات منخفضة المخزون
            showNotification('warning', 'يوجد ' + response.data.length + ' منتج بكمية منخفضة في المخزون');

            // إضافة قائمة بالمنتجات إلى قائمة الإشعارات
            var lowStockItems = '';
            response.data.forEach(function(item) {
                lowStockItems += '<li><a href="' + BASE_URL + 'pages/inventory/index.php">' +
                                   item.name + ' - الكمية المتبقية: ' + item.quantity + '</a></li>';
            });

            $("#notification-dropdown .low-stock-items").html(lowStockItems);
            $(".notification-count").text(response.data.length).show();
        }
    }
});
}

/**
* التحقق من المواعيد القادمة
*/
function checkUpcomingAppointments() {
// جلب بيانات المواعيد القادمة من API
AjaxHandler.request({
    url: 'appointments.php',
    method: 'GET',
    data: {
        action: 'list',
        upcoming: true,
        limit: 5
    },
    success: function(response) {
        if (response.status === 'success' && response.appointments && response.appointments.length > 0) {
            // إضافة قائمة بالمواعيد إلى قائمة الإشعارات
            var appointmentItems = '';
            response.appointments.forEach(function(appointment) {
                appointmentItems += '<li><a href="' + BASE_URL + 'pages/appointments/index.php">' +
                                      appointment.customer_name + ' - ' + formatDate(appointment.date) +
                                      ' ' + appointment.start_time + '</a></li>';
            });

            $("#notification-dropdown .upcoming-appointments").html(appointmentItems);
        }
    }
});
}

/**
* التحقق من تحديثات النظام
*/
function checkSystemUpdates() {
// جلب بيانات التحديثات من API
AjaxHandler.request({
    url: 'settings.php',
    method: 'GET',
    data: {
        action: 'check_updates'
    },
    success: function(response) {
        if (response.status === 'success' && response.has_updates) {
            showNotification('info', 'يوجد تحديث جديد للنظام: ' + response.latest_version);
        }
    }
});
}

/**
* عرض مؤشر التحميل
*/
function showLoading() {
if (!document.getElementById('global-loader')) {
    var loader = document.createElement('div');
    loader.id = 'global-loader';
    loader.innerHTML = '<div class="spinner"><div class="bounce1"></div><div class="bounce2"></div><div class="bounce3"></div></div>';
    document.body.appendChild(loader);
}

$('#global-loader').fadeIn(200);
}

/**
* إخفاء مؤشر التحميل
*/
function hideLoading() {
$('#global-loader').fadeOut(200);
}

/**
* عرض تنبيه للمستخدم
* @param {string} type نوع التنبيه (success, error, warning, info)
* @param {string} message رسالة التنبيه
* @param {object} options خيارات إضافية للتنبيه
*/
function showNotification(type, message, options) {
options = options || {};

if (typeof toastr !== 'undefined') {
    // استخدام Toastr إذا كان متاحًا
    switch (type) {
        case 'success':
            toastr.success(message, options.title || '');
            break;
        case 'error':
            toastr.error(message, options.title || '');
            break;
        case 'warning':
            toastr.warning(message, options.title || '');
            break;
        case 'info':
            toastr.info(message, options.title || '');
            break;
        default:
            toastr.info(message, options.title || '');
    }
} else if (typeof swal !== 'undefined') {
    // استخدام SweetAlert إذا كان متاحًا
    swal({
        title: options.title || '',
        text: message,
        icon: type,
        button: "موافق"
    });
} else {
    // استخدام تنبيه متصفح عادي
    alert(message);
}
}

/**
* تهيئة المخططات البيانية
*/
function initializeCharts() {
// تهيئة مخطط المبيعات
initializeSalesChart();

// تهيئة مخطط الخدمات
initializeServicesChart();

// تهيئة مخطط المصروفات
initializeExpensesChart();
}

/**
* تهيئة مخطط المبيعات
*/
function initializeSalesChart() {
var salesChartCanvas = document.getElementById('sales-chart');

if (salesChartCanvas) {
    // جلب بيانات المبيعات
    AjaxHandler.request({
        url: 'reports.php',
        method: 'GET',
        data: {
            type: 'sales',
            period: 'month'
        },
        success: function(response) {
            if (response.success && response.data) {
                // إنشاء المخطط
                var ctx = salesChartCanvas.getContext('2d');
                window.salesChart = new Chart(ctx, {
                    type: 'line',
                    data: {
                        labels: response.data.sales_chart_data.map(function(item) {
                            return item.label;
                        }),
                        datasets: [{
                            label: 'المبيعات',
                            data: response.data.sales_chart_data.map(function(item) {
                                return item.value;
                            }),
                            backgroundColor: 'rgba(54, 162, 235, 0.2)',
                            borderColor: 'rgba(54, 162, 235, 1)',
                            borderWidth: 1
                        }]
                    },
                    options: {
                        scales: {
                            y: {
                                beginAtZero: true
                            }
                        }
                    }
                });
            }
        }
    });
}
}

/**
* تحديث مخطط المبيعات
* @param {array} data بيانات المبيعات
*/
function updateSalesChart(data) {
if (window.salesChart && data) {
    window.salesChart.data.labels = data.map(function(item) {
        return item.label;
    });

    window.salesChart.data.datasets[0].data = data.map(function(item) {
        return item.value;
    });

    window.salesChart.update();
}
}

/**
* تهيئة مخطط الخدمات
*/
function initializeServicesChart() {
var servicesChartCanvas = document.getElementById('services-chart');

if (servicesChartCanvas) {
    // جلب بيانات الخدمات
    AjaxHandler.request({
        url: 'reports.php',
        method: 'GET',
        data: {
            type: 'services'
        },
        success: function(response) {
            if (response.success && response.data && response.data.top_services) {
                // إنشاء المخطط
                var ctx = servicesChartCanvas.getContext('2d');
                window.servicesChart = new Chart(ctx, {
                    type: 'pie',
                    data: {
                        labels: response.data.top_services.map(function(item) {
                            return item.name;
                        }),
                        datasets: [{
                            data: response.data.top_services.map(function(item) {
                                return item.count;
                            }),
                            backgroundColor: [
                                'rgba(255, 99, 132, 0.7)',
                                'rgba(54, 162, 235, 0.7)',
                                'rgba(255, 206, 86, 0.7)',
                                'rgba(75, 192, 192, 0.7)',
                                'rgba(153, 102, 255, 0.7)'
                            ]
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false
                    }
                });
            }
        }
    });
}
}

/**
* تحديث مخطط الخدمات
* @param {array} data بيانات الخدمات
*/
function updateServicesChart(data) {
if (window.servicesChart && data) {
    window.servicesChart.data.labels = data.map(function(item) {
        return item.name;
    });

    window.servicesChart.data.datasets[0].data = data.map(function(item) {
        return item.count;
    });

    window.servicesChart.update();
}
}

/**
* تهيئة مخطط المصروفات
*/
function initializeExpensesChart() {
var expensesChartCanvas = document.getElementById('expenses-chart');

if (expensesChartCanvas) {
    // جلب بيانات المصروفات
    AjaxHandler.request({
        url: 'reports.php',
        method: 'GET',
        data: {
            type: 'expenses'
        },
        success: function(response) {
            if (response.success && response.data && response.data.categories) {
                // إنشاء المخطط
                var ctx = expensesChartCanvas.getContext('2d');
                new Chart(ctx, {
                    type: 'doughnut',
                    data: {
                        labels: response.data.categories.map(function(item) {
                            return item.name;
                        }),
                        datasets: [{
                            data: response.data.categories.map(function(item) {
                                return item.amount;
                            }),
                            backgroundColor: [
                                'rgba(255, 99, 132, 0.7)',
                                'rgba(54, 162, 235, 0.7)',
                                'rgba(255, 206, 86, 0.7)',
                                'rgba(75, 192, 192, 0.7)',
                                'rgba(153, 102, 255, 0.7)'
                            ]
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false
                    }
                });
            }
        }
    });
}
}

/**
* تهيئة التقويم وعرض المواعيد
*/
function initializeAppointmentsCalendar() {
var calendarEl = document.getElementById('appointments-calendar');

if (calendarEl) {
    // جلب بيانات المواعيد
    AjaxHandler.request({
        url: 'appointments.php',
        method: 'GET',
        data: {
            action: 'list'
        },
        success: function(response) {
            if (response.status === 'success' && response.appointments) {
                // تحويل بيانات المواعيد إلى تنسيق التقويم
                var events = response.appointments.map(function(appointment) {
                    // تحديد اللون حسب حالة الموعد
                    var color;
                    switch (appointment.status) {
                        case 'completed':
                            color = '#28a745'; // أخضر
                            break;
                        case 'cancelled':
                            color = '#dc3545'; // أحمر
                            break;
                        case 'waiting':
                            color = '#ffc107'; // أصفر
                                break;
                            default:
                                color = '#007bff'; // أزرق
                        }

                        return {
                            id: appointment.id,
                            title: appointment.customer_name + (appointment.service_name ? ' - ' + appointment.service_name : ''),
                            start: appointment.date + 'T' + appointment.start_time,
                            end: appointment.date + 'T' + appointment.end_time,
                            backgroundColor: color,
                            borderColor: color,
                            extendedProps: {
                                employee: appointment.employee_name,
                                service: appointment.service_name,
                                status: appointment.status,
                                notes: appointment.notes
                            }
                        };
                    });

                    // إنشاء التقويم
                    var calendar = new FullCalendar.Calendar(calendarEl, {
                        locale: 'ar',
                        initialView: 'dayGridMonth',
                        headerToolbar: {
                            left: 'prev,next today',
                            center: 'title',
                            right: 'dayGridMonth,timeGridWeek,timeGridDay,listWeek'
                        },
                        themeSystem: 'bootstrap',
                        buttonText: {
                            today: 'اليوم',
                            month: 'شهر',
                            week: 'أسبوع',
                            day: 'يوم',
                            list: 'قائمة'
                        },
                        direction: 'rtl',
                        firstDay: parseInt(localStorage.getItem('calendar_first_day') || '0'),
                        events: events,
                        eventClick: function(info) {
                            // عرض تفاصيل الموعد
                            showAppointmentDetails(info.event);
                        },
                        dateClick: function(info) {
                            // إضافة موعد جديد
                            showAddAppointmentForm(info.dateStr);
                        },
                        eventTimeFormat: {
                            hour: '2-digit',
                            minute: '2-digit',
                            meridiem: false,
                            hour12: false
                        }
                    });

                    calendar.render();
                }
            }
        });
    }
}

/**
 * عرض تفاصيل الموعد
 * @param {Object} event حدث التقويم
 */
function showAppointmentDetails(event) {
    // جلب بيانات الموعد من API للحصول على التفاصيل الكاملة
    AjaxHandler.request({
        url: 'appointments.php',
        method: 'GET',
        data: {
            action: 'view',
            id: event.id
        },
        success: function(response) {
            if (response.status === 'success' && response.appointment) {
                var appointment = response.appointment;
                var statusClass = '';

                // تحديد لون حالة الموعد
                switch (appointment.status) {
                    case 'completed':
                        statusClass = 'badge-success';
                        break;
                    case 'cancelled':
                        statusClass = 'badge-danger';
                        break;
                    case 'waiting':
                        statusClass = 'badge-warning';
                        break;
                    default:
                        statusClass = 'badge-primary';
                }

                // إعداد محتوى النافذة المنبثقة
                var modalContent = `
                    <div class="modal-header">
                        <h5 class="modal-title">تفاصيل الموعد</h5>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <div class="modal-body">
                        <div class="appointment-details">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>العميل:</label>
                                        <p>${appointment.customer_name || '-'}</p>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>الموظف:</label>
                                        <p>${appointment.employee_name || '-'}</p>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>الخدمة:</label>
                                        <p>${appointment.service_name || '-'}</p>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>الحالة:</label>
                                        <p><span class="badge ${statusClass}">${getStatusText(appointment.status)}</span></p>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>التاريخ:</label>
                                        <p>${formatDate(appointment.date)}</p>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>الوقت:</label>
                                        <p>${appointment.start_time} - ${appointment.end_time}</p>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <label>ملاحظات:</label>
                                <p>${appointment.notes || '-'}</p>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        ${appointment.status !== 'completed' && appointment.status !== 'cancelled' ?
                          `<button type="button" class="btn btn-success complete-appointment" data-id="${appointment.id}">إكمال الموعد</button>
                           <button type="button" class="btn btn-danger cancel-appointment" data-id="${appointment.id}">إلغاء الموعد</button>` : ''}
                        <button type="button" class="btn btn-primary edit-appointment" data-id="${appointment.id}">تعديل</button>
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">إغلاق</button>
                    </div>
                `;

                // عرض النافذة المنبثقة
                $("#appointment-modal .modal-content").html(modalContent);
                $("#appointment-modal").modal('show');

                // تعريف أحداث الأزرار
                $("#appointment-modal .complete-appointment").on('click', function() {
                    updateAppointmentStatus($(this).data('id'), 'completed');
                });

                $("#appointment-modal .cancel-appointment").on('click', function() {
                    updateAppointmentStatus($(this).data('id'), 'cancelled');
                });

                $("#appointment-modal .edit-appointment").on('click', function() {
                    window.location.href = BASE_URL + 'pages/appointments/edit.php?id=' + $(this).data('id');
                });
            }
        }
    });
}

/**
 * عرض نموذج إضافة موعد جديد
 * @param {string} dateStr التاريخ المختار
 */
function showAddAppointmentForm(dateStr) {
    // استخراج التاريخ من النص
    var date = new Date(dateStr);
    var formattedDate = date.toISOString().split('T')[0];

    // الانتقال إلى صفحة إضافة موعد جديد مع تحديد التاريخ
    window.location.href = BASE_URL + 'pages/appointments/add.php?date=' + formattedDate;
}

/**
 * تحديث حالة الموعد
 * @param {number} appointmentId معرف الموعد
 * @param {string} status الحالة الجديدة
 */
function updateAppointmentStatus(appointmentId, status) {
    AjaxHandler.request({
        url: 'appointments.php',
        method: 'POST',
        data: {
            action: 'update',
            id: appointmentId,
            status: status
        },
        success: function(response) {
            if (response.status === 'success') {
                showNotification('success', 'تم تحديث حالة الموعد بنجاح');
                $("#appointment-modal").modal('hide');

                // إعادة تحميل الصفحة لتحديث التقويم
                setTimeout(function() {
                    window.location.reload();
                }, 1000);
            } else {
                showNotification('error', response.message || 'حدث خطأ أثناء تحديث حالة الموعد');
            }
        }
    });
}

/**
 * الحصول على النص المقابل لحالة الموعد
 * @param {string} status رمز الحالة
 * @return {string} نص الحالة
 */
function getStatusText(status) {
    switch (status) {
        case 'booked':
            return 'محجوز';
        case 'waiting':
            return 'في الانتظار';
        case 'completed':
            return 'مكتمل';
        case 'cancelled':
            return 'ملغي';
        default:
            return status;
    }
}

/**
 * إنشاء تهيئة المخططات البيانية لتقارير المبيعات
 */
function initializeSalesReportCharts() {
    var ctx = document.getElementById('sales-by-day-chart');

    if (ctx) {
        // جلب بيانات المبيعات
        var startDate = $("#report-start-date").val();
        var endDate = $("#report-end-date").val();
        var branchId = $("#branch-filter").val();

        AjaxHandler.request({
            url: 'reports.php',
            method: 'GET',
            data: {
                type: 'sales',
                start_date: startDate,
                end_date: endDate,
                branch_id: branchId
            },
            success: function(response) {
                if (response.success && response.data) {
                    // بيانات المبيعات اليومية
                    var salesByDay = response.data.sales_by_day;

                    // إنشاء المخطط البياني
                    new Chart(ctx, {
                        type: 'bar',
                        data: {
                            labels: salesByDay.map(function(item) { return item.date; }),
                            datasets: [
                                {
                                    label: 'المبيعات',
                                    data: salesByDay.map(function(item) { return item.total; }),
                                    backgroundColor: 'rgba(54, 162, 235, 0.5)',
                                    borderColor: 'rgba(54, 162, 235, 1)',
                                    borderWidth: 1
                                },
                                {
                                    label: 'عدد الفواتير',
                                    data: salesByDay.map(function(item) { return item.count; }),
                                    backgroundColor: 'rgba(255, 99, 132, 0.5)',
                                    borderColor: 'rgba(255, 99, 132, 1)',
                                    borderWidth: 1,
                                    yAxisID: 'y1'
                                }
                            ]
                        },
                        options: {
                            responsive: true,
                            scales: {
                                y: {
                                    beginAtZero: true,
                                    title: {
                                        display: true,
                                        text: 'المبيعات (ريال)'
                                    }
                                },
                                y1: {
                                    beginAtZero: true,
                                    position: 'right',
                                    grid: {
                                        drawOnChartArea: false
                                    },
                                    title: {
                                        display: true,
                                        text: 'عدد الفواتير'
                                    }
                                }
                            }
                        }
                    });

                    // إنشاء مخطط توزيع المبيعات حسب طريقة الدفع
                    var paymentMethodsData = response.data.payment_methods;
                    var paymentMethodsCtx = document.getElementById('sales-by-payment-method-chart');

                    if (paymentMethodsCtx && paymentMethodsData) {
                        new Chart(paymentMethodsCtx, {
                            type: 'pie',
                            data: {
                                labels: Object.keys(paymentMethodsData).map(function(key) {
                                    return getPaymentMethodText(key);
                                }),
                                datasets: [{
                                    data: Object.values(paymentMethodsData),
                                    backgroundColor: [
                                        'rgba(54, 162, 235, 0.7)',
                                        'rgba(255, 99, 132, 0.7)',
                                        'rgba(255, 206, 86, 0.7)'
                                    ]
                                }]
                            },
                            options: {
                                responsive: true
                            }
                        });
                    }

                    // إنشاء مخطط أفضل الخدمات مبيعًا
                    var topServicesData = response.data.top_services;
                    var topServicesCtx = document.getElementById('top-services-chart');

                    if (topServicesCtx && topServicesData) {
                        new Chart(topServicesCtx, {
                            type: 'horizontalBar',
                            data: {
                                labels: topServicesData.map(function(service) { return service.name; }),
                                datasets: [{
                                    label: 'المبيعات',
                                    data: topServicesData.map(function(service) { return service.total; }),
                                    backgroundColor: 'rgba(75, 192, 192, 0.7)',
                                    borderColor: 'rgba(75, 192, 192, 1)',
                                    borderWidth: 1
                                }]
                            },
                            options: {
                                responsive: true,
                                scales: {
                                    x: {
                                        beginAtZero: true
                                    }
                                }
                            }
                        });
                    }
                }
            }
        });
    }
}

/**
 * الحصول على النص المقابل لطريقة الدفع
 * @param {string} method رمز طريقة الدفع
 * @return {string} نص طريقة الدفع
 */
function getPaymentMethodText(method) {
    switch (method) {
        case 'cash':
            return 'نقدًا';
        case 'card':
            return 'بطاقة ائتمان';
        case 'other':
            return 'طرق أخرى';
        default:
            return method;
    }
}

/**
 * إنشاء تهيئة المخططات البيانية لتقارير المصروفات
 */
function initializeExpensesReportCharts() {
    var ctx = document.getElementById('expenses-by-category-chart');

    if (ctx) {
        // جلب بيانات المصروفات
        var startDate = $("#report-start-date").val();
        var endDate = $("#report-end-date").val();
        var branchId = $("#branch-filter").val();

        AjaxHandler.request({
            url: 'reports.php',
            method: 'GET',
            data: {
                type: 'expenses',
                start_date: startDate,
                end_date: endDate,
                branch_id: branchId
            },
            success: function(response) {
                if (response.success && response.data) {
                    // بيانات المصروفات حسب الفئة
                    var expensesByCategory = response.data.expenses_by_category;

                    // إنشاء المخطط البياني
                    new Chart(ctx, {
                        type: 'pie',
                        data: {
                            labels: expensesByCategory.map(function(item) { return item.name; }),
                            datasets: [{
                                data: expensesByCategory.map(function(item) { return item.total; }),
                                backgroundColor: [
                                    'rgba(255, 99, 132, 0.7)',
                                    'rgba(54, 162, 235, 0.7)',
                                    'rgba(255, 206, 86, 0.7)',
                                    'rgba(75, 192, 192, 0.7)',
                                    'rgba(153, 102, 255, 0.7)',
                                    'rgba(255, 159, 64, 0.7)'
                                ]
                            }]
                        },
                        options: {
                            responsive: true
                        }
                    });

                    // إنشاء مخطط المصروفات اليومية
                    var expensesByDay = response.data.expenses_by_day;
                    var expensesByDayCtx = document.getElementById('expenses-by-day-chart');

                    if (expensesByDayCtx && expensesByDay) {
                        new Chart(expensesByDayCtx, {
                            type: 'bar',
                            data: {
                                labels: expensesByDay.map(function(item) { return item.date; }),
                                datasets: [{
                                    label: 'المصروفات',
                                    data: expensesByDay.map(function(item) { return item.total; }),
                                    backgroundColor: 'rgba(255, 99, 132, 0.5)',
                                    borderColor: 'rgba(255, 99, 132, 1)',
                                    borderWidth: 1
                                }]
                            },
                            options: {
                                responsive: true,
                                scales: {
                                    y: {
                                        beginAtZero: true
                                    }
                                }
                            }
                        });
                    }

                    // إنشاء مخطط مقارنة المبيعات والمصروفات
                    var salesVsExpenses = response.data.sales_vs_expenses;
                    var salesVsExpensesCtx = document.getElementById('sales-vs-expenses-chart');

                    if (salesVsExpensesCtx && salesVsExpenses) {
                        new Chart(salesVsExpensesCtx, {
                            type: 'bar',
                            data: {
                                labels: salesVsExpenses.map(function(item) { return item.date; }),
                                datasets: [
                                    {
                                        label: 'المبيعات',
                                        data: salesVsExpenses.map(function(item) { return item.sales; }),
                                        backgroundColor: 'rgba(54, 162, 235, 0.5)',
                                        borderColor: 'rgba(54, 162, 235, 1)',
                                        borderWidth: 1
                                    },
                                    {
                                        label: 'المصروفات',
                                        data: salesVsExpenses.map(function(item) { return item.expenses; }),
                                        backgroundColor: 'rgba(255, 99, 132, 0.5)',
                                        borderColor: 'rgba(255, 99, 132, 1)',
                                        borderWidth: 1
                                    }
                                ]
                            },
                            options: {
                                responsive: true,
                                scales: {
                                    y: {
                                        beginAtZero: true
                                    }
                                }
                            }
                        });
                    }
                }
            }
        });
    }
}

/**
 * إنشاء تهيئة المخططات البيانية لتقارير الموظفين
 */
function initializeEmployeesReportCharts() {
    var ctx = document.getElementById('employee-performance-chart');

    if (ctx) {
        // جلب بيانات أداء الموظفين
        var startDate = $("#report-start-date").val();
        var endDate = $("#report-end-date").val();
        var branchId = $("#branch-filter").val();

        AjaxHandler.request({
            url: 'reports.php',
            method: 'GET',
            data: {
                type: 'employees',
                start_date: startDate,
                end_date: endDate,
                branch_id: branchId
            },
            success: function(response) {
                if (response.success && response.data) {
                    // بيانات أداء الموظفين
                    var employeePerformance = response.data.employee_performance;

                    // إنشاء المخطط البياني
                    new Chart(ctx, {
                        type: 'bar',
                        data: {
                            labels: employeePerformance.map(function(item) { return item.name; }),
                            datasets: [
                                {
                                    label: 'المبيعات',
                                    data: employeePerformance.map(function(item) { return item.sales; }),
                                    backgroundColor: 'rgba(54, 162, 235, 0.5)',
                                    borderColor: 'rgba(54, 162, 235, 1)',
                                    borderWidth: 1
                                },
                                {
                                    label: 'عدد الخدمات',
                                    data: employeePerformance.map(function(item) { return item.services_count; }),
                                    backgroundColor: 'rgba(255, 99, 132, 0.5)',
                                    borderColor: 'rgba(255, 99, 132, 1)',
                                    borderWidth: 1,
                                    yAxisID: 'y1'
                                }
                            ]
                        },
                        options: {
                            responsive: true,
                            scales: {
                                y: {
                                    beginAtZero: true,
                                    title: {
                                        display: true,
                                        text: 'المبيعات (ريال)'
                                    }
                                },
                                y1: {
                                    beginAtZero: true,
                                    position: 'right',
                                    grid: {
                                        drawOnChartArea: false
                                    },
                                    title: {
                                        display: true,
                                        text: 'عدد الخدمات'
                                    }
                                }
                            }
                        }
                    });

                    // إنشاء مخطط الحضور
                    var attendanceData = response.data.attendance;
                    var attendanceCtx = document.getElementById('employee-attendance-chart');

                    if (attendanceCtx && attendanceData) {
                        new Chart(attendanceCtx, {
                            type: 'bar',
                            data: {
                                labels: attendanceData.map(function(item) { return item.name; }),
                                datasets: [{
                                    label: 'أيام الحضور',
                                    data: attendanceData.map(function(item) { return item.present_days; }),
                                    backgroundColor: 'rgba(75, 192, 192, 0.5)',
                                    borderColor: 'rgba(75, 192, 192, 1)',
                                    borderWidth: 1
                                },
                                {
                                    label: 'أيام الغياب',
                                    data: attendanceData.map(function(item) { return item.absent_days; }),
                                    backgroundColor: 'rgba(255, 99, 132, 0.5)',
                                    borderColor: 'rgba(255, 99, 132, 1)',
                                    borderWidth: 1
                                }]
                            },
                            options: {
                                responsive: true,
                                scales: {
                                    x: {
                                        stacked: true
                                    },
                                    y: {
                                        stacked: true,
                                        beginAtZero: true
                                    }
                                }
                            }
                        });
                    }
                }
            }
        });
    }
}

// تنفيذ الدالة الأساسية بعد اكتمال تحميل المستند
$(document).ready(function() {
    initializeCommonComponents();
    handlePageSpecificFunctions();
    setSystemPreferences();
    initializeNotificationSystem();
    initializeHelperFunctions();
    initializeNavigation();
    checkSystemAlerts();
});