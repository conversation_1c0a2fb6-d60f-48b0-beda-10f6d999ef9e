<?php
/**
 * الوظائف المساعدة لواجهة برمجة التطبيقات للمواعيد
 */

// منع الوصول المباشر للملف
if (!defined('BASEPATH')) {
    exit('لا يمكن الوصول المباشر لهذا الملف');
}

/**
 * تحويل الأرقام العربية إلى أرقام إنجليزية
 *
 * @param string $str النص الذي يحتوي على أرقام عربية
 * @return string النص بعد تحويل الأرقام العربية إلى إنجليزية
 */
function convertArabicToEnglishNumbers($str) {
    if (!$str) return $str;

    $arabicNumbers = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٣', '٨', '٩'];
    $englishNumbers = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'];

    return str_replace($arabicNumbers, $englishNumbers, $str);
}

/**
 * استرجاع إحصائيات المواعيد
 *
 * @param Database $db كائن قاعدة البيانات
 * @param int $branchId معرف الفرع (اختياري)
 * @return array الإحصائيات
 */
function getAppointmentStats($db, $branchId = null) {
    // التاريخ الحالي
    $today = date('Y-m-d');
    $tomorrow = date('Y-m-d', strtotime('+1 day'));
    $weekStart = date('Y-m-d');
    $weekEnd = date('Y-m-d', strtotime('+7 days'));
    $monthStart = date('Y-m-01');
    $monthEnd = date('Y-m-t');

    // إعداد الاستعلام الأساسي
    $baseQuery = "SELECT COUNT(*) FROM appointments WHERE status != 'cancelled'";
    $bindings = [];

    // إضافة شرط الفرع إذا تم تحديده
    if ($branchId !== null) {
        $baseQuery .= " AND branch_id = :branch_id";
        $bindings[':branch_id'] = $branchId;
    }

    // استعلام مواعيد اليوم
    $todayQuery = $baseQuery . " AND date = :today";
    $bindings[':today'] = $today;

    $db->prepare($todayQuery);
    foreach ($bindings as $param => $value) {
        $db->bind($param, $value);
    }
    $todayCount = $db->fetchColumn();

    // استعلام مواعيد الغد
    $tomorrowQuery = $baseQuery . " AND date = :tomorrow";
    $bindings[':tomorrow'] = $tomorrow;

    $db->prepare($tomorrowQuery);
    foreach ($bindings as $param => $value) {
        $db->bind($param, $value);
    }
    $tomorrowCount = $db->fetchColumn();

    // استعلام مواعيد الأسبوع
    $weekQuery = $baseQuery . " AND date BETWEEN :week_start AND :week_end";
    $bindings[':week_start'] = $weekStart;
    $bindings[':week_end'] = $weekEnd;

    $db->prepare($weekQuery);
    foreach ($bindings as $param => $value) {
        $db->bind($param, $value);
    }
    $weekCount = $db->fetchColumn();

    // استعلام مواعيد الشهر
    $monthQuery = $baseQuery . " AND date BETWEEN :month_start AND :month_end";
    $bindings[':month_start'] = $monthStart;
    $bindings[':month_end'] = $monthEnd;

    $db->prepare($monthQuery);
    foreach ($bindings as $param => $value) {
        $db->bind($param, $value);
    }
    $monthCount = $db->fetchColumn();

    // إرجاع الإحصائيات
    return [
        'today' => $todayCount,
        'tomorrow' => $tomorrowCount,
        'week' => $weekCount,
        'month' => $monthCount
    ];
}

/**
 * التحقق من توفر وقت محدد لموعد
 *
 * @param Database $db كائن قاعدة البيانات
 * @param array $data بيانات الموعد
 * @return bool متوفر أم لا
 */
function checkAppointmentAvailability($db, $data) {
    // استخراج البيانات
    $branchId = intval($data['branch_id']);
    $serviceId = intval($data['service_id']);
    $employeeId = intval($data['employee_id']);
    $date = $data['date'];
    $startTime = $data['start_time'];

    // استرجاع مدة الخدمة
    $db->prepare("SELECT duration FROM services WHERE id = :service_id");
    $db->bind(':service_id', $serviceId);
    $serviceDuration = $db->fetchColumn();

    if (!$serviceDuration) {
        $serviceDuration = 30; // المدة الافتراضية بالدقائق
    }

    // حساب وقت الانتهاء بناءً على وقت البدء ومدة الخدمة
    $endTime = date('H:i:s', strtotime($startTime) + ($serviceDuration * 60));

    // التحقق من تداخل المواعيد للموظف المحدد
    $db->prepare("SELECT COUNT(*) FROM appointments
                 WHERE employee_id = :employee_id
                 AND date = :date
                 AND status IN ('booked', 'waiting')
                 AND (
                     (start_time <= :start_time AND end_time > :start_time) OR
                     (start_time < :end_time AND end_time >= :end_time) OR
                     (start_time >= :start_time AND end_time <= :end_time)
                 )");

    $db->bind(':employee_id', $employeeId);
    $db->bind(':date', $date);
    $db->bind(':start_time', $startTime);
    $db->bind(':end_time', $endTime);

    $conflictCount = $db->fetchColumn();

    // التحقق من ساعات عمل الفرع (يمكن إضافة المزيد من التحقق هنا)

    // إرجاع النتيجة (متوفر إذا لم يكن هناك تعارض)
    return $conflictCount == 0;
}

/**
 * إنشاء موعد جديد
 *
 * @param Database $db كائن قاعدة البيانات
 * @param array $data بيانات الموعد
 * @return int|bool معرف الموعد الجديد أو false في حالة الفشل
 */
function createAppointment($db, $data) {
    // التحقق من توفر الوقت
    if (!checkAppointmentAvailability($db, $data)) {
        return false;
    }

    // استرجاع مدة الخدمة
    $db->prepare("SELECT duration FROM services WHERE id = :service_id");
    $db->bind(':service_id', $data['service_id']);
    $serviceDuration = $db->fetchColumn();

    if (!$serviceDuration) {
        $serviceDuration = 30; // المدة الافتراضية بالدقائق
    }

    // حساب وقت الانتهاء
    $endTime = date('H:i:s', strtotime($data['start_time']) + ($serviceDuration * 60));

    // إنشاء الموعد
    $currentDateTime = date('Y-m-d H:i:s'); // الحصول على التاريخ والوقت الحالي من PHP

    $db->prepare("INSERT INTO appointments (
                    customer_id,
                    employee_id,
                    service_id,
                    date,
                    start_time,
                    end_time,
                    status,
                    notes,
                    branch_id,
                    created_at
                ) VALUES (
                    :customer_id,
                    :employee_id,
                    :service_id,
                    :date,
                    :start_time,
                    :end_time,
                    :status,
                    :notes,
                    :branch_id,
                    :created_at
                )");

    $db->bind(':customer_id', $data['customer_id']);
    $db->bind(':employee_id', $data['employee_id']);
    $db->bind(':service_id', $data['service_id']);
    $db->bind(':date', $data['date']);
    $db->bind(':start_time', $data['start_time']);
    $db->bind(':end_time', $endTime);
    $db->bind(':status', 'booked');
    $db->bind(':notes', $data['notes'] ?? null);
    $db->bind(':branch_id', $data['branch_id']);
    $db->bind(':created_at', $currentDateTime);

    if ($db->execute()) {
        return $db->lastInsertId();
    }

    return false;
}

/**
 * تحديث بيانات موعد
 *
 * @param Database $db كائن قاعدة البيانات
 * @param int $appointmentId معرف الموعد
 * @param array $data بيانات الموعد الجديدة
 * @return bool نجاح العملية
 */
function updateAppointment($db, $appointmentId, $data) {
    // التحقق من توفر الوقت إذا تم تغيير الوقت أو التاريخ أو الموظف أو الخدمة
    $checkAvailability = false;

    // استرجاع بيانات الموعد الحالية
    $db->prepare("SELECT * FROM appointments WHERE id = :id");
    $db->bind(':id', $appointmentId);
    $currentAppointment = $db->fetch();

    if (!$currentAppointment) {
        return false;
    }

    // التحقق مما إذا كانت هناك تغييرات في البيانات المؤثرة على التوفر
    if (isset($data['date']) && $data['date'] != $currentAppointment['date']) {
        $checkAvailability = true;
    }

    if (isset($data['start_time']) && $data['start_time'] != $currentAppointment['start_time']) {
        $checkAvailability = true;
    }

    if (isset($data['employee_id']) && $data['employee_id'] != $currentAppointment['employee_id']) {
        $checkAvailability = true;
    }

    if (isset($data['service_id']) && $data['service_id'] != $currentAppointment['service_id']) {
        $checkAvailability = true;
    }

    // إذا كانت هناك تغييرات مؤثرة، نتحقق من التوفر
    if ($checkAvailability) {
        $availabilityData = [
            'branch_id' => $data['branch_id'] ?? $currentAppointment['branch_id'],
            'service_id' => $data['service_id'] ?? $currentAppointment['service_id'],
            'employee_id' => $data['employee_id'] ?? $currentAppointment['employee_id'],
            'date' => $data['date'] ?? $currentAppointment['date'],
            'start_time' => $data['start_time'] ?? $currentAppointment['start_time']
        ];

        if (!checkAppointmentAvailability($db, $availabilityData)) {
            return false;
        }

        // إذا تم تغيير الخدمة، نحسب وقت الانتهاء الجديد
        if (isset($data['service_id']) && $data['service_id'] != $currentAppointment['service_id']) {
            $db->prepare("SELECT duration FROM services WHERE id = :service_id");
            $db->bind(':service_id', $data['service_id']);
            $serviceDuration = $db->fetchColumn();

            if (!$serviceDuration) {
                $serviceDuration = 30; // المدة الافتراضية بالدقائق
            }

            $startTime = $data['start_time'] ?? $currentAppointment['start_time'];
            $data['end_time'] = date('H:i:s', strtotime($startTime) + ($serviceDuration * 60));
        } elseif (isset($data['start_time']) && $data['start_time'] != $currentAppointment['start_time']) {
            // إذا تم تغيير وقت البدء فقط، نحسب وقت الانتهاء الجديد
            $db->prepare("SELECT duration FROM services WHERE id = :service_id");
            $serviceId = $data['service_id'] ?? $currentAppointment['service_id'];
            $db->bind(':service_id', $serviceId);
            $serviceDuration = $db->fetchColumn();

            if (!$serviceDuration) {
                $serviceDuration = 30; // المدة الافتراضية بالدقائق
            }

            $data['end_time'] = date('H:i:s', strtotime($data['start_time']) + ($serviceDuration * 60));
        }
    }

    // بناء استعلام التحديث
    $updateFields = [];
    $bindings = [];

    if (isset($data['customer_id'])) {
        $updateFields[] = "customer_id = :customer_id";
        $bindings[':customer_id'] = $data['customer_id'];
    }

    if (isset($data['employee_id'])) {
        $updateFields[] = "employee_id = :employee_id";
        $bindings[':employee_id'] = $data['employee_id'];
    }

    if (isset($data['service_id'])) {
        $updateFields[] = "service_id = :service_id";
        $bindings[':service_id'] = $data['service_id'];
    }

    if (isset($data['date'])) {
        $updateFields[] = "date = :date";
        $bindings[':date'] = $data['date'];
    }

    if (isset($data['start_time'])) {
        $updateFields[] = "start_time = :start_time";
        $bindings[':start_time'] = $data['start_time'];
    }

    if (isset($data['end_time'])) {
        $updateFields[] = "end_time = :end_time";
        $bindings[':end_time'] = $data['end_time'];
    }

    if (isset($data['status'])) {
        $updateFields[] = "status = :status";
        $bindings[':status'] = $data['status'];
    }

    if (isset($data['notes'])) {
        $updateFields[] = "notes = :notes";
        $bindings[':notes'] = $data['notes'];
    }

    if (isset($data['branch_id'])) {
        $updateFields[] = "branch_id = :branch_id";
        $bindings[':branch_id'] = $data['branch_id'];
    }

    $updateFields[] = "updated_at = NOW()";

    // إذا لم تكن هناك حقول للتحديث
    if (empty($updateFields)) {
        return true;
    }

    // بناء وتنفيذ الاستعلام
    $sql = "UPDATE appointments SET " . implode(', ', $updateFields) . " WHERE id = :id";
    $bindings[':id'] = $appointmentId;

    $db->prepare($sql);

    foreach ($bindings as $param => $value) {
        $db->bind($param, $value);
    }

    return $db->execute();
}

/**
 * إلغاء موعد
 *
 * @param Database $db كائن قاعدة البيانات
 * @param int $appointmentId معرف الموعد
 * @return bool نجاح العملية
 */
function cancelAppointment($db, $appointmentId) {
    $db->prepare("UPDATE appointments SET status = 'cancelled', updated_at = NOW() WHERE id = :id");
    $db->bind(':id', $appointmentId);

    return $db->execute();
}

/**
 * استرجاع قائمة المواعيد
 *
 * @param Database $db كائن قاعدة البيانات
 * @param array $filters معايير الفلترة
 * @return array قائمة المواعيد
 */
function getAppointments($db, $filters = []) {
    $sql = "SELECT a.*,
                   c.name AS customer_name,
                   c.phone AS customer_phone,
                   s.name AS service_name,
                   s.duration,
                   e.name AS employee_name,
                   b.name AS branch_name
            FROM appointments a
            LEFT JOIN customers c ON a.customer_id = c.id
            LEFT JOIN services s ON a.service_id = s.id
            LEFT JOIN employees e ON a.employee_id = e.id
            LEFT JOIN branches b ON a.branch_id = b.id";

    $whereConditions = [];
    $bindings = [];

    // تطبيق الفلاتر
    if (!empty($filters['branch_id'])) {
        $whereConditions[] = "a.branch_id = :branch_id";
        $bindings[':branch_id'] = $filters['branch_id'];
    }

    if (!empty($filters['customer_id'])) {
        $whereConditions[] = "a.customer_id = :customer_id";
        $bindings[':customer_id'] = $filters['customer_id'];
    }

    if (!empty($filters['employee_id'])) {
        $whereConditions[] = "a.employee_id = :employee_id";
        $bindings[':employee_id'] = $filters['employee_id'];
    }

    if (!empty($filters['service_id'])) {
        $whereConditions[] = "a.service_id = :service_id";
        $bindings[':service_id'] = $filters['service_id'];
    }

    if (!empty($filters['status'])) {
        $whereConditions[] = "a.status = :status";
        $bindings[':status'] = $filters['status'];
    }

    if (!empty($filters['date'])) {
        $whereConditions[] = "a.date = :date";
        $bindings[':date'] = $filters['date'];
    }

    if (!empty($filters['start_date']) && !empty($filters['end_date'])) {
        $whereConditions[] = "a.date BETWEEN :start_date AND :end_date";
        $bindings[':start_date'] = $filters['start_date'];
        $bindings[':end_date'] = $filters['end_date'];
    } elseif (!empty($filters['start_date'])) {
        $whereConditions[] = "a.date >= :start_date";
        $bindings[':start_date'] = $filters['start_date'];
    } elseif (!empty($filters['end_date'])) {
        $whereConditions[] = "a.date <= :end_date";
        $bindings[':end_date'] = $filters['end_date'];
    }

    // إضافة شروط WHERE
    if (!empty($whereConditions)) {
        $sql .= " WHERE " . implode(' AND ', $whereConditions);
    }

    // الترتيب
    $sql .= " ORDER BY a.date ASC, a.start_time ASC";

    // الحد والإزاحة
    if (!empty($filters['limit'])) {
        $sql .= " LIMIT :limit";
        $bindings[':limit'] = (int)$filters['limit'];

        if (!empty($filters['offset'])) {
            $sql .= " OFFSET :offset";
            $bindings[':offset'] = (int)$filters['offset'];
        }
    }

    $db->prepare($sql);

    // ربط القيم
    foreach ($bindings as $param => $value) {
        $db->bind($param, $value);
    }

    return $db->fetchAll();
}

/**
 * استرجاع عدد المواعيد
 *
 * @param Database $db كائن قاعدة البيانات
 * @param array $filters معايير الفلترة
 * @return int عدد المواعيد
 */
function getAppointmentsCount($db, $filters = []) {
    $sql = "SELECT COUNT(*) FROM appointments a";

    $whereConditions = [];
    $bindings = [];

    // تطبيق الفلاتر
    if (!empty($filters['branch_id'])) {
        $whereConditions[] = "a.branch_id = :branch_id";
        $bindings[':branch_id'] = $filters['branch_id'];
    }

    if (!empty($filters['customer_id'])) {
        $whereConditions[] = "a.customer_id = :customer_id";
        $bindings[':customer_id'] = $filters['customer_id'];
    }

    if (!empty($filters['employee_id'])) {
        $whereConditions[] = "a.employee_id = :employee_id";
        $bindings[':employee_id'] = $filters['employee_id'];
    }

    if (!empty($filters['service_id'])) {
        $whereConditions[] = "a.service_id = :service_id";
        $bindings[':service_id'] = $filters['service_id'];
    }
    if (!empty($filters['status'])) {
        $whereConditions[] = "a.status = :status";
        $bindings[':status'] = $filters['status'];
    }

    if (!empty($filters['date'])) {
        $whereConditions[] = "a.date = :date";
        $bindings[':date'] = $filters['date'];
    }

    if (!empty($filters['start_date']) && !empty($filters['end_date'])) {
        $whereConditions[] = "a.date BETWEEN :start_date AND :end_date";
        $bindings[':start_date'] = $filters['start_date'];
        $bindings[':end_date'] = $filters['end_date'];
    } elseif (!empty($filters['start_date'])) {
        $whereConditions[] = "a.date >= :start_date";
        $bindings[':start_date'] = $filters['start_date'];
    } elseif (!empty($filters['end_date'])) {
        $whereConditions[] = "a.date <= :end_date";
        $bindings[':end_date'] = $filters['end_date'];
    }

    // إضافة شروط WHERE
    if (!empty($whereConditions)) {
        $sql .= " WHERE " . implode(' AND ', $whereConditions);
    }

    $db->prepare($sql);

    // ربط القيم
    foreach ($bindings as $param => $value) {
        $db->bind($param, $value);
    }

    return $db->fetchColumn();
}

/**
 * استرجاع بيانات موعد محدد
 *
 * @param Database $db كائن قاعدة البيانات
 * @param int $appointmentId معرف الموعد
 * @return array|false بيانات الموعد أو false إذا لم يتم العثور عليه
 */
function getAppointmentById($db, $appointmentId) {
    $db->prepare("SELECT a.*,
                       c.name AS customer_name,
                       c.phone AS customer_phone,
                       s.name AS service_name,
                       s.duration,
                       e.name AS employee_name,
                       b.name AS branch_name
                FROM appointments a
                LEFT JOIN customers c ON a.customer_id = c.id
                LEFT JOIN services s ON a.service_id = s.id
                LEFT JOIN employees e ON a.employee_id = e.id
                LEFT JOIN branches b ON a.branch_id = b.id
                WHERE a.id = :id");
    $db->bind(':id', $appointmentId);

    return $db->fetch();
}

/**
 * إنشاء تقرير المواعيد
 *
 * @param Database $db كائن قاعدة البيانات
 * @param array $filters معايير الفلترة
 * @return array بيانات التقرير
 */
function generateAppointmentsReport($db, $filters = []) {
    // استرجاع المواعيد حسب الفلاتر
    $appointments = getAppointments($db, $filters);
    $totalAppointments = getAppointmentsCount($db, $filters);

    // حساب إحصائيات الحالة
    $statusStats = [
        'booked' => 0,
        'waiting' => 0,
        'completed' => 0,
        'cancelled' => 0
    ];

    foreach ($appointments as $appointment) {
        if (isset($statusStats[$appointment['status']])) {
            $statusStats[$appointment['status']]++;
        }
    }

    // حساب إحصائيات الموظفين
    $employeeStats = [];
    foreach ($appointments as $appointment) {
        $employeeId = $appointment['employee_id'];
        $employeeName = $appointment['employee_name'];

        if (!isset($employeeStats[$employeeId])) {
            $employeeStats[$employeeId] = [
                'id' => $employeeId,
                'name' => $employeeName,
                'count' => 0
            ];
        }

        $employeeStats[$employeeId]['count']++;
    }

    // حساب إحصائيات الخدمات
    $serviceStats = [];
    foreach ($appointments as $appointment) {
        $serviceId = $appointment['service_id'];
        $serviceName = $appointment['service_name'];

        if (!isset($serviceStats[$serviceId])) {
            $serviceStats[$serviceId] = [
                'id' => $serviceId,
                'name' => $serviceName,
                'count' => 0
            ];
        }

        $serviceStats[$serviceId]['count']++;
    }

    // حساب إحصائيات الأيام
    $dayStats = [];
    foreach ($appointments as $appointment) {
        $day = $appointment['date'];

        if (!isset($dayStats[$day])) {
            $dayStats[$day] = [
                'date' => $day,
                'count' => 0
            ];
        }

        $dayStats[$day]['count']++;
    }

    // ترتيب الإحصائيات
    usort($employeeStats, function($a, $b) {
        return $b['count'] - $a['count'];
    });

    usort($serviceStats, function($a, $b) {
        return $b['count'] - $a['count'];
    });

    ksort($dayStats);

    // تجميع البيانات في تقرير
    $report = [
        'filters' => $filters,
        'total_appointments' => $totalAppointments,
        'status_stats' => $statusStats,
        'employee_stats' => array_values($employeeStats),
        'service_stats' => array_values($serviceStats),
        'day_stats' => array_values($dayStats),
        'appointments' => $appointments
    ];

    return $report;
}

/**
 * مقارنة المواعيد بين فترتين
 *
 * @param Database $db كائن قاعدة البيانات
 * @param array $period1 بيانات الفترة الأولى (start_date, end_date)
 * @param array $period2 بيانات الفترة الثانية (start_date, end_date)
 * @param int $branchId معرف الفرع (اختياري)
 * @return array بيانات المقارنة
 */
function compareAppointmentsPeriods($db, $period1, $period2, $branchId = null) {
    // استرجاع التقرير للفترة الأولى
    $filters1 = [
        'start_date' => $period1['start_date'],
        'end_date' => $period1['end_date'],
        'branch_id' => $branchId
    ];

    $report1 = generateAppointmentsReport($db, $filters1);

    // استرجاع التقرير للفترة الثانية
    $filters2 = [
        'start_date' => $period2['start_date'],
        'end_date' => $period2['end_date'],
        'branch_id' => $branchId
    ];

    $report2 = generateAppointmentsReport($db, $filters2);

    // مقارنة النتائج
    $comparison = [
        'period1' => [
            'start_date' => $period1['start_date'],
            'end_date' => $period1['end_date'],
            'total_appointments' => $report1['total_appointments'],
            'status_stats' => $report1['status_stats'],
            'employee_stats' => $report1['employee_stats'],
            'service_stats' => $report1['service_stats']
        ],
        'period2' => [
            'start_date' => $period2['start_date'],
            'end_date' => $period2['end_date'],
            'total_appointments' => $report2['total_appointments'],
            'status_stats' => $report2['status_stats'],
            'employee_stats' => $report2['employee_stats'],
            'service_stats' => $report2['service_stats']
        ],
        'changes' => [
            'total_appointments' => $report2['total_appointments'] - $report1['total_appointments'],
            'status_stats' => [
                'booked' => $report2['status_stats']['booked'] - $report1['status_stats']['booked'],
                'waiting' => $report2['status_stats']['waiting'] - $report1['status_stats']['waiting'],
                'completed' => $report2['status_stats']['completed'] - $report1['status_stats']['completed'],
                'cancelled' => $report2['status_stats']['cancelled'] - $report1['status_stats']['cancelled']
            ]
        ]
    ];

    // حساب النسبة المئوية للتغيير في إجمالي المواعيد
    if ($report1['total_appointments'] > 0) {
        $comparison['changes']['total_appointments_percentage'] = round(
            ($comparison['changes']['total_appointments'] / $report1['total_appointments']) * 100,
            2
        );
    } else {
        $comparison['changes']['total_appointments_percentage'] = $report2['total_appointments'] > 0 ? 100 : 0;
    }

    return $comparison;
}
