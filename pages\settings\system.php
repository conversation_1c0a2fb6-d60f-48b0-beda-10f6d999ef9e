<?php
/**
 * صفحة إعدادات النظام
 * تتيح للمدير إدارة إعدادات النظام المختلفة
 */

// تعريف ثابت لمنع الوصول المباشر للملفات المرتبطة
define('BASEPATH', true);

// استدعاء ملفات التهيئة
require_once '../../config/init.php';

// التحقق من تسجيل الدخول
requireLogin();

// التحقق من صلاحيات المستخدم (المدير فقط)
redirectIfNotAllowed([ROLE_ADMIN, ROLE_MANAGER]);

// عنوان الصفحة
$pageTitle = 'إعدادات النظام';

// إنشاء كائن الإعدادات
$settingsModel = new Settings($db);

// معالجة إرسال النموذج (حفظ الإعدادات)
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['update_settings'])) {
    try {
        // تسجيل بيانات النموذج للتصحيح
        error_log("POST data: " . print_r($_POST, true));

        // تجميع الإعدادات من النموذج
        $updatedSettings = [];
        $isAdmin = $_SESSION['user_role'] == ROLE_ADMIN;

        // تسجيل معلومات المستخدم للتصحيح
        error_log("User role: {$_SESSION['user_role']}, isAdmin: " . ($isAdmin ? 'true' : 'false'));

        // إعدادات الفاتورة
        $invoiceSettings = [];
        // معالجة خاصة للقيم المنطقية (الاختيارات)
        $invoiceSettings['invoice_include_tax'] = isset($_POST['invoice']['include_tax']) ? '1' : '0';
        $invoiceSettings['invoice_print_without_preview'] = isset($_POST['invoice']['print_without_preview']) ? '1' : '0';

        // معالجة القيم العادية
        if (isset($_POST['invoice'])) {
            foreach ($_POST['invoice'] as $key => $value) {
                // تجنب تكرار القيم المنطقية التي تمت معالجتها بالفعل
                if (!in_array($key, ['include_tax', 'print_without_preview'])) {
                    $invoiceSettings['invoice_' . $key] = sanitizeInput($value);
                }
            }
        }

        // تحديث إعدادات الفاتورة
        $result = $settingsModel->updateSettings($invoiceSettings, $isAdmin);
        error_log("Invoice settings update result: " . ($result ? 'success' : 'failed'));

        // إعدادات النظام
        $systemSettings = [];
        // معالجة خاصة للقيم المنطقية (الاختيارات)
        $systemSettings['system_auto_backup'] = isset($_POST['system']['auto_backup']) ? '1' : '0';
        $systemSettings['enable_background_tasks'] = isset($_POST['system']['enable_background_tasks']) ? '1' : '0';

        // معالجة القيم العادية
        if (isset($_POST['system'])) {
            foreach ($_POST['system'] as $key => $value) {
                // تجنب تكرار القيم المنطقية التي تمت معالجتها بالفعل
                if (!in_array($key, ['auto_backup', 'enable_background_tasks'])) {
                    $systemSettings['system_' . $key] = sanitizeInput($value);
                }
            }
        }

        // تحديث إعدادات النظام
        $result = $settingsModel->updateSettings($systemSettings, $isAdmin);
        error_log("System settings update result: " . ($result ? 'success' : 'failed'));

        // إعدادات المواعيد
        $appointmentSettings = [];
        // معالجة خاصة للقيم المنطقية (الاختيارات)
        $appointmentSettings['appointment_allow_online_booking'] = isset($_POST['appointment']['allow_online_booking']) ? '1' : '0';

        // معالجة القيم العادية
        if (isset($_POST['appointment'])) {
            foreach ($_POST['appointment'] as $key => $value) {
                // تجنب تكرار القيم المنطقية التي تمت معالجتها بالفعل
                if (!in_array($key, ['allow_online_booking'])) {
                    $appointmentSettings['appointment_' . $key] = sanitizeInput($value);
                }
            }
        }

        // تحديث إعدادات المواعيد
        $result = $settingsModel->updateSettings($appointmentSettings, $isAdmin);
        error_log("Appointment settings update result: " . ($result ? 'success' : 'failed'));

        // إعدادات الرواتب
        $salarySettings = [];
        // معالجة خاصة للقيم المنطقية (الاختيارات)
        $salarySettings['salary_payment_reminder'] = isset($_POST['salary']['payment_reminder']) ? '1' : '0';

        // معالجة القيم العادية
        if (isset($_POST['salary'])) {
            foreach ($_POST['salary'] as $key => $value) {
                // تجنب تكرار القيم المنطقية التي تمت معالجتها بالفعل
                if (!in_array($key, ['payment_reminder'])) {
                    $salarySettings['salary_' . $key] = sanitizeInput($value);
                }
            }
        }

        // تحديث إعدادات الرواتب
        $result = $settingsModel->updateSettings($salarySettings, $isAdmin);
        error_log("Salary settings update result: " . ($result ? 'success' : 'failed'));

        // إعدادات الإشعارات
        $notificationSettings = [];
        // معالجة خاصة للقيم المنطقية (الاختيارات)
        $notificationSettings['notification_enable_email'] = isset($_POST['notification']['enable_email']) ? '1' : '0';
        $notificationSettings['notification_enable_sms'] = isset($_POST['notification']['enable_sms']) ? '1' : '0';
        $notificationSettings['notification_enable_whatsapp'] = isset($_POST['notification']['enable_whatsapp']) ? '1' : '0';
        $notificationSettings['notification_appointment_reminder'] = isset($_POST['notification']['appointment_reminder']) ? '1' : '0';
        $notificationSettings['notification_admin_notifications'] = isset($_POST['notification']['admin_notifications']) ? '1' : '0';
        $notificationSettings['notification_invoice_notification'] = isset($_POST['notification']['invoice_notification']) ? '1' : '0';
        $notificationSettings['notification_notify_workday_open'] = isset($_POST['notification']['notify_workday_open']) ? '1' : '0';
        $notificationSettings['notification_notify_workday_close'] = isset($_POST['notification']['notify_workday_close']) ? '1' : '0';
        $notificationSettings['notification_notify_daily_report'] = isset($_POST['notification']['notify_daily_report']) ? '1' : '0';
        $notificationSettings['notification_notify_new_invoice'] = isset($_POST['notification']['notify_new_invoice']) ? '1' : '0';
        $notificationSettings['whatsapp_client_side'] = isset($_POST['notification']['whatsapp_client_side']) ? '1' : '0';

        // معالجة القيم العادية
        if (isset($_POST['notification'])) {
            foreach ($_POST['notification'] as $key => $value) {
                // تجنب تكرار القيم المنطقية التي تمت معالجتها بالفعل
                if (!in_array($key, ['enable_email', 'enable_sms', 'enable_whatsapp', 'appointment_reminder', 'admin_notifications', 'invoice_notification', 'whatsapp_client_side', 'notify_workday_open', 'notify_workday_close', 'notify_daily_report', 'notify_new_invoice'])) {
                    $notificationSettings['notification_' . $key] = sanitizeInput($value);
                }
            }
        }

        // تحديث إعدادات الإشعارات
        $result = $settingsModel->updateSettings($notificationSettings, $isAdmin);
        error_log("Notification settings update result: " . ($result ? 'success' : 'failed'));
        error_log("Notification settings: " . print_r($notificationSettings, true));

        // رسالة نجاح
        $_SESSION['success_message'] = 'تم حفظ الإعدادات بنجاح';

        // إضافة تسجيل للتشخيص
        error_log('تم تحديث الإعدادات بنجاح');
    } catch (Exception $e) {
        // رسالة خطأ
        $_SESSION['error_message'] = 'حدث خطأ أثناء حفظ الإعدادات: ' . getErrorMessage($e);
        error_log('خطأ في حفظ الإعدادات: ' . $e->getMessage());
    }

    // إعادة توجيه لتجنب إعادة إرسال النموذج
    header('Location: ' . $_SERVER['PHP_SELF']);
    exit;
}

// استرجاع الإعدادات الحالية
// تعديل طريقة استرجاع الإعدادات
$invoiceSettings = [];
$systemSettings = [];
$appointmentSettings = [];
$salarySettings = [];
$notificationSettings = [];

// استرجاع جميع الإعدادات
$allSettings = $settingsModel->getAllSettings();

// فرز الإعدادات حسب النوع
foreach ($allSettings as $key => $value) {
    if (strpos($key, 'invoice_') === 0) {
        $shortKey = str_replace('invoice_', '', $key);
        $invoiceSettings[$shortKey] = $value;
    } elseif (strpos($key, 'system_') === 0) {
        $shortKey = str_replace('system_', '', $key);
        $systemSettings[$shortKey] = $value;
    } elseif (strpos($key, 'appointment_') === 0) {
        $shortKey = str_replace('appointment_', '', $key);
        $appointmentSettings[$shortKey] = $value;
    } elseif (strpos($key, 'salary_') === 0) {
        $shortKey = str_replace('salary_', '', $key);
        $salarySettings[$shortKey] = $value;
    } elseif (strpos($key, 'notification_') === 0) {
        $shortKey = str_replace('notification_', '', $key);
        $notificationSettings[$shortKey] = $value;
    }
}

// إضافة تسجيل للتشخيص
error_log('تم استرجاع الإعدادات: ' . json_encode([
    'invoice' => count($invoiceSettings),
    'system' => count($systemSettings),
    'appointment' => count($appointmentSettings),
    'salary' => count($salarySettings),
    'notification' => count($notificationSettings)
]));

// التأكد من وجود الإعدادات الافتراضية
if (empty($systemSettings) && empty($invoiceSettings)) {
    error_log('لا توجد إعدادات، جاري تهيئة الإعدادات الافتراضية...');
    $settingsModel->initDefaultSettings();

    // إعادة استرجاع الإعدادات بعد التهيئة
    $allSettings = $settingsModel->getAllSettings();

    // فرز الإعدادات مرة أخرى
    foreach ($allSettings as $key => $value) {
        if (strpos($key, 'invoice_') === 0) {
            $shortKey = str_replace('invoice_', '', $key);
            $invoiceSettings[$shortKey] = $value;
        } elseif (strpos($key, 'system_') === 0) {
            $shortKey = str_replace('system_', '', $key);
            $systemSettings[$shortKey] = $value;
        } elseif (strpos($key, 'appointment_') === 0) {
            $shortKey = str_replace('appointment_', '', $key);
            $appointmentSettings[$shortKey] = $value;
        } elseif (strpos($key, 'salary_') === 0) {
            $shortKey = str_replace('salary_', '', $key);
            $salarySettings[$shortKey] = $value;
        } elseif (strpos($key, 'notification_') === 0) {
            $shortKey = str_replace('notification_', '', $key);
            $notificationSettings[$shortKey] = $value;
        }
    }
}

// استدعاء رأس الصفحة
include '../../includes/templates/header.php';
?>

<!-- المحتوى الرئيسي للصفحة -->
<div class="container-fluid">
    <!-- العنوان وشريط التنقل -->
    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-body">
                    <h1 class="h3 mb-0">إعدادات النظام</h1>
                </div>
            </div>
        </div>
    </div>

    <!-- عرض رسائل النجاح والخطأ -->
    <?php if (isset($_SESSION['success_message'])): ?>
    <div class="alert alert-success" role="alert">
        <?php
            echo $_SESSION['success_message'];
            unset($_SESSION['success_message']);
        ?>
    </div>
    <?php endif; ?>

    <?php if (isset($_SESSION['error_message'])): ?>
    <div class="alert alert-danger" role="alert">
        <?php
            echo $_SESSION['error_message'];
            unset($_SESSION['error_message']);
        ?>
    </div>
    <?php endif; ?>

    <!-- نموذج الإعدادات -->
    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-body">
                    <form method="post">
                        <ul class="nav nav-tabs mb-4" id="settingsTabs" role="tablist">
                            <li class="nav-item" role="presentation">
                                <button class="nav-link active" id="system-tab" data-bs-toggle="tab" data-bs-target="#system" type="button" role="tab" aria-controls="system" aria-selected="true">إعدادات النظام</button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="invoice-tab" data-bs-toggle="tab" data-bs-target="#invoice" type="button" role="tab" aria-controls="invoice" aria-selected="false">إعدادات الفاتورة</button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="appointment-tab" data-bs-toggle="tab" data-bs-target="#appointment" type="button" role="tab" aria-controls="appointment" aria-selected="false">إعدادات المواعيد</button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="salary-tab" data-bs-toggle="tab" data-bs-target="#salary" type="button" role="tab" aria-controls="salary" aria-selected="false">إعدادات الرواتب</button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="notification-tab" data-bs-toggle="tab" data-bs-target="#notification" type="button" role="tab" aria-controls="notification" aria-selected="false">إعدادات الإشعارات</button>
                            </li>
                        </ul>

                        <div class="tab-content" id="settingsTabsContent">
                            <!-- إعدادات النظام -->
                            <div class="tab-pane fade show active" id="system" role="tabpanel" aria-labelledby="system-tab">
                                <h4 class="mb-3">إعدادات النظام العامة</h4>

                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <label for="default_language" class="form-label">اللغة الافتراضية</label>
                                        <select id="default_language" name="system[default_language]" class="form-select">
                                            <option value="ar" <?php echo ($systemSettings['default_language'] ?? 'ar') == 'ar' ? 'selected' : ''; ?>>العربية</option>
                                            <option value="en" <?php echo ($systemSettings['default_language'] ?? 'ar') == 'en' ? 'selected' : ''; ?>>الإنجليزية</option>
                                        </select>
                                    </div>
                                    <div class="col-md-6">
                                        <label for="timezone" class="form-label">المنطقة الزمنية</label>
                                        <select id="timezone" name="system[timezone]" class="form-select">
                                            <option value="Asia/Riyadh" <?php echo ($systemSettings['timezone'] ?? 'Asia/Riyadh') == 'Asia/Riyadh' ? 'selected' : ''; ?>>الرياض (GMT+3)</option>
                                            <option value="Asia/Dubai" <?php echo ($systemSettings['timezone'] ?? 'Asia/Riyadh') == 'Asia/Dubai' ? 'selected' : ''; ?>>دبي (GMT+4)</option>
                                            <option value="Asia/Kuwait" <?php echo ($systemSettings['timezone'] ?? 'Asia/Riyadh') == 'Asia/Kuwait' ? 'selected' : ''; ?>>الكويت (GMT+3)</option>
                                            <option value="Asia/Qatar" <?php echo ($systemSettings['timezone'] ?? 'Asia/Riyadh') == 'Asia/Qatar' ? 'selected' : ''; ?>>قطر (GMT+3)</option>
                                            <option value="Asia/Bahrain" <?php echo ($systemSettings['timezone'] ?? 'Asia/Riyadh') == 'Asia/Bahrain' ? 'selected' : ''; ?>>البحرين (GMT+3)</option>
                                            <option value="Africa/Cairo" <?php echo ($systemSettings['timezone'] ?? 'Asia/Riyadh') == 'Africa/Cairo' ? 'selected' : ''; ?>>القاهرة (GMT+2)</option>
                                        </select>
                                    </div>
                                </div>

                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <label for="currency" class="form-label">العملة</label>
                                        <input type="text" id="currency" name="system[currency]" class="form-control" value="<?php echo $systemSettings['currency'] ?? 'ريال سعودي'; ?>">
                                    </div>
                                    <div class="col-md-6">
                                        <label for="currency_symbol" class="form-label">رمز العملة</label>
                                        <input type="text" id="currency_symbol" name="system[currency_symbol]" class="form-control" value="<?php echo $systemSettings['currency_symbol'] ?? 'ر.س'; ?>">
                                    </div>
                                </div>

                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox" id="auto_backup" name="system[auto_backup]" <?php echo isset($systemSettings['auto_backup']) && $systemSettings['auto_backup'] ? 'checked' : ''; ?>>
                                            <label class="form-check-label" for="auto_backup">النسخ الاحتياطي التلقائي</label>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <label for="backup_interval" class="form-label">فترة النسخ الاحتياطي (بالأيام)</label>
                                        <input type="number" id="backup_interval" name="system[backup_interval]" class="form-control" value="<?php echo $systemSettings['backup_interval'] ?? '7'; ?>" min="1">
                                    </div>
                                </div>
                            </div>

                            <!-- إعدادات الفاتورة -->
                            <div class="tab-pane fade" id="invoice" role="tabpanel" aria-labelledby="invoice-tab">
                                <h4 class="mb-3">إعدادات الفاتورة</h4>

                                <div class="mb-3">
                                    <label for="company_name" class="form-label">اسم الشركة</label>
                                    <input type="text" id="company_name" name="invoice[company_name]" class="form-control" value="<?php echo $invoiceSettings['company_name'] ?? ''; ?>">
                                </div>

                                <div class="mb-3">
                                    <label for="header_text" class="form-label">نص ترويسة الفاتورة</label>
                                    <textarea id="header_text" name="invoice[header_text]" class="form-control" rows="2"><?php echo $invoiceSettings['header_text'] ?? ''; ?></textarea>
                                </div>

                                <div class="mb-3">
                                    <label for="footer_text" class="form-label">نص تذييل الفاتورة</label>
                                    <textarea id="footer_text" name="invoice[footer_text]" class="form-control" rows="2"><?php echo $invoiceSettings['footer_text'] ?? ''; ?></textarea>
                                </div>

                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox" id="include_tax" name="invoice[include_tax]" <?php echo isset($invoiceSettings['include_tax']) && $invoiceSettings['include_tax'] ? 'checked' : ''; ?>>
                                            <label class="form-check-label" for="include_tax">تضمين الضريبة</label>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <label for="tax_rate" class="form-label">معدل الضريبة (%)</label>
                                        <input type="number" id="tax_rate" name="invoice[tax_rate]" class="form-control" value="<?php echo $invoiceSettings['tax_rate'] ?? '15'; ?>" min="0" max="100" step="0.01">
                                    </div>
                                </div>

                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <label for="receipt_width" class="form-label">عرض إيصال الطباعة</label>
                                        <select id="receipt_width" name="invoice[receipt_width]" class="form-select">
                                            <option value="80" <?php echo ($invoiceSettings['receipt_width'] ?? '80') == '80' ? 'selected' : ''; ?>>80 ملم</option>
                                            <option value="58" <?php echo ($invoiceSettings['receipt_width'] ?? '80') == '58' ? 'selected' : ''; ?>>58 ملم</option>
                                        </select>
                                    </div>
                                    <div class="col-md-6">
                                        <label for="copies" class="form-label">عدد نسخ الفاتورة</label>
                                        <input type="number" id="copies" name="invoice[copies]" class="form-control" value="<?php echo $invoiceSettings['copies'] ?? '1'; ?>" min="1" max="5">
                                        <div class="form-text">عدد النسخ التي سيتم طباعتها تلقائياً</div>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="print_without_preview" name="invoice[print_without_preview]" value="1" <?php echo isset($invoiceSettings['print_without_preview']) && $invoiceSettings['print_without_preview'] == '1' ? 'checked' : ''; ?>>
                                        <label class="form-check-label" for="print_without_preview">الطباعة بدون معاينة</label>
                                    </div>
                                    <div class="form-text">عند تفعيل هذا الخيار، سيتم طباعة الفاتورة مباشرة دون عرض صفحة المعاينة</div>
                                </div>

                                <div class="mb-3">
                                    <label for="company_logo" class="form-label">شعار الشركة</label>
                                    <div class="input-group mb-3">
                                        <input type="file" class="form-control" id="company_logo_file" accept="image/*">
                                        <button class="btn btn-outline-secondary" type="button" id="upload-logo-btn">رفع</button>
                                    </div>
                                    <input type="hidden" id="company_logo" name="invoice[company_logo]" value="<?php echo $invoiceSettings['company_logo'] ?? ''; ?>">

                                    <?php if (!empty($invoiceSettings['company_logo'])): ?>
                                    <div class="mt-2" id="logo-preview-container">
                                        <img src="<?php echo BASE_URL . $invoiceSettings['company_logo']; ?>" alt="شعار الشركة" class="img-thumbnail" style="max-height: 100px;" id="logo-preview">
                                        <button type="button" class="btn btn-sm btn-danger mt-2" id="remove-logo-btn">إزالة الشعار</button>
                                    </div>
                                    <?php else: ?>
                                    <div class="mt-2" id="logo-preview-container" style="display: none;">
                                        <img src="" alt="شعار الشركة" class="img-thumbnail" style="max-height: 100px;" id="logo-preview">
                                        <button type="button" class="btn btn-sm btn-danger mt-2" id="remove-logo-btn">إزالة الشعار</button>
                                    </div>
                                    <?php endif; ?>
                                    <div class="form-text">يظهر الشعار في أعلى الفاتورة. الحجم المثالي: 300×100 بكسل.</div>
                                </div>

                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <label for="logo_width" class="form-label">عرض الشعار (بكسل)</label>
                                        <input type="number" id="logo_width" name="invoice[logo_width]" class="form-control" value="<?php echo $invoiceSettings['logo_width'] ?? '200'; ?>" min="50" max="500">
                                    </div>
                                    <div class="col-md-6">
                                        <label for="logo_height" class="form-label">ارتفاع الشعار (بكسل)</label>
                                        <input type="number" id="logo_height" name="invoice[logo_height]" class="form-control" value="<?php echo $invoiceSettings['logo_height'] ?? '100'; ?>" min="25" max="250">
                                    </div>
                                    <div class="col-12 mt-2">
                                        <div class="form-text">تحديد أبعاد الشعار في الفاتورة. القيم المناسبة للطباعة الحرارية: عرض 150-200 بكسل، ارتفاع 60-100 بكسل.</div>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label for="logo_print_quality" class="form-label">جودة طباعة الشعار</label>
                                    <select id="logo_print_quality" name="invoice[logo_print_quality]" class="form-select">
                                        <option value="normal" <?php echo ($invoiceSettings['logo_print_quality'] ?? 'normal') == 'normal' ? 'selected' : ''; ?>>عادية</option>
                                        <option value="enhanced" <?php echo ($invoiceSettings['logo_print_quality'] ?? 'normal') == 'enhanced' ? 'selected' : ''; ?>>محسنة</option>
                                        <option value="high" <?php echo ($invoiceSettings['logo_print_quality'] ?? 'normal') == 'high' ? 'selected' : ''; ?>>عالية</option>
                                    </select>
                                    <div class="form-text">تحسين جودة الشعار في الفاتورة المطبوعة. الجودة العالية تجعل الشعار أكثر وضوحًا في الطباعة الحرارية.</div>
                                </div>
                            </div>

                            <!-- إعدادات المواعيد -->
                            <div class="tab-pane fade" id="appointment" role="tabpanel" aria-labelledby="appointment-tab">
                                <h4 class="mb-3">إعدادات المواعيد</h4>

                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <label for="start_time" class="form-label">وقت بدء العمل</label>
                                        <input type="time" id="start_time" name="appointment[start_time]" class="form-control" value="<?php echo $appointmentSettings['start_time'] ?? '09:00'; ?>">
                                    </div>
                                    <div class="col-md-6">
                                        <label for="end_time" class="form-label">وقت انتهاء العمل</label>
                                        <input type="time" id="end_time" name="appointment[end_time]" class="form-control" value="<?php echo $appointmentSettings['end_time'] ?? '21:00'; ?>">
                                    </div>
                                </div>

                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <label for="interval" class="form-label">الفاصل الزمني (بالدقائق)</label>
                                        <input type="number" id="interval" name="appointment[interval]" class="form-control" value="<?php echo $appointmentSettings['interval'] ?? '30'; ?>" min="5" step="5">
                                    </div>
                                    <div class="col-md-6">
                                        <label for="days_in_advance" class="form-label">أيام الحجز المسبق</label>
                                        <input type="number" id="days_in_advance" name="appointment[days_in_advance]" class="form-control" value="<?php echo $appointmentSettings['days_in_advance'] ?? '30'; ?>" min="1">
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="allow_online_booking" name="appointment[allow_online_booking]" <?php echo isset($appointmentSettings['allow_online_booking']) && $appointmentSettings['allow_online_booking'] ? 'checked' : ''; ?>>
                                        <label class="form-check-label" for="allow_online_booking">السماح بالحجز عبر الإنترنت</label>
                                    </div>
                                </div>
                            </div>

                            <!-- إعدادات الرواتب -->
                            <div class="tab-pane fade" id="salary" role="tabpanel" aria-labelledby="salary-tab">
                                <h4 class="mb-3">إعدادات الرواتب</h4>

                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <label for="day_of_month" class="form-label">يوم صرف الرواتب من الشهر</label>
                                        <input type="number" id="day_of_month" name="salary[day_of_month]" class="form-control" value="<?php echo $salarySettings['day_of_month'] ?? '28'; ?>" min="1" max="31">
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-check form-switch mt-4">
                                            <input class="form-check-input" type="checkbox" id="payment_reminder" name="salary[payment_reminder]" <?php echo isset($salarySettings['payment_reminder']) && $salarySettings['payment_reminder'] ? 'checked' : ''; ?>>
                                            <label class="form-check-label" for="payment_reminder">تفعيل تذكير دفع الرواتب</label>
                                        </div>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label for="reminder_days" class="form-label">عدد أيام التذكير قبل موعد الدفع</label>
                                    <input type="number" id="reminder_days" name="salary[reminder_days]" class="form-control" value="<?php echo $salarySettings['reminder_days'] ?? '3'; ?>" min="1">
                                </div>
                            </div>

                            <!-- إعدادات الإشعارات -->
                            <div class="tab-pane fade" id="notification" role="tabpanel" aria-labelledby="notification-tab">
                                <h4 class="mb-3">إعدادات الإشعارات</h4>

                                <div class="mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="enable_email" name="notification[enable_email]" <?php echo isset($notificationSettings['enable_email']) && $notificationSettings['enable_email'] ? 'checked' : ''; ?>>
                                        <label class="form-check-label" for="enable_email">تفعيل إشعارات البريد الإلكتروني</label>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="enable_sms" name="notification[enable_sms]" <?php echo isset($notificationSettings['enable_sms']) && $notificationSettings['enable_sms'] ? 'checked' : ''; ?>>
                                        <label class="form-check-label" for="enable_sms">تفعيل إشعارات الرسائل النصية</label>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="enable_whatsapp" name="notification[enable_whatsapp]" <?php echo isset($notificationSettings['enable_whatsapp']) && $notificationSettings['enable_whatsapp'] ? 'checked' : ''; ?>>
                                        <label class="form-check-label" for="enable_whatsapp">تفعيل إشعارات واتساب</label>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="whatsapp_client_side" name="notification[whatsapp_client_side]" <?php echo isset($notificationSettings['whatsapp_client_side']) && $notificationSettings['whatsapp_client_side'] ? 'checked' : ''; ?>>
                                        <label class="form-check-label" for="whatsapp_client_side">استخدام واتساب من جانب العميل</label>
                                    </div>
                                    <div class="form-text">عند تفعيل هذا الخيار، سيتم استخدام واتساب من جانب العميل بدلاً من الخادم</div>
                                </div>

                                <div class="mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="appointment_reminder" name="notification[appointment_reminder]" <?php echo isset($notificationSettings['appointment_reminder']) && $notificationSettings['appointment_reminder'] ? 'checked' : ''; ?>>
                                        <label class="form-check-label" for="appointment_reminder">تفعيل تذكير المواعيد</label>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="invoice_notification" name="notification[invoice_notification]" <?php echo isset($notificationSettings['invoice_notification']) && $notificationSettings['invoice_notification'] == '1' ? 'checked' : ''; ?>>
                                        <label class="form-check-label" for="invoice_notification">تفعيل إشعارات الفواتير للعملاء</label>
                                    </div>
                                    <div class="form-text">عند تفعيل هذا الخيار، سيتم إرسال رسالة شكر للعميل بعد إنشاء فاتورة جديدة</div>
                                </div>

                                <div class="mb-3 invoice-notification-options" id="invoice-notification-options" style="<?php echo isset($notificationSettings['invoice_notification']) && $notificationSettings['invoice_notification'] == '1' ? '' : 'display: none;'; ?>">
                                    <label for="invoice_message_template" class="form-label">قالب رسالة الفاتورة</label>
                                    <textarea id="invoice_message_template" name="notification[invoice_message_template]" class="form-control" rows="5"><?php echo $notificationSettings['invoice_message_template'] ?? "شكراً لزيارتكم {company_name}\n\nتفاصيل الفاتورة:\nرقم الفاتورة: {invoice_number}\nالتاريخ: {invoice_date}\nالمبلغ الإجمالي: {invoice_total}\n\nنتطلع لرؤيتكم مرة أخرى!"; ?></textarea>
                                    <div class="form-text">يمكنك استخدام المتغيرات التالية: {customer_name}, {company_name}, {invoice_number}, {invoice_date}, {invoice_total}, {invoice_items}</div>
                                </div>

                                <div class="mb-3 invoice-notification-options" style="<?php echo isset($notificationSettings['invoice_notification']) && $notificationSettings['invoice_notification'] == '1' ? '' : 'display: none;'; ?>">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="include_invoice_details" name="notification[include_invoice_details]" <?php echo isset($notificationSettings['include_invoice_details']) && $notificationSettings['include_invoice_details'] ? 'checked' : ''; ?>>
                                        <label class="form-check-label" for="include_invoice_details">تضمين تفاصيل الفاتورة في الرسالة</label>
                                    </div>
                                    <div class="form-text">عند تفعيل هذا الخيار، سيتم تضمين تفاصيل المنتجات والخدمات في الرسالة</div>
                                </div>

                                <div class="mb-3">
                                    <label for="reminder_hours" class="form-label">عدد ساعات التذكير قبل الموعد</label>
                                    <input type="number" id="reminder_hours" name="notification[reminder_hours]" class="form-control" value="<?php echo $notificationSettings['reminder_hours'] ?? '24'; ?>" min="1">
                                </div>

                                <div class="mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="enable_minutes_reminder" name="notification[enable_minutes_reminder]" <?php echo isset($notificationSettings['enable_minutes_reminder']) && $notificationSettings['enable_minutes_reminder'] ? 'checked' : ''; ?>>
                                        <label class="form-check-label" for="enable_minutes_reminder">تفعيل تذكير قبل الموعد بدقائق</label>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label for="reminder_minutes" class="form-label">عدد دقائق التذكير قبل الموعد</label>
                                    <input type="number" id="reminder_minutes" name="notification[reminder_minutes]" class="form-control" value="<?php echo $notificationSettings['reminder_minutes'] ?? '30'; ?>" min="5" max="120">
                                    <div class="form-text">سيتم إرسال إشعار للمستخدم قبل الموعد بهذا العدد من الدقائق</div>
                                </div>

                                <div class="mb-3">
                                    <label for="reminder_method" class="form-label">طريقة إرسال التذكيرات</label>
                                    <select id="reminder_method" name="notification[reminder_method]" class="form-select">
                                        <option value="cron" <?php echo (!isset($notificationSettings['reminder_method']) || $notificationSettings['reminder_method'] == 'cron') ? 'selected' : ''; ?>>باستخدام Cron Job (مناسب للخوادم التي تدعم Cron)</option>
                                        <option value="js" <?php echo (isset($notificationSettings['reminder_method']) && $notificationSettings['reminder_method'] == 'js') ? 'selected' : ''; ?>>باستخدام JavaScript (مناسب للاستضافة التي لا تدعم Cron)</option>
                                    </select>
                                    <div class="form-text">اختر طريقة إرسال التذكيرات المناسبة لبيئة الاستضافة الخاصة بك</div>
                                </div>

                                <div class="mb-3 js-reminder-options" style="<?php echo (isset($notificationSettings['reminder_method']) && $notificationSettings['reminder_method'] == 'js') ? '' : 'display: none;'; ?>">
                                    <label for="check_interval" class="form-label">الفترة الزمنية بين عمليات التحقق (بالدقائق)</label>
                                    <input type="number" id="check_interval" name="notification[check_interval]" class="form-control" value="<?php echo $notificationSettings['check_interval'] ?? '5'; ?>" min="1" max="60">
                                    <div class="form-text">الفترة الزمنية بين عمليات التحقق من وجود تذكيرات جديدة للإرسال</div>
                                </div>

                                <div class="mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="debug_mode" name="notification[debug_mode]" <?php echo isset($notificationSettings['debug_mode']) && $notificationSettings['debug_mode'] ? 'checked' : ''; ?>>
                                        <label class="form-check-label" for="debug_mode">تفعيل وضع التصحيح</label>
                                    </div>
                                    <div class="form-text">تسجيل معلومات إضافية عن عمليات الإشعارات في ملفات السجل</div>
                                </div>

                                <div class="mb-3">
                                    <a href="../../scripts/create_notifications_table.php" class="btn btn-primary">إنشاء جدول الإشعارات</a>
                                    <a href="../../scripts/test_appointment_reminders.php" class="btn btn-success ms-2">اختبار نظام التذكير</a>
                                    <div class="form-text mt-2">يجب إنشاء جدول الإشعارات لتفعيل نظام تذكير المواعيد</div>
                                </div>

                                <hr class="my-4">

                                <h5 class="mb-3">إشعارات الواتساب لمدير النظام</h5>

                                <div class="mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="admin_notifications" name="notification[admin_notifications]" <?php echo isset($notificationSettings['admin_notifications']) && $notificationSettings['admin_notifications'] == '1' ? 'checked' : ''; ?>>
                                        <label class="form-check-label" for="admin_notifications">تفعيل إشعارات الواتساب لمدير النظام</label>
                                    </div>
                                    <div class="form-text">عند تفعيل هذا الخيار، سيتم إرسال إشعارات دورية لمدير النظام عبر الواتساب</div>
                                </div>

                                <div class="mb-3 admin-notification-options" style="<?php echo isset($notificationSettings['admin_notifications']) && $notificationSettings['admin_notifications'] == '1' ? '' : 'display: none;'; ?>">
                                    <label for="admin_phone_numbers" class="form-label">أرقام الواتساب للمدراء</label>
                                    <textarea id="admin_phone_numbers" name="notification[admin_phone_numbers]" class="form-control" rows="3" placeholder="أدخل رقم واحد في كل سطر بالصيغة الدولية مثل: 201012345678"><?php echo $notificationSettings['admin_phone_numbers'] ?? ''; ?></textarea>
                                    <div class="form-text">أدخل أرقام الواتساب للمدراء (رقم واحد في كل سطر) بالصيغة الدولية</div>
                                </div>

                                <div class="mb-3 admin-notification-options" style="<?php echo isset($notificationSettings['admin_notifications']) && $notificationSettings['admin_notifications'] == '1' ? '' : 'display: none;'; ?>">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="notify_workday_open" name="notification[notify_workday_open]" <?php echo isset($notificationSettings['notify_workday_open']) && $notificationSettings['notify_workday_open'] == '1' ? 'checked' : ''; ?>>
                                        <label class="form-check-label" for="notify_workday_open">إشعار عند فتح يوم عمل جديد</label>
                                    </div>
                                </div>

                                <div class="mb-3 admin-notification-options" style="<?php echo isset($notificationSettings['admin_notifications']) && $notificationSettings['admin_notifications'] == '1' ? '' : 'display: none;'; ?>">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="notify_workday_close" name="notification[notify_workday_close]" <?php echo isset($notificationSettings['notify_workday_close']) && $notificationSettings['notify_workday_close'] == '1' ? 'checked' : ''; ?>>
                                        <label class="form-check-label" for="notify_workday_close">إشعار عند إغلاق يوم عمل</label>
                                    </div>
                                </div>

                                <div class="mb-3 admin-notification-options" style="<?php echo isset($notificationSettings['admin_notifications']) && $notificationSettings['admin_notifications'] == '1' ? '' : 'display: none;'; ?>">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="notify_daily_report" name="notification[notify_daily_report]" <?php echo isset($notificationSettings['notify_daily_report']) && $notificationSettings['notify_daily_report'] == '1' ? 'checked' : ''; ?>>
                                        <label class="form-check-label" for="notify_daily_report">إرسال تقرير يومي</label>
                                    </div>
                                    <div class="form-text">سيتم إرسال تقرير يومي شامل عند إغلاق يوم العمل</div>
                                </div>

                                <div class="mb-3 admin-notification-options" style="<?php echo isset($notificationSettings['admin_notifications']) && $notificationSettings['admin_notifications'] == '1' ? '' : 'display: none;'; ?>">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="notify_new_invoice" name="notification[notify_new_invoice]" <?php echo isset($notificationSettings['notify_new_invoice']) && $notificationSettings['notify_new_invoice'] == '1' ? 'checked' : ''; ?>>
                                        <label class="form-check-label" for="notify_new_invoice">إشعار عند إنشاء فاتورة جديدة</label>
                                    </div>
                                    <div class="form-text">سيتم إرسال إشعار للمدراء عند إنشاء فاتورة جديدة</div>
                                </div>

                                <div class="mb-3 admin-notification-options" style="<?php echo isset($notificationSettings['admin_notifications']) && $notificationSettings['admin_notifications'] == '1' ? '' : 'display: none;'; ?>">
                                    <label for="admin_message_template" class="form-label">قالب رسالة التقرير اليومي</label>
                                    <textarea id="admin_message_template" name="notification[admin_message_template]" class="form-control" rows="5"><?php echo $notificationSettings['admin_message_template'] ?? "تقرير يومي {date}\n\nإجمالي المبيعات: {total_sales}\nعدد الفواتير: {invoices_count}\nإجمالي المصروفات: {total_expenses}\nصافي الربح: {net_profit}\n\nأفضل المنتجات مبيعاً:\n{top_products}\n\nأفضل الخدمات مبيعاً:\n{top_services}"; ?></textarea>
                                    <div class="form-text">يمكنك استخدام المتغيرات التالية: {date}, {total_sales}, {invoices_count}, {total_expenses}, {net_profit}, {top_products}, {top_services}, {branch_name}</div>
                                </div>
                            </div>
                        </div>

                        <div class="mt-4">
                            <button type="submit" name="update_settings" class="btn btn-primary">حفظ الإعدادات</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- استدعاء قالب الفوتر -->
<?php include '../../includes/templates/footer.php'; ?>

<!-- ملف JavaScript لإعدادات الإشعارات -->
<script src="<?php echo BASE_URL; ?>assets/js/notification-settings.js"></script>

<!-- سكريبت للتأكد من حفظ جميع الإعدادات -->
<script>
$(document).ready(function() {
    // تسجيل معلومات التصحيح
    console.log('Settings page loaded');

    // معالجة تقديم النموذج
    $('form').on('submit', function(e) {
        // التأكد من أن جميع الاختيارات موجودة في النموذج
        // إضافة حقول مخفية للاختيارات غير المحددة

        // التأكد من وجود اختيارات الفاتورة
        if (!$('input[name="invoice[include_tax]"]').is(':checked')) {
            $(this).append('<input type="hidden" name="invoice[include_tax]" value="0">');
        }
        if (!$('input[name="invoice[print_without_preview]"]').is(':checked')) {
            $(this).append('<input type="hidden" name="invoice[print_without_preview]" value="0">');
        }

        // التأكد من وجود اختيارات النظام
        if (!$('input[name="system[auto_backup]"]').is(':checked')) {
            $(this).append('<input type="hidden" name="system[auto_backup]" value="0">');
        }

        // التأكد من وجود اختيارات المواعيد
        if (!$('input[name="appointment[allow_online_booking]"]').is(':checked')) {
            $(this).append('<input type="hidden" name="appointment[allow_online_booking]" value="0">');
        }

        // التأكد من وجود اختيارات الرواتب
        if (!$('input[name="salary[payment_reminder]"]').is(':checked')) {
            $(this).append('<input type="hidden" name="salary[payment_reminder]" value="0">');
        }

        // التأكد من وجود اختيارات الإشعارات
        if (!$('input[name="notification[enable_email]"]').is(':checked')) {
            $(this).append('<input type="hidden" name="notification[enable_email]" value="0">');
        }
        if (!$('input[name="notification[enable_sms]"]').is(':checked')) {
            $(this).append('<input type="hidden" name="notification[enable_sms]" value="0">');
        }
        if (!$('input[name="notification[enable_whatsapp]"]').is(':checked')) {
            $(this).append('<input type="hidden" name="notification[enable_whatsapp]" value="0">');
        }
        if (!$('input[name="notification[whatsapp_client_side]"]').is(':checked')) {
            $(this).append('<input type="hidden" name="notification[whatsapp_client_side]" value="0">');
        }
        if (!$('input[name="notification[appointment_reminder]"]').is(':checked')) {
            $(this).append('<input type="hidden" name="notification[appointment_reminder]" value="0">');
        }
        if (!$('input[name="notification[admin_notifications]"]').is(':checked')) {
            $(this).append('<input type="hidden" name="notification[admin_notifications]" value="0">');
        }
        if (!$('input[name="notification[invoice_notification]"]').is(':checked')) {
            $(this).append('<input type="hidden" name="notification[invoice_notification]" value="0">');
        }
        if (!$('input[name="notification[notify_workday_open]"]').is(':checked')) {
            $(this).append('<input type="hidden" name="notification[notify_workday_open]" value="0">');
        }
        if (!$('input[name="notification[notify_workday_close]"]').is(':checked')) {
            $(this).append('<input type="hidden" name="notification[notify_workday_close]" value="0">');
        }
        if (!$('input[name="notification[notify_daily_report]"]').is(':checked')) {
            $(this).append('<input type="hidden" name="notification[notify_daily_report]" value="0">');
        }
        if (!$('input[name="notification[notify_new_invoice]"]').is(':checked')) {
            $(this).append('<input type="hidden" name="notification[notify_new_invoice]" value="0">');
        }
        if (!$('input[name="notification[enable_minutes_reminder]"]').is(':checked')) {
            $(this).append('<input type="hidden" name="notification[enable_minutes_reminder]" value="0">');
        }

        console.log('Form submitted with all checkboxes accounted for');
    });
});
</script>

<script>
$(document).ready(function() {
    // إظهار/إخفاء خيارات إشعارات الفواتير
    $('#invoice_notification').on('change', function() {
        if ($(this).is(':checked')) {
            $('.invoice-notification-options').show();
        } else {
            $('.invoice-notification-options').hide();
        }
    });

    // إظهار/إخفاء خيارات إشعارات المدير
    $('#admin_notifications').on('change', function() {
        if ($(this).is(':checked')) {
            $('.admin-notification-options').show();
            // إعادة تفعيل الخيارات الفرعية
            $('#notify_workday_open').prop('disabled', false);
            $('#notify_workday_close').prop('disabled', false);
            $('#notify_daily_report').prop('disabled', false);
            $('#notify_new_invoice').prop('disabled', false);
        } else {
            $('.admin-notification-options').hide();
            // تعطيل الخيارات الفرعية
            $('#notify_workday_open').prop('disabled', true);
            $('#notify_workday_close').prop('disabled', true);
            $('#notify_daily_report').prop('disabled', true);
            $('#notify_new_invoice').prop('disabled', true);
        }
    });
});
</script>

<script>
$(document).ready(function() {
    // معالجة رفع شعار الشركة
    $('#upload-logo-btn').on('click', function() {
        const fileInput = $('#company_logo_file')[0];
        if (fileInput.files.length === 0) {
            alert('الرجاء اختيار صورة أولاً');
            return;
        }

        const file = fileInput.files[0];
        const formData = new FormData();
        formData.append('logo', file);
        formData.append('action', 'upload-logo');

        $.ajax({
            url: '<?php echo BASE_URL; ?>api/settings.php',
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            beforeSend: function() {
                $('#upload-logo-btn').prop('disabled', true).html('<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> جاري الرفع...');
            },
            success: function(response) {
                $('#upload-logo-btn').prop('disabled', false).html('رفع');

                if (response.status === 'success') {
                    // تحديث قيمة الحقل المخفي
                    $('#company_logo').val(response.logo_path);

                    // عرض معاينة الشعار
                    $('#logo-preview').attr('src', '<?php echo BASE_URL; ?>' + response.logo_path);
                    $('#logo-preview-container').show();

                    // حفظ الإعدادات تلقائيًا بعد رفع الشعار
                    // نسخ جميع قيم النموذج الحالية
                    // إضافة مسار الشعار الجديد
                    $('#company_logo').val(response.logo_path);

                    // إنشاء بيانات النموذج
                    var formData = $('form').serialize() + '&update_settings=true';

                    // إرسال النموذج بالكامل
                    $.ajax({
                        url: '<?php echo $_SERVER["PHP_SELF"]; ?>',
                        type: 'POST',
                        data: formData,
                        success: function() {
                            // إظهار رسالة نجاح
                            alert('تم رفع وحفظ الشعار بنجاح');
                        },
                        error: function() {
                            alert('تم رفع الشعار بنجاح لكن يجب النقر على زر حفظ الإعدادات لتأكيد التغييرات');
                        }
                    });
                } else {
                    // إظهار رسالة الخطأ
                    alert('حدث خطأ أثناء رفع الشعار: ' + response.message);
                }
            },
            error: function() {
                $('#upload-logo-btn').prop('disabled', false).html('رفع');
                alert('حدث خطأ أثناء رفع الشعار');
            }
        });
    });

    // معالجة إزالة شعار الشركة
    $('#remove-logo-btn').on('click', function() {
        if (confirm('هل أنت متأكد من رغبتك في إزالة الشعار؟')) {
            $.ajax({
                url: '<?php echo BASE_URL; ?>api/settings.php',
                type: 'POST',
                data: {
                    action: 'remove-logo',
                    logo_path: $('#company_logo').val()
                },
                dataType: 'json',
                beforeSend: function() {
                    $('#remove-logo-btn').prop('disabled', true).html('<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> جاري الإزالة...');
                },
                success: function(response) {
                    $('#remove-logo-btn').prop('disabled', false).html('إزالة الشعار');

                    if (response.status === 'success') {
                        // إعادة تعيين قيمة الحقل المخفي
                        $('#company_logo').val('');

                        // إخفاء معاينة الشعار
                        $('#logo-preview-container').hide();

                        // حفظ الإعدادات تلقائيًا بعد إزالة الشعار
                        // إضافة قيمة فارغة للشعار
                        $('#company_logo').val('');

                        // إنشاء بيانات النموذج
                        var formData = $('form').serialize() + '&update_settings=true';

                        // إرسال النموذج بالكامل
                        $.ajax({
                            url: '<?php echo $_SERVER["PHP_SELF"]; ?>',
                            type: 'POST',
                            data: formData,
                            success: function() {
                                // إظهار رسالة نجاح
                                alert('تم إزالة وحفظ الشعار بنجاح');
                            },
                            error: function() {
                                alert('تم إزالة الشعار بنجاح لكن يجب النقر على زر حفظ الإعدادات لتأكيد التغييرات');
                            }
                        });
                    } else {
                        // إظهار رسالة الخطأ
                        alert('حدث خطأ أثناء إزالة الشعار: ' + response.message);
                    }
                },
                error: function() {
                    $('#remove-logo-btn').prop('disabled', false).html('إزالة الشعار');
                    alert('حدث خطأ أثناء إزالة الشعار');
                }
            });
        }
    });
});
</script>
