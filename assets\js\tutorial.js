/**
 * Tutorial System for Salon Management System
 * Provides interactive tutorials for each page
 */

class TutorialSystem {
    constructor() {
        this.currentPage = this.getCurrentPageName();
        this.tutorialData = {};
        this.isModalOpen = false;
        this.init();
    }

    init() {
        this.loadTutorialData();
        this.createTutorialButton();
        this.bindEvents();
    }

    getCurrentPageName() {
        const path = window.location.pathname;
        const segments = path.split('/');
        const fileName = segments[segments.length - 1];
        const folderName = segments[segments.length - 2];
        
        // Create a unique identifier for the page
        if (folderName && folderName !== 'pages') {
            return `${folderName}_${fileName.replace('.php', '')}`;
        }
        return fileName.replace('.php', '');
    }

    loadTutorialData() {
        // Tutorial data for each page
        this.tutorialData = {
            'dashboard': {
                title: 'لوحة التحكم الرئيسية',
                description: 'مرحباً بك في لوحة التحكم الرئيسية لنظام إدارة الصالون',
                content: `
                    <div class="tutorial-highlight">
                        <strong>الغرض من هذه الصفحة:</strong> عرض ملخص شامل لأداء الصالون وأهم المؤشرات
                    </div>
                    
                    <h4>المؤشرات الرئيسية:</h4>
                    <div class="tutorial-steps">
                        <div class="tutorial-step">
                            <strong>مبيعات اليوم:</strong> إجمالي المبيعات لليوم الحالي أو اليوم المفتوح
                        </div>
                        <div class="tutorial-step">
                            <strong>مصروفات اليوم:</strong> إجمالي المصروفات المسجلة لليوم
                        </div>
                        <div class="tutorial-step">
                            <strong>مواعيد اليوم:</strong> عدد المواعيد المحجوزة لليوم الحالي
                        </div>
                        <div class="tutorial-step">
                            <strong>المنتجات منخفضة المخزون:</strong> عدد المنتجات التي تحتاج إعادة تموين
                        </div>
                    </div>

                    <h4>الرسوم البيانية والقوائم:</h4>
                    <ul>
                        <li><strong>رسم بياني المبيعات:</strong> يعرض تطور المبيعات خلال الفترة الماضية</li>
                        <li><strong>المواعيد القادمة:</strong> قائمة بأهم المواعيد المحجوزة لليوم</li>
                        <li><strong>أفضل الموظفين:</strong> ترتيب الموظفين حسب الأداء</li>
                        <li><strong>أفضل العملاء:</strong> العملاء الأكثر إنفاقاً</li>
                    </ul>

                    <div class="tutorial-highlight">
                        <strong>نصيحة:</strong> استخدم هذه الصفحة للحصول على نظرة سريعة على أداء الصالون قبل بدء العمل اليومي
                    </div>
                `
            },
            'auth_login': {
                title: 'صفحة تسجيل الدخول',
                description: 'صفحة الدخول الآمن إلى نظام إدارة الصالون',
                content: `
                    <div class="tutorial-highlight">
                        <strong>الغرض من هذه الصفحة:</strong> تسجيل الدخول الآمن للمستخدمين المصرح لهم
                    </div>
                    
                    <h4>كيفية تسجيل الدخول:</h4>
                    <div class="tutorial-steps">
                        <div class="tutorial-step">
                            أدخل اسم المستخدم الخاص بك في الحقل الأول
                        </div>
                        <div class="tutorial-step">
                            أدخل كلمة المرور في الحقل الثاني
                        </div>
                        <div class="tutorial-step">
                            اختر "تذكرني" إذا كنت تريد البقاء مسجلاً للدخول
                        </div>
                        <div class="tutorial-step">
                            اضغط على زر "تسجيل الدخول"
                        </div>
                    </div>

                    <h4>مستويات المستخدمين:</h4>
                    <ul>
                        <li><strong>المدير:</strong> صلاحيات كاملة على النظام</li>
                        <li><strong>مدير الفرع:</strong> إدارة فرع محدد</li>
                        <li><strong>الكاشير:</strong> نقطة البيع والفواتير</li>
                        <li><strong>الموظف:</strong> المواعيد والخدمات</li>
                    </ul>

                    <div class="tutorial-highlight">
                        <strong>أمان:</strong> النظام يحمي بياناتك بتشفير متقدم ومصادقة آمنة
                    </div>
                `
            },
            'appointments_index': {
                title: 'إدارة المواعيد',
                description: 'نظام شامل لإدارة مواعيد العملاء وجدولة الخدمات',
                content: `
                    <div class="tutorial-highlight">
                        <strong>الغرض من هذه الصفحة:</strong> إدارة وتنظيم مواعيد العملاء مع الموظفين
                    </div>
                    
                    <h4>الوظائف الرئيسية:</h4>
                    <div class="tutorial-steps">
                        <div class="tutorial-step">
                            <strong>إضافة موعد جديد:</strong> حجز موعد لعميل مع موظف محدد
                        </div>
                        <div class="tutorial-step">
                            <strong>عرض المواعيد:</strong> استعراض جميع المواعيد بتواريخها وأوقاتها
                        </div>
                        <div class="tutorial-step">
                            <strong>تعديل الموعد:</strong> تغيير تفاصيل الموعد أو نقله لوقت آخر
                        </div>
                        <div class="tutorial-step">
                            <strong>إلغاء الموعد:</strong> إلغاء المواعيد غير المرغوب فيها
                        </div>
                    </div>

                    <h4>حالات المواعيد:</h4>
                    <ul>
                        <li><strong>محجوز:</strong> موعد مؤكد ومحجوز</li>
                        <li><strong>في الانتظار:</strong> العميل وصل وينتظر الخدمة</li>
                        <li><strong>مكتمل:</strong> تم تقديم الخدمة بنجاح</li>
                        <li><strong>ملغي:</strong> موعد تم إلغاؤه</li>
                    </ul>

                    <div class="tutorial-highlight">
                        <strong>نصيحة:</strong> استخدم الفلاتر للبحث عن مواعيد محددة حسب التاريخ أو الموظف أو العميل
                    </div>
                `
            },
            'customers_index': {
                title: 'إدارة العملاء',
                description: 'نظام شامل لإدارة بيانات العملاء وتتبع تاريخهم',
                content: `
                    <div class="tutorial-highlight">
                        <strong>الغرض من هذه الصفحة:</strong> إدارة قاعدة بيانات العملاء وتتبع معلوماتهم الشخصية
                    </div>

                    <h4>الوظائف الرئيسية:</h4>
                    <div class="tutorial-steps">
                        <div class="tutorial-step">
                            <strong>إضافة عميل جديد:</strong> تسجيل عميل جديد مع بياناته الأساسية
                        </div>
                        <div class="tutorial-step">
                            <strong>تعديل بيانات العميل:</strong> تحديث معلومات العميل الشخصية
                        </div>
                        <div class="tutorial-step">
                            <strong>عرض تاريخ العميل:</strong> مراجعة جميع زيارات وفواتير العميل
                        </div>
                        <div class="tutorial-step">
                            <strong>نقاط الولاء:</strong> تتبع وإدارة نقاط الولاء للعميل
                        </div>
                    </div>

                    <h4>البيانات المحفوظة:</h4>
                    <ul>
                        <li><strong>المعلومات الأساسية:</strong> الاسم، الهاتف، البريد الإلكتروني</li>
                        <li><strong>العنوان:</strong> عنوان العميل للتواصل</li>
                        <li><strong>تاريخ الميلاد:</strong> لإرسال التهاني والعروض الخاصة</li>
                        <li><strong>الملاحظات:</strong> أي ملاحظات خاصة بالعميل</li>
                        <li><strong>نقاط الولاء:</strong> النقاط المكتسبة من الزيارات</li>
                    </ul>

                    <div class="tutorial-highlight">
                        <strong>نصيحة:</strong> استخدم البحث السريع للعثور على العملاء بسهولة عن طريق الاسم أو رقم الهاتف
                    </div>
                `
            },
            'pos_index': {
                title: 'نقطة البيع',
                description: 'نظام نقطة البيع لإنشاء الفواتير وتسجيل المبيعات',
                content: `
                    <div class="tutorial-highlight">
                        <strong>الغرض من هذه الصفحة:</strong> إنشاء فواتير المبيعات وتسجيل الخدمات والمنتجات
                    </div>

                    <h4>خطوات إنشاء فاتورة:</h4>
                    <div class="tutorial-steps">
                        <div class="tutorial-step">
                            <strong>اختيار العميل:</strong> ابحث عن العميل أو أضف عميل جديد
                        </div>
                        <div class="tutorial-step">
                            <strong>إضافة الخدمات:</strong> اختر الخدمات المقدمة مع الموظف المسؤول
                        </div>
                        <div class="tutorial-step">
                            <strong>إضافة المنتجات:</strong> أضف أي منتجات مباعة مع الكمية
                        </div>
                        <div class="tutorial-step">
                            <strong>تطبيق الخصومات:</strong> أضف أي خصومات أو عروض ترويجية
                        </div>
                        <div class="tutorial-step">
                            <strong>اختيار طريقة الدفع:</strong> نقدي، بطاقة، أو مؤجل
                        </div>
                        <div class="tutorial-step">
                            <strong>طباعة الفاتورة:</strong> اطبع الفاتورة وسلمها للعميل
                        </div>
                    </div>

                    <h4>أنواع الدفع:</h4>
                    <ul>
                        <li><strong>نقدي:</strong> دفع فوري بالنقد</li>
                        <li><strong>بطاقة:</strong> دفع بالبطاقة الائتمانية</li>
                        <li><strong>مؤجل:</strong> دفع لاحق (دين على العميل)</li>
                        <li><strong>نقاط الولاء:</strong> استخدام نقاط العميل للدفع</li>
                    </ul>

                    <div class="tutorial-highlight">
                        <strong>مهم:</strong> تأكد من حفظ الفاتورة قبل الطباعة لضمان تسجيل البيانات في النظام
                    </div>
                `
            },
            'invoices_index': {
                title: 'إدارة الفواتير',
                description: 'عرض وإدارة جميع فواتير المبيعات والمدفوعات',
                content: `
                    <div class="tutorial-highlight">
                        <strong>الغرض من هذه الصفحة:</strong> مراجعة وإدارة جميع الفواتير الصادرة من النظام
                    </div>

                    <h4>الوظائف المتاحة:</h4>
                    <div class="tutorial-steps">
                        <div class="tutorial-step">
                            <strong>عرض الفواتير:</strong> استعراض جميع الفواتير مع تفاصيلها
                        </div>
                        <div class="tutorial-step">
                            <strong>البحث والفلترة:</strong> البحث عن فواتير محددة حسب التاريخ أو العميل
                        </div>
                        <div class="tutorial-step">
                            <strong>طباعة الفاتورة:</strong> إعادة طباعة أي فاتورة سابقة
                        </div>
                        <div class="tutorial-step">
                            <strong>تحديث حالة الدفع:</strong> تغيير حالة الفاتورة من مؤجل إلى مدفوع
                        </div>
                    </div>

                    <h4>حالات الفواتير:</h4>
                    <ul>
                        <li><strong>مدفوعة:</strong> فواتير تم دفعها بالكامل</li>
                        <li><strong>مؤجلة:</strong> فواتير لم يتم دفعها بعد</li>
                        <li><strong>مؤجلة جزئياً:</strong> فواتير تم دفع جزء منها فقط</li>
                        <li><strong>ملغية:</strong> فواتير تم إلغاؤها</li>
                    </ul>

                    <div class="tutorial-highlight">
                        <strong>نصيحة:</strong> استخدم التقارير لمتابعة الفواتير المؤجلة والمتأخرة في السداد
                    </div>
                `
            },
            'services_index': {
                title: 'إدارة الخدمات',
                description: 'إدارة خدمات الصالون وأسعارها ومدتها الزمنية',
                content: `
                    <div class="tutorial-highlight">
                        <strong>الغرض من هذه الصفحة:</strong> إدارة قائمة خدمات الصالون مع تحديد الأسعار والأوقات
                    </div>

                    <h4>إدارة الخدمات:</h4>
                    <div class="tutorial-steps">
                        <div class="tutorial-step">
                            <strong>إضافة خدمة جديدة:</strong> إنشاء خدمة جديدة مع تحديد السعر والمدة
                        </div>
                        <div class="tutorial-step">
                            <strong>تعديل الخدمة:</strong> تحديث أسعار أو أوقات الخدمات الموجودة
                        </div>
                        <div class="tutorial-step">
                            <strong>تصنيف الخدمات:</strong> تنظيم الخدمات في فئات مختلفة
                        </div>
                        <div class="tutorial-step">
                            <strong>تفعيل/إلغاء الخدمة:</strong> إظهار أو إخفاء خدمات معينة
                        </div>
                    </div>

                    <h4>معلومات الخدمة:</h4>
                    <ul>
                        <li><strong>اسم الخدمة:</strong> الاسم الذي يظهر للعملاء</li>
                        <li><strong>الوصف:</strong> تفاصيل الخدمة المقدمة</li>
                        <li><strong>السعر:</strong> تكلفة الخدمة</li>
                        <li><strong>المدة:</strong> الوقت المتوقع لإنجاز الخدمة</li>
                        <li><strong>الفئة:</strong> تصنيف الخدمة (قص، صبغة، علاج، إلخ)</li>
                    </ul>

                    <div class="tutorial-highlight">
                        <strong>نصيحة:</strong> حدد أوقات دقيقة للخدمات لضمان جدولة مواعيد فعالة
                    </div>
                `
            },
            'products_index': {
                title: 'إدارة المنتجات',
                description: 'إدارة منتجات الصالون وأسعار البيع',
                content: `
                    <div class="tutorial-highlight">
                        <strong>الغرض من هذه الصفحة:</strong> إدارة قائمة المنتجات المباعة في الصالون
                    </div>

                    <h4>إدارة المنتجات:</h4>
                    <div class="tutorial-steps">
                        <div class="tutorial-step">
                            <strong>إضافة منتج جديد:</strong> تسجيل منتج جديد مع تفاصيله وسعره
                        </div>
                        <div class="tutorial-step">
                            <strong>تحديث الأسعار:</strong> تعديل أسعار المنتجات حسب السوق
                        </div>
                        <div class="tutorial-step">
                            <strong>إدارة الفئات:</strong> تصنيف المنتجات في مجموعات
                        </div>
                        <div class="tutorial-step">
                            <strong>ربط بالمخزون:</strong> ربط المنتج بنظام إدارة المخزون
                        </div>
                    </div>

                    <h4>معلومات المنتج:</h4>
                    <ul>
                        <li><strong>اسم المنتج:</strong> الاسم التجاري للمنتج</li>
                        <li><strong>الباركود:</strong> رمز المنتج للمسح السريع</li>
                        <li><strong>سعر البيع:</strong> السعر المعروض للعملاء</li>
                        <li><strong>سعر التكلفة:</strong> سعر شراء المنتج</li>
                        <li><strong>الوصف:</strong> تفاصيل المنتج ومكوناته</li>
                    </ul>

                    <div class="tutorial-highlight">
                        <strong>نصيحة:</strong> استخدم الباركود لتسريع عملية البيع في نقطة البيع
                    </div>
                `
            },
            'inventory_index': {
                title: 'إدارة المخزون',
                description: 'متابعة مخزون المنتجات وحركات الدخول والخروج',
                content: `
                    <div class="tutorial-highlight">
                        <strong>الغرض من هذه الصفحة:</strong> مراقبة مستويات المخزون وإدارة حركات المنتجات
                    </div>

                    <h4>وظائف المخزون:</h4>
                    <div class="tutorial-steps">
                        <div class="tutorial-step">
                            <strong>عرض المخزون الحالي:</strong> مراجعة كميات جميع المنتجات
                        </div>
                        <div class="tutorial-step">
                            <strong>إضافة مخزون:</strong> تسجيل وصول منتجات جديدة
                        </div>
                        <div class="tutorial-step">
                            <strong>تعديل المخزون:</strong> تصحيح الكميات عند الجرد
                        </div>
                        <div class="tutorial-step">
                            <strong>تتبع الحركات:</strong> مراجعة تاريخ دخول وخروج المنتجات
                        </div>
                    </div>

                    <h4>تنبيهات المخزون:</h4>
                    <ul>
                        <li><strong>مخزون منخفض:</strong> منتجات تحتاج إعادة تموين</li>
                        <li><strong>مخزون منتهي:</strong> منتجات نفدت من المخزون</li>
                        <li><strong>منتجات راكدة:</strong> منتجات لم تباع لفترة طويلة</li>
                        <li><strong>تواريخ انتهاء:</strong> منتجات قاربت على الانتهاء</li>
                    </ul>

                    <div class="tutorial-highlight">
                        <strong>مهم:</strong> راجع المخزون بانتظام لتجنب نفاد المنتجات المهمة
                    </div>
                `
            },
            'employees_index': {
                title: 'إدارة الموظفين',
                description: 'إدارة بيانات الموظفين ومتابعة أدائهم',
                content: `
                    <div class="tutorial-highlight">
                        <strong>الغرض من هذه الصفحة:</strong> إدارة فريق العمل ومتابعة أداء الموظفين
                    </div>

                    <h4>إدارة الموظفين:</h4>
                    <div class="tutorial-steps">
                        <div class="tutorial-step">
                            <strong>إضافة موظف جديد:</strong> تسجيل موظف جديد مع بياناته الأساسية
                        </div>
                        <div class="tutorial-step">
                            <strong>تحديد المهارات:</strong> تسجيل الخدمات التي يقدمها كل موظف
                        </div>
                        <div class="tutorial-step">
                            <strong>متابعة الأداء:</strong> مراجعة إحصائيات أداء الموظف
                        </div>
                        <div class="tutorial-step">
                            <strong>إدارة الجدول:</strong> تحديد أوقات عمل كل موظف
                        </div>
                    </div>

                    <h4>معلومات الموظف:</h4>
                    <ul>
                        <li><strong>البيانات الشخصية:</strong> الاسم، الهاتف، العنوان</li>
                        <li><strong>المنصب:</strong> مصفف، خبير تجميل، مساعد</li>
                        <li><strong>الخدمات:</strong> قائمة الخدمات التي يقدمها</li>
                        <li><strong>نسبة العمولة:</strong> نسبة الموظف من كل خدمة</li>
                        <li><strong>ساعات العمل:</strong> جدول العمل الأسبوعي</li>
                    </ul>

                    <div class="tutorial-highlight">
                        <strong>نصيحة:</strong> حدد مهارات كل موظف بدقة لضمان جودة الخدمة المقدمة
                    </div>
                `
            },
            'expenses_index': {
                title: 'إدارة المصروفات',
                description: 'تسجيل ومتابعة جميع مصروفات الصالون',
                content: `
                    <div class="tutorial-highlight">
                        <strong>الغرض من هذه الصفحة:</strong> تسجيل وتتبع جميع المصروفات التشغيلية للصالون
                    </div>

                    <h4>أنواع المصروفات:</h4>
                    <div class="tutorial-steps">
                        <div class="tutorial-step">
                            <strong>مصروفات ثابتة:</strong> إيجار، رواتب، فواتير الخدمات
                        </div>
                        <div class="tutorial-step">
                            <strong>مصروفات متغيرة:</strong> مشتريات، صيانة، تسويق
                        </div>
                        <div class="tutorial-step">
                            <strong>مصروفات طارئة:</strong> إصلاحات، استبدال معدات
                        </div>
                        <div class="tutorial-step">
                            <strong>مصروفات شخصية:</strong> مكافآت، بدلات الموظفين
                        </div>
                    </div>

                    <h4>تصنيف المصروفات:</h4>
                    <ul>
                        <li><strong>التشغيل:</strong> مصروفات العمليات اليومية</li>
                        <li><strong>التسويق:</strong> إعلانات وحملات ترويجية</li>
                        <li><strong>الصيانة:</strong> إصلاح وصيانة المعدات</li>
                        <li><strong>المشتريات:</strong> شراء منتجات ومواد</li>
                        <li><strong>أخرى:</strong> مصروفات متنوعة</li>
                    </ul>

                    <div class="tutorial-highlight">
                        <strong>مهم:</strong> سجل جميع المصروفات لحساب الربحية الحقيقية للصالون
                    </div>
                `
            },
            'reports_reports': {
                title: 'التقارير والإحصائيات',
                description: 'تقارير شاملة عن أداء الصالون وتحليل البيانات',
                content: `
                    <div class="tutorial-highlight">
                        <strong>الغرض من هذه الصفحة:</strong> الحصول على تقارير مفصلة لاتخاذ قرارات إدارية مدروسة
                    </div>

                    <h4>أنواع التقارير:</h4>
                    <div class="tutorial-steps">
                        <div class="tutorial-step">
                            <strong>تقرير المبيعات:</strong> إجمالي المبيعات حسب الفترة الزمنية
                        </div>
                        <div class="tutorial-step">
                            <strong>تقرير الأرباح:</strong> حساب صافي الربح بعد خصم المصروفات
                        </div>
                        <div class="tutorial-step">
                            <strong>تقرير الموظفين:</strong> أداء كل موظف وعمولاته
                        </div>
                        <div class="tutorial-step">
                            <strong>تقرير العملاء:</strong> أفضل العملاء وتكرار الزيارات
                        </div>
                    </div>

                    <h4>فوائد التقارير:</h4>
                    <ul>
                        <li><strong>اتخاذ القرارات:</strong> قرارات مبنية على بيانات حقيقية</li>
                        <li><strong>تحسين الأداء:</strong> تحديد نقاط القوة والضعف</li>
                        <li><strong>التخطيط المالي:</strong> وضع ميزانيات وتوقعات مستقبلية</li>
                        <li><strong>متابعة الأهداف:</strong> قياس تحقيق الأهداف المحددة</li>
                    </ul>

                    <div class="tutorial-highlight">
                        <strong>نصيحة:</strong> راجع التقارير بانتظام لمتابعة نمو الصالون وتحسين الأداء
                    </div>
                `
            },
            'endday_index': {
                title: 'نهاية اليوم',
                description: 'إغلاق اليوم المحاسبي وحفظ البيانات',
                content: `
                    <div class="tutorial-highlight">
                        <strong>الغرض من هذه الصفحة:</strong> إنهاء اليوم المحاسبي وحفظ جميع المعاملات
                    </div>

                    <h4>خطوات إنهاء اليوم:</h4>
                    <div class="tutorial-steps">
                        <div class="tutorial-step">
                            <strong>مراجعة المبيعات:</strong> التأكد من صحة جميع الفواتير
                        </div>
                        <div class="tutorial-step">
                            <strong>تسجيل المصروفات:</strong> إدخال جميع مصروفات اليوم
                        </div>
                        <div class="tutorial-step">
                            <strong>عد النقدية:</strong> مطابقة النقدية الفعلية مع النظام
                        </div>
                        <div class="tutorial-step">
                            <strong>إغلاق اليوم:</strong> حفظ البيانات وإنهاء اليوم المحاسبي
                        </div>
                    </div>

                    <h4>أهمية إنهاء اليوم:</h4>
                    <ul>
                        <li><strong>دقة البيانات:</strong> ضمان صحة الأرقام المحاسبية</li>
                        <li><strong>الأمان:</strong> حماية البيانات من التعديل غير المصرح</li>
                        <li><strong>التقارير:</strong> إنشاء تقارير يومية دقيقة</li>
                        <li><strong>المتابعة:</strong> تتبع الأداء اليومي للصالون</li>
                    </ul>

                    <div class="tutorial-highlight">
                        <strong>مهم:</strong> لا تنس إنهاء اليوم قبل مغادرة الصالون لضمان حفظ جميع البيانات
                    </div>
                `
            },
            'settings_system': {
                title: 'إعدادات النظام',
                description: 'تخصيص إعدادات النظام حسب احتياجات الصالون',
                content: `
                    <div class="tutorial-highlight">
                        <strong>الغرض من هذه الصفحة:</strong> تخصيص النظام ليناسب طريقة عمل صالونك
                    </div>

                    <h4>الإعدادات الأساسية:</h4>
                    <div class="tutorial-steps">
                        <div class="tutorial-step">
                            <strong>معلومات الصالون:</strong> الاسم، العنوان، أرقام التواصل
                        </div>
                        <div class="tutorial-step">
                            <strong>إعدادات العملة:</strong> نوع العملة ورمزها
                        </div>
                        <div class="tutorial-step">
                            <strong>إعدادات الضرائب:</strong> نسب الضرائب المطبقة
                        </div>
                        <div class="tutorial-step">
                            <strong>إعدادات الطباعة:</strong> تخصيص شكل الفواتير المطبوعة
                        </div>
                    </div>

                    <h4>إعدادات متقدمة:</h4>
                    <ul>
                        <li><strong>نقاط الولاء:</strong> نظام مكافآت العملاء</li>
                        <li><strong>التذكيرات:</strong> إرسال تذكيرات المواعيد</li>
                        <li><strong>النسخ الاحتياطي:</strong> جدولة النسخ التلقائية</li>
                        <li><strong>الصلاحيات:</strong> تحديد صلاحيات المستخدمين</li>
                    </ul>

                    <div class="tutorial-highlight">
                        <strong>تحذير:</strong> كن حذراً عند تغيير الإعدادات الأساسية لتجنب مشاكل في النظام
                    </div>
                `
            }
        };
    }

    createTutorialButton() {
        // Remove existing button if any
        const existingBtn = document.querySelector('.tutorial-btn');
        if (existingBtn) {
            existingBtn.remove();
        }

        // Create tutorial button
        const button = document.createElement('button');
        button.className = 'tutorial-btn';
        button.innerHTML = '<i class="fas fa-question"></i>';
        button.title = 'دليل الاستخدام';
        
        // Add badge if tutorial is available for current page
        if (this.tutorialData[this.currentPage]) {
            const badge = document.createElement('span');
            badge.className = 'tutorial-badge';
            badge.textContent = '!';
            button.appendChild(badge);
        }

        document.body.appendChild(button);
    }

    bindEvents() {
        // Tutorial button click
        document.addEventListener('click', (e) => {
            if (e.target.closest('.tutorial-btn')) {
                this.showTutorial();
            }
        });

        // Close modal events
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('tutorial-modal') || 
                e.target.closest('.tutorial-close')) {
                this.closeTutorial();
            }
        });

        // Escape key to close
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && this.isModalOpen) {
                this.closeTutorial();
            }
        });
    }

    showTutorial() {
        const tutorialData = this.tutorialData[this.currentPage];
        
        if (!tutorialData) {
            this.showDefaultTutorial();
            return;
        }

        this.createModal(tutorialData);
        this.isModalOpen = true;
    }

    showDefaultTutorial() {
        const defaultData = {
            title: 'مرحباً بك في نظام إدارة الصالون',
            description: 'نظام شامل لإدارة جميع عمليات الصالون',
            content: `
                <div class="tutorial-highlight">
                    <strong>هذا النظام يساعدك في:</strong>
                </div>
                
                <ul>
                    <li>إدارة المواعيد والحجوزات</li>
                    <li>متابعة العملاء وبياناتهم</li>
                    <li>إدارة المخزون والمنتجات</li>
                    <li>تتبع المبيعات والأرباح</li>
                    <li>إدارة الموظفين والفروع</li>
                    <li>إنشاء التقارير المفصلة</li>
                </ul>

                <div class="tutorial-highlight">
                    <strong>للحصول على مساعدة مخصصة لكل صفحة، اضغط على أيقونة المساعدة في أي صفحة</strong>
                </div>
            `
        };
        
        this.createModal(defaultData);
        this.isModalOpen = true;
    }

    createModal(data) {
        // Remove existing modal
        const existingModal = document.querySelector('.tutorial-modal');
        if (existingModal) {
            existingModal.remove();
        }

        // Create modal
        const modal = document.createElement('div');
        modal.className = 'tutorial-modal';
        modal.innerHTML = `
            <div class="tutorial-content">
                <div class="tutorial-header">
                    <button class="tutorial-close">
                        <i class="fas fa-times"></i>
                    </button>
                    <h3>${data.title}</h3>
                    <p style="margin: 10px 0 0 0; opacity: 0.9; font-size: 16px;">${data.description}</p>
                </div>
                <div class="tutorial-body">
                    ${data.content}
                </div>
            </div>
        `;

        document.body.appendChild(modal);
        
        // Show modal with animation
        setTimeout(() => {
            modal.classList.add('show');
        }, 10);
    }

    closeTutorial() {
        const modal = document.querySelector('.tutorial-modal');
        if (modal) {
            modal.classList.remove('show');
            setTimeout(() => {
                modal.remove();
            }, 300);
        }
        this.isModalOpen = false;
    }
}

// Initialize tutorial system when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new TutorialSystem();
});
