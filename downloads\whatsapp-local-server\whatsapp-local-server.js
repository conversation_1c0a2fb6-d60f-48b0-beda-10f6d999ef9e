/**
 * خادم WhatsApp المحلي
 * يستخدم لإرسال رسائل WhatsApp من جانب العميل
 */

const express = require('express');
const puppeteer = require('puppeteer');
const cors = require('cors');
const fs = require('fs');
const path = require('path');
const app = express();
const port = 3000;

// السماح بطلبات CORS من الموقع
app.use(cors());
app.use(express.json());
app.use(express.static(path.join(__dirname, 'public')));

// إنشاء مجلد public إذا لم يكن موجودًا
const publicDir = path.join(__dirname, 'public');
if (!fs.existsSync(publicDir)) {
    fs.mkdirSync(publicDir, { recursive: true });
}

// إنشاء ملف HTML للواجهة
const htmlContent = `
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>خادم WhatsApp المحلي</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
            padding: 20px;
        }
        .card {
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }
        .btn-whatsapp {
            background-color: #25D366;
            color: white;
        }
        .btn-whatsapp:hover {
            background-color: #128C7E;
            color: white;
        }
        .status-indicator {
            width: 15px;
            height: 15px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 5px;
        }
        .status-connected {
            background-color: #25D366;
        }
        .status-disconnected {
            background-color: #dc3545;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header bg-success text-white">
                        <h4 class="mb-0"><i class="fab fa-whatsapp me-2"></i> خادم WhatsApp المحلي</h4>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info">
                            <p>هذا الخادم المحلي يسمح بإرسال رسائل WhatsApp من جهازك بدلاً من الخادم.</p>
                            <p>يجب أن يظل هذا الخادم قيد التشغيل طالما تريد استخدام ميزة إرسال رسائل WhatsApp.</p>
                        </div>

                        <div class="mb-4">
                            <h5>حالة الاتصال:</h5>
                            <div class="d-flex align-items-center">
                                <div id="status-indicator" class="status-indicator status-disconnected"></div>
                                <span id="status-text">جاري التحقق...</span>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <button id="open-whatsapp" class="btn btn-whatsapp w-100 mb-2">
                                    <i class="fab fa-whatsapp me-2"></i> فتح WhatsApp Web
                                </button>
                            </div>
                            <div class="col-md-6">
                                <button id="reset-session" class="btn btn-danger w-100 mb-2">
                                    <i class="fas fa-sync-alt me-2"></i> إعادة تعيين الجلسة
                                </button>
                            </div>
                        </div>

                        <div class="card bg-light">
                            <div class="card-body">
                                <h5>اختبار إرسال رسالة</h5>
                                <div class="mb-3">
                                    <label for="test-phone" class="form-label">رقم الهاتف</label>
                                    <input type="text" id="test-phone" class="form-control" placeholder="أدخل رقم الهاتف بتنسيق دولي (مثال: 201234567890)">
                                </div>
                                <div class="mb-3">
                                    <label for="test-message" class="form-label">الرسالة</label>
                                    <textarea id="test-message" class="form-control" rows="3" placeholder="أدخل نص الرسالة">مرحبا، هذه رسالة اختبار من نظام إدارة صالون البدرواي.</textarea>
                                </div>
                                <button id="send-test" class="btn btn-primary">
                                    <i class="fas fa-paper-plane me-2"></i> إرسال رسالة اختبار
                                </button>
                                <div id="test-result" class="mt-3"></div>
                            </div>
                        </div>
                    </div>
                    <div class="card-footer text-center">
                        <p class="mb-0">خادم WhatsApp المحلي - يعمل على المنفذ ${port}</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://kit.fontawesome.com/a076d05399.js" crossorigin="anonymous"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const statusIndicator = document.getElementById('status-indicator');
            const statusText = document.getElementById('status-text');
            const openWhatsAppBtn = document.getElementById('open-whatsapp');
            const resetSessionBtn = document.getElementById('reset-session');
            const sendTestBtn = document.getElementById('send-test');
            const testPhone = document.getElementById('test-phone');
            const testMessage = document.getElementById('test-message');
            const testResult = document.getElementById('test-result');

            // التحقق من حالة الاتصال
            function checkStatus() {
                fetch('/status')
                    .then(response => response.json())
                    .then(data => {
                        if (data.status === 'success' && data.isLoggedIn) {
                            statusIndicator.className = 'status-indicator status-connected';
                            statusText.textContent = 'متصل بـ WhatsApp Web';
                        } else {
                            statusIndicator.className = 'status-indicator status-disconnected';
                            statusText.textContent = 'غير متصل بـ WhatsApp Web';
                        }
                    })
                    .catch(error => {
                        console.error('Error checking status:', error);
                        statusIndicator.className = 'status-indicator status-disconnected';
                        statusText.textContent = 'خطأ في الاتصال';
                    });
            }

            // فتح WhatsApp Web
            openWhatsAppBtn.addEventListener('click', function() {
                openWhatsAppBtn.disabled = true;
                openWhatsAppBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> جاري الفتح...';

                fetch('/open')
                    .then(response => response.json())
                    .then(data => {
                        if (data.status === 'success') {
                            showAlert('تم فتح WhatsApp Web بنجاح. يرجى مسح رمز QR باستخدام تطبيق WhatsApp على هاتفك.', 'success');
                            // التحقق من الحالة بعد 10 ثواني
                            setTimeout(checkStatus, 10000);
                        } else {
                            showAlert('خطأ في فتح WhatsApp Web: ' + data.message, 'danger');
                        }
                        openWhatsAppBtn.disabled = false;
                        openWhatsAppBtn.innerHTML = '<i class="fab fa-whatsapp me-2"></i> فتح WhatsApp Web';
                    })
                    .catch(error => {
                        console.error('Error opening WhatsApp:', error);
                        showAlert('خطأ في الاتصال بالخادم', 'danger');
                        openWhatsAppBtn.disabled = false;
                        openWhatsAppBtn.innerHTML = '<i class="fab fa-whatsapp me-2"></i> فتح WhatsApp Web';
                    });
            });

            // إعادة تعيين الجلسة
            resetSessionBtn.addEventListener('click', function() {
                if (!confirm('هل أنت متأكد من رغبتك في إعادة تعيين جلسة WhatsApp؟ \n\nسيؤدي هذا إلى حذف بيانات الجلسة الحالية وستحتاج إلى إعادة تسجيل الدخول مرة أخرى.')) {
                    return;
                }

                resetSessionBtn.disabled = true;
                resetSessionBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> جاري إعادة التعيين...';

                fetch('/reset', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({})
                })
                    .then(response => response.json())
                    .then(data => {
                        if (data.status === 'success') {
                            showAlert('تم إعادة تعيين جلسة WhatsApp بنجاح. يرجى إعادة تسجيل الدخول مرة أخرى.', 'success');
                            // التحقق من الحالة بعد 5 ثواني
                            setTimeout(checkStatus, 5000);
                        } else {
                            showAlert('خطأ في إعادة تعيين الجلسة: ' + data.message, 'danger');
                        }
                        resetSessionBtn.disabled = false;
                        resetSessionBtn.innerHTML = '<i class="fas fa-sync-alt me-2"></i> إعادة تعيين الجلسة';
                    })
                    .catch(error => {
                        console.error('Error resetting session:', error);
                        showAlert('خطأ في الاتصال بالخادم', 'danger');
                        resetSessionBtn.disabled = false;
                        resetSessionBtn.innerHTML = '<i class="fas fa-sync-alt me-2"></i> إعادة تعيين الجلسة';
                    });
            });

            // إرسال رسالة اختبار
            sendTestBtn.addEventListener('click', function() {
                const phone = testPhone.value.trim();
                const message = testMessage.value.trim();

                if (!phone) {
                    showAlert('يرجى إدخال رقم الهاتف', 'warning');
                    return;
                }

                if (!message) {
                    showAlert('يرجى إدخال نص الرسالة', 'warning');
                    return;
                }

                sendTestBtn.disabled = true;
                sendTestBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> جاري الإرسال...';
                testResult.innerHTML = '<div class="alert alert-info">جاري إرسال الرسالة...</div>';

                fetch('/send', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        phone: phone,
                        message: message
                    })
                })
                    .then(response => response.json())
                    .then(data => {
                        if (data.status === 'success') {
                            showAlert('تم إرسال الرسالة بنجاح!', 'success');
                        } else {
                            showAlert('فشل إرسال الرسالة: ' + data.message, 'danger');
                        }
                        sendTestBtn.disabled = false;
                        sendTestBtn.innerHTML = '<i class="fas fa-paper-plane me-2"></i> إرسال رسالة اختبار';
                    })
                    .catch(error => {
                        console.error('Error sending message:', error);
                        showAlert('خطأ في الاتصال بالخادم', 'danger');
                        sendTestBtn.disabled = false;
                        sendTestBtn.innerHTML = '<i class="fas fa-paper-plane me-2"></i> إرسال رسالة اختبار';
                    });
            });

            // دالة مساعدة لعرض التنبيهات
            function showAlert(message, type = 'info') {
                testResult.innerHTML = '<div class="alert alert-' + type + '">' + message + '</div>';
            }

            // التحقق من الحالة عند تحميل الصفحة
            checkStatus();

            // التحقق من الحالة كل 30 ثانية
            setInterval(checkStatus, 30000);
        });
    </script>
</body>
</html>
`;

// كتابة ملف HTML
fs.writeFileSync(path.join(publicDir, 'index.html'), htmlContent);

// إعادة توجيه المسار الجذر إلى الواجهة
app.get('/', (_, res) => {
    res.sendFile(path.join(publicDir, 'index.html'));
});

// مسار حفظ جلسة WhatsApp
const userDataDir = path.join(__dirname, 'whatsapp-session');

// متغيرات لتخزين حالة المتصفح والصفحة الرئيسية
let browser = null;
let mainPage = null;

// طابور الأوامر
let commandQueue = [];
let isProcessingQueue = false;

// دالة لمعالجة طابور الأوامر
async function processQueue() {
    if (isProcessingQueue || commandQueue.length === 0) {
        return;
    }

    isProcessingQueue = true;

    try {
        const command = commandQueue.shift();
        console.log(`جاري تنفيذ الأمر: ${command.type}`);

        await command.execute();

        console.log(`تم تنفيذ الأمر: ${command.type}`);
    } catch (error) {
        console.error('خطأ في معالجة الأمر:', error);
    } finally {
        isProcessingQueue = false;

        // معالجة الأمر التالي في الطابور
        setTimeout(processQueue, 500);
    }
}

// دالة لإضافة أمر إلى الطابور
function addToQueue(commandType, executeFunction) {
    return new Promise((resolve, reject) => {
        const command = {
            type: commandType,
            execute: async () => {
                try {
                    const result = await executeFunction();
                    resolve(result);
                } catch (error) {
                    reject(error);
                }
            }
        };

        commandQueue.push(command);
        console.log(`تمت إضافة أمر إلى الطابور: ${commandType}. طول الطابور: ${commandQueue.length}`);

        // بدء معالجة الطابور إذا لم تكن جارية بالفعل
        if (!isProcessingQueue) {
            processQueue();
        }
    });
}

// دالة لبدء المتصفح والصفحة الرئيسية إذا لم يكن قد بدأ بالفعل
async function startBrowser() {
    if (!browser) {
        // إنشاء مجلد جلسة WhatsApp إذا لم يكن موجودًا
        if (!fs.existsSync(userDataDir)) {
            fs.mkdirSync(userDataDir, { recursive: true });
        }

        // تحديد User-Agent لتحسين التوافق مع WhatsApp Web
        const userAgent = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36';

        browser = await puppeteer.launch({
            headless: false,
            args: [
                '--no-sandbox',
                '--disable-setuid-sandbox',
                `--user-agent=${userAgent}`,
                '--disable-blink-features=AutomationControlled',
                '--window-size=1280,800'
            ],
            userDataDir: userDataDir,
            defaultViewport: null,
            ignoreDefaultArgs: ['--enable-automation']
        });

        // الإصدار الجديد من Puppeteer يستخدم متصفح Chrome المضمن بشكل افتراضي
        // هذا يساعد على تحميل ملفات CSS وJavaScript بشكل صحيح

        // إنشاء الصفحة الرئيسية
        // استخدام الصفحة الافتراضية بدلاً من إنشاء صفحة جديدة
        const pages = await browser.pages();
        mainPage = pages[0]; // استخدام الصفحة الافتراضية التي يفتحها المتصفح

        // تعيين User-Agent للصفحة
        await mainPage.setUserAgent(userAgent);

        // إعدادات لتحسين الأداء
        await mainPage.setDefaultNavigationTimeout(120000); // زيادة مهلة التصفح
        await mainPage.setViewport({ width: 1280, height: 800 });

        // إخفاء رسالة التحكم الآلي
        await mainPage.evaluateOnNewDocument(() => {
            // إعادة تعريف خاصية navigator.webdriver لإخفاء رسالة التحكم الآلي
            Object.defineProperty(navigator, 'webdriver', {
                get: () => false
            });

            // إزالة السمات التي تشير إلى التحكم الآلي
            delete window.cdc_adoQpoasnfa76pfcZLmcfl_Array;
            delete window.cdc_adoQpoasnfa76pfcZLmcfl_Promise;
            delete window.cdc_adoQpoasnfa76pfcZLmcfl_Symbol;
        });

        // فتح WhatsApp Web في الصفحة الرئيسية
        try {
            // فتح WhatsApp Web مع خيارات محسنة
            await mainPage.goto('https://web.whatsapp.com', {
                waitUntil: 'networkidle0', // انتظار حتى تكتمل جميع الطلبات الشبكية
                timeout: 120000 // زيادة المهلة للتأكد من تحميل جميع الموارد
            });

            // تنظيف الذاكرة بعد التحميل
            if (global.gc) {
                global.gc();
            }

            console.log('تم فتح WhatsApp Web في الصفحة الرئيسية');
        } catch (error) {
            console.error('خطأ في فتح WhatsApp Web:', error);
            // محاولة فتح الصفحة بطريقة أبسط في حالة الفشل
            await mainPage.goto('https://web.whatsapp.com', {
                waitUntil: 'load', // انتظار حتى يكتمل حدث load
                timeout: 120000
            });
            console.log('تم فتح WhatsApp Web بطريقة بديلة');
        }
    }

    return { browser, mainPage };
}

// فتح WhatsApp Web يدويًا
app.get('/open', async (_, res) => {
    try {
        console.log('جاري فتح WhatsApp Web يدويًا...');

        // إضافة أمر فتح WhatsApp Web إلى الطابور
        await addToQueue('open_whatsapp', async () => {
            await openWhatsAppWeb();
            return true;
        });

        res.json({ status: 'success', message: 'تم فتح WhatsApp Web بنجاح' });
    } catch (error) {
        console.error('خطأ في فتح WhatsApp Web:', error);
        res.json({ status: 'error', message: error.message });
    }
});

// التحقق من حالة تسجيل الدخول
app.get('/status', async (_, res) => {
    try {
        // إضافة أمر التحقق من حالة تسجيل الدخول إلى الطابور
        const isLoggedIn = await addToQueue('check_status', async () => {
            const { mainPage } = await startBrowser();

            // التحقق من وجود عنصر يظهر فقط بعد تسجيل الدخول
            return await mainPage.evaluate(() => {
                return document.querySelector('div[data-testid="chat-list"]') !== null;
            });
        });

        res.json({ status: 'success', isLoggedIn });
    } catch (error) {
        console.error('خطأ في التحقق من حالة تسجيل الدخول:', error);
        res.json({ status: 'error', message: error.message });
    }
});

// إرسال رسالة WhatsApp
app.post('/send', async (req, res) => {
    try {
        const { phone, message } = req.body;

        if (!phone || !message) {
            return res.json({ status: 'error', message: 'يجب توفير رقم الهاتف والرسالة' });
        }

        console.log(`جاري إرسال رسالة إلى ${phone}...`);

        // إضافة أمر إرسال الرسالة إلى الطابور
        await addToQueue('send_message', async () => {
            const { mainPage } = await startBrowser();

            try {
                // فتح رابط WhatsApp مع رقم الهاتف والرسالة
                const url = `https://web.whatsapp.com/send?phone=${phone}&text=${encodeURIComponent(message)}`;
                console.log(`جاري فتح الرابط: ${url}`);

                // فتح الرابط بشكل مباشر وانتظار حتى يكتمل تحميل الصفحة
                await mainPage.goto(url, { waitUntil: 'networkidle2', timeout: 60000 });

                // انتظار للتأكد من تحميل الصفحة
                // استخدام setTimeout بدلاً من waitForTimeout للتوافق مع الإصدار الجديد من Puppeteer
                await new Promise(resolve => setTimeout(resolve, 5000));

                console.log('تم تحميل الصفحة بنجاح');

                // التحقق مما إذا كان المستخدم بحاجة إلى تسجيل الدخول
                const needsLogin = await mainPage.evaluate(() => {
                    return document.querySelector('div[data-ref]') !== null ||
                           document.querySelector('div[data-testid="qr-code-canvas"]') !== null;
                });

                if (needsLogin) {
                    console.log('يجب تسجيل الدخول إلى WhatsApp Web أولاً');
                    throw new Error('يرجى تسجيل الدخول إلى WhatsApp Web أولاً');
                }

                // انتظار قليلاً للتأكد من تحميل الصفحة بشكل كامل
                await new Promise(resolve => setTimeout(resolve, 2000));

                console.log('جاري انتظار ظهور زر الإرسال...');

                // محاولة العثور على زر الإرسال بعدة طرق
                try {
                    // محاولة العثور على زر الإرسال باستخدام المحدد data-icon="send"
                    await mainPage.waitForSelector('span[data-icon="send"]', { timeout: 20000 });
                    console.log('تم العثور على زر الإرسال');

                    // انتظار قليلاً للتأكد من أن الزر قابل للنقر
                    await new Promise(resolve => setTimeout(resolve, 1000));

                    // النقر على زر الإرسال
                    await mainPage.click('span[data-icon="send"]');
                } catch (selectorError) {
                    console.log('لم يتم العثور على زر الإرسال بالطريقة الأولى، جاري المحاولة بطريقة أخرى...');

                    // محاولة بديلة باستخدام الضغط على Enter
                    await mainPage.keyboard.press('Enter');
                    console.log('تم الضغط على مفتاح Enter لإرسال الرسالة');
                }

                // انتظار قليلاً للتأكد من إرسال الرسالة
                console.log('جاري الانتظار للتأكد من إرسال الرسالة...');
                await new Promise(resolve => setTimeout(resolve, 2000));

                // تنظيف الذاكرة بعد الإرسال
                if (global.gc) {
                    global.gc();
                }

                console.log('تم إرسال الرسالة بنجاح');
                return true;
            } catch (error) {
                console.error('خطأ في إرسال الرسالة:', error);

                // محاولة تنظيف الذاكرة في حالة الخطأ
                if (global.gc) {
                    global.gc();
                }

                throw error;
            }
        });

        res.json({ status: 'success', message: 'تم إرسال الرسالة بنجاح' });
    } catch (error) {
        console.error('خطأ في إرسال الرسالة:', error);
        res.json({ status: 'error', message: `خطأ في إرسال الرسالة: ${error.message}` });
    }
});

// إعادة تعيين الجلسة
app.post('/reset', async (_, res) => {
    try {
        console.log('جاري إعادة تعيين جلسة WhatsApp...');

        // إضافة أمر إعادة تعيين الجلسة إلى الطابور
        // هذا الأمر سيقوم بإلغاء جميع الأوامر الأخرى في الطابور
        commandQueue = []; // إلغاء جميع الأوامر المعلقة
        isProcessingQueue = false; // إيقاف معالجة الطابور الحالية

        await addToQueue('reset_session', async () => {
            // إغلاق المتصفح إذا كان مفتوحًا
            if (browser) {
                try {
                    // إغلاق جميع الصفحات المفتوحة
                    const pages = await browser.pages();
                    for (const page of pages) {
                        try {
                            await page.close();
                        } catch (pageError) {
                            console.error('خطأ في إغلاق الصفحة:', pageError);
                        }
                    }

                    // إغلاق المتصفح
                    await browser.close();
                    console.log('تم إغلاق المتصفح بنجاح');
                } catch (browserError) {
                    console.error('خطأ في إغلاق المتصفح:', browserError);
                }
                browser = null;
                mainPage = null;
            }

            // حذف مجلد الجلسة وإعادة إنشائه
            console.log('جاري حذف مجلد الجلسة...');
            if (fs.existsSync(userDataDir)) {
                try {
                    fs.rmdirSync(userDataDir, { recursive: true });
                    console.log('تم حذف مجلد الجلسة بنجاح');
                } catch (rmError) {
                    console.error('خطأ في حذف مجلد الجلسة:', rmError);
                }
            }

            try {
                fs.mkdirSync(userDataDir, { recursive: true });
                console.log('تم إنشاء مجلد جلسة جديد');
            } catch (mkdirError) {
                console.error('خطأ في إنشاء مجلد الجلسة:', mkdirError);
            }

            // فتح WhatsApp Web مرة أخرى
            await openWhatsAppWeb();

            return true;
        });

        console.log('تم إعادة تعيين جلسة WhatsApp بنجاح');
        res.json({ status: 'success', message: 'تم إعادة تعيين جلسة WhatsApp بنجاح' });
    } catch (error) {
        console.error('خطأ في إعادة تعيين الجلسة:', error);
        res.json({ status: 'error', message: `خطأ في إعادة تعيين الجلسة: ${error.message}` });
    }
});

// دالة لفتح WhatsApp Web عند بدء الخادم
async function openWhatsAppWeb() {
    try {
        console.log('جاري فتح WhatsApp Web...');

        // استخدام الصفحة الرئيسية التي تم إنشاؤها بالفعل
        const { mainPage } = await startBrowser();

        // فتح WhatsApp Web بشكل مباشر وانتظار حتى يكتمل تحميل الصفحة
        await mainPage.goto('https://web.whatsapp.com', { waitUntil: 'networkidle2', timeout: 60000 });

        // انتظار للتأكد من تحميل الصفحة
        // استخدام setTimeout بدلاً من waitForTimeout للتوافق مع الإصدار الجديد من Puppeteer
        await new Promise(resolve => setTimeout(resolve, 5000));

        // تنظيف الذاكرة بعد التحميل
        if (global.gc) {
            global.gc();
        }

        console.log('تم فتح WhatsApp Web بنجاح. يرجى مسح رمز QR باستخدام تطبيق WhatsApp على هاتفك.');

        return mainPage;
    } catch (error) {
        console.error('خطأ في فتح WhatsApp Web:', error);
        throw error;
    }
}

// إضافة جامع القمامة لتنظيف الذاكرة بشكل دوري
let gcInterval;

// دالة لتنظيف الذاكرة بشكل دوري
function startGarbageCollection() {
    // تنظيف الذاكرة كل 5 دقائق
    gcInterval = setInterval(() => {
        try {
            if (global.gc) {
                console.log('جاري تنظيف الذاكرة...');
                global.gc();
                console.log('تم تنظيف الذاكرة بنجاح');
            }
        } catch (error) {
            console.error('خطأ في تنظيف الذاكرة:', error);
        }
    }, 5 * 60 * 1000); // 5 دقائق
}

// بدء الخادم
app.listen(port, () => {
    console.log(`WhatsApp Local Server running at http://localhost:${port}`);
    console.log('Press Ctrl+C to stop the server');

    // بدء جامع القمامة
    startGarbageCollection();

    // فتح WhatsApp Web تلقائيًا عند بدء الخادم
    addToQueue('initial_open', async () => {
        await openWhatsAppWeb();
        return true;
    });

    // بدء معالجة الطابور
    processQueue();

    // إيقاف جامع القمامة عند إيقاف الخادم
    process.on('SIGINT', () => {
        console.log('جاري إيقاف الخادم...');
        if (gcInterval) {
            clearInterval(gcInterval);
        }
        process.exit(0);
    });
});
