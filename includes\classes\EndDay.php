<?php
/**
 * نموذج نهاية اليوم
 *
 * يتعامل مع عمليات بدء وإغلاق يوم العمل
 */

class EndDay {
    private $db;

    /**
     * إنشاء كائن جديد
     * @param Database $db كائن قاعدة البيانات
     */
    public function __construct($db) {
        $this->db = $db;
    }

    /**
     * الحصول على سجل نهاية اليوم الحالي للفرع
     *
     * @param int $branchId معرف الفرع
     * @param string $date التاريخ (اختياري، افتراضي اليوم الحالي)
     * @return array|false بيانات نهاية اليوم أو false إذا لم يتم العثور عليها
     */
    public function getCurrentEndDay($branchId, $date = null) {
        // التحقق من صحة معرف الفرع
        if (!$branchId || $branchId <= 0) {
            return false;
        }

        if ($date === null) {
            $date = date('Y-m-d');
        }

        try {
            // البحث عن أي يوم عمل مفتوح للفرع
            $this->db->prepare("SELECT ed.id, ed.date, ed.created_at, ed.closed_at,
                              ed.total_sales, ed.total_expenses, ed.cash_amount, ed.card_amount, ed.other_amount,
                              (SELECT SUM(i.final_amount) FROM invoices i WHERE i.branch_id = ed.branch_id AND DATE(i.created_at) = SUBSTRING_INDEX(ed.date, ' (', 1) AND i.payment_status = 'paid') as current_sales,
                              (SELECT SUM(e.amount) FROM expenses e WHERE e.branch_id = ed.branch_id AND DATE(e.date) = SUBSTRING_INDEX(ed.date, ' (', 1)) as current_expenses,
                              (SELECT SUM(i.final_amount) FROM invoices i WHERE i.branch_id = ed.branch_id AND DATE(i.created_at) = SUBSTRING_INDEX(ed.date, ' (', 1) AND i.payment_method = 'cash' AND i.payment_status = 'paid') as current_cash,
                              (SELECT SUM(i.final_amount) FROM invoices i WHERE i.branch_id = ed.branch_id AND DATE(i.created_at) = SUBSTRING_INDEX(ed.date, ' (', 1) AND i.payment_method = 'card' AND i.payment_status = 'paid') as current_card,
                              (SELECT SUM(i.final_amount) FROM invoices i WHERE i.branch_id = ed.branch_id AND DATE(i.created_at) = SUBSTRING_INDEX(ed.date, ' (', 1) AND i.payment_method NOT IN ('cash', 'card') AND i.payment_status = 'paid') as current_other
                              FROM end_days ed
                              WHERE ed.branch_id = :branch_id
                              AND closed_at IS NULL");
            $this->db->bind(':branch_id', $branchId);
            $openEndDay = $this->db->fetch();

            if ($openEndDay) {
                return $openEndDay;
            }

            // إذا لم يكن هناك يوم مفتوح، نبحث عن سجل للتاريخ المحدد
            $this->db->prepare("SELECT ed.id, ed.date, ed.created_at, ed.closed_at,
                              ed.total_sales, ed.total_expenses, ed.cash_amount, ed.card_amount, ed.other_amount,
                              (SELECT SUM(i.final_amount) FROM invoices i WHERE i.branch_id = ed.branch_id AND DATE(i.created_at) = SUBSTRING_INDEX(ed.date, ' (', 1) AND i.payment_status = 'paid') as current_sales,
                              (SELECT SUM(e.amount) FROM expenses e WHERE e.branch_id = ed.branch_id AND DATE(e.date) = SUBSTRING_INDEX(ed.date, ' (', 1)) as current_expenses,
                              (SELECT SUM(i.final_amount) FROM invoices i WHERE i.branch_id = ed.branch_id AND DATE(i.created_at) = SUBSTRING_INDEX(ed.date, ' (', 1) AND i.payment_method = 'cash' AND i.payment_status = 'paid') as current_cash,
                              (SELECT SUM(i.final_amount) FROM invoices i WHERE i.branch_id = ed.branch_id AND DATE(i.created_at) = SUBSTRING_INDEX(ed.date, ' (', 1) AND i.payment_method = 'card' AND i.payment_status = 'paid') as current_card,
                              (SELECT SUM(i.final_amount) FROM invoices i WHERE i.branch_id = ed.branch_id AND DATE(i.created_at) = SUBSTRING_INDEX(ed.date, ' (', 1) AND i.payment_method NOT IN ('cash', 'card') AND i.payment_status = 'paid') as current_other
                              FROM end_days ed
                              WHERE ed.branch_id = :branch_id
                              AND (ed.date = :date OR ed.date LIKE CONCAT(:date, ' (%)'))
                              ORDER BY ed.id DESC
                              LIMIT 1");
            $this->db->bind(':branch_id', $branchId);
            $this->db->bind(':date', $date);

            return $this->db->fetch();
        } catch (Exception $e) {
            error_log('Error in getCurrentEndDay: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * بدء يوم عمل جديد للفرع
     *
     * @param int $branchId معرف الفرع
     * @param int $userId معرف المستخدم الذي بدأ اليوم
     * @param string|null $date التاريخ (اختياري، افتراضي اليوم الحالي)
     * @return int|false معرف سجل نهاية اليوم الجديد أو false إذا فشلت العملية
     */
    public function startEndDay($branchId, $userId, $date = null) {
        try {
            // استخدام التاريخ الحالي إذا لم يتم تحديد تاريخ
            if ($date === null) {
                $date = date('Y-m-d');
            }

            // التحقق من عدم وجود يوم مفتوح بالفعل للفرع (بغض النظر عن التاريخ)
            $this->db->prepare("SELECT id FROM end_days
                              WHERE branch_id = :branch_id
                              AND closed_at IS NULL");
            $this->db->bind(':branch_id', $branchId);
            $existingEndDay = $this->db->fetch();

            if ($existingEndDay) {
                throw new Exception('يوجد بالفعل يوم عمل مفتوح لهذا الفرع. يجب إغلاق اليوم الحالي قبل بدء يوم جديد');
            }

            // التحقق من وجود سجل لنفس التاريخ والفرع
            $this->db->prepare("SELECT id, closed_at FROM end_days
                              WHERE branch_id = :branch_id
                              AND date = :date");
            $this->db->bind(':branch_id', $branchId);
            $this->db->bind(':date', $date);
            $existingDateRecord = $this->db->fetch();

            // إذا كان هناك سجل لنفس التاريخ والفرع، نتحقق من أنه مغلق
            if ($existingDateRecord && $existingDateRecord['closed_at'] === null) {
                throw new Exception('يوجد بالفعل يوم عمل مفتوح لهذا التاريخ والفرع');
            }

            // لا يمكن فتح يوم عمل جديد في نفس التاريخ لنفس الفرع
            if ($existingDateRecord && $existingDateRecord['closed_at'] !== null) {
                throw new Exception('لا يمكن فتح يوم عمل جديد في نفس التاريخ. يرجى اختيار تاريخ آخر لبدء يوم عمل جديد.');
            }

            // إنشاء سجل جديد إذا لم يكن هناك سجل لنفس التاريخ
            $this->db->prepare("INSERT INTO end_days (
                              branch_id, date, created_at, closed_by
                          ) VALUES (
                              :branch_id, :date, :created_at, :closed_by
                          )");
            $this->db->bind(':branch_id', $branchId);
            $this->db->bind(':date', $date);
            $this->db->bind(':created_at', date('Y-m-d H:i:s'));
            $this->db->bind(':closed_by', $userId);

            try {
                if ($this->db->execute()) {
                    return (int)$this->db->lastInsertId();
                }
                return false;
            } catch (PDOException $e) {
                // التحقق من أن الخطأ هو انتهاك قيد التفرد
                if ($e->getCode() == 23000 && strpos($e->getMessage(), 'Duplicate entry') !== false && strpos($e->getMessage(), 'branch_date') !== false) {
                    throw new Exception('لا يمكن فتح يوم عمل جديد في نفس التاريخ. يرجى اختيار تاريخ آخر لبدء يوم عمل جديد.');
                }
                throw $e;
            }
        } catch (Exception $e) {
            error_log('خطأ أثناء بدء يوم العمل: ' . $e->getMessage());
            throw $e;
        } catch (PDOException $e) {
            // التحقق من أن الخطأ هو انتهاك قيد التفرد
            if ($e->getCode() == 23000 && strpos($e->getMessage(), 'Duplicate entry') !== false && strpos($e->getMessage(), 'branch_date') !== false) {
                error_log('خطأ قيد التفرد أثناء بدء يوم العمل: ' . $e->getMessage());
                throw new Exception('لا يمكن فتح يوم عمل جديد في نفس التاريخ. يرجى اختيار تاريخ آخر لبدء يوم عمل جديد.');
            }
            error_log('خطأ قاعدة البيانات أثناء بدء يوم العمل: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * إغلاق يوم العمل
     *
     * @param int $endDayId معرف سجل نهاية اليوم
     * @param string $notes ملاحظات (اختياري)
     * @param int $userId معرف المستخدم الذي أغلق اليوم
     * @return bool نجاح أو فشل العملية
     */
    public function closeEndDay($endDayId, $notes, $userId) {
        try {
            // التحقق من وجود السجل وأنه غير مغلق
            $this->db->prepare("SELECT id, branch_id FROM end_days
                              WHERE id = :id
                              AND closed_at IS NULL");
            $this->db->bind(':id', $endDayId);
            $endDay = $this->db->fetch();

            if (!$endDay) {
                throw new Exception('لم يتم العثور على سجل نهاية اليوم أو تم إغلاقه بالفعل');
            }

            $branchId = $endDay['branch_id'];

            // الحصول على تاريخ اليوم الذي يتم إغلاقه
            $this->db->prepare("SELECT date FROM end_days WHERE id = :id");
            $this->db->bind(':id', $endDayId);
            $endDayDate = $this->db->fetch();
            $date = $endDayDate['date'];

            // تسجيل التاريخ للتحقق
            error_log('End day date: ' . $date);
            error_log('End day date id: ' . $endDayId);
            error_log('End day date branch id: ' . $branchId);

            // التحقق من التاريخ واستخراج التاريخ الأساسي إذا كان يحتوي على رقم تسلسلي
            $baseDate = $date;
            if (strpos($date, ' (') !== false) {
                $baseDate = substr($date, 0, strpos($date, ' ('));
            }

            // حساب المبيعات حسب طريقة الدفع (فقط الفواتير المدفوعة)
            $this->db->prepare("SELECT payment_method, SUM(final_amount) as total
                              FROM invoices
                              WHERE branch_id = :branch_id
                              AND end_day_id = :endDayId
                              AND payment_status = 'paid'
                              GROUP BY payment_method");
            $this->db->bind(':branch_id', $branchId);
            $this->db->bind(':endDayId', $endDayId);
            $paymentMethodTotals = $this->db->fetchAll();

            // تسجيل الاستعلام للتحقق
            error_log('SQL for payment methods: ' . $this->db->getLastQuery());

            // تحديد المبالغ حسب طريقة الدفع
            $cashAmount = 0;
            $cardAmount = 0;
            $otherAmount = 0;

            foreach ($paymentMethodTotals as $method) {
                switch ($method['payment_method']) {
                    case 'cash':
                        $cashAmount = $method['total'];
                        break;
                    case 'card':
                        $cardAmount = $method['total'];
                        break;
                    default:
                        $otherAmount += $method['total'];
                }
            }

            // حساب إجمالي المبيعات (فقط الفواتير المدفوعة)
            $this->db->prepare("SELECT SUM(final_amount) as total_sales
                              FROM invoices
                              WHERE branch_id = :branch_id
                              AND end_day_id = :endDayId
                              AND payment_status = 'paid'");
            $this->db->bind(':branch_id', $branchId);
            $this->db->bind(':endDayId', $endDayId);
            $salesResult = $this->db->fetch();
            $totalSales = $salesResult ? $salesResult['total_sales'] : 0;

            // تسجيل إجمالي المبيعات للتحقق
            error_log('Total sales: ' . $totalSales);

            // حساب إجمالي المصروفات
            $this->db->prepare("SELECT SUM(amount) as total_expenses
                              FROM expenses
                              WHERE branch_id = :branch_id
                              AND end_day_id = :endDayId");
            $this->db->bind(':branch_id', $branchId);
            $this->db->bind(':endDayId', $endDayId);
            $expensesResult = $this->db->fetch();
            $totalExpenses = $expensesResult ? $expensesResult['total_expenses'] : 0;

            // تسجيل إجمالي المصروفات للتحقق
            error_log('Total expenses: ' . $totalExpenses);

            // حساب إجمالي الخصومات (فقط الفواتير المدفوعة)
            $this->db->prepare("SELECT SUM(discount_amount) as total_discounts
                              FROM invoices
                              WHERE branch_id = :branch_id
                              AND end_day_id = :endDayId
                              AND payment_status = 'paid'");
            $this->db->bind(':branch_id', $branchId);
            $this->db->bind(':endDayId', $endDayId);
            $discountsResult = $this->db->fetch();
            $totalDiscounts = $discountsResult ? $discountsResult['total_discounts'] : 0;

            // تسجيل إجمالي الخصومات للتحقق
            error_log('Total discounts: ' . $totalDiscounts);

            $currentDateTime = date('Y-m-d H:i:s'); // الحصول على التاريخ والوقت الحالي من PHP

            $this->db->prepare("UPDATE end_days
                              SET closed_at = :closed_at,
                                  notes = :notes,
                                  total_sales = :total_sales,
                                  total_expenses = :total_expenses,
                                  total_discounts = :total_discounts,
                                  cash_amount = :cash_amount,
                                  card_amount = :card_amount,
                                  other_amount = :other_amount,
                                  closed_by = :closed_by
                              WHERE id = :id");
            $this->db->bind(':id', $endDayId);
            $this->db->bind(':closed_at', $currentDateTime);
            $this->db->bind(':notes', $notes);
            $this->db->bind(':total_sales', $totalSales);
            $this->db->bind(':total_expenses', $totalExpenses);
            $this->db->bind(':total_discounts', $totalDiscounts);
            $this->db->bind(':cash_amount', $cashAmount);
            $this->db->bind(':card_amount', $cardAmount);
            $this->db->bind(':other_amount', $otherAmount);
            $this->db->bind(':closed_by', $userId);

            return $this->db->execute();
        } catch (Exception $e) {
            error_log('خطأ أثناء إغلاق يوم العمل: ' . $e->getMessage());
            throw $e;
        }
    }
    /**
     * الحصول على سجل نهاية اليوم المفتوح للفرع المحدد
     *
     * @param int $branchId معرف الفرع
     * @return array|false سجل نهاية اليوم المفتوح أو false إذا لم يوجد
     */
    public function getOpenEndDay($branchId) {
        try {
            // التأكد من وجود مجلد السجلات
            $logsDir = BASEPATH . '/logs';
            if (!is_dir($logsDir)) {
                // إنشاء المجلد إذا لم يكن موجودًا
                mkdir($logsDir, 0755, true);
            }

            $logFile = $logsDir . '/end_day_debug_' . date('Y-m-d') . '.log';

            // محاولة كتابة السجل مع التحقق من الأخطاء
            if (is_writable($logsDir) || (file_exists($logFile) && is_writable($logFile))) {
                file_put_contents(
                    $logFile,
                    date('Y-m-d H:i:s') . " - بدء تنفيذ getOpenEndDay للفرع: " . $branchId . "\n",
                    FILE_APPEND
                );
            }

            // الحصول على سجل نهاية اليوم المفتوح للفرع
            $this->db->prepare("SELECT ed.*,
                              b.name as branch_name,
                              u.name as closed_by_name
                       FROM end_days ed
                       LEFT JOIN branches b ON ed.branch_id = b.id
                       LEFT JOIN users u ON ed.closed_by = u.id
                       WHERE ed.branch_id = :branch_id
                       AND ed.closed_at IS NULL
                       ORDER BY ed.created_at DESC
                       LIMIT 1");
            $this->db->bind(':branch_id', $branchId);
            $endDay = $this->db->fetch();

            if (is_writable($logsDir) || (file_exists($logFile) && is_writable($logFile))) {
                file_put_contents(
                    $logFile,
                    date('Y-m-d H:i:s') . " - استعلام اليوم المفتوح: " . $this->db->getLastQuery() . "\n",
                    FILE_APPEND
                );
            }

            if (!$endDay) {
                if (is_writable($logsDir) || (file_exists($logFile) && is_writable($logFile))) {
                    file_put_contents(
                        $logFile,
                        date('Y-m-d H:i:s') . " - لم يتم العثور على يوم مفتوح للفرع: " . $branchId . "\n",
                        FILE_APPEND
                    );
                }
                return false;
            }

            if (is_writable($logsDir) || (file_exists($logFile) && is_writable($logFile))) {
                file_put_contents(
                    $logFile,
                    date('Y-m-d H:i:s') . " - تم العثور على يوم مفتوح برقم: " . $endDay['id'] . "\n",
                    FILE_APPEND
                );
            }

            $endDayId = $endDay['id'];
            $date = $endDay['date'];

            // حساب إجمالي المبيعات للفواتير المرتبطة بهذا اليوم المفتوح
            $this->db->prepare("SELECT SUM(final_amount) as total_sales
                              FROM invoices
                              WHERE end_day_id = :end_day_id
                              AND payment_status = 'paid'");
            $this->db->bind(':end_day_id', $endDayId);
            $salesResult = $this->db->fetch();
            $endDay['total_sales'] = $salesResult && $salesResult['total_sales'] ? $salesResult['total_sales'] : 0;

            if (is_writable($logsDir) || (file_exists($logFile) && is_writable($logFile))) {
                file_put_contents(
                    $logFile,
                    date('Y-m-d H:i:s') . " - استعلام المبيعات لليوم المفتوح: " . $this->db->getLastQuery() . "\n",
                    FILE_APPEND
                );
            }
            if (is_writable($logsDir) || (file_exists($logFile) && is_writable($logFile))) {
                file_put_contents(
                    $logFile,
                    date('Y-m-d H:i:s') . " - إجمالي المبيعات لليوم المفتوح: " . $endDay['total_sales'] . "\n",
                    FILE_APPEND
                );
            }

            // حساب المبيعات حسب طريقة الدفع
            $this->db->prepare("SELECT payment_method, SUM(final_amount) as total
                              FROM invoices
                              WHERE end_day_id = :end_day_id
                              AND payment_status = 'paid'
                              GROUP BY payment_method");
            $this->db->bind(':end_day_id', $endDayId);
            $paymentMethodTotals = $this->db->fetchAll();

            if (is_writable($logsDir) || (file_exists($logFile) && is_writable($logFile))) {
                file_put_contents(
                    $logFile,
                    date('Y-m-d H:i:s') . " - استعلام طرق الدفع لليوم المفتوح: " . $this->db->getLastQuery() . "\n",
                    FILE_APPEND
                );
            }

            // تحديد المبالغ حسب طريقة الدفع
            $endDay['cash_amount'] = 0;
            $endDay['card_amount'] = 0;
            $endDay['other_amount'] = 0;

            foreach ($paymentMethodTotals as $method) {
                if (is_writable($logsDir) || (file_exists($logFile) && is_writable($logFile))) {
                    file_put_contents(
                        $logFile,
                        date('Y-m-d H:i:s') . " - طريقة الدفع: " . $method['payment_method'] . ", المبلغ: " . $method['total'] . "\n",
                        FILE_APPEND
                    );
                }

                switch ($method['payment_method']) {
                    case 'cash':
                        $endDay['cash_amount'] = $method['total'];
                        break;
                    case 'card':
                        $endDay['card_amount'] = $method['total'];
                        break;
                    default:
                        $endDay['other_amount'] += $method['total'];
                }
            }

            // حساب إجمالي المصروفات
            $this->db->prepare("SELECT SUM(amount) as total_expenses
                              FROM expenses
                              WHERE end_day_id = :end_day_id");
            $this->db->bind(':end_day_id', $endDayId);
            $expensesResult = $this->db->fetch();
            $endDay['total_expenses'] = $expensesResult && $expensesResult['total_expenses'] ? $expensesResult['total_expenses'] : 0;

            file_put_contents(
                $logFile,
                date('Y-m-d H:i:s') . " - استعلام المصروفات لليوم المفتوح: " . $this->db->getLastQuery() . "\n",
                FILE_APPEND
            );
            file_put_contents(
                $logFile,
                date('Y-m-d H:i:s') . " - إجمالي المصروفات لليوم المفتوح: " . $endDay['total_expenses'] . "\n",
                FILE_APPEND
            );

            // حساب إجمالي الخصومات
            $this->db->prepare("SELECT SUM(discount_amount) as total_discounts
                              FROM invoices
                              WHERE end_day_id = :end_day_id
                              AND payment_status = 'paid'");
            $this->db->bind(':end_day_id', $endDayId);
            $discountsResult = $this->db->fetch();
            $endDay['total_discounts'] = $discountsResult && $discountsResult['total_discounts'] ? $discountsResult['total_discounts'] : 0;

            file_put_contents(
                $logFile,
                date('Y-m-d H:i:s') . " - استعلام الخصومات لليوم المفتوح: " . $this->db->getLastQuery() . "\n",
                FILE_APPEND
            );
            file_put_contents(
                $logFile,
                date('Y-m-d H:i:s') . " - إجمالي الخصومات لليوم المفتوح: " . $endDay['total_discounts'] . "\n",
                FILE_APPEND
            );

            file_put_contents(
                $logFile,
                date('Y-m-d H:i:s') . " - القيم النهائية لليوم المفتوح: نقدي=" . $endDay['cash_amount'] .
                ", بطاقة=" . $endDay['card_amount'] . ", أخرى=" . $endDay['other_amount'] .
                ", إجمالي المبيعات=" . $endDay['total_sales'] . ", إجمالي المصروفات=" . $endDay['total_expenses'] . "\n",
                FILE_APPEND
            );

            return $endDay;
        } catch (Exception $e) {
            $logFile = BASEPATH . '/logs/end_day_debug_' . date('Y-m-d') . '.log';
            file_put_contents(
                $logFile,
                date('Y-m-d H:i:s') . " - خطأ أثناء استرجاع سجل نهاية اليوم المفتوح: " . $e->getMessage() . "\n",
                FILE_APPEND
            );
            error_log('خطأ أثناء استرجاع سجل نهاية اليوم المفتوح: ' . $e->getMessage());
            throw $e;
        }
    }
    /**
     * الحصول على قائمة سجلات نهاية اليوم
     *
     * @param array $filters فلاتر البحث (اختياري)
     * @return array قائمة سجلات نهاية اليوم
     */

    public function getEndDays($filters = []) {
        try {
            // التأكد من وجود مجلد السجلات
            $logsDir = BASEPATH . '/logs';
            if (!is_dir($logsDir)) {
                // إنشاء المجلد إذا لم يكن موجودًا
                mkdir($logsDir, 0755, true);
            }

            $logFile = $logsDir . '/end_day_debug_' . date('Y-m-d') . '.log';

            // كتابة بداية التنفيذ في ملف السجل
            // محاولة كتابة السجل مع التحقق من الأخطاء
            // if (is_writable($logsDir) || is_writable($logFile)) {
            //     file_put_contents(
            //         $logFile,
            //         date('Y-m-d H:i:s') . " - بدء تنفيذ getEndDays\n",
            //         FILE_APPEND
            //     );
            // }

            $sql = "SELECT ed.*,
                          b.name as branch_name,
                          u.name as closed_by_name,
                          COALESCE(ed.total_sales, 0) as total_sales,
                          COALESCE(ed.total_expenses, 0) as total_expenses,
                          COALESCE(ed.cash_amount, 0) as cash_amount,
                          COALESCE(ed.card_amount, 0) as card_amount,
                          COALESCE(ed.other_amount, 0) as other_amount
                   FROM end_days ed
                   LEFT JOIN branches b ON ed.branch_id = b.id
                   LEFT JOIN users u ON ed.closed_by = u.id";

            $whereConditions = [];
            $bindings = [];

            // تطبيق الفلاتر
            if (!empty($filters['branch_id'])) {
                $whereConditions[] = "ed.branch_id = :branch_id";
                $bindings[':branch_id'] = $filters['branch_id'];
            }

            // إذا كان المستخدم كاشير، عرض فقط اليوم المفتوح أو اليوم الحالي
            if (!empty($filters['is_cashier']) && $filters['is_cashier'] === true) {
                // عرض فقط اليوم المفتوح أو اليوم الحالي
                $whereConditions[] = "(ed.closed_at IS NULL OR DATE(ed.date) = CURDATE())";

                // تسجيل للتشخيص
                file_put_contents(
                    $logFile,
                    date('Y-m-d H:i:s') . " - المستخدم كاشير، عرض فقط اليوم المفتوح أو اليوم الحالي\n",
                    FILE_APPEND
                );
            }

            if (!empty($filters['start_date']) && !empty($filters['end_date'])) {
                $whereConditions[] = "ed.date BETWEEN :start_date AND :end_date";
                $bindings[':start_date'] = $filters['start_date'];
                $bindings[':end_date'] = $filters['end_date'];
            }

            // إضافة شروط WHERE إذا وجدت
            if (!empty($whereConditions)) {
                $sql .= " WHERE " . implode(' AND ', $whereConditions);
            }

            // ترتيب النتائج
            $sql .= " ORDER BY ed.date DESC, ed.id DESC";

            // تطبيق الصفحات
            if (!empty($filters['limit'])) {
                $offset = !empty($filters['page']) ? ($filters['page'] - 1) * $filters['limit'] : 0;
                $sql .= " LIMIT :offset, :limit";
                $bindings[':offset'] = $offset;
                $bindings[':limit'] = $filters['limit'];
            }

            $this->db->prepare($sql);

            // ربط القيم
            foreach ($bindings as $param => $value) {
                $this->db->bind($param, $value);
            }

            $result = $this->db->fetchAll();

            // file_put_contents(
            //     $logFile,
            //     date('Y-m-d H:i:s') . " - تم استرجاع " . count($result) . " سجل نهاية يوم\n",
            //     FILE_APPEND
            // );

            // التحقق من البيانات وتحديثها إذا لزم الأمر
            foreach ($result as &$endDay) {
                $endDayId = $endDay['id'];
                $branchId = $endDay['branch_id'];

                // file_put_contents(
                //     $logFile,
                //     date('Y-m-d H:i:s') . " - معالجة سجل نهاية اليوم رقم: " . $endDayId . " للفرع: " . $branchId . "\n",
                //     FILE_APPEND
                // );

                // حساب إجمالي المبيعات (فقط الفواتير المدفوعة المرتبطة بهذا اليوم)
                $this->db->prepare("SELECT SUM(final_amount) as total_sales
                                  FROM invoices
                                  WHERE end_day_id = :end_day_id
                                  AND payment_status = 'paid'");
                $this->db->bind(':end_day_id', $endDayId);
                $salesResult = $this->db->fetch();
                $endDay['total_sales'] = $salesResult && $salesResult['total_sales'] ? $salesResult['total_sales'] : 0;

                // file_put_contents(
                //     $logFile,
                //     date('Y-m-d H:i:s') . " - استعلام إجمالي المبيعات: " . $this->db->getLastQuery() . "\n",
                //     FILE_APPEND
                // );
                // file_put_contents(
                //     $logFile,
                //     date('Y-m-d H:i:s') . " - إجمالي المبيعات لليوم " . $endDayId . ": " . $endDay['total_sales'] . "\n",
                //     FILE_APPEND
                // );

                // حساب المبيعات حسب طريقة الدفع (فقط الفواتير المدفوعة المرتبطة بهذا اليوم)
                $this->db->prepare("SELECT payment_method, SUM(final_amount) as total
                                  FROM invoices
                                  WHERE end_day_id = :end_day_id
                                  AND payment_status = 'paid'
                                  GROUP BY payment_method");
                $this->db->bind(':end_day_id', $endDayId);
                $paymentMethodTotals = $this->db->fetchAll();

                // file_put_contents(
                //     $logFile,
                //     date('Y-m-d H:i:s') . " - استعلام طرق الدفع: " . $this->db->getLastQuery() . "\n",
                //     FILE_APPEND
                // );
                // file_put_contents(
                //     $logFile,
                //     date('Y-m-d H:i:s') . " - عدد طرق الدفع: " . count($paymentMethodTotals) . "\n",
                //     FILE_APPEND
                // );

                // تحديد المبالغ حسب طريقة الدفع
                $endDay['cash_amount'] = 0;
                $endDay['card_amount'] = 0;
                $endDay['other_amount'] = 0;

                foreach ($paymentMethodTotals as $method) {
                    // file_put_contents(
                    //     $logFile,
                    //     date('Y-m-d H:i:s') . " - طريقة الدفع: " . $method['payment_method'] . ", المبلغ: " . $method['total'] . "\n",
                    //     FILE_APPEND
                    // );
                    switch ($method['payment_method']) {
                        case 'cash':
                            $endDay['cash_amount'] = $method['total'];
                            break;
                        case 'card':
                            $endDay['card_amount'] = $method['total'];
                            break;
                        default:
                            $endDay['other_amount'] += $method['total'];
                    }
                }

                // حساب إجمالي المصروفات المرتبطة بهذا اليوم
                $this->db->prepare("SELECT SUM(amount) as total_expenses
                                  FROM expenses
                                  WHERE end_day_id = :end_day_id");
                $this->db->bind(':end_day_id', $endDayId);
                $expensesResult = $this->db->fetch();
                $endDay['total_expenses'] = $expensesResult && $expensesResult['total_expenses'] ? $expensesResult['total_expenses'] : 0;

                // file_put_contents(
                //     $logFile,
                //     date('Y-m-d H:i:s') . " - استعلام المصروفات: " . $this->db->getLastQuery() . "\n",
                //     FILE_APPEND
                // );
                // file_put_contents(
                //     $logFile,
                //     date('Y-m-d H:i:s') . " - إجمالي المصروفات لليوم " . $endDayId . ": " . $endDay['total_expenses'] . "\n",
                //     FILE_APPEND
                // );

                // حساب إجمالي الخصومات المرتبطة بهذا اليوم
                $this->db->prepare("SELECT SUM(discount_amount) as total_discounts
                                  FROM invoices
                                  WHERE end_day_id = :end_day_id
                                  AND payment_status = 'paid'");
                $this->db->bind(':end_day_id', $endDayId);
                $discountsResult = $this->db->fetch();
                $endDay['total_discounts'] = $discountsResult && $discountsResult['total_discounts'] ? $discountsResult['total_discounts'] : 0;

                // تحديث السجل في قاعدة البيانات
                $this->db->prepare("UPDATE end_days
                                  SET total_sales = :total_sales,
                                      total_expenses = :total_expenses,
                                      total_discounts = :total_discounts,
                                      cash_amount = :cash_amount,
                                      card_amount = :card_amount,
                                      other_amount = :other_amount
                                  WHERE id = :id");
                $this->db->bind(':id', $endDay['id']);
                $this->db->bind(':total_sales', $endDay['total_sales']);
                $this->db->bind(':total_expenses', $endDay['total_expenses']);
                $this->db->bind(':total_discounts', $endDay['total_discounts']);
                $this->db->bind(':cash_amount', $endDay['cash_amount']);
                $this->db->bind(':card_amount', $endDay['card_amount']);
                $this->db->bind(':other_amount', $endDay['other_amount']);
                $updateResult = $this->db->execute();

                file_put_contents(
                    $logFile,
                    date('Y-m-d H:i:s') . " - نتيجة تحديث اليوم " . $endDay['id'] . ": " . ($updateResult ? "نجاح" : "فشل") . "\n",
                    FILE_APPEND
                );

                file_put_contents(
                    $logFile,
                    date('Y-m-d H:i:s') . " - القيم النهائية: نقدي=" . $endDay['cash_amount'] . ", بطاقة=" . $endDay['card_amount'] .
                    ", أخرى=" . $endDay['other_amount'] . ", إجمالي المبيعات=" . $endDay['total_sales'] .
                    ", إجمالي المصروفات=" . $endDay['total_expenses'] . "\n",
                    FILE_APPEND
                );
            }

            // محاولة كتابة السجل مع التحقق من الأخطاء
            if (is_writable($logsDir) || (file_exists($logFile) && is_writable($logFile))) {
                file_put_contents(
                    $logFile,
                    date('Y-m-d H:i:s') . " - انتهاء تنفيذ getEndDays\n",
                    FILE_APPEND
                );
            }

            return $result;
        } catch (Exception $e) {
            $logFile = BASEPATH . '/logs/end_day_debug_' . date('Y-m-d') . '.log';
            file_put_contents(
                $logFile,
                date('Y-m-d H:i:s') . " - خطأ أثناء استرجاع سجلات نهاية اليوم: " . $e->getMessage() . "\n",
                FILE_APPEND
            );
            error_log('خطأ أثناء استرجاع سجلات نهاية اليوم: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * حساب عدد سجلات نهاية اليوم
     *
     * @param array $filters فلاتر البحث (اختياري)
     * @return int عدد السجلات
     */
    public function countEndDays($filters = []) {
        try {
            $sql = "SELECT COUNT(*) FROM end_days ed";

            $whereConditions = [];
            $bindings = [];

            // تطبيق الفلاتر
            if (!empty($filters['branch_id'])) {
                $whereConditions[] = "ed.branch_id = :branch_id";
                $bindings[':branch_id'] = $filters['branch_id'];
            }

            // إذا كان المستخدم كاشير، عرض فقط اليوم المفتوح أو اليوم الحالي
            if (!empty($filters['is_cashier']) && $filters['is_cashier'] === true) {
                // عرض فقط اليوم المفتوح أو اليوم الحالي
                $whereConditions[] = "(ed.closed_at IS NULL OR DATE(ed.date) = CURDATE())";

                // تسجيل للتشخيص
                error_log('CountEndDays - المستخدم كاشير، عرض فقط اليوم المفتوح أو اليوم الحالي');
            }

            if (!empty($filters['start_date']) && !empty($filters['end_date'])) {
                $whereConditions[] = "ed.date BETWEEN :start_date AND :end_date";
                $bindings[':start_date'] = $filters['start_date'];
                $bindings[':end_date'] = $filters['end_date'];
            }

            // إضافة شروط WHERE إذا وجدت
            if (!empty($whereConditions)) {
                $sql .= " WHERE " . implode(' AND ', $whereConditions);
            }

            $this->db->prepare($sql);

            // ربط القيم
            foreach ($bindings as $param => $value) {
                $this->db->bind($param, $value);
            }

            return $this->db->fetchColumn();
        } catch (Exception $e) {
            error_log('خطأ أثناء حساب عدد سجلات نهاية اليوم: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * إعادة فتح يوم عمل مغلق
     *
     * @param int $endDayId معرف سجل نهاية اليوم
     * @param int $userId معرف المستخدم الذي يقوم بإعادة الفتح
     * @return bool نجاح أو فشل العملية
     */
    public function reopenEndDay($endDayId, $userId) {
        try {
            // التحقق من وجود السجل وأنه مغلق
            $this->db->prepare("SELECT id, branch_id, date, closed_at FROM end_days
                              WHERE id = :id
                              AND closed_at IS NOT NULL");
            $this->db->bind(':id', $endDayId);
            $endDay = $this->db->fetch();

            if (!$endDay) {
                throw new Exception('لم يتم العثور على سجل نهاية اليوم أو أنه غير مغلق');
            }

            $branchId = $endDay['branch_id'];
            $date = $endDay['date'];

            // التحقق من عدم وجود يوم عمل مفتوح بالفعل لنفس الفرع
            $this->db->prepare("SELECT id FROM end_days
                              WHERE branch_id = :branch_id
                              AND closed_at IS NULL");
            $this->db->bind(':branch_id', $branchId);
            $openEndDay = $this->db->fetch();

            if ($openEndDay) {
                throw new Exception('يوجد بالفعل يوم عمل مفتوح لهذا الفرع. يجب إغلاقه قبل إعادة فتح يوم سابق');
            }

            // التحقق من عدم وجود يوم عمل أحدث لنفس الفرع
            $this->db->prepare("SELECT id FROM end_days
                              WHERE branch_id = :branch_id
                              AND created_at > (SELECT created_at FROM end_days WHERE id = :end_day_id)
                              LIMIT 1");
            $this->db->bind(':branch_id', $branchId);
            $this->db->bind(':end_day_id', $endDayId);
            $newerEndDay = $this->db->fetch();

            if ($newerEndDay) {
                throw new Exception('لا يمكن إعادة فتح هذا اليوم لأنه يوجد يوم عمل أحدث منه');
            }

            // إعادة فتح يوم العمل بوضع closed_at كقيمة NULL
            $this->db->prepare("UPDATE end_days
                              SET closed_at = NULL,
                                  notes = CONCAT(IFNULL(notes, ''), '\n\nتم إعادة فتح هذا اليوم بواسطة المستخدم رقم ', :user_id, ' بتاريخ ', NOW(), '.')
                              WHERE id = :id");
            $this->db->bind(':id', $endDayId);
            $this->db->bind(':user_id', $userId);

            return $this->db->execute();
        } catch (Exception $e) {
            error_log('خطأ أثناء إعادة فتح يوم العمل: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * حذف يوم عمل وجميع البيانات المرتبطة به
     *
     * @param int $endDayId معرف سجل نهاية اليوم
     * @param int $userId معرف المستخدم الذي يقوم بالحذف
     * @return bool نجاح أو فشل العملية
     */
    public function deleteEndDay($endDayId, $userId) {
        try {
            // التحقق من وجود السجل
            $this->db->prepare("SELECT id, branch_id FROM end_days WHERE id = :id");
            $this->db->bind(':id', $endDayId);
            $endDay = $this->db->fetch();

            if (!$endDay) {
                throw new Exception('لم يتم العثور على سجل نهاية اليوم');
            }

            $branchId = $endDay['branch_id'];

            // التحقق من عدم وجود يوم عمل أحدث لنفس الفرع
            $this->db->prepare("SELECT id FROM end_days
                              WHERE branch_id = :branch_id
                              AND created_at > (SELECT created_at FROM end_days WHERE id = :end_day_id)
                              LIMIT 1");
            $this->db->bind(':branch_id', $branchId);
            $this->db->bind(':end_day_id', $endDayId);
            $newerEndDay = $this->db->fetch();

            if ($newerEndDay) {
                throw new Exception('لا يمكن حذف هذا اليوم لأنه يوجد يوم عمل أحدث منه. يجب حذف الأيام الأحدث أولاً');
            }

            // بدء معاملة قاعدة البيانات
            $this->db->beginTransaction();

            // 1. تحديث المصروفات المرتبطة بيوم العمل لإزالة الارتباط
            $this->db->prepare("UPDATE expenses SET end_day_id = NULL WHERE end_day_id = :end_day_id");
            $this->db->bind(':end_day_id', $endDayId);
            $this->db->execute();

            // 2. حذف الفواتير المرتبطة باليوم المحدد
            // أولاً: حذف عناصر الفواتير
            $this->db->prepare("DELETE invoice_items FROM invoice_items
                              INNER JOIN invoices ON invoice_items.invoice_id = invoices.id
                              WHERE invoices.end_day_id = :end_day_id");
            $this->db->bind(':end_day_id', $endDayId);
            $this->db->execute();

            // ثانياً: حذف الفواتير نفسها
            $this->db->prepare("DELETE FROM invoices
                              WHERE end_day_id = :end_day_id");
            $this->db->bind(':end_day_id', $endDayId);
            $this->db->execute();

            // 3. حذف سجل نهاية اليوم
            $this->db->prepare("DELETE FROM end_days WHERE id = :id");
            $this->db->bind(':id', $endDayId);
            $this->db->execute();

            // إنهاء المعاملة
            $this->db->commit();

            return true;
        } catch (Exception $e) {
            // التراجع عن المعاملة في حالة حدوث خطأ
            if ($this->db->inTransaction()) {
                $this->db->rollBack();
            }
            error_log('خطأ أثناء حذف يوم العمل: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * الحصول على سجل نهاية اليوم بواسطة المعرف
     *
     * @param int $endDayId معرف سجل نهاية اليوم
     * @return array|false بيانات نهاية اليوم أو false إذا لم يتم العثور عليها
     */
    public function getEndDayById($endDayId) {
        try {
            $this->db->prepare("SELECT ed.*, b.name as branch_name, u.name as closed_by_name
                              FROM end_days ed
                              LEFT JOIN branches b ON ed.branch_id = b.id
                              LEFT JOIN users u ON ed.closed_by = u.id
                              WHERE ed.id = :id");
            $this->db->bind(':id', $endDayId);
            return $this->db->fetch();
        } catch (Exception $e) {
            error_log('خطأ أثناء الحصول على سجل نهاية اليوم: ' . $e->getMessage());
            throw $e;
        }
    }
}

