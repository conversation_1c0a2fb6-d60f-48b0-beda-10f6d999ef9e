# نظام النسخة التجريبية - Demo Mode System

## نظرة عامة

تم تطوير نظام شامل للنسخة التجريبية يمنع المستخدمين من إجراء أي تعديلات أو حفظ للبيانات، مع عرض رسائل احترافية تشجع على شراء النسخة المدفوعة.

## المطور

**بشمهندس محمود صلاح**
- 📱 الهاتف: +201556262660
- 🌐 الموقع: hoktech.site
- 📧 البريد: <EMAIL>

---

## الملفات المحدثة

### 1. ملفات JavaScript
- `assets/js/demo-mode.js` - الملف الرئيسي لنظام النسخة التجريبية
- تم إضافة استدعاء الملف في `includes/templates/footer.php`

### 2. ملفات CSS
- تم إضافة أنماط النسخة التجريبية في `includes/templates/header.php`

### 3. صفحات إعدادات WhatsApp
- `pages/settings/whatsapp.php` - تم تعطيل جميع الوظائف
- `pages/settings/wati.php` - تم تعطيل جميع الوظائف  
- `pages/settings/whatsapp_test.php` - تم تعطيل جميع الوظائف

---

## المميزات المطبقة

### 🚫 اعتراض العمليات
- **اعتراض الأزرار**: جميع أزرار الحفظ، التعديل، الحذف، الإضافة
- **اعتراض النماذج**: منع إرسال أي نموذج
- **اعتراض AJAX**: منع طلبات POST, PUT, PATCH, DELETE
- **مراقبة DOM**: اعتراض العناصر الجديدة تلقائياً

### 🎨 العناصر البصرية
- **شريط التحذير**: في أعلى الصفحة مع إمكانية الإغلاق
- **شارة النسخة التجريبية**: في الزاوية العلوية اليمنى
- **العلامة المائية**: في خلفية الصفحة
- **تأثيرات بصرية**: أنيميشن وتأثيرات تفاعلية

### 💬 النوافذ المنبثقة
- **نافذة التقييد الرئيسية**: تظهر عند محاولة أي عملية محظورة
- **معلومات شاملة**: عن المميزات المتاحة في النسخة المدفوعة
- **أزرار التواصل**: واتساب، هاتف، بريد إلكتروني

---

## الكلمات المفتاحية المعترضة

```javascript
const restrictedButtonTexts = [
    'حفظ', 'save', 'تحديث', 'update', 'إضافة', 'add', 'تعديل', 'edit',
    'حذف', 'delete', 'إزالة', 'remove', 'تأكيد', 'confirm', 'موافق', 'ok',
    'تطبيق', 'apply', 'إرسال', 'send', 'submit', 'تنفيذ', 'execute',
    'تفعيل', 'activate', 'إيقاف', 'deactivate', 'تغيير', 'change'
];
```

## العناصر المعترضة

```javascript
const restrictedSelectors = [
    'button[type="submit"]',
    'input[type="submit"]', 
    'button.btn-primary',
    'button.btn-success',
    'button.btn-danger',
    'button.btn-warning',
    '.btn-save', '.btn-update', '.btn-delete', '.btn-add', '.btn-edit',
    '.save-btn', '.update-btn', '.delete-btn', '.add-btn', '.edit-btn'
];
```

---

## كيفية العمل

### 1. التحميل التلقائي
- يتم تحميل `demo-mode.js` في جميع صفحات النظام عبر `footer.php`
- يبدأ النظام تلقائياً عند تحميل أي صفحة

### 2. الاعتراض المتعدد المستويات
1. **اعتراض الأزرار**: بناءً على النص والـ CSS classes
2. **اعتراض النماذج**: منع إرسال أي form
3. **اعتراض AJAX**: منع طلبات التعديل عبر jQuery و fetch
4. **مراقبة DOM**: اعتراض العناصر الجديدة المضافة ديناميكياً

### 3. النافذة المنبثقة
- تظهر عند أي محاولة لإجراء عملية محظورة
- تحتوي على معلومات شاملة عن النسخة المدفوعة
- أزرار تواصل مباشرة مع المطور

---

## الرسائل والمحتوى

### رسالة التقييد الرئيسية
```
"لا يمكن إتمام عمليات الحفظ أو التعديل أو الحذف في النسخة التجريبية. 
هذه النسخة مخصصة لعرض مميزات النظام فقط."
```

### العمليات المقيدة
- ❌ حفظ البيانات الجديدة
- ❌ تعديل البيانات الموجودة  
- ❌ حذف البيانات
- ❌ تغيير الإعدادات
- ❌ إرسال الرسائل
- ❌ تصدير البيانات

### المميزات في النسخة المدفوعة
- ✅ إدارة كاملة للبيانات
- ✅ نسخ احتياطية تلقائية
- ✅ تكامل مع WhatsApp
- ✅ تقارير مفصلة
- ✅ دعم فني مباشر
- ✅ تحديثات مجانية

---

## معلومات التواصل في النظام

### المطور
- **الاسم**: بشمهندس محمود صلاح
- **الهاتف**: +201556262660
- **الموقع**: hoktech.site
- **البريد**: <EMAIL>

### أزرار التواصل
- 🟢 **واتساب**: `https://wa.me/201556262660`
- 📞 **اتصال**: `tel:+201556262660`
- 📧 **بريد**: `mailto:<EMAIL>`

---

## الاختبار

### ملف الاختبار
تم إنشاء `test-demo.html` لاختبار النظام:
- يحتوي على أزرار وأنماذج مختلفة
- يعرض جميع العناصر البصرية
- يختبر وظائف الاعتراض

### كيفية الاختبار
1. افتح `test-demo.html` في المتصفح
2. انقر على أي زر أو حاول إرسال النموذج
3. يجب أن تظهر النافذة المنبثقة
4. تحقق من وجود شريط التحذير والشارة

---

## التخصيص

### تفعيل/إلغاء النظام
```javascript
const isDemoMode = true; // غير إلى false لإلغاء النظام
```

### إضافة كلمات مفتاحية جديدة
```javascript
restrictedButtonTexts.push('كلمة_جديدة');
```

### إضافة selectors جديدة
```javascript
restrictedSelectors.push('.my-custom-button');
```

---

## الأمان

### الحماية المتعددة
- اعتراض على مستوى DOM
- اعتراض على مستوى JavaScript
- اعتراض طلبات AJAX
- مراقبة مستمرة للتغييرات

### منع التحايل
- مراقبة DOM للعناصر الجديدة
- اعتراض متعدد المستويات
- حماية من التلاعب بـ JavaScript

---

## الصيانة

### إضافة صفحات جديدة
النظام يعمل تلقائياً في جميع الصفحات التي تستدعي `footer.php`

### تحديث الرسائل
يمكن تعديل محتوى النوافذ المنبثقة في `demo-mode.js`

### إضافة مميزات جديدة
يمكن إضافة وظائف جديدة في دالة `initDemoMode()`

---

## الدعم الفني

للحصول على الدعم أو التخصيص:
- 📱 واتساب: +201556262660
- 📧 بريد: <EMAIL>
- 🌐 موقع: hoktech.site

---

**تم التطوير بواسطة: بشمهندس محمود صلاح**
**hoktech.site | +201556262660**
