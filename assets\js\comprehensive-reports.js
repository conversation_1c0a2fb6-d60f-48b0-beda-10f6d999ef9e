/**
 * ملف جافاسكريبت للتقارير الشاملة
 * يتعامل مع عرض وتحليل بيانات المبيعات والمصروفات والأرباح
 */

// التحقق من وجود BASE_URL وتعريفه إذا لم يكن موجودًا
if (typeof BASE_URL === 'undefined') {
    console.warn('BASE_URL is not defined, using fallback');
    // الحصول على المسار الأساسي من عنوان URL الحالي
    var pathParts = window.location.pathname.split('/');
    var rootFolder = '';

    // التحقق من أننا في مجلد فرعي
    if (pathParts.length > 2) {
        // الحصول على اسم المجلد الرئيسي
        rootFolder = '/' + pathParts[1] + '/';
    } else {
        rootFolder = '/';
    }

    var BASE_URL = rootFolder;
    console.log('Using BASE_URL:', BASE_URL);
}

// التحقق من وجود currencySymbol وتعريفه إذا لم يكن موجودًا
if (typeof currencySymbol === 'undefined') {
    console.warn('currencySymbol is not defined, using fallback');
    var currencySymbol = 'ر.س';
}

// تهيئة المتغيرات العامة
let reportData = {
    summary: {},
    daily_sales: [],
    payment_methods: [],
    expenses: []
};

// تهيئة المخططات
let salesExpensesChart = null;
let profitChart = null;
let paymentMethodsChart = null;
let expensesCategoriesChart = null;

/**
 * تحديد المسار الصحيح للـ API
 * @returns {string} مسار API
 */
function getApiUrl() {
    // التحقق من المسار الحالي للصفحة
    console.log('Current path:', window.location.pathname);
    console.log('Current hostname:', window.location.hostname);

    // قائمة بالمسارات المحتملة للـ API
    const possiblePaths = [
        '../../api/reports.php',
        '../api/reports.php',
        '/api/reports.php',
    ];

    // إذا كان هناك BASE_URL محدد، أضفه إلى القائمة
    if (typeof BASE_URL !== 'undefined' && BASE_URL !== '/') {
        possiblePaths.unshift(BASE_URL + 'api/reports.php');
    }

    // إذا كنا نعمل على خادم محلي
    if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
        // محاولة استخدام مسار مطلق للخادم المحلي
        const pathParts = window.location.pathname.split('/');
        // الحصول على اسم المجلد الرئيسي للمشروع
        if (pathParts.length > 1) {
            const rootFolder = pathParts[1]; // مثل 'salon_system'
            if (rootFolder) {
                possiblePaths.unshift('/' + rootFolder + '/api/reports.php');
            }
        }
    }

    console.log('Possible API paths:', possiblePaths);

    // استخدام المسار الأول كافتراضي
    return possiblePaths[0];
}

/**
 * الحصول على قائمة المسارات المحتملة للـ API
 * @returns {Array} قائمة المسارات المحتملة
 */
function getPossibleApiPaths() {
    // قائمة بالمسارات المحتملة للـ API
    const possiblePaths = [];

    // إذا كان هناك API_URL محدد من الصفحة، استخدمه أولاً
    if (typeof API_URL !== 'undefined') {
        console.log('Using predefined API_URL:', API_URL);
        possiblePaths.push(API_URL);
    }

    // الحصول على المسار الرئيسي للمشروع
    let rootPath = '';

    // إذا كان هناك BASE_URL محدد، استخدمه
    if (typeof BASE_URL !== 'undefined' && BASE_URL !== '/') {
        rootPath = BASE_URL;
    } else {
        // محاولة استخراج المسار من URL الحالي
        const pathParts = window.location.pathname.split('/');
        if (pathParts.length > 1 && pathParts[1]) {
            rootPath = '/' + pathParts[1] + '/';
        } else {
            rootPath = '/';
        }
    }

    console.log('Using root path:', rootPath);

    // إضافة المسار المطلق للـ API
    possiblePaths.push(rootPath + 'api/reports.php');

    // إضافة مسارات بديلة للتجربة
    possiblePaths.push(window.location.origin + rootPath + 'api/reports.php');
    possiblePaths.push(window.location.origin + '/system_elbadrawy/api/reports.php');
    possiblePaths.push('/system_elbadrawy/api/reports.php');

    // إضافة مسارات نسبية
    possiblePaths.push('../../api/reports.php');
    possiblePaths.push('../api/reports.php');

    // طباعة جميع المسارات المحتملة للتصحيح
    console.log('All possible API paths:', possiblePaths);

    return possiblePaths;
}

/**
 * محاولة جميع مسارات API المحتملة حتى ينجح أحدها
 * @param {Object} filters فلاتر التقرير
 * @param {Function} onSuccess دالة تنفذ عند نجاح الطلب
 * @param {Function} onError دالة تنفذ عند فشل جميع المحاولات
 */
function tryAllApiPaths(filters, onSuccess, onError) {
    const paths = getPossibleApiPaths();
    let currentPathIndex = 0;

    function tryNextPath() {
        if (currentPathIndex >= paths.length) {
            // فشلت جميع المحاولات
            onError('Failed to connect to API after trying all possible paths');
            return;
        }

        const currentPath = paths[currentPathIndex];
        console.log(`Trying API path ${currentPathIndex + 1}/${paths.length}: ${currentPath}`);

        $.ajax({
            url: currentPath,
            type: 'GET',
            data: {
                type: 'comprehensive',
                ...filters
            },
            dataType: 'json',
            success: function(response) {
                console.log(`API path ${currentPath} worked!`);
                onSuccess(response);
            },
            error: function(xhr, status, error) {
                console.log(`API path ${currentPath} failed with status: ${status}, error: ${error}`);
                console.log('Response text:', xhr.responseText);
                console.log('Status code:', xhr.status);

                // محاولة المسار التالي
                currentPathIndex++;
                tryNextPath();
            }
        });
    }

    // بدء المحاولات
    tryNextPath();
}

// تهيئة الكود عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOMContentLoaded event fired');

    // التحقق من وجود jQuery
    if (typeof jQuery === 'undefined') {
        console.error('jQuery غير متوفر. يرجى التأكد من تحميل jQuery قبل هذا الملف.');
        return;
    }
    console.log('jQuery is available');

    // التحقق من وجود AjaxHandler
    if (typeof AjaxHandler === 'undefined') {
        console.error('AjaxHandler غير متوفر. يرجى التأكد من تحميل ajax-handler.js قبل هذا الملف.');
    } else {
        console.log('AjaxHandler is available');
        // تهيئة AjaxHandler
        if (typeof AjaxHandler.init === 'function') {
            console.log('Initializing AjaxHandler');
            AjaxHandler.init();
        }
    }

    // التحقق من وجود WhatsAppDirectReportNotification وتهيئته
    if (typeof WhatsAppDirectReportNotification !== 'undefined') {
        console.log('WhatsAppDirectReportNotification is available');
        // تهيئة WhatsAppDirectReportNotification
        WhatsAppDirectReportNotification.init({
            debug: true,
            showSuccessMessage: true
        });
        console.log('WhatsAppDirectReportNotification initialized');
    } else {
        console.warn('WhatsAppDirectReportNotification غير متوفر. سيتم البحث عن بدائل أخرى.');
    }

    // التحقق من وجود WhatsAppReportNotification وتهيئته كخيار ثاني
    if (typeof WhatsAppReportNotification !== 'undefined') {
        console.log('WhatsAppReportNotification is available');
        // تهيئة WhatsAppReportNotification
        WhatsAppReportNotification.init({
            debug: true,
            showSuccessMessage: true
        });
        console.log('WhatsAppReportNotification initialized');
    } else {
        console.warn('WhatsAppReportNotification غير متوفر. سيتم استخدام الطريقة القديمة لإرسال التقارير إذا لزم الأمر.');
    }

    // تهيئة الأحداث
    initializeEvents();

    // تعيين التاريخ الافتراضي للفترة المخصصة
    setDefaultDates();

    // إضافة حدث النقر على زر عرض التقرير مباشرة
    console.log('Adding click event to generate report button');
    const generateReportBtn = document.getElementById('generate-report');
    if (generateReportBtn) {
        console.log('Generate report button found, adding click event');
        generateReportBtn.addEventListener('click', function() {
            console.log('Generate report button clicked');
            generateReport();
        });
    } else {
        console.error('Generate report button not found');
    }
});

/**
 * تهيئة أحداث الصفحة
 */
function initializeEvents() {
    // حدث تغيير نوع الفترة الزمنية
    $('#date-range').on('change', function() {
        // إظهار/إخفاء حقول التاريخ المخصص
        if ($(this).val() === 'custom') {
            $('.custom-date-range').show();
        } else {
            $('.custom-date-range').hide();
        }
    });

    // حدث النقر على زر عرض التقرير
    $('#generate-report').on('click', function() {
        generateReport();
    });

    // حدث النقر على زر تصدير إلى Excel
    $('#export-excel').on('click', function() {
        exportToExcel();
    });

    // حدث النقر على زر الطباعة
    $('#print-report').on('click', function() {
        printReport();
    });

    // حدث النقر على زر إرسال التقرير للمدراء
    $('#send-report').on('click', function() {
        sendReportToAdmins();
    });

    // حدث النقر على رابط عرض الفواتير المدفوعة جزئياً
    $('#view-partial-paid-invoices').on('click', function() {
        // الحصول على الفلاتر الحالية
        const filters = getFilters();

        // بناء رابط صفحة الفواتير مع الفلاتر
        let invoicesUrl = `${BASE_URL}pages/invoices/index.php?payment_status=partial`;

        // إضافة فلاتر التاريخ
        if (filters.start_date && filters.end_date) {
            invoicesUrl += `&start_date=${filters.start_date}&end_date=${filters.end_date}`;
        }

        // إضافة فلتر الفرع
        if (filters.branch_id) {
            invoicesUrl += `&branch_id=${filters.branch_id}`;
        }

        // فتح صفحة الفواتير في نافذة جديدة
        window.open(invoicesUrl, '_blank');
    });
}

/**
 * تعيين التاريخ الافتراضي للفترة المخصصة
 */
function setDefaultDates() {
    // تاريخ اليوم
    const today = new Date();

    // تاريخ بداية الشهر الحالي
    const firstDayOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);

    // تنسيق التواريخ بصيغة YYYY-MM-DD
    const formattedToday = today.toISOString().split('T')[0];
    const formattedFirstDay = firstDayOfMonth.toISOString().split('T')[0];

    // تعيين التواريخ في حقول الإدخال
    $('#start-date').val(formattedFirstDay);
    $('#end-date').val(formattedToday);
}

/**
 * إنشاء التقرير بناءً على الفلاتر المحددة
 */
function generateReport() {
    console.log('Generating report...');

    // إظهار مؤشر التحميل
    showLoading();

    // جمع بيانات الفلاتر
    const filters = getFilters();

    // استدعاء API للحصول على بيانات التقرير
    console.log('Making AJAX request to get report data');

    // محاولة جميع مسارات API المحتملة
    tryAllApiPaths(
        filters,
        // دالة النجاح
        function(response) {
            console.log('Received API response:', response);
            if (response.success) {
                // تخزين بيانات التقرير
                reportData = response.data;

                // تسجيل البيانات المستلمة للتصحيح
                console.log('API Response Data:', response.data);
                console.log('Summary Data:', response.data.summary);
                console.log('Total Partial Paid Invoices:', response.data.summary.total_partial_paid_invoices);

                // عرض البيانات
                displayReportData();

                // تفعيل أزرار التصدير والطباعة وإرسال التقرير
                $('#export-excel, #print-report, #send-report').prop('disabled', false);

                // إخفاء مؤشر التحميل
                hideLoading();
            } else {
                // عرض رسالة الخطأ
                showError(response.message || 'حدث خطأ أثناء جلب بيانات التقرير');
                hideLoading();
            }
        },
        // دالة الخطأ
        function(errorMessage) {
            console.error('All API paths failed:', errorMessage);
            showError('لم يتم العثور على ملف API. تمت محاولة جميع المسارات المحتملة.');
            hideLoading();
        }
    );
}

/**
 * الحصول على قيم الفلاتر المحددة
 * @returns {Object} كائن يحتوي على قيم الفلاتر
 */
function getFilters() {
    console.log('Getting filters...');

    // التحقق من وجود عناصر النموذج
    if ($('#date-range').length === 0) {
        console.error('Element #date-range not found');
    }
    if ($('#branch-id').length === 0) {
        console.error('Element #branch-id not found');
    }

    const dateRange = $('#date-range').val();
    console.log('Selected date range:', dateRange);

    let filters = {
        date_range: dateRange,
        branch_id: $('#branch-id').val() || null
    };

    // إضافة تواريخ البداية والنهاية للفترة المخصصة
    if (dateRange === 'custom') {
        filters.start_date = $('#start-date').val();
        filters.end_date = $('#end-date').val();
        console.log('Custom date range:', filters.start_date, 'to', filters.end_date);
    }

    console.log('Filters:', filters);
    return filters;
}

/**
 * عرض بيانات التقرير
 */
function displayReportData() {
    // عرض ملخص التقرير
    displaySummary();

    // عرض المخططات
    renderCharts();

    // عرض التقرير اليومي
    displayDailyReport();

    // عرض بيانات طرق الدفع
    displayPaymentMethods();

    // عرض بيانات المصروفات
    displayExpenses();

    // عرض بيانات الرواتب
    displaySalaries();

    // إظهار قسم ملخص التقرير
    $('#report-summary').show();
}

/**
 * عرض ملخص التقرير
 */
function displaySummary() {
    const summary = reportData.summary;
    console.log('Summary data in displaySummary:', summary);

    // تنسيق المبالغ بالعملة
    $('#total-sales').text(formatCurrency(summary.total_sales));
    $('#total-expenses').text(formatCurrency(summary.total_expenses));
    $('#total-salaries').text(formatCurrency(summary.total_salaries || 0));
    $('#total-profits').text(formatCurrency(summary.total_profits));

    // عرض إجمالي الخصومات
    const totalDiscounts = parseFloat(summary.total_discounts) || 0;
    console.log('Total discounts:', totalDiscounts);
    $('#total-discounts').text(formatCurrency(totalDiscounts));

    // عرض إجمالي الفواتير المدفوعة جزئياً
    console.log('Total partial paid invoices in displaySummary:', summary.total_partial_paid_invoices);
    console.log('Type of total_partial_paid_invoices:', typeof summary.total_partial_paid_invoices);

    // التأكد من أن القيمة رقمية قبل عرضها
    const partialPaidValue = parseFloat(summary.total_partial_paid_invoices) || 0;
    console.log('Parsed partial paid value:', partialPaidValue);
    $('#total-partial-paid').text(formatCurrency(partialPaidValue));

    // عرض صافي الربح أو الخسارة مع اللون المناسب
    const netProfitElement = $('#net-profit');
    netProfitElement.text(formatCurrency(summary.net_profit));

    // إضافة نسبة الربح/الخسارة
    const percentageText = summary.profit_percentage > 0 ?
        `+${summary.profit_percentage}%` :
        `${summary.profit_percentage}%`;

    // إضافة النسبة إلى النص
    netProfitElement.append(`<small class="d-block mt-1">${percentageText}</small>`);

    // تغيير لون بطاقة صافي الربح بناءً على النتيجة
    const netProfitCard = netProfitElement.closest('.card');

    if (summary.result_type === 'loss') {
        // تغيير لون البطاقة إلى الأحمر للخسارة
        netProfitCard.removeClass('bg-info').addClass('bg-danger');

        // تغيير عنوان البطاقة
        netProfitCard.find('.card-title').text('صافي الخسارة (بعد خصم الرواتب)');
    } else {
        // التأكد من أن اللون هو الأزرق للربح
        netProfitCard.removeClass('bg-danger').addClass('bg-info');

        // التأكد من أن العنوان صحيح
        netProfitCard.find('.card-title').text('صافي الربح (بعد خصم الرواتب)');
    }
}

/**
 * عرض التقرير اليومي
 */
function displayDailyReport() {
    const dailySales = reportData.daily_sales || [];
    let tableBody = '';

    // متغيرات لحساب الإجماليات
    let totalSalesSum = 0;
    let totalExpensesSum = 0;
    let totalSalariesSum = 0;
    let totalProfitSum = 0;
    let totalNetProfitSum = 0;

    // إنشاء صفوف الجدول
    dailySales.forEach(day => {
        // السماح بالقيم السالبة (الخسائر)
        const sales = parseFloat(day.total_sales);
        const expenses = parseFloat(day.total_expenses);
        const salaries = parseFloat(day.daily_salaries || 0);
        const profit = parseFloat(day.profit); // الربح قبل خصم الرواتب
        const netProfit = parseFloat(day.net_profit); // صافي الربح بعد خصم الرواتب

        // إضافة للإجماليات
        totalSalesSum += sales;
        totalExpensesSum += expenses;
        totalSalariesSum += salaries;
        totalProfitSum += profit;
        totalNetProfitSum += netProfit;

        // تحديد لون خلية الربح بناءً على القيمة
        const profitClass = profit >= 0 ? 'text-success' : 'text-danger';
        const netProfitClass = netProfit >= 0 ? 'text-success' : 'text-danger';

        // حساب نسبة الربح/الخسارة
        let profitPercentage = 0;
        if (sales > 0) {
            profitPercentage = (netProfit / sales) * 100;
        }

        // تنسيق النسبة المئوية
        const formattedPercentage = profitPercentage.toFixed(2) + '%';

        // التحقق من وجود معرف لليوم
        const dayId = day.id || day.end_day_id || '';

        // إنشاء أزرار الإجراءات
        const viewButton = dayId ? `<a href="${BASE_URL}pages/endday/view.php?id=${dayId}" class="btn btn-sm btn-info" target="_blank"><i class="fas fa-eye"></i></a>` : '';
        const printButton = dayId ? `<a href="${BASE_URL}pages/endday/print.php?id=${dayId}" class="btn btn-sm btn-secondary" target="_blank"><i class="fas fa-print"></i></a>` : '';

        tableBody += `
            <tr>
                <td>${formatDate(day.date)}</td>
                <td>${formatCurrency(sales)}</td>
                <td>${formatCurrency(expenses)}</td>
                <td>${formatCurrency(salaries)}</td>
                <td class="${profitClass}">${formatCurrency(profit)}</td>
                <td class="${netProfitClass}">
                    ${formatCurrency(netProfit)}
                    <small class="d-block">${formattedPercentage}</small>
                </td>
                <td class="text-center">
                    <div class="btn-group btn-group-sm" role="group">
                        ${printButton}
                        ${viewButton}
                    </div>
                </td>
            </tr>
        `;
    });

    // تحديث إجماليات الجدول
    $('#total-sales-sum').text(formatCurrency(totalSalesSum));
    $('#total-expenses-sum').text(formatCurrency(totalExpensesSum));
    $('#total-salaries-sum').text(formatCurrency(totalSalariesSum));
    $('#total-profit-sum').text(formatCurrency(totalProfitSum));

    // تحديد لون إجمالي صافي الربح/الخسارة
    const netProfitSumElement = $('#total-net-profit-sum');
    netProfitSumElement.text(formatCurrency(totalNetProfitSum));

    if (totalNetProfitSum < 0) {
        netProfitSumElement.removeClass('text-white').addClass('text-danger');
    } else {
        netProfitSumElement.removeClass('text-danger').addClass('text-white');
    }

    // تدمير جدول DataTables الموجود إذا كان موجودًا
    if ($.fn.DataTable.isDataTable('#daily-report-table')) {
        $('#daily-report-table').DataTable().destroy();
    }

    // إضافة البيانات للجدول أولاً
    $('#daily-report-body').html(tableBody);

    // تهيئة جدول البيانات بعد إضافة البيانات
    $('#daily-report-table').DataTable({
        language: {
            url: 'https://cdn.datatables.net/plug-ins/1.11.5/i18n/ar.json'
        },
        ordering: true,
        paging: true,
        searching: true,
        responsive: true,
        // إضافة ملخص للجدول
        drawCallback: function() {
            // إظهار الملخص في التذييل
            $(this).find('tfoot').show();
        }
    });
}

/**
 * عرض بيانات طرق الدفع
 */
function displayPaymentMethods() {
    const paymentMethods = reportData.payment_methods || [];
    let tableBody = '';

    // حساب المجموع الكلي
    const totalAmount = paymentMethods.reduce((sum, method) => sum + parseFloat(method.total), 0);

    // إنشاء صفوف الجدول
    paymentMethods.forEach(method => {
        const percentage = totalAmount > 0 ? ((parseFloat(method.total) / totalAmount) * 100).toFixed(2) : '0.00';

        tableBody += `
            <tr>
                <td>${getPaymentMethodName(method.method)}</td>
                <td>${formatCurrency(method.total)}</td>
                <td>${percentage}%</td>
            </tr>
        `;
    });

    // إضافة البيانات للجدول
    $('#payment-methods-body').html(tableBody);
}

/**
 * عرض بيانات المصروفات
 */
function displayExpenses() {
    const expenses = reportData.expenses || [];
    let tableBody = '';

    // حساب المجموع الكلي
    const totalAmount = expenses.reduce((sum, expense) => sum + parseFloat(expense.total), 0);

    // إنشاء صفوف الجدول
    expenses.forEach(expense => {
        const percentage = totalAmount > 0 ? ((parseFloat(expense.total) / totalAmount) * 100).toFixed(2) : '0.00';

        tableBody += `
            <tr>
                <td>${expense.category_name}</td>
                <td>${formatCurrency(expense.total)}</td>
                <td>${percentage}%</td>
            </tr>
        `;
    });

    // إضافة البيانات للجدول
    $('#expenses-categories-body').html(tableBody);
}

/**
 * عرض بيانات الرواتب
 */
function displaySalaries() {
    console.log('Displaying salaries data:', reportData.salaries);

    const salaries = reportData.salaries || [];
    let tableBody = '';

    // حساب المجموع الكلي
    const totalAmount = salaries.reduce((sum, salary) => {
        // التحقق من وجود قيمة للمبلغ
        const amount = parseFloat(salary.amount || salary.salary_amount || 0);
        console.log(`Salary for ${salary.employee_name || salary.user_name || 'unknown'}: ${amount}`);
        return sum + amount;
    }, 0);

    console.log('Total salaries amount:', totalAmount);

    if (salaries.length === 0) {
        // إذا لم تكن هناك رواتب
        tableBody = `
            <tr>
                <td colspan="4" class="text-center">لا توجد بيانات رواتب للفترة المحددة</td>
            </tr>
        `;
    } else {
        // إنشاء صفوف الجدول
        salaries.forEach(salary => {
            // التحقق من وجود قيمة للمبلغ في أي من الحقول المحتملة
            const amount = parseFloat(salary.amount || salary.salary_amount || salary.payment_amount || 0);

            // التحقق من وجود اسم الموظف في أي من الحقول المحتملة
            const employeeName = salary.employee_name || salary.user_name || salary.name || 'غير محدد';

            // التحقق من وجود تاريخ في أي من الحقول المحتملة
            const date = formatDate(salary.date || salary.payment_date || salary.created_at || '');

            // التحقق من وجود ملاحظات في أي من الحقول المحتملة
            const notes = salary.notes || salary.description || salary.comment || '';

            tableBody += `
                <tr>
                    <td>${employeeName}</td>
                    <td>${date}</td>
                    <td>${formatCurrency(amount)}</td>
                    <td>${notes}</td>
                </tr>
            `;
        });
    }

    // إضافة البيانات للجدول
    $('#salaries-body').html(tableBody);

    // تحديث إجمالي الرواتب
    $('#salaries-total').text(formatCurrency(totalAmount));

    // التحقق من أن إجمالي الرواتب يتطابق مع القيمة في الملخص
    const summaryTotalSalaries = parseFloat(reportData.summary.total_salaries || 0);
    console.log('Summary total salaries:', summaryTotalSalaries);

    if (Math.abs(totalAmount - summaryTotalSalaries) > 0.01) {
        console.warn('Mismatch between calculated total salaries and summary total salaries');
    }
}

/**
 * إنشاء المخططات البيانية
 */
function renderCharts() {
    // إنشاء مخطط المبيعات والمصروفات
    renderSalesExpensesChart();

    // إنشاء مخطط الأرباح
    renderProfitChart();

    // إنشاء مخطط طرق الدفع
    renderPaymentMethodsChart();

    // إنشاء مخطط فئات المصروفات
    renderExpensesCategoriesChart();
}

/**
 * إنشاء مخطط المبيعات والمصروفات
 */
function renderSalesExpensesChart() {
    const dailySales = reportData.daily_sales || [];
    const summary = reportData.summary || {};

    // تجهيز البيانات للمخطط
    const labels = dailySales.map(day => formatDate(day.date));
    const salesData = dailySales.map(day => parseFloat(day.total_sales));
    const expensesData = dailySales.map(day => parseFloat(day.total_expenses));

    // إضافة بيانات الرواتب إذا كانت متوفرة
    const salariesData = [];
    if (summary.total_salaries && summary.total_salaries > 0) {
        // توزيع إجمالي الرواتب على عدد الأيام للتمثيل البياني
        const averageDailySalary = summary.total_salaries / dailySales.length;
        for (let i = 0; i < dailySales.length; i++) {
            salariesData.push(averageDailySalary);
        }
    }

    // تدمير المخطط السابق إذا كان موجودًا
    if (salesExpensesChart) {
        salesExpensesChart.destroy();
    }

    // إنشاء المخطط الجديد
    const ctx = document.getElementById('sales-expenses-chart').getContext('2d');

    // إعداد مجموعات البيانات
    const datasets = [
        {
            label: 'المبيعات',
            data: salesData,
            borderColor: 'rgb(54, 162, 235)',
            backgroundColor: 'rgba(54, 162, 235, 0.2)',
            tension: 0.1,
            fill: true
        },
        {
            label: 'المصروفات',
            data: expensesData,
            borderColor: 'rgb(255, 99, 132)',
            backgroundColor: 'rgba(255, 99, 132, 0.2)',
            tension: 0.1,
            fill: true
        }
    ];

    // إضافة الرواتب إذا كانت متوفرة
    if (salariesData.length > 0) {
        datasets.push({
            label: 'الرواتب',
            data: salariesData,
            borderColor: 'rgb(255, 159, 64)',
            backgroundColor: 'rgba(255, 159, 64, 0.2)',
            tension: 0.1,
            fill: true,
            borderDash: [5, 5] // خط متقطع لتمييز الرواتب
        });
    }

    salesExpensesChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: labels,
            datasets: datasets
        },
        options: {
            responsive: true,
            plugins: {
                title: {
                    display: true,
                    text: 'المبيعات والمصروفات والرواتب'
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            return context.dataset.label + ': ' + formatCurrency(context.raw);
                        }
                    }
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        callback: function(value) {
                            return formatCurrency(value);
                        }
                    }
                }
            }
        }
    });
}

/**
 * إنشاء مخطط الأرباح
 */
function renderProfitChart() {
    const dailySales = reportData.daily_sales || [];

    // تجهيز البيانات للمخطط
    const labels = dailySales.map(day => formatDate(day.date));
    const profitData = dailySales.map(day => parseFloat(day.profit));
    const netProfitData = dailySales.map(day => parseFloat(day.net_profit));

    // تدمير المخطط السابق إذا كان موجودًا
    if (profitChart) {
        profitChart.destroy();
    }

    // إنشاء المخطط الجديد
    const ctx = document.getElementById('profit-chart').getContext('2d');
    profitChart = new Chart(ctx, {
        type: 'bar',
        data: {
            labels: labels,
            datasets: [
                {
                    label: 'الأرباح',
                    data: profitData,
                    backgroundColor: function(context) {
                        // لون أخضر للأرباح وأحمر للخسائر
                        return context.raw >= 0 ? 'rgba(75, 192, 192, 0.5)' : 'rgba(255, 99, 132, 0.5)';
                    },
                    borderColor: function(context) {
                        return context.raw >= 0 ? 'rgb(75, 192, 192)' : 'rgb(255, 99, 132)';
                    },
                    borderWidth: 1
                },
                {
                    label: 'صافي الربح',
                    data: netProfitData,
                    backgroundColor: function(context) {
                        // لون أزرق للأرباح وأحمر داكن للخسائر
                        return context.raw >= 0 ? 'rgba(153, 102, 255, 0.5)' : 'rgba(220, 53, 69, 0.5)';
                    },
                    borderColor: function(context) {
                        return context.raw >= 0 ? 'rgb(153, 102, 255)' : 'rgb(220, 53, 69)';
                    },
                    borderWidth: 1
                }
            ]
        },
        options: {
            responsive: true,
            plugins: {
                title: {
                    display: true,
                    text: 'الأرباح وصافي الربح'
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            // إضافة نسبة الربح/الخسارة في التلميح
                            const value = context.raw;
                            const label = context.dataset.label;

                            // حساب النسبة المئوية
                            let percentage = 0;
                            const dayIndex = context.dataIndex;
                            const totalSales = parseFloat(dailySales[dayIndex].total_sales);

                            if (totalSales > 0) {
                                percentage = (value / totalSales) * 100;
                            }

                            return `${label}: ${formatCurrency(value)} (${percentage.toFixed(2)}%)`;
                        }
                    }
                }
            },
            scales: {
                y: {
                    // السماح بالقيم السالبة
                    beginAtZero: false,
                    ticks: {
                        callback: function(value) {
                            return formatCurrency(value);
                        }
                    }
                }
            }
        }
    });
}

/**
 * إنشاء مخطط طرق الدفع
 */
function renderPaymentMethodsChart() {
    const paymentMethods = reportData.payment_methods || [];

    // تجهيز البيانات للمخطط
    const labels = paymentMethods.map(method => getPaymentMethodName(method.method));
    const data = paymentMethods.map(method => parseFloat(method.total));

    // تدمير المخطط السابق إذا كان موجودًا
    if (paymentMethodsChart) {
        paymentMethodsChart.destroy();
    }

    // إنشاء المخطط الجديد
    const ctx = document.getElementById('payment-methods-chart').getContext('2d');
    paymentMethodsChart = new Chart(ctx, {
        type: 'pie',
        data: {
            labels: labels,
            datasets: [
                {
                    data: data,
                    backgroundColor: [
                        'rgba(255, 99, 132, 0.5)',
                        'rgba(54, 162, 235, 0.5)',
                        'rgba(255, 206, 86, 0.5)'
                    ],
                    borderColor: [
                        'rgb(255, 99, 132)',
                        'rgb(54, 162, 235)',
                        'rgb(255, 206, 86)'
                    ],
                    borderWidth: 1
                }
            ]
        },
        options: {
            responsive: true,
            plugins: {
                title: {
                    display: true,
                    text: 'المبيعات حسب طريقة الدفع'
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            const label = context.label || '';
                            const value = formatCurrency(context.raw);
                            const total = context.dataset.data.reduce((a, b) => a + b, 0);
                            const percentage = ((context.raw / total) * 100).toFixed(2) + '%';
                            return label + ': ' + value + ' (' + percentage + ')';
                        }
                    }
                }
            }
        }
    });
}

/**
 * إنشاء مخطط فئات المصروفات
 */
function renderExpensesCategoriesChart() {
    const expenses = reportData.expenses || [];

    // تجهيز البيانات للمخطط
    const labels = expenses.map(expense => expense.category_name);
    const data = expenses.map(expense => parseFloat(expense.total));

    // تدمير المخطط السابق إذا كان موجودًا
    if (expensesCategoriesChart) {
        expensesCategoriesChart.destroy();
    }

    // إنشاء المخطط الجديد
    const ctx = document.getElementById('expenses-categories-chart').getContext('2d');
    expensesCategoriesChart = new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: labels,
            datasets: [
                {
                    data: data,
                    backgroundColor: [
                        'rgba(255, 99, 132, 0.5)',
                        'rgba(54, 162, 235, 0.5)',
                        'rgba(255, 206, 86, 0.5)',
                        'rgba(75, 192, 192, 0.5)',
                        'rgba(153, 102, 255, 0.5)',
                        'rgba(255, 159, 64, 0.5)'
                    ],
                    borderColor: [
                        'rgb(255, 99, 132)',
                        'rgb(54, 162, 235)',
                        'rgb(255, 206, 86)',
                        'rgb(75, 192, 192)',
                        'rgb(153, 102, 255)',
                        'rgb(255, 159, 64)'
                    ],
                    borderWidth: 1
                }
            ]
        },
        options: {
            responsive: true,
            plugins: {
                title: {
                    display: true,
                    text: 'المصروفات حسب الفئة'
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            const label = context.label || '';
                            const value = formatCurrency(context.raw);
                            const total = context.dataset.data.reduce((a, b) => a + b, 0);
                            const percentage = ((context.raw / total) * 100).toFixed(2) + '%';
                            return label + ': ' + value + ' (' + percentage + ')';
                        }
                    }
                }
            }
        }
    });
}

/**
 * تصدير التقرير إلى ملف Excel
 */
function exportToExcel() {
    // إنشاء مصفوفة البيانات للتصدير
    const dailySales = reportData.daily_sales || [];
    const workbook = XLSX.utils.book_new();

    // إنشاء ورقة التقرير اليومي
    const dailyData = [
        ['التاريخ', 'المبيعات', 'المصروفات', 'الأرباح', 'صافي الربح']
    ];

    dailySales.forEach(day => {
        dailyData.push([
            formatDate(day.date),
            parseFloat(day.total_sales),
            parseFloat(day.total_expenses),
            parseFloat(day.profit),
            parseFloat(day.net_profit)
        ]);
    });

    // إضافة ورقة التقرير اليومي
    const dailySheet = XLSX.utils.aoa_to_sheet(dailyData);
    XLSX.utils.book_append_sheet(workbook, dailySheet, 'التقرير اليومي');

    // إنشاء ورقة طرق الدفع
    const paymentMethods = reportData.payment_methods || [];
    const paymentData = [
        ['طريقة الدفع', 'المبلغ', 'النسبة']
    ];

    const totalPayment = paymentMethods.reduce((sum, method) => sum + parseFloat(method.total), 0);

    paymentMethods.forEach(method => {
        const percentage = totalPayment > 0 ? ((parseFloat(method.total) / totalPayment) * 100).toFixed(2) : '0.00';
        paymentData.push([
            getPaymentMethodName(method.method),
            parseFloat(method.total),
            percentage + '%'
        ]);
    });

    // إضافة ورقة طرق الدفع
    const paymentSheet = XLSX.utils.aoa_to_sheet(paymentData);
    XLSX.utils.book_append_sheet(workbook, paymentSheet, 'طرق الدفع');

    // إنشاء ورقة المصروفات
    const expenses = reportData.expenses || [];
    const expensesData = [
        ['الفئة', 'المبلغ', 'النسبة']
    ];

    const totalExpenses = expenses.reduce((sum, expense) => sum + parseFloat(expense.total), 0);

    expenses.forEach(expense => {
        const percentage = totalExpenses > 0 ? ((parseFloat(expense.total) / totalExpenses) * 100).toFixed(2) : '0.00';
        expensesData.push([
            expense.category,
            parseFloat(expense.total),
            percentage + '%'
        ]);
    });

    // إضافة ورقة المصروفات
    const expensesSheet = XLSX.utils.aoa_to_sheet(expensesData);
    XLSX.utils.book_append_sheet(workbook, expensesSheet, 'المصروفات');

    // إنشاء ورقة الرواتب
    const salaries = reportData.salaries || [];
    const salariesData = [
        ['الموظف', 'التاريخ', 'المبلغ', 'الملاحظات']
    ];

    if (salaries.length === 0) {
        // إذا لم تكن هناك رواتب
        salariesData.push(['لا توجد بيانات رواتب للفترة المحددة', '', '', '']);
    } else {
        // إضافة بيانات الرواتب
        salaries.forEach(salary => {
            // التحقق من وجود قيمة للمبلغ في أي من الحقول المحتملة
            const amount = parseFloat(salary.amount || salary.salary_amount || salary.payment_amount || 0);

            // التحقق من وجود اسم الموظف في أي من الحقول المحتملة
            const employeeName = salary.employee_name || salary.user_name || salary.name || 'غير محدد';

            // التحقق من وجود تاريخ في أي من الحقول المحتملة
            const date = salary.date || salary.payment_date || salary.created_at || '';

            // التحقق من وجود ملاحظات في أي من الحقول المحتملة
            const notes = salary.notes || salary.description || salary.comment || '';

            salariesData.push([employeeName, date, amount, notes]);
        });
    }

    // إضافة صف للمجموع
    const totalSalaries = salaries.reduce((sum, salary) => {
        const amount = parseFloat(salary.amount || salary.salary_amount || salary.payment_amount || 0);
        return sum + amount;
    }, 0);
    salariesData.push(['الإجمالي', '', totalSalaries, '']);

    // إضافة ورقة الرواتب
    const salariesSheet = XLSX.utils.aoa_to_sheet(salariesData);
    XLSX.utils.book_append_sheet(workbook, salariesSheet, 'الرواتب');

    // إنشاء ورقة الملخص
    const summary = reportData.summary;
    const summaryData = [
        ['البند', 'القيمة'],
        ['إجمالي المبيعات', parseFloat(summary.total_sales)],
        ['إجمالي المصروفات', parseFloat(summary.total_expenses)],
        ['إجمالي الرواتب', parseFloat(summary.total_salaries || 0)],
        ['إجمالي الأرباح', parseFloat(summary.total_profits)],
        ['صافي الربح', parseFloat(summary.net_profit)]
    ];

    // إضافة ورقة الملخص
    const summarySheet = XLSX.utils.aoa_to_sheet(summaryData);
    XLSX.utils.book_append_sheet(workbook, summarySheet, 'الملخص');

    // تحديد اسم الملف
    const fileName = `تقرير_شامل_${new Date().toISOString().split('T')[0]}.xlsx`;

    // تنزيل الملف
    XLSX.writeFile(workbook, fileName);
}

/**
 * طباعة التقرير
 */
function printReport() {
    window.print();
}

/**
 * تنسيق المبلغ بالعملة
 * @param {number|string} amount المبلغ
 * @returns {string} المبلغ منسقًا بالعملة
 */
function formatCurrency(amount) {
    // التحقق من وجود المبلغ
    if (amount === null || amount === undefined) {
        console.log('formatCurrency: amount is null or undefined');
        return '0 ' + currencySymbol;
    }

    // تحويل المبلغ إلى رقم
    const numAmount = parseFloat(amount);
    console.log('formatCurrency: input =', amount, 'parsed =', numAmount, 'type =', typeof numAmount);

    if (isNaN(numAmount)) {
        console.log('formatCurrency: numAmount is NaN');
        return '0 ' + currencySymbol;
    }

    // تنسيق المبلغ بفاصلة للآلاف ورقمين عشريين
    const formatted = numAmount.toLocaleString('ar-SA', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    }) + ' ' + currencySymbol;

    console.log('formatCurrency: formatted =', formatted);
    return formatted;
}

/**
 * تنسيق التاريخ
 * @param {string} dateStr التاريخ بصيغة YYYY-MM-DD
 * @returns {string} التاريخ منسقًا
 */
function formatDate(dateStr) {
    if (!dateStr) return '';

    const date = new Date(dateStr);
    if (isNaN(date.getTime())) return dateStr; // إذا كان التاريخ غير صالح

    // تنسيق التاريخ بالميلادي بصيغة YYYY-MM-DD
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');

    return `${year}-${month}-${day}`;
}

/**
 * الحصول على اسم طريقة الدفع بالعربية
 * @param {string} method رمز طريقة الدفع
 * @returns {string} اسم طريقة الدفع بالعربية
 */
function getPaymentMethodName(method) {
    switch (method) {
        case 'cash': return 'نقدي';
        case 'card': return 'بطاقة';
        case 'other': return 'أخرى';
        default: return method;
    }
}

/**
 * إظهار مؤشر التحميل
 */
function showLoading() {
    console.log('Showing loading indicator');

    // إنشاء عنصر مؤشر التحميل إذا لم يكن موجودًا
    if ($('#loading-indicator').length === 0) {
        console.log('Creating loading indicator element');
        $('body').append(`
            <div id="loading-indicator" style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; background-color: rgba(0, 0, 0, 0.5); z-index: 9999; display: flex; justify-content: center; align-items: center;">
                <div class="spinner-border text-light" role="status">
                    <span class="visually-hidden">جاري التحميل...</span>
                </div>
            </div>
        `);
    } else {
        console.log('Loading indicator already exists, showing it');
        $('#loading-indicator').show();
    }
}

/**
 * إخفاء مؤشر التحميل
 */
function hideLoading() {
    console.log('Hiding loading indicator');
    $('#loading-indicator').hide();
}

/**
 * عرض رسالة خطأ
 * @param {string} message نص الرسالة
 */
function showError(message) {
    console.error('Error:', message);

    // استخدام toastr إذا كان متاحًا
    if (typeof toastr !== 'undefined') {
        console.log('Using toastr for error display');
        toastr.error(message);
    } else {
        console.log('Using alert for error display');
        alert(message);
    }
}

/**
 * عرض رسالة نجاح
 * @param {string} message نص الرسالة
 */
function showSuccess(message) {
    console.log('Success:', message);

    // استخدام toastr إذا كان متاحًا
    if (typeof toastr !== 'undefined') {
        console.log('Using toastr for success display');
        toastr.success(message);
    } else {
        console.log('Using alert for success display');
        alert(message);
    }
}

/**
 * الحصول على نص الفترة الزمنية
 * @returns {string} نص الفترة الزمنية
 */
function getDateRangeText() {
    const dateRange = $('#date-range').val();
    let rangeText = '';

    if (dateRange === 'custom') {
        const startDate = $('#start-date').val();
        const endDate = $('#end-date').val();
        rangeText = `${startDate} إلى ${endDate}`;
    } else {
        // الحصول على النص من القائمة المنسدلة
        rangeText = $('#date-range option:selected').text();
    }

    return rangeText;
}

/**
 * إرسال التقرير للمدراء عبر الواتساب
 */
function sendReportToAdmins() {
    // إظهار مؤشر التحميل
    showLoading();

    // إعداد محتوى التقرير
    const reportTitle = `تقرير شامل - ${getDateRangeText()}`;

    // إعداد محتوى التقرير
    let reportContent = `ملخص التقرير:\n`;
    reportContent += `\nإجمالي المبيعات: ${$('#total-sales').text()}`;
    reportContent += `\nإجمالي المصروفات: ${$('#total-expenses').text()}`;
    reportContent += `\nإجمالي الرواتب: ${$('#total-salaries').text()}`;
    reportContent += `\nإجمالي الخصومات: ${$('#total-discounts').text()}`;
    reportContent += `\nإجمالي الأرباح: ${$('#total-profits').text()}`;
    reportContent += `\nصافي الربح: ${$('#net-profit').text().split('<')[0]}`;

    // إضافة معلومات عن الفواتير المدفوعة جزئياً
    reportContent += `\n\nالفواتير المدفوعة جزئياً: ${$('#total-partial-paid').text()}`;

    // إضافة أفضل المنتجات والخدمات مبيعاً
    if (reportData.top_products && reportData.top_products.length > 0) {
        reportContent += `\n\nأفضل المنتجات مبيعاً:`;
        reportData.top_products.slice(0, 5).forEach((product, index) => {
            reportContent += `\n${index + 1}. ${product.name} - ${formatCurrency(product.total_amount)}`;
        });
    }

    if (reportData.top_services && reportData.top_services.length > 0) {
        reportContent += `\n\nأفضل الخدمات مبيعاً:`;
        reportData.top_services.slice(0, 5).forEach((service, index) => {
            reportContent += `\n${index + 1}. ${service.name} - ${formatCurrency(service.total_amount)}`;
        });
    }

    // التحقق من وجود موديول WhatsAppDirectReportNotification أولاً
    if (typeof WhatsAppDirectReportNotification !== 'undefined') {
        console.log('استخدام WhatsAppDirectReportNotification لإرسال التقرير مباشرة');

        // إرسال التقرير باستخدام الموديول المباشر
        WhatsAppDirectReportNotification.sendReportNotification(reportTitle, reportContent)
            .then(function(result) {
                hideLoading();
                console.log('تم إرسال التقرير بنجاح مباشرة:', result);
                // لا نحتاج لعرض رسالة نجاح هنا لأن الموديول يقوم بذلك بالفعل
            })
            .catch(function(error) {
                hideLoading();
                console.error('خطأ في إرسال التقرير مباشرة:', error);
                showError(error.message || 'حدث خطأ أثناء إرسال التقرير');
            });
    }
    // التحقق من وجود موديول WhatsAppReportNotification كخيار ثاني
    else if (typeof WhatsAppReportNotification !== 'undefined') {
        console.log('استخدام WhatsAppReportNotification لإرسال التقرير');

        // إرسال التقرير باستخدام الموديول الجديد
        WhatsAppReportNotification.sendReportNotification(reportTitle, reportContent)
            .then(function(result) {
                hideLoading();
                console.log('تم إرسال التقرير بنجاح:', result);
                // لا نحتاج لعرض رسالة نجاح هنا لأن الموديول يقوم بذلك بالفعل
            })
            .catch(function(error) {
                hideLoading();
                console.error('خطأ في إرسال التقرير:', error);
                showError(error.message || 'حدث خطأ أثناء إرسال التقرير');
            });
    } else {
        console.log('لم يتم العثور على أي موديول لإرسال التقارير عبر واتساب، استخدام الطريقة القديمة');

        // إرسال التقرير عبر API بالطريقة القديمة
        $.ajax({
            url: `${BASE_URL}api/admin_notification.php`,
            type: 'POST',
            data: {
                action: 'send_custom_report',
                report_title: reportTitle,
                report_content: reportContent
            },
            dataType: 'json',
            success: function(response) {
                hideLoading();
                if (response.status === 'success') {
                    showSuccess('تم إرسال التقرير بنجاح للمدراء');
                } else {
                    showError(response.message || 'حدث خطأ أثناء إرسال التقرير');
                }
            },
            error: function(_, __, errorThrown) {
                hideLoading();
                showError('حدث خطأ أثناء الاتصال بالخادم');
                console.error('Error sending report:', errorThrown);
            }
        });
    }
}
