    /**
     * استرجاع إجمالي الإنفاق للعميل ضمن فترة محددة
     *
     * @param int $customerId
     * @param string $startDate
     * @param string $endDate
     * @return float
     */
    public function getCustomerTotalSpending($customerId, $startDate = null, $endDate = null) {
        $customerId = $this->db->real_escape_string($customerId);
        
        $sql = "SELECT SUM(id.final_price) AS total_spending 
                FROM invoice_details id 
                JOIN invoices i ON id.invoice_id = i.id 
                WHERE i.customer_id = '$customerId' 
                AND i.status != 'cancelled'";
        
        if ($startDate) {
            $startDate = $this->db->real_escape_string($startDate);
            $sql .= " AND i.created_at >= '$startDate 00:00:00'";
        }
        
        if ($endDate) {
            $endDate = $this->db->real_escape_string($endDate);
            $sql .= " AND i.created_at <= '$endDate 23:59:59'";
        }
        
        $result = $this->db->query($sql);
        
        if ($result && $row = $result->fetch_assoc()) {
            return (float)$row['total_spending'];
        }
        
        return 0;
    }

    /**
     * استرجاع إجمالي الخصومات للعميل ضمن فترة محددة
     *
     * @param int $customerId
     * @param string $startDate
     * @param string $endDate
     * @return float
     */
    public function getCustomerTotalDiscounts($customerId, $startDate = null, $endDate = null) {
        try {
            error_log("getCustomerTotalDiscounts - Start: Customer ID: $customerId, Start Date: $startDate, End Date: $endDate");
            
            $customerId = $this->db->real_escape_string($customerId);
            
            // جمع الخصومات من تفاصيل الفواتير
            $sql = "SELECT SUM(id.discount) AS total_discounts 
                    FROM invoice_details id 
                    JOIN invoices i ON id.invoice_id = i.id 
                    WHERE i.customer_id = '$customerId' 
                    AND i.status != 'cancelled'";
            
            // إضافة خصم الفاتورة نفسها
            $sql2 = "SELECT SUM(discount_amount) AS invoice_discounts 
                    FROM invoices 
                    WHERE customer_id = '$customerId' 
                    AND status != 'cancelled'";
            
            if ($startDate) {
                $startDate = $this->db->real_escape_string($startDate);
                $sql .= " AND i.created_at >= '$startDate 00:00:00'";
                $sql2 .= " AND created_at >= '$startDate 00:00:00'";
            }
            
            if ($endDate) {
                $endDate = $this->db->real_escape_string($endDate);
                $sql .= " AND i.created_at <= '$endDate 23:59:59'";
                $sql2 .= " AND created_at <= '$endDate 23:59:59'";
            }
            
            error_log("getCustomerTotalDiscounts - SQL Item Discounts: $sql");
            error_log("getCustomerTotalDiscounts - SQL Invoice Discounts: $sql2");
            
            // استرجاع الخصومات على مستوى المنتجات/الخدمات
            $result = $this->db->query($sql);
            if (!$result) {
                error_log("getCustomerTotalDiscounts - Error in item discounts query: " . $this->db->error);
                return 0;
            }
            
            $itemDiscounts = 0;
            if ($row = $result->fetch_assoc()) {
                $itemDiscounts = (float)$row['total_discounts'];
                error_log("getCustomerTotalDiscounts - Item Discounts: $itemDiscounts");
            }
            
            // استرجاع الخصومات على مستوى الفاتورة
            $result2 = $this->db->query($sql2);
            if (!$result2) {
                error_log("getCustomerTotalDiscounts - Error in invoice discounts query: " . $this->db->error);
                return $itemDiscounts; // نعيد فقط خصومات المنتجات إذا فشل استعلام خصومات الفاتورة
            }
            
            $invoiceDiscounts = 0;
            if ($row = $result2->fetch_assoc()) {
                $invoiceDiscounts = (float)$row['invoice_discounts'];
                error_log("getCustomerTotalDiscounts - Invoice Discounts: $invoiceDiscounts");
            }
            
            // إجمالي الخصومات = خصومات المنتجات + خصومات الفاتورة
            $totalDiscounts = $itemDiscounts + $invoiceDiscounts;
            error_log("getCustomerTotalDiscounts - Total Discounts: $totalDiscounts");
            
            return $totalDiscounts;
        } catch (Exception $e) {
            error_log("getCustomerTotalDiscounts - Exception: " . $e->getMessage());
            return 0;
        }
    }
    
    /**
     * استرجاع ملخص الإنفاق حسب الفئات
     *
     */ 