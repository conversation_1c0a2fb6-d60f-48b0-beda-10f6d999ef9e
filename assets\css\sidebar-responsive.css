/**
 * Responsive Sidebar Styles
 * تنسيقات القائمة الجانبية المتجاوبة مع الموبايل
 */

/* تنسيقات القائمة الجانبية الأساسية */
.sidebar {
    position: fixed;
    top: 0;
    right: 0;
    width: 250px;
    height: 100vh;
    z-index: 1030;
    background: #343a40;
    color: #fff;
    transition: all 0.3s ease-in-out;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    overflow-y: auto;
    overflow-x: hidden;
}

/* رأس القائمة الجانبية */
.sidebar .sidebar-header {
    padding: 15px;
    background: #262b33;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

/* عناصر القائمة */
.sidebar ul.components {
    padding: 15px 0;
}

.sidebar ul li {
    padding: 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.sidebar ul li a {
    padding: 12px 15px;
    font-size: 1rem;
    display: flex;
    align-items: center;
    color: #fff;
    text-decoration: none;
    transition: all 0.3s;
    border-radius: 4px;
    margin: 2px 5px;
}

.sidebar ul li a i {
    margin-left: 10px;
    font-size: 1.1rem;
    width: 20px;
    text-align: center;
}

.sidebar ul li a:hover {
    color: #fff;
    background: rgba(0, 123, 255, 0.7);
}

.sidebar ul li.active > a {
    color: #fff;
    background: #007bff;
}

/* القوائم الفرعية */
.sidebar ul li ul.collapse {
    padding-right: 15px;
}

.sidebar ul li ul.collapse li {
    border-bottom: none;
}

.sidebar ul li ul.collapse li a {
    padding: 10px 15px;
    font-size: 0.9rem;
}

/* زر التبديل للموبايل */
.mobile-toggle-btn {
    position: fixed;
    top: 10px;
    right: 10px;
    z-index: 1020;
    padding: 10px 15px;
    border-radius: 4px;
    display: none;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    background-color: #007bff;
    border: none;
}

.mobile-toggle-btn:hover, .mobile-toggle-btn:focus {
    background-color: #0069d9;
}

.mobile-toggle-btn i {
    font-size: 1.2rem;
}

/* تنسيقات الشاشات المتوسطة والصغيرة */
@media (max-width: 991.98px) {
    .sidebar {
        width: 230px;
    }
}

/* تنسيقات الشاشات الصغيرة (الموبايل) */
@media (max-width: 767.98px) {
    .mobile-toggle-btn {
        display: block;
    }

    .sidebar {
        margin-right: -250px;
        box-shadow: none;
    }

    .sidebar.active {
        margin-right: 0;
        box-shadow: 0 0 15px rgba(0, 0, 0, 0.2);
    }

    /* تغطية الشاشة عند فتح القائمة */
    .sidebar-overlay {
        display: none;
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: rgba(0, 0, 0, 0.5);
        z-index: 1020;
    }

    .sidebar-overlay.active {
        display: block;
    }

    /* تحسين حجم العناصر للمس */
    .sidebar ul li a {
        padding: 15px;
        font-size: 1.1rem;
    }

    .sidebar ul li a i {
        font-size: 1.2rem;
    }

    /* تحسين القوائم الفرعية */
    .sidebar ul li ul.collapse li a {
        padding: 12px 15px;
        font-size: 1rem;
    }

    /* تحسينات للمحتوى على الشاشات الصغيرة */
    .card {
        overflow-x: auto;
    }

    .table-responsive {
        overflow-x: auto;
    }

    .main-navbar {
        flex-direction: column;
        align-items: flex-start;
    }

    .main-navbar > div:last-child {
        margin-top: 10px;
        align-self: flex-end;
    }
}

/* تنسيقات الشاشات الصغيرة جدًا */
@media (max-width: 575.98px) {
    .sidebar {
        width: 100%;
        margin-right: -100%;
    }

    .sidebar.active {
        margin-right: 0;
    }

    /* تحسينات إضافية للشاشات الصغيرة جدًا */
    .form-group {
        margin-bottom: 15px;
    }

    .btn-group {
        display: flex;
        flex-direction: column;
        width: 100%;
    }

    .btn-group .btn {
        margin-bottom: 5px;
        border-radius: 4px !important;
    }

    .dataTables_wrapper .dataTables_length,
    .dataTables_wrapper .dataTables_filter {
        text-align: right;
        width: 100%;
        margin-bottom: 10px;
    }

    .dataTables_wrapper .dataTables_paginate {
        float: none;
        text-align: center;
        margin-top: 10px;
    }

    .card-header {
        flex-direction: column;
        align-items: flex-start;
    }

    .card-header .btn {
        margin-top: 10px;
        align-self: flex-start;
    }
}

/* تنسيقات خاصة بصفحة نقطة البيع */
.pos-items-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 10px;
}

@media (min-width: 768px) {
    .pos-items-grid {
        grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
    }
}

@media (max-width: 767.98px) {
    .pos-items-grid {
        grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    }
}

/* تنسيقات خاصة بجداول نقطة البيع */
#invoice-items-table {
    width: 100%;
}

@media (min-width: 768px) {
    #invoice-items-table {
        display: table;
    }
}

/* تأثيرات انتقالية للقوائم الفرعية */
.sidebar .dropdown-toggle::after {
    display: block;
    position: absolute;
    left: 20px;
    top: 50%;
    transform: translateY(-50%);
}

.sidebar ul ul a {
    font-size: 0.9em !important;
    padding-right: 30px !important;
}

/* تنسيقات إضافية للتجاوب */
body.sidebar-active {
    overflow: hidden;
}

/* تنسيقات للقائمة المصغرة */
.sidebar.collapsed {
    width: 70px;
}

.sidebar.collapsed .sidebar-header h4 {
    display: none;
}

.sidebar.collapsed ul li a span {
    display: none;
}

.sidebar.collapsed ul li a i {
    font-size: 1.3rem;
    margin: 0 auto;
}

.sidebar.collapsed ul li ul.collapse {
    position: absolute;
    right: 70px;
    top: 0;
    width: 200px;
    background: #343a40;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    border-radius: 4px;
    display: none;
}

.sidebar.collapsed ul li:hover ul.collapse {
    display: block;
}

/* تنسيقات للمحتوى الرئيسي */
.content {
    margin-right: 250px;
    transition: all 0.3s;
    width: auto;
    padding: 15px;
    overflow-x: hidden;
}

.content.active {
    margin-right: 0;
}

/* تحسينات عامة للتجاوب */
@media (max-width: 767.98px) {
    .table {
        width: 100%;
        max-width: 100%;
        overflow-x: auto;
        display: block;
    }
}

/* تنسيقات عامة للبطاقات والنماذج */
.card {
    margin-bottom: 20px;
}

.form-control {
    max-width: 100%;
}

.img-fluid {
    max-width: 100%;
    height: auto;
}

/* تنسيقات خاصة بالجداول */
.table {
    width: 100%;
    margin-bottom: 1rem;
    color: #212529;
    border-collapse: collapse;
}

.table th,
.table td {
    padding: 0.75rem;
    vertical-align: top;
    border-top: 1px solid #dee2e6;
}

.table thead th {
    vertical-align: bottom;
    border-bottom: 2px solid #dee2e6;
}

.table-responsive {
    min-height: .01%;
    overflow-x: auto;
}

/* تنسيقات خاصة بالجداول على الشاشات الكبيرة */
@media (min-width: 768px) {
    .table-responsive {
        overflow-x: visible;
    }

    .table {
        display: table;
        width: 100%;
    }

    .table thead {
        display: table-header-group;
    }

    .table tbody {
        display: table-row-group;
    }

    .table tr {
        display: table-row;
    }

    .table th,
    .table td {
        display: table-cell;
    }

    /* تحسينات خاصة بجداول لوحة التحكم */
    .dashboard-table {
        table-layout: fixed;
    }

    .dashboard-table th,
    .dashboard-table td {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }
}

@media (max-width: 767.98px) {
    .content {
        margin-right: 0;
        width: 100%;
        padding: 10px;
    }

    /* تحسينات إضافية للمحتوى على الشاشات الصغيرة */
    .row {
        margin-left: -5px;
        margin-right: -5px;
    }

    .col, .col-1, .col-2, .col-3, .col-4, .col-5, .col-6,
    .col-7, .col-8, .col-9, .col-10, .col-11, .col-12,
    .col-sm, .col-md, .col-lg, .col-xl {
        padding-left: 5px;
        padding-right: 5px;
    }

    /* تحسين عرض الجداول على الشاشات الصغيرة */
    .table-responsive {
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
    }
}
