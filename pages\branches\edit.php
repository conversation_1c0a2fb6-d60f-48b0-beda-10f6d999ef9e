<?php
/**
 * صفحة تعديل الفرع
 */

// تعريف ثابت لمنع الوصول المباشر للملفات المرتبطة
define('BASEPATH', true);

// استدعاء ملفات التهيئة
require_once '../../config/init.php';

// التحقق من تسجيل الدخول
requireLogin();

// التحقق من وجود صلاحية تعديل فرع
requirePermission('branches_edit');

// عنوان الصفحة
$pageTitle = 'تعديل الفرع';

// إنشاء كائن قاعدة البيانات
$branchModel = new Branch($db);

// التحقق من وجود معرف الفرع
if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    $_SESSION['error_message'] = 'معرف الفرع غير صحيح';
    header('Location: index.php');
    exit;
}

$branchId = intval($_GET['id']);

// استرجاع بيانات الفرع
$branch = $branchModel->getBranchById($branchId);

if (!$branch) {
    $_SESSION['error_message'] = 'الفرع غير موجود';
    header('Location: index.php');
    exit;
}

// معالجة نموذج تعديل الفرع
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    try {
        // جمع بيانات الفرع
        $branchData = [
            'name' => sanitizeInput($_POST['name']),
            'address' => sanitizeInput($_POST['address']),
            'phone' => sanitizeInput($_POST['phone']),
            'country_code' => sanitizeInput($_POST['country_code']),
            'manager_id' => intval($_POST['manager_id']),
            'is_active' => isset($_POST['is_active']) ? 1 : 0
        ];

        // التحقق من صحة البيانات
        if (empty($branchData['name'])) {
            throw new Exception('يرجى إدخال اسم الفرع');
        }

        // تحديث الفرع
        $branchModel->updateBranch($branchId, $branchData);

        // رسالة نجاح
        $_SESSION['success_message'] = 'تم تحديث الفرع بنجاح';

        // إعادة التوجيه إلى صفحة الفروع
        header('Location: index.php');
        exit;
    } catch (Exception $e) {
        $_SESSION['error_message'] = $e->getMessage();
    }
}

// استرجاع قائمة المديرين
$userModel = new User($db);
$managers = $userModel->getUsersByRole('manager');

// استدعاء رأس الصفحة
include '../../includes/templates/header.php';
?>

<!-- المحتوى الرئيسي للصفحة -->
<div class="container-fluid">
    <!-- رأس الصفحة -->
    <div class="card border-0 shadow-sm mb-4">
        <div class="card-body d-flex justify-content-between align-items-center">
            <h5 class="mb-0">
                <i class="fas fa-store me-2"></i> تعديل الفرع: <?php echo htmlspecialchars($branch['name']); ?>
            </h5>
            <a href="index.php" class="btn btn-secondary">
                <i class="fas fa-arrow-right me-1"></i> العودة للفروع
            </a>
        </div>
    </div>

    <!-- نموذج تعديل الفرع -->
    <div class="card border-0 shadow-sm">
        <div class="card-body">
            <form method="post" action="">
                <div class="row mb-3">
                    <div class="col-md-6">
                        <label for="name" class="form-label">اسم الفرع <span class="text-danger">*</span></label>
                        <input type="text" id="name" name="name" class="form-control" value="<?php echo htmlspecialchars($branch['name']); ?>" required>
                    </div>
                    <div class="col-md-3">
                        <label for="country_code" class="form-label">رمز البلد</label>
                        <input type="text" id="country_code" name="country_code" class="form-control" value="<?php echo htmlspecialchars($branch['country_code'] ?? '+20'); ?>" placeholder="مثل: +20">
                    </div>
                    <div class="col-md-3">
                        <label for="phone" class="form-label">رقم الهاتف</label>
                        <input type="text" id="phone" name="phone" class="form-control" value="<?php echo htmlspecialchars($branch['phone'] ?? ''); ?>">
                    </div>
                </div>

                <div class="mb-3">
                    <label for="address" class="form-label">العنوان</label>
                    <textarea id="address" name="address" class="form-control" rows="3"><?php echo htmlspecialchars($branch['address'] ?? ''); ?></textarea>
                </div>

                <div class="row mb-3">
                    <div class="col-md-6">
                        <label for="manager_id" class="form-label">مدير الفرع</label>
                        <select id="manager_id" name="manager_id" class="form-select">
                            <option value="0">اختر مدير الفرع</option>
                            <?php foreach ($managers as $manager): ?>
                                <option value="<?php echo $manager['id']; ?>" <?php echo ($branch['manager_id'] == $manager['id']) ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($manager['name']); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="col-md-6">
                        <div class="form-check mt-4">
                            <input class="form-check-input" type="checkbox" id="is_active" name="is_active" <?php echo ($branch['is_active'] == 1) ? 'checked' : ''; ?>>
                            <label class="form-check-label" for="is_active">فرع نشط</label>
                        </div>
                    </div>
                </div>

                <div class="text-end">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-1"></i> حفظ التغييرات
                    </button>
                    <a href="index.php" class="btn btn-secondary">
                        <i class="fas fa-times me-1"></i> إلغاء
                    </a>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- استدعاء قالب الفوتر -->
<?php include '../../includes/templates/footer.php'; ?>
